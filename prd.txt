# NeurometerGo CLI - Product Requirements Document

## Executive Summary

NeurometerGo CLI is a high-performance, command-line load testing engine built in Go for Linux systems with CUDA GPU acceleration. It provides a modern, headless alternative to JMeter's non-GUI mode, designed for automated testing, CI/CD pipelines, and distributed load generation at scale. The system emphasizes real-world testing against actual servers with no mocks or stubs, ensuring accurate performance measurements, code base is well devoloped and originally included windows and mac ui components, adjust the code base for a pure linux cli engine approuch.

## Product Vision

### Problem Statement
Current CLI load testing tools suffer from:
- Poor resource efficiency and limited scalability
- Lack of GPU acceleration for AI-powered workloads
- Complex configuration and poor automation support
- Reliance on mocks that don't reflect real performance
- Limited real-time monitoring capabilities in headless mode

### Solution
NeurometerGo CLI addresses these challenges through:
- **Pure CLI Design**: Command-line interface for all operations
- **CUDA GPU Acceleration**: Native CUDA support for AI workload generation
- **Real Server Testing**: All testing against actual infrastructure
- **YAML/JSON Configuration**: Simple, version-controlled test definitions
- **Headless Operation**: Designed for servers, containers, and CI/CD

### Target Users
- **DevOps Engineers**: Automated performance testing in CI/CD
- **SRE Teams**: Production load testing and capacity planning
- **QA Automation**: Headless testing in test environments
- **Cloud Engineers**: Distributed load testing on GPU instances

## Architecture Overview

### System Design

```
neuralmeter (CLI binary)
├── Command Parser (cobra/cli)
├── Configuration Loader (YAML/JSON)
├── Test Execution Engine
├── HTTP Client (with GPU payload generation)
├── Worker Pool Manager
├── Metrics Collector
├── Result Exporter
└── CUDA GPU Interface
```

### Deployment Modes

#### 1. Single Instance Mode
```bash
./neuralmeter run -c stress_test.yaml
```
- Local execution on single Linux machine
- Direct CUDA GPU access for AI workloads
- Suitable for development and moderate load

#### 2. Daemon Mode
```bash
./neuralmeter server start --port 8080 --daemon
```
- Background service accepting remote jobs
- REST/gRPC API for job submission
- Auto-scaling worker pool
- Real-time result streaming

#### 3. Distributed Mode
```bash
./neuralmeter run -c test.yaml --master-nodes node1:8080,node2:8080
```
- Coordinate across multiple instances
- Automatic load distribution
- Centralized metrics aggregation

## Core Features

### 1. Command-Line Interface

#### Basic Commands
```bash
# Run a test
./neuralmeter run -c test_config.yaml

# Validate configuration
./neuralmeter validate -c test_config.yaml

# Run with specific parameters
./neuralmeter run -c api_test.yaml --duration 5m --users 1000 --ramp-up 30s

# Daemon mode operations
./neuralmeter server start --port 8080 --daemon
./neuralmeter server stop
./neuralmeter server status

# GPU management
./neuralmeter gpu list
./neuralmeter gpu status
./neuralmeter gpu benchmark

# Configuration management
./neuralmeter config show
./neuralmeter config validate -c production.yaml
```

#### Advanced Options
```bash
# Output formats
./neuralmeter run -c test.yaml --output json --report-file results.json

# Real-time monitoring
./neuralmeter run -c test.yaml --metrics-port 9090 --metrics-interval 1s

# Distributed execution
./neuralmeter run -c test.yaml --workers 10 --master-nodes node1,node2

# GPU configuration
./neuralmeter run -c test.yaml --gpu-device 0 --gpu-memory-limit 8GB
```

### 2. Configuration Format

#### YAML Test Configuration
```yaml
# stress_test.yaml
test:
  name: "API Stress Test"
  duration: "10m"
  rampUp: "1m"
  workers: 100
  
scenarios:
  - name: "User Authentication"
    weight: 30
    requests:
      - method: POST
        url: "${BASE_URL}/auth/login"
        headers:
          Content-Type: "application/json"
        body:
          username: "${USERNAME}"
          password: "${PASSWORD}"
        extract:
          - json: "$.token"
            as: "auth_token"
        validate:
          - status: 200
          - responseTime: "<500ms"
          
  - name: "API Operations"
    weight: 70
    requests:
      - method: GET
        url: "${BASE_URL}/api/data"
        headers:
          Authorization: "Bearer ${auth_token}"
        validate:
          - status: 200
          - json: "$.status == 'success'"

gpu:
  enabled: true
  device: 0
  workload:
    type: "llm_prompt"
    model: "llama2-7b"
    temperature: 0.7
    
metrics:
  export:
    - type: "prometheus"
      endpoint: "http://prometheus:9090"
    - type: "file"
      format: "json"
      path: "./results/metrics.json"
```

### 3. GPU-Accelerated Features

#### CUDA Integration
- **Payload Generation**: LLM prompts, embeddings, synthetic data
- **Model Inference**: Run AI models for realistic workload simulation
- **Batch Processing**: Optimize GPU utilization with batching
- **Memory Management**: Efficient GPU memory pooling
- **Multi-GPU Support**: Scale across multiple CUDA devices

#### GPU Commands
```bash
# List available GPUs
./neuralmeter gpu list
# Output:
# GPU 0: NVIDIA A100 (40GB) - CUDA 11.8
# GPU 1: NVIDIA A100 (40GB) - CUDA 11.8

# Run benchmark
./neuralmeter gpu benchmark --device 0
# Output:
# Inference throughput: 1,250 req/s
# Memory bandwidth: 1,555 GB/s
# Utilization: 94%

# Monitor during test
./neuralmeter gpu status --watch
```

### 4. Test Execution Engine

#### Features
- **Scenario-Based Testing**: Weighted distribution of test scenarios
- **Dynamic Variables**: Runtime variable extraction and reuse
- **Request Correlation**: Extract values from responses for subsequent requests
- **Validation Rules**: Response validation with multiple criteria
- **Think Time**: Realistic delays between requests
- **Conditional Logic**: Skip/repeat based on conditions

#### Execution Flow
1. Load and validate configuration
2. Initialize GPU resources (if enabled)
3. Create worker pool
4. Start metrics collection
5. Execute test scenarios
6. Stream results in real-time
7. Generate final report

### 5. Metrics and Reporting

#### Real-Time Metrics
```bash
# Console output during test
./neuralmeter run -c test.yaml --verbose
[10:23:45] INFO: Test started - 1000 users, 10m duration
[10:23:46] METRICS: RPS: 1,234 | P95: 45ms | Errors: 0.1% | GPU: 82%
[10:23:47] METRICS: RPS: 1,456 | P95: 42ms | Errors: 0.1% | GPU: 85%
```

#### Export Formats
- **Prometheus**: Real-time metrics scraping
- **JSON**: Detailed results with all metrics
- **CSV**: Tabular data for analysis
- **JUnit XML**: CI/CD integration
- **HTML**: Human-readable reports

#### Metrics Collected
- Requests per second (RPS)
- Response time percentiles (P50, P90, P95, P99)
- Error rate and error types
- Throughput (bytes/sec)
- Connection statistics
- GPU utilization and memory
- CPU and memory usage

### 6. Daemon Mode Operation

#### API Endpoints
```bash
# Submit a test job
curl -X POST http://localhost:8080/api/jobs \
  -H "Content-Type: application/json" \
  -d @test_config.json

# Check job status
curl http://localhost:8080/api/jobs/job-123/status

# Stream results
curl -N http://localhost:8080/api/jobs/job-123/stream

# Health check
curl http://localhost:8080/health
```

#### Process Management
- PID file management (`/var/run/neuralmeter.pid`)
- Signal handling (SIGTERM, SIGINT)
- Graceful shutdown with timeout
- Automatic restart on failure
- Log rotation support

## Technical Specifications

### System Requirements
- **OS**: Linux (Ubuntu 20.04+, RHEL 8+, Debian 11+)
- **CPU**: x86_64 or ARM64
- **Memory**: 4GB minimum, 16GB recommended
- **GPU**: NVIDIA GPU with CUDA 11.8+ support
- **Storage**: 10GB for binary and logs

### Performance Targets
- **Concurrent Connections**: 100,000+ per instance
- **Requests per Second**: 50,000+ per instance
- **GPU Inference**: 1,000+ prompts/second
- **Memory Usage**: <10KB per connection
- **Startup Time**: <2 seconds
- **Configuration Load**: <100ms

### Dependencies
```bash
# System packages
nvidia-driver-470+
cuda-toolkit-11.8+
gcc
make

# Go modules (built into binary)
github.com/spf13/cobra      # CLI framework
gopkg.in/yaml.v3           # YAML parsing
github.com/gorilla/mux     # HTTP server
google.golang.org/grpc     # gRPC support
```

### Testing Philosophy
- **No Mocks**: All tests run against real servers
- **Real GPU**: Actual CUDA operations, no simulation
- **Production-Like**: Match production conditions
- **Continuous Validation**: Health checks during execution

## Implementation Details

### CLI Command Structure
```go
// Main command groups
./neuralmeter
├── run          # Execute tests
├── validate     # Validate configurations
├── server       # Daemon mode operations
├── gpu          # GPU management
├── config       # Configuration utilities
├── convert      # Import from JMeter
└── version      # Version information
```

### Configuration Loading
1. Parse command-line arguments
2. Load YAML/JSON configuration
3. Validate against schema
4. Apply environment variable substitution
5. Merge with command-line overrides

### Worker Pool Management
- Dynamic scaling based on load
- Goroutine pool with configurable limits
- Connection pooling per target host
- Graceful shutdown with drain timeout
- Memory-efficient job queue

### GPU Resource Management
- CUDA context per worker thread
- Memory pool allocation
- Batch request aggregation
- Automatic GPU selection
- Thermal throttling monitoring

## Security Considerations

### Authentication
- API key authentication for daemon mode
- TLS support for API endpoints
- Configuration file encryption
- Secure credential storage

### Resource Limits
- Maximum concurrent connections
- Memory usage caps
- GPU memory limits
- Rate limiting for API

### Audit Logging
- All API operations logged
- Test execution audit trail
- Configuration change tracking
- Security event logging

## Monitoring and Observability

### Logging
```bash
# Log levels
./neuralmeter run -c test.yaml --log-level debug

# Log outputs
./neuralmeter run -c test.yaml --log-file /var/log/neuralmeter.log

# Structured logging (JSON)
./neuralmeter run -c test.yaml --log-format json
```

### Metrics Exposure
- Prometheus metrics endpoint
- StatsD integration
- Custom metric collectors
- Real-time streaming API

### Health Checks
```bash
# Basic health
curl http://localhost:8080/health

# Detailed health
curl http://localhost:8080/health/detailed
```

## Deployment Scenarios

### CI/CD Integration
```bash
# Jenkins pipeline
stage('Performance Test') {
  sh './neuralmeter run -c test.yaml --exit-on-error --report-file results.json'
  publishHTML target: [reportFiles: 'results.html']
}
```

### Container Deployment
```dockerfile
FROM nvidia/cuda:11.8.0-runtime-ubuntu22.04
COPY neuralmeter /usr/local/bin/
ENTRYPOINT ["neuralmeter", "server", "start"]
```

### Kubernetes Job
```yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: load-test
spec:
  template:
    spec:
      containers:
      - name: neuralmeter
        image: neuralmeter:latest
        command: ["neuralmeter", "run", "-c", "/config/test.yaml"]
        resources:
          limits:
            nvidia.com/gpu: 1
```

## Success Metrics

### Performance
- 10x more efficient than JMeter non-GUI mode
- Linear scaling to 1M+ total connections
- Sub-millisecond metric collection overhead
- 90%+ GPU utilization during AI workloads

### Reliability
- 99.9% uptime in daemon mode
- Zero memory leaks over 24h operation
- Graceful handling of all error conditions
- Accurate metrics within 1% margin

### Usability
- Single binary deployment
- Configuration validation in <1 second
- Clear error messages with solutions
- Comprehensive --help documentation

## Roadmap

### Phase 1: Core CLI (Current)
- 🔄 Basic command structure
- 🔄 Configuration loading
- 🔄 HTTP client implementation
- 🔄 Worker pool
- 🔄 Daemon mode 

### Phase 2: GPU Integration
- 🔄 CUDA detection
- 🔄 Model loading
- 🔄 Performance monitoring
- 🔄 Multi-GPU support

### Phase 3: Advanced Features
- Distributed coordination
- Advanced protocols (WebSocket, gRPC)
- Custom plugin support
- Cloud provider integration

### Phase 4: Enterprise
- RBAC for API access
- Multi-tenancy support
- Advanced scheduling
- Cost optimization features