server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"
  tls:
    enabled: false
    cert_file: ""
    key_file: ""

load_test:
  default_duration: "60s"
  default_concurrency: 10
  max_concurrency: 1000
  ramp_up_duration: "10s"
  ramp_down_duration: "10s"

metrics:
  enabled: true
  collection_interval: "1s"
  buffer_size: 1000
  retention_period: "24h"
  export_interval: "10s"

output:
  format: "json"
  file: ""
  console: true
  verbose: false
  templates: {}
  compression: false

dashboard:
  enabled: false
  host: "localhost"
  port: 8081
  refresh_rate: 1
  history_limit: 1000

worker:
  pool_size: 10
  queue_size: 100
  max_retries: 3
  retry_delay: "1s"
  shutdown_timeout: "30s"

gpu:
  enabled: true
  prefer_cuda: true
  min_memory_gb: 2.0
  min_compute_capability:
    major: 3
    minor: 5
  max_memory_utilization: 90.0
  device_id: -1
  monitoring_interval: "1s"
  allow_fallback: false
  profile_mode: "basic"
  log_gpu_events: true
  resource_limits:
    max_gpu_utilization: 95.0
    max_memory_usage: 90.0
    max_power_consumption: 300.0
    throttle_on_high_memory: true
    throttle_on_power_limit: true
  performance_thresholds:
    warning_memory_usage: 80.0
    warning_gpu_utilization: 85.0
    critical_memory_usage: 95.0
    critical_gpu_utilization: 98.0

global:
  log_level: "info"
  config_dir: "./config"
  data_dir: "./data"
  temp_dir: "/tmp"
  environment: "development"
  debug: false
  profiles_enabled: false
  variables:
    test_var: "test_value"
    api_version: "v1" 