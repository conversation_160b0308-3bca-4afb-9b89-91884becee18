# Design Document

## Overview

This design document outlines the implementation of WebSocket streaming functionality for the NeuralMeter CLI tool. The implementation will add a `--stream-results` flag that enables real-time streaming of load test metrics to WebSocket endpoints while maintaining GPU acceleration capabilities and minimal performance impact.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    CLI[NeuralMeter CLI] --> Parser[Command Parser]
    Parser --> Engine[Execution Engine]
    Engine --> GPU[GPU Manager]
    Engine --> Streaming[Streaming Manager]
    Streaming --> WSClient[WebSocket Client]
    WSClient --> Endpoint[WebSocket Endpoint]
    
    Engine --> Metrics[Metrics Collector]
    Metrics --> Streaming
    GPU --> Metrics
    
    subgraph "New Components"
        Streaming
        WSClient
    end
    
    subgraph "Existing Components"
        CLI
        Parser
        Engine
        GPU
        Metrics
    end
```

### Component Integration

The WebSocket streaming functionality will integrate with existing components:

1. **CLI Command Parser** - Add `--stream-results` flag to `runCmd`
2. **Execution Engine** - Integrate streaming manager for real-time metrics
3. **GPU Manager** - Provide GPU metrics to streaming pipeline
4. **Metrics Collector** - Format and route metrics to streaming manager

## Components and Interfaces

### 1. CLI Flag Integration

**File**: `cmd/neuralmeter/main.go`

```go
var (
    // Existing flags...
    streamResults string // New flag for WebSocket endpoint
)

// Add to runCmd flags
runCmd.Flags().StringVar(&streamResults, "stream-results", "", "WebSocket endpoint for real-time result streaming (ws://host:port/path)")
```

### 2. Streaming Manager

**File**: `internal/streaming/manager.go`

```go
type StreamingManager struct {
    client     *WebSocketClient
    config     *StreamingConfig
    metrics    chan *MetricsMessage
    errors     chan error
    done       chan struct{}
    logger     *log.Logger
}

type StreamingConfig struct {
    Endpoint        string
    UpdateInterval  time.Duration
    BufferSize      int
    RetryAttempts   int
    RetryDelay      time.Duration
    Headers         map[string]string
}

type MetricsMessage struct {
    Type      string                 `json:"type"`
    Timestamp time.Time             `json:"timestamp"`
    Data      map[string]interface{} `json:"data"`
}

func NewStreamingManager(config *StreamingConfig) *StreamingManager
func (sm *StreamingManager) Start() error
func (sm *StreamingManager) Stop() error
func (sm *StreamingManager) StreamMetrics(metrics *MetricsMessage) error
```

### 3. WebSocket Client

**File**: `internal/streaming/websocket_client.go`

```go
type WebSocketClient struct {
    conn           *websocket.Conn
    endpoint       string
    headers        map[string]string
    reconnectDelay time.Duration
    maxRetries     int
    logger         *log.Logger
    mutex          sync.RWMutex
}

func NewWebSocketClient(endpoint string, headers map[string]string) *WebSocketClient
func (wsc *WebSocketClient) Connect() error
func (wsc *WebSocketClient) Send(message []byte) error
func (wsc *WebSocketClient) Close() error
func (wsc *WebSocketClient) IsConnected() bool
```

### 4. Message Types

**File**: `internal/streaming/messages.go`

```go
// Message type constants
const (
    MessageTypeTestStart    = "test_start"
    MessageTypeMetrics      = "metrics"
    MessageTypeScenario     = "scenario_change"
    MessageTypeError        = "error"
    MessageTypeTestComplete = "test_complete"
    MessageTypeGPUMetrics   = "gpu_metrics"
)

// Specific message structures
type TestStartMessage struct {
    TestPlan     string    `json:"test_plan"`
    StartTime    time.Time `json:"start_time"`
    Scenarios    []string  `json:"scenarios"`
    GPUEnabled   bool      `json:"gpu_enabled"`
}

type MetricsUpdateMessage struct {
    RequestsExecuted   int64         `json:"requests_executed"`
    RequestsSucceeded  int64         `json:"requests_succeeded"`
    RequestsFailed     int64         `json:"requests_failed"`
    AverageLatency     time.Duration `json:"average_latency"`
    Throughput         float64       `json:"throughput"`
    ActiveConnections  int           `json:"active_connections"`
    ErrorRate          float64       `json:"error_rate"`
}

type GPUMetricsMessage struct {
    GPUUtilization    float64 `json:"gpu_utilization"`
    MemoryUtilization float64 `json:"memory_utilization"`
    Temperature       float64 `json:"temperature"`
    PowerUsage        float64 `json:"power_usage"`
}
```

### 5. Engine Integration

**File**: `internal/engine/streaming_integration.go`

```go
// Add to ExecutionEngine struct
type ExecutionEngine struct {
    // Existing fields...
    streamingManager *streaming.StreamingManager
    streamingEnabled bool
}

// Integration methods
func (ee *ExecutionEngine) initializeStreaming(endpoint string) error
func (ee *ExecutionEngine) streamMetricsUpdate() error
func (ee *ExecutionEngine) streamGPUMetrics() error
func (ee *ExecutionEngine) streamTestStart() error
func (ee *ExecutionEngine) streamTestComplete() error
```

## Data Models

### Configuration Structure

```yaml
# Test plan streaming configuration
streaming:
  enabled: true
  endpoint: "ws://localhost:8080/stream"
  update_interval: "1s"
  buffer_size: 1000
  retry_attempts: 3
  retry_delay: "5s"
  headers:
    Authorization: "Bearer token123"
    X-Client-ID: "neuralmeter-cli"

# CLI flag takes precedence over test plan configuration
```

### WebSocket Message Format

```json
{
  "type": "metrics",
  "timestamp": "2025-07-25T14:30:00Z",
  "data": {
    "requests_executed": 1500,
    "requests_succeeded": 1485,
    "requests_failed": 15,
    "average_latency": "150ms",
    "throughput": 50.5,
    "active_connections": 10,
    "error_rate": 0.01,
    "gpu_metrics": {
      "gpu_utilization": 75.5,
      "memory_utilization": 60.2,
      "temperature": 65.0,
      "power_usage": 180.5
    }
  }
}
```

## Error Handling

### Connection Failures

1. **Initial Connection Failure**
   - Log warning message
   - Continue test execution without streaming
   - Provide clear error message to user

2. **Connection Lost During Test**
   - Attempt automatic reconnection with exponential backoff
   - Buffer metrics during reconnection attempts
   - Drop oldest metrics if buffer overflows

3. **Authentication Failures**
   - Log authentication error
   - Do not retry automatically
   - Fail fast with clear error message

### Performance Safeguards

1. **Buffer Management**
   - Use circular buffer for metrics
   - Drop oldest metrics when buffer is full
   - Never block load generation for streaming

2. **GPU Resource Conflicts**
   - Prioritize load generation over streaming
   - Reduce streaming frequency if GPU utilization > 90%
   - Include GPU throttling warnings in stream

## Testing Strategy

### Unit Tests

1. **WebSocket Client Tests**
   - Connection establishment and failure scenarios
   - Message sending and error handling
   - Reconnection logic with mock WebSocket server

2. **Streaming Manager Tests**
   - Metrics formatting and transmission
   - Buffer management under load
   - Configuration validation

3. **Engine Integration Tests**
   - Streaming initialization and cleanup
   - Metrics collection and forwarding
   - GPU metrics integration

### Integration Tests

1. **End-to-End Streaming Tests**
   - CLI with `--stream-results` flag
   - Real WebSocket server receiving messages
   - GPU acceleration with streaming enabled

2. **Performance Impact Tests**
   - Load generation throughput with/without streaming
   - Memory usage comparison
   - GPU utilization impact measurement

3. **Error Scenario Tests**
   - Network interruption handling
   - WebSocket server unavailability
   - Invalid endpoint configuration

### Test WebSocket Server

**File**: `test/websocket-server/server.go`

```go
// Simple WebSocket server for testing
type TestWebSocketServer struct {
    server   *http.Server
    messages []string
    mutex    sync.RWMutex
}

func NewTestWebSocketServer(port int) *TestWebSocketServer
func (tws *TestWebSocketServer) Start() error
func (tws *TestWebSocketServer) Stop() error
func (tws *TestWebSocketServer) GetMessages() []string
```

## Implementation Plan

### Phase 1: Core WebSocket Client
- Implement `internal/streaming/websocket_client.go`
- Add connection management and basic message sending
- Create unit tests for WebSocket client

### Phase 2: Streaming Manager
- Implement `internal/streaming/manager.go`
- Add metrics formatting and buffering
- Create message type definitions

### Phase 3: CLI Integration
- Add `--stream-results` flag to CLI
- Integrate streaming manager with execution engine
- Add configuration parsing for streaming options

### Phase 4: GPU Integration
- Integrate GPU metrics collection
- Add GPU performance monitoring
- Implement GPU resource prioritization

### Phase 5: Testing and Optimization
- Create comprehensive test suite
- Performance testing and optimization
- Documentation and examples

## Performance Considerations

### Streaming Overhead
- Target: <5% impact on load generation throughput
- Use separate goroutines for streaming operations
- Implement non-blocking metrics collection

### Memory Management
- Circular buffer for metrics (default 1000 messages)
- Automatic garbage collection of old messages
- Memory pool for message objects

### GPU Resource Management
- Monitor GPU utilization during streaming
- Reduce streaming frequency if GPU is constrained
- Prioritize load generation over streaming operations

## Security Considerations

### WebSocket Security
- Support for WSS (secure WebSocket) connections
- TLS certificate validation
- Authentication header support

### Input Validation
- Validate WebSocket URLs before connection
- Sanitize configuration inputs
- Prevent injection attacks through headers

### Error Information Disclosure
- Avoid exposing sensitive information in error messages
- Log detailed errors locally, send generic errors to stream
- Implement rate limiting for error messages

## Dependencies

### New Dependencies
- `github.com/gorilla/websocket` - WebSocket client implementation
- Standard library packages: `net/url`, `crypto/tls`

### Integration Points
- `internal/engine` - Execution engine integration
- `internal/gpu` - GPU metrics collection
- `internal/metrics` - Metrics formatting
- `internal/config` - Configuration management
- `internal/validation` - Configuration validation

## Backward Compatibility

### Existing Functionality
- All existing CLI commands and flags remain unchanged
- No performance impact when streaming is disabled
- Existing output formats continue to work

### Configuration Migration
- Streaming configuration is additive
- No breaking changes to existing test plan formats
- Default behavior remains unchanged

## Monitoring and Observability

### Logging
- Structured logging for streaming operations
- Debug logs for connection state changes
- Performance metrics logging

### Metrics
- Streaming connection status
- Message send/receive rates
- Buffer utilization metrics
- Error rates and types

### Health Checks
- WebSocket connection health monitoring
- Streaming performance impact measurement
- GPU resource utilization tracking