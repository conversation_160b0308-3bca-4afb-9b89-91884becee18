#!/usr/bin/env python3
"""
Script to restructure GPU tasks 69-88 according to docs/opus_attempts.md solution.

This implements the single branch with directory-based platform separation:
- Each original task (69-88) becomes 4 tasks:
  - Core task: Interface definitions only
  - Linux task: CUDA implementation 
  - macOS task: Metal implementation
  - Windows task: DirectML implementation
"""

import json
import sys
from datetime import datetime

def create_core_task(original_task, new_id):
    """Create a core task with interface definitions only"""
    return {
        "id": new_id,
        "title": f"{original_task['title']} - Core Interfaces",
        "description": f"Define core interfaces and types for {original_task['title'].lower()}. NO IMPLEMENTATION - interfaces only.",
        "status": "pending",
        "dependencies": [],  # Will be updated based on original dependencies
        "priority": original_task.get("priority", "medium"),
        "details": f"""CORE INTERFACES ONLY - NO IMPLEMENTATION

This task defines the core interfaces, types, and contracts for {original_task['title'].lower()}.

**Directory Structure:**
- `internal/core/interfaces/` - Interface definitions
- `internal/core/types/` - Shared type definitions

**Deliverables:**
1. Interface definitions for all platform implementations
2. Shared type definitions and constants
3. Error types and validation contracts
4. Configuration structures
5. Testing interfaces and mocks

**Critical Rules:**
- NO platform-specific code
- NO actual implementations
- NO build tags
- Only interface definitions and types
- Must compile on all platforms

**Integration:**
Platform-specific implementations will implement these interfaces:
- Linux: CUDA implementations in `internal/gpu/linux/cuda/`
- macOS: Metal implementations in `internal/gpu/macos/metal/`
- Windows: DirectML implementations in `internal/gpu/windows/directml/`

Original task details (for reference):
{original_task.get('details', 'No details available')}
""",
        "testStrategy": f"""Test strategy for core interfaces:

1. **Interface Validation Tests:**
   - Verify all interfaces compile on all platforms
   - Test interface contracts and method signatures
   - Validate type definitions and constants

2. **Mock Implementation Tests:**
   - Create mock implementations for testing
   - Verify interface compliance
   - Test error handling contracts

3. **Cross-Platform Compilation:**
   - Build on macOS, Linux, and Windows
   - Verify no platform-specific dependencies
   - Test with different Go versions

Original test strategy (for reference):
{original_task.get('testStrategy', 'No test strategy available')}
""",
        "subtasks": []
    }

def create_platform_task(original_task, new_id, platform, gpu_tech, directory):
    """Create a platform-specific task"""
    platform_details = {
        "Linux": {
            "tech": "CUDA",
            "apis": "CUDA Runtime API, cuDNN, cuBLAS, NVML",
            "requirements": "NVIDIA GPU with CUDA support, CUDA Toolkit 11.0+",
            "build_tags": "//go:build linux && cuda",
            "frameworks": "CUDA Runtime, cuDNN for neural networks, cuBLAS for linear algebra"
        },
        "macOS": {
            "tech": "Metal",
            "apis": "Metal Framework, Metal Performance Shaders (MPS), IOKit",
            "requirements": "macOS 10.15+, Metal-compatible GPU (Apple Silicon or Intel Mac)",
            "build_tags": "//go:build darwin && metal",
            "frameworks": "Metal.framework, MetalPerformanceShaders.framework"
        },
        "Windows": {
            "tech": "DirectML",
            "apis": "DirectML API, DirectX 12, Windows ML",
            "requirements": "Windows 10 1903+, DirectX 12 compatible GPU",
            "build_tags": "//go:build windows && directml",
            "frameworks": "DirectML, DirectX 12, Windows Runtime"
        }
    }
    
    info = platform_details[platform]
    
    return {
        "id": new_id,
        "title": f"{original_task['title']} - {platform} ({info['tech']})",
        "description": f"Implement {original_task['title'].lower()} using {info['tech']} on {platform}. REAL {info['tech']} API CALLS ONLY - NO MOCKS, NO CPU FALLBACK.",
        "status": "pending", 
        "dependencies": [new_id - 3],  # Depends on the core task (3 positions back)
        "priority": original_task.get("priority", "medium"),
        "details": f"""PLATFORM-SPECIFIC IMPLEMENTATION: {platform} with {info['tech']}

**Build Tags:** `{info['build_tags']}`

**Directory Structure:**
- `{directory}` - {platform} {info['tech']} implementation

**Technology Stack:**
- **Primary API:** {info['tech']}
- **Supporting APIs:** {info['apis']}
- **Frameworks:** {info['frameworks']}
- **Requirements:** {info['requirements']}

**Implementation Requirements:**
1. **REAL {info['tech']} API CALLS ONLY**
   - Use actual {info['tech']} runtime APIs
   - NO mocks, stubs, or simulated implementations
   - NO CPU fallback code

2. **Platform-Specific Optimizations:**
   - Leverage {platform}-specific {info['tech']} features
   - Optimize for {platform} architecture and drivers
   - Use platform-native error handling

3. **Native {info['tech']} Integration:**
   - Direct {info['tech']} API bindings via CGo
   - Platform-specific memory management
   - Native {info['tech']} context and device handling

4. **Performance Focus:**
   - Optimize for {platform} {info['tech']} performance characteristics
   - Use {info['tech']}-specific profiling and optimization
   - Implement {platform}-native resource management

**Critical Rules:**
- MUST be developed on {platform} with {info['tech']} hardware
- NO cross-platform abstraction at this level
- NO CPU fallback mechanisms
- Use {info['tech']} best practices and idioms
- Implement comprehensive {info['tech']} error handling

**Development Environment:**
- **Platform:** {platform} only
- **Hardware:** {info['tech']}-compatible GPU required
- **IDE:** Cursor on {platform} with {platform}-specific rules
- **Testing:** Real {info['tech']} hardware testing only

Original task details (for {info['tech']} implementation):
{original_task.get('details', 'No details available')}
""",
        "testStrategy": f"""Testing strategy for {platform} {info['tech']} implementation:

1. **{info['tech']} API Tests:**
   - Test all {info['tech']} API calls with real hardware
   - Verify {info['tech']} context creation and management
   - Test {info['tech']} memory allocation and transfers
   - Validate {info['tech']} compute operations

2. **Platform-Specific Hardware Tests:**
   - Test on various {platform} {info['tech']} configurations
   - Verify performance on different {info['tech']} architectures
   - Test with different driver versions
   - Validate {platform}-specific optimizations

3. **{info['tech']} Error Handling Tests:**
   - Test {info['tech']}-specific error conditions
   - Verify proper {info['tech']} error code handling
   - Test recovery from {info['tech']} failures
   - Validate {info['tech']} resource cleanup

4. **Performance Benchmarks:**
   - Benchmark against {platform} {info['tech']} baselines
   - Measure {info['tech']} memory transfer rates
   - Test {info['tech']} compute performance
   - Profile {info['tech']} resource utilization

5. **Integration Tests:**
   - Test with core interfaces from {new_id - 3}-Core task
   - Verify proper {info['tech']} lifecycle management
   - Test with real {platform} {info['tech']} workloads
   - Validate {platform} deployment scenarios

**Hardware Requirements:**
- {info['requirements']}
- Real {info['tech']} hardware for all tests
- NO emulation or simulation

Original test strategy (adapted for {info['tech']}):
{original_task.get('testStrategy', 'No test strategy available')}
""",
        "subtasks": []
    }

def restructure_tasks(tasks_data):
    """Restructure tasks 69-88 into platform-specific tasks"""
    original_tasks = {}
    new_tasks = []
    
    # Find and store original tasks 69-88
    for task in tasks_data.get("master", {}).get("tasks", []):
        task_id = task.get("id")
        if isinstance(task_id, int) and 69 <= task_id <= 88:
            original_tasks[task_id] = task
    
    print(f"Found {len(original_tasks)} tasks to restructure: {sorted(original_tasks.keys())}")
    
    # Create new task ID counter starting after 88
    new_id_counter = 89
    
    # For each original task, create 4 new tasks
    for task_id in sorted(original_tasks.keys()):
        original_task = original_tasks[task_id]
        
        print(f"Restructuring task {task_id}: {original_task.get('title', 'Unknown')}")
        
        # Create 4 new tasks for each original task
        core_id = new_id_counter
        linux_id = new_id_counter + 1
        macos_id = new_id_counter + 2
        windows_id = new_id_counter + 3
        
        # Create core task (interfaces only)
        core_task = create_core_task(original_task, core_id)
        new_tasks.append(core_task)
        
        # Create platform-specific tasks
        linux_task = create_platform_task(
            original_task, linux_id, "Linux", "CUDA", 
            "internal/gpu/linux/cuda/"
        )
        new_tasks.append(linux_task)
        
        macos_task = create_platform_task(
            original_task, macos_id, "macOS", "Metal",
            "internal/gpu/macos/metal/"
        )
        new_tasks.append(macos_task)
        
        windows_task = create_platform_task(
            original_task, windows_id, "Windows", "DirectML",
            "internal/gpu/windows/directml/"
        )
        new_tasks.append(windows_task)
        
        new_id_counter += 4
        
        print(f"  Created tasks: {core_id}-Core, {linux_id}-Linux, {macos_id}-macOS, {windows_id}-Windows")
    
    return new_tasks, original_tasks

def update_dependencies(new_tasks, original_tasks):
    """Update dependencies to point to new task structure"""
    # Create mapping from old task IDs to new core task IDs
    old_to_new_core = {}
    
    # Build mapping: original task ID -> new core task ID
    task_index = 0
    for old_id in sorted(original_tasks.keys()):
        core_id = 89 + (task_index * 4)  # Core task is first of each group of 4
        old_to_new_core[old_id] = core_id
        task_index += 1
    
    print(f"Dependency mapping: {old_to_new_core}")
    
    # Update dependencies in new tasks
    for task in new_tasks:
        if "dependencies" in task and task["dependencies"]:
            updated_deps = []
            for dep in task["dependencies"]:
                if dep in old_to_new_core:
                    # Map to new core task
                    updated_deps.append(old_to_new_core[dep])
                    print(f"  Updated dependency: {dep} -> {old_to_new_core[dep]} in task {task['id']}")
                elif dep < 69:
                    # Keep dependencies on tasks before 69 unchanged
                    updated_deps.append(dep)
                # Skip dependencies on tasks 69-88 that weren't in our mapping
            task["dependencies"] = updated_deps
    
    return new_tasks

def main():
    """Main function to restructure the tasks"""
    input_file = ".taskmaster/tasks/tasks.json"
    output_file = ".taskmaster/tasks/tasks_restructured.json"
    
    print("Loading tasks.json...")
    try:
        with open(input_file, 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Error: {input_file} not found")
        return 1
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
        return 1
    
    print("Restructuring tasks 69-88...")
    new_tasks, original_tasks = restructure_tasks(data)
    
    print("Updating dependencies...")
    new_tasks = update_dependencies(new_tasks, original_tasks)
    
    # Remove original tasks 69-88 from the task list
    if "master" in data and "tasks" in data["master"]:
        data["master"]["tasks"] = [
            task for task in data["master"]["tasks"] 
            if not (isinstance(task.get("id"), int) and 69 <= task.get("id") <= 88)
        ]
        
        # Add new tasks
        data["master"]["tasks"].extend(new_tasks)
        
        print(f"Removed {len(original_tasks)} original tasks")
        print(f"Added {len(new_tasks)} new platform-specific tasks")
    
    # Update metadata
    if "master" in data and "metadata" in data["master"]:
        data["master"]["metadata"]["lastModified"] = datetime.now().isoformat()
        data["master"]["metadata"]["description"] = "Restructured GPU tasks 69-88 into platform-specific implementations"
    
    print(f"Saving restructured tasks to {output_file}...")
    try:
        with open(output_file, 'w') as f:
            json.dump(data, f, indent=2)
        print("✅ Task restructuring completed successfully!")
        print(f"📁 New file: {output_file}")
        print("\n🔄 Next steps:")
        print("1. Review the restructured tasks")
        print("2. Replace original tasks.json with restructured version")
        print("3. Begin platform-specific development on respective platforms")
        return 0
    except Exception as e:
        print(f"Error saving file: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 