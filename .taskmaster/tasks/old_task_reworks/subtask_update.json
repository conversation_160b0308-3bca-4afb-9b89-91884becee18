{"task_52": {"subtasks": [{"id": 1, "title": "Chart Data Formats for UI", "description": "Define and implement standardized data formats for UI chart components", "details": "Create ChartDataAPI struct for UI integration. Implement data transformation functions for various chart types. Define JSON schemas for chart data exchange. Plan real-time update strategy for live charts.", "status": "pending", "dependencies": [], "parentTaskId": 52, "tags": ["ui_interface", "charts", "api", "visualization", "data-formats"], "estimated_hours": 3}]}, "task_55": {"subtasks": [{"id": 1, "title": "Report Generation API for UI", "description": "Create REST API endpoints for report generation and management in future UI", "details": "Define API endpoints: POST /api/reports/generate, GET /api/reports/list, GET /api/reports/download/{id}. Create ReportAPI struct for UI integration. Implement report template management and scheduling system.", "status": "pending", "dependencies": [], "parentTaskId": 55, "tags": ["ui_interface", "reports", "api", "templates", "html"], "estimated_hours": 4}]}, "task_56": {"subtasks": [{"id": 1, "title": "Dashboard Backend API for UI", "description": "Create REST API endpoints for dashboard data and control in future UI", "details": "Define API endpoints: GET /api/dashboard/status, POST /api/dashboard/control, GET /api/dashboard/metrics. Create DashboardAPI struct for UI integration. Implement efficient data structures for UI consumption.", "status": "todo", "dependencies": [], "parentTaskId": 56, "tags": ["ui_interface", "dashboard", "api", "websocket", "real-time"], "estimated_hours": 6}]}}