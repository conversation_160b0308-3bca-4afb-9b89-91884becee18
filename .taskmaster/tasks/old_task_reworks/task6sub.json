{"task_6_subtasks": {"id": 6, "title": "CLI Interface Implementation", "description": "Build Linux CLI binary with platform-specific GPU support and server capabilities", "status": "pending", "dependencies": [1], "priority": "high", "type": "interface", "details": "Linux CLI server that can run as daemon, accept remote work, execute tests with GPU acceleration, and stream results back to controllers. Complete rework needed from previous Mac implementation.", "subtasks": [{"id": "6.1", "title": "CLI Command Structure Setup", "description": "Implement basic CLI command parsing and help system", "status": "pending", "dependencies": [], "details": "Set up cobra/cli library, implement basic commands: --help, --version, start, stop, status, run. Command routing and argument parsing. Code may be partially written - check codebase and adapt for Linux."}, {"id": "6.2", "title": "Server Daemon Mode Implementation", "description": "Implement background daemon mode for CLI server", "status": "pending", "dependencies": ["6.1"], "details": "neuralmeter-server start/stop/status commands. Process management, PID files, daemon lifecycle, signal handling for graceful shutdown. Code may be partially written - check codebase and adapt for Linux."}, {"id": "6.3", "title": "Remote API Server Setup", "description": "Implement HTTP/gRPC server for receiving work from controllers", "status": "pending", "dependencies": ["6.2"], "details": "REST API endpoints and/or gRPC server. Accept test plans, job submission, status queries, health checks from remote controller nodes. Code may be partially written - check codebase and adapt for Linux."}, {"id": "6.4", "title": "Job Queue Integration", "description": "Integrate with job queue system for handling remote requests", "status": "pending", "dependencies": ["6.3", 13], "details": "Connect API server to job queue (Task 13). Queue incoming test requests, job scheduling, priority handling, concurrent job execution. Code may be partially written - check codebase and adapt for Linux."}, {"id": "6.5", "title": "Worker Registration System", "description": "Implement worker registration with master/controller nodes", "status": "pending", "dependencies": ["6.4"], "details": "Auto-discovery of master nodes, worker capability reporting (GPU specs, memory, CPU), heartbeat system, health status reporting. Code may be partially written - check codebase and adapt for Linux."}, {"id": "6.6", "title": "Test Execution Integration", "description": "Integrate CLI with test execution engine for running received jobs", "status": "pending", "dependencies": ["6.5", 51], "details": "Connect to test execution engine (Task 51). Execute received test plans, handle test lifecycle, progress tracking, error handling. Code may be partially written - check codebase and adapt for Linux."}, {"id": "6.7", "title": "Result Streaming Implementation", "description": "Implement real-time result streaming back to controllers", "status": "pending", "dependencies": ["6.6", 52], "details": "Stream test results, metrics, and progress back to controller nodes. WebSocket/gRPC streaming, result aggregation (Task 52), real-time updates. Code may be partially written - check codebase and adapt for Linux."}, {"id": "6.8", "title": "GPU Command Integration", "description": "Integrate CLI with GPU detection and management functions", "status": "pending", "dependencies": ["6.7", 69], "details": "neuralmeter gpu list/status/benchmark commands. Integration with GPU detection (Task 69), GPU status reporting, GPU resource management. Code may be partially written - check codebase and adapt for Linux."}, {"id": "6.9", "title": "Configuration Management Integration", "description": "Integrate CLI with configuration loading and validation", "status": "pending", "dependencies": ["6.8", 42], "details": "Configuration file loading, environment variables, validation. neuralmeter config show/validate commands. Integration with config system (Task 42). Code may be partially written - check codebase and adapt for Linux."}, {"id": "6.10", "title": "Output Formatting and Logging", "description": "Implement output formatting, logging, and error handling for CLI", "status": "pending", "dependencies": ["6.9"], "details": "JSON/text output formats, structured logging, error messages, exit codes. User-friendly CLI output and comprehensive logging for debugging. Code may be partially written - check codebase and adapt for Linux."}]}}