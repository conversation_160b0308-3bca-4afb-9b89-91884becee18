{
    "project": "NeuralMeter",
    "description": "GPU-Accelerated Load Testing Platform in Go",
    "version": "1.0.0",
    "created": "2025-01-21",
    "phases": [
      {
        "phase": 1,
        "name": "Foundation",
        "duration": "Week 1-2",
        "description": "Core project structure and basic functionality"
      },
      {
        "phase": 2,
        "name": "Scale and Optimize",
        "duration": "Week 3-4",
        "description": "Achieve high-scale performance targets"
      },
      {
        "phase": 3,
        "name": "GPU Integration",
        "duration": "Week 5-7",
        "description": "Add AI-powered payload generation capabilities"
      },
      {
        "phase": 4,
        "name": "Advanced Features",
        "duration": "Week 8-10",
        "description": "Production-grade features and enterprise capabilities"
      },
      {
        "phase": 5,
        "name": "Deployment and Documentation",
        "duration": "Week 11-12",
        "description": "Production deployment and comprehensive documentation"
      }
    ],
    "tasks": [
      {
        "id": 1,
        "title": "Go Project Structure Setup",
        "description": "Initialize Go module and create project directory structure",
        "details": "Create neuralmeter Go module with proper directory structure:\n- cmd/ for binaries (orchestrator, worker, gpu-generator)\n- internal/ for private packages\n- pkg/ for public packages\n- api/proto/ for gRPC definitions\n- configs/ for test plans\n- deployments/ for Docker/K8s files\n\nInitialize go.mod with:\n```\nmodule github.com/neuralmeter/neuralmeter\ngo 1.21\n```",
        "complexity": 3,
        "priority": "high",
        "status": "ready",
        "phase": 1,
        "dependencies": [],
        "acceptance_criteria": [
          "Go module initialized with go.mod",
          "Directory structure created according to spec",
          "Basic README.md with project overview",
          "Makefile with build targets",
          ".gitignore configured for Go projects"
        ]
      },
      {
        "id": 2,
        "title": "Configuration Management System",
        "description": "Implement YAML configuration parser and environment variable support",
        "details": "Create configuration system in internal/config/:\n```go\ntype Config struct {\n    Server ServerConfig `yaml:\"server\"`\n    Worker WorkerConfig `yaml:\"worker\"`\n    GPU    GPUConfig    `yaml:\"gpu\"`\n    Metrics MetricsConfig `yaml:\"metrics\"`\n}\n\nfunc LoadConfig(path string) (*Config, error) {\n    // Load from YAML file\n    // Override with environment variables\n}\n```",
        "complexity": 4,
        "priority": "high",
        "status": "ready",
        "phase": 1,
        "dependencies": [1],
        "acceptance_criteria": [
          "YAML configuration loading implemented",
          "Environment variable override support",
          "Configuration validation",
          "Default configuration values",
          "Unit tests for configuration loading"
        ]
      },
      {
        "id": 3,
        "title": "HTTP Connection Pool Implementation",
        "description": "Create optimized HTTP client with connection pooling",
        "details": "Implement in pkg/client/http.go:\n```go\ntype ClientConfig struct {\n    MaxIdleConns        int\n    MaxIdleConnsPerHost int\n    IdleConnTimeout     time.Duration\n    ResponseHeaderTimeout time.Duration\n    TLSHandshakeTimeout time.Duration\n}\n\ntype HTTPClient struct {\n    client *http.Client\n    config *ClientConfig\n}\n\nfunc NewHTTPClient(config *ClientConfig) *HTTPClient {\n    transport := &http.Transport{\n        MaxIdleConns:        config.MaxIdleConns,\n        MaxIdleConnsPerHost: config.MaxIdleConnsPerHost,\n        IdleConnTimeout:     config.IdleConnTimeout,\n    }\n    // Return configured client\n}\n```",
        "complexity": 5,
        "priority": "high",
        "status": "ready",
        "phase": 1,
        "dependencies": [1],
        "acceptance_criteria": [
          "Connection pool with configurable parameters",
          "HTTP/2 support enabled",
          "Keep-alive optimizations",
          "Connection reuse metrics",
          "Benchmarks showing performance improvement"
        ]
      },
      {
        "id": 4,
        "title": "HTTP Methods Implementation",
        "description": "Implement all HTTP methods (GET, POST, PUT, DELETE, PATCH)",
        "details": "Add to pkg/client/http.go:\n```go\ntype Request struct {\n    Method  string\n    URL     string\n    Headers map[string]string\n    Body    []byte\n    Timeout time.Duration\n}\n\ntype Response struct {\n    StatusCode int\n    Headers    map[string]string\n    Body       []byte\n    Duration   time.Duration\n    Error      error\n}\n\nfunc (c *HTTPClient) Execute(req *Request) (*Response, error) {\n    // Generic execution method\n}\n\nfunc (c *HTTPClient) Get(url string, headers map[string]string) (*Response, error)\nfunc (c *HTTPClient) Post(url string, body []byte, headers map[string]string) (*Response, error)\n// Additional methods...\n```",
        "complexity": 4,
        "priority": "high",
        "status": "ready",
        "phase": 1,
        "dependencies": [3],
        "acceptance_criteria": [
          "All HTTP methods implemented",
          "Request/Response structs defined",
          "Header management working",
          "Timeout handling implemented",
          "Unit tests for each method"
        ]
      },
      {
        "id": 5,
        "title": "HTTP Error Handling and Retry Logic",
        "description": "Implement comprehensive error handling and retry mechanism",
        "details": "Add retry logic with exponential backoff:\n```go\ntype RetryConfig struct {\n    MaxRetries int\n    InitialDelay time.Duration\n    MaxDelay time.Duration\n    Multiplier float64\n}\n\nfunc (c *HTTPClient) ExecuteWithRetry(req *Request, config *RetryConfig) (*Response, error) {\n    var lastErr error\n    delay := config.InitialDelay\n    \n    for i := 0; i <= config.MaxRetries; i++ {\n        resp, err := c.Execute(req)\n        if err == nil && resp.StatusCode < 500 {\n            return resp, nil\n        }\n        \n        lastErr = err\n        if i < config.MaxRetries {\n            time.Sleep(delay)\n            delay = time.Duration(float64(delay) * config.Multiplier)\n            if delay > config.MaxDelay {\n                delay = config.MaxDelay\n            }\n        }\n    }\n    \n    return nil, fmt.Errorf(\"max retries exceeded: %w\", lastErr)\n}\n```",
        "complexity": 5,
        "priority": "high",
        "status": "blocked",
        "phase": 1,
        "dependencies": [3, 4],
        "acceptance_criteria": [
          "Retry logic with exponential backoff",
          "Circuit breaker pattern implemented",
          "Error categorization (network, timeout, server)",
          "Retry metrics collection",
          "Configurable retry policies"
        ]
      },
      {
        "id": 6,
        "title": "Job Queue Data Structure",
        "description": "Implement job queue with priority support",
        "details": "Create in internal/workload/job.go:\n```go\ntype Job struct {\n    ID       string\n    Type     string\n    Request  *Request\n    Priority int\n    Result   chan JobResult\n    Created  time.Time\n    Retries  int\n    MaxRetries int\n}\n\ntype JobResult struct {\n    Job      *Job\n    Response *Response\n    Duration time.Duration\n    Error    error\n}\n\ntype JobQueue struct {\n    jobs     chan Job\n    capacity int\n    metrics  *QueueMetrics\n}\n\nfunc (jq *JobQueue) Enqueue(job Job) error {\n    select {\n    case jq.jobs <- job:\n        atomic.AddInt64(&jq.metrics.Enqueued, 1)\n        return nil\n    default:\n        return fmt.Errorf(\"queue is full\")\n    }\n}\n```",
        "complexity": 4,
        "priority": "high",
        "status": "ready",
        "phase": 1,
        "dependencies": [1],
        "acceptance_criteria": [
          "Thread-safe job queue implementation",
          "Priority queue support",
          "Queue metrics (size, throughput)",
          "Job result channel mechanism",
          "Queue overflow handling"
        ]
      },
      {
        "id": 7,
        "title": "Worker Implementation - Core",
        "description": "Implement basic worker goroutine functionality",
        "details": "Create worker core in internal/workload/worker.go:\n```go\ntype Worker struct {\n    ID       int\n    Pool     *WorkerPool\n    JobQueue chan Job\n    Quit     chan bool\n    Client   *HTTPClient\n    Status   WorkerStatus\n}\n\nfunc (w *Worker) Start() {\n    go func() {\n        for {\n            select {\n            case job := <-w.JobQueue:\n                w.processJob(job)\n            case <-w.Quit:\n                w.Status = WorkerStopped\n                return\n            }\n        }\n    }()\n}\n\nfunc (w *Worker) processJob(job Job) {\n    w.Status = WorkerBusy\n    resp, err := w.Client.Execute(job.Request)\n    job.Result <- JobResult{Job: &job, Response: resp, Error: err}\n    w.Status = WorkerIdle\n}\n```",
        "complexity": 5,
        "priority": "high",
        "status": "blocked",
        "phase": 1,
        "dependencies": [3, 4, 6],
        "acceptance_criteria": [
          "Worker goroutine lifecycle management",
          "Job processing implementation",
          "Worker status tracking",
          "Graceful shutdown support",
          "Worker-level metrics"
        ]
      },
      {
        "id": 8,
        "title": "Worker Pool Manager",
        "description": "Implement worker pool with dynamic scaling",
        "details": "Create pool manager in internal/workload/pool.go:\n```go\ntype WorkerPool struct {\n    workers    []*Worker\n    jobQueue   chan Job\n    maxWorkers int\n    minWorkers int\n    client     *HTTPClient\n    metrics    *PoolMetrics\n    mu         sync.RWMutex\n}\n\nfunc (wp *WorkerPool) Start() {\n    // Initialize minimum workers\n    for i := 0; i < wp.minWorkers; i++ {\n        worker := NewWorker(i, wp, wp.client)\n        wp.workers = append(wp.workers, worker)\n        worker.Start()\n    }\n    \n    // Start auto-scaler\n    go wp.autoScale()\n}\n\nfunc (wp *WorkerPool) autoScale() {\n    ticker := time.NewTicker(10 * time.Second)\n    defer ticker.Stop()\n    \n    for range ticker.C {\n        queueSize := len(wp.jobQueue)\n        workerCount := len(wp.workers)\n        \n        if queueSize > workerCount*10 && workerCount < wp.maxWorkers {\n            wp.scaleUp()\n        } else if queueSize < workerCount*2 && workerCount > wp.minWorkers {\n            wp.scaleDown()\n        }\n    }\n}\n```",
        "complexity": 6,
        "priority": "high",
        "status": "blocked",
        "phase": 1,
        "dependencies": [6, 7],
        "acceptance_criteria": [
          "Dynamic worker pool scaling",
          "Load-based auto-scaling logic",
          "Worker lifecycle management",
          "Pool metrics and monitoring",
          "Thread-safe operations"
        ]
      },
      {
        "id": 9,
        "title": "Worker Pool Auto-Scaling Algorithm",
        "description": "Implement advanced auto-scaling based on queue depth and response times",
        "details": "Enhanced auto-scaling in internal/workload/autoscale.go:\n```go\ntype AutoScaler struct {\n    pool              *WorkerPool\n    targetQueueDepth  int\n    targetResponseTime time.Duration\n    scaleUpThreshold  float64\n    scaleDownThreshold float64\n}\n\nfunc (as *AutoScaler) calculateOptimalWorkers() int {\n    avgQueueDepth := as.pool.metrics.AvgQueueDepth()\n    avgResponseTime := as.pool.metrics.AvgResponseTime()\n    \n    queueRatio := float64(avgQueueDepth) / float64(as.targetQueueDepth)\n    timeRatio := float64(avgResponseTime) / float64(as.targetResponseTime)\n    \n    scaleFactor := math.Max(queueRatio, timeRatio)\n    currentWorkers := len(as.pool.workers)\n    \n    if scaleFactor > as.scaleUpThreshold {\n        return int(float64(currentWorkers) * scaleFactor)\n    } else if scaleFactor < as.scaleDownThreshold {\n        return int(float64(currentWorkers) * scaleFactor)\n    }\n    \n    return currentWorkers\n}\n```",
        "complexity": 7,
        "priority": "medium",
        "status": "blocked",
        "phase": 2,
        "dependencies": [8],
        "acceptance_criteria": [
          "Multi-factor scaling decisions",
          "Predictive scaling based on trends",
          "Configurable thresholds",
          "Scaling cooldown periods",
          "Performance impact metrics"
        ]
      },
      {
        "id": 10,
        "title": "Metrics Core Data Structures",
        "description": "Implement metrics collection system foundation",
        "details": "Create in internal/metrics/metrics.go:\n```go\ntype Metrics struct {\n    mu         sync.RWMutex\n    counters   map[string]*Counter\n    gauges     map[string]*Gauge\n    histograms map[string]*Histogram\n    timers     map[string]*Timer\n}\n\ntype Counter struct {\n    value int64\n    name  string\n    tags  map[string]string\n}\n\nfunc (c *Counter) Inc() {\n    atomic.AddInt64(&c.value, 1)\n}\n\nfunc (c *Counter) Add(delta int64) {\n    atomic.AddInt64(&c.value, delta)\n}\n\ntype Timer struct {\n    mu        sync.RWMutex\n    durations []time.Duration\n    name      string\n}\n\nfunc (t *Timer) Record(d time.Duration) {\n    t.mu.Lock()\n    t.durations = append(t.durations, d)\n    t.mu.Unlock()\n}\n```",
        "complexity": 4,
        "priority": "high",
        "status": "ready",
        "phase": 1,
        "dependencies": [1],
        "acceptance_criteria": [
          "Thread-safe metrics types",
          "Counter, Gauge, Histogram, Timer implementations",
          "Tag support for metrics",
          "Memory-efficient storage",
          "Atomic operations where applicable"
        ]
      },
      {
        "id": 11,
        "title": "Prometheus Integration",
        "description": "Integrate metrics with Prometheus client library",
        "details": "Implement Prometheus exporter in internal/metrics/prometheus.go:\n```go\nimport (\n    \"github.com/prometheus/client_golang/prometheus\"\n    \"github.com/prometheus/client_golang/prometheus/promhttp\"\n)\n\ntype PrometheusExporter struct {\n    registry   *prometheus.Registry\n    counters   map[string]prometheus.Counter\n    gauges     map[string]prometheus.Gauge\n    histograms map[string]prometheus.Histogram\n}\n\nfunc (pe *PrometheusExporter) ExportCounter(name string, value int64, tags map[string]string) {\n    counter, exists := pe.counters[name]\n    if !exists {\n        counter = prometheus.NewCounter(prometheus.CounterOpts{\n            Name: name,\n            ConstLabels: tags,\n        })\n        pe.registry.MustRegister(counter)\n        pe.counters[name] = counter\n    }\n    counter.Set(float64(value))\n}\n\nfunc (pe *PrometheusExporter) Handler() http.Handler {\n    return promhttp.HandlerFor(pe.registry, promhttp.HandlerOpts{})\n}\n```",
        "complexity": 5,
        "priority": "high",
        "status": "blocked",
        "phase": 1,
        "dependencies": [10],
        "acceptance_criteria": [
          "Prometheus client integration",
          "Metric type mapping",
          "HTTP endpoint for scraping",
          "Custom registry support",
          "Label/tag conversion"
        ]
      },
      {
        "id": 12,
        "title": "Metrics Aggregation Engine",
        "description": "Implement distributed metrics aggregation across nodes",
        "details": "Create aggregation in internal/metrics/aggregator.go:\n```go\ntype MetricsAggregator struct {\n    sources    []MetricsSource\n    interval   time.Duration\n    aggregated *Metrics\n}\n\ntype MetricsSource interface {\n    GetMetrics() *Metrics\n    NodeID() string\n}\n\nfunc (ma *MetricsAggregator) Start() {\n    ticker := time.NewTicker(ma.interval)\n    go func() {\n        for range ticker.C {\n            ma.aggregate()\n        }\n    }()\n}\n\nfunc (ma *MetricsAggregator) aggregate() {\n    // Collect from all sources\n    // Merge counters (sum)\n    // Merge gauges (average or latest)\n    // Merge histograms (combine buckets)\n    // Merge timers (percentiles)\n}\n```",
        "complexity": 6,
        "priority": "medium",
        "status": "blocked",
        "phase": 2,
        "dependencies": [10, 11],
        "acceptance_criteria": [
          "Multi-node metric collection",
          "Aggregation strategies per metric type",
          "Time-window based aggregation",
          "Efficient memory usage",
          "Concurrent aggregation support"
        ]
      },
      {
        "id": 13,
        "title": "YAML Test Plan Structure Definition",
        "description": "Define YAML schema for test plans",
        "details": "Create schema documentation in docs/testplan-schema.yaml:\n```yaml\n# Test Plan Schema\nname: string # Test plan name\nduration: duration # Total test duration (e.g., \"5m\")\nconcurrency: int # Number of concurrent users\nramp_up: duration # Ramp-up period\n\nscenarios:\n  - name: string # Scenario name\n    weight: float # Probability weight (0.0-1.0)\n    requests:\n      - method: string # HTTP method\n        url: string # Target URL\n        headers: # Optional headers\n          key: value\n        body: string # Optional request body\n        timeout: duration # Request timeout\n        assertions: # Response validation\n          - type: string # status_code, response_time, body_contains\n            operator: string # eq, lt, gt, contains\n            value: any # Expected value\n        variables: # Extract variables from response\n          - name: string # Variable name\n            jsonpath: string # JSONPath expression\n            regex: string # Regex pattern\n            header: string # Header name\n\nglobal: # Global settings\n  base_url: string\n  headers:\n    key: value\n  timeout: duration\n  retry_count: int\n  retry_delay: duration\n```",
        "complexity": 3,
        "priority": "high",
        "status": "ready",
        "phase": 1,
        "dependencies": [1],
        "acceptance_criteria": [
          "Complete YAML schema documentation",
          "Example test plans",
          "Validation rules defined",
          "Schema versioning strategy",
          "Migration path for schema changes"
        ]
      },
      {
        "id": 14,
        "title": "Go Structs for Test Plan",
        "description": "Implement Go structs matching YAML schema",
        "details": "Create in internal/testplan/types.go:\n```go\npackage testplan\n\nimport \"time\"\n\ntype TestPlan struct {\n    Name        string        `yaml:\"name\" json:\"name\"`\n    Duration    time.Duration `yaml:\"duration\" json:\"duration\"`\n    Concurrency int           `yaml:\"concurrency\" json:\"concurrency\"`\n    RampUp      time.Duration `yaml:\"ramp_up\" json:\"ramp_up\"`\n    Scenarios   []Scenario    `yaml:\"scenarios\" json:\"scenarios\"`\n    Global      GlobalConfig  `yaml:\"global,omitempty\" json:\"global,omitempty\"`\n}\n\ntype Scenario struct {\n    Name     string    `yaml:\"name\" json:\"name\"`\n    Weight   float64   `yaml:\"weight\" json:\"weight\"`\n    Requests []Request `yaml:\"requests\" json:\"requests\"`\n}\n\ntype Request struct {\n    Method     string            `yaml:\"method\" json:\"method\"`\n    URL        string            `yaml:\"url\" json:\"url\"`\n    Headers    map[string]string `yaml:\"headers,omitempty\" json:\"headers,omitempty\"`\n    Body       string            `yaml:\"body,omitempty\" json:\"body,omitempty\"`\n    Timeout    time.Duration     `yaml:\"timeout,omitempty\" json:\"timeout,omitempty\"`\n    Assertions []Assertion       `yaml:\"assertions,omitempty\" json:\"assertions,omitempty\"`\n    Variables  []Variable        `yaml:\"variables,omitempty\" json:\"variables,omitempty\"`\n}\n\ntype Assertion struct {\n    Type     string      `yaml:\"type\" json:\"type\"`\n    Target   string      `yaml:\"target\" json:\"target\"`\n    Operator string      `yaml:\"operator\" json:\"operator\"`\n    Value    interface{} `yaml:\"value\" json:\"value\"`\n}\n\ntype Variable struct {\n    Name     string `yaml:\"name\" json:\"name\"`\n    JSONPath string `yaml:\"jsonpath,omitempty\" json:\"jsonpath,omitempty\"`\n    Regex    string `yaml:\"regex,omitempty\" json:\"regex,omitempty\"`\n    Header   string `yaml:\"header,omitempty\" json:\"header,omitempty\"`\n}\n\ntype GlobalConfig struct {\n    BaseURL    string            `yaml:\"base_url,omitempty\" json:\"base_url,omitempty\"`\n    Headers    map[string]string `yaml:\"headers,omitempty\" json:\"headers,omitempty\"`\n    Timeout    time.Duration     `yaml:\"timeout,omitempty\" json:\"timeout,omitempty\"`\n    RetryCount int               `yaml:\"retry_count,omitempty\" json:\"retry_count,omitempty\"`\n    RetryDelay time.Duration     `yaml:\"retry_delay,omitempty\" json:\"retry_delay,omitempty\"`\n}\n```",
        "complexity": 3,
        "priority": "high",
        "status": "blocked",
        "phase": 1,
        "dependencies": [13],
        "acceptance_criteria": [
          "Complete struct definitions",
          "YAML and JSON tags",
          "Proper type handling for durations",
          "Validation methods",
          "String representation methods"
        ]
      },
      {
        "id": 15,
        "title": "YAML Parser Implementation",
        "description": "Implement YAML parsing and validation logic",
        "details": "Create parser in internal/testplan/parser.go:\n```go\nimport (\n    \"fmt\"\n    \"io/ioutil\"\n    \"gopkg.in/yaml.v3\"\n)\n\ntype Parser struct {\n    validators []Validator\n}\n\ntype Validator interface {\n    Validate(plan *TestPlan) error\n}\n\nfunc NewParser() *Parser {\n    return &Parser{\n        validators: []Validator{\n            &DurationValidator{},\n            &ScenarioWeightValidator{},\n            &URLValidator{},\n        },\n    }\n}\n\nfunc (p *Parser) ParseFile(path string) (*TestPlan, error) {\n    data, err := ioutil.ReadFile(path)\n    if err != nil {\n        return nil, fmt.Errorf(\"failed to read file: %w\", err)\n    }\n    \n    return p.Parse(data)\n}\n\nfunc (p *Parser) Parse(data []byte) (*TestPlan, error) {\n    var plan TestPlan\n    \n    if err := yaml.Unmarshal(data, &plan); err != nil {\n        return nil, fmt.Errorf(\"failed to parse YAML: %w\", err)\n    }\n    \n    // Run validators\n    for _, validator := range p.validators {\n        if err := validator.Validate(&plan); err != nil {\n            return nil, fmt.Errorf(\"validation failed: %w\", err)\n        }\n    }\n    \n    // Apply global config to requests\n    p.applyGlobalConfig(&plan)\n    \n    return &plan, nil\n}\n\nfunc (p *Parser) applyGlobalConfig(plan *TestPlan) {\n    for i := range plan.Scenarios {\n        for j := range plan.Scenarios[i].Requests {\n            req := &plan.Scenarios[i].Requests[j]\n            \n            // Apply base URL\n            if plan.Global.BaseURL != \"\" && !strings.HasPrefix(req.URL, \"http\") {\n                req.URL = plan.Global.BaseURL + req.URL\n            }\n            \n            // Apply global headers\n            if req.Headers == nil {\n                req.Headers = make(map[string]string)\n            }\n            for k, v := range plan.Global.Headers {\n                if _, exists := req.Headers[k]; !exists {\n                    req.Headers[k] = v\n                }\n            }\n            \n            // Apply global timeout\n            if req.Timeout == 0 && plan.Global.Timeout > 0 {\n                req.Timeout = plan.Global.Timeout\n            }\n        }\n    }\n}\n```",
        "complexity": 5,
        "priority": "high",
        "status": "blocked",
        "phase": 1,
        "dependencies": [14],
        "acceptance_criteria": [
          "YAML parsing with error handling",
          "Validation framework",
          "Global config application",
          "Environment variable substitution",
          "Comprehensive error messages"
        ]
      },
      {
        "id": 16,
        "title": "Test Plan Validators",
        "description": "Implement validation rules for test plans",
        "details": "Create validators in internal/testplan/validators.go:\n```go\ntype DurationValidator struct{}\n\nfunc (dv *DurationValidator) Validate(plan *TestPlan) error {\n    if plan.Duration <= 0 {\n        return fmt.Errorf(\"test duration must be positive\")\n    }\n    if plan.RampUp > plan.Duration {\n        return fmt.Errorf(\"ramp-up period cannot exceed test duration\")\n    }\n    return nil\n}\n\ntype ScenarioWeightValidator struct{}\n\nfunc (swv *ScenarioWeightValidator) Validate(plan *TestPlan) error {\n    totalWeight := 0.0\n    for _, scenario := range plan.Scenarios {\n        if scenario.Weight < 0 || scenario.Weight > 1 {\n            return fmt.Errorf(\"scenario weight must be between 0 and 1\")\n        }\n        totalWeight += scenario.Weight\n    }\n    \n    if math.Abs(totalWeight-1.0) > 0.001 {\n        return fmt.Errorf(\"scenario weights must sum to 1.0, got %.3f\", totalWeight)\n    }\n    \n    return nil\n}\n\ntype URLValidator struct{}\n\nfunc (uv *URLValidator) Validate(plan *TestPlan) error {\n    for _, scenario := range plan.Scenarios {\n        for _, request := range scenario.Requests {\n            if request.URL == \"\" {\n                return fmt.Errorf(\"request URL cannot be empty\")\n            }\n            \n            // Validate URL format\n            if !strings.HasPrefix(request.URL, \"http\") && \n               !strings.HasPrefix(request.URL, \"/\") && \n               plan.Global.BaseURL == \"\" {\n                return fmt.Errorf(\"invalid URL: %s (no base URL configured)\", request.URL)\n            }\n        }\n    }\n    return nil\n}\n```",
        "complexity": 4,
        "priority": "medium",
        "status": "blocked",
        "phase": 1,
        "dependencies": [14, 15],
        "acceptance_criteria": [
          "Duration validation",
          "Scenario weight validation",
          "URL format validation",
          "Method validation",
          "Custom validator support"
        ]
      },
      {
        "id": 17,
        "title": "Orchestrator Core Implementation",
        "description": "Implement master node orchestrator service",
        "details": "Create orchestrator in cmd/orchestrator/main.go and internal/orchestrator/:\n```go\ntype Orchestrator struct {\n    config      *config.Config\n    server      *http.Server\n    grpcServer  *grpc.Server\n    workers     map[string]*WorkerNode\n    gpuNodes    map[string]*GPUNode\n    tests       map[string]*TestExecution\n    metrics     *metrics.Collector\n    mu          sync.RWMutex\n}\n\nfunc NewOrchestrator(config *config.Config) (*Orchestrator, error) {\n    o := &Orchestrator{\n        config:   config,\n        workers:  make(map[string]*WorkerNode),\n        gpuNodes: make(map[string]*GPUNode),\n        tests:    make(map[string]*TestExecution),\n        metrics:  metrics.NewCollector(),\n    }\n    \n    // Initialize HTTP server\n    o.server = &http.Server{\n        Addr:    config.Server.ListenAddr,\n        Handler: o.setupRoutes(),\n    }\n    \n    // Initialize gRPC server\n    o.grpcServer = grpc.NewServer()\n    pb.RegisterOrchestratorServer(o.grpcServer, o)\n    \n    return o, nil\n}\n\nfunc (o *Orchestrator) setupRoutes() http.Handler {\n    mux := http.NewServeMux()\n    mux.HandleFunc(\"/api/v1/tests\", o.handleTests)\n    mux.HandleFunc(\"/api/v1/workers\", o.handleWorkers)\n    mux.HandleFunc(\"/api/v1/status\", o.handleStatus)\n    mux.Handle(\"/metrics\", o.metrics.Handler())\n    return mux\n}\n```",
        "complexity": 6,
        "priority": "high",
        "status": "blocked",
        "phase": 1,
        "dependencies": [2, 10, 11],
        "acceptance_criteria": [
          "HTTP API endpoints implemented",
          "gRPC service for node communication",
          "Worker registration and tracking",
          "Test execution management",
          "Health check endpoints"
        ]
      },
      {
        "id": 18,
        "title": "Orchestrator API Handlers",
        "description": "Implement REST API handlers for test management",
        "details": "Add API handlers in internal/orchestrator/handlers.go:\n```go\nfunc (o *Orchestrator) handleTests(w http.ResponseWriter, r *http.Request) {\n    switch r.Method {\n    case http.MethodPost:\n        o.createTest(w, r)\n    case http.MethodGet:\n        o.listTests(w, r)\n    default:\n        http.Error(w, \"Method not allowed\", http.StatusMethodNotAllowed)\n    }\n}\n\nfunc (o *Orchestrator) createTest(w http.ResponseWriter, r *http.Request) {\n    var req CreateTestRequest\n    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {\n        http.Error(w, err.Error(), http.StatusBadRequest)\n        return\n    }\n    \n    // Parse test plan\n    parser := testplan.NewParser()\n    plan, err := parser.Parse([]byte(req.TestPlan))\n    if err != nil {\n        http.Error(w, fmt.Sprintf(\"Invalid test plan: %v\", err), http.StatusBadRequest)\n        return\n    }\n    \n    // Create test execution\n    test := &TestExecution{\n        ID:       uuid.New().String(),\n        Plan:     plan,\n        Status:   TestStatusPending,\n        Created:  time.Now(),\n    }\n    \n    o.mu.Lock()\n    o.tests[test.ID] = test\n    o.mu.Unlock()\n    \n    // Start test execution\n    go o.executeTest(test)\n    \n    w.Header().Set(\"Content-Type\", \"application/json\")\n    json.NewEncoder(w).Encode(test)\n}\n\nfunc (o *Orchestrator) executeTest(test *TestExecution) {\n    test.Status = TestStatusRunning\n    test.StartTime = time.Now()\n    \n    // Distribute work to workers\n    ctx, cancel := context.WithTimeout(context.Background(), test.Plan.Duration)\n    defer cancel()\n    \n    var wg sync.WaitGroup\n    workersPerScenario := o.calculateWorkerDistribution(test.Plan)\n    \n    for scenarioIdx, workerCount := range workersPerScenario {\n        scenario := test.Plan.Scenarios[scenarioIdx]\n        \n        for i := 0; i < workerCount; i++ {\n            wg.Add(1)\n            go func(s Scenario) {\n                defer wg.Done()\n                o.executeScenario(ctx, test.ID, s)\n            }(scenario)\n        }\n    }\n    \n    wg.Wait()\n    \n    test.Status = TestStatusCompleted\n    test.EndTime = time.Now()\n}\n```",
        "complexity": 5,
        "priority": "high",
        "status": "blocked",
        "phase": 1,
        "dependencies": [17],
        "acceptance_criteria": [
          "Create test endpoint",
          "List tests endpoint",
          "Get test status endpoint",
          "Stop test endpoint",
          "Test execution orchestration"
        ]
      },
      {
        "id": 19,
        "title": "Worker Registration Protocol",
        "description": "Implement gRPC protocol for worker registration",
        "details": "Create protocol in api/proto/worker.proto and implementation:\n```protobuf\nsyntax = \"proto3\";\n\npackage neuralmeter.v1;\n\nservice WorkerService {\n    rpc Register(RegisterRequest) returns (RegisterResponse);\n    rpc Heartbeat(HeartbeatRequest) returns (HeartbeatResponse);\n    rpc ExecuteWorkload(WorkloadRequest) returns (stream WorkloadResult);\n}\n\nmessage RegisterRequest {\n    string worker_id = 1;\n    int32 max_concurrency = 2;\n    map<string, string> capabilities = 3;\n}\n\nmessage RegisterResponse {\n    bool success = 1;\n    string message = 2;\n    string assigned_id = 3;\n}\n```\n\nImplement in internal/protocol/worker.go:\n```go\nfunc (o *Orchestrator) Register(ctx context.Context, req *pb.RegisterRequest) (*pb.RegisterResponse, error) {\n    o.mu.Lock()\n    defer o.mu.Unlock()\n    \n    worker := &WorkerNode{\n        ID:             req.WorkerId,\n        MaxConcurrency: int(req.MaxConcurrency),\n        Capabilities:   req.Capabilities,\n        LastHeartbeat:  time.Now(),\n        Status:         WorkerStatusActive,\n    }\n    \n    o.workers[worker.ID] = worker\n    \n    return &pb.RegisterResponse{\n        Success:    true,\n        AssignedId: worker.ID,\n    }, nil\n}\n```",
        "complexity": 5,
        "priority": "high",
        "status": "blocked",
        "phase": 1,
        "dependencies": [17],
        "acceptance_criteria": [
          "gRPC service definition",
          "Worker registration flow",
          "Heartbeat mechanism",
          "Capability negotiation",
          "Connection management"
        ]
      },
      {
        "id": 20,
        "title": "Test Execution Coordinator",
        "description": "Implement test execution distribution logic",
        "details": "Create coordinator in internal/orchestrator/coordinator.go:\n```go\ntype TestCoordinator struct {\n    orchestrator *Orchestrator\n    scheduler    *WorkloadScheduler\n}\n\ntype WorkloadScheduler struct {\n    workers []WorkerNode\n    queue   *PriorityQueue\n}\n\nfunc (tc *TestCoordinator) DistributeWorkload(test *TestExecution) error {\n    // Calculate total virtual users\n    totalVUs := test.Plan.Concurrency\n    \n    // Get available workers\n    workers := tc.orchestrator.getActiveWorkers()\n    if len(workers) == 0 {\n        return fmt.Errorf(\"no active workers available\")\n    }\n    \n    // Calculate VUs per worker\n    vusPerWorker := totalVUs / len(workers)\n    remainder := totalVUs % len(workers)\n    \n    // Create workload assignments\n    assignments := make([]*WorkloadAssignment, 0, len(workers))\n    \n    for i, worker := range workers {\n        assignment := &WorkloadAssignment{\n            WorkerID: worker.ID,\n            TestID:   test.ID,\n            VUs:      vusPerWorker,\n        }\n        \n        // Distribute remainder\n        if i < remainder {\n            assignment.VUs++\n        }\n        \n        // Calculate scenario distribution\n        assignment.Scenarios = tc.distributeScenarios(test.Plan.Scenarios, assignment.VUs)\n        \n        assignments = append(assignments, assignment)\n    }\n    \n    // Send assignments to workers\n    for _, assignment := range assignments {\n        if err := tc.sendAssignment(assignment); err != nil {\n            return fmt.Errorf(\"failed to send assignment to worker %s: %w\", assignment.WorkerID, err)\n        }\n    }\n    \n    return nil\n}\n\nfunc (tc *TestCoordinator) distributeScenarios(scenarios []Scenario, vus int) []ScenarioAssignment {\n    assignments := make([]ScenarioAssignment, 0, len(scenarios))\n    \n    for _, scenario := range scenarios {\n        scenarioVUs := int(float64(vus) * scenario.Weight)\n        if scenarioVUs > 0 {\n            assignments = append(assignments, ScenarioAssignment{\n                Scenario: scenario,\n                VUs:      scenarioVUs,\n            })\n        }\n    }\n    \n    return assignments\n}\n```",
        "complexity": 6,
        "priority": "high",
        "status": "blocked",
        "phase": 2,
        "dependencies": [17, 18, 19],
        "acceptance_criteria": [
          "Workload distribution algorithm",
          "Virtual user allocation",
          "Scenario weight handling",
          "Worker capacity consideration",
          "Load balancing logic"
        ]
      },
      {
        "id": 21,
        "title": "CLI Interface Foundation",
        "description": "Implement command-line interface for NeuralMeter",
        "details": "Create CLI in cmd/neuralmeter/main.go:\n```go\npackage main\n\nimport (\n    \"fmt\"\n    \"os\"\n    \n    \"github.com/spf13/cobra\"\n)\n\nvar rootCmd = &cobra.Command{\n    Use:   \"neuralmeter\",\n    Short: \"GPU-accelerated load testing platform\",\n    Long:  `NeuralMeter is a high-performance load testing tool with AI workload simulation`,\n}\n\nvar runCmd = &cobra.Command{\n    Use:   \"run [test-file]\",\n    Short: \"Run a load test\",\n    Args:  cobra.ExactArgs(1),\n    RunE:  runTest,\n}\n\nvar (\n    orchestratorURL string\n    outputFormat    string\n    watch           bool\n)\n\nfunc init() {\n    rootCmd.AddCommand(runCmd)\n    rootCmd.AddCommand(statusCmd)\n    rootCmd.AddCommand(stopCmd)\n    \n    runCmd.Flags().StringVarP(&orchestratorURL, \"orchestrator\", \"o\", \"http://localhost:8080\", \"Orchestrator URL\")\n    runCmd.Flags().StringVarP(&outputFormat, \"format\", \"f\", \"json\", \"Output format (json, table, yaml)\")\n    runCmd.Flags().BoolVarP(&watch, \"watch\", \"w\", false, \"Watch test progress\")\n}\n\nfunc runTest(cmd *cobra.Command, args []string) error {\n    testFile := args[0]\n    \n    // Read test plan\n    data, err := os.ReadFile(testFile)\n    if err != nil {\n        return fmt.Errorf(\"failed to read test file: %w\", err)\n    }\n    \n    // Create test via API\n    client := NewAPIClient(orchestratorURL)\n    test, err := client.CreateTest(data)\n    if err != nil {\n        return fmt.Errorf(\"failed to create test: %w\", err)\n    }\n    \n    fmt.Printf(\"Test created: %s\\n\", test.ID)\n    \n    if watch {\n        return watchTestProgress(client, test.ID)\n    }\n    \n    return nil\n}\n\nfunc main() {\n    if err := rootCmd.Execute(); err != nil {\n        fmt.Fprintln(os.Stderr, err)\n        os.Exit(1)\n    }\n}\n```",
        "complexity": 4,
        "priority": "medium",
        "status": "blocked",
        "phase": 1,
        "dependencies": [17, 18],
        "acceptance_criteria": [
          "Basic CLI structure with cobra",
          "Run test command",
          "Status command",
          "Stop test command",
          "Output formatting options"
        ]
      },
      {
        "id": 22,
        "title": "Worker Node Core Implementation",
        "description": "Implement worker node service",
        "details": "Create worker in cmd/worker/main.go and internal/worker/:\n```go\ntype WorkerNode struct {\n    id           string\n    config       *config.WorkerConfig\n    orchestrator *OrchestratorClient\n    pool         *WorkerPool\n    metrics      *metrics.Collector\n    grpcClient   pb.OrchestratorClient\n}\n\nfunc NewWorkerNode(config *config.WorkerConfig) (*WorkerNode, error) {\n    w := &WorkerNode{\n        id:      generateWorkerID(),\n        config:  config,\n        metrics: metrics.NewCollector(),\n    }\n    \n    // Connect to orchestrator\n    conn, err := grpc.Dial(config.OrchestratorAddr, grpc.WithInsecure())\n    if err != nil {\n        return nil, fmt.Errorf(\"failed to connect to orchestrator: %w\", err)\n    }\n    \n    w.grpcClient = pb.NewOrchestratorClient(conn)\n    \n    // Initialize worker pool\n    httpClient := client.NewHTTPClient(&config.HTTP)\n    w.pool = workload.NewWorkerPool(config.MaxConcurrency, httpClient)\n    \n    return w, nil\n}\n\nfunc (w *WorkerNode) Start() error {\n    // Register with orchestrator\n    resp, err := w.grpcClient.Register(context.Background(), &pb.RegisterRequest{\n        WorkerId:       w.id,\n        MaxConcurrency: int32(w.config.MaxConcurrency),\n        Capabilities: map[string]string{\n            \"version\": \"1.0.0\",\n            \"protocols\": \"http,https\",\n        },\n    })\n    \n    if err != nil {\n        return fmt.Errorf(\"failed to register: %w\", err)\n    }\n    \n    if !resp.Success {\n        return fmt.Errorf(\"registration failed: %s\", resp.Message)\n    }\n    \n    // Start worker pool\n    w.pool.Start()\n    \n    // Start heartbeat\n    go w.heartbeatLoop()\n    \n    // Start workload receiver\n    go w.receiveWorkloads()\n    \n    // Start metrics server\n    go w.startMetricsServer()\n    \n    return nil\n}\n\nfunc (w *WorkerNode) heartbeatLoop() {\n    ticker := time.NewTicker(10 * time.Second)\n    defer ticker.Stop()\n    \n    for range ticker.C {\n        _, err := w.grpcClient.Heartbeat(context.Background(), &pb.HeartbeatRequest{\n            WorkerId: w.id,\n            Status: &pb.WorkerStatus{\n                ActiveVUs:    int32(w.pool.ActiveWorkers()),\n                QueuedJobs:   int32(w.pool.QueuedJobs()),\n                CompletedJobs: w.metrics.Counter(\"jobs_completed\").Value(),\n            },\n        })\n        \n        if err != nil {\n            log.Printf(\"Heartbeat failed: %v\", err)\n        }\n    }\n}\n```",
        "complexity": 6,
        "priority": "high",
        "status": "blocked",
        "phase": 1,
        "dependencies": [7, 8, 19],
        "acceptance_criteria": [
          "Worker node initialization",
          "Orchestrator registration",
          "Heartbeat mechanism",
          "Workload reception",
          "Metrics reporting"
        ]
      },
      {
        "id": 23,
        "title": "Workload Execution Engine",
        "description": "Implement workload execution on worker nodes",
        "details": "Create execution engine in internal/worker/executor.go:\n```go\ntype WorkloadExecutor struct {\n    worker   *WorkerNode\n    pool     *WorkerPool\n    scenarios map[string]*ScenarioExecutor\n}\n\ntype ScenarioExecutor struct {\n    scenario  Scenario\n    vus       int\n    rampUp    time.Duration\n    duration  time.Duration\n    client    *HTTPClient\n    metrics   *ScenarioMetrics\n}\n\nfunc (we *WorkloadExecutor) Execute(assignment *pb.WorkloadAssignment) error {\n    ctx, cancel := context.WithTimeout(context.Background(), time.Duration(assignment.Duration))\n    defer cancel()\n    \n    // Create scenario executors\n    for _, scenarioAssignment := range assignment.Scenarios {\n        executor := &ScenarioExecutor{\n            scenario: scenarioAssignment.Scenario,\n            vus:      int(scenarioAssignment.VUs),\n            rampUp:   time.Duration(assignment.RampUp),\n            duration: time.Duration(assignment.Duration),\n            client:   we.pool.HTTPClient(),\n            metrics:  NewScenarioMetrics(scenarioAssignment.Scenario.Name),\n        }\n        \n        we.scenarios[scenarioAssignment.Scenario.Name] = executor\n        go executor.Run(ctx)\n    }\n    \n    // Wait for completion\n    <-ctx.Done()\n    \n    return nil\n}\n\nfunc (se *ScenarioExecutor) Run(ctx context.Context) {\n    // Implement ramp-up\n    rampUpDelay := se.rampUp / time.Duration(se.vus)\n    \n    for i := 0; i < se.vus; i++ {\n        go se.runVirtualUser(ctx, i)\n        \n        if i < se.vus-1 {\n            time.Sleep(rampUpDelay)\n        }\n    }\n}\n\nfunc (se *ScenarioExecutor) runVirtualUser(ctx context.Context, vuID int) {\n    for {\n        select {\n        case <-ctx.Done():\n            return\n        default:\n            // Execute scenario requests in sequence\n            for _, request := range se.scenario.Requests {\n                if err := se.executeRequest(ctx, request); err != nil {\n                    se.metrics.RecordError(err)\n                }\n                \n                // Check if we should continue\n                if ctx.Err() != nil {\n                    return\n                }\n            }\n        }\n    }\n}\n\nfunc (se *ScenarioExecutor) executeRequest(ctx context.Context, request Request) error {\n    start := time.Now()\n    \n    // Build HTTP request\n    httpReq := &client.Request{\n        Method:  request.Method,\n        URL:     request.URL,\n        Headers: request.Headers,\n        Body:    []byte(request.Body),\n        Timeout: request.Timeout,\n    }\n    \n    // Execute request\n    resp, err := se.client.Execute(httpReq)\n    duration := time.Since(start)\n    \n    // Record metrics\n    se.metrics.RecordResponse(resp, duration)\n    \n    if err != nil {\n        return err\n    }\n    \n    // Run assertions\n    for _, assertion := range request.Assertions {\n        if err := se.runAssertion(assertion, resp); err != nil {\n            se.metrics.RecordAssertionFailure(assertion, err)\n        }\n    }\n    \n    return nil\n}\n```",
        "complexity": 7,
        "priority": "high",
        "status": "blocked",
        "phase": 2,
        "dependencies": [22],
        "acceptance_criteria": [
          "Virtual user lifecycle management",
          "Scenario execution logic",
          "Request sequencing",
          "Ramp-up implementation",
          "Assertion execution"
        ]
      },
      {
        "id": 24,
        "title": "Response Assertion Engine",
        "description": "Implement assertion validation for responses",
        "details": "Create assertion engine in internal/worker/assertions.go:\n```go\ntype AssertionEngine struct {\n    validators map[string]AssertionValidator\n}\n\ntype AssertionValidator interface {\n    Validate(assertion Assertion, response *Response) error\n}\n\nfunc NewAssertionEngine() *AssertionEngine {\n    ae := &AssertionEngine{\n        validators: make(map[string]AssertionValidator),\n    }\n    \n    // Register built-in validators\n    ae.Register(\"status_code\", &StatusCodeValidator{})\n    ae.Register(\"response_time\", &ResponseTimeValidator{})\n    ae.Register(\"body_contains\", &BodyContainsValidator{})\n    ae.Register(\"json_path\", &JSONPathValidator{})\n    ae.Register(\"header\", &HeaderValidator{})\n    \n    return ae\n}\n\ntype StatusCodeValidator struct{}\n\nfunc (scv *StatusCodeValidator) Validate(assertion Assertion, response *Response) error {\n    expected, ok := assertion.Value.(float64)\n    if !ok {\n        return fmt.Errorf(\"invalid status code value type\")\n    }\n    \n    switch assertion.Operator {\n    case \"eq\":\n        if response.StatusCode != int(expected) {\n            return fmt.Errorf(\"expected status %d, got %d\", int(expected), response.StatusCode)\n        }\n    case \"ne\":\n        if response.StatusCode == int(expected) {\n            return fmt.Errorf(\"expected status not %d, got %d\", int(expected), response.StatusCode)\n        }\n    default:\n        return fmt.Errorf(\"unsupported operator: %s\", assertion.Operator)\n    }\n    \n    return nil\n}\n\ntype ResponseTimeValidator struct{}\n\nfunc (rtv *ResponseTimeValidator) Validate(assertion Assertion, response *Response) error {\n    maxDuration, err := time.ParseDuration(assertion.Value.(string))\n    if err != nil {\n        return fmt.Errorf(\"invalid duration: %w\", err)\n    }\n    \n    switch assertion.Operator {\n    case \"lt\":\n        if response.Duration >= maxDuration {\n            return fmt.Errorf(\"response time %v exceeds %v\", response.Duration, maxDuration)\n        }\n    case \"gt\":\n        if response.Duration <= maxDuration {\n            return fmt.Errorf(\"response time %v below %v\", response.Duration, maxDuration)\n        }\n    default:\n        return fmt.Errorf(\"unsupported operator: %s\", assertion.Operator)\n    }\n    \n    return nil\n}\n\ntype JSONPathValidator struct{}\n\nfunc (jpv *JSONPathValidator) Validate(assertion Assertion, response *Response) error {\n    // Parse JSON response\n    var data interface{}\n    if err := json.Unmarshal(response.Body, &data); err != nil {\n        return fmt.Errorf(\"failed to parse JSON: %w\", err)\n    }\n    \n    // Extract value using JSONPath\n    value, err := jsonpath.Get(assertion.Target, data)\n    if err != nil {\n        return fmt.Errorf(\"JSONPath extraction failed: %w\", err)\n    }\n    \n    // Compare values\n    return compareValues(assertion.Operator, value, assertion.Value)\n}\n```",
        "complexity": 5,
        "priority": "medium",
        "status": "blocked",
        "phase": 2,
        "dependencies": [23],
        "acceptance_criteria": [
          "Status code assertions",
          "Response time assertions",
          "Body content assertions",
          "JSONPath assertions",
          "Header assertions"
        ]
      },
      {
        "id": 25,
        "title": "Variable Extraction System",
        "description": "Implement variable extraction from responses",
        "details": "Create variable system in internal/worker/variables.go:\n```go\ntype VariableExtractor struct {\n    extractors map[string]Extractor\n    store      *VariableStore\n}\n\ntype Extractor interface {\n    Extract(response *Response, config Variable) (interface{}, error)\n}\n\ntype VariableStore struct {\n    mu    sync.RWMutex\n    vars  map[string]interface{}\n}\n\nfunc NewVariableExtractor() *VariableExtractor {\n    ve := &VariableExtractor{\n        extractors: make(map[string]Extractor),\n        store:      NewVariableStore(),\n    }\n    \n    // Register extractors\n    ve.Register(\"jsonpath\", &JSONPathExtractor{})\n    ve.Register(\"regex\", &RegexExtractor{})\n    ve.Register(\"header\", &HeaderExtractor{})\n    \n    return ve\n}\n\ntype JSONPathExtractor struct{}\n\nfunc (jpe *JSONPathExtractor) Extract(response *Response, config Variable) (interface{}, error) {\n    var data interface{}\n    if err := json.Unmarshal(response.Body, &data); err != nil {\n        return nil, fmt.Errorf(\"failed to parse JSON: %w\", err)\n    }\n    \n    value, err := jsonpath.Get(config.JSONPath, data)\n    if err != nil {\n        return nil, fmt.Errorf(\"JSONPath extraction failed: %w\", err)\n    }\n    \n    return value, nil\n}\n\ntype RegexExtractor struct{}\n\nfunc (re *RegexExtractor) Extract(response *Response, config Variable) (interface{}, error) {\n    regex, err := regexp.Compile(config.Regex)\n    if err != nil {\n        return nil, fmt.Errorf(\"invalid regex: %w\", err)\n    }\n    \n    matches := regex.FindSubmatch(response.Body)\n    if len(matches) < 2 {\n        return nil, fmt.Errorf(\"regex did not match\")\n    }\n    \n    return string(matches[1]), nil\n}\n\ntype HeaderExtractor struct{}\n\nfunc (he *HeaderExtractor) Extract(response *Response, config Variable) (interface{}, error) {\n    value, exists := response.Headers[config.Header]\n    if !exists {\n        return nil, fmt.Errorf(\"header %s not found\", config.Header)\n    }\n    \n    return value, nil\n}\n\n// Variable substitution in requests\nfunc (ve *VariableExtractor) SubstituteVariables(request *Request) *Request {\n    // Clone request\n    newReq := *request\n    \n    // Substitute in URL\n    newReq.URL = ve.substituteString(newReq.URL)\n    \n    // Substitute in headers\n    newHeaders := make(map[string]string)\n    for k, v := range newReq.Headers {\n        newHeaders[k] = ve.substituteString(v)\n    }\n    newReq.Headers = newHeaders\n    \n    // Substitute in body\n    newReq.Body = ve.substituteString(newReq.Body)\n    \n    return &newReq\n}\n\nfunc (ve *VariableExtractor) substituteString(s string) string {\n    return ve.store.Substitute(s)\n}\n```",
        "complexity": 5,
        "priority": "medium",
        "status": "blocked",
        "phase": 2,
        "dependencies": [23, 24],
        "acceptance_criteria": [
          "JSONPath extraction",
          "Regex extraction",
          "Header extraction",
          "Variable storage",
          "Variable substitution in requests"
        ]
      },
      {
        "id": 26,
        "title": "Performance Optimization - Connection Pooling",
        "description": "Optimize HTTP client connection pooling for 10K users",
        "details": "Enhance connection pooling in pkg/client/pool.go:\n```go\ntype AdvancedHTTPClient struct {\n    clients    []*http.Client\n    poolSize   int\n    nextClient uint32\n    config     *PoolConfig\n}\n\ntype PoolConfig struct {\n    PoolSize            int\n    MaxIdleConns        int\n    MaxIdleConnsPerHost int\n    MaxConnsPerHost     int\n    IdleConnTimeout     time.Duration\n    DisableCompression  bool\n    DisableKeepAlives   bool\n}\n\nfunc NewAdvancedHTTPClient(config *PoolConfig) *AdvancedHTTPClient {\n    if config.PoolSize == 0 {\n        config.PoolSize = runtime.NumCPU()\n    }\n    \n    ahc := &AdvancedHTTPClient{\n        clients:  make([]*http.Client, config.PoolSize),\n        poolSize: config.PoolSize,\n        config:   config,\n    }\n    \n    // Create multiple HTTP clients for better concurrency\n    for i := 0; i < config.PoolSize; i++ {\n        transport := &http.Transport{\n            MaxIdleConns:        config.MaxIdleConns / config.PoolSize,\n            MaxIdleConnsPerHost: config.MaxIdleConnsPerHost,\n            MaxConnsPerHost:     config.MaxConnsPerHost,\n            IdleConnTimeout:     config.IdleConnTimeout,\n            DisableCompression:  config.DisableCompression,\n            DisableKeepAlives:   config.DisableKeepAlives,\n            TLSHandshakeTimeout: 10 * time.Second,\n            ExpectContinueTimeout: 1 * time.Second,\n        }\n        \n        ahc.clients[i] = &http.Client{\n            Transport: transport,\n            Timeout:   30 * time.Second,\n        }\n    }\n    \n    return ahc\n}\n\nfunc (ahc *AdvancedHTTPClient) GetClient() *http.Client {\n    // Round-robin client selection for load distribution\n    idx := atomic.AddUint32(&ahc.nextClient, 1) % uint32(ahc.poolSize)\n    return ahc.clients[idx]\n}\n```",
        "complexity": 6,
        "priority": "high",
        "status": "blocked",
        "phase": 2,
        "dependencies": [3, 4],
        "acceptance_criteria": [
          "Multiple HTTP client instances",
          "Round-robin load distribution",
          "Optimized transport settings",
          "Support for 10K+ concurrent connections",
          "Performance benchmarks"
        ]
      },
      {
        "id": 27,
        "title": "Memory Pool for Request/Response Objects",
        "description": "Implement object pooling to reduce GC pressure",
        "details": "Create memory pool in pkg/client/mempool.go:\n```go\nvar (\n    requestPool = sync.Pool{\n        New: func() interface{} {\n            return &Request{\n                Headers: make(map[string]string),\n            }\n        },\n    }\n    \n    responsePool = sync.Pool{\n        New: func() interface{} {\n            return &Response{\n                Headers: make(map[string]string),\n            }\n        },\n    }\n    \n    bufferPool = sync.Pool{\n        New: func() interface{} {\n            return bytes.NewBuffer(make([]byte, 0, 4096))\n        },\n    }\n)\n\nfunc GetRequest() *Request {\n    req := requestPool.Get().(*Request)\n    req.Reset()\n    return req\n}\n\nfunc PutRequest(req *Request) {\n    req.Reset()\n    requestPool.Put(req)\n}\n\nfunc GetResponse() *Response {\n    resp := responsePool.Get().(*Response)\n    resp.Reset()\n    return resp\n}\n\nfunc PutResponse(resp *Response) {\n    resp.Reset()\n    responsePool.Put(resp)\n}\n\nfunc (r *Request) Reset() {\n    r.Method = \"\"\n    r.URL = \"\"\n    r.Body = r.Body[:0]\n    r.Timeout = 0\n    \n    // Clear headers map\n    for k := range r.Headers {\n        delete(r.Headers, k)\n    }\n}\n\nfunc (r *Response) Reset() {\n    r.StatusCode = 0\n    r.Body = r.Body[:0]\n    r.Duration = 0\n    r.Error = nil\n    \n    // Clear headers map\n    for k := range r.Headers {\n        delete(r.Headers, k)\n    }\n}\n```",
        "complexity": 5,
        "priority": "high",
        "status": "blocked",
        "phase": 2,
        "dependencies": [3, 4],
        "acceptance_criteria": [
          "sync.Pool for Request objects",
          "sync.Pool for Response objects",
          "Buffer pooling for body data",
          "Proper object reset methods",
          "Memory usage reduction metrics"
        ]
      },
      {
        "id": 28,
        "title": "Goroutine Pool Implementation",
        "description": "Implement goroutine pool to control resource usage",
        "details": "Create goroutine pool in internal/workload/gopool.go:\n```go\ntype GoroutinePool struct {\n    maxWorkers int\n    jobQueue   chan func()\n    workerWG   sync.WaitGroup\n    stopCh     chan struct{}\n    metrics    *PoolMetrics\n}\n\ntype PoolMetrics struct {\n    ActiveWorkers   int32\n    QueuedJobs      int32\n    CompletedJobs   int64\n    RejectedJobs    int64\n    AvgJobDuration  time.Duration\n}\n\nfunc NewGoroutinePool(maxWorkers int, queueSize int) *GoroutinePool {\n    gp := &GoroutinePool{\n        maxWorkers: maxWorkers,\n        jobQueue:   make(chan func(), queueSize),\n        stopCh:     make(chan struct{}),\n        metrics:    &PoolMetrics{},\n    }\n    \n    // Start workers\n    for i := 0; i < maxWorkers; i++ {\n        gp.workerWG.Add(1)\n        go gp.worker(i)\n    }\n    \n    return gp\n}\n\nfunc (gp *GoroutinePool) worker(id int) {\n    defer gp.workerWG.Done()\n    atomic.AddInt32(&gp.metrics.ActiveWorkers, 1)\n    defer atomic.AddInt32(&gp.metrics.ActiveWorkers, -1)\n    \n    for {\n        select {\n        case job, ok := <-gp.jobQueue:\n            if !ok {\n                return\n            }\n            \n            atomic.AddInt32(&gp.metrics.QueuedJobs, -1)\n            start := time.Now()\n            \n            // Execute job with panic recovery\n            func() {\n                defer func() {\n                    if r := recover(); r != nil {\n                        log.Printf(\"Worker %d panic: %v\", id, r)\n                    }\n                }()\n                job()\n            }()\n            \n            duration := time.Since(start)\n            atomic.AddInt64(&gp.metrics.CompletedJobs, 1)\n            gp.updateAvgDuration(duration)\n            \n        case <-gp.stopCh:\n            return\n        }\n    }\n}\n\nfunc (gp *GoroutinePool) Submit(job func()) error {\n    select {\n    case gp.jobQueue <- job:\n        atomic.AddInt32(&gp.metrics.QueuedJobs, 1)\n        return nil\n    default:\n        atomic.AddInt64(&gp.metrics.RejectedJobs, 1)\n        return fmt.Errorf(\"job queue is full\")\n    }\n}\n\nfunc (gp *GoroutinePool) SubmitWithTimeout(job func(), timeout time.Duration) error {\n    select {\n    case gp.jobQueue <- job:\n        atomic.AddInt32(&gp.metrics.QueuedJobs, 1)\n        return nil\n    case <-time.After(timeout):\n        atomic.AddInt64(&gp.metrics.RejectedJobs, 1)\n        return fmt.Errorf(\"timeout submitting job\")\n    }\n}\n```",
        "complexity": 6,
        "priority": "high",
        "status": "blocked",
        "phase": 2,
        "dependencies": [7, 8],
        "acceptance_criteria": [
          "Fixed-size goroutine pool",
          "Job queue with backpressure",
          "Panic recovery in workers",
          "Pool metrics collection",
          "Graceful shutdown support"
        ]
      },
      {
        "id": 29,
        "title": "Rate Limiting Implementation",
        "description": "Implement rate limiting for controlled load generation",
        "details": "Create rate limiter in internal/workload/ratelimit.go:\n```go\ntype RateLimiter interface {\n    Wait(ctx context.Context) error\n    SetRate(rps float64)\n    GetRate() float64\n}\n\ntype TokenBucketLimiter struct {\n    limiter  *rate.Limiter\n    mu       sync.RWMutex\n    maxBurst int\n}\n\nfunc NewTokenBucketLimiter(rps float64, burst int) *TokenBucketLimiter {\n    return &TokenBucketLimiter{\n        limiter:  rate.NewLimiter(rate.Limit(rps), burst),\n        maxBurst: burst,\n    }\n}\n\nfunc (tbl *TokenBucketLimiter) Wait(ctx context.Context) error {\n    return tbl.limiter.Wait(ctx)\n}\n\nfunc (tbl *TokenBucketLimiter) SetRate(rps float64) {\n    tbl.mu.Lock()\n    defer tbl.mu.Unlock()\n    tbl.limiter.SetLimit(rate.Limit(rps))\n}\n\n// Adaptive rate limiter that adjusts based on response times\ntype AdaptiveRateLimiter struct {\n    base           RateLimiter\n    targetLatency  time.Duration\n    currentRate    float64\n    maxRate        float64\n    minRate        float64\n    adjustInterval time.Duration\n    metrics        *metrics.Collector\n}\n\nfunc NewAdaptiveRateLimiter(config *AdaptiveConfig) *AdaptiveRateLimiter {\n    arl := &AdaptiveRateLimiter{\n        base:           NewTokenBucketLimiter(config.InitialRate, config.Burst),\n        targetLatency:  config.TargetLatency,\n        currentRate:    config.InitialRate,\n        maxRate:        config.MaxRate,\n        minRate:        config.MinRate,\n        adjustInterval: config.AdjustInterval,\n        metrics:        metrics.NewCollector(),\n    }\n    \n    go arl.adjustLoop()\n    return arl\n}\n\nfunc (arl *AdaptiveRateLimiter) adjustLoop() {\n    ticker := time.NewTicker(arl.adjustInterval)\n    defer ticker.Stop()\n    \n    for range ticker.C {\n        avgLatency := arl.metrics.Timer(\"response_time\").Mean()\n        \n        if avgLatency > arl.targetLatency {\n            // Decrease rate if latency is too high\n            arl.currentRate *= 0.9\n        } else if avgLatency < arl.targetLatency*0.8 {\n            // Increase rate if latency is low\n            arl.currentRate *= 1.1\n        }\n        \n        // Apply bounds\n        if arl.currentRate > arl.maxRate {\n            arl.currentRate = arl.maxRate\n        } else if arl.currentRate < arl.minRate {\n            arl.currentRate = arl.minRate\n        }\n        \n        arl.base.SetRate(arl.currentRate)\n    }\n}\n```",
        "complexity": 5,
        "priority": "medium",
        "status": "blocked",
        "phase": 2,
        "dependencies": [23],
        "acceptance_criteria": [
          "Token bucket rate limiter",
          "Adaptive rate limiting",
          "Per-scenario rate limits",
          "Rate adjustment based on latency",
          "Rate limit metrics"
        ]
      },
      {
        "id": 30,
        "title": "Load Pattern Generators",
        "description": "Implement various load patterns (constant, ramp, spike, wave)",
        "details": "Create load patterns in internal/workload/patterns.go:\n```go\ntype LoadPattern interface {\n    GetRate(elapsed time.Duration) float64\n    Duration() time.Duration\n}\n\ntype ConstantLoad struct {\n    rate     float64\n    duration time.Duration\n}\n\nfunc (cl *ConstantLoad) GetRate(elapsed time.Duration) float64 {\n    if elapsed > cl.duration {\n        return 0\n    }\n    return cl.rate\n}\n\ntype RampUpLoad struct {\n    startRate float64\n    endRate   float64\n    rampTime  time.Duration\n    duration  time.Duration\n}\n\nfunc (rul *RampUpLoad) GetRate(elapsed time.Duration) float64 {\n    if elapsed > rul.duration {\n        return 0\n    }\n    \n    if elapsed < rul.rampTime {\n        // Linear ramp\n        progress := float64(elapsed) / float64(rul.rampTime)\n        return rul.startRate + (rul.endRate-rul.startRate)*progress\n    }\n    \n    return rul.endRate\n}\n\ntype SpikeLoad struct {\n    baseRate  float64\n    spikeRate float64\n    interval  time.Duration\n    spikeDur  time.Duration\n    duration  time.Duration\n}\n\nfunc (sl *SpikeLoad) GetRate(elapsed time.Duration) float64 {\n    if elapsed > sl.duration {\n        return 0\n    }\n    \n    cycleTime := elapsed % sl.interval\n    if cycleTime < sl.spikeDur {\n        return sl.spikeRate\n    }\n    \n    return sl.baseRate\n}\n\ntype WaveLoad struct {\n    minRate   float64\n    maxRate   float64\n    period    time.Duration\n    duration  time.Duration\n}\n\nfunc (wl *WaveLoad) GetRate(elapsed time.Duration) float64 {\n    if elapsed > wl.duration {\n        return 0\n    }\n    \n    // Sine wave pattern\n    cycle := float64(elapsed) / float64(wl.period)\n    amplitude := (wl.maxRate - wl.minRate) / 2\n    midpoint := (wl.maxRate + wl.minRate) / 2\n    \n    return midpoint + amplitude*math.Sin(2*math.Pi*cycle)\n}\n\n// Pattern executor\ntype PatternExecutor struct {\n    pattern    LoadPattern\n    limiter    RateLimiter\n    startTime  time.Time\n}\n\nfunc (pe *PatternExecutor) Execute(ctx context.Context, job func()) error {\n    ticker := time.NewTicker(100 * time.Millisecond)\n    defer ticker.Stop()\n    \n    for {\n        select {\n        case <-ctx.Done():\n            return ctx.Err()\n        case <-ticker.C:\n            elapsed := time.Since(pe.startTime)\n            rate := pe.pattern.GetRate(elapsed)\n            \n            if rate == 0 {\n                return nil\n            }\n            \n            pe.limiter.SetRate(rate)\n            \n            if err := pe.limiter.Wait(ctx); err != nil {\n                return err\n            }\n            \n            go job()\n        }\n    }\n}\n```",
        "complexity": 5,
        "priority": "medium",
        "status": "blocked",
        "phase": 2,
        "dependencies": [29],
        "acceptance_criteria": [
          "Constant load pattern",
          "Ramp-up/down patterns",
          "Spike load patterns",
          "Wave/sine patterns",
          "Custom pattern support"
        ]
      },
      {
        "id": 31,
        "title": "Grafana Dashboard Templates",
        "description": "Create Grafana dashboard JSON templates for metrics visualization",
        "details": "Create dashboard templates in deployments/grafana/dashboards/:\n```json\n{\n  \"dashboard\": {\n    \"title\": \"NeuralMeter Performance Dashboard\",\n    \"panels\": [\n      {\n        \"title\": \"Request Rate\",\n        \"targets\": [\n          {\n            \"expr\": \"rate(neuralmeter_requests_total[1m])\",\n            \"legendFormat\": \"{{worker}}\"\n          }\n        ],\n        \"type\": \"graph\"\n      },\n      {\n        \"title\": \"Response Time Percentiles\",\n        \"targets\": [\n          {\n            \"expr\": \"histogram_quantile(0.50, neuralmeter_response_time_bucket)\",\n            \"legendFormat\": \"p50\"\n          },\n          {\n            \"expr\": \"histogram_quantile(0.95, neuralmeter_response_time_bucket)\",\n            \"legendFormat\": \"p95\"\n          },\n          {\n            \"expr\": \"histogram_quantile(0.99, neuralmeter_response_time_bucket)\",\n            \"legendFormat\": \"p99\"\n          }\n        ],\n        \"type\": \"graph\"\n      },\n      {\n        \"title\": \"Error Rate\",\n        \"targets\": [\n          {\n            \"expr\": \"rate(neuralmeter_errors_total[1m])\",\n            \"legendFormat\": \"{{error_type}}\"\n          }\n        ],\n        \"type\": \"graph\"\n      },\n      {\n        \"title\": \"Active Virtual Users\",\n        \"targets\": [\n          {\n            \"expr\": \"sum(neuralmeter_active_vus) by (worker)\",\n            \"legendFormat\": \"{{worker}}\"\n          }\n        ],\n        \"type\": \"graph\"\n      },\n      {\n        \"title\": \"Worker CPU Usage\",\n        \"targets\": [\n          {\n            \"expr\": \"neuralmeter_worker_cpu_percent\",\n            \"legendFormat\": \"{{worker}}\"\n          }\n        ],\n        \"type\": \"graph\"\n      },\n      {\n        \"title\": \"Worker Memory Usage\",\n        \"targets\": [\n          {\n            \"expr\": \"neuralmeter_worker_memory_bytes / 1024 / 1024\",\n            \"legendFormat\": \"{{worker}} MB\"\n          }\n        ],\n        \"type\": \"graph\"\n      }\n    ]\n  }\n}\n```\n\nAlso create provisioning configuration:\n```yaml\napiVersion: 1\n\nproviders:\n  - name: 'NeuralMeter'\n    orgId: 1\n    folder: ''\n    type: file\n    disableDeletion: false\n    updateIntervalSeconds: 10\n    options:\n      path: /etc/grafana/provisioning/dashboards\n```",
        "complexity": 3,
        "priority": "medium",
        "status": "blocked",
        "phase": 2,
        "dependencies": [11],
        "acceptance_criteria": [
          "Main performance dashboard",
          "Worker health dashboard",
          "Test execution dashboard",
          "GPU metrics dashboard",
          "Alert rules configuration"
        ]
      },
      {
        "id": 32,
        "title": "Docker Container Build Files",
        "description": "Create Dockerfiles for all components",
        "details": "Create Dockerfiles in deployments/docker/:\n\nDockerfile.orchestrator:\n```dockerfile\nFROM golang:1.21-alpine AS builder\n\nWORKDIR /app\nCOPY go.mod go.sum ./\nRUN go mod download\n\nCOPY . .\nRUN CGO_ENABLED=0 GOOS=linux go build -ldflags=\"-s -w\" \\\n    -o neuralmeter-orchestrator cmd/orchestrator/main.go\n\nFROM alpine:latest\nRUN apk --no-cache add ca-certificates\n\nWORKDIR /root/\nCOPY --from=builder /app/neuralmeter-orchestrator .\nCOPY configs/orchestrator.yaml ./configs/\n\nEXPOSE 8080 9090\nCMD [\"./neuralmeter-orchestrator\"]\n```\n\nDockerfile.worker:\n```dockerfile\nFROM golang:1.21-alpine AS builder\n\nWORKDIR /app\nCOPY go.mod go.sum ./\nRUN go mod download\n\nCOPY . .\nRUN CGO_ENABLED=0 GOOS=linux go build -ldflags=\"-s -w\" \\\n    -o neuralmeter-worker cmd/worker/main.go\n\nFROM alpine:latest\nRUN apk --no-cache add ca-certificates\n\nWORKDIR /root/\nCOPY --from=builder /app/neuralmeter-worker .\nCOPY configs/worker.yaml ./configs/\n\nEXPOSE 9091\nCMD [\"./neuralmeter-worker\"]\n```\n\nDockerfile.gpu-generator:\n```dockerfile\nFROM nvidia/cuda:11.8.0-runtime-ubuntu22.04 AS builder\n\nRUN apt-get update && apt-get install -y \\\n    golang-1.21 \\\n    build-essential \\\n    && rm -rf /var/lib/apt/lists/*\n\nENV PATH=/usr/lib/go-1.21/bin:$PATH\n\nWORKDIR /app\nCOPY go.mod go.sum ./\nRUN go mod download\n\nCOPY . .\nRUN CGO_ENABLED=1 GOOS=linux go build -ldflags=\"-s -w\" \\\n    -o neuralmeter-gpu-generator cmd/gpu-generator/main.go\n\nFROM nvidia/cuda:11.8.0-runtime-ubuntu22.04\n\nWORKDIR /root/\nCOPY --from=builder /app/neuralmeter-gpu-generator .\nCOPY configs/gpu-generator.yaml ./configs/\n\nEXPOSE 9092\nCMD [\"./neuralmeter-gpu-generator\"]\n```",
        "complexity": 3,
        "priority": "high",
        "status": "blocked",
        "phase": 2,
        "dependencies": [17, 22],
        "acceptance_criteria": [
          "Multi-stage Dockerfiles",
          "Minimal base images",
          "Proper CUDA support for GPU",
          "Configuration mounting",
          "Health check support"
        ]
      },
      {
        "id": 33,
        "title": "Kubernetes Manifests",
        "description": "Create Kubernetes deployment manifests",
        "details": "Create K8s manifests in deployments/k8s/:\n\norchestrator-deployment.yaml:\n```yaml\napiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: neuralmeter-orchestrator\n  labels:\n    app: neuralmeter\n    component: orchestrator\nspec:\n  replicas: 1\n  selector:\n    matchLabels:\n      app: neuralmeter\n      component: orchestrator\n  template:\n    metadata:\n      labels:\n        app: neuralmeter\n        component: orchestrator\n    spec:\n      containers:\n      - name: orchestrator\n        image: neuralmeter/orchestrator:latest\n        ports:\n        - containerPort: 8080\n          name: http\n        - containerPort: 9090\n          name: grpc\n        env:\n        - name: LOG_LEVEL\n          value: \"info\"\n        resources:\n          requests:\n            memory: \"256Mi\"\n            cpu: \"100m\"\n          limits:\n            memory: \"512Mi\"\n            cpu: \"500m\"\n        livenessProbe:\n          httpGet:\n            path: /health\n            port: 8080\n          initialDelaySeconds: 10\n          periodSeconds: 10\n        readinessProbe:\n          httpGet:\n            path: /ready\n            port: 8080\n          initialDelaySeconds: 5\n          periodSeconds: 5\n---\napiVersion: v1\nkind: Service\nmetadata:\n  name: neuralmeter-orchestrator\nspec:\n  selector:\n    app: neuralmeter\n    component: orchestrator\n  ports:\n  - name: http\n    port: 8080\n    targetPort: 8080\n  - name: grpc\n    port: 9090\n    targetPort: 9090\n```\n\nworker-deployment.yaml:\n```yaml\napiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: neuralmeter-worker\nspec:\n  replicas: 10\n  selector:\n    matchLabels:\n      app: neuralmeter\n      component: worker\n  template:\n    metadata:\n      labels:\n        app: neuralmeter\n        component: worker\n    spec:\n      containers:\n      - name: worker\n        image: neuralmeter/worker:latest\n        env:\n        - name: ORCHESTRATOR_ADDR\n          value: \"neuralmeter-orchestrator:9090\"\n        - name: MAX_CONCURRENCY\n          value: \"10000\"\n        resources:\n          requests:\n            memory: \"1Gi\"\n            cpu: \"1000m\"\n          limits:\n            memory: \"2Gi\"\n            cpu: \"2000m\"\n```",
        "complexity": 4,
        "priority": "high",
        "status": "blocked",
        "phase": 2,
        "dependencies": [32],
        "acceptance_criteria": [
          "Deployment manifests for all components",
          "Service definitions",
          "ConfigMap for configuration",
          "HPA for auto-scaling",
          "Resource limits and requests"
        ]
      },
      {
        "id": 34,
        "title": "Helm Chart Creation",
        "description": "Create Helm chart for easy deployment",
        "details": "Create Helm chart in deployments/helm/neuralmeter/:\n\nChart.yaml:\n```yaml\napiVersion: v2\nname: neuralmeter\ndescription: GPU-Accelerated Load Testing Platform\ntype: application\nversion: 1.0.0\nappVersion: \"1.0.0\"\nkeywords:\n  - load-testing\n  - performance\n  - gpu\n  - ai\nhome: https://github.com/neuralmeter/neuralmeter\nsources:\n  - https://github.com/neuralmeter/neuralmeter\nmaintainers:\n  - name: NeuralMeter Team\n    email: <EMAIL>\n```\n\nvalues.yaml:\n```yaml\norchestrator:\n  replicaCount: 1\n  image:\n    repository: neuralmeter/orchestrator\n    tag: latest\n    pullPolicy: IfNotPresent\n  resources:\n    requests:\n      memory: 256Mi\n      cpu: 100m\n    limits:\n      memory: 512Mi\n      cpu: 500m\n  service:\n    type: ClusterIP\n    httpPort: 8080\n    grpcPort: 9090\n\nworker:\n  replicaCount: 10\n  image:\n    repository: neuralmeter/worker\n    tag: latest\n    pullPolicy: IfNotPresent\n  maxConcurrency: 10000\n  resources:\n    requests:\n      memory: 1Gi\n      cpu: 1000m\n    limits:\n      memory: 2Gi\n      cpu: 2000m\n  autoscaling:\n    enabled: true\n    minReplicas: 5\n    maxReplicas: 50\n    targetCPUUtilizationPercentage: 70\n\ngpuGenerator:\n  enabled: true\n  replicaCount: 2\n  image:\n    repository: neuralmeter/gpu-generator\n    tag: latest\n    pullPolicy: IfNotPresent\n  resources:\n    limits:\n      nvidia.com/gpu: 1\n  nodeSelector:\n    accelerator: nvidia\n\nprometheus:\n  enabled: true\n  retention: 30d\n\ngrafana:\n  enabled: true\n  adminPassword: admin\n```",
        "complexity": 4,
        "priority": "medium",
        "status": "blocked",
        "phase": 2,
        "dependencies": [33],
        "acceptance_criteria": [
          "Complete Helm chart structure",
          "Config