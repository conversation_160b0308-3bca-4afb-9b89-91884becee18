pipeline {
    agent any
    
    parameters {
        choice(name: 'CLI_TEST_SUITE', choices: ['none', 'connectivity', 'basic', 'load', 'full'], description: 'CLI test suite to run after deployment')
        booleanParam(name: 'SKIP_CLI_TESTS', defaultValue: false, description: 'Skip CLI testing stage')
    }
    
    environment {
        SYSTEM_A_HOST = '*************'
        SYSTEM_B_HOST = '*************'
        JENKINS_CREDENTIALS_ID = 'ssh-credentials'
        CLI_BINARY_NAME = 'neuralmeter'
        DEPLOY_PATH = '/home/<USER>/cli'
    }
    
    stages {
        stage('Checkout Code') {
            steps {
                checkout scm
                echo "✅ Code checked out from Gitea repository"
            }
        }
        
        stage('Build NeuralMeter CLI') {
            steps {
                script {
                    echo "🔨 Building NeuralMeter CLI for Linux..."
                    
                    // Build the CLI binary with CUDA 12.9 support
                    sh """
                        cd cmd/neuralmeter
                        export CGO_CFLAGS="-I/usr/include -I/usr/local/cuda-12.9/include"
                        export CGO_LDFLAGS="-L/usr/local/cuda-12.9/targets/x86_64-linux/lib -Wl,-rpath,/usr/local/cuda-12.9/targets/x86_64-linux/lib"
                        go build -tags cuda -o ${CLI_BINARY_NAME} .
                        ls -la ${CLI_BINARY_NAME}
                    """
                    
                    echo "✅ NeuralMeter CLI built successfully"
                }
            }
        }
        
        stage('Deploy to System A') {
            steps {
                script {
                    echo "🚀 Deploying CLI to System A (CLI Execution Machine)..."
                    
                    sshagent([JENKINS_CREDENTIALS_ID]) {
                        // Create deployment directory
                        sh """
                            ssh -o StrictHostKeyChecking=no neuro@${SYSTEM_A_HOST} 'mkdir -p ${DEPLOY_PATH}'
                        """
                    
                        // Copy binary to System A
                        sh """
                            scp -o StrictHostKeyChecking=no cmd/neuralmeter/${CLI_BINARY_NAME} neuro@${SYSTEM_A_HOST}:${DEPLOY_PATH}/
                        """
                    
                        // Set executable permissions
                        sh """
                            ssh -o StrictHostKeyChecking=no neuro@${SYSTEM_A_HOST} 'chmod +x ${DEPLOY_PATH}/${CLI_BINARY_NAME}'
                        """
                    
                        // Verify deployment
                        sh """
                            ssh -o StrictHostKeyChecking=no neuro@${SYSTEM_A_HOST} '${DEPLOY_PATH}/${CLI_BINARY_NAME} --version || echo "CLI deployed but version check failed"'
                        """
                    }
                    
                    echo "✅ CLI deployed to System A successfully"
                }
            }
        }
        
        stage('Verify System A Setup') {
            steps {
                script {
                    echo "🔍 Verifying System A (CLI Execution Machine) setup..."
                    
                    sshagent([JENKINS_CREDENTIALS_ID]) {
                        // Test Go installation
                        sh """
                            ssh -o StrictHostKeyChecking=no neuro@${SYSTEM_A_HOST} '/usr/local/go/bin/go version'
                        """
                    
                    // Test GPU detection
                        sh """
                            ssh -o StrictHostKeyChecking=no neuro@${SYSTEM_A_HOST} '/usr/bin/nvidia-smi || echo "NVIDIA GPU not detected"'
                        """
                        
                        // Test CLI binary
                        sh """
                            ssh -o StrictHostKeyChecking=no neuro@${SYSTEM_A_HOST} '${DEPLOY_PATH}/${CLI_BINARY_NAME} --help'
                        """
                    }
                    
                    echo "✅ System A verification completed"
                }
            }
        }
        
        stage('Verify System B Services') {
            steps {
                script {
                    echo "🔍 Verifying System B (Target Services) availability..."
                    
                    sshagent([JENKINS_CREDENTIALS_ID]) {
                        // Test nginx service
                        sh """
                            ssh -o StrictHostKeyChecking=no neuro@${SYSTEM_B_HOST} '/bin/systemctl is-active nginx'
                        """
                        
                        // Test web endpoints
                        sh """
                            ssh -o StrictHostKeyChecking=no neuro@${SYSTEM_B_HOST} '/usr/bin/curl -s http://localhost/ | head -1'
                        """
                    
                        sh """
                            ssh -o StrictHostKeyChecking=no neuro@${SYSTEM_B_HOST} '/usr/bin/curl -s http://localhost/api/ | head -1'
                        """
                    
                        sh """
                            ssh -o StrictHostKeyChecking=no neuro@${SYSTEM_B_HOST} '/usr/bin/curl -s http://localhost/health | head -1'
                        """
                    }
                    
                    echo "✅ System B services verified"
                }
            }
        }
        
        stage('Run Basic Load Test') {
            steps {
                script {
                    echo "🧪 Running basic load test from System A against System B..."
                    
                    sshagent([JENKINS_CREDENTIALS_ID]) {
                        // Create a simple test plan
                        sh """
                            ssh -o StrictHostKeyChecking=no neuro@${SYSTEM_A_HOST} 'cat > ${DEPLOY_PATH}/basic-test.yaml << EOF
targets:
  - url: http://${SYSTEM_B_HOST}/
    method: GET
    duration: 30s
    rate: 10
EOF'
                        """
                        
                        // Run the test
                        sh """
                            ssh -o StrictHostKeyChecking=no neuro@${SYSTEM_A_HOST} 'cd ${DEPLOY_PATH} && ./${CLI_BINARY_NAME} run --config basic-test.yaml || echo "Load test completed"'
                        """
                    }
                    
                    echo "✅ Basic load test completed"
                }
            }
        }
        
        stage('Run CLI Tests') {
            when {
                allOf {
                    not { params.SKIP_CLI_TESTS }
                    not { equals expected: 'none', actual: params.CLI_TEST_SUITE }
                }
            }
            steps {
                script {
                    echo "🧪 Running comprehensive CLI tests..."
                    echo "Test Suite: ${params.CLI_TEST_SUITE}"
                    
                    sshagent([JENKINS_CREDENTIALS_ID]) {
                        // Set environment variables for the test script
                        env.TEST_SUITE = params.CLI_TEST_SUITE
                        env.CLI_BINARY_PATH = "${DEPLOY_PATH}/${CLI_BINARY_NAME}"
                        env.SSH_USER = 'neuro'
                        
                        // Run the CLI test script
                        sh './test/cli-scripts/run-cli-tests-from-main-pipeline.sh'
                    }
                    
                    echo "✅ CLI tests completed"
                }
            }
            post {
                always {
                    // Archive CLI test results
                    archiveArtifacts artifacts: 'test-results/**/*', allowEmptyArchive: true
                }
            }
        }
    }
    
    post {
        always {
            echo "🏁 NeuralMeter CLI deployment pipeline completed"
        }
        success {
            script {
                echo "✅ Pipeline successful: CLI deployed and tested"
                echo "📊 System A (${SYSTEM_A_HOST}): CLI execution machine ready"
                echo "🎯 System B (${SYSTEM_B_HOST}): Target services available"
                
                if (!params.SKIP_CLI_TESTS && params.CLI_TEST_SUITE != 'none') {
                    echo "🧪 CLI Tests: ${params.CLI_TEST_SUITE} suite completed"
                }
            }
        }
        failure {
            echo "❌ Pipeline failed - check logs for details"
        }
    }
}