---
alwaysApply: false
---
# Platform-Specific GPU Development Rules

## Overview
This project implements GPU-accelerated load testing with platform-specific implementations. Each platform must be developed on its native OS with real GPU API calls. **NO CPU FALLBACKS, NO MOCKS, NO STUBS.**

## Platform-Specific Development Requirements

### **Linux Development (CUDA)**
- **Required OS**: Linux with NVIDIA GPU
- **Required APIs**: Real CUDA API calls only
- **Directory Structure**: `internal/gpu/linux/cuda/`
- **Build Tags**: `//go:build linux && cuda`
- **Forbidden**: CPU fallbacks, CUDA mocks, stub implementations
- **Required Libraries**: CUDA Toolkit, cuDNN, NVML
- **IDE Setup**: Cursor on Linux with CUDA development environment

### **macOS Development (Metal)**
- **Required OS**: macOS with Metal-capable GPU
- **Required APIs**: Real Metal framework calls only
- **Directory Structure**: `internal/gpu/macos/metal/`
- **Build Tags**: `//go:build darwin && metal`
- **Forbidden**: CPU fallbacks, Metal mocks, stub implementations
- **Required Frameworks**: Metal, MetalKit, MetalPerformanceShaders
- **IDE Setup**: Cursor on macOS with Xcode command line tools

### **Windows Development (DirectML)**
- **Required OS**: Windows with DirectML-capable GPU
- **Required APIs**: Real DirectML API calls only
- **Directory Structure**: `internal/gpu/windows/directml/`
- **Build Tags**: `//go:build windows && directml`
- **Forbidden**: CPU fallbacks, DirectML mocks, stub implementations
- **Required Libraries**: DirectML, DirectX 12, Windows SDK
- **IDE Setup**: Cursor on Windows with Visual Studio Build Tools

## Task Structure Rules

### **Core Tasks (Interfaces Only)**
- **Purpose**: Define interfaces and types only
- **Implementation**: NO actual GPU code
- **Directory**: `internal/core/`
- **Example**: `69-Core`, `70-Core`, `78-Core`, etc.
- **Content**: Go interfaces, type definitions, error types
- **Forbidden**: Any platform-specific implementation

### **Platform Tasks (Real Implementations)**
- **Purpose**: Implement core interfaces with real GPU APIs
- **Naming**: `{TaskID}-{Platform}` (e.g., `69-Linux`, `70-macOS`, `78-Windows`)
- **Dependencies**: Must depend on corresponding Core task
- **Implementation**: Real GPU API calls only
- **Testing**: Must test on native platform with real GPU

## Code Implementation Rules

### **CUDA Implementation (Linux)**
```go
// REQUIRED: Real CUDA API calls
//go:build linux && cuda

import "C"
import "unsafe"

// Example: Real CUDA device detection
func detectCUDADevices() ([]CUDADevice, error) {
    var deviceCount C.int
    result := C.cudaGetDeviceCount(&deviceCount)
    if result != C.cudaSuccess {
        return nil, fmt.Errorf("CUDA error: %v", result)
    }
    // ... real CUDA implementation
}

// FORBIDDEN: CPU fallbacks
func detectCUDADevices() ([]CUDADevice, error) {
    // ❌ NEVER DO THIS
    if !hasCUDA() {
        return detectCPUDevices() // ❌ NO CPU FALLBACK
    }
}
```

### **Metal Implementation (macOS)**
```go
// REQUIRED: Real Metal framework calls
//go:build darwin && metal

/*
#cgo LDFLAGS: -framework Metal -framework Foundation
#include <Metal/Metal.h>
*/
import "C"

// Example: Real Metal device detection
func detectMetalDevices() ([]MetalDevice, error) {
    devices := C.MTLCopyAllDevices()
    if devices == nil {
        return nil, fmt.Errorf("no Metal devices found")
    }
    // ... real Metal implementation
}

// FORBIDDEN: CPU fallbacks
func detectMetalDevices() ([]MetalDevice, error) {
    // ❌ NEVER DO THIS
    if !hasMetalSupport() {
        return simulateGPU() // ❌ NO SIMULATION
    }
}
```

### **DirectML Implementation (Windows)**
```go
// REQUIRED: Real DirectML API calls
//go:build windows && directml

/*
#cgo LDFLAGS: -ldirectml -ld3d12
#include <directml.h>
#include <d3d12.h>
*/
import "C"

// Example: Real DirectML device detection
func detectDirectMLDevices() ([]DirectMLDevice, error) {
    var factory *C.IDXGIFactory4
    hr := C.CreateDXGIFactory2(0, &C.IID_IDXGIFactory4, unsafe.Pointer(&factory))
    if C.FAILED(hr) {
        return nil, fmt.Errorf("DirectML factory creation failed: %v", hr)
    }
    // ... real DirectML implementation
}

// FORBIDDEN: CPU fallbacks
func detectDirectMLDevices() ([]DirectMLDevice, error) {
    // ❌ NEVER DO THIS
    if !hasDirectMLSupport() {
        return []DirectMLDevice{createMockDevice()} // ❌ NO MOCKS
    }
}
```

## Development Workflow Rules

### **1. Platform Isolation**
- **Rule**: Never mix platform implementations in single files
- **Structure**: One platform per directory tree
- **Build Tags**: Strict platform-specific build tags
- **Testing**: Platform-specific test files with same build tags

### **2. No Cross-Platform Compatibility Code**
- **Forbidden**: `runtime.GOOS` checks in GPU code
- **Forbidden**: Interface{} type assertions for platform detection
- **Forbidden**: Conditional compilation within GPU implementations
- **Required**: Clean separation via directory structure and build tags

### **3. Real Hardware Requirements**
- **Linux Tasks**: Must have NVIDIA GPU with CUDA support
- **macOS Tasks**: Must have Metal-capable GPU (Intel/Apple Silicon)
- **Windows Tasks**: Must have DirectML-capable GPU
- **Testing**: All tests must run on real hardware with real APIs

### **4. Error Handling**
- **Required**: Fail fast when GPU not available
- **Required**: Clear error messages indicating missing GPU support
- **Forbidden**: Silent fallbacks to CPU computation
- **Forbidden**: Mock implementations that hide hardware requirements

## File Organization Rules

### **Directory Structure**
```
internal/
├── core/                    # Core interfaces only
│   ├── gpu_interfaces.go
│   ├── types.go
│   └── errors.go
├── gpu/
│   ├── linux/
│   │   └── cuda/           # CUDA implementations
│   │       ├── detection/
│   │       ├── memory/
│   │       ├── streams/
│   │       └── hal/
│   ├── macos/
│   │   └── metal/          # Metal implementations
│   │       ├── detection/
│   │       ├── memory/
│   │       ├── streams/
│   │       └── hal/
│   └── windows/
│       └── directml/       # DirectML implementations
│           ├── detection/
│           ├── memory/
│           ├── streams/
│           └── hal/
```

### **Build Tag Requirements**
```go
// Core files (no build tags)
package core

// Linux CUDA files
//go:build linux && cuda
package cuda

// macOS Metal files  
//go:build darwin && metal
package metal

// Windows DirectML files
//go:build windows && directml
package directml
```

## Testing Rules

### **Platform-Specific Tests**
```go
// Linux CUDA tests
//go:build linux && cuda
package cuda

func TestCUDADeviceDetection(t *testing.T) {
    devices, err := detectCUDADevices()
    require.NoError(t, err)
    require.NotEmpty(t, devices)
    
    // Test with real CUDA device
    for _, device := range devices {
        assert.True(t, device.HasCUDASupport())
        assert.Greater(t, device.MemoryMB(), 0)
    }
}
```

### **Integration Tests**
- **Requirement**: Test real GPU operations
- **Requirement**: Measure actual performance metrics
- **Requirement**: Validate memory usage patterns
- **Forbidden**: Mock GPU responses in integration tests

## Code Review Rules

### **Approval Requirements**
- **Core Tasks**: Can be reviewed by any team member
- **Platform Tasks**: Must be reviewed by someone with access to target platform
- **GPU Code**: Must be tested on real hardware before approval
- **Performance**: Must include benchmarks with real GPU metrics

### **Red Flags (Auto-Reject)**
- Any CPU fallback code in GPU implementations
- Mock implementations in production GPU code
- Cross-platform compatibility code in platform-specific files
- Missing build tags on platform-specific files
- Stub functions that don't call real GPU APIs

## Build System Rules

### **Build Outputs**
- **neuralmeter-linux**: Linux binary with CUDA support
- **neuralmeter-macos**: macOS binary with Metal support  
- **neuralmeter-windows.exe**: Windows binary with DirectML support
- **neuralmeter-linux-worker**: Linux worker node binary

### **Cross-Compilation**
- **Forbidden**: Cross-compilation of GPU code
- **Required**: Build on target platform with target GPU
- **CI/CD**: Platform-specific build agents required

## Documentation Rules

### **Required Documentation**
- **Hardware Requirements**: Document minimum GPU requirements per platform
- **Setup Instructions**: Platform-specific development environment setup
- **API Usage**: Document real GPU API calls used
- **Performance Characteristics**: Document expected GPU performance metrics

### **Forbidden Documentation**
- References to CPU fallback modes
- Instructions for mock/stub development
- Cross-platform compatibility guides for GPU code

## Enforcement

### **Automated Checks**
- Build tag validation in CI
- Directory structure validation
- GPU API usage validation (no CPU fallbacks)
- Real hardware test requirements

### **Manual Review Points**
- GPU code must demonstrate real API usage
- Performance tests must show real GPU metrics
- Error handling must fail fast without fallbacks
- Documentation must reflect real hardware requirements

This ensures NeuralMeter becomes a true GPU-only load testing platform with real implementations for each supported platform.
# Platform-Specific GPU Development Rules

## Overview
This project implements GPU-accelerated load testing with platform-specific implementations. Each platform must be developed on its native OS with real GPU API calls. **NO CPU FALLBACKS, NO MOCKS, NO STUBS.**

## Platform-Specific Development Requirements

### **Linux Development (CUDA)**
- **Required OS**: Linux with NVIDIA GPU
- **Required APIs**: Real CUDA API calls only
- **Directory Structure**: `internal/gpu/linux/cuda/`
- **Build Tags**: `//go:build linux && cuda`
- **Forbidden**: CPU fallbacks, CUDA mocks, stub implementations
- **Required Libraries**: CUDA Toolkit, cuDNN, NVML
- **IDE Setup**: Cursor on Linux with CUDA development environment

### **macOS Development (Metal)**
- **Required OS**: macOS with Metal-capable GPU
- **Required APIs**: Real Metal framework calls only
- **Directory Structure**: `internal/gpu/macos/metal/`
- **Build Tags**: `//go:build darwin && metal`
- **Forbidden**: CPU fallbacks, Metal mocks, stub implementations
- **Required Frameworks**: Metal, MetalKit, MetalPerformanceShaders
- **IDE Setup**: Cursor on macOS with Xcode command line tools

### **Windows Development (DirectML)**
- **Required OS**: Windows with DirectML-capable GPU
- **Required APIs**: Real DirectML API calls only
- **Directory Structure**: `internal/gpu/windows/directml/`
- **Build Tags**: `//go:build windows && directml`
- **Forbidden**: CPU fallbacks, DirectML mocks, stub implementations
- **Required Libraries**: DirectML, DirectX 12, Windows SDK
- **IDE Setup**: Cursor on Windows with Visual Studio Build Tools

## Task Structure Rules

### **Core Tasks (Interfaces Only)**
- **Purpose**: Define interfaces and types only
- **Implementation**: NO actual GPU code
- **Directory**: `internal/core/`
- **Example**: `69-Core`, `70-Core`, `78-Core`, etc.
- **Content**: Go interfaces, type definitions, error types
- **Forbidden**: Any platform-specific implementation

### **Platform Tasks (Real Implementations)**
- **Purpose**: Implement core interfaces with real GPU APIs
- **Naming**: `{TaskID}-{Platform}` (e.g., `69-Linux`, `70-macOS`, `78-Windows`)
- **Dependencies**: Must depend on corresponding Core task
- **Implementation**: Real GPU API calls only
- **Testing**: Must test on native platform with real GPU

## Code Implementation Rules

### **CUDA Implementation (Linux)**
```go
// REQUIRED: Real CUDA API calls
//go:build linux && cuda

import "C"
import "unsafe"

// Example: Real CUDA device detection
func detectCUDADevices() ([]CUDADevice, error) {
    var deviceCount C.int
    result := C.cudaGetDeviceCount(&deviceCount)
    if result != C.cudaSuccess {
        return nil, fmt.Errorf("CUDA error: %v", result)
    }
    // ... real CUDA implementation
}

// FORBIDDEN: CPU fallbacks
func detectCUDADevices() ([]CUDADevice, error) {
    // ❌ NEVER DO THIS
    if !hasCUDA() {
        return detectCPUDevices() // ❌ NO CPU FALLBACK
    }
}
```

### **Metal Implementation (macOS)**
```go
// REQUIRED: Real Metal framework calls
//go:build darwin && metal

/*
#cgo LDFLAGS: -framework Metal -framework Foundation
#include <Metal/Metal.h>
*/
import "C"

// Example: Real Metal device detection
func detectMetalDevices() ([]MetalDevice, error) {
    devices := C.MTLCopyAllDevices()
    if devices == nil {
        return nil, fmt.Errorf("no Metal devices found")
    }
    // ... real Metal implementation
}

// FORBIDDEN: CPU fallbacks
func detectMetalDevices() ([]MetalDevice, error) {
    // ❌ NEVER DO THIS
    if !hasMetalSupport() {
        return simulateGPU() // ❌ NO SIMULATION
    }
}
```

### **DirectML Implementation (Windows)**
```go
// REQUIRED: Real DirectML API calls
//go:build windows && directml

/*
#cgo LDFLAGS: -ldirectml -ld3d12
#include <directml.h>
#include <d3d12.h>
*/
import "C"

// Example: Real DirectML device detection
func detectDirectMLDevices() ([]DirectMLDevice, error) {
    var factory *C.IDXGIFactory4
    hr := C.CreateDXGIFactory2(0, &C.IID_IDXGIFactory4, unsafe.Pointer(&factory))
    if C.FAILED(hr) {
        return nil, fmt.Errorf("DirectML factory creation failed: %v", hr)
    }
    // ... real DirectML implementation
}

// FORBIDDEN: CPU fallbacks
func detectDirectMLDevices() ([]DirectMLDevice, error) {
    // ❌ NEVER DO THIS
    if !hasDirectMLSupport() {
        return []DirectMLDevice{createMockDevice()} // ❌ NO MOCKS
    }
}
```

## Development Workflow Rules

### **1. Platform Isolation**
- **Rule**: Never mix platform implementations in single files
- **Structure**: One platform per directory tree
- **Build Tags**: Strict platform-specific build tags
- **Testing**: Platform-specific test files with same build tags

### **2. No Cross-Platform Compatibility Code**
- **Forbidden**: `runtime.GOOS` checks in GPU code
- **Forbidden**: Interface{} type assertions for platform detection
- **Forbidden**: Conditional compilation within GPU implementations
- **Required**: Clean separation via directory structure and build tags

### **3. Real Hardware Requirements**
- **Linux Tasks**: Must have NVIDIA GPU with CUDA support
- **macOS Tasks**: Must have Metal-capable GPU (Intel/Apple Silicon)
- **Windows Tasks**: Must have DirectML-capable GPU
- **Testing**: All tests must run on real hardware with real APIs

### **4. Error Handling**
- **Required**: Fail fast when GPU not available
- **Required**: Clear error messages indicating missing GPU support
- **Forbidden**: Silent fallbacks to CPU computation
- **Forbidden**: Mock implementations that hide hardware requirements

## File Organization Rules

### **Directory Structure**
```
internal/
├── core/                    # Core interfaces only
│   ├── gpu_interfaces.go
│   ├── types.go
│   └── errors.go
├── gpu/
│   ├── linux/
│   │   └── cuda/           # CUDA implementations
│   │       ├── detection/
│   │       ├── memory/
│   │       ├── streams/
│   │       └── hal/
│   ├── macos/
│   │   └── metal/          # Metal implementations
│   │       ├── detection/
│   │       ├── memory/
│   │       ├── streams/
│   │       └── hal/
│   └── windows/
│       └── directml/       # DirectML implementations
│           ├── detection/
│           ├── memory/
│           ├── streams/
│           └── hal/
```

### **Build Tag Requirements**
```go
// Core files (no build tags)
package core

// Linux CUDA files
//go:build linux && cuda
package cuda

// macOS Metal files  
//go:build darwin && metal
package metal

// Windows DirectML files
//go:build windows && directml
package directml
```

## Testing Rules

### **Platform-Specific Tests**
```go
// Linux CUDA tests
//go:build linux && cuda
package cuda

func TestCUDADeviceDetection(t *testing.T) {
    devices, err := detectCUDADevices()
    require.NoError(t, err)
    require.NotEmpty(t, devices)
    
    // Test with real CUDA device
    for _, device := range devices {
        assert.True(t, device.HasCUDASupport())
        assert.Greater(t, device.MemoryMB(), 0)
    }
}
```

### **Integration Tests**
- **Requirement**: Test real GPU operations
- **Requirement**: Measure actual performance metrics
- **Requirement**: Validate memory usage patterns
- **Forbidden**: Mock GPU responses in integration tests

## Code Review Rules

### **Approval Requirements**
- **Core Tasks**: Can be reviewed by any team member
- **Platform Tasks**: Must be reviewed by someone with access to target platform
- **GPU Code**: Must be tested on real hardware before approval
- **Performance**: Must include benchmarks with real GPU metrics

### **Red Flags (Auto-Reject)**
- Any CPU fallback code in GPU implementations
- Mock implementations in production GPU code
- Cross-platform compatibility code in platform-specific files
- Missing build tags on platform-specific files
- Stub functions that don't call real GPU APIs

## Build System Rules

### **Build Outputs**
- **neuralmeter-linux**: Linux binary with CUDA support
- **neuralmeter-macos**: macOS binary with Metal support  
- **neuralmeter-windows.exe**: Windows binary with DirectML support
- **neuralmeter-linux-worker**: Linux worker node binary

### **Cross-Compilation**
- **Forbidden**: Cross-compilation of GPU code
- **Required**: Build on target platform with target GPU
- **CI/CD**: Platform-specific build agents required

## Documentation Rules

### **Required Documentation**
- **Hardware Requirements**: Document minimum GPU requirements per platform
- **Setup Instructions**: Platform-specific development environment setup
- **API Usage**: Document real GPU API calls used
- **Performance Characteristics**: Document expected GPU performance metrics

### **Forbidden Documentation**
- References to CPU fallback modes
- Instructions for mock/stub development
- Cross-platform compatibility guides for GPU code

## Enforcement

### **Automated Checks**
- Build tag validation in CI
- Directory structure validation
- GPU API usage validation (no CPU fallbacks)
- Real hardware test requirements

### **Manual Review Points**
- GPU code must demonstrate real API usage
- Performance tests must show real GPU metrics
- Error handling must fail fast without fallbacks
- Documentation must reflect real hardware requirements

This ensures NeuralMeter becomes a true GPU-only load testing platform with real implementations for each supported platform.
