---
alwaysApply: false
---
# GPU Task Restructuring Rules

## Overview
Tasks 69-88 have been restructured from problematic mixed-platform tasks into clean platform-specific tasks following the `docs/opus_attempts.md` solution.

## Task Naming Convention

### **Core Tasks (Interfaces Only)**
- **Format**: `{TaskID}-Core` (e.g., `69-Core`, `70-Core`)
- **Purpose**: Define interfaces and types only
- **Content**: Go interfaces, type definitions, error types
- **Status**: Most are already marked as "done" or "in-progress"
- **Dependencies**: Original task dependencies

### **Platform-Specific Tasks**
- **Format**: `{TaskID}-{Platform}` (e.g., `69-Linux`, `70-macOS`, `78-Windows`)
- **Platforms**: Linux (CUDA), macOS (Metal), Windows (DirectML)
- **Dependencies**: Must depend on corresponding Core task
- **Status**: All should start as "pending"
- **Implementation**: Real GPU API calls only

## Task Restructuring Pattern

### **Original Problematic Task Structure**
```json
{
  "id": 69,
  "title": "GPU Detection & Initialization",
  "description": "Mixed platform implementation causing stub code"
}
```

### **New Platform-Specific Structure**
```json
{
  "id": "69-Core",
  "title": "GPU Detection & Initialization - Core Interfaces",
  "description": "Define core GPU detection interfaces and types. NO IMPLEMENTATION - interfaces only."
},
{
  "id": "69-Linux", 
  "title": "GPU Detection & Initialization - CUDA Implementation",
  "description": "Implement CUDA-specific GPU detection with real CUDA API calls. NO CPU FALLBACK.",
  "dependencies": ["69-Core"]
},
{
  "id": "69-macOS",
  "title": "GPU Detection & Initialization - Metal Implementation", 
  "description": "Implement Metal-specific GPU detection with real Metal framework calls. NO CPU FALLBACK.",
  "dependencies": ["69-Core"]
},
{
  "id": "69-Windows",
  "title": "GPU Detection & Initialization - DirectML Implementation",
  "description": "Implement DirectML-specific GPU detection with real DirectML API calls. NO CPU FALLBACK.", 
  "dependencies": ["69-Core"]
}
```

## Required Task Properties

### **Core Tasks**
- **Title Pattern**: `{Original Title} - Core Interfaces`
- **Description**: `Define core {functionality} interfaces and types. NO IMPLEMENTATION - interfaces only.`
- **Status**: Keep original status (done/in-progress)
- **Dependencies**: Keep original dependencies
- **Directory**: `internal/core/`

### **Linux Tasks (CUDA)**
- **Title Pattern**: `{Original Title} - CUDA Implementation`
- **Description**: `Implement CUDA-specific {functionality} with real CUDA API calls. NO CPU FALLBACK.`
- **Status**: `pending`
- **Dependencies**: `["{TaskID}-Core"]`
- **Directory**: `internal/gpu/linux/cuda/`
- **Build Tags**: `//go:build linux && cuda`
- **Test Strategy**: `Test on Linux with CUDA GPU using real CUDA API calls`

### **macOS Tasks (Metal)**
- **Title Pattern**: `{Original Title} - Metal Implementation`
- **Description**: `Implement Metal-specific {functionality} with real Metal framework calls. NO CPU FALLBACK.`
- **Status**: `pending`
- **Dependencies**: `["{TaskID}-Core"]`
- **Directory**: `internal/gpu/macos/metal/`
- **Build Tags**: `//go:build darwin && metal`
- **Test Strategy**: `Test on macOS with Metal GPU using real Metal framework calls`

### **Windows Tasks (DirectML)**
- **Title Pattern**: `{Original Title} - DirectML Implementation`
- **Description**: `Implement DirectML-specific {functionality} with real DirectML API calls. NO CPU FALLBACK.`
- **Status**: `pending`
- **Dependencies**: `["{TaskID}-Core"]`
- **Directory**: `internal/gpu/windows/directml/`
- **Build Tags**: `//go:build windows && directml`
- **Test Strategy**: `Test on Windows with DirectML GPU using real DirectML API calls`

## Implementation Details Template

### **CUDA Implementation Details**
```
This task implements CUDA-specific {functionality} using real CUDA API calls:

1. **CUDA {Primary Function}**:
   - Use cuda{PrimaryAPI}() for {primary purpose}
   - Implement cuda{SecondaryAPI}() for {secondary purpose}
   - Use cuda{TertiaryAPI}() for {tertiary purpose}
   - Implement CUDA {specific feature}

2. **CUDA {Secondary Function}**:
   - Use cu{DriverAPI}() for {driver purpose}
   - Implement CUDA {advanced feature}
   - Use {CUDA library} for {specialized purpose}
   - Implement {performance optimization}

3. **Directory Structure**: `internal/gpu/linux/cuda/{module}/`
4. **Build Tags**: `//go:build linux && cuda`
5. **NO CPU FALLBACK** - GPU-only {functionality}
```

### **Metal Implementation Details**
```
This task implements Metal-specific {functionality} using real Metal framework calls:

1. **Metal {Primary Function}**:
   - Use MTL{PrimaryAPI}() for {primary purpose}
   - Implement MTL{SecondaryAPI} lifecycle management
   - Use Metal {framework feature} for {specific purpose}
   - Implement Metal {coordination feature}

2. **Metal {Secondary Function}**:
   - Use Metal {advanced feature}
   - Implement Metal {optimization technique}
   - Use Metal {performance feature} for {purpose}
   - Implement Metal {monitoring feature}

3. **Directory Structure**: `internal/gpu/macos/metal/{module}/`
4. **Build Tags**: `//go:build darwin && metal`
5. **NO CPU FALLBACK** - GPU-only {functionality}
```

### **DirectML Implementation Details**
```
This task implements DirectML-specific {functionality} using real DirectML API calls:

1. **DirectML {Primary Function}**:
   - Use ID3D12{PrimaryAPI} for {primary purpose}
   - Implement DirectML {feature} recording
   - Use DirectX 12 {feature} for {purpose}
   - Implement DirectML {resource management}

2. **DirectML {Secondary Function}**:
   - Use DirectML {advanced feature}
   - Implement DirectML {optimization}
   - Use DirectX 12 {performance feature}
   - Implement DirectML {monitoring}

3. **Directory Structure**: `internal/gpu/windows/directml/{module}/`
4. **Build Tags**: `//go:build windows && directml`
5. **NO CPU FALLBACK** - GPU-only {functionality}
```

## Tasks Successfully Restructured

### **Completed Restructuring**
- ✅ Task 69: GPU Detection & Initialization
- ✅ Task 70: GPU Model Loading & Inference Pipeline  
- ✅ Task 72: GPU Error Handling & Recovery
- ✅ Task 74: GPU Configuration & Optimization Interface
- ✅ Task 78: GPU Stream Management & Synchronization
- ✅ Task 84: GPU Hardware Abstraction Layer

### **Remaining Tasks to Restructure**
- 🔄 Task 75: GPU Resource Pool Management
- 🔄 Task 76: GPU Tensor Operations Library
- 🔄 Task 77: GPU Memory Pool Management
- 🔄 Task 85: GPU Security & Isolation
- 🔄 Task 86: GPU Power Management & Thermal Control  
- 🔄 Task 87: GPU Checkpoint & Recovery System
- 🔄 Task 88: GPU Model Optimization Pipeline

## Quality Assurance Rules

### **When Adding New Platform Tasks**
1. **Verify Core Task Exists**: Ensure corresponding Core task is created first
2. **Check Dependencies**: Platform tasks must depend on Core task
3. **Validate Descriptions**: Must specify "real {API} calls" and "NO CPU FALLBACK"
4. **Confirm Directory Structure**: Must specify correct platform directory
5. **Set Status**: All new platform tasks start as "pending"
6. **Add Test Strategy**: Must specify testing on native platform with real GPU

### **When Modifying Existing Tasks**
1. **Preserve Core Interface**: Don't change Core task interfaces once platform tasks exist
2. **Maintain Dependencies**: Platform tasks must always depend on Core task
3. **Keep Platform Separation**: Never mix platform implementations
4. **Update All Platforms**: Changes to one platform may require updates to others

### **Red Flags (Immediate Fix Required)**
- Platform tasks without Core task dependency
- Mixed platform implementations in single task
- CPU fallback mentions in platform task descriptions
- Missing build tags or directory structure specifications
- Mock or stub implementations in platform tasks

## Benefits of New Structure

### **Development Benefits**
- **Clear Separation**: Each platform developed independently
- **Real Implementations**: Forces real GPU API usage
- **Proper Testing**: Platform-specific testing on real hardware
- **No Confusion**: Claude Sonnet won't mix platforms

### **Project Benefits**  
- **True GPU Tool**: No CPU fallbacks anywhere
- **Platform Optimization**: Each platform optimized for its GPU APIs
- **Maintainable**: Clear ownership and responsibility per platform
- **Scalable**: Easy to add new platforms or GPU APIs

This restructuring transforms NeuralMeter from a confused mixed-platform project into a clean, platform-specific GPU-only load testing tool.
# GPU Task Restructuring Rules

## Overview
Tasks 69-88 have been restructured from problematic mixed-platform tasks into clean platform-specific tasks following the `docs/opus_attempts.md` solution.

## Task Naming Convention

### **Core Tasks (Interfaces Only)**
- **Format**: `{TaskID}-Core` (e.g., `69-Core`, `70-Core`)
- **Purpose**: Define interfaces and types only
- **Content**: Go interfaces, type definitions, error types
- **Status**: Most are already marked as "done" or "in-progress"
- **Dependencies**: Original task dependencies

### **Platform-Specific Tasks**
- **Format**: `{TaskID}-{Platform}` (e.g., `69-Linux`, `70-macOS`, `78-Windows`)
- **Platforms**: Linux (CUDA), macOS (Metal), Windows (DirectML)
- **Dependencies**: Must depend on corresponding Core task
- **Status**: All should start as "pending"
- **Implementation**: Real GPU API calls only

## Task Restructuring Pattern

### **Original Problematic Task Structure**
```json
{
  "id": 69,
  "title": "GPU Detection & Initialization",
  "description": "Mixed platform implementation causing stub code"
}
```

### **New Platform-Specific Structure**
```json
{
  "id": "69-Core",
  "title": "GPU Detection & Initialization - Core Interfaces",
  "description": "Define core GPU detection interfaces and types. NO IMPLEMENTATION - interfaces only."
},
{
  "id": "69-Linux", 
  "title": "GPU Detection & Initialization - CUDA Implementation",
  "description": "Implement CUDA-specific GPU detection with real CUDA API calls. NO CPU FALLBACK.",
  "dependencies": ["69-Core"]
},
{
  "id": "69-macOS",
  "title": "GPU Detection & Initialization - Metal Implementation", 
  "description": "Implement Metal-specific GPU detection with real Metal framework calls. NO CPU FALLBACK.",
  "dependencies": ["69-Core"]
},
{
  "id": "69-Windows",
  "title": "GPU Detection & Initialization - DirectML Implementation",
  "description": "Implement DirectML-specific GPU detection with real DirectML API calls. NO CPU FALLBACK.", 
  "dependencies": ["69-Core"]
}
```

## Required Task Properties

### **Core Tasks**
- **Title Pattern**: `{Original Title} - Core Interfaces`
- **Description**: `Define core {functionality} interfaces and types. NO IMPLEMENTATION - interfaces only.`
- **Status**: Keep original status (done/in-progress)
- **Dependencies**: Keep original dependencies
- **Directory**: `internal/core/`

### **Linux Tasks (CUDA)**
- **Title Pattern**: `{Original Title} - CUDA Implementation`
- **Description**: `Implement CUDA-specific {functionality} with real CUDA API calls. NO CPU FALLBACK.`
- **Status**: `pending`
- **Dependencies**: `["{TaskID}-Core"]`
- **Directory**: `internal/gpu/linux/cuda/`
- **Build Tags**: `//go:build linux && cuda`
- **Test Strategy**: `Test on Linux with CUDA GPU using real CUDA API calls`

### **macOS Tasks (Metal)**
- **Title Pattern**: `{Original Title} - Metal Implementation`
- **Description**: `Implement Metal-specific {functionality} with real Metal framework calls. NO CPU FALLBACK.`
- **Status**: `pending`
- **Dependencies**: `["{TaskID}-Core"]`
- **Directory**: `internal/gpu/macos/metal/`
- **Build Tags**: `//go:build darwin && metal`
- **Test Strategy**: `Test on macOS with Metal GPU using real Metal framework calls`

### **Windows Tasks (DirectML)**
- **Title Pattern**: `{Original Title} - DirectML Implementation`
- **Description**: `Implement DirectML-specific {functionality} with real DirectML API calls. NO CPU FALLBACK.`
- **Status**: `pending`
- **Dependencies**: `["{TaskID}-Core"]`
- **Directory**: `internal/gpu/windows/directml/`
- **Build Tags**: `//go:build windows && directml`
- **Test Strategy**: `Test on Windows with DirectML GPU using real DirectML API calls`

## Implementation Details Template

### **CUDA Implementation Details**
```
This task implements CUDA-specific {functionality} using real CUDA API calls:

1. **CUDA {Primary Function}**:
   - Use cuda{PrimaryAPI}() for {primary purpose}
   - Implement cuda{SecondaryAPI}() for {secondary purpose}
   - Use cuda{TertiaryAPI}() for {tertiary purpose}
   - Implement CUDA {specific feature}

2. **CUDA {Secondary Function}**:
   - Use cu{DriverAPI}() for {driver purpose}
   - Implement CUDA {advanced feature}
   - Use {CUDA library} for {specialized purpose}
   - Implement {performance optimization}

3. **Directory Structure**: `internal/gpu/linux/cuda/{module}/`
4. **Build Tags**: `//go:build linux && cuda`
5. **NO CPU FALLBACK** - GPU-only {functionality}
```

### **Metal Implementation Details**
```
This task implements Metal-specific {functionality} using real Metal framework calls:

1. **Metal {Primary Function}**:
   - Use MTL{PrimaryAPI}() for {primary purpose}
   - Implement MTL{SecondaryAPI} lifecycle management
   - Use Metal {framework feature} for {specific purpose}
   - Implement Metal {coordination feature}

2. **Metal {Secondary Function}**:
   - Use Metal {advanced feature}
   - Implement Metal {optimization technique}
   - Use Metal {performance feature} for {purpose}
   - Implement Metal {monitoring feature}

3. **Directory Structure**: `internal/gpu/macos/metal/{module}/`
4. **Build Tags**: `//go:build darwin && metal`
5. **NO CPU FALLBACK** - GPU-only {functionality}
```

### **DirectML Implementation Details**
```
This task implements DirectML-specific {functionality} using real DirectML API calls:

1. **DirectML {Primary Function}**:
   - Use ID3D12{PrimaryAPI} for {primary purpose}
   - Implement DirectML {feature} recording
   - Use DirectX 12 {feature} for {purpose}
   - Implement DirectML {resource management}

2. **DirectML {Secondary Function}**:
   - Use DirectML {advanced feature}
   - Implement DirectML {optimization}
   - Use DirectX 12 {performance feature}
   - Implement DirectML {monitoring}

3. **Directory Structure**: `internal/gpu/windows/directml/{module}/`
4. **Build Tags**: `//go:build windows && directml`
5. **NO CPU FALLBACK** - GPU-only {functionality}
```

## Tasks Successfully Restructured

### **Completed Restructuring**
- ✅ Task 69: GPU Detection & Initialization
- ✅ Task 70: GPU Model Loading & Inference Pipeline  
- ✅ Task 72: GPU Error Handling & Recovery
- ✅ Task 74: GPU Configuration & Optimization Interface
- ✅ Task 78: GPU Stream Management & Synchronization
- ✅ Task 84: GPU Hardware Abstraction Layer

### **Remaining Tasks to Restructure**
- 🔄 Task 75: GPU Resource Pool Management
- 🔄 Task 76: GPU Tensor Operations Library
- 🔄 Task 77: GPU Memory Pool Management
- 🔄 Task 85: GPU Security & Isolation
- 🔄 Task 86: GPU Power Management & Thermal Control  
- 🔄 Task 87: GPU Checkpoint & Recovery System
- 🔄 Task 88: GPU Model Optimization Pipeline

## Quality Assurance Rules

### **When Adding New Platform Tasks**
1. **Verify Core Task Exists**: Ensure corresponding Core task is created first
2. **Check Dependencies**: Platform tasks must depend on Core task
3. **Validate Descriptions**: Must specify "real {API} calls" and "NO CPU FALLBACK"
4. **Confirm Directory Structure**: Must specify correct platform directory
5. **Set Status**: All new platform tasks start as "pending"
6. **Add Test Strategy**: Must specify testing on native platform with real GPU

### **When Modifying Existing Tasks**
1. **Preserve Core Interface**: Don't change Core task interfaces once platform tasks exist
2. **Maintain Dependencies**: Platform tasks must always depend on Core task
3. **Keep Platform Separation**: Never mix platform implementations
4. **Update All Platforms**: Changes to one platform may require updates to others

### **Red Flags (Immediate Fix Required)**
- Platform tasks without Core task dependency
- Mixed platform implementations in single task
- CPU fallback mentions in platform task descriptions
- Missing build tags or directory structure specifications
- Mock or stub implementations in platform tasks

## Benefits of New Structure

### **Development Benefits**
- **Clear Separation**: Each platform developed independently
- **Real Implementations**: Forces real GPU API usage
- **Proper Testing**: Platform-specific testing on real hardware
- **No Confusion**: Claude Sonnet won't mix platforms

### **Project Benefits**  
- **True GPU Tool**: No CPU fallbacks anywhere
- **Platform Optimization**: Each platform optimized for its GPU APIs
- **Maintainable**: Clear ownership and responsibility per platform
- **Scalable**: Easy to add new platforms or GPU APIs

This restructuring transforms NeuralMeter from a confused mixed-platform project into a clean, platform-specific GPU-only load testing tool.
