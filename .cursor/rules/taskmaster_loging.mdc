---
alwaysApply: false
---
# TaskMaster Quality Control Rules

## Development Machine Specifications
- **Hardware**: M1 Mac with 8GB RAM
- **GPU**: Metal GPU (Apple Silicon integrated)
- **Limitations**: 
  - Memory constraints for large-scale testing
  - Metal-specific GPU acceleration (not CUDA/ROCm)
  - ARM64 architecture considerations
- **Testing Strategy**: Focus on core logic validation, use mocks for high-memory scenarios
- **Hardware-Dependent Features**: Defer CUDA/ROCm testing, implement Metal-compatible alternatives where possible

## Testing Protocol
- **Always create and run tests** for all tasks and subtasks
- **If task has `testStrategy`**: Follow it exactly
- **If no `testStrategy`**: Create appropriate tests (unit, integration, build verification)
- **Document results**: Brief pass/fail status in task log
- **Tests must pass** any failure needs to be fixed before marking "done" (with hardware exceptions below)
- **Cross-platform tasks**: Test on available platform, mock unavailable platforms, document coverage
- **GPU tasks (69-88)**: Test per task due to complexity and dependencies
- **Other tasks**: Phase-based testing for token efficiency

### Hardware-Dependent Testing Rules
- **Production hardware required**: Tasks requiring specific GPU models, large memory, or specialized hardware
- **Development approach**: Implement with unit tests for core logic, mock hardware-dependent components
- **Deferred testing**: Mark task as "implemented, pending hardware validation" 
- **End-of-project validation**: Schedule comprehensive hardware testing phase before final release
- **Documentation requirement**: Document hardware requirements and expected test scenarios for later validation

## Task Sequence
- **Follow order** in `tasks_in_order_of_dev.md`
- **Check dependencies** before starting
- **Work sequentially** - only next available task

## Progress Tracking
- **Mark completion**: Add `✅ **DONE**` to task in `tasks_in_order_of_dev.md`
- **Log operation**: Brief entry in `.taskmaster/tasklogs/task_changes.log`

## Completion Protocol
1. **Implement** per task requirements
2. **Test** (create tests if none specified)
3. **VERIFY COMPILATION**: Ensure ALL code compiles without errors across the entire project
4. **Verify** all tests pass (or document hardware limitations)
5. **Log** completion with test results
6. **Mark done** via TaskMaster (or "implemented, pending hardware validation")
7. **Update** visual progress
8. **Notify** user for commit

### Hardware-Limited Completion
- **Status**: Use "implemented, pending hardware validation" for hardware-dependent features
- **Documentation**: Create detailed test plans for hardware validation phase
- **Core logic**: Ensure all non-hardware logic is fully tested and working
- **Integration**: Verify system works with mocked/simulated hardware components

## Quality Gates
- **Start**: Task is next in sequence, dependencies complete
- **Finish**: Implementation done, ALL code compiles, tests pass, progress updated

## Essential TaskMaster Operations
- Use `get_tasks` to check current status
- Use `next_task` to determine what to work on
- Use `get_task` to view task details and dependencies
- Use `set_task_status` to mark completion
- Use `update_subtask` to log implementation progress

## Compilation Issue Management - CRITICAL RULES

### **ABSOLUTE REQUIREMENT: NO TASK IS COMPLETE WITH COMPILATION ERRORS**
- **NEVER mark tasks "done" with ANY compilation errors anywhere in the project**: Implementation is incomplete if ANY code doesn't compile
- **Project-wide compilation check required**: Before marking any task complete, verify `go build ./...` succeeds
- **Zero tolerance for compilation errors**: Even unrelated compilation errors prevent task completion
- **Create dedicated fix tasks**: When compilation issues are discovered, create a new high-priority task to track fixes
- **Break down fix tasks into subtasks**: Each compilation issue becomes a separate subtask requiring user review and approval
- **Use "review" status**: Mark original task as "review" instead of "done" when compilation issues exist ANYWHERE
- **Track in visual progress**: Update `tasks_in_order_of_dev.md` to show the relationship between original task and fix task

### **Compilation Verification Protocol**
1. **Before marking ANY task complete**: Run `go build ./...` to verify entire project compiles
2. **If ANY compilation errors exist**: Task cannot be marked "done" regardless of task-specific implementation
3. **Document ALL errors**: List every compilation error found during verification
4. **Fix ALL errors**: Either fix immediately or create comprehensive fix task with subtasks
5. **Re-verify compilation**: Ensure `go build ./...` succeeds before final completion

### Compilation Issue Subtask Protocol
1. **Identify each issue**: Create one subtask per distinct compilation problem
2. **Document specifics**: Include file names, error messages, and proposed solution approach
3. **Require user review**: Each subtask must be reviewed and approved before implementation
4. **Verify fixes**: Test compilation after each subtask completion
5. **Update dependencies**: Ensure fix task depends on original task to maintain proper sequencing

### Example Fix Task Structure
```
Task #XXX: Fix [Component] Compilation Issues
- Subtask 1: Resolve Type Redeclaration in file1.go and file2.go
- Subtask 2: Add Missing Interface Method to ComponentInterface
- Subtask 3: Complete Type Renaming from OldType to NewType
- Subtask 4: Add Missing Struct Fields and Update Serialization
```

## Error Recovery
- **If tests fail**: Fix issues before marking done
- **If task order violated**: Return to proper sequence
- **If dependencies missing**: Complete prerequisite tasks first
- **If compilation fails ANYWHERE**: fix it, do not set task as done untill its fixed
- **If platform unavailable for testing**: Create mocks, document limitation
- **If hardware unavailable**: Implement with mocks, document hardware requirements, defer validation
- **If algorithm complexity exceeds session**: Implement working subset, disable problematic components, document issues

## End-of-Project Hardware Validation Phase
- **Timing**: After all development tasks complete, before final release
- **Scope**: Test all "pending hardware validation" features on production hardware
- **Requirements**: Document specific hardware needs (GPU models, memory, etc.)
- **Success criteria**: All deferred tests pass on target hardware
- **Fallback plan**: Document any features that require hardware upgrades or alternative approaches