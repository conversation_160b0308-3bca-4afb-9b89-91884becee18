---
alwaysApply: false
---
# Private Code Protection Rule

**CRITICAL**: This is private code. Never reference external repositories or services.

## **Prohibited Actions**
- **Never use GitHub, GitLab, or any public repository URLs** in module names
- **Never suggest pushing to public repositories**
- **Never add public repository references** in comments, documentation, or code
- **Never use external domain names** in module paths unless explicitly requested
- **Never assume code will be published publicly**

## **Required Practices**

### **Go Module Naming**
```go
// ✅ DO: Use simple local module names
module neuralmetergo
module projectname
module company.local/projectname

// ❌ DON'T: Use public repository URLs
module github.com/user/repo
module gitlab.com/user/repo
module bitbucket.org/user/repo
```

### **Import Paths**
```go
// ✅ DO: Use local module-relative imports
import "neuralmetergo/internal/client"
import "projectname/pkg/utils"

// ❌ DON'T: Reference external repositories
import "github.com/someone/package"  // Only if explicitly required dependency
```

### **Documentation and Comments**
```go
// ✅ DO: Generic references
// Package client provides HTTP functionality
// See internal documentation for details

// ❌ DON'T: External references
// Package client - see https://github.com/...
// Based on public repo at github.com/...
```

### **Configuration Files**
```yaml
# ✅ DO: Local or generic references
name: "NeuralMeterGo"
repository: "internal"

# ❌ DON'T: Public repository references  
repository: "https://github.com/user/repo"
```

## **Default Assumptions**
- **All code is private** unless explicitly stated otherwise
- **Use local module names** by default
- **Ask before adding any external references**
- **Prefer self-contained solutions**

## **When External References Are Needed**
- **Ask the user first** before adding any external dependencies
- **Explain why** the external reference is necessary
- **Provide alternatives** when possible
- **Use minimal, well-established dependencies** only

## **Git Operations**
- **Never automatically commit** or push code
- **Ask before any git operations** that might expose code
- **Warn about public repository risks** if user mentions GitHub/GitLab

## **Error Recovery**
If external references are accidentally added:
1. **Immediately identify and remove** all external references
2. **Replace with local alternatives**
3. **Document the correction** in task logs
4. **Verify no sensitive information** was exposed
