package main

import (
    "io"
    "log"
    "net"

    "google.golang.org/grpc"
    pb "nmtest/echo/proto" // generated via setup script
    "google.golang.org/grpc/reflection"
)

type server struct {
    pb.UnimplementedEchoServiceServer
}

func (s *server) Echo(stream pb.EchoService_EchoServer) error {
    for {
        in, err := stream.Recv()
        if err == io.EOF {
            return nil
        }
        if err != nil {
            return err
        }
        if err := stream.Send(&pb.EchoMessage{Message: in.Message}); err != nil {
            return err
        }
    }
}

func main() {
    l, err := net.Listen("tcp", ":50051")
    if err != nil {
        log.Fatalf("failed to listen: %v", err)
    }
    g := grpc.NewServer()
    pb.RegisterEchoServiceServer(g, &server{})
    reflection.Register(g)
    log.Println("gRPC Echo server listening on :50051")
    if err := g.<PERSON>(l); err != nil {
        log.Fatalf("failed to serve: %v", err)
    }
} 