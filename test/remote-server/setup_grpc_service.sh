#!/usr/bin/env bash
# setup_grpc_service.sh - Build and register a simple gRPC Echo server on port 50051.
# Requires sudo privileges for system-wide installation.
set -euo pipefail

SERVICE_NAME="nm_test_grpc"
INSTALL_PREFIX="/usr/local/bin"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
GRPC_DIR="${SCRIPT_DIR}/grpc"

# 1. Install build dependencies (Go + protoc) if missing
if ! command -v go >/dev/null 2>&1; then
  echo "[+] Installing Go toolchain"
  sudo apt-get update
  sudo apt-get install -y golang-go
fi

if ! command -v protoc >/dev/null 2>&1; then
  echo "[+] Installing protobuf compiler"
  sudo apt-get update
  sudo apt-get install -y protobuf-compiler
fi

# Install protoc Go plugins in GOPATH/bin if missing
if ! command -v protoc-gen-go >/dev/null 2>&1; then
  echo "[+] Installing protoc-gen-go"
  go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
fi
if ! command -v protoc-gen-go-grpc >/dev/null 2>&1; then
  echo "[+] Installing protoc-gen-go-grpc"
  go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
fi

export PATH="$(go env GOPATH)/bin:$PATH"

# 2. Generate Go stubs and build server
pushd "${GRPC_DIR}" >/dev/null

go mod tidy
protoc --go_out=. --go_opt=paths=source_relative \
       --go-grpc_out=. --go-grpc_opt=paths=source_relative \
       proto/echo.proto

go build -o "${SERVICE_NAME}" echo_server.go
popd >/dev/null

# 3. Install binary
sudo install -m 0755 "${GRPC_DIR}/${SERVICE_NAME}" "${INSTALL_PREFIX}/${SERVICE_NAME}"

# 4. Register systemd unit
sudo tee "/etc/systemd/system/${SERVICE_NAME}.service" >/dev/null <<EOF
[Unit]
Description=NeuralMeter Test gRPC Echo Server
After=network.target

[Service]
ExecStart=${INSTALL_PREFIX}/${SERVICE_NAME}
Restart=on-failure
User=${USER}

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl enable --now "${SERVICE_NAME}.service"

echo "[✔] gRPC Echo service active on port 50051" 