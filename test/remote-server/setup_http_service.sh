#!/usr/bin/env bash
# setup_http_service.sh - Install and configure a minimal NGINX site on port 8080
# for NeuralMeter integration tests.  Requires sudo privileges.
set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
HTML_SRC="${SCRIPT_DIR}/html"
WEBROOT="/var/www/neuralmeter"

if ! command -v nginx >/dev/null 2>&1; then
  echo "[+] Installing NGINX web-server"
  sudo apt-get update
  sudo apt-get install -y nginx
fi

echo "[+] Copying static assets to ${WEBROOT}"
sudo mkdir -p "${WEBROOT}"
sudo cp -r "${HTML_SRC}/." "${WEBROOT}/"

NGINX_SITE="/etc/nginx/sites-available/neuralmeter_test"

echo "[+] Writing NGINX site file ${NGINX_SITE} (listens on :8080)"
# shellcheck disable=SC2416
sudo tee "${NGINX_SITE}" >/dev/null <<'EOF'
server {
    listen 8080 default_server;
    listen [::]:8080 default_server;

    root /var/www/neuralmeter;
    index page.html;

    location / {
        try_files $uri =404;
    }
}
EOF

sudo ln -sf "${NGINX_SITE}" /etc/nginx/sites-enabled/neuralmeter_test

echo "[+] Restarting NGINX"
sudo systemctl restart nginx

echo "[✔] Test site deployed → http://<host>:8080/health.json" 