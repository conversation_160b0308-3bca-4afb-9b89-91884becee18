name: Basic WebSocket Test
description: Simple WebSocket streaming test for NeuralMeter CLI verification
version: 1.0

duration: 10s
concurrency: 1
ramp_up: 2s

scenarios:
  - name: basic_websocket_streaming
    description: Basic WebSocket streaming verification
    weight: 100
    requests:
      - name: simple_api_test
        method: GET
        url: http://*************:8011/health
        headers:
          User-Agent: NeuralMeter-WebSocket-Basic/1.0
        timeout: 5s

global:
  timeout: 8s

output:
  format: 
    - json
  detailed: true
  stream_results: true