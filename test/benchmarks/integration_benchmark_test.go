package benchmarks

import (
	"testing"
	"time"

	"neuralmetergo/internal/metrics"
)

// BenchmarkMetricsIntegrator_AddMetricValue benchmarks adding metric values
// This is scaled for development environments - not overwhelming
func BenchmarkMetricsIntegrator_AddMetricValue(b *testing.B) {
	config := getDevelopmentBenchmarkConfig()
	integrator, err := metrics.NewMetricsIntegrator(config)
	if err != nil {
		b.Fatalf("Failed to create metrics integrator: %v", err)
	}

	if err := integrator.Start(); err != nil {
		b.Fatalf("Failed to start metrics integrator: %v", err)
	}
	defer integrator.Stop()

	tags := map[string]string{"benchmark": "true"}

	b.ResetTimer()
	b.<PERSON>l(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			value := float64(i % 100) // Cycle through 0-99
			if err := integrator.AddMetricValue("benchmark_metric", value, tags); err != nil {
				b.<PERSON><PERSON><PERSON>("Failed to add metric value: %v", err)
			}
			i++
		}
	})
}

// BenchmarkMetricsIntegrator_SlidingWindow benchmarks sliding window operations
func BenchmarkMetricsIntegrator_SlidingWindow(b *testing.B) {
	config := getDevelopmentBenchmarkConfig()
	integrator, err := metrics.NewMetricsIntegrator(config)
	if err != nil {
		b.Fatalf("Failed to create metrics integrator: %v", err)
	}

	if err := integrator.Start(); err != nil {
		b.Fatalf("Failed to start metrics integrator: %v", err)
	}
	defer integrator.Stop()

	metricName := "sliding_window_benchmark"
	tags := map[string]string{"benchmark": "sliding_window"}

	// Pre-populate with some data
	for i := 0; i < 10; i++ {
		integrator.AddMetricValue(metricName, float64(i), tags)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Add a value
		integrator.AddMetricValue(metricName, float64(i%50), tags)

		// Get stats every 10 iterations to simulate real usage
		if i%10 == 0 {
			_, err := integrator.GetSlidingWindowStats(metricName)
			if err != nil {
				b.Errorf("Failed to get sliding window stats: %v", err)
			}
		}
	}
}

// BenchmarkMetricsIntegrator_StatisticalSummary benchmarks statistical summary operations
func BenchmarkMetricsIntegrator_StatisticalSummary(b *testing.B) {
	config := getDevelopmentBenchmarkConfig()
	integrator, err := metrics.NewMetricsIntegrator(config)
	if err != nil {
		b.Fatalf("Failed to create metrics integrator: %v", err)
	}

	if err := integrator.Start(); err != nil {
		b.Fatalf("Failed to start metrics integrator: %v", err)
	}
	defer integrator.Stop()

	metricName := "stats_summary_benchmark"
	tags := map[string]string{"benchmark": "stats_summary"}

	// Pre-populate with some data
	for i := 0; i < 20; i++ {
		integrator.AddMetricValue(metricName, float64(i), tags)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Add a value
		integrator.AddMetricValue(metricName, float64(i%100), tags)

		// Get summary every 5 iterations
		if i%5 == 0 {
			_, err := integrator.GetStatisticalSummary(metricName)
			if err != nil {
				b.Errorf("Failed to get statistical summary: %v", err)
			}
		}
	}
}

// BenchmarkMetricsIntegrator_FullWorkflow benchmarks the complete workflow
func BenchmarkMetricsIntegrator_FullWorkflow(b *testing.B) {
	config := getDevelopmentBenchmarkConfig()
	config.EnableCollection = true // Enable collection for full workflow
	config.EnableBasicAgg = true   // Enable basic aggregation too

	integrator, err := metrics.NewMetricsIntegrator(config)
	if err != nil {
		b.Fatalf("Failed to create metrics integrator: %v", err)
	}

	if err := integrator.Start(); err != nil {
		b.Fatalf("Failed to start metrics integrator: %v", err)
	}
	defer integrator.Stop()

	// Register some metrics for collection
	counter := metrics.NewCounter()
	gauge := metrics.NewGauge()
	histogram := metrics.NewHistogram()

	tags := map[string]string{"benchmark": "full_workflow"}

	integrator.RegisterCounter("bench_counter", counter, tags)
	integrator.RegisterGauge("bench_gauge", gauge, tags)
	integrator.RegisterHistogram("bench_histogram", histogram, tags)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Simulate typical load testing operations
		counter.Inc()
		gauge.Set(float64(i % 1000))
		histogram.Observe(float64(i%10) * 0.1)

		// Add direct metric values
		integrator.AddMetricValue("direct_metric", float64(i%50), tags)

		// Occasionally read stats (every 20 iterations)
		if i%20 == 0 {
			integrator.GetSlidingWindowStats("direct_metric")
			integrator.GetStatisticalSummary("direct_metric")
			integrator.GetBasicAggregatedValues("bench_counter")
		}
	}
}

// getDevelopmentBenchmarkConfig returns a configuration optimized for development benchmarking
// This configuration is designed to be resource-friendly while still being meaningful
func getDevelopmentBenchmarkConfig() metrics.IntegratorConfig {
	return metrics.IntegratorConfig{
		CollectionConfig: metrics.CollectionConfig{
			WorkerCount:     2,                     // Minimal workers
			CollectionRate:  10 * time.Millisecond, // Fast but not overwhelming
			ChannelBuffer:   50,                    // Small buffer
			BatchSize:       10,
			EnableBatching:  true,
			MaxRetries:      3,
			RetryDelay:      2 * time.Millisecond,
			EnableMetrics:   true,
			ShutdownTimeout: 2 * time.Second,
		},
		AggregationConfig: metrics.AggregationConfig{
			Interval:    20 * time.Millisecond, // Fast aggregation
			Type:        metrics.AggregationSlidingWindow,
			Percentile:  0.95,
			BufferSize:  100,   // Reasonable buffer for benchmarking
			EnableAsync: false, // Synchronous for consistent benchmarking
			WindowConfig: metrics.SlidingWindowConfig{
				Type:        metrics.WindowTypeCount,
				Capacity:    50, // Small window for development
				EnableStats: true,
				EnableAsync: false, // Synchronous for benchmarking
			},
			BucketConfig: metrics.TimeBucketConfig{
				Granularity:        metrics.GranularitySecond,
				RetentionBuckets:   60, // 1 minute retention
				MaxClockSkew:       100 * time.Millisecond,
				EnableDownsampling: false,
				EnableUpsampling:   false,
				CleanupInterval:    10 * time.Second,
				EnableAsync:        false, // Synchronous for benchmarking
				EnableCompression:  false,
				MaxMemoryUsage:     5 * 1024 * 1024, // 5MB limit
				FlushOnShutdown:    true,
			},
			EnableAdvancedStats: true,
		},
		EnableEnhanced:   true,
		EnableCollection: false, // Start with collection disabled
		EnableBasicAgg:   false, // Start with basic agg disabled
	}
}

// TestBenchmarkSanity is a simple test to ensure benchmarks can run without errors
func TestBenchmarkSanity(t *testing.T) {
	config := getDevelopmentBenchmarkConfig()
	integrator, err := metrics.NewMetricsIntegrator(config)
	if err != nil {
		t.Fatalf("Failed to create metrics integrator: %v", err)
	}

	if err := integrator.Start(); err != nil {
		t.Fatalf("Failed to start metrics integrator: %v", err)
	}

	// Add a few values
	tags := map[string]string{"test": "sanity"}
	for i := 0; i < 5; i++ {
		if err := integrator.AddMetricValue("sanity_test", float64(i), tags); err != nil {
			t.Errorf("Failed to add metric value: %v", err)
		}
	}

	// Get some stats
	if _, err := integrator.GetSlidingWindowStats("sanity_test"); err != nil {
		t.Errorf("Failed to get sliding window stats: %v", err)
	}

	if _, err := integrator.GetStatisticalSummary("sanity_test"); err != nil {
		t.Errorf("Failed to get statistical summary: %v", err)
	}

	if err := integrator.Stop(); err != nil {
		t.Fatalf("Failed to stop metrics integrator: %v", err)
	}

	t.Log("Benchmark sanity test passed")
}
