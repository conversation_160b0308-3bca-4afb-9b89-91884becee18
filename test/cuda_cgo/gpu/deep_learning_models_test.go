package gpu

import (
	"testing"
	"time"

	"neuralmetergo/internal/gpu"
)

func TestLSTMModel_Creation(t *testing.T) {
	model := gpu.NewLSTMModel(10, 32, 1, 5, 0.01, 50)

	if model.GetModelType() != "lstm" {
		t.<PERSON><PERSON><PERSON>("Expected model type 'lstm', got '%s'", model.GetModelType())
	}

	if model.GetAccuracy() != 0.0 {
		t.<PERSON><PERSON>("Expected initial accuracy to be 0.0, got %f", model.GetAccuracy())
	}
}

func TestGRUModel_Creation(t *testing.T) {
	model := gpu.NewGRUModel(10, 32, 1, 5, 0.01, 50)

	if model.GetModelType() != "gru" {
		t.<PERSON>("Expected model type 'gru', got '%s'", model.GetModelType())
	}

	if model.GetAccuracy() != 0.0 {
		t.<PERSON><PERSON>("Expected initial accuracy to be 0.0, got %f", model.GetAccuracy())
	}
}

func TestDeepLearningEnsemble_Creation(t *testing.T) {
	ensemble := gpu.NewDeepLearningEnsemble()

	if ensemble.GetModelType() != "deep_learning_ensemble" {
		t.Errorf("Expected model type 'deep_learning_ensemble', got '%s'", ensemble.GetModelType())
	}

	// Add models to ensemble
	lstmModel := gpu.NewLSTMModel(10, 16, 1, 3, 0.01, 10)
	gruModel := gpu.NewGRUModel(10, 16, 1, 3, 0.01, 10)

	ensemble.AddModel(lstmModel, 0.6)
	ensemble.AddModel(gruModel, 0.4)

	if len(ensemble.Models) != 2 {
		t.Errorf("Expected 2 models in ensemble, got %d", len(ensemble.Models))
	}
}

func createTestData(count int) []gpu.WorkloadDataPoint {
	baseTime := time.Now().Add(-time.Duration(count) * time.Minute)
	data := make([]gpu.WorkloadDataPoint, count)

	for i := 0; i < count; i++ {
		// Create synthetic data with some pattern
		queueLength := 20 + int(10*float64(i%24)/24) + i%5 // Daily pattern + small variation
		utilization := 0.3 + 0.4*float64(queueLength)/50   // Utilization correlates with queue

		data[i] = gpu.WorkloadDataPoint{
			Timestamp:      baseTime.Add(time.Duration(i) * time.Minute),
			QueueLength:    queueLength,
			ActiveTasks:    int(float64(queueLength) * 0.8),
			AvgUtilization: utilization,
			NodesActive:    max(1, queueLength/10),
		}
	}

	return data
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func TestLSTMModel_Training(t *testing.T) {
	model := gpu.NewLSTMModel(10, 16, 1, 5, 0.01, 10) // Smaller model for faster testing

	// Test with insufficient data
	smallData := createTestData(5)
	err := model.Train(smallData)
	if err == nil {
		t.Error("Expected error with insufficient training data")
	}

	// Test with sufficient data
	data := createTestData(50)
	err = model.Train(data)
	if err != nil {
		t.Errorf("Training failed: %v", err)
	}

	// Check that accuracy is set
	accuracy := model.GetAccuracy()
	if accuracy < 0 || accuracy > 1 {
		t.Errorf("Invalid accuracy value: %f", accuracy)
	}

	// Test prediction
	prediction, confidence, err := model.Predict(data)
	if err != nil {
		t.Errorf("Prediction failed: %v", err)
	}

	if prediction < 0 {
		t.Errorf("Invalid prediction: %d", prediction)
	}

	if confidence < 0 || confidence > 1 {
		t.Errorf("Invalid confidence: %f", confidence)
	}
}

func TestGRUModel_Training(t *testing.T) {
	model := gpu.NewGRUModel(10, 16, 1, 5, 0.01, 10) // Smaller model for faster testing

	// Test with insufficient data
	smallData := createTestData(5)
	err := model.Train(smallData)
	if err == nil {
		t.Error("Expected error with insufficient training data")
	}

	// Test with sufficient data
	data := createTestData(50)
	err = model.Train(data)
	if err != nil {
		t.Errorf("Training failed: %v", err)
	}

	// Check that accuracy is set
	accuracy := model.GetAccuracy()
	if accuracy < 0 || accuracy > 1 {
		t.Errorf("Invalid accuracy value: %f", accuracy)
	}

	// Test prediction
	prediction, confidence, err := model.Predict(data)
	if err != nil {
		t.Errorf("Prediction failed: %v", err)
	}

	if prediction < 0 {
		t.Errorf("Invalid prediction: %d", prediction)
	}

	if confidence < 0 || confidence > 1 {
		t.Errorf("Invalid confidence: %f", confidence)
	}
}

func TestDeepLearningEnsemble_Training(t *testing.T) {
	ensemble := gpu.NewDeepLearningEnsemble()

	// Add smaller models for faster testing
	lstmModel := gpu.NewLSTMModel(10, 8, 1, 3, 0.01, 5)
	gruModel := gpu.NewGRUModel(10, 8, 1, 3, 0.01, 5)

	ensemble.AddModel(lstmModel, 0.6)
	ensemble.AddModel(gruModel, 0.4)

	// Test with sufficient data
	data := createTestData(30)
	err := ensemble.Train(data)
	if err != nil {
		t.Errorf("Ensemble training failed: %v", err)
	}

	// Check that ensemble accuracy is calculated
	accuracy := ensemble.GetAccuracy()
	if accuracy < 0 || accuracy > 1 {
		t.Errorf("Invalid ensemble accuracy: %f", accuracy)
	}

	// Test prediction
	prediction, confidence, err := ensemble.Predict(data)
	if err != nil {
		t.Errorf("Ensemble prediction failed: %v", err)
	}

	if prediction < 0 {
		t.Errorf("Invalid ensemble prediction: %d", prediction)
	}

	if confidence < 0 || confidence > 1 {
		t.Errorf("Invalid ensemble confidence: %f", confidence)
	}
}

func TestLSTMPredictionModel_Integration(t *testing.T) {
	model := gpu.NewLSTMPredictionModel()

	// Test training
	data := createTestData(30)
	err := model.Train(data)
	if err != nil {
		t.Errorf("LSTM prediction model training failed: %v", err)
	}

	// Test prediction
	horizon := time.Hour
	prediction, err := model.Predict(horizon)
	if err != nil {
		t.Errorf("LSTM prediction model prediction failed: %v", err)
	}

	// Validate prediction structure
	if prediction.PredictedQueue < 0 {
		t.Errorf("Invalid predicted queue length: %d", prediction.PredictedQueue)
	}

	if prediction.PredictedTasks < 0 {
		t.Errorf("Invalid predicted tasks: %d", prediction.PredictedTasks)
	}

	if prediction.PredictedNodes < 0 {
		t.Errorf("Invalid predicted nodes: %d", prediction.PredictedNodes)
	}

	if prediction.Confidence < 0 || prediction.Confidence > 1 {
		t.Errorf("Invalid confidence: %f", prediction.Confidence)
	}

	// Check that timestamp is in the future
	if !prediction.Timestamp.After(time.Now()) {
		t.Error("Prediction timestamp should be in the future")
	}
}

func TestGRUPredictionModel_Integration(t *testing.T) {
	model := gpu.NewGRUPredictionModel()

	// Test training
	data := createTestData(30)
	err := model.Train(data)
	if err != nil {
		t.Errorf("GRU prediction model training failed: %v", err)
	}

	// Test prediction
	horizon := time.Hour
	prediction, err := model.Predict(horizon)
	if err != nil {
		t.Errorf("GRU prediction model prediction failed: %v", err)
	}

	// Validate prediction structure
	if prediction.PredictedQueue < 0 {
		t.Errorf("Invalid predicted queue length: %d", prediction.PredictedQueue)
	}

	if prediction.PredictedTasks < 0 {
		t.Errorf("Invalid predicted tasks: %d", prediction.PredictedTasks)
	}

	if prediction.PredictedNodes < 0 {
		t.Errorf("Invalid predicted nodes: %d", prediction.PredictedNodes)
	}

	if prediction.Confidence < 0 || prediction.Confidence > 1 {
		t.Errorf("Invalid confidence: %f", prediction.Confidence)
	}
}

func TestDeepLearningEnsemblePredictionModel_Integration(t *testing.T) {
	model := gpu.NewDeepLearningEnsemblePredictionModel()

	// Test training
	data := createTestData(30)
	err := model.Train(data)
	if err != nil {
		t.Errorf("Deep learning ensemble prediction model training failed: %v", err)
	}

	// Test prediction
	horizon := time.Hour
	prediction, err := model.Predict(horizon)
	if err != nil {
		t.Errorf("Deep learning ensemble prediction model prediction failed: %v", err)
	}

	// Validate prediction structure
	if prediction.PredictedQueue < 0 {
		t.Errorf("Invalid predicted queue length: %d", prediction.PredictedQueue)
	}

	if prediction.PredictedTasks < 0 {
		t.Errorf("Invalid predicted tasks: %d", prediction.PredictedTasks)
	}

	if prediction.PredictedNodes < 0 {
		t.Errorf("Invalid predicted nodes: %d", prediction.PredictedNodes)
	}

	if prediction.Confidence < 0 || prediction.Confidence > 1 {
		t.Errorf("Invalid confidence: %f", prediction.Confidence)
	}
}

func TestWorkloadPredictor_DeepLearningModels(t *testing.T) {
	// Test LSTM model integration
	lstmConfig := gpu.PredictionConfig{
		ModelType:     "lstm",
		HistoryWindow: 24 * time.Hour,
		MinDataPoints: 20,
	}

	lstmPredictor := gpu.NewWorkloadPredictor(lstmConfig)
	if lstmPredictor == nil {
		t.Fatal("Failed to create LSTM predictor")
	}

	// Test GRU model integration
	gruConfig := gpu.PredictionConfig{
		ModelType:     "gru",
		HistoryWindow: 24 * time.Hour,
		MinDataPoints: 20,
	}

	gruPredictor := gpu.NewWorkloadPredictor(gruConfig)
	if gruPredictor == nil {
		t.Fatal("Failed to create GRU predictor")
	}

	// Test deep learning ensemble integration
	ensembleConfig := gpu.PredictionConfig{
		ModelType:     "deep_learning_ensemble",
		HistoryWindow: 24 * time.Hour,
		MinDataPoints: 20,
	}

	ensemblePredictor := gpu.NewWorkloadPredictor(ensembleConfig)
	if ensemblePredictor == nil {
		t.Fatal("Failed to create deep learning ensemble predictor")
	}

	// Add test data and verify predictions work
	data := createTestData(25)
	for _, point := range data {
		lstmPredictor.AddDataPoint(point)
		gruPredictor.AddDataPoint(point)
		ensemblePredictor.AddDataPoint(point)
	}

	// Test predictions
	horizon := time.Hour

	lstmPrediction, err := lstmPredictor.GetPrediction(horizon)
	if err != nil {
		t.Errorf("LSTM predictor failed: %v", err)
	} else {
		t.Logf("LSTM prediction: Queue=%d, Confidence=%.2f", lstmPrediction.PredictedQueue, lstmPrediction.Confidence)
	}

	gruPrediction, err := gruPredictor.GetPrediction(horizon)
	if err != nil {
		t.Errorf("GRU predictor failed: %v", err)
	} else {
		t.Logf("GRU prediction: Queue=%d, Confidence=%.2f", gruPrediction.PredictedQueue, gruPrediction.Confidence)
	}

	ensemblePrediction, err := ensemblePredictor.GetPrediction(horizon)
	if err != nil {
		t.Errorf("Ensemble predictor failed: %v", err)
	} else {
		t.Logf("Ensemble prediction: Queue=%d, Confidence=%.2f", ensemblePrediction.PredictedQueue, ensemblePrediction.Confidence)
	}
}

func TestLSTMModel_FeatureEngineering(t *testing.T) {
	model := gpu.NewLSTMModel(10, 8, 1, 3, 0.01, 5)

	// Test that the model uses feature engineering
	if model.FeatureEngineer == nil {
		t.Error("LSTM model should have feature engineer")
	}

	// Create data with temporal patterns
	data := createTestDataWithPatterns(30)

	// Train model
	err := model.Train(data)
	if err != nil {
		t.Errorf("Training with feature engineering failed: %v", err)
	}

	// Verify that feature engineering is applied
	if len(model.FeatureMins) == 0 || len(model.FeatureMaxs) == 0 {
		t.Error("Feature normalization parameters should be set after training")
	}
}

func TestGRUModel_FeatureEngineering(t *testing.T) {
	model := gpu.NewGRUModel(10, 8, 1, 3, 0.01, 5)

	// Test that the model uses feature engineering
	if model.FeatureEngineer == nil {
		t.Error("GRU model should have feature engineer")
	}

	// Create data with temporal patterns
	data := createTestDataWithPatterns(30)

	// Train model
	err := model.Train(data)
	if err != nil {
		t.Errorf("Training with feature engineering failed: %v", err)
	}

	// Verify that feature engineering is applied
	if len(model.FeatureMins) == 0 || len(model.FeatureMaxs) == 0 {
		t.Error("Feature normalization parameters should be set after training")
	}
}

func createTestDataWithPatterns(count int) []gpu.WorkloadDataPoint {
	baseTime := time.Now().Add(-time.Duration(count) * time.Hour) // Hourly data
	data := make([]gpu.WorkloadDataPoint, count)

	for i := 0; i < count; i++ {
		timestamp := baseTime.Add(time.Duration(i) * time.Hour)
		hour := timestamp.Hour()

		// Create realistic patterns
		baseQueue := 30
		dailyPattern := int(10 * (1 + 0.5*float64(hour)/12)) // Peak during mid-day
		weekdayPattern := 0
		if timestamp.Weekday() >= time.Monday && timestamp.Weekday() <= time.Friday {
			weekdayPattern = 10 // Higher load on weekdays
		}

		queueLength := baseQueue + dailyPattern + weekdayPattern + (i % 3) // Small random variation
		utilization := 0.2 + 0.6*float64(queueLength)/60                   // Utilization correlates with queue

		data[i] = gpu.WorkloadDataPoint{
			Timestamp:      timestamp,
			QueueLength:    queueLength,
			ActiveTasks:    int(float64(queueLength) * 0.8),
			AvgUtilization: utilization,
			NodesActive:    max(1, queueLength/15),
		}
	}

	return data
}

// Benchmark tests for performance evaluation
func BenchmarkLSTMModel_Training(b *testing.B) {
	data := createTestData(50)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		model := gpu.NewLSTMModel(10, 16, 1, 5, 0.01, 10)
		model.Train(data)
	}
}

func BenchmarkGRUModel_Training(b *testing.B) {
	data := createTestData(50)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		model := gpu.NewGRUModel(10, 16, 1, 5, 0.01, 10)
		model.Train(data)
	}
}

func BenchmarkLSTMModel_Prediction(b *testing.B) {
	model := gpu.NewLSTMModel(10, 16, 1, 5, 0.01, 10)
	data := createTestData(50)
	model.Train(data)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		model.Predict(data)
	}
}

func BenchmarkGRUModel_Prediction(b *testing.B) {
	model := gpu.NewGRUModel(10, 16, 1, 5, 0.01, 10)
	data := createTestData(50)
	model.Train(data)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		model.Predict(data)
	}
}

func BenchmarkDeepLearningEnsemble_Prediction(b *testing.B) {
	ensemble := gpu.NewDeepLearningEnsemble()

	lstmModel := gpu.NewLSTMModel(10, 8, 1, 3, 0.01, 5)
	gruModel := gpu.NewGRUModel(10, 8, 1, 3, 0.01, 5)

	ensemble.AddModel(lstmModel, 0.6)
	ensemble.AddModel(gruModel, 0.4)

	data := createTestData(30)
	ensemble.Train(data)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ensemble.Predict(data)
	}
}
