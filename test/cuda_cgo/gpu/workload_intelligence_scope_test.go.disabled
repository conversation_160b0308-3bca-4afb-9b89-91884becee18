package gpu

import (
	"context"
	"log"
	"testing"
	"time"

	"neuralmetergo/internal/gpu"
)

func TestScopeCompliantWorkloadIntelligenceCoordinator_Creation(t *testing.T) {
	// Create mock dependencies
	multiDeviceMgr := &gpu.MultiDeviceManager{}
	performanceMon := &gpu.GPUPerformanceMonitor{}
	config := gpu.DefaultIntelligenceConfig()
	logger := log.Default()

	coordinator, err := gpu.NewWorkloadIntelligenceCoordinator(
		multiDeviceMgr,
		performanceMon,
		config,
		logger,
	)

	if err != nil {
		t.Fatalf("Failed to create coordinator: %v", err)
	}

	if coordinator == nil {
		t.Fatal("Coordinator should not be nil")
	}

	// Verify configuration excludes thermal control
	stats := coordinator.GetStats()
	if scopeCompliance, ok := stats["scope_compliance"].(string); ok {
		if scopeCompliance != "no_thermal_control" {
			t.<PERSON><PERSON><PERSON>("Expected scope_compliance to be 'no_thermal_control', got %s", scopeCompliance)
		}
	} else {
		t.Error("Expected scope_compliance field in stats")
	}
}

func TestScopeCompliantIntelligenceConfig_Validation(t *testing.T) {
	// Test valid configuration
	validConfig := gpu.IntelligenceConfig{
		UtilizationWeight:       0.35,
		MemoryWeight:            0.35,
		PerformanceWeight:       0.20,
		EfficiencyWeight:        0.10,
		MaxUtilizationThreshold: 0.85,
		MaxMemoryThreshold:      0.90,
		MinEfficiencyThreshold:  0.60,
	}

	multiDeviceMgr := &gpu.MultiDeviceManager{}
	performanceMon := &gpu.GPUPerformanceMonitor{}

	_, err := gpu.NewWorkloadIntelligenceCoordinator(
		multiDeviceMgr,
		performanceMon,
		validConfig,
		nil,
	)

	if err != nil {
		t.Fatalf("Valid configuration should not produce error: %v", err)
	}

	// Test invalid configuration (weights don't sum to 1.0)
	invalidConfig := gpu.IntelligenceConfig{
		UtilizationWeight:       0.5,
		MemoryWeight:            0.5,
		PerformanceWeight:       0.5, // This makes total > 1.0
		EfficiencyWeight:        0.1,
		MaxUtilizationThreshold: 0.85,
		MaxMemoryThreshold:      0.90,
		MinEfficiencyThreshold:  0.60,
	}

	_, err = gpu.NewWorkloadIntelligenceCoordinator(
		multiDeviceMgr,
		performanceMon,
		invalidConfig,
		nil,
	)

	if err == nil {
		t.Fatal("Invalid configuration should produce error")
	}
}

func TestScopeCompliantDefaultIntelligenceConfig_NoThermalControl(t *testing.T) {
	config := gpu.DefaultIntelligenceConfig()

	// Verify no thermal weight in default configuration
	totalWeight := config.UtilizationWeight + config.MemoryWeight +
		config.PerformanceWeight + config.EfficiencyWeight

	if totalWeight < 0.99 || totalWeight > 1.01 {
		t.Errorf("Weights should sum to 1.0, got %.2f", totalWeight)
	}

	// Verify thermal-related fields are not present (by checking all weights are accounted for)
	expectedWeights := []float64{
		config.UtilizationWeight,
		config.MemoryWeight,
		config.PerformanceWeight,
		config.EfficiencyWeight,
	}

	for _, weight := range expectedWeights {
		if weight < 0 {
			t.Errorf("All weights should be non-negative, found negative weight: %.2f", weight)
		}
	}

	// Verify graceful degradation is enabled
	if !config.GracefulDegradation {
		t.Error("Graceful degradation should be enabled by default")
	}

	// Verify required metrics don't include thermal
	requiredMetrics := config.RequiredMetrics
	for _, metric := range requiredMetrics {
		if metric == "thermal" || metric == "temperature" {
			t.Errorf("Required metrics should not include thermal metrics, found: %s", metric)
		}
	}
}

func TestScopeCompliantIntelligentScheduler_Creation(t *testing.T) {
	// Create dependencies
	multiDeviceMgr := &gpu.MultiDeviceManager{}
	performanceMon := &gpu.GPUPerformanceMonitor{}
	config := gpu.DefaultIntelligenceConfig()

	coordinator, err := gpu.NewWorkloadIntelligenceCoordinator(
		multiDeviceMgr,
		performanceMon,
		config,
		nil,
	)
	if err != nil {
		t.Fatalf("Failed to create coordinator: %v", err)
	}

	scheduler, err := gpu.NewIntelligentScheduler(
		coordinator,
		multiDeviceMgr,
		nil,
	)

	if err != nil {
		t.Fatalf("Failed to create scheduler: %v", err)
	}

	if scheduler == nil {
		t.Fatal("Scheduler should not be nil")
	}

	// Verify initial stats
	stats := scheduler.GetStats()
	if stats.CurrentQueueSize != 0 {
		t.Errorf("Initial queue size should be 0, got %d", stats.CurrentQueueSize)
	}
	if stats.ActiveTasks != 0 {
		t.Errorf("Initial active tasks should be 0, got %d", stats.ActiveTasks)
	}
}

func TestScopeCompliantCloudEnvironmentDetector_Creation(t *testing.T) {
	detector := gpu.NewCloudEnvironmentDetector(nil)

	if detector == nil {
		t.Fatal("Detector should not be nil")
	}

	// Test environment detection
	ctx := context.Background()
	env, err := detector.DetectEnvironment(ctx)

	if err != nil {
		t.Fatalf("Environment detection should not fail: %v", err)
	}

	if env == nil {
		t.Fatal("Environment should not be nil")
	}

	// Verify no thermal metrics in capabilities
	capabilities := env.Capabilities
	// Check that thermal metrics are not included (by verifying only expected fields exist)
	expectedFields := []bool{
		capabilities.UtilizationMetrics,
		capabilities.MemoryMetrics,
		capabilities.PerformanceMetrics,
		capabilities.NetworkMetrics,
	}

	// All fields should be boolean values (no thermal field should exist)
	for i, field := range expectedFields {
		_ = field // Use the field to verify it exists
		_ = i     // Suppress unused variable warning
	}
}

func TestScopeCompliantCloudCompatibilityManager_AdaptConfiguration(t *testing.T) {
	detector := gpu.NewCloudEnvironmentDetector(nil)
	manager := gpu.NewCloudCompatibilityManager(detector, nil)

	ctx := context.Background()
	err := manager.Initialize(ctx)
	if err != nil {
		t.Fatalf("Manager initialization should not fail: %v", err)
	}

	// Test configuration adaptation
	originalConfig := gpu.DefaultIntelligenceConfig()
	adaptedConfig := manager.AdaptConfiguration(&originalConfig)

	if adaptedConfig == nil {
		t.Fatal("Adapted configuration should not be nil")
	}

	// Verify weights still sum to 1.0 after adaptation
	totalWeight := adaptedConfig.UtilizationWeight + adaptedConfig.MemoryWeight +
		adaptedConfig.PerformanceWeight + adaptedConfig.EfficiencyWeight

	if totalWeight < 0.99 || totalWeight > 1.01 {
		t.Errorf("Adapted weights should sum to 1.0, got %.2f", totalWeight)
	}

	// Verify graceful degradation is enabled for cloud environments
	if manager.IsCloudEnvironment() && !adaptedConfig.GracefulDegradation {
		t.Error("Graceful degradation should be enabled for cloud environments")
	}
}

func TestScopeCompliantWorkloadRequest_NoThermalConstraints(t *testing.T) {
	// Create a workload request
	request := &gpu.WorkloadRequest{
		TaskID:           "test_task",
		TaskType:         "inference",
		Priority:         5,
		MemoryRequired:   1024 * 1024 * 1024, // 1GB
		ComputeIntensity: 0.8,
		EstimatedTime:    time.Minute * 5,
		Constraints: gpu.TaskConstraints{
			RequiredVendor:     "NVIDIA",
			MinMemoryAvailable: 512 * 1024 * 1024, // 512MB
		},
	}

	// Verify constraints don't include thermal limits
	constraints := request.Constraints

	// Check that only expected constraint fields exist (no thermal constraints)
	if constraints.RequiredVendor == "" && len(constraints.ExcludedDevices) == 0 &&
		len(constraints.PreferredDevices) == 0 && constraints.MinMemoryAvailable == 0 &&
		len(constraints.RequiredFeatures) == 0 {
		// This is fine - empty constraints
	}

	// Verify required vendor is set correctly
	if constraints.RequiredVendor != "NVIDIA" {
		t.Errorf("Expected vendor NVIDIA, got %s", constraints.RequiredVendor)
	}

	// Verify memory requirement is set correctly
	if constraints.MinMemoryAvailable != 512*1024*1024 {
		t.Errorf("Expected memory requirement 512MB, got %d", constraints.MinMemoryAvailable)
	}
}
