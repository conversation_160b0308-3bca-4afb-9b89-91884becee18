package gpu

import (
	"context"
	"fmt"
	"log"
	"os"
	"testing"
	"time"

	"neuralmetergo/internal/gpu"
)

// TestWorkloadIntelligenceCoordinator_Creation tests coordinator creation and validation
func TestWorkloadIntelligenceCoordinator_Creation(t *testing.T) {
	tests := []struct {
		name        string
		config      gpu.IntelligenceConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "Valid configuration",
			config: gpu.IntelligenceConfig{
				UtilizationWeight:       0.35,
				MemoryWeight:            0.35,
				PerformanceWeight:       0.20,
				EfficiencyWeight:        0.10,
				MaxUtilizationThreshold: 0.85,
				MaxMemoryThreshold:      0.90,
				MinEfficiencyThreshold:  0.60,
				RebalanceInterval:       time.Minute * 5,
				TaskHistorySize:         1000,
			},
			expectError: false,
		},
		{
			name: "Invalid weights sum",
			config: gpu.IntelligenceConfig{
				UtilizationWeight:       0.5,
				MemoryWeight:            0.5,
				PerformanceWeight:       0.5, // Sum > 1.0
				EfficiencyWeight:        0.1,
				MaxUtilizationThreshold: 0.85,
				MaxMemoryThreshold:      0.90,
				MinEfficiencyThreshold:  0.60,
				RebalanceInterval:       time.Minute * 5,
				TaskHistorySize:         1000,
			},
			expectError: true,
			errorMsg:    "scoring weights must sum to 1.0",
		},
		{
			name: "Invalid utilization threshold",
			config: gpu.IntelligenceConfig{
				UtilizationWeight:       0.25,
				MemoryWeight:            0.25,
				PerformanceWeight:       0.25,
				EfficiencyWeight:        0.25,
				MaxUtilizationThreshold: 1.5, // Invalid > 1.0
				MaxMemoryThreshold:      0.90,
				MinEfficiencyThreshold:  0.60,
				RebalanceInterval:       time.Minute * 5,
				TaskHistorySize:         1000,
			},
			expectError: true,
			errorMsg:    "max utilization threshold must be between 0 and 1",
		},
		{
			name: "Invalid rebalance interval",
			config: gpu.IntelligenceConfig{
				UtilizationWeight:       0.25,
				MemoryWeight:            0.25,
				PerformanceWeight:       0.25,
				EfficiencyWeight:        0.25,
				MaxUtilizationThreshold: 0.85,
				MaxMemoryThreshold:      0.90,
				MinEfficiencyThreshold:  0.60,
				RebalanceInterval:       -time.Minute, // Invalid negative
				TaskHistorySize:         1000,
			},
			expectError: true,
			errorMsg:    "rebalance interval must be positive",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

			// Create mock components (nil for this test)
			coordinator, err := gpu.NewWorkloadIntelligenceCoordinator(
				nil, nil, nil, tt.config, logger,
			)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error containing '%s', got nil", tt.errorMsg)
					return
				}
				if err.Error() == "" || len(err.Error()) == 0 {
					t.Errorf("Expected non-empty error message")
					return
				}
				// Note: We can't check exact error message without knowing the validation details
				t.Logf("Got expected error: %v", err)
			} else {
				if err != nil {
					t.Errorf("Expected no error, got: %v", err)
					return
				}
				if coordinator == nil {
					t.Errorf("Expected coordinator to be created, got nil")
					return
				}
			}
		})
	}
}

// TestWorkloadIntelligenceCoordinator_DefaultConfig tests the default configuration
func TestWorkloadIntelligenceCoordinator_DefaultConfig(t *testing.T) {
	config := gpu.DefaultIntelligenceConfig()

	// Test that weights sum to 1.0
	totalWeight := config.UtilizationWeight + config.MemoryWeight +
		config.PerformanceWeight + config.EfficiencyWeight

	if totalWeight < 0.99 || totalWeight > 1.01 {
		t.Errorf("Default config weights should sum to 1.0, got %.3f", totalWeight)
	}

	// Test reasonable defaults
	if config.MaxUtilizationThreshold <= 0 || config.MaxUtilizationThreshold > 1 {
		t.Errorf("Invalid default utilization threshold: %f", config.MaxUtilizationThreshold)
	}

	if config.MaxMemoryThreshold <= 0 || config.MaxMemoryThreshold > 1 {
		t.Errorf("Invalid default memory threshold: %f", config.MaxMemoryThreshold)
	}

	if config.RebalanceInterval <= 0 {
		t.Errorf("Invalid default rebalance interval: %v", config.RebalanceInterval)
	}

	if config.TaskHistorySize <= 0 {
		t.Errorf("Invalid default task history size: %d", config.TaskHistorySize)
	}

	// Test cloud compatibility defaults
	if !config.GracefulDegradation {
		t.Errorf("Expected graceful degradation to be enabled by default")
	}

	if len(config.RequiredMetrics) == 0 {
		t.Errorf("Expected some required metrics to be specified")
	}

	if config.FallbackStrategy == "" {
		t.Errorf("Expected fallback strategy to be specified")
	}
}

// MockMultiDeviceManager provides a mock implementation for testing
type MockMultiDeviceManager struct {
	devices     []*gpu.ManagedDevice
	initialized bool
}

func (m *MockMultiDeviceManager) GetActiveDevices() []*gpu.ManagedDevice {
	var active []*gpu.ManagedDevice
	for _, device := range m.devices {
		if device.IsActive {
			active = append(active, device)
		}
	}
	return active
}

func (m *MockMultiDeviceManager) GetDevice(id string) (*gpu.ManagedDevice, error) {
	for _, device := range m.devices {
		if device.Device.ID == id {
			return device, nil
		}
	}
	return nil, fmt.Errorf("device %s not found", id)
}

func (m *MockMultiDeviceManager) IsInitialized() bool {
	return m.initialized
}

// MockGPUPerformanceMonitor provides a mock implementation for testing
type MockGPUPerformanceMonitor struct {
	snapshot *gpu.SystemPerformanceSnapshot
	err      error
}

func (m *MockGPUPerformanceMonitor) GetCurrentSnapshot() (*gpu.SystemPerformanceSnapshot, error) {
	return m.snapshot, m.err
}

// createTestDevices creates test devices for mocking
func createTestDevices() []*gpu.ManagedDevice {
	return []*gpu.ManagedDevice{
		{
			Device: &gpu.GPUDevice{
				ID:     "gpu-0",
				Vendor: "NVIDIA",
				Name:   "RTX 4090",
			},
			IsActive:  true,
			LoadLevel: 0.2, // 20% load
		},
		{
			Device: &gpu.GPUDevice{
				ID:     "gpu-1",
				Vendor: "NVIDIA",
				Name:   "RTX 4080",
			},
			IsActive:  true,
			LoadLevel: 0.8, // 80% load
		},
		{
			Device: &gpu.GPUDevice{
				ID:     "gpu-2",
				Vendor: "AMD",
				Name:   "RX 7900 XTX",
			},
			IsActive:  true,
			LoadLevel: 0.5, // 50% load
		},
		{
			Device: &gpu.GPUDevice{
				ID:     "gpu-3",
				Vendor: "NVIDIA",
				Name:   "RTX 4070",
			},
			IsActive:  false, // Inactive device
			LoadLevel: 0.0,
		},
	}
}

// createTestPerformanceSnapshot creates a test performance snapshot
func createTestPerformanceSnapshot() *gpu.SystemPerformanceSnapshot {
	return &gpu.SystemPerformanceSnapshot{
		Timestamp: time.Now(),
		DevicePerformance: map[string]*gpu.DevicePerformance{
			"gpu-0": {
				DeviceID: "gpu-0",
				UtilizationMetrics: gpu.UtilizationMetrics{
					GPUUtilization: 0.25, // 25% utilization
				},
				MemoryMetrics: gpu.MemoryPerformanceMetrics{
					TotalMemory: 24 * 1024 * 1024 * 1024, // 24GB
					UsedMemory:  6 * 1024 * 1024 * 1024,  // 6GB used
				},
				ThermalMetrics: gpu.ThermalMetrics{
					CurrentTemp: 45, // 45°C
				},
				TaskMetrics: gpu.TaskPerformanceMetrics{
					TaskSuccessRate: 0.95, // 95% success rate
					TasksPerSecond:  8.5,  // 8.5 tasks/sec
				},
				EfficiencyScore: 0.85, // 85% efficiency
			},
			"gpu-1": {
				DeviceID: "gpu-1",
				UtilizationMetrics: gpu.UtilizationMetrics{
					GPUUtilization: 0.85, // 85% utilization
				},
				MemoryMetrics: gpu.MemoryPerformanceMetrics{
					TotalMemory: 16 * 1024 * 1024 * 1024, // 16GB
					UsedMemory:  14 * 1024 * 1024 * 1024, // 14GB used
				},
				ThermalMetrics: gpu.ThermalMetrics{
					CurrentTemp: 78, // 78°C
				},
				TaskMetrics: gpu.TaskPerformanceMetrics{
					TaskSuccessRate: 0.92, // 92% success rate
					TasksPerSecond:  12.0, // 12 tasks/sec
				},
				EfficiencyScore: 0.75, // 75% efficiency
			},
			"gpu-2": {
				DeviceID: "gpu-2",
				UtilizationMetrics: gpu.UtilizationMetrics{
					GPUUtilization: 0.50, // 50% utilization
				},
				MemoryMetrics: gpu.MemoryPerformanceMetrics{
					TotalMemory: 24 * 1024 * 1024 * 1024, // 24GB
					UsedMemory:  12 * 1024 * 1024 * 1024, // 12GB used
				},
				ThermalMetrics: gpu.ThermalMetrics{
					CurrentTemp: 65, // 65°C
				},
				TaskMetrics: gpu.TaskPerformanceMetrics{
					TaskSuccessRate: 0.88, // 88% success rate
					TasksPerSecond:  10.2, // 10.2 tasks/sec
				},
				EfficiencyScore: 0.80, // 80% efficiency
			},
		},
	}
}

// TestWorkloadIntelligenceCoordinator_BasicFunctionality tests basic coordinator functionality
func TestWorkloadIntelligenceCoordinator_BasicFunctionality(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	config := gpu.DefaultIntelligenceConfig()

	// Create mock components
	devices := createTestDevices()
	mockMultiDeviceMgr := &MockMultiDeviceManager{
		devices:     devices,
		initialized: true,
	}

	mockPerfMon := &MockGPUPerformanceMonitor{
		snapshot: createTestPerformanceSnapshot(),
		err:      nil,
	}

	// Create coordinator
	coordinator, err := gpu.NewWorkloadIntelligenceCoordinator(
		mockMultiDeviceMgr, mockPerfMon, nil, config, logger,
	)
	if err != nil {
		t.Fatalf("Failed to create coordinator: %v", err)
	}

	// Test starting the coordinator
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	err = coordinator.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start coordinator: %v", err)
	}

	// Allow some time for initialization
	time.Sleep(100 * time.Millisecond)

	// Test getting device scores
	scores := coordinator.GetDeviceScores()
	if len(scores) == 0 {
		t.Errorf("Expected device scores, got empty map")
	}

	// Test that active devices have scores
	activeDevices := mockMultiDeviceMgr.GetActiveDevices()
	for _, device := range activeDevices {
		score, exists := scores[device.Device.ID]
		if !exists {
			t.Errorf("Expected score for device %s", device.Device.ID)
			continue
		}

		if score.DeviceID != device.Device.ID {
			t.Errorf("Score device ID mismatch: expected %s, got %s", device.Device.ID, score.DeviceID)
		}

		if score.OverallScore < 0 || score.OverallScore > 1 {
			t.Errorf("Invalid overall score for device %s: %f", device.Device.ID, score.OverallScore)
		}

		t.Logf("Device %s: Overall Score = %.3f, Available = %v",
			device.Device.ID, score.OverallScore, score.IsAvailable)
	}

	// Test getting statistics
	stats := coordinator.GetStats()
	if stats == nil {
		t.Errorf("Expected statistics, got nil")
	}

	// Verify expected statistics keys
	expectedKeys := []string{
		"is_running", "total_tasks_routed", "successful_routes",
		"success_rate", "rebalance_count", "active_devices",
	}
	for _, key := range expectedKeys {
		if _, exists := stats[key]; !exists {
			t.Errorf("Expected statistic key '%s' not found", key)
		}
	}

	// Test stopping the coordinator
	err = coordinator.Stop()
	if err != nil {
		t.Errorf("Failed to stop coordinator: %v", err)
	}
}

// TestWorkloadIntelligenceCoordinator_TaskAssignment tests workload assignment functionality
func TestWorkloadIntelligenceCoordinator_TaskAssignment(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	config := gpu.DefaultIntelligenceConfig()

	// Create mock components
	devices := createTestDevices()
	mockMultiDeviceMgr := &MockMultiDeviceManager{
		devices:     devices,
		initialized: true,
	}

	mockPerfMon := &MockGPUPerformanceMonitor{
		snapshot: createTestPerformanceSnapshot(),
		err:      nil,
	}

	// Create coordinator
	coordinator, err := gpu.NewWorkloadIntelligenceCoordinator(
		mockMultiDeviceMgr, mockPerfMon, nil, nil, config, logger,
	)
	if err != nil {
		t.Fatalf("Failed to create coordinator: %v", err)
	}

	// Start coordinator
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	err = coordinator.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start coordinator: %v", err)
	}

	// Allow time for initialization
	time.Sleep(100 * time.Millisecond)

	// Test basic workload assignment
	request := &gpu.WorkloadRequest{
		TaskID:           "test-task-1",
		TaskType:         "inference",
		Priority:         1,
		MemoryRequired:   2 * 1024 * 1024 * 1024, // 2GB
		ComputeIntensity: 0.7,
		EstimatedTime:    time.Minute * 5,
		Constraints:      gpu.TaskConstraints{},
		Metadata:         map[string]string{"test": "true"},
	}

	assignment, err := coordinator.AssignWorkload(ctx, request)
	if err != nil {
		t.Fatalf("Failed to assign workload: %v", err)
	}

	if assignment == nil {
		t.Fatalf("Expected assignment, got nil")
	}

	if assignment.AssignedDevice == nil {
		t.Errorf("Expected assigned device, got nil")
	}

	if assignment.DeviceScore == nil {
		t.Errorf("Expected device score, got nil")
	}

	if assignment.AssignmentReason == "" {
		t.Errorf("Expected assignment reason, got empty string")
	}

	t.Logf("Assigned task to device %s with score %.3f, reason: %s",
		assignment.AssignedDevice.Device.ID, assignment.DeviceScore.OverallScore, assignment.AssignmentReason)

	// Test workload assignment with vendor constraint
	requestWithConstraint := &gpu.WorkloadRequest{
		TaskID:           "test-task-2",
		TaskType:         "training",
		Priority:         2,
		MemoryRequired:   4 * 1024 * 1024 * 1024, // 4GB
		ComputeIntensity: 0.9,
		EstimatedTime:    time.Minute * 10,
		Constraints: gpu.TaskConstraints{
			RequiredVendor: "NVIDIA",
		},
		Metadata: map[string]string{"vendor_test": "true"},
	}

	assignmentWithConstraint, err := coordinator.AssignWorkload(ctx, requestWithConstraint)
	if err != nil {
		t.Fatalf("Failed to assign workload with constraint: %v", err)
	}

	if assignmentWithConstraint.AssignedDevice.Device.Vendor != "NVIDIA" {
		t.Errorf("Expected NVIDIA device, got %s", assignmentWithConstraint.AssignedDevice.Device.Vendor)
	}

	t.Logf("Assigned constrained task to device %s (vendor: %s)",
		assignmentWithConstraint.AssignedDevice.Device.ID, assignmentWithConstraint.AssignedDevice.Device.Vendor)

	// Test task history
	history := coordinator.GetTaskHistory()
	if len(history) != 2 {
		t.Errorf("Expected 2 tasks in history, got %d", len(history))
	}

	// Stop coordinator
	err = coordinator.Stop()
	if err != nil {
		t.Errorf("Failed to stop coordinator: %v", err)
	}
}

// TestWorkloadIntelligenceCoordinator_GracefulDegradation tests fallback behavior
func TestWorkloadIntelligenceCoordinator_GracefulDegradation(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	config := gpu.DefaultIntelligenceConfig()
	config.GracefulDegradation = true

	// Create mock components with failing performance monitor
	devices := createTestDevices()
	mockMultiDeviceMgr := &MockMultiDeviceManager{
		devices:     devices,
		initialized: true,
	}

	mockPerfMon := &MockGPUPerformanceMonitor{
		snapshot: nil,
		err:      &gpu.MonitoringError{Message: "Performance monitoring unavailable"},
	}

	// Create coordinator
	coordinator, err := gpu.NewWorkloadIntelligenceCoordinator(
		mockMultiDeviceMgr, mockPerfMon, nil, nil, config, logger,
	)
	if err != nil {
		t.Fatalf("Failed to create coordinator: %v", err)
	}

	// Start coordinator
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	err = coordinator.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start coordinator: %v", err)
	}

	// Allow time for initialization
	time.Sleep(100 * time.Millisecond)

	// Test that basic functionality still works with degraded monitoring
	scores := coordinator.GetDeviceScores()
	if len(scores) == 0 {
		t.Errorf("Expected device scores even with degraded monitoring, got empty map")
	}

	// Verify that fallback scoring is being used
	for deviceID, score := range scores {
		if score.OverallScore < 0 || score.OverallScore > 1 {
			t.Errorf("Invalid fallback score for device %s: %f", deviceID, score.OverallScore)
		}

		// In fallback mode, some scores should be default values (0.5)
		if score.MemoryScore != 0.5 || score.ThermalScore != 0.5 {
			t.Logf("Device %s using fallback scoring: Memory=%.1f, Thermal=%.1f",
				deviceID, score.MemoryScore, score.ThermalScore)
		}
	}

	// Test workload assignment still works
	request := &gpu.WorkloadRequest{
		TaskID:           "fallback-task",
		TaskType:         "inference",
		Priority:         1,
		MemoryRequired:   1 * 1024 * 1024 * 1024, // 1GB
		ComputeIntensity: 0.5,
		EstimatedTime:    time.Minute * 2,
		Constraints:      gpu.TaskConstraints{},
		Metadata:         map[string]string{"fallback": "true"},
	}

	assignment, err := coordinator.AssignWorkload(ctx, request)
	if err != nil {
		t.Fatalf("Failed to assign workload in degraded mode: %v", err)
	}

	if assignment == nil {
		t.Fatalf("Expected assignment in degraded mode, got nil")
	}

	t.Logf("Successfully assigned task in degraded mode to device %s", assignment.AssignedDevice.Device.ID)

	// Stop coordinator
	err = coordinator.Stop()
	if err != nil {
		t.Errorf("Failed to stop coordinator: %v", err)
	}
}

// TestWorkloadIntelligenceCoordinator_ErrorCases tests error handling
func TestWorkloadIntelligenceCoordinator_ErrorCases(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	config := gpu.DefaultIntelligenceConfig()

	// Test with uninitialized multi-device manager
	mockMultiDeviceMgr := &MockMultiDeviceManager{
		devices:     createTestDevices(),
		initialized: false, // Not initialized
	}

	mockPerfMon := &MockGPUPerformanceMonitor{
		snapshot: createTestPerformanceSnapshot(),
		err:      nil,
	}

	coordinator, err := gpu.NewWorkloadIntelligenceCoordinator(
		mockMultiDeviceMgr, mockPerfMon, nil, nil, config, logger,
	)
	if err != nil {
		t.Fatalf("Failed to create coordinator: %v", err)
	}

	// Try to start with uninitialized manager
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	err = coordinator.Start(ctx)
	if err == nil {
		t.Errorf("Expected error when starting with uninitialized manager")
	} else {
		t.Logf("Got expected error: %v", err)
	}

	// Test assignment without running coordinator
	request := &gpu.WorkloadRequest{
		TaskID:           "error-task",
		TaskType:         "inference",
		Priority:         1,
		MemoryRequired:   1 * 1024 * 1024 * 1024,
		ComputeIntensity: 0.5,
		EstimatedTime:    time.Minute,
		Constraints:      gpu.TaskConstraints{},
	}

	assignment, err := coordinator.AssignWorkload(ctx, request)
	if err == nil {
		t.Errorf("Expected error when assigning workload to stopped coordinator")
	}
	if assignment != nil {
		t.Errorf("Expected nil assignment when coordinator not running")
	}

	// Test starting already running coordinator
	mockMultiDeviceMgr.initialized = true
	err = coordinator.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start coordinator: %v", err)
	}

	err = coordinator.Start(ctx)
	if err == nil {
		t.Errorf("Expected error when starting already running coordinator")
	}

	// Test stopping not running coordinator
	err = coordinator.Stop()
	if err != nil {
		t.Errorf("Failed to stop coordinator: %v", err)
	}

	err = coordinator.Stop()
	if err == nil {
		t.Errorf("Expected error when stopping already stopped coordinator")
	}
}
