package gpu

import (
	"testing"
	"time"

	"neuralmetergo/internal/gpu"
)

func TestAdvancedEnsemble_WeightedAveraging(t *testing.T) {
	config := gpu.EnsembleConfig{
		Method:               gpu.WeightedAveraging,
		IncludeTimeSeries:    true,
		IncludeDeepLearning:  true,
		AdaptiveLearningRate: 0.1,
		MetaLearnerType:      "linear",
	}

	ensemble := gpu.NewAdvancedEnsemble(config)

	// Generate test data
	data := generateAdvancedTestData(50)

	// Train ensemble
	err := ensemble.Train(data)
	if err != nil {
		t.Fatalf("Failed to train advanced ensemble: %v", err)
	}

	// Test prediction
	prediction, err := ensemble.Predict(time.Hour)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Failed to generate prediction: %v", err)
	}

	if prediction.PredictedQueue < 0 {
		t.<PERSON><PERSON><PERSON>("Invalid prediction queue length: %d", prediction.PredictedQueue)
	}

	if prediction.Confidence < 0 || prediction.Confidence > 1 {
		t.<PERSON><PERSON>("Invalid confidence: %f", prediction.Confidence)
	}

	accuracy := ensemble.GetAccuracy()
	if accuracy < 0 || accuracy > 1 {
		t.<PERSON><PERSON><PERSON>("Invalid accuracy: %f", accuracy)
	}

	t.Logf("Weighted Averaging Ensemble - Accuracy: %.2f%%, Prediction: %d", accuracy*100, prediction.PredictedQueue)
}

func TestAdvancedEnsemble_BayesianModelAveraging(t *testing.T) {
	config := gpu.EnsembleConfig{
		Method:               gpu.BayesianModelAveraging,
		IncludeTimeSeries:    true,
		IncludeDeepLearning:  true,
		AdaptiveLearningRate: 0.1,
		MetaLearnerType:      "linear",
	}

	ensemble := gpu.NewAdvancedEnsemble(config)

	// Generate test data
	data := generateAdvancedTestData(50)

	// Train ensemble
	err := ensemble.Train(data)
	if err != nil {
		t.Fatalf("Failed to train Bayesian ensemble: %v", err)
	}

	// Test prediction
	prediction, err := ensemble.Predict(time.Hour)
	if err != nil {
		t.Errorf("Failed to generate Bayesian prediction: %v", err)
	}

	accuracy := ensemble.GetAccuracy()
	t.Logf("Bayesian Model Averaging - Accuracy: %.2f%%, Prediction: %d", accuracy*100, prediction.PredictedQueue)
}

func TestAdvancedEnsemble_VotingEnsemble(t *testing.T) {
	config := gpu.EnsembleConfig{
		Method:               gpu.VotingEnsemble,
		IncludeTimeSeries:    true,
		IncludeDeepLearning:  true,
		AdaptiveLearningRate: 0.1,
		MetaLearnerType:      "linear",
	}

	ensemble := gpu.NewAdvancedEnsemble(config)

	// Generate test data
	data := generateAdvancedTestData(50)

	// Train ensemble
	err := ensemble.Train(data)
	if err != nil {
		t.Fatalf("Failed to train voting ensemble: %v", err)
	}

	// Test prediction
	prediction, err := ensemble.Predict(time.Hour)
	if err != nil {
		t.Errorf("Failed to generate voting prediction: %v", err)
	}

	accuracy := ensemble.GetAccuracy()
	t.Logf("Voting Ensemble - Accuracy: %.2f%%, Prediction: %d", accuracy*100, prediction.PredictedQueue)
}

func TestAdvancedEnsemble_Stacking(t *testing.T) {
	config := gpu.EnsembleConfig{
		Method:               gpu.Stacking,
		IncludeTimeSeries:    true,
		IncludeDeepLearning:  false, // Use only time series for faster testing
		AdaptiveLearningRate: 0.1,
		MetaLearnerType:      "linear",
	}

	ensemble := gpu.NewAdvancedEnsemble(config)

	// Generate test data (more data needed for stacking)
	data := generateAdvancedTestData(100)

	// Train ensemble
	err := ensemble.Train(data)
	if err != nil {
		t.Fatalf("Failed to train stacking ensemble: %v", err)
	}

	// Test prediction
	prediction, err := ensemble.Predict(time.Hour)
	if err != nil {
		t.Errorf("Failed to generate stacking prediction: %v", err)
	}

	accuracy := ensemble.GetAccuracy()
	t.Logf("Stacking Ensemble - Accuracy: %.2f%%, Prediction: %d", accuracy*100, prediction.PredictedQueue)
}

func TestAdvancedEnsemble_AdaptiveWeighting(t *testing.T) {
	config := gpu.EnsembleConfig{
		Method:               gpu.AdaptiveWeighting,
		IncludeTimeSeries:    true,
		IncludeDeepLearning:  true,
		AdaptiveLearningRate: 0.2,
		MetaLearnerType:      "linear",
	}

	ensemble := gpu.NewAdvancedEnsemble(config)

	// Generate test data
	data := generateAdvancedTestData(50)

	// Train ensemble
	err := ensemble.Train(data)
	if err != nil {
		t.Fatalf("Failed to train adaptive ensemble: %v", err)
	}

	// Test prediction
	prediction, err := ensemble.Predict(time.Hour)
	if err != nil {
		t.Errorf("Failed to generate adaptive prediction: %v", err)
	}

	accuracy := ensemble.GetAccuracy()
	t.Logf("Adaptive Weighting - Accuracy: %.2f%%, Prediction: %d", accuracy*100, prediction.PredictedQueue)
}

func TestAdvancedEnsemblePredictionModel_Integration(t *testing.T) {
	config := gpu.EnsembleConfig{
		Method:               gpu.WeightedAveraging,
		IncludeTimeSeries:    true,
		IncludeDeepLearning:  true,
		AdaptiveLearningRate: 0.1,
		MetaLearnerType:      "linear",
	}

	model := gpu.NewAdvancedEnsemblePredictionModel(config)

	// Generate test data
	data := generateAdvancedTestData(50)

	// Train model
	err := model.Train(data)
	if err != nil {
		t.Fatalf("Failed to train advanced ensemble prediction model: %v", err)
	}

	// Test prediction
	prediction, err := model.Predict(time.Hour)
	if err != nil {
		t.Errorf("Failed to generate prediction: %v", err)
	}

	accuracy := model.GetAccuracy()
	if accuracy < 0 || accuracy > 1 {
		t.Errorf("Invalid model accuracy: %f", accuracy)
	}

	t.Logf("Advanced Ensemble Model - Accuracy: %.2f%%, Prediction: %d", accuracy*100, prediction.PredictedQueue)
}

func TestWorkloadPredictor_AdvancedEnsembleIntegration(t *testing.T) {
	// Test advanced ensemble integration
	advancedConfig := gpu.PredictionConfig{
		ModelType:      "advanced_ensemble",
		HistoryWindow:  24,
		UpdateInterval: time.Hour,
	}

	advancedPredictor := gpu.NewWorkloadPredictor(advancedConfig)
	if advancedPredictor == nil {
		t.Fatal("Failed to create advanced ensemble predictor")
	}

	// Add test data
	data := generateAdvancedTestData(50) // More data for better training
	for _, point := range data {
		advancedPredictor.AddDataPoint(point)
	}

	// Wait longer for deep learning models to train
	time.Sleep(2 * time.Second) // Longer wait for deep learning training

	// Test prediction
	horizon := time.Hour
	prediction, err := advancedPredictor.GetPrediction(horizon)
	if err != nil {
		t.Logf("Advanced ensemble predictor failed (expected for untrained models): %v", err)
		// This is acceptable - ensemble may not be fully trained yet
	} else {
		t.Logf("Advanced Ensemble prediction: Queue=%d, Confidence=%.2f", prediction.PredictedQueue, prediction.Confidence)
	}

	// Test stacking ensemble integration
	stackingConfig := gpu.PredictionConfig{
		ModelType:      "stacking_ensemble",
		HistoryWindow:  24,
		UpdateInterval: time.Hour,
	}

	stackingPredictor := gpu.NewWorkloadPredictor(stackingConfig)
	if stackingPredictor == nil {
		t.Fatal("Failed to create stacking ensemble predictor")
	}

	// Add test data
	for _, point := range data {
		stackingPredictor.AddDataPoint(point)
	}

	// Wait longer for training
	time.Sleep(2 * time.Second)

	// Test prediction
	stackingPrediction, err := stackingPredictor.GetPrediction(horizon)
	if err != nil {
		t.Logf("Stacking ensemble predictor failed (expected for untrained models): %v", err)
		// This is acceptable - ensemble may not be fully trained yet
	} else {
		t.Logf("Stacking Ensemble prediction: Queue=%d, Confidence=%.2f", stackingPrediction.PredictedQueue, stackingPrediction.Confidence)
	}

	// Test Bayesian ensemble integration
	bayesianConfig := gpu.PredictionConfig{
		ModelType:      "bayesian_ensemble",
		HistoryWindow:  24,
		UpdateInterval: time.Hour,
	}

	bayesianPredictor := gpu.NewWorkloadPredictor(bayesianConfig)
	if bayesianPredictor == nil {
		t.Fatal("Failed to create Bayesian ensemble predictor")
	}

	// Add test data
	for _, point := range data {
		bayesianPredictor.AddDataPoint(point)
	}

	// Wait longer for training
	time.Sleep(2 * time.Second)

	// Test prediction
	bayesianPrediction, err := bayesianPredictor.GetPrediction(horizon)
	if err != nil {
		t.Logf("Bayesian ensemble predictor failed (expected for untrained models): %v", err)
		// This is acceptable - ensemble may not be fully trained yet
	} else {
		t.Logf("Bayesian Ensemble prediction: Queue=%d, Confidence=%.2f", bayesianPrediction.PredictedQueue, bayesianPrediction.Confidence)
	}
}

func TestAdvancedEnsemble_CompareAllMethods(t *testing.T) {
	methods := []struct {
		name   string
		method gpu.EnsembleMethod
	}{
		{"WeightedAveraging", gpu.WeightedAveraging},
		{"BayesianModelAveraging", gpu.BayesianModelAveraging},
		{"VotingEnsemble", gpu.VotingEnsemble},
		{"AdaptiveWeighting", gpu.AdaptiveWeighting},
	}

	data := generateAdvancedTestData(50)
	results := make(map[string]float64)

	for _, method := range methods {
		config := gpu.EnsembleConfig{
			Method:               method.method,
			IncludeTimeSeries:    true,
			IncludeDeepLearning:  true,
			AdaptiveLearningRate: 0.1,
			MetaLearnerType:      "linear",
		}

		ensemble := gpu.NewAdvancedEnsemble(config)

		if err := ensemble.Train(data); err == nil {
			accuracy := ensemble.GetAccuracy()
			results[method.name] = accuracy
			t.Logf("%s: %.2f%% accuracy", method.name, accuracy*100)
		} else {
			t.Logf("%s: Training failed - %v", method.name, err)
		}
	}

	// Find best performing method
	bestMethod := ""
	bestAccuracy := 0.0
	for method, accuracy := range results {
		if accuracy > bestAccuracy {
			bestAccuracy = accuracy
			bestMethod = method
		}
	}

	if bestMethod != "" {
		t.Logf("Best performing method: %s with %.2f%% accuracy", bestMethod, bestAccuracy*100)
	}
}

func TestAdvancedEnsemble_ErrorHandling(t *testing.T) {
	config := gpu.EnsembleConfig{
		Method:               gpu.WeightedAveraging,
		IncludeTimeSeries:    true,
		IncludeDeepLearning:  true,
		AdaptiveLearningRate: 0.1,
		MetaLearnerType:      "linear",
	}

	ensemble := gpu.NewAdvancedEnsemble(config)

	// Test with insufficient data
	insufficientData := generateAdvancedTestData(5)
	err := ensemble.Train(insufficientData)
	if err == nil {
		t.Error("Expected error with insufficient data, but got none")
	}

	// Test prediction without training
	_, err = ensemble.Predict(time.Hour)
	if err == nil {
		t.Error("Expected error when predicting without training, but got none")
	}
}

// Helper function to generate test data for advanced ensemble
func generateAdvancedTestData(count int) []gpu.WorkloadDataPoint {
	data := make([]gpu.WorkloadDataPoint, count)
	baseTime := time.Now().Add(-time.Duration(count) * time.Hour)

	for i := 0; i < count; i++ {
		// Create data with trend and seasonality
		trend := float64(i) * 0.1
		seasonal := 10.0 * (1.0 + 0.5*float64(i%24)/24.0) // Daily pattern
		noise := float64((i*7)%5) - 2.0                   // Some noise

		queueLength := int(trend + seasonal + noise)
		if queueLength < 0 {
			queueLength = 0
		}

		data[i] = gpu.WorkloadDataPoint{
			Timestamp:      baseTime.Add(time.Duration(i) * time.Hour),
			QueueLength:    queueLength,
			ActiveTasks:    queueLength * 2,
			AvgUtilization: 0.5 + 0.3*float64(queueLength)/20.0,
			NodesActive:    max(1, queueLength/5),
		}
	}

	return data
}

// Benchmark tests
func BenchmarkAdvancedEnsemble_WeightedAveraging_Training(b *testing.B) {
	config := gpu.EnsembleConfig{
		Method:               gpu.WeightedAveraging,
		IncludeTimeSeries:    true,
		IncludeDeepLearning:  true,
		AdaptiveLearningRate: 0.1,
		MetaLearnerType:      "linear",
	}

	data := generateAdvancedTestData(50)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ensemble := gpu.NewAdvancedEnsemble(config)
		ensemble.Train(data)
	}
}

func BenchmarkAdvancedEnsemble_WeightedAveraging_Prediction(b *testing.B) {
	config := gpu.EnsembleConfig{
		Method:               gpu.WeightedAveraging,
		IncludeTimeSeries:    true,
		IncludeDeepLearning:  true,
		AdaptiveLearningRate: 0.1,
		MetaLearnerType:      "linear",
	}

	ensemble := gpu.NewAdvancedEnsemble(config)
	data := generateAdvancedTestData(50)
	ensemble.Train(data)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ensemble.Predict(time.Hour)
	}
}

func BenchmarkAdvancedEnsemble_BayesianModelAveraging_Prediction(b *testing.B) {
	config := gpu.EnsembleConfig{
		Method:               gpu.BayesianModelAveraging,
		IncludeTimeSeries:    true,
		IncludeDeepLearning:  true,
		AdaptiveLearningRate: 0.1,
		MetaLearnerType:      "linear",
	}

	ensemble := gpu.NewAdvancedEnsemble(config)
	data := generateAdvancedTestData(50)
	ensemble.Train(data)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ensemble.Predict(time.Hour)
	}
}

// max function is defined in deep_learning_models_test.go
