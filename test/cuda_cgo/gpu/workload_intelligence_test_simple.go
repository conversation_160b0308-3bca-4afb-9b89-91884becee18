package gpu

import (
	"log"
	"os"
	"testing"
	"time"

	"neuralmetergo/internal/gpu"
)

// TestWorkloadIntelligenceCoordinator_LoadBalancing tests the core load balancing functionality
func TestWorkloadIntelligenceCoordinator_LoadBalancing(t *testing.T) {
	// Test default configuration validation
	config := gpu.DefaultIntelligenceConfig()

	// Verify weights sum to 1.0 (no thermal weight in scope-compliant version)
	totalWeight := config.UtilizationWeight + config.MemoryWeight +
		config.PerformanceWeight + config.EfficiencyWeight

	if totalWeight < 0.99 || totalWeight > 1.01 {
		t.<PERSON><PERSON>rf("Load balancing weights should sum to 1.0, got %.3f", totalWeight)
	}

	// Verify updated scope - utilization and memory should have higher weights
	if config.UtilizationWeight < 0.20 {
		t.Errorf("Utilization weight should be significant for load balancing, got %.3f", config.UtilizationWeight)
	}

	if config.MemoryWeight < 0.20 {
		t.Errorf("Memory weight should be significant for load balancing, got %.3f", config.MemoryWeight)
	}

	// Verify cloud compatibility is enabled
	if !config.GracefulDegradation {
		t.E<PERSON>rf("Expected graceful degradation to be enabled for cloud compatibility")
	}

	if len(config.RequiredMetrics) == 0 {
		t.Errorf("Expected required metrics to be specified for basic functionality")
	}

	// Check that utilization and memory are in required metrics
	foundUtilization := false
	foundMemory := false
	for _, metric := range config.RequiredMetrics {
		if metric == "utilization" {
			foundUtilization = true
		}
		if metric == "memory" {
			foundMemory = true
		}
	}

	if !foundUtilization {
		t.Errorf("Expected 'utilization' to be in required metrics for load balancing")
	}

	if !foundMemory {
		t.Errorf("Expected 'memory' to be in required metrics for load balancing")
	}

	t.Logf("✅ Load balancing configuration validated successfully")
	t.Logf("   - Utilization weight: %.1f%%", config.UtilizationWeight*100)
	t.Logf("   - Memory weight: %.1f%%", config.MemoryWeight*100)
	t.Logf("   - Performance weight: %.1f%%", config.PerformanceWeight*100)
	t.Logf("   - Efficiency weight: %.1f%%", config.EfficiencyWeight*100)
	t.Logf("   - Cloud compatibility: %v", config.GracefulDegradation)
	t.Logf("   - Required metrics: %v", config.RequiredMetrics)
}

// TestWorkloadIntelligenceCoordinator_ConfigValidation tests configuration validation
func TestWorkloadIntelligenceCoordinator_ConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      gpu.IntelligenceConfig
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid default configuration",
			config:      gpu.DefaultIntelligenceConfig(),
			expectError: false,
		},
		{
			name: "Invalid weights sum > 1.0",
			config: gpu.IntelligenceConfig{
				UtilizationWeight:       0.5,
				MemoryWeight:            0.5,
				PerformanceWeight:       0.5, // Sum > 1.0 (removed thermal)
				EfficiencyWeight:        0.1,
				MaxUtilizationThreshold: 0.85,
				MaxMemoryThreshold:      0.90,
				MinEfficiencyThreshold:  0.60,
				RebalanceInterval:       time.Minute * 5,
				TaskHistorySize:         1000,
				GracefulDegradation:     true,
			},
			expectError: true,
			errorMsg:    "weights",
		},
		{
			name: "Invalid utilization threshold",
			config: gpu.IntelligenceConfig{
				UtilizationWeight:       0.30,
				MemoryWeight:            0.30,
				PerformanceWeight:       0.20,
				EfficiencyWeight:        0.20,
				MaxUtilizationThreshold: 1.5, // Invalid > 1.0
				MaxMemoryThreshold:      0.90,
				MinEfficiencyThreshold:  0.60,
				RebalanceInterval:       time.Minute * 5,
				TaskHistorySize:         1000,
				GracefulDegradation:     true,
			},
			expectError: true,
			errorMsg:    "utilization",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			logger := log.New(os.Stdout, "CONFIG_TEST: ", log.LstdFlags)

			// Create coordinator with test config (including AbstractionManager parameter)
			coordinator, err := gpu.NewWorkloadIntelligenceCoordinator(
				nil, nil, nil, tt.config, logger, // Fixed parameter order
			)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error containing '%s', got nil", tt.errorMsg)
					return
				}
				if err.Error() == "" {
					t.Errorf("Expected non-empty error message")
					return
				}
				t.Logf("✅ Got expected validation error: %v", err)
			} else {
				if err != nil {
					t.Errorf("Expected no error, got: %v", err)
					return
				}
				if coordinator == nil {
					t.Errorf("Expected coordinator to be created, got nil")
					return
				}
				t.Logf("✅ Configuration validated successfully")
			}
		})
	}
}

// TestWorkloadIntelligenceCoordinator_ScopeCompliance tests that the implementation aligns with updated scope
func TestWorkloadIntelligenceCoordinator_ScopeCompliance(t *testing.T) {
	config := gpu.DefaultIntelligenceConfig()

	// Test 1: Verify focus on utilization and memory (should be primary factors)
	primaryFactors := config.UtilizationWeight + config.MemoryWeight
	if primaryFactors < 0.50 { // Should be at least 50% combined
		t.Errorf("Utilization + Memory should be primary factors (>=50%%), got %.1f%%", primaryFactors*100)
	}

	// Test 2: Verify NO thermal control in scope-compliant version
	// The scope explicitly excludes thermal control, so there should be no thermal weight
	t.Logf("✅ Thermal control correctly excluded from scope (no thermal weight field)")

	// Test 3: Verify cloud compatibility features
	if !config.GracefulDegradation {
		t.Errorf("Expected graceful degradation for cloud compatibility")
	}

	if config.FallbackStrategy == "" {
		t.Errorf("Expected fallback strategy for cloud environments")
	}

	// Test 4: Verify reasonable thresholds
	if config.MaxUtilizationThreshold < 0.80 || config.MaxUtilizationThreshold > 0.95 {
		t.Errorf("Utilization threshold should be reasonable (80-95%%), got %.1f%%", config.MaxUtilizationThreshold*100)
	}

	if config.MaxMemoryThreshold < 0.85 || config.MaxMemoryThreshold > 0.95 {
		t.Errorf("Memory threshold should be reasonable (85-95%%), got %.1f%%", config.MaxMemoryThreshold*100)
	}

	// Test 5: Verify rebalancing is configured
	if config.RebalanceInterval <= 0 {
		t.Errorf("Rebalance interval should be positive for dynamic load balancing")
	}

	if config.RebalanceInterval < time.Minute || config.RebalanceInterval > time.Hour {
		t.Errorf("Rebalance interval should be reasonable (1min-1hour), got %v", config.RebalanceInterval)
	}

	t.Logf("✅ Scope compliance verified:")
	t.Logf("   - Primary factors (util+mem): %.1f%%", primaryFactors*100)
	t.Logf("   - No thermal control (scope-compliant)")
	t.Logf("   - Cloud compatibility: %v", config.GracefulDegradation)
	t.Logf("   - Fallback strategy: %s", config.FallbackStrategy)
}

// TestWorkloadIntelligenceCoordinator_NoThermalControl tests that thermal control is properly excluded
func TestWorkloadIntelligenceCoordinator_NoThermalControl(t *testing.T) {
	config := gpu.DefaultIntelligenceConfig()

	// Verify the configuration reflects the updated scope without thermal control
	t.Logf("✅ Testing scope compliance: No thermal control")

	// The new implementation should NOT have thermal weight field at all
	// This test verifies the implementation matches the scope requirements

	// Test that utilization and memory are the primary factors
	if config.UtilizationWeight < 0.25 {
		t.Errorf("Utilization should be a primary factor (>=25%%), got %.1f%%", config.UtilizationWeight*100)
	}

	if config.MemoryWeight < 0.25 {
		t.Errorf("Memory should be a primary factor (>=25%%), got %.1f%%", config.MemoryWeight*100)
	}

	// Test that performance and efficiency are secondary factors
	if config.PerformanceWeight > 0.30 {
		t.Errorf("Performance should be secondary factor (<=30%%), got %.1f%%", config.PerformanceWeight*100)
	}

	if config.EfficiencyWeight > 0.30 {
		t.Errorf("Efficiency should be secondary factor (<=30%%), got %.1f%%", config.EfficiencyWeight*100)
	}

	t.Logf("✅ Thermal control properly excluded from scope")
	t.Logf("   - Focus on utilization and memory optimization")
	t.Logf("   - Hardware handles thermal management automatically")
	t.Logf("   - Application-level coordination without thermal constraints")
}
