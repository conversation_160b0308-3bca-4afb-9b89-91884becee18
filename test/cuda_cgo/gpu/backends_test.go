//go:build cuda && cuda_cgo
// +build cuda,cuda_cgo

package gpu

import (
	"context"
	"log"
	"testing"
	"time"

	"neuralmetergo/internal/gpu"
	"neuralmetergo/internal/gpu/types"
)

func TestBackendRegistry(t *testing.T) {
	registry := gpu.NewBackendRegistry()

	// Test empty registry
	backends := registry.GetOrderedBackends()
	if len(backends) != 0 {
		t.<PERSON>rrorf("Expected empty registry, got %d backends", len(backends))
	}

	// Test backend registration
	mockBackend := &MockBackend{name: "TestBackend"}
	registry.Register("TestBackend", mockBackend, 100)

	backends = registry.GetOrderedBackends()
	if len(backends) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected 1 backend after registration, got %d", len(backends))
	}

	if backends[0].Name() != "TestBackend" {
		t.Errorf("Expected backend name 'TestBackend', got '%s'", backends[0].Name())
	}

	// Test backend retrieval
	backend, exists := registry.GetBackend("TestBackend")
	if !exists {
		t.Error("Backend should exist after registration")
	}
	if backend.Name() != "TestBackend" {
		t.Errorf("Expected backend name 'TestBackend', got '%s'", backend.Name())
	}

	// Test non-existent backend
	_, exists = registry.GetBackend("NonExistent")
	if exists {
		t.Error("Non-existent backend should not exist")
	}
}

func TestBackendIntegration(t *testing.T) {
	manager := gpu.NewAbstractionManager(log.Default())
	ctx := context.Background()

	// Test device enumeration
	devices, err := manager.EnumerateDevices(ctx)
	if err != nil {
		t.Fatalf("Failed to enumerate devices: %v", err)
	}

	if len(devices) == 0 {
		t.Skip("No devices available for backend testing")
	}

	// Test each detected device
	for _, device := range devices {
		t.Run(device.Backend, func(t *testing.T) {
			// Test device properties
			if device.ID == "" {
				t.Error("Device ID is empty")
			}
			if device.Name == "" {
				t.Error("Device name is empty")
			}
			if device.Backend == "" {
				t.Error("Device backend is empty")
			}

			// Test context creation
			ctx, err := manager.CreateContext(context.Background(), &device)
			if err != nil {
				t.Errorf("Failed to create context: %v", err)
				return
			}
			defer ctx.Destroy()

			// Test context validity
			if !ctx.IsValid() {
				t.Error("Context is not valid")
			}

			// Test device ID matches
			if ctx.GetDeviceID() != device.ID {
				t.Errorf("Context device ID mismatch: expected %s, got %s", device.ID, ctx.GetDeviceID())
			}

			// Test memory manager creation
			memMgr, err := manager.CreateMemoryManager(ctx)
			if err != nil {
				t.Logf("Memory manager creation failed (may be expected): %v", err)
			} else if memMgr != nil {
				// Test memory stats
				stats := memMgr.GetStats()
				t.Logf("Memory stats for %s: Total=%d, Used=%d, Free=%d",
					device.Name, stats.TotalAllocated, stats.CurrentUsed, stats.FreeMemory)
			}

			// Test executor creation
			executor, err := manager.CreateExecutor(ctx)
			if err != nil {
				t.Logf("Executor creation failed (may be expected): %v", err)
			} else if executor != nil {
				// Test executor methods
				streams := executor.GetStreams()
				t.Logf("Executor for %s has %d streams", device.Name, len(streams))
			}

			// Test capabilities
			caps, err := manager.GetCapabilities(&device)
			if err != nil {
				t.Errorf("Failed to get capabilities: %v", err)
			} else if caps != nil {
				t.Logf("Device %s capabilities: MaxBufferSize=%d, MaxWorkGroupSize=%d, MaxComputeUnits=%d",
					device.Name, caps.MaxBufferSize, caps.MaxWorkGroupSize, caps.MaxComputeUnits)

				// Test feature support
				supportsCompute := manager.SupportsFeature(&device, types.FeatureCompute)
				t.Logf("Device %s supports compute: %v", device.Name, supportsCompute)
			}
		})
	}
}

func TestBackendPriority(t *testing.T) {
	manager := gpu.NewAbstractionManager(log.Default())

	// Test backend listing
	backends := manager.ListBackends()
	if len(backends) == 0 {
		t.Error("No backends registered")
	}

	for _, backend := range backends {
		t.Logf("Available backend: %s", backend)
	}

	// Test best backend selection
	ctx := context.Background()
	bestBackend, err := manager.GetBestBackend(ctx)
	if err != nil {
		t.Errorf("Failed to get best backend: %v", err)
	} else if bestBackend != nil {
		t.Logf("Best backend: %s", bestBackend.Name())
	}
}

// MockBackend implements types.GPUBackend for testing
type MockBackend struct {
	name string
}

func (m *MockBackend) Name() string {
	return m.name
}

func (m *MockBackend) Version() string {
	return "1.0.0"
}

func (m *MockBackend) Platform() string {
	return "test"
}

func (m *MockBackend) EnumerateDevices(ctx context.Context) ([]types.GPUDevice, error) {
	return []types.GPUDevice{
		{
			ID:      "mock-0",
			Name:    "Mock Device",
			Backend: m.name,
			Status:  types.DeviceStatusAvailable,
		},
	}, nil
}

func (m *MockBackend) GetDevice(deviceID string) (*types.GPUDevice, error) {
	if deviceID == "mock-0" {
		return &types.GPUDevice{
			ID:      "mock-0",
			Name:    "Mock Device",
			Backend: m.name,
			Status:  types.DeviceStatusAvailable,
		}, nil
	}
	return nil, types.NewGPUError(types.ErrorTypeInitialization, 1002, "device not found", -1)
}

func (m *MockBackend) GetCapabilities(device *types.GPUDevice) (*types.GPUCapability, error) {
	return &types.GPUCapability{
		MaxBufferSize:    1024 * 1024,
		MaxWorkGroupSize: 256,
		MaxComputeUnits:  8,
		Features:         make(map[types.GPUFeature]bool),
	}, nil
}

func (m *MockBackend) SupportsFeature(device *types.GPUDevice, feature types.GPUFeature) bool {
	return feature == types.FeatureCompute
}

func (m *MockBackend) CreateContext(device *types.GPUDevice) (types.GPUContext, error) {
	return &MockContext{deviceID: device.ID}, nil
}

func (m *MockBackend) CreateMemoryManager(ctx types.GPUContext) (types.GPUMemoryManager, error) {
	return &MockMemoryManager{}, nil
}

func (m *MockBackend) CreateExecutor(ctx types.GPUContext) (types.GPUExecutor, error) {
	return &MockExecutor{}, nil
}

// MockContext implements types.GPUContext for testing
type MockContext struct {
	deviceID string
}

func (m *MockContext) GetDevice() *types.GPUDevice {
	return &types.GPUDevice{ID: m.deviceID}
}

func (m *MockContext) IsValid() bool {
	return true
}

func (m *MockContext) Synchronize() error {
	return nil
}

func (m *MockContext) GetDeviceID() string {
	return m.deviceID
}

func (m *MockContext) GetBackend() string {
	return "mock"
}

func (m *MockContext) Destroy() error {
	return nil
}

// MockMemoryManager implements types.GPUMemoryManager for testing
type MockMemoryManager struct{}

func (m *MockMemoryManager) Allocate(size uint64) (types.GPUMemory, error) {
	return &MockMemory{size: size}, nil
}

func (m *MockMemoryManager) AllocateType(size uint64, memType types.MemoryType) (types.GPUMemory, error) {
	return &MockMemory{size: size, memType: memType}, nil
}

func (m *MockMemoryManager) GetStats() types.GPUMemoryStats {
	return types.GPUMemoryStats{
		TotalAllocated: 1024 * 1024,
		CurrentUsed:    512 * 1024,
		FreeMemory:     512 * 1024,
	}
}

func (m *MockMemoryManager) Cleanup() error {
	return nil
}

// MockMemory implements types.GPUMemory for testing
type MockMemory struct {
	size    uint64
	memType types.MemoryType
}

func (m *MockMemory) Ptr() uintptr {
	return 0x1000
}

func (m *MockMemory) Size() uint64 {
	return m.size
}

func (m *MockMemory) Type() types.MemoryType {
	return m.memType
}

func (m *MockMemory) Free() error {
	return nil
}

func (m *MockMemory) CopyFrom(src []byte) error {
	return nil
}

func (m *MockMemory) CopyTo(dst []byte) error {
	return nil
}

func (m *MockMemory) CopyFromGPU(src types.GPUMemory) error {
	return nil
}

// MockExecutor implements types.GPUExecutor for testing
type MockExecutor struct{}

func (m *MockExecutor) CreateKernel(source string, entryPoint string) (types.GPUKernel, error) {
	return &MockKernel{name: entryPoint}, nil
}

func (m *MockExecutor) CreateStream() (types.GPUStream, error) {
	return &MockStream{id: "mock-stream"}, nil
}

func (m *MockExecutor) CreateEvent() (types.GPUEvent, error) {
	return &MockEvent{id: "mock-event"}, nil
}

func (m *MockExecutor) Synchronize() error {
	return nil
}

func (m *MockExecutor) GetStreams() []types.GPUStream {
	return []types.GPUStream{}
}

// MockKernel implements types.GPUKernel for testing
type MockKernel struct {
	name string
}

func (m *MockKernel) GetName() string {
	return m.name
}

func (m *MockKernel) Launch(grid types.GridDimension, block types.GridDimension, args []interface{}, stream types.GPUStream) error {
	return nil
}

func (m *MockKernel) GetAttributes() map[string]interface{} {
	return make(map[string]interface{})
}

func (m *MockKernel) Destroy() error {
	return nil
}

// MockStream implements types.GPUStream for testing
type MockStream struct {
	id string
}

func (m *MockStream) ID() string {
	return m.id
}

func (m *MockStream) Submit(kernel types.GPUKernel, grid types.GridDimension, block types.GridDimension, args []interface{}) error {
	return nil
}

func (m *MockStream) Synchronize() error {
	return nil
}

func (m *MockStream) Query() types.StreamState {
	return types.StreamStateComplete
}

func (m *MockStream) Destroy() error {
	return nil
}

// MockEvent implements types.GPUEvent for testing
type MockEvent struct {
	id string
}

func (m *MockEvent) ID() string {
	return m.id
}

func (m *MockEvent) Record(stream types.GPUStream) error {
	return nil
}

func (m *MockEvent) Wait() error {
	return nil
}

func (m *MockEvent) Query() types.EventState {
	return types.EventStateComplete
}

func (m *MockEvent) ElapsedTime(start types.GPUEvent) (time.Duration, error) {
	return 0, nil
}

func (m *MockEvent) Destroy() error {
	return nil
}
