package gpu

import (
	"context"
	"log"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"neuralmetergo/internal/gpu"
	clusterpb "neuralmetergo/internal/gpu/proto"
)

func TestNewClusterCommunicationManager(t *testing.T) {
	config := &gpu.CommunicationConfig{
		NodeID:                "test-node-1",
		GRPCPort:              8080,
		GRPCMaxMessageSize:    4 * 1024 * 1024, // 4MB
		GRPCKeepaliveTime:     30 * time.Second,
		GRPCKeepaliveTimeout:  5 * time.Second,
		GRPCConnectionTimeout: 10 * time.Second,

		ZMQPubPort:   5555,
		ZMQSubPort:   5556,
		ZMQReqPort:   5557,
		ZMQRepPort:   5558,
		ZMQTransport: "tcp",

		TLSEnabled:           false,
		BufferSize:           1000,
		RetryAttempts:        3,
		RetryDelay:           time.Second,
		MaxConcurrentStreams: 100,
	}

	logger := log.Default()

	commManager, err := gpu.NewClusterCommunicationManager(config, logger)

	assert.NoError(t, err)
	assert.NotNil(t, commManager)
	assert.Equal(t, "test-node-1", commManager.GetNodeID())
}

func TestDefaultCommunicationConfig(t *testing.T) {
	config := gpu.DefaultCommunicationConfig()

	assert.Equal(t, 8080, config.GRPCPort)
	assert.Equal(t, 4*1024*1024, config.GRPCMaxMessageSize)
	assert.Equal(t, 30*time.Second, config.GRPCKeepaliveTime)
	assert.Equal(t, 5*time.Second, config.GRPCKeepaliveTimeout)
	assert.Equal(t, 10*time.Second, config.GRPCConnectionTimeout)

	assert.Equal(t, 5555, config.ZMQPubPort)
	assert.Equal(t, 5556, config.ZMQSubPort)
	assert.Equal(t, 5557, config.ZMQReqPort)
	assert.Equal(t, 5558, config.ZMQRepPort)
	assert.Equal(t, "tcp", config.ZMQTransport)

	assert.False(t, config.TLSEnabled)
	assert.Equal(t, 1000, config.BufferSize)
	assert.Equal(t, 3, config.RetryAttempts)
	assert.Equal(t, time.Second, config.RetryDelay)
	assert.Equal(t, uint32(100), config.MaxConcurrentStreams)
}

func TestCommunicationManagerLifecycle(t *testing.T) {
	config := gpu.DefaultCommunicationConfig()
	// Use different ports to avoid conflicts
	config.NodeID = "test-node-2"
	config.GRPCPort = 8081
	config.ZMQPubPort = 5559
	config.ZMQSubPort = 5560
	config.ZMQReqPort = 5561
	config.ZMQRepPort = 5562

	logger := log.Default()

	commManager, err := gpu.NewClusterCommunicationManager(config, logger)
	require.NoError(t, err)
	require.NotNil(t, commManager)

	// Test start
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err = commManager.Start(ctx)
	assert.NoError(t, err)

	// Test that it's running
	assert.True(t, commManager.IsRunning())

	// Test stop
	err = commManager.Stop()
	assert.NoError(t, err)

	// Test that it's stopped
	assert.False(t, commManager.IsRunning())
}

func TestProtobufMessageTypes(t *testing.T) {
	// Test task assignment request using actual protobuf types
	taskReq := &clusterpb.TaskAssignmentRequest{
		TaskId:         "task-123",
		PreferredNodes: []string{"node-456"},
		Priority:       clusterpb.TaskPriority_PRIORITY_HIGH,
	}

	assert.Equal(t, "task-123", taskReq.TaskId)
	assert.Equal(t, []string{"node-456"}, taskReq.PreferredNodes)
	assert.Equal(t, clusterpb.TaskPriority_PRIORITY_HIGH, taskReq.Priority)

	// Test node registration request
	nodeReq := &clusterpb.NodeRegistrationRequest{
		NodeInfo: &clusterpb.NodeInfo{
			NodeId:    "node-789",
			Hostname:  "test-host",
			IpAddress: "*************",
			Port:      8080,
		},
		AuthenticationToken: "test-token",
	}

	assert.Equal(t, "node-789", nodeReq.NodeInfo.NodeId)
	assert.Equal(t, "test-host", nodeReq.NodeInfo.Hostname)
	assert.Equal(t, "*************", nodeReq.NodeInfo.IpAddress)
	assert.Equal(t, int32(8080), nodeReq.NodeInfo.Port)
	assert.Equal(t, "test-token", nodeReq.AuthenticationToken)

	// Test task status update
	statusUpdate := &clusterpb.TaskStatusUpdate{
		TaskId:          "task-456",
		NodeId:          "node-123",
		Status:          clusterpb.TaskStatus_STATUS_RUNNING,
		ProgressPercent: 75.5,
		Message:         "Processing...",
	}

	assert.Equal(t, "task-456", statusUpdate.TaskId)
	assert.Equal(t, "node-123", statusUpdate.NodeId)
	assert.Equal(t, clusterpb.TaskStatus_STATUS_RUNNING, statusUpdate.Status)
	assert.Equal(t, float32(75.5), statusUpdate.ProgressPercent)
	assert.Equal(t, "Processing...", statusUpdate.Message)
}

func TestZMQMessageTypes(t *testing.T) {
	// Test ZMQ message structure (this is defined in the communication manager)
	zmqMsg := &gpu.ZMQMessage{
		Type:      "task_assignment",
		Source:    "coordinator",
		Target:    "worker-node-1",
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"task_id":  "task-123",
			"priority": "high",
		},
	}

	assert.Equal(t, "task_assignment", zmqMsg.Type)
	assert.Equal(t, "coordinator", zmqMsg.Source)
	assert.Equal(t, "worker-node-1", zmqMsg.Target)
	assert.Equal(t, "task-123", zmqMsg.Data["task_id"])
	assert.Equal(t, "high", zmqMsg.Data["priority"])
}
