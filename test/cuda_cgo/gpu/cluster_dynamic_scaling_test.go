package gpu

import (
	"log"
	"os"
	"testing"
	"time"

	gpu "neuralmetergo/internal/gpu"
)

// TestWorkloadPredictor tests the workload prediction functionality
func TestWorkloadPredictor(t *testing.T) {
	config := gpu.PredictionConfig{
		HistoryWindow:    1 * time.Hour,
		MinDataPoints:    5,
		ModelType:        "linear_regression",
		UpdateInterval:   1 * time.Minute,
		SeasonalPatterns: false,
	}

	predictor := gpu.NewWorkloadPredictor(config)
	if predictor == nil {
		t.Fatal("Failed to create workload predictor")
	}

	// Add some test data points
	now := time.Now()
	testData := []gpu.WorkloadDataPoint{
		{Timestamp: now.Add(-5 * time.Minute), QueueLength: 10, ActiveTasks: 8, AvgUtilization: 0.6, NodesActive: 2},
		{Timestamp: now.Add(-4 * time.Minute), QueueLength: 15, ActiveTasks: 12, AvgUtilization: 0.7, NodesActive: 2},
		{Timestamp: now.Add(-3 * time.Minute), QueueLength: 20, ActiveTasks: 16, AvgUtilization: 0.8, NodesActive: 2},
		{Timestamp: now.Add(-2 * time.Minute), QueueLength: 25, ActiveTasks: 20, AvgUtilization: 0.9, NodesActive: 2},
		{Timestamp: now.Add(-1 * time.Minute), QueueLength: 30, ActiveTasks: 24, AvgUtilization: 0.95, NodesActive: 2},
	}

	// Add data points
	for _, point := range testData {
		predictor.AddDataPoint(point)
	}

	// Test prediction
	prediction, err := predictor.GetPrediction(5 * time.Minute)
	if err != nil {
		t.Fatalf("Failed to get prediction: %v", err)
	}

	if prediction.PredictedQueue <= 0 {
		t.Errorf("Expected positive predicted queue length, got %d", prediction.PredictedQueue)
	}

	if prediction.Confidence < 0 || prediction.Confidence > 1 {
		t.Errorf("Expected confidence between 0 and 1, got %f", prediction.Confidence)
	}

	t.Logf("Prediction: Queue=%d, Tasks=%d, Nodes=%d, Confidence=%.2f, Action=%s",
		prediction.PredictedQueue, prediction.PredictedTasks, prediction.PredictedNodes,
		prediction.Confidence, prediction.RecommendedAction)
}

// TestLinearRegressionModel tests the linear regression prediction model
func TestLinearRegressionModel(t *testing.T) {
	model := gpu.NewLinearRegressionModel()
	if model == nil {
		t.Fatal("Failed to create linear regression model")
	}

	// Create test data with a clear trend
	now := time.Now()
	testData := []gpu.WorkloadDataPoint{
		{Timestamp: now.Add(-10 * time.Minute), QueueLength: 10},
		{Timestamp: now.Add(-8 * time.Minute), QueueLength: 15},
		{Timestamp: now.Add(-6 * time.Minute), QueueLength: 20},
		{Timestamp: now.Add(-4 * time.Minute), QueueLength: 25},
		{Timestamp: now.Add(-2 * time.Minute), QueueLength: 30},
	}

	// Train the model
	err := model.Train(testData)
	if err != nil {
		t.Fatalf("Failed to train model: %v", err)
	}

	// Test prediction
	prediction, err := model.Predict(5 * time.Minute)
	if err != nil {
		t.Fatalf("Failed to get prediction: %v", err)
	}

	// With the upward trend, we expect a higher predicted queue
	if prediction.PredictedQueue <= 30 {
		t.Errorf("Expected predicted queue > 30, got %d", prediction.PredictedQueue)
	}

	accuracy := model.GetAccuracy()
	if accuracy < 0 || accuracy > 1 {
		t.Errorf("Expected accuracy between 0 and 1, got %f", accuracy)
	}

	t.Logf("Linear regression prediction: Queue=%d, Accuracy=%.2f", prediction.PredictedQueue, accuracy)
}

// TestMovingAverageModel tests the moving average prediction model
func TestMovingAverageModel(t *testing.T) {
	model := gpu.NewMovingAverageModel()
	if model == nil {
		t.Fatal("Failed to create moving average model")
	}

	// Create test data
	now := time.Now()
	testData := make([]gpu.WorkloadDataPoint, 15)
	for i := 0; i < 15; i++ {
		testData[i] = gpu.WorkloadDataPoint{
			Timestamp:   now.Add(time.Duration(-15+i) * time.Minute),
			QueueLength: 20 + i%5, // Oscillating pattern
		}
	}

	// Train the model
	err := model.Train(testData)
	if err != nil {
		t.Fatalf("Failed to train model: %v", err)
	}

	// Test prediction
	prediction, err := model.Predict(5 * time.Minute)
	if err != nil {
		t.Fatalf("Failed to get prediction: %v", err)
	}

	if prediction.PredictedQueue <= 0 {
		t.Errorf("Expected positive predicted queue, got %d", prediction.PredictedQueue)
	}

	accuracy := model.GetAccuracy()
	if accuracy < 0 || accuracy > 1 {
		t.Errorf("Expected accuracy between 0 and 1, got %f", accuracy)
	}

	t.Logf("Moving average prediction: Queue=%d, Accuracy=%.2f", prediction.PredictedQueue, accuracy)
}

// TestExponentialSmoothingModel tests the exponential smoothing prediction model
func TestExponentialSmoothingModel(t *testing.T) {
	model := gpu.NewExponentialSmoothingModel()
	if model == nil {
		t.Fatal("Failed to create exponential smoothing model")
	}

	// Create test data
	now := time.Now()
	testData := []gpu.WorkloadDataPoint{
		{Timestamp: now.Add(-6 * time.Minute), QueueLength: 10},
		{Timestamp: now.Add(-5 * time.Minute), QueueLength: 12},
		{Timestamp: now.Add(-4 * time.Minute), QueueLength: 15},
		{Timestamp: now.Add(-3 * time.Minute), QueueLength: 18},
		{Timestamp: now.Add(-2 * time.Minute), QueueLength: 20},
		{Timestamp: now.Add(-1 * time.Minute), QueueLength: 22},
	}

	// Train the model
	err := model.Train(testData)
	if err != nil {
		t.Fatalf("Failed to train model: %v", err)
	}

	// Test prediction
	prediction, err := model.Predict(5 * time.Minute)
	if err != nil {
		t.Fatalf("Failed to get prediction: %v", err)
	}

	if prediction.PredictedQueue <= 0 {
		t.Errorf("Expected positive predicted queue, got %d", prediction.PredictedQueue)
	}

	accuracy := model.GetAccuracy()
	if accuracy < 0 || accuracy > 1 {
		t.Errorf("Expected accuracy between 0 and 1, got %f", accuracy)
	}

	t.Logf("Exponential smoothing prediction: Queue=%d, Accuracy=%.2f", prediction.PredictedQueue, accuracy)
}

// TestNodeLifecycleManager tests the node lifecycle management functionality
func TestNodeLifecycleManager(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	config := gpu.NodeLifecycleConfig{
		MaxProvisioningTime:     5 * time.Minute,
		MaxTerminationTime:      2 * time.Minute,
		HealthCheckInterval:     10 * time.Second,
		EnableSpotInstances:     true,
		PreferredRegions:        []string{"us-west-2"},
		MaxConcurrentOperations: 3,
	}

	manager := gpu.NewNodeLifecycleManager(config, logger)
	if manager == nil {
		t.Fatal("Failed to create node lifecycle manager")
	}

	// Test getting initial status
	integrations := manager.GetPendingIntegrations()
	if len(integrations) != 0 {
		t.Errorf("Expected 0 pending integrations, got %d", len(integrations))
	}

	removals := manager.GetPendingRemovals()
	if len(removals) != 0 {
		t.Errorf("Expected 0 pending removals, got %d", len(removals))
	}

	// Test statistics
	stats := manager.GetStatistics()
	if stats.PendingIntegrations != 0 {
		t.Errorf("Expected 0 pending integrations in stats, got %d", stats.PendingIntegrations)
	}

	// Test cleanup (should not error on empty state)
	manager.CleanupFailedOperations()

	t.Logf("Node lifecycle manager test completed successfully")
}

// TestScalingDecisionEngine tests the scaling decision engine
func TestScalingDecisionEngine(t *testing.T) {
	config := gpu.ScalingDecisionConfig{
		DecisionInterval:    1 * time.Minute,
		ConfidenceThreshold: 0.5,
		MaxDecisionHistory:  100,
		WeightQueueLength:   0.4,
		WeightUtilization:   0.3,
		WeightCost:          0.2,
		WeightLatency:       0.1,
	}

	engine := gpu.NewScalingDecisionEngine(config)
	if engine == nil {
		t.Fatal("Failed to create scaling decision engine")
	}

	t.Logf("Scaling decision engine created successfully")
}

// TestDynamicScalingConfig tests the configuration structures
func TestDynamicScalingConfig(t *testing.T) {
	config := gpu.DefaultDynamicScalingConfig()

	// Validate basic configuration values
	if config.MinNodes <= 0 {
		t.Errorf("Expected positive MinNodes, got %d", config.MinNodes)
	}

	if config.MaxNodes <= config.MinNodes {
		t.Errorf("Expected MaxNodes > MinNodes, got MaxNodes=%d, MinNodes=%d", config.MaxNodes, config.MinNodes)
	}

	if config.ScaleUpCooldown <= 0 {
		t.Errorf("Expected positive ScaleUpCooldown, got %v", config.ScaleUpCooldown)
	}

	if config.ScaleDownCooldown <= 0 {
		t.Errorf("Expected positive ScaleDownCooldown, got %v", config.ScaleDownCooldown)
	}

	if config.MonitoringInterval <= 0 {
		t.Errorf("Expected positive MonitoringInterval, got %v", config.MonitoringInterval)
	}

	t.Logf("Dynamic scaling config validation passed")
}

// TestScalingActions tests the scaling action constants
func TestScalingActions(t *testing.T) {
	actions := []gpu.ScalingAction{
		gpu.ScalingActionNone,
		gpu.ScalingActionScaleUp,
		gpu.ScalingActionScaleDown,
		gpu.ScalingActionOptimize,
	}

	for _, action := range actions {
		if string(action) == "" {
			t.Errorf("Empty scaling action found")
		}
	}

	t.Logf("Scaling actions test passed")
}

// BenchmarkLinearRegression benchmarks the linear regression prediction model
func BenchmarkLinearRegression(b *testing.B) {
	model := gpu.NewLinearRegressionModel()

	// Create test data
	now := time.Now()
	testData := make([]gpu.WorkloadDataPoint, 100)
	for i := 0; i < 100; i++ {
		testData[i] = gpu.WorkloadDataPoint{
			Timestamp:   now.Add(time.Duration(-100+i) * time.Minute),
			QueueLength: 10 + i/10,
		}
	}

	// Train once
	model.Train(testData)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := model.Predict(5 * time.Minute)
		if err != nil {
			b.Fatalf("Prediction failed: %v", err)
		}
	}
}

// BenchmarkWorkloadPredictor benchmarks the workload predictor
func BenchmarkWorkloadPredictor(b *testing.B) {
	config := gpu.PredictionConfig{
		HistoryWindow:    1 * time.Hour,
		MinDataPoints:    10,
		ModelType:        "linear_regression",
		UpdateInterval:   1 * time.Minute,
		SeasonalPatterns: false,
	}

	predictor := gpu.NewWorkloadPredictor(config)

	// Add initial data
	now := time.Now()
	for i := 0; i < 20; i++ {
		predictor.AddDataPoint(gpu.WorkloadDataPoint{
			Timestamp:   now.Add(time.Duration(-20+i) * time.Minute),
			QueueLength: 10 + i,
		})
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := predictor.GetPrediction(5 * time.Minute)
		if err != nil {
			b.Fatalf("Prediction failed: %v", err)
		}
	}
}
