package gpu_test

import (
	"log"
	"os"
	"runtime"
	"testing"

	"neuralmetergo/internal/gpu"
)

func TestCrossPlatformGPUDetection(t *testing.T) {
	logger := log.New(os.Stdout, "[GPU-Test] ", log.LstdFlags)
	config := gpu.DefaultGPUConfig()

	// Create GPU manager
	manager := gpu.NewManager(config, logger)

	// Test GPU detection works on all platforms
	gpus, err := manager.GetAvailableGPUs()
	if err != nil {
		t.Logf("GPU detection failed (this may be expected on CI): %v", err)
		return // Don't fail test if no GPUs available
	}

	t.Logf("Detected %d GPU(s) on %s/%s", len(gpus), runtime.GOOS, runtime.GOARCH)

	// Verify that we get at least CPU fallback
	if len(gpus) == 0 {
		t.Error("Expected at least CPU fallback detector to work")
		return
	}

	// Test platform-specific expectations
	platformExpectations := map[string]func([]gpu.GPUType){
		"darwin": func(types []gpu.GPUType) {
			hasExpectedType := false
			for _, gpuType := range types {
				if gpuType == gpu.GPUTypeMetal || gpuType == gpu.GPUTypeCUDA || gpuType == gpu.GPUTypeOpenCL {
					hasExpectedType = true
					break
				}
			}
			if !hasExpectedType {
				t.Log("macOS: Expected Metal, CUDA, or OpenCL support (this may be expected if build tags not specified)")
			}
		},
		"linux": func(types []gpu.GPUType) {
			hasExpectedType := false
			for _, gpuType := range types {
				if gpuType == gpu.GPUTypeCUDA || gpuType == gpu.GPUTypeOpenCL {
					hasExpectedType = true
					break
				}
			}
			if !hasExpectedType {
				t.Log("Linux: Expected CUDA or OpenCL support (this may be expected if no GPU hardware)")
			}
		},
		"windows": func(types []gpu.GPUType) {
			hasExpectedType := false
			for _, gpuType := range types {
				if gpuType == gpu.GPUTypeCUDA || gpuType == gpu.GPUTypeOpenCL {
					hasExpectedType = true
					break
				}
			}
			if !hasExpectedType {
				t.Log("Windows: Expected CUDA or OpenCL support (this may be expected if no GPU hardware)")
			}
		},
	}

	// Get supported GPU types
	supportedTypes := manager.GetGPUTypes()

	// Check platform expectations
	if expectation, exists := platformExpectations[runtime.GOOS]; exists {
		expectation(supportedTypes)
	}

	// Test that each detected GPU provides basic information
	for _, gpu := range gpus {
		t.Logf("GPU %d: %s (%s) - %d MB", gpu.ID, gpu.Name, gpu.Type, gpu.TotalMemory/(1024*1024))

		// Verify required fields are populated
		if gpu.Name == "" {
			t.Errorf("GPU %d: Name should not be empty", gpu.ID)
		}
		if gpu.Type == "" {
			t.Errorf("GPU %d: Type should not be empty", gpu.ID)
		}
		if gpu.TotalMemory <= 0 {
			t.Errorf("GPU %d: TotalMemory should be positive", gpu.ID)
		}
	}

	// Test GPU selection works
	bestGPU, err := manager.SelectBestGPU(config)
	if err != nil {
		t.Logf("Best GPU selection failed: %v", err)
	} else {
		t.Logf("Best GPU selected: %s (%s)", bestGPU.Name, bestGPU.Type)
	}

	// Cleanup
	err = manager.Cleanup()
	if err != nil {
		t.Errorf("Manager cleanup failed: %v", err)
	}
}

func TestPlatformSpecificGPUTypes(t *testing.T) {
	tests := []struct {
		platform    string
		arch        string
		expectedMin int // minimum expected GPU types
		description string
	}{
		{"darwin", "amd64", 1, "macOS Intel should have at least CPU fallback"},
		{"darwin", "arm64", 1, "macOS Apple Silicon should have at least CPU fallback"},
		{"linux", "amd64", 1, "Linux should have at least CPU fallback"},
		{"windows", "amd64", 1, "Windows should have at least CPU fallback"},
	}

	currentPlatform := runtime.GOOS
	currentArch := runtime.GOARCH

	for _, test := range tests {
		if test.platform == currentPlatform && test.arch == currentArch {
			t.Run(test.description, func(t *testing.T) {
				logger := log.New(os.Stdout, "[Platform-Test] ", log.LstdFlags)
				config := gpu.DefaultGPUConfig()
				manager := gpu.NewManager(config, logger)

				types := manager.GetGPUTypes()
				if len(types) < test.expectedMin {
					t.Errorf("Expected at least %d GPU types, got %d", test.expectedMin, len(types))
				}

				// Always expect CPU fallback
				hasCPU := false
				for _, gpuType := range types {
					if gpuType == gpu.GPUTypeCPU {
						hasCPU = true
						break
					}
				}
				if !hasCPU {
					t.Error("Expected CPU fallback to always be available")
				}

				// Platform-specific checks
				switch test.platform {
				case "darwin":
					// On macOS, we might have Metal (if build tags included)
					t.Logf("macOS detected types: %v", types)
				case "linux", "windows":
					// On Linux/Windows, we might have CUDA/OpenCL
					t.Logf("%s detected types: %v", test.platform, types)
				}

				manager.Cleanup()
			})
		}
	}
}

func TestGPUFallbackBehavior(t *testing.T) {
	logger := log.New(os.Stdout, "[Fallback-Test] ", log.LstdFlags)

	// Test with GPU disabled
	config := gpu.DefaultGPUConfig()
	config.Enabled = false

	manager := gpu.NewManager(config, logger)

	// Should still work but not select GPU
	_, err := manager.SelectBestGPU(config)
	if err == nil {
		t.Error("Expected error when GPU is disabled")
	}

	// Test with impossible requirements
	config.Enabled = true
	config.MinMemoryGB = 999999 // Impossible memory requirement

	bestGPU, err := manager.SelectBestGPU(config)
	if err == nil {
		t.Errorf("Expected error with impossible memory requirement, got GPU: %v", bestGPU)
	}

	manager.Cleanup()
}

// Benchmark cross-platform GPU detection performance
func BenchmarkGPUDetection(b *testing.B) {
	logger := log.New(os.Stdout, "[Benchmark] ", log.LstdFlags)
	config := gpu.DefaultGPUConfig()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		manager := gpu.NewManager(config, logger)
		_, err := manager.GetAvailableGPUs()
		if err != nil {
			b.Logf("Detection failed: %v", err)
		}
		manager.Cleanup()
	}
}
