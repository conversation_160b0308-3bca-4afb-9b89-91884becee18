package gpu

import (
	"math"
	"math/rand/v2"
	"testing"
	"time"

	"neuralmetergo/internal/gpu"
)

// TestARIMAModel tests the ARIMA prediction model
func TestARIMAModel(t *testing.T) {
	model := gpu.NewARIMAModel()

	// Create test data with trend
	data := generateTrendData(50, 10.0, 2.0, 5.0)

	// Train the model
	err := model.Train(data)
	if err != nil {
		t.Fatalf("Failed to train ARIMA model: %v", err)
	}

	// Test prediction
	prediction, err := model.Predict(time.Hour)
	if err != nil {
		t.Fatalf("Failed to get prediction: %v", err)
	}

	// Validate prediction structure
	if prediction.PredictedQueue < 0 {
		t.<PERSON><PERSON>r("Predicted queue length should be non-negative")
	}

	if prediction.Confidence < 0 || prediction.Confidence > 1 {
		t.<PERSON><PERSON><PERSON>("Confidence should be between 0 and 1, got %f", prediction.Confidence)
	}

	if prediction.RecommendedAction == "" {
		t.Error("Recommended action should not be empty")
	}

	// Test accuracy
	accuracy := model.GetAccuracy()
	if accuracy < 0 || accuracy > 1 {
		t.<PERSON><PERSON><PERSON>("Accuracy should be between 0 and 1, got %f", accuracy)
	}
}

// TestSARIMAModel tests the Seasonal ARIMA prediction model
func TestSARIMAModel(t *testing.T) {
	model := gpu.NewSARIMAModel()

	// Create test data with seasonal pattern
	data := generateSeasonalData(100, 50.0, 20.0, 24)

	// Train the model
	err := model.Train(data)
	if err != nil {
		t.Fatalf("Failed to train SARIMA model: %v", err)
	}

	// Test prediction
	prediction, err := model.Predict(time.Hour)
	if err != nil {
		t.Fatalf("Failed to get prediction: %v", err)
	}

	// Validate prediction
	if prediction.PredictedQueue < 0 {
		t.Error("Predicted queue length should be non-negative")
	}

	if prediction.Confidence < 0 || prediction.Confidence > 1 {
		t.Errorf("Confidence should be between 0 and 1, got %f", prediction.Confidence)
	}

	// Test with insufficient data
	smallData := generateSeasonalData(10, 50.0, 20.0, 24)
	err = model.Train(smallData)
	if err == nil {
		t.Error("Should fail with insufficient data")
	}
}

// TestProphetModel tests the Prophet-like prediction model
func TestProphetModel(t *testing.T) {
	model := gpu.NewProphetModel()

	// Create test data with trend and seasonality
	data := generateComplexSeasonalData(50, 40.0, 1.0, 10.0, 5.0)

	// Train the model
	err := model.Train(data)
	if err != nil {
		t.Fatalf("Failed to train Prophet model: %v", err)
	}

	// Test prediction
	prediction, err := model.Predict(time.Hour * 2)
	if err != nil {
		t.Fatalf("Failed to get prediction: %v", err)
	}

	// Validate prediction
	if prediction.PredictedQueue < 0 {
		t.Error("Predicted queue length should be non-negative")
	}

	if prediction.Confidence < 0 || prediction.Confidence > 1 {
		t.Errorf("Confidence should be between 0 and 1, got %f", prediction.Confidence)
	}

	// Test that Prophet model produces reasonable predictions
	// (Cannot test internal methods as they are unexported)
	if prediction.PredictedQueue > 1000 {
		t.Errorf("Prediction seems unreasonably high: %d", prediction.PredictedQueue)
	}
}

// TestHoltWintersModel tests the Holt-Winters exponential smoothing model
func TestHoltWintersModel(t *testing.T) {
	model := gpu.NewHoltWintersModel()

	// Create test data with trend and seasonality
	data := generateComplexSeasonalData(60, 30.0, 0.5, 15.0, 8.0)

	// Train the model
	err := model.Train(data)
	if err != nil {
		t.Fatalf("Failed to train Holt-Winters model: %v", err)
	}

	// Test prediction
	prediction, err := model.Predict(time.Hour)
	if err != nil {
		t.Fatalf("Failed to get prediction: %v", err)
	}

	// Validate prediction
	if prediction.PredictedQueue < 0 {
		t.Error("Predicted queue length should be non-negative")
	}

	if prediction.Confidence < 0 || prediction.Confidence > 1 {
		t.Errorf("Confidence should be between 0 and 1, got %f", prediction.Confidence)
	}

	// Test with insufficient data
	smallData := generateComplexSeasonalData(10, 30.0, 0.5, 15.0, 8.0)
	err = model.Train(smallData)
	if err == nil {
		t.Error("Should fail with insufficient data for Holt-Winters")
	}
}

// TestEnsembleModel tests the ensemble prediction model
func TestEnsembleModel(t *testing.T) {
	model := gpu.NewEnsembleModel()

	// Create test data
	data := generateComplexSeasonalData(80, 45.0, 1.2, 12.0, 6.0)

	// Train the model
	err := model.Train(data)
	if err != nil {
		t.Fatalf("Failed to train ensemble model: %v", err)
	}

	// Test prediction
	prediction, err := model.Predict(time.Hour)
	if err != nil {
		t.Fatalf("Failed to get prediction: %v", err)
	}

	// Validate prediction
	if prediction.PredictedQueue < 0 {
		t.Error("Predicted queue length should be non-negative")
	}

	if prediction.Confidence < 0 || prediction.Confidence > 1 {
		t.Errorf("Confidence should be between 0 and 1, got %f", prediction.Confidence)
	}

	// Ensemble should generally have good accuracy due to combining models
	accuracy := model.GetAccuracy()
	if accuracy < 0.1 {
		t.Errorf("Ensemble accuracy seems too low: %f", accuracy)
	}
}

// TestWorkloadPredictorWithAdvancedModels tests the WorkloadPredictor with new model types
func TestWorkloadPredictorWithAdvancedModels(t *testing.T) {
	modelTypes := []string{"arima", "sarima", "prophet", "holt_winters", "ensemble"}

	for _, modelType := range modelTypes {
		t.Run(modelType, func(t *testing.T) {
			config := gpu.PredictionConfig{
				HistoryWindow:    time.Hour * 200, // Increase window to capture all data
				MinDataPoints:    50,
				ModelType:        modelType,
				UpdateInterval:   time.Minute * 5,
				SeasonalPatterns: true,
			}

			predictor := gpu.NewWorkloadPredictor(config)

			// Add test data
			data := generateComplexSeasonalData(80, 35.0, 0.8, 18.0, 7.0)
			for _, point := range data {
				predictor.AddDataPoint(point)
			}

			// Get prediction
			prediction, err := predictor.GetPrediction(time.Hour)
			if err != nil {
				t.Fatalf("Failed to get prediction for %s: %v", modelType, err)
			}

			// Validate prediction
			if prediction.PredictedQueue < 0 {
				t.Errorf("Predicted queue length should be non-negative for %s", modelType)
			}

			if prediction.Confidence < 0 || prediction.Confidence > 1 {
				t.Errorf("Confidence should be between 0 and 1 for %s, got %f", modelType, prediction.Confidence)
			}
		})
	}
}

// TestModelComparison compares accuracy of different models on the same dataset
func TestModelComparison(t *testing.T) {
	// Create consistent test data
	data := generateComplexSeasonalData(100, 50.0, 1.0, 20.0, 10.0)

	models := map[string]gpu.PredictionModel{
		"linear_regression":     gpu.NewLinearRegressionModel(),
		"moving_average":        gpu.NewMovingAverageModel(),
		"exponential_smoothing": gpu.NewExponentialSmoothingModel(),
		"arima":                 gpu.NewARIMAModel(),
		"holt_winters":          gpu.NewHoltWintersModel(),
		"ensemble":              gpu.NewEnsembleModel(),
	}

	accuracies := make(map[string]float64)

	// Train all models and collect accuracies
	for name, model := range models {
		err := model.Train(data)
		if err != nil {
			t.Logf("Model %s failed to train: %v", name, err)
			continue
		}

		accuracy := model.GetAccuracy()
		accuracies[name] = accuracy
		t.Logf("Model %s accuracy: %f", name, accuracy)

		// Test prediction capability
		_, err = model.Predict(time.Hour)
		if err != nil {
			t.Errorf("Model %s failed to predict: %v", name, err)
		}
	}

	// Ensemble should generally perform well
	if ensembleAcc, exists := accuracies["ensemble"]; exists {
		if ensembleAcc < 0.2 {
			t.Errorf("Ensemble accuracy seems too low: %f", ensembleAcc)
		}
	}

	// Advanced models should generally outperform basic ones on complex data
	if arimaAcc, exists := accuracies["arima"]; exists {
		if linearAcc, exists := accuracies["linear_regression"]; exists {
			if arimaAcc < linearAcc*0.5 {
				t.Logf("ARIMA underperformed linear regression significantly: %f vs %f", arimaAcc, linearAcc)
			}
		}
	}
}

// Helper functions for generating test data

// generateTrendData creates data with a linear trend
func generateTrendData(count int, base, trend, noise float64) []gpu.WorkloadDataPoint {
	data := make([]gpu.WorkloadDataPoint, count)
	baseTime := time.Now().Add(-time.Duration(count) * time.Hour)

	for i := 0; i < count; i++ {
		value := base + trend*float64(i) + (rand.Float64()-0.5)*noise*2
		if value < 0 {
			value = 0
		}

		data[i] = gpu.WorkloadDataPoint{
			Timestamp:      baseTime.Add(time.Duration(i) * time.Hour),
			QueueLength:    int(value),
			ActiveTasks:    int(value * 0.8),
			AvgUtilization: 0.7,
			NodesActive:    5,
		}
	}

	return data
}

// generateSeasonalData creates data with seasonal patterns
func generateSeasonalData(count int, base, amplitude float64, period int) []gpu.WorkloadDataPoint {
	data := make([]gpu.WorkloadDataPoint, count)
	baseTime := time.Now().Add(-time.Duration(count) * time.Hour)

	for i := 0; i < count; i++ {
		seasonal := amplitude * math.Sin(2*math.Pi*float64(i)/float64(period))
		value := base + seasonal + (rand.Float64()-0.5)*5
		if value < 0 {
			value = 0
		}

		data[i] = gpu.WorkloadDataPoint{
			Timestamp:      baseTime.Add(time.Duration(i) * time.Hour),
			QueueLength:    int(value),
			ActiveTasks:    int(value * 0.8),
			AvgUtilization: 0.6 + 0.3*seasonal/amplitude,
			NodesActive:    3 + int(value/20),
		}
	}

	return data
}

// generateComplexSeasonalData creates data with trend, seasonality, and noise
func generateComplexSeasonalData(count int, base, trend, amplitude, noise float64) []gpu.WorkloadDataPoint {
	data := make([]gpu.WorkloadDataPoint, count)
	baseTime := time.Now().Add(-time.Duration(count) * time.Hour)

	for i := 0; i < count; i++ {
		// Daily seasonality (24-hour period)
		dailySeasonal := amplitude * math.Sin(2*math.Pi*float64(i)/24.0)
		// Weekly seasonality (168-hour period)
		weeklySeasonal := amplitude * 0.5 * math.Sin(2*math.Pi*float64(i)/168.0)
		// Linear trend
		trendComponent := trend * float64(i)
		// Random noise
		noiseComponent := (rand.Float64() - 0.5) * noise * 2

		value := base + trendComponent + dailySeasonal + weeklySeasonal + noiseComponent
		if value < 0 {
			value = 0
		}

		data[i] = gpu.WorkloadDataPoint{
			Timestamp:      baseTime.Add(time.Duration(i) * time.Hour),
			QueueLength:    int(value),
			ActiveTasks:    int(value * 0.8),
			AvgUtilization: 0.5 + 0.4*(dailySeasonal+weeklySeasonal)/amplitude,
			NodesActive:    2 + int(value/15),
		}
	}

	return data
}
