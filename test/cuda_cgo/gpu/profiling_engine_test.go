package gpu

import (
	"context"
	"log"
	"os"
	"testing"
	"time"

	"neuralmetergo/internal/gpu"
)

func TestProfilingEngineBasicFunctionality(t *testing.T) {
	// Create a test logger
	logger := log.New(os.Stdout, "[ProfilingEngineTest] ", log.LstdFlags)

	// Create profiling engine with default config
	config := gpu.DefaultProfilingConfig()
	engine := gpu.NewProfilingEngine(nil, nil, config, logger)

	if engine == nil {
		t.Fatal("Failed to create profiling engine")
	}

	// Test initial state
	if engine.IsRunning() {
		t.Error("Profiling engine should not be running initially")
	}

	stats := engine.GetProfilingStats()
	if stats.TotalSessions != 0 {
		t.Error("Expected 0 total sessions initially")
	}
	if stats.IsRunning {
		t.Error("Expected IsRunning to be false initially")
	}
}

func TestProfilingEngineStartStop(t *testing.T) {
	logger := log.New(os.Stdout, "[ProfilingEngineTest] ", log.LstdFlags)
	config := gpu.DefaultProfilingConfig()
	engine := gpu.NewProfilingEngine(nil, nil, config, logger)

	ctx := context.Background()

	// Test starting the engine
	err := engine.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start profiling engine: %v", err)
	}

	if !engine.IsRunning() {
		t.Error("Profiling engine should be running after Start()")
	}

	// Test starting already running engine
	err = engine.Start(ctx)
	if err == nil {
		t.Error("Expected error when starting already running engine")
	}

	// Test stopping the engine
	err = engine.Stop()
	if err != nil {
		t.Fatalf("Failed to stop profiling engine: %v", err)
	}

	if engine.IsRunning() {
		t.Error("Profiling engine should not be running after Stop()")
	}

	// Test stopping already stopped engine (should not error)
	err = engine.Stop()
	if err != nil {
		t.Errorf("Unexpected error when stopping already stopped engine: %v", err)
	}
}

func TestProfilingEngineDisabledConfig(t *testing.T) {
	logger := log.New(os.Stdout, "[ProfilingEngineTest] ", log.LstdFlags)
	config := gpu.DefaultProfilingConfig()
	config.Enabled = false
	engine := gpu.NewProfilingEngine(nil, nil, config, logger)

	ctx := context.Background()

	// Test starting disabled engine
	err := engine.Start(ctx)
	if err == nil {
		t.Error("Expected error when starting disabled profiling engine")
	}

	if engine.IsRunning() {
		t.Error("Disabled profiling engine should not be running")
	}
}

func TestProfilingSessionManagement(t *testing.T) {
	logger := log.New(os.Stdout, "[ProfilingEngineTest] ", log.LstdFlags)
	config := gpu.DefaultProfilingConfig()
	engine := gpu.NewProfilingEngine(nil, nil, config, logger)

	ctx := context.Background()
	err := engine.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start profiling engine: %v", err)
	}
	defer engine.Stop()

	// Test starting a profiling session
	sessionConfig := gpu.SessionConfiguration{
		DeviceFilter:        []string{"device_0"},
		KernelFilter:        []string{},
		TimingPrecision:     time.Microsecond,
		SamplingRate:        1.0,
		MemoryTrackingLevel: gpu.MemoryTrackingBasic,
		PowerSamplingRate:   100 * time.Millisecond,
	}

	session, err := engine.StartProfilingSession("device_0", sessionConfig)
	if err != nil {
		t.Fatalf("Failed to start profiling session: %v", err)
	}

	if session == nil {
		t.Fatal("Profiling session should not be nil")
	}

	if session.ID == "" {
		t.Error("Profiling session should have a non-empty ID")
	}

	if session.DeviceID != "device_0" {
		t.Errorf("Expected device ID 'device_0', got '%s'", session.DeviceID)
	}

	if session.Status != gpu.ProfilingStatusStarted {
		t.Errorf("Expected status 'started', got '%s'", session.Status)
	}

	// Test getting active sessions
	activeSessions := engine.GetActiveSessions()
	if len(activeSessions) != 1 {
		t.Errorf("Expected 1 active session, got %d", len(activeSessions))
	}

	// Test stopping the profiling session
	results, err := engine.StopProfilingSession(session.ID)
	if err != nil {
		t.Fatalf("Failed to stop profiling session: %v", err)
	}

	if results == nil {
		t.Fatal("Session results should not be nil")
	}

	// Verify session status changed
	if session.Status != gpu.ProfilingStatusCompleted {
		t.Errorf("Expected status 'completed', got '%s'", session.Status)
	}

	// Test stopping non-existent session
	_, err = engine.StopProfilingSession("non_existent_session")
	if err == nil {
		t.Error("Expected error when stopping non-existent session")
	}
}

func TestProfilingSessionWithNotRunningEngine(t *testing.T) {
	logger := log.New(os.Stdout, "[ProfilingEngineTest] ", log.LstdFlags)
	config := gpu.DefaultProfilingConfig()
	engine := gpu.NewProfilingEngine(nil, nil, config, logger)

	// Try to start session without starting engine
	sessionConfig := gpu.SessionConfiguration{}
	_, err := engine.StartProfilingSession("device_0", sessionConfig)
	if err == nil {
		t.Error("Expected error when starting session with stopped engine")
	}
}

func TestProfilingEngineStats(t *testing.T) {
	logger := log.New(os.Stdout, "[ProfilingEngineTest] ", log.LstdFlags)
	config := gpu.DefaultProfilingConfig()
	engine := gpu.NewProfilingEngine(nil, nil, config, logger)

	ctx := context.Background()
	err := engine.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start profiling engine: %v", err)
	}
	defer engine.Stop()

	// Get initial stats
	stats := engine.GetProfilingStats()
	if !stats.IsRunning {
		t.Error("Expected IsRunning to be true")
	}
	if stats.TotalSessions != 0 {
		t.Error("Expected 0 total sessions initially")
	}
	if stats.ActiveSessions != 0 {
		t.Error("Expected 0 active sessions initially")
	}

	// Start a session
	sessionConfig := gpu.SessionConfiguration{
		DeviceFilter: []string{"device_0"},
	}
	session, err := engine.StartProfilingSession("device_0", sessionConfig)
	if err != nil {
		t.Fatalf("Failed to start profiling session: %v", err)
	}

	// Check stats after starting session
	stats = engine.GetProfilingStats()
	if stats.TotalSessions != 1 {
		t.Errorf("Expected 1 total session, got %d", stats.TotalSessions)
	}
	if stats.ActiveSessions != 1 {
		t.Errorf("Expected 1 active session, got %d", stats.ActiveSessions)
	}

	// Stop the session
	_, err = engine.StopProfilingSession(session.ID)
	if err != nil {
		t.Fatalf("Failed to stop profiling session: %v", err)
	}

	// Check stats after stopping session
	stats = engine.GetProfilingStats()
	if stats.TotalSessions != 1 {
		t.Errorf("Expected 1 total session, got %d", stats.TotalSessions)
	}
	if stats.ActiveSessions != 0 {
		t.Errorf("Expected 0 active sessions, got %d", stats.ActiveSessions)
	}
}

func TestDefaultProfilingConfig(t *testing.T) {
	config := gpu.DefaultProfilingConfig()

	if !config.Enabled {
		t.Error("Default config should be enabled")
	}
	if config.KernelTimingPrecision != time.Microsecond {
		t.Errorf("Expected microsecond precision, got %v", config.KernelTimingPrecision)
	}
	if !config.MemoryAnalysisEnabled {
		t.Error("Memory analysis should be enabled by default")
	}
	if !config.OccupancyTrackingEnabled {
		t.Error("Occupancy tracking should be enabled by default")
	}
	if !config.PowerMonitoringEnabled {
		t.Error("Power monitoring should be enabled by default")
	}
	if !config.BottleneckDetectionEnabled {
		t.Error("Bottleneck detection should be enabled by default")
	}
	if config.ProfilingInterval != 100*time.Millisecond {
		t.Errorf("Expected 100ms profiling interval, got %v", config.ProfilingInterval)
	}
	if config.MaxSessionHistory != 100 {
		t.Errorf("Expected 100 max session history, got %d", config.MaxSessionHistory)
	}
}

func TestSessionResultsAnalysis(t *testing.T) {
	logger := log.New(os.Stdout, "[ProfilingEngineTest] ", log.LstdFlags)
	config := gpu.DefaultProfilingConfig()
	engine := gpu.NewProfilingEngine(nil, nil, config, logger)

	ctx := context.Background()
	err := engine.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start profiling engine: %v", err)
	}
	defer engine.Stop()

	// Start a session
	sessionConfig := gpu.SessionConfiguration{
		DeviceFilter: []string{"device_0"},
	}
	session, err := engine.StartProfilingSession("device_0", sessionConfig)
	if err != nil {
		t.Fatalf("Failed to start profiling session: %v", err)
	}

	// Add some mock kernel executions for testing analysis
	mockKernel := &gpu.KernelExecution{
		KernelName:         "test_kernel",
		DeviceID:           "device_0",
		StartTime:          time.Now(),
		EndTime:            time.Now().Add(1 * time.Millisecond),
		Duration:           1 * time.Millisecond,
		GridSize:           [3]int{32, 32, 1},
		BlockSize:          [3]int{16, 16, 1},
		SharedMemoryUsed:   1024,
		RegistersPerThread: 32,
		OccupancyPercent:   75.0,
		ThroughputGOPS:     100.0,
		MemoryBandwidthGB:  150.0,
	}
	session.KernelExecutions = append(session.KernelExecutions, mockKernel)

	// Add some mock memory operations
	mockMemoryOp := &gpu.MemoryOperation{
		OperationType:        gpu.MemoryOpHostToDevice,
		DeviceID:             "device_0",
		StartTime:            time.Now(),
		EndTime:              time.Now().Add(500 * time.Microsecond),
		Duration:             500 * time.Microsecond,
		BytesTransferred:     1024 * 1024, // 1MB
		BandwidthMBps:        2048,        // 2GB/s
		SourceLocation:       gpu.MemoryLocationHost,
		DestLocation:         gpu.MemoryLocationDevice,
		CoalescingEfficiency: 85.0,
	}
	session.MemoryOperations = append(session.MemoryOperations, mockMemoryOp)

	// Add some mock power measurements
	mockPowerMeasurement := &gpu.PowerMeasurement{
		DeviceID:       "device_0",
		Timestamp:      time.Now(),
		PowerWatts:     250.0,
		Temperature:    65,
		FanSpeedRPM:    2000,
		MemoryClockMHz: 6000,
		CoreClockMHz:   1500,
		Utilization:    80.0,
	}
	session.PowerMeasurements = append(session.PowerMeasurements, mockPowerMeasurement)

	// Stop the session and get results
	results, err := engine.StopProfilingSession(session.ID)
	if err != nil {
		t.Fatalf("Failed to stop profiling session: %v", err)
	}

	// Verify results structure
	if results.Summary.TotalKernels != 1 {
		t.Errorf("Expected 1 kernel in summary, got %d", results.Summary.TotalKernels)
	}

	if results.Summary.TotalMemoryTransfers != 1 {
		t.Errorf("Expected 1 memory transfer in summary, got %d", results.Summary.TotalMemoryTransfers)
	}

	if results.Summary.AverageOccupancy != 75.0 {
		t.Errorf("Expected 75%% average occupancy, got %.1f", results.Summary.AverageOccupancy)
	}

	if len(results.KernelAnalysis) != 1 {
		t.Errorf("Expected 1 kernel analysis, got %d", len(results.KernelAnalysis))
	}

	if results.MemoryAnalysis == nil {
		t.Error("Memory analysis should not be nil")
	}

	if results.PowerAnalysis == nil {
		t.Error("Power analysis should not be nil")
	}

	if results.PerformanceScore <= 0 || results.PerformanceScore > 100 {
		t.Errorf("Performance score should be between 0 and 100, got %.2f", results.PerformanceScore)
	}

	// Verify kernel analysis
	kernelAnalysis := results.KernelAnalysis[0]
	if kernelAnalysis.KernelName != "test_kernel" {
		t.Errorf("Expected kernel name 'test_kernel', got '%s'", kernelAnalysis.KernelName)
	}

	if kernelAnalysis.PerformanceRating <= 0 {
		t.Errorf("Performance rating should be positive, got %.2f", kernelAnalysis.PerformanceRating)
	}

	// Verify memory analysis
	if results.MemoryAnalysis.CoalescingEfficiency != 85.0 {
		t.Errorf("Expected 85%% coalescing efficiency, got %.1f", results.MemoryAnalysis.CoalescingEfficiency)
	}

	// Verify power analysis
	if results.PowerAnalysis.EnergyConsumption != 250.0 {
		t.Errorf("Expected 250W energy consumption, got %.1f", results.PowerAnalysis.EnergyConsumption)
	}
}

func TestProfilingEngineWithNilLogger(t *testing.T) {
	config := gpu.DefaultProfilingConfig()
	engine := gpu.NewProfilingEngine(nil, nil, config, nil)

	if engine == nil {
		t.Fatal("Engine should be created even with nil logger")
	}

	// Should work without crashing
	ctx := context.Background()
	err := engine.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start profiling engine with nil logger: %v", err)
	}
	defer engine.Stop()
}

func BenchmarkProfilingEngineSessionCreation(b *testing.B) {
	logger := log.New(os.Stdout, "[ProfilingEngineBench] ", log.LstdFlags)
	config := gpu.DefaultProfilingConfig()
	engine := gpu.NewProfilingEngine(nil, nil, config, logger)

	ctx := context.Background()
	err := engine.Start(ctx)
	if err != nil {
		b.Fatalf("Failed to start profiling engine: %v", err)
	}
	defer engine.Stop()

	sessionConfig := gpu.SessionConfiguration{
		DeviceFilter: []string{"device_0"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		session, err := engine.StartProfilingSession("device_0", sessionConfig)
		if err != nil {
			b.Fatalf("Failed to start profiling session: %v", err)
		}
		_, err = engine.StopProfilingSession(session.ID)
		if err != nil {
			b.Fatalf("Failed to stop profiling session: %v", err)
		}
	}
}
