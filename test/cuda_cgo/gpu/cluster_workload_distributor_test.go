package gpu

import (
	"log"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"neuralmetergo/internal/gpu"
)

func TestNewClusterWorkloadDistributor(t *testing.T) {
	resourceDiscovery := createMockResourceDiscovery(t)
	localDistributor := createMockLocalDistributor(t)
	logger := log.Default()

	distributor, err := gpu.NewClusterWorkloadDistributor(
		resourceDiscovery,
		localDistributor,
		gpu.ClusterStrategyResourceBased,
		logger,
	)

	assert.NoError(t, err)
	assert.NotNil(t, distributor)
	assert.Equal(t, gpu.ClusterStrategyResourceBased, distributor.GetStrategy())
}

func TestDefaultClusterDistributorConfig(t *testing.T) {
	config := gpu.DefaultClusterDistributorConfig()

	assert.Equal(t, 0.15, config.CPUMemoryWeight)
	assert.Equal(t, 0.25, config.GPUMemoryWeight)
	assert.Equal(t, 0.20, config.ComputeCapacityWeight)
	assert.Equal(t, 0.10, config.NetworkLatencyWeight)
	assert.Equal(t, 0.10, config.BandwidthWeight)
	assert.True(t, config.FailoverEnabled)
	assert.Equal(t, 3, config.MaxFailureRetries)
	assert.Equal(t, 10, config.MaxTasksPerNode)
	assert.Equal(t, 0.10, config.MinResourceThreshold)
}

func TestClusterDistributionStrategies(t *testing.T) {
	strategies := []gpu.ClusterDistributionStrategy{
		gpu.ClusterStrategyRoundRobin,
		gpu.ClusterStrategyResourceBased,
		gpu.ClusterStrategyLatencyOptimized,
		gpu.ClusterStrategyDataLocality,
		gpu.ClusterStrategyHybrid,
		gpu.ClusterStrategyAdaptive,
	}

	for _, strategy := range strategies {
		t.Run(strategy.String(), func(t *testing.T) {
			assert.NotEmpty(t, strategy.String())
		})
	}
}

func TestSetAndGetStrategy(t *testing.T) {
	distributor := createTestClusterDistributor(t)

	distributor.SetStrategy(gpu.ClusterStrategyAdaptive)
	assert.Equal(t, gpu.ClusterStrategyAdaptive, distributor.GetStrategy())
}

func TestSetAndGetConfig(t *testing.T) {
	distributor := createTestClusterDistributor(t)

	config := gpu.DefaultClusterDistributorConfig()
	config.CPUMemoryWeight = 0.25
	config.GPUMemoryWeight = 0.35

	err := distributor.SetConfig(config)
	assert.NoError(t, err)

	retrievedConfig := distributor.GetConfig()
	assert.Equal(t, 0.25, retrievedConfig.CPUMemoryWeight)
	assert.Equal(t, 0.35, retrievedConfig.GPUMemoryWeight)
}

func TestCreateClusterTask(t *testing.T) {
	task := &gpu.ClusterTask{
		WorkloadTask: gpu.WorkloadTask{
			ID:                "test-task-1",
			Priority:          5,
			MemoryRequirement: 2048 * 1024 * 1024, // 2GB
			ComputeIntensity:  gpu.ComputeModerate,
			EstimatedDuration: time.Minute * 30,
		},
		DataLocation: []string{"node-1"},
		DataSize:     1024 * 1024 * 1024, // 1GB
	}

	assert.Equal(t, "test-task-1", task.ID)
	assert.Equal(t, gpu.TaskPriority(5), task.Priority)
	assert.Equal(t, int64(2048*1024*1024), task.MemoryRequirement)
	assert.Equal(t, gpu.ComputeModerate, task.ComputeIntensity)
	assert.Equal(t, []string{"node-1"}, task.DataLocation)
	assert.Equal(t, int64(1024*1024*1024), task.DataSize)
}

func TestClusterTaskWithNetworkRequirements(t *testing.T) {
	task := &gpu.ClusterTask{
		WorkloadTask: gpu.WorkloadTask{
			ID:                "test-task-network",
			Priority:          3,
			MemoryRequirement: 1024 * 1024 * 1024, // 1GB
			ComputeIntensity:  gpu.ComputeLight,
			EstimatedDuration: time.Minute * 15,
		},
		NetworkRequirements: &gpu.NetworkRequirements{
			MinBandwidth:  100.0, // 100 Mbps
			MaxLatency:    50.0,  // 50ms
			MaxPacketLoss: 1.0,   // 1%
		},
	}

	assert.NotNil(t, task.NetworkRequirements)
	assert.Equal(t, 100.0, task.NetworkRequirements.MinBandwidth)
	assert.Equal(t, 50.0, task.NetworkRequirements.MaxLatency)
	assert.Equal(t, 1.0, task.NetworkRequirements.MaxPacketLoss)
}

func TestClusterTaskWithAffinity(t *testing.T) {
	task := &gpu.ClusterTask{
		WorkloadTask: gpu.WorkloadTask{
			ID:                "test-task-affinity",
			Priority:          4,
			MemoryRequirement: 512 * 1024 * 1024, // 512MB
			ComputeIntensity:  gpu.ComputeLight,
			EstimatedDuration: time.Minute * 10,
		},
		AffinityNodes:     []string{"node-1"},
		AntiAffinityNodes: []string{"node-2"},
	}

	assert.Equal(t, []string{"node-1"}, task.AffinityNodes)
	assert.Equal(t, []string{"node-2"}, task.AntiAffinityNodes)
}

// Helper functions

func createTestClusterDistributor(t *testing.T) *gpu.ClusterWorkloadDistributor {
	return createTestClusterDistributorWithStrategy(t, gpu.ClusterStrategyResourceBased)
}

func createTestClusterDistributorWithStrategy(t *testing.T, strategy gpu.ClusterDistributionStrategy) *gpu.ClusterWorkloadDistributor {
	resourceDiscovery := createMockResourceDiscovery(t)
	localDistributor := createMockLocalDistributor(t)
	logger := log.Default()

	distributor, err := gpu.NewClusterWorkloadDistributor(
		resourceDiscovery,
		localDistributor,
		strategy,
		logger,
	)
	require.NoError(t, err)

	return distributor
}

func createMockResourceDiscovery(t *testing.T) *gpu.GPUResourceDiscovery {
	config := gpu.DefaultDiscoveryConfig()
	config.MulticastPort = 19200
	config.UnicastPort = 19300

	logger := log.Default()
	discovery, err := gpu.NewGPUResourceDiscovery(config, logger)
	require.NoError(t, err)

	return discovery
}

func createMockLocalDistributor(t *testing.T) *gpu.WorkloadDistributor {
	// Return a minimal mock distributor
	return &gpu.WorkloadDistributor{}
}
