package gpu_test

import (
	"fmt"
	"log"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"neuralmetergo/internal/gpu"
)

// mockLogger implements the Logger interface for testing
type mockLogger struct {
	messages []string
}

func (m *mockLogger) Printf(format string, args ...interface{}) {
	// Store messages for testing
	m.messages = append(m.messages, fmt.Sprintf(format, args...))
}

func (m *mockLogger) Errorf(format string, args ...interface{}) {
	// Store error messages for testing
	m.messages = append(m.messages, fmt.Sprintf("ERROR: "+format, args...))
}

func TestNewGPUStateSerializerPublicAPI(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()

	serializer := gpu.NewGPUStateSerializer(config, logger)

	assert.NotNil(t, serializer)
	// Only test public methods
	tensors := serializer.GetRegisteredTensors()
	// GetRegisteredTensors might return nil for empty cache
	if tensors != nil {
		assert.Equal(t, 0, len(tensors))
	}
}

func TestDefaultSerializerConfigValues(t *testing.T) {
	config := gpu.DefaultSerializerConfig()

	assert.True(t, config.CompressionEnabled)
	assert.Equal(t, 6, config.CompressionLevel)
	assert.False(t, config.EncryptionEnabled)
	assert.True(t, config.ChecksumEnabled)
	assert.Equal(t, int64(100*1024*1024), config.MaxTensorSize)
	assert.True(t, config.IncludeData)
	assert.Equal(t, int64(1024*1024), config.ChunkSize)
	assert.True(t, config.Parallel)
	assert.Equal(t, 4, config.WorkerCount)
}

func TestDefaultCompressorPublicAPI(t *testing.T) {
	compressor := gpu.NewDefaultCompressor(6)

	testData := []byte("Hello, World! This is test data for compression.")

	// Test compression
	compressed, err := compressor.Compress(testData)
	require.NoError(t, err)
	assert.NotNil(t, compressed)

	// Test decompression
	decompressed, err := compressor.Decompress(compressed)
	require.NoError(t, err)
	assert.Equal(t, testData, decompressed)
}

func TestTensorRegistrationPublicAPI(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)

	// Create a test tensor
	shape := gpu.TensorShape{2, 3, 4}
	tensor, err := gpu.NewTensor(shape, gpu.TensorFloat32, gpu.DeviceCPU, 0)
	require.NoError(t, err)
	defer tensor.Free()

	// Register tensor
	tensorID := "test_tensor_1"
	serializer.RegisterTensor(tensorID, tensor)

	// Check registration
	registeredTensors := serializer.GetRegisteredTensors()
	assert.Contains(t, registeredTensors, tensorID)

	// Unregister tensor
	serializer.UnregisterTensor(tensorID)
	registeredTensors = serializer.GetRegisteredTensors()
	assert.NotContains(t, registeredTensors, tensorID)
}

func TestTensorSerializationPublicAPI(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	config.IncludeData = true
	config.ChecksumEnabled = true
	serializer := gpu.NewGPUStateSerializer(config, logger)

	// Create a test tensor
	shape := gpu.TensorShape{2, 3}
	tensor, err := gpu.NewTensor(shape, gpu.TensorFloat32, gpu.DeviceCPU, 0)
	require.NoError(t, err)
	defer tensor.Free()

	// Fill tensor with test data
	err = tensor.Fill(float32(42.0))
	require.NoError(t, err)

	// Register and serialize tensor
	tensorID := "test_tensor_serialization"
	serializer.RegisterTensor(tensorID, tensor)

	// Capture GPU state
	snapshot, err := serializer.CaptureGPUState(0, "cpu")
	require.NoError(t, err)
	assert.NotNil(t, snapshot)

	// Verify snapshot properties
	assert.Equal(t, 0, snapshot.DeviceID)
	assert.Equal(t, "cpu", snapshot.APIType)
	assert.Equal(t, "1.0", snapshot.Version)
	assert.Len(t, snapshot.TensorStates, 1)

	// Verify tensor state
	tensorState := snapshot.TensorStates[0]
	assert.Equal(t, tensorID, tensorState.ID)
	assert.Equal(t, []int64{2, 3}, tensorState.Shape)
	assert.Equal(t, "float32", tensorState.DataType)
	assert.Equal(t, "cpu", tensorState.Device)
	assert.Equal(t, 0, tensorState.DeviceID)
	assert.Equal(t, int64(24), tensorState.Size) // 2*3*4 bytes for float32
	assert.NotEmpty(t, tensorState.Data)
	assert.NotEmpty(t, tensorState.Checksum)
}

func TestSnapshotSerializationPublicAPI(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	config.CompressionEnabled = true
	serializer := gpu.NewGPUStateSerializer(config, logger)

	// Create a test snapshot
	snapshot := &gpu.GPUStateSnapshot{
		Timestamp:      time.Now(),
		DeviceID:       0,
		APIType:        "cuda",
		Version:        "1.0",
		TensorStates:   []gpu.TensorState{},
		CustomData:     make(map[string]interface{}),
		OriginalSize:   1024,
		CompressedSize: 512,
	}

	// Serialize snapshot
	data, err := serializer.SerializeSnapshot(snapshot)
	require.NoError(t, err)
	assert.NotNil(t, data)

	// Deserialize snapshot
	deserializedSnapshot, err := serializer.DeserializeSnapshot(data)
	require.NoError(t, err)
	assert.NotNil(t, deserializedSnapshot)

	// Verify deserialized data
	assert.Equal(t, snapshot.DeviceID, deserializedSnapshot.DeviceID)
	assert.Equal(t, snapshot.APIType, deserializedSnapshot.APIType)
	assert.Equal(t, snapshot.Version, deserializedSnapshot.Version)
}

func TestMemoryPoolRegistrationPublicAPI(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, &mockLogger{})

	// Create a test memory pool
	poolConfig := gpu.DefaultPoolConfig(0)
	memoryPool, err := gpu.NewAdvancedMemoryPool(poolConfig, logger)
	require.NoError(t, err)
	defer memoryPool.Shutdown()

	err = memoryPool.Initialize()
	require.NoError(t, err)

	// Register memory pool
	deviceID := 0
	serializer.RegisterMemoryPool(deviceID, memoryPool)

	// Capture state to verify memory pool registration
	snapshot, err := serializer.CaptureGPUState(deviceID, "cuda")
	require.NoError(t, err)

	// Verify memory pool state is captured
	assert.Equal(t, deviceID, snapshot.MemoryState.DeviceID)
}

func TestClearCachePublicAPI(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)

	// Create and register test tensor
	shape := gpu.TensorShape{1, 1}
	tensor, err := gpu.NewTensor(shape, gpu.TensorFloat32, gpu.DeviceCPU, 0)
	require.NoError(t, err)
	defer tensor.Free()

	tensorID := "test_tensor_clear"
	serializer.RegisterTensor(tensorID, tensor)

	// Verify tensor is registered
	tensors := serializer.GetRegisteredTensors()
	assert.Contains(t, tensors, tensorID)

	// Clear cache
	serializer.ClearCache()

	// Verify cache is cleared
	tensors = serializer.GetRegisteredTensors()
	assert.NotContains(t, tensors, tensorID)
}

// mockStreamManager for testing
type mockStreamManager struct {
	deviceID int
}

func (m *mockStreamManager) GetDeviceID() int {
	return m.deviceID
}

func TestStreamManagerRegistrationPublicAPI(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)

	// Create a mock stream manager
	streamManager := &mockStreamManager{deviceID: 0}

	// Register stream manager
	apiType := "cuda"
	serializer.RegisterStreamManager(apiType, streamManager)

	// Capture state to verify stream manager registration
	snapshot, err := serializer.CaptureGPUState(0, apiType)
	require.NoError(t, err)

	// Basic verification that snapshot was created
	assert.Equal(t, 0, snapshot.DeviceID)
	assert.Equal(t, apiType, snapshot.APIType)
}

func BenchmarkTensorSerializationPublicAPI(b *testing.B) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)

	// Create a larger tensor for benchmarking
	shape := gpu.TensorShape{100, 100}
	tensor, err := gpu.NewTensor(shape, gpu.TensorFloat32, gpu.DeviceCPU, 0)
	require.NoError(b, err)
	defer tensor.Free()

	err = tensor.Fill(float32(1.0))
	require.NoError(b, err)

	tensorID := "benchmark_tensor"
	serializer.RegisterTensor(tensorID, tensor)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := serializer.CaptureGPUState(0, "cpu")
		require.NoError(b, err)
	}
}

func BenchmarkSnapshotSerializationPublicAPI(b *testing.B) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)

	snapshot := &gpu.GPUStateSnapshot{
		Timestamp:    time.Now(),
		DeviceID:     0,
		APIType:      "cuda",
		Version:      "1.0",
		TensorStates: []gpu.TensorState{},
		CustomData:   make(map[string]interface{}),
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		data, err := serializer.SerializeSnapshot(snapshot)
		require.NoError(b, err)

		_, err = serializer.DeserializeSnapshot(data)
		require.NoError(b, err)
	}
}
