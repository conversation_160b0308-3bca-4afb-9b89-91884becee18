package gpu

import (
	"context"
	"log"
	"os"
	"testing"

	"neuralmetergo/internal/gpu"
)

func TestCloudEnvironmentDetector(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	detector := gpu.NewCloudEnvironmentDetector(logger)

	// Test cloud environment detection
	ctx := context.Background()
	environment, err := detector.DetectEnvironment(ctx)
	if err != nil {
		t.Fatalf("Failed to detect cloud environment: %v", err)
	}

	if environment == nil {
		t.Fatal("DetectEnvironment returned nil environment")
	}

	// Should detect local environment in test
	if environment.Provider != "local" {
		t.Logf("Detected provider: %s (expected local in test environment)", environment.Provider)
	}

	// Test capabilities
	capabilities := environment.Capabilities
	t.Logf("Provider %s capabilities: Utilization=%v, Memory=%v, Performance=%v, Network=%v",
		environment.Provider, capabilities.UtilizationMetrics, capabilities.MemoryMetrics,
		capabilities.PerformanceMetrics, capabilities.NetworkMetrics)

	// Verify capabilities structure for local environment
	if environment.Provider == "local" {
		if !capabilities.UtilizationMetrics {
			t.Error("Local environment should have utilization metrics")
		}
		if !capabilities.MemoryMetrics {
			t.Error("Local environment should have memory metrics")
		}
		if !capabilities.PerformanceMetrics {
			t.Error("Local environment should have performance metrics")
		}
		if !capabilities.NetworkMetrics {
			t.Error("Local environment should have network metrics")
		}
	}

	// Verify metadata exists
	if environment.Metadata == nil {
		t.Error("Environment metadata should not be nil")
	}
}

func TestCloudCompatibilityManager(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	detector := gpu.NewCloudEnvironmentDetector(logger)
	manager := gpu.NewCloudCompatibilityManager(detector, logger)

	// Test initialization
	ctx := context.Background()
	err := manager.Initialize(ctx)
	if err != nil {
		t.Fatalf("Failed to initialize cloud compatibility manager: %v", err)
	}

	// Test environment detection
	environment := manager.GetEnvironment()
	if environment == nil {
		t.Fatal("GetEnvironment returned nil")
	}

	// Test cloud environment detection
	isCloudEnvironment := manager.IsCloudEnvironment()
	t.Logf("Cloud environment: %v", isCloudEnvironment)

	// Test cloud provider
	provider := manager.GetCloudProvider()
	t.Logf("Cloud provider: %s", provider)

	// Test platform
	platform := manager.GetPlatform()
	t.Logf("Platform: %s", platform)

	// Test metric availability
	metricAvailability := manager.GetMetricAvailability()
	if metricAvailability == nil {
		t.Error("GetMetricAvailability returned nil")
	} else {
		for metric, available := range metricAvailability {
			t.Logf("Metric %s available: %v", metric, available)
		}
	}
}

func TestCloudCompatibilityConfigAdaptation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	detector := gpu.NewCloudEnvironmentDetector(logger)
	manager := gpu.NewCloudCompatibilityManager(detector, logger)

	// Initialize manager
	ctx := context.Background()
	err := manager.Initialize(ctx)
	if err != nil {
		t.Fatalf("Failed to initialize cloud compatibility manager: %v", err)
	}

	// Create test intelligence config
	originalConfig := &gpu.IntelligenceConfig{
		UtilizationWeight:   0.25,
		MemoryWeight:        0.25,
		PerformanceWeight:   0.25,
		EfficiencyWeight:    0.25,
		GracefulDegradation: false,
		RequiredMetrics:     []string{"utilization", "memory"},
		FallbackStrategy:    "static_weights",
	}

	// Test config adaptation
	adaptedConfig := manager.AdaptConfiguration(originalConfig)
	if adaptedConfig == nil {
		t.Fatal("AdaptConfiguration returned nil")
	}

	// Verify weights still sum to approximately 1.0
	totalWeight := adaptedConfig.UtilizationWeight + adaptedConfig.MemoryWeight +
		adaptedConfig.PerformanceWeight + adaptedConfig.EfficiencyWeight
	if totalWeight < 0.99 || totalWeight > 1.01 {
		t.Errorf("Adapted weights don't sum to 1.0: got %.3f", totalWeight)
	}

	t.Logf("Original config weights: U=%.2f, M=%.2f, P=%.2f, E=%.2f",
		originalConfig.UtilizationWeight, originalConfig.MemoryWeight,
		originalConfig.PerformanceWeight, originalConfig.EfficiencyWeight)
	t.Logf("Adapted config weights: U=%.2f, M=%.2f, P=%.2f, E=%.2f",
		adaptedConfig.UtilizationWeight, adaptedConfig.MemoryWeight,
		adaptedConfig.PerformanceWeight, adaptedConfig.EfficiencyWeight)
}

func TestCloudCompatibilityEnvironmentDetection(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	detector := gpu.NewCloudEnvironmentDetector(logger)

	ctx := context.Background()
	environment, err := detector.DetectEnvironment(ctx)
	if err != nil {
		t.Fatalf("Failed to detect environment: %v", err)
	}

	// Verify environment structure
	if environment.Provider == "" {
		t.Error("Environment provider should not be empty")
	}
	if environment.Platform == "" {
		t.Error("Environment platform should not be empty")
	}
	if environment.Metadata == nil {
		t.Error("Environment metadata should not be nil")
	}

	t.Logf("Environment: Provider=%s, Platform=%s, IsCloudBased=%v",
		environment.Provider, environment.Platform, environment.IsCloudBased)
}
