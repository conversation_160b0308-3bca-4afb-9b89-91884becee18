//go:build cuda && cuda_cgo
// +build cuda,cuda_cgo

package gpu

import (
	"context"
	"log"
	"testing"

	"neuralmetergo/internal/gpu"
	"neuralmetergo/internal/gpu/types"
)

func TestAbstractionManager(t *testing.T) {
	logger := log.Default()
	manager := gpu.NewAbstractionManager(logger)

	ctx := context.Background()

	// Test device enumeration
	devices, err := manager.EnumerateDevices(ctx)
	if err != nil {
		t.Fatalf("Failed to enumerate devices: %v", err)
	}

	if len(devices) == 0 {
		t.Skip("No GPU devices found for testing")
	}

	// Test device properties
	for _, device := range devices {
		t.Logf("Device: %s (Backend: %s)", device.Name, device.Backend)

		// Test device status
		if device.Status == "" {
			t.Errorf("Device %s has empty status", device.ID)
		}

		// Test device capabilities
		caps, err := manager.GetCapabilities(&device)
		if err != nil {
			t.<PERSON>rrorf("Failed to get capabilities for device %s: %v", device.ID, err)
			continue
		}

		if caps == nil {
			t.Errorf("Capabilities are nil for device %s", device.ID)
			continue
		}

		// Test capability fields that actually exist
		if caps.MaxBufferSize == 0 {
			t.Errorf("MaxBufferSize is 0 for device %s", device.ID)
		}

		if caps.MaxWorkGroupSize == 0 {
			t.Errorf("MaxWorkGroupSize is 0 for device %s", device.ID)
		}

		if caps.MaxComputeUnits == 0 {
			t.Errorf("MaxComputeUnits is 0 for device %s", device.ID)
		}

		// Test features
		if caps.Features == nil {
			t.Errorf("Features map is nil for device %s", device.ID)
		}

		// Test context creation
		gpuCtx, err := manager.CreateContext(ctx, &device)
		if err != nil {
			t.Errorf("Failed to create context for device %s: %v", device.ID, err)
			continue
		}

		if gpuCtx == nil {
			t.Errorf("Context is nil for device %s", device.ID)
			continue
		}

		// Test context properties
		if gpuCtx.GetDeviceID() != device.ID {
			t.Errorf("Context device ID mismatch: expected %s, got %s", device.ID, gpuCtx.GetDeviceID())
		}

		if !gpuCtx.IsValid() {
			t.Errorf("Context is not valid for device %s", device.ID)
		}

		// Test memory manager creation
		memMgr, err := manager.CreateMemoryManager(gpuCtx)
		if err != nil {
			t.Errorf("Failed to create memory manager for device %s: %v", device.ID, err)
		} else if memMgr == nil {
			t.Errorf("Memory manager is nil for device %s", device.ID)
		}

		// Test executor creation
		executor, err := manager.CreateExecutor(gpuCtx)
		if err != nil {
			t.Errorf("Failed to create executor for device %s: %v", device.ID, err)
		} else if executor == nil {
			t.Errorf("Executor is nil for device %s", device.ID)
		}

		// Test feature support
		supportsCompute := manager.SupportsFeature(&device, types.FeatureCompute)
		t.Logf("Device %s supports compute: %v", device.ID, supportsCompute)

		supportsFloat16 := manager.SupportsFeature(&device, types.FeatureFloat16)
		t.Logf("Device %s supports float16: %v", device.ID, supportsFloat16)

		// Clean up context
		if err := gpuCtx.Destroy(); err != nil {
			t.Errorf("Failed to destroy context for device %s: %v", device.ID, err)
		}
	}

	// Test best device selection
	bestDevice, err := manager.SelectBestDevice(ctx)
	if err != nil {
		t.Errorf("Failed to select best device: %v", err)
	} else if bestDevice == nil {
		t.Error("Best device is nil")
	} else {
		t.Logf("Best device: %s (Backend: %s)", bestDevice.Name, bestDevice.Backend)
	}

	// Test backend listing
	backends := manager.ListBackends()
	if len(backends) == 0 {
		t.Error("No backends found")
	}

	for _, backend := range backends {
		t.Logf("Available backend: %s", backend)
	}

	// Test cleanup
	if err := manager.Cleanup(); err != nil {
		t.Errorf("Failed to cleanup manager: %v", err)
	}
}
