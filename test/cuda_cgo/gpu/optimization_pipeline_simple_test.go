package gpu

import (
	"context"
	"io/ioutil"
	"log"
	"os"
	"testing"
	"time"

	gpu "neuralmetergo/internal/gpu"
)

func TestOptimizationPipelineSimple_Creation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultOptPipelineConfig()

	pipeline := gpu.NewOptimizationPipelineSimple(
		nil, // quantizationEngine
		nil, // fusionEngine
		nil, // performanceMonitor
		nil, // profilingEngine
		config,
		logger,
	)

	if pipeline == nil {
		t.Fatal("Expected pipeline to be created, got nil")
	}

	if pipeline.IsRunning() {
		t.Error("Expected pipeline to not be running initially")
	}
}

func TestOptimizationPipelineSimple_StartStop(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultOptPipelineConfig()

	pipeline := gpu.NewOptimizationPipelineSimple(
		nil, nil, nil, nil, config, logger,
	)

	ctx := context.Background()

	// Test start
	err := pipeline.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start pipeline: %v", err)
	}

	if !pipeline.IsRunning() {
		t.Error("Expected pipeline to be running after start")
	}

	// Test double start (should fail)
	err = pipeline.Start(ctx)
	if err == nil {
		t.Error("Expected error when starting already running pipeline")
	}

	// Test stop
	err = pipeline.Stop()
	if err != nil {
		t.Fatalf("Failed to stop pipeline: %v", err)
	}

	if pipeline.IsRunning() {
		t.Error("Expected pipeline to not be running after stop")
	}

	// Test double stop (should fail)
	err = pipeline.Stop()
	if err == nil {
		t.Error("Expected error when stopping already stopped pipeline")
	}
}

func TestOptimizationPipelineSimple_DisabledPipeline(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultOptPipelineConfig()
	config.Enabled = false

	pipeline := gpu.NewOptimizationPipelineSimple(
		nil, nil, nil, nil, config, logger,
	)

	ctx := context.Background()

	// Test start with disabled pipeline
	err := pipeline.Start(ctx)
	if err == nil {
		t.Error("Expected error when starting disabled pipeline")
	}

	// Test optimize with disabled pipeline
	model := &testModel{name: "test"}
	context := gpu.ModelContext{
		BatchSize:       32,
		ModelComplexity: 1.5,
		InputShape:      []int64{1, 3, 224, 224},
		Metadata:        make(map[string]interface{}),
	}

	_, err = pipeline.OptimizeModel(model, context)
	if err == nil {
		t.Error("Expected error when optimizing with disabled pipeline")
	}
}

func TestOptimizationPipelineSimple_ModelOptimization(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultOptPipelineConfig()

	pipeline := gpu.NewOptimizationPipelineSimple(
		nil, nil, nil, nil, config, logger,
	)

	ctx := context.Background()
	err := pipeline.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start pipeline: %v", err)
	}
	defer pipeline.Stop()

	// Test model optimization
	model := &testModel{name: "test_model"}
	context := gpu.ModelContext{
		BatchSize:       32,
		ModelComplexity: 2.0,
		InputShape:      []int64{1, 3, 224, 224},
		Metadata:        make(map[string]interface{}),
	}

	result, err := pipeline.OptimizeModel(model, context)
	if err != nil {
		t.Fatalf("Failed to optimize model: %v", err)
	}

	if result == nil {
		t.Fatal("Expected optimization result, got nil")
	}

	// Validate result structure
	if result.ID == "" {
		t.Error("Expected result to have an ID")
	}

	if result.OriginalModel == nil {
		t.Error("Expected result to have original model")
	}

	if result.OptimizedModel == nil {
		t.Error("Expected result to have optimized model")
	}

	if result.BaselineMetrics == nil {
		t.Error("Expected result to have baseline metrics")
	}

	if result.OptimizedMetrics == nil {
		t.Error("Expected result to have optimized metrics")
	}

	if result.OptimizationReport == nil {
		t.Error("Expected result to have optimization report")
	}

	// Validate optimization improvements
	report := result.OptimizationReport
	if report.SpeedupAchieved <= 1.0 {
		t.Errorf("Expected speedup > 1.0, got %.2f", report.SpeedupAchieved)
	}

	if report.MemoryReduction <= 0 {
		t.Errorf("Expected memory reduction > 0, got %.2f", report.MemoryReduction)
	}

	if len(report.AppliedTechniques) == 0 {
		t.Error("Expected at least one optimization technique to be applied")
	}

	if len(report.Recommendations) == 0 {
		t.Error("Expected at least one recommendation")
	}

	if report.OverallScore < 0 || report.OverallScore > 100 {
		t.Errorf("Expected overall score between 0-100, got %.2f", report.OverallScore)
	}
}

func TestOptimizationPipelineSimple_Caching(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultOptPipelineConfig()

	pipeline := gpu.NewOptimizationPipelineSimple(
		nil, nil, nil, nil, config, logger,
	)

	ctx := context.Background()
	err := pipeline.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start pipeline: %v", err)
	}
	defer pipeline.Stop()

	model := &testModel{name: "cached_model"}
	context := gpu.ModelContext{
		BatchSize:       16,
		ModelComplexity: 1.0,
		InputShape:      []int64{1, 3, 128, 128},
		Metadata:        make(map[string]interface{}),
	}

	// First optimization
	start1 := time.Now()
	result1, err := pipeline.OptimizeModel(model, context)
	if err != nil {
		t.Fatalf("Failed to optimize model: %v", err)
	}
	duration1 := time.Since(start1)

	// Second optimization (should use cache)
	start2 := time.Now()
	result2, err := pipeline.OptimizeModel(model, context)
	if err != nil {
		t.Fatalf("Failed to optimize model (cached): %v", err)
	}
	duration2 := time.Since(start2)

	// Cached result should be much faster
	if duration2 >= duration1 {
		t.Logf("Warning: Cached optimization took %v, original took %v", duration2, duration1)
	}

	// Results should be identical
	if result1.ID != result2.ID {
		t.Error("Expected cached result to have same ID")
	}

	if result1.Version != result2.Version {
		t.Error("Expected cached result to have same version")
	}
}

func TestOptimizationPipelineSimple_Stats(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultOptPipelineConfig()

	pipeline := gpu.NewOptimizationPipelineSimple(
		nil, nil, nil, nil, config, logger,
	)

	ctx := context.Background()
	err := pipeline.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start pipeline: %v", err)
	}
	defer pipeline.Stop()

	// Get initial stats
	stats := pipeline.GetStats()
	if !stats.IsRunning {
		t.Error("Expected stats to show pipeline is running")
	}

	if stats.TotalOptimizations != 0 {
		t.Errorf("Expected 0 initial optimizations, got %d", stats.TotalOptimizations)
	}

	// Perform optimization
	model := &testModel{name: "stats_test"}
	context := gpu.ModelContext{
		BatchSize:       8,
		ModelComplexity: 0.5,
		InputShape:      []int64{1, 1, 64, 64},
		Metadata:        make(map[string]interface{}),
	}

	_, err = pipeline.OptimizeModel(model, context)
	if err != nil {
		t.Fatalf("Failed to optimize model: %v", err)
	}

	// Check updated stats
	stats = pipeline.GetStats()
	if stats.TotalOptimizations != 1 {
		t.Errorf("Expected 1 optimization, got %d", stats.TotalOptimizations)
	}

	if stats.SuccessfulOpts != 1 {
		t.Errorf("Expected 1 successful optimization, got %d", stats.SuccessfulOpts)
	}

	if stats.CachedModels != 1 {
		t.Errorf("Expected 1 cached model, got %d", stats.CachedModels)
	}

	if stats.AverageSpeedup <= 1.0 {
		t.Errorf("Expected average speedup > 1.0, got %.2f", stats.AverageSpeedup)
	}
}

func TestOptimizationPipelineSimple_AutoOptimization(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultOptPipelineConfig()
	config.AutoOptimization = true
	config.OptimizationInterval = time.Millisecond * 100 // Very short for testing

	pipeline := gpu.NewOptimizationPipelineSimple(
		nil, nil, nil, nil, config, logger,
	)

	ctx := context.Background()
	err := pipeline.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start pipeline: %v", err)
	}

	// Let auto-optimization run for a short time
	time.Sleep(time.Millisecond * 250)

	err = pipeline.Stop()
	if err != nil {
		t.Fatalf("Failed to stop pipeline: %v", err)
	}

	// Test passes if no panics or deadlocks occur
}

func TestOptimizationPipelineSimple_ConfigVariations(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	testCases := []struct {
		name   string
		config gpu.OptPipelineConfig
	}{
		{
			name: "PruningOnly",
			config: gpu.OptPipelineConfig{
				Enabled:            true,
				EnablePruning:      true,
				EnableFusion:       false,
				EnableQuantization: false,
				EnableMemoryOpt:    false,
			},
		},
		{
			name: "FusionOnly",
			config: gpu.OptPipelineConfig{
				Enabled:            true,
				EnablePruning:      false,
				EnableFusion:       true,
				EnableQuantization: false,
				EnableMemoryOpt:    false,
			},
		},
		{
			name: "QuantizationOnly",
			config: gpu.OptPipelineConfig{
				Enabled:            true,
				EnablePruning:      false,
				EnableFusion:       false,
				EnableQuantization: true,
				EnableMemoryOpt:    false,
			},
		},
		{
			name: "AllDisabled",
			config: gpu.OptPipelineConfig{
				Enabled:            true,
				EnablePruning:      false,
				EnableFusion:       false,
				EnableQuantization: false,
				EnableMemoryOpt:    false,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			pipeline := gpu.NewOptimizationPipelineSimple(
				nil, nil, nil, nil, tc.config, logger,
			)

			ctx := context.Background()
			err := pipeline.Start(ctx)
			if err != nil {
				t.Fatalf("Failed to start pipeline: %v", err)
			}
			defer pipeline.Stop()

			model := &testModel{name: tc.name}
			context := gpu.ModelContext{
				BatchSize:       4,
				ModelComplexity: 1.0,
				InputShape:      []int64{1, 1, 32, 32},
				Metadata:        make(map[string]interface{}),
			}

			result, err := pipeline.OptimizeModel(model, context)
			if err != nil {
				t.Fatalf("Failed to optimize model: %v", err)
			}

			// Validate that appropriate techniques were applied
			report := result.OptimizationReport
			if tc.name == "AllDisabled" {
				if len(report.AppliedTechniques) != 0 {
					t.Errorf("Expected no techniques for %s, got %v", tc.name, report.AppliedTechniques)
				}
			} else if tc.name == "PruningOnly" {
				// Pruning should work without external engines
				if len(report.AppliedTechniques) == 0 {
					t.Errorf("Expected at least one technique for %s", tc.name)
				}
				expectedTechnique := "pruning"
				found := false
				for _, technique := range report.AppliedTechniques {
					if technique == expectedTechnique {
						found = true
						break
					}
				}
				if !found {
					t.Errorf("Expected %s technique for %s, got %v", expectedTechnique, tc.name, report.AppliedTechniques)
				}
			} else if tc.name == "FusionOnly" || tc.name == "QuantizationOnly" {
				// These require external engines that are nil in tests, so no techniques should be applied
				if len(report.AppliedTechniques) != 0 {
					t.Logf("Note: %s requires external engines (nil in test), got techniques: %v", tc.name, report.AppliedTechniques)
				}
			}
		})
	}
}

func TestDefaultOptPipelineConfig(t *testing.T) {
	config := gpu.DefaultOptPipelineConfig()

	if !config.Enabled {
		t.Error("Expected default config to be enabled")
	}

	if config.AutoOptimization {
		t.Error("Expected default config to have auto-optimization disabled")
	}

	if config.OptimizationInterval != time.Hour {
		t.Errorf("Expected default interval to be 1 hour, got %v", config.OptimizationInterval)
	}

	if !config.EnablePruning {
		t.Error("Expected default config to enable pruning")
	}

	if !config.EnableFusion {
		t.Error("Expected default config to enable fusion")
	}

	if !config.EnableQuantization {
		t.Error("Expected default config to enable quantization")
	}

	if !config.EnableMemoryOpt {
		t.Error("Expected default config to enable memory optimization")
	}

	if config.MinSpeedupThreshold <= 1.0 {
		t.Errorf("Expected speedup threshold > 1.0, got %.2f", config.MinSpeedupThreshold)
	}

	if config.MaxAccuracyLoss <= 0 {
		t.Errorf("Expected max accuracy loss > 0, got %.2f", config.MaxAccuracyLoss)
	}
}

// Benchmark tests

func BenchmarkOptimizationPipelineSimple_OptimizeModel(b *testing.B) {
	logger := log.New(ioutil.Discard, "", 0) // Disable logging for benchmarks
	config := gpu.DefaultOptPipelineConfig()

	pipeline := gpu.NewOptimizationPipelineSimple(
		nil, nil, nil, nil, config, logger,
	)

	ctx := context.Background()
	err := pipeline.Start(ctx)
	if err != nil {
		b.Fatalf("Failed to start pipeline: %v", err)
	}
	defer pipeline.Stop()

	model := &testModel{name: "benchmark_model"}
	context := gpu.ModelContext{
		BatchSize:       32,
		ModelComplexity: 1.0,
		InputShape:      []int64{1, 3, 224, 224},
		Metadata:        make(map[string]interface{}),
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		// Create unique context for each iteration to avoid caching
		uniqueContext := context
		uniqueContext.BatchSize = 32 + i%10 // Vary batch size slightly

		_, err := pipeline.OptimizeModel(model, uniqueContext)
		if err != nil {
			b.Fatalf("Failed to optimize model: %v", err)
		}
	}
}

func BenchmarkOptimizationPipelineSimple_CachedOptimization(b *testing.B) {
	logger := log.New(ioutil.Discard, "", 0)
	config := gpu.DefaultOptPipelineConfig()

	pipeline := gpu.NewOptimizationPipelineSimple(
		nil, nil, nil, nil, config, logger,
	)

	ctx := context.Background()
	err := pipeline.Start(ctx)
	if err != nil {
		b.Fatalf("Failed to start pipeline: %v", err)
	}
	defer pipeline.Stop()

	model := &testModel{name: "cached_benchmark"}
	context := gpu.ModelContext{
		BatchSize:       32,
		ModelComplexity: 1.0,
		InputShape:      []int64{1, 3, 224, 224},
		Metadata:        make(map[string]interface{}),
	}

	// Prime the cache
	_, err = pipeline.OptimizeModel(model, context)
	if err != nil {
		b.Fatalf("Failed to prime cache: %v", err)
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_, err := pipeline.OptimizeModel(model, context)
		if err != nil {
			b.Fatalf("Failed to optimize model (cached): %v", err)
		}
	}
}

// Test helper types

type testModel struct {
	name string
}

func (tm *testModel) String() string {
	return tm.name
}
