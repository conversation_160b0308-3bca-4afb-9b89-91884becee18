package gpu

import (
	"log"
	"neuralmetergo/internal/gpu"
	"os"
	"testing"
)

func TestMemoryAwareBatcher_Creation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultMemoryAwareBatcherConfig(0)

	// Test that we can create a memory-aware batcher
	batcher, err := gpu.NewMemoryAwareBatcher(
		config,
		nil, // batchOptimizer - can be nil for basic test
		nil, // inferenceQueue - can be nil for basic test
		nil, // memoryPool - can be nil for basic test
		nil, // resourceManager - can be nil for basic test
		logger,
	)

	if err != nil {
		t.Fatalf("Failed to create memory-aware batcher: %v", err)
	}

	if batcher == nil {
		t.Fatal("Batcher should not be nil")
	}

	// Test model registration
	modelID := "test-model"
	baseMemory := uint64(100 * 1024 * 1024) // 100MB
	batcher.RegisterModel(modelID, baseMemory, 1.5, 1.2)

	// Test getting statistics
	stats := batcher.GetStatistics()
	if stats == nil {
		t.Error("Statistics should not be nil")
	}

	// Test getting current memory pressure
	pressure := batcher.GetCurrentMemoryPressure()
	t.Logf("Current memory pressure: %v", pressure)
}

func TestMemoryPredictor_Creation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	predictor := gpu.NewMemoryPredictor(0, 100, logger)
	if predictor == nil {
		t.Fatal("Memory predictor should not be nil")
	}

	// Test model registration
	modelID := "test-model"
	baseMemory := uint64(50 * 1024 * 1024) // 50MB
	predictor.RegisterModel(modelID, baseMemory, 1.0, 1.0)

	// Test accuracy before any usage data
	accuracy := predictor.GetPredictionAccuracy()
	t.Logf("Initial prediction accuracy: %f", accuracy)
}

func TestMemoryPressureManager_Creation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultMemoryAwareBatcherConfig(0)

	manager := gpu.NewMemoryPressureManager(config, nil, nil, logger)
	if manager == nil {
		t.Fatal("Memory pressure manager should not be nil")
	}

	// Test getting current level
	level := manager.GetCurrentLevel()
	if level != gpu.PressureLow {
		t.Errorf("Expected initial pressure level to be PressureLow, got %v", level)
	}

	// Test getting recommended batch size
	batchSize := manager.GetRecommendedBatchSize()
	expectedSize := config.MaxBatchSizes[gpu.PressureLow]
	if batchSize != expectedSize {
		t.Errorf("Expected batch size %d, got %d", expectedSize, batchSize)
	}

	// Test getting safety margin
	margin := manager.GetSafetyMargin()
	expectedMargin := config.SafetyMargins[gpu.PressureLow]
	if margin != expectedMargin {
		t.Errorf("Expected safety margin %f, got %f", expectedMargin, margin)
	}
}

func TestMemoryAwareBatcherConfig_Defaults(t *testing.T) {
	config := gpu.DefaultMemoryAwareBatcherConfig(0)

	if config == nil {
		t.Fatal("Default config should not be nil")
	}

	// Verify default values
	if config.DeviceID != 0 {
		t.Errorf("Expected device ID 0, got %d", config.DeviceID)
	}

	if !config.OOMPreventionEnabled {
		t.Error("OOM prevention should be enabled by default")
	}

	if !config.MonitoringEnabled {
		t.Error("Monitoring should be enabled by default")
	}

	// Check that all pressure levels have thresholds
	for level := gpu.PressureLow; level <= gpu.PressureCritical; level++ {
		if _, exists := config.MemoryThresholds[level]; !exists {
			t.Errorf("Missing memory threshold for pressure level %v", level)
		}

		if _, exists := config.SafetyMargins[level]; !exists {
			t.Errorf("Missing safety margin for pressure level %v", level)
		}

		if _, exists := config.MaxBatchSizes[level]; !exists {
			t.Errorf("Missing max batch size for pressure level %v", level)
		}
	}
}
