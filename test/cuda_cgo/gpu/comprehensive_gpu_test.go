//go:build cuda && cuda_cgo
// +build cuda,cuda_cgo

package gpu

import (
	"context"
	"fmt"
	"log"
	"os"
	"runtime"
	"sync"
	"testing"
	"time"

	"neuralmetergo/internal/gpu"
)

// TestGPUComprehensiveValidation performs comprehensive testing of all GPU features
func TestGPUComprehensiveValidation(t *testing.T) {
	// Skip GPU tests in CI environments without GPU hardware
	if os.Getenv("CI") == "true" && os.Getenv("GPU_TESTS_ENABLED") != "true" {
		t.Skip("Skipping GPU tests in CI environment")
	}

	t.Run("GPUDetection", testGPUDetection)
	t.Run("GPUMemoryManagement", testGPUMemoryManagement)
	t.Run("GPUResourceMonitoring", testGPUResourceMonitoring)
	t.Run("GPUConcurrency", testGPUConcurrency)
	t.Run("GPUFallbackMechanisms", testGPUFallbackMechanisms)
	t.Run("GPUCrossPlatformSupport", testGPUCrossPlatformSupport)
}

// testGPUDetection validates GPU hardware detection and enumeration
func testGPUDetection(t *testing.T) {
	manager := gpu.NewManager(gpu.DefaultGPUConfig(), log.Default())

	// Test basic GPU detection
	gpus, err := manager.GetAvailableGPUs()
	if err != nil {
		t.Fatalf("Failed to detect GPUs: %v", err)
	}

	// Should have at least CPU fallback
	if len(gpus) == 0 {
		t.Fatal("No GPUs detected, should at least have CPU fallback")
	}

	// Validate GPU information
	for _, gpuInfo := range gpus {
		t.Logf("Detected GPU: %s (Type: %s, Memory: %d MB)",
			gpuInfo.Name, gpuInfo.Type, gpuInfo.TotalMemory/(1024*1024))

		// Basic validation
		if gpuInfo.Name == "" {
			t.Error("GPU name is empty")
		}
		if gpuInfo.TotalMemory < 0 {
			t.Error("GPU memory is negative")
		}
		if gpuInfo.ID < 0 {
			t.Error("GPU ID is negative")
		}
	}

	// Test GPU selection based on capabilities
	cfg := gpu.DefaultGPUConfig()
	cfg.PreferCUDA = false // Use CPU for tests
	cfg.MinMemoryGB = 1.0

	selectedGPU, err := manager.SelectBestGPU(cfg)
	if err != nil {
		t.Fatalf("Failed to select GPU: %v", err)
	}

	if selectedGPU == nil {
		t.Fatal("No GPU selected")
	}

	t.Logf("Selected GPU: %s", selectedGPU.Name)
}

// testGPUMemoryManagement validates GPU memory allocation and management
func testGPUMemoryManagement(t *testing.T) {
	manager := gpu.NewManager(gpu.DefaultGPUConfig(), log.Default())
	cfg := gpu.DefaultGPUConfig()
	cfg.PreferCUDA = false

	selectedGPU, err := manager.SelectBestGPU(cfg)
	if err != nil {
		t.Fatalf("Failed to select GPU: %v", err)
	}

	// Test memory info validation
	if selectedGPU.TotalMemory <= 0 {
		t.Errorf("Invalid total memory: %d", selectedGPU.TotalMemory)
	}

	// Test memory utilization calculation
	utilization := selectedGPU.MemoryUtilization()
	if utilization < 0 || utilization > 100 {
		t.Errorf("Invalid memory utilization: %.2f%%", utilization)
	}

	t.Logf("Memory utilization: %.2f%%", utilization)
	t.Logf("Total memory: %d MB", selectedGPU.TotalMemory/(1024*1024))
	t.Logf("Free memory: %d MB", selectedGPU.FreeMemory/(1024*1024))
}

// testGPUResourceMonitoring validates GPU resource monitoring functionality
func testGPUResourceMonitoring(t *testing.T) {
	manager := gpu.NewManager(gpu.DefaultGPUConfig(), log.Default())

	// Check if we have any GPUs available
	gpus, err := manager.GetAvailableGPUs()
	if err != nil {
		t.Fatalf("Failed to get available GPUs: %v", err)
	}
	if len(gpus) == 0 {
		t.Skip("No GPUs available for monitoring test")
	}

	// Create resource monitor
	monitorConfig := gpu.ResourceMonitoringConfig{
		Interval:       100 * time.Millisecond,
		Enabled:        true,
		LogEvents:      false, // Reduce noise in tests
		MaxHistorySize: 10,
		Thresholds: gpu.ResourceThresholds{
			WarningMemoryUsage:     80.0,
			CriticalMemoryUsage:    90.0,
			WarningGPUUtilization:  80.0,
			CriticalGPUUtilization: 95.0,
		},
	}

	monitor := gpu.NewResourceMonitor(manager, monitorConfig)

	// Start monitoring
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	err = monitor.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start monitoring: %v", err)
	}

	// Let monitoring run for a short time
	time.Sleep(500 * time.Millisecond)

	// Check monitoring metrics
	metrics, err := monitor.GetCurrentMetrics()
	if err != nil {
		t.Fatalf("Failed to get metrics: %v", err)
	}

	if metrics == nil {
		t.Fatal("No metrics collected")
	}

	t.Logf("GPU Metrics - Device: %d, Utilization: %.2f%%, Memory: %.2f%%",
		metrics.DeviceID, metrics.GPUUtilization, metrics.MemoryUtilization)

	// Test that metrics are reasonable
	if metrics.GPUUtilization < 0 || metrics.GPUUtilization > 100 {
		t.Errorf("Invalid utilization value: %.2f", metrics.GPUUtilization)
	}
	if metrics.MemoryUtilization < 0 || metrics.MemoryUtilization > 100 {
		t.Errorf("Invalid memory usage value: %.2f", metrics.MemoryUtilization)
	}

	// Test monitoring stats
	stats := monitor.GetMonitoringStats()
	if !stats.Running {
		t.Error("Monitor should be running")
	}
	if stats.UpdateCount == 0 {
		t.Error("Should have at least one update")
	}

	// Stop monitoring
	err = monitor.Stop()
	if err != nil {
		t.Errorf("Failed to stop monitoring: %v", err)
	}

	// Verify monitoring stopped
	time.Sleep(200 * time.Millisecond)
	if monitor.IsRunning() {
		t.Error("Monitor should have stopped")
	}
}

// testGPUConcurrency validates GPU operations under concurrent access
func testGPUConcurrency(t *testing.T) {
	manager := gpu.NewManager(gpu.DefaultGPUConfig(), log.Default())
	cfg := gpu.DefaultGPUConfig()
	cfg.PreferCUDA = false

	selectedGPU, err := manager.SelectBestGPU(cfg)
	if err != nil {
		t.Fatalf("Failed to select GPU: %v", err)
	}

	const numGoroutines = 10
	const operationsPerGoroutine = 100

	var wg sync.WaitGroup
	errors := make(chan error, numGoroutines)

	// Run concurrent GPU operations
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for j := 0; j < operationsPerGoroutine; j++ {
				// Simulate GPU work by checking availability
				if !manager.IsGPUAvailable(selectedGPU.ID) {
					errors <- fmt.Errorf("worker %d: GPU became unavailable", workerID)
					return
				}

				// Small delay to simulate work
				time.Sleep(time.Millisecond)
			}
		}(i)
	}

	// Wait for all goroutines to complete
	wg.Wait()
	close(errors)

	// Check for errors
	for err := range errors {
		t.Errorf("Concurrent operation error: %v", err)
	}
}

// testGPUFallbackMechanisms validates graceful fallback to CPU-only mode
func testGPUFallbackMechanisms(t *testing.T) {
	// Test fallback when GPU detection fails
	manager := gpu.NewManager(gpu.DefaultGPUConfig(), log.Default())

	// Test with impossible GPU requirements to force fallback
	cfg := gpu.DefaultGPUConfig()
	cfg.PreferCUDA = true
	cfg.MinMemoryGB = 999.0                                               // Impossibly high memory requirement
	cfg.MinComputeCapability = gpu.ComputeCapability{Major: 99, Minor: 0} // Impossibly high compute capability

	selectedGPU, err := manager.SelectBestGPU(cfg)
	if err != nil {
		// Check if we can get available GPUs for fallback
		gpus, err2 := manager.GetAvailableGPUs()
		if err2 != nil {
			t.Fatalf("Failed to get available GPUs: %v", err2)
		}

		// Find CPU fallback
		var cpuGPU *gpu.GPUInfo
		for _, gpuInfo := range gpus {
			if gpuInfo.Type == gpu.GPUTypeCPU {
				cpuGPU = gpuInfo
				break
			}
		}

		if cpuGPU == nil {
			t.Skip("No CPU fallback available for fallback test")
		}

		selectedGPU = cpuGPU
		t.Logf("Used CPU fallback due to impossible requirements: %s", selectedGPU.Name)
	} else {
		// Should have fallen back to CPU or found compatible GPU
		t.Logf("Selected GPU with high requirements: %s (Type: %s)", selectedGPU.Name, selectedGPU.Type)
	}

	if selectedGPU == nil {
		t.Fatal("Should have selected some GPU (including CPU fallback)")
	}

	// Test that fallback still provides basic functionality
	if selectedGPU.ID < 0 {
		t.Error("Fallback GPU should have valid device ID")
	}

	if !manager.IsGPUAvailable(selectedGPU.ID) {
		t.Error("Fallback GPU should be available")
	}

	t.Logf("Fallback GPU functionality verified: %s", selectedGPU.Name)
}

// testGPUCrossPlatformSupport validates platform-specific GPU features
func testGPUCrossPlatformSupport(t *testing.T) {
	manager := gpu.NewManager(gpu.DefaultGPUConfig(), log.Default())

	// Test platform detection
	platform := runtime.GOOS
	t.Logf("Testing on platform: %s", platform)

	gpus, err := manager.GetAvailableGPUs()
	if err != nil {
		t.Fatalf("Failed to get GPUs on platform %s: %v", platform, err)
	}

	// Validate platform-specific GPU types
	var foundPlatformSpecific bool
	for _, gpuInfo := range gpus {
		switch platform {
		case "darwin": // macOS
			if gpuInfo.Type == gpu.GPUTypeMetal {
				foundPlatformSpecific = true
				t.Logf("Found Metal GPU on macOS: %s", gpuInfo.Name)
			}
		case "linux":
			if gpuInfo.Type == gpu.GPUTypeCUDA {
				foundPlatformSpecific = true
				t.Logf("Found CUDA GPU on Linux: %s", gpuInfo.Name)
			}
		case "windows":
			if gpuInfo.Type == gpu.GPUTypeCUDA || gpuInfo.Type == gpu.GPUTypeOpenCL {
				foundPlatformSpecific = true
				t.Logf("Found GPU on Windows: %s (Type: %s)", gpuInfo.Name, gpuInfo.Type)
			}
		}
	}

	// Always should have CPU fallback
	var foundCPU bool
	for _, gpuInfo := range gpus {
		if gpuInfo.Type == gpu.GPUTypeCPU {
			foundCPU = true
			t.Logf("Found CPU fallback: %s", gpuInfo.Name)
			break
		}
	}

	if !foundCPU {
		t.Error("CPU fallback not found - should always be available")
	}

	t.Logf("Platform-specific GPU support: %v", foundPlatformSpecific)

	// Test supported GPU types
	supportedTypes := manager.GetGPUTypes()
	t.Logf("Supported GPU types on %s: %v", platform, supportedTypes)

	if len(supportedTypes) == 0 {
		t.Error("Should support at least CPU fallback")
	}
}

// BenchmarkGPUDetectionCGO benchmarks GPU detection performance
func BenchmarkGPUDetectionCGO(b *testing.B) {
	if os.Getenv("CI") == "true" && os.Getenv("GPU_TESTS_ENABLED") != "true" {
		b.Skip("Skipping GPU benchmarks in CI environment")
	}

	manager := gpu.NewManager(gpu.DefaultGPUConfig(), log.Default())

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := manager.GetAvailableGPUs()
		if err != nil {
			b.Fatalf("GPU detection failed: %v", err)
		}
	}
}

// BenchmarkGPUSelection benchmarks GPU selection performance
func BenchmarkGPUSelection(b *testing.B) {
	if os.Getenv("CI") == "true" && os.Getenv("GPU_TESTS_ENABLED") != "true" {
		b.Skip("Skipping GPU benchmarks in CI environment")
	}

	manager := gpu.NewManager(gpu.DefaultGPUConfig(), log.Default())
	cfg := gpu.DefaultGPUConfig()
	cfg.PreferCUDA = false
	cfg.MinMemoryGB = 0.5

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := manager.SelectBestGPU(cfg)
		if err != nil {
			b.Fatalf("GPU selection failed: %v", err)
		}
	}
}

// BenchmarkGPUMonitoring benchmarks GPU monitoring performance
func BenchmarkGPUMonitoring(b *testing.B) {
	if os.Getenv("CI") == "true" && os.Getenv("GPU_TESTS_ENABLED") != "true" {
		b.Skip("Skipping GPU benchmarks in CI environment")
	}

	manager := gpu.NewManager(gpu.DefaultGPUConfig(), log.Default())
	cfg := gpu.DefaultGPUConfig()
	cfg.PreferCUDA = false

	selectedGPU, err := manager.SelectBestGPU(cfg)
	if err != nil {
		b.Fatalf("Failed to select GPU: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := manager.GetCurrentMetrics(selectedGPU.ID)
		if err != nil {
			b.Fatalf("GPU metrics failed: %v", err)
		}
	}
}

// TestGPUStressTest performs stress testing of GPU operations
func TestGPUStressTest(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping stress test in short mode")
	}

	if os.Getenv("CI") == "true" && os.Getenv("GPU_TESTS_ENABLED") != "true" {
		t.Skip("Skipping GPU stress tests in CI environment")
	}

	manager := gpu.NewManager(gpu.DefaultGPUConfig(), log.Default())
	cfg := gpu.DefaultGPUConfig()
	cfg.PreferCUDA = false

	selectedGPU, err := manager.SelectBestGPU(cfg)
	if err != nil {
		t.Fatalf("Failed to select GPU: %v", err)
	}

	// Run stress test for 10 seconds
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	const numWorkers = 50
	var wg sync.WaitGroup
	errors := make(chan error, numWorkers)

	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for {
				select {
				case <-ctx.Done():
					return
				default:
					// Simulate GPU operations
					if !manager.IsGPUAvailable(selectedGPU.ID) {
						errors <- fmt.Errorf("worker %d: GPU unavailable", workerID)
						return
					}

					// Try to get metrics
					_, err := manager.GetCurrentMetrics(selectedGPU.ID)
					if err != nil {
						errors <- fmt.Errorf("worker %d: metrics error: %v", workerID, err)
						return
					}

					// Small delay to prevent overwhelming the system
					time.Sleep(10 * time.Millisecond)
				}
			}
		}(i)
	}

	wg.Wait()
	close(errors)

	// Check for errors
	errorCount := 0
	for err := range errors {
		t.Logf("Stress test error: %v", err)
		errorCount++
	}

	if errorCount > 0 {
		t.Errorf("Stress test completed with %d errors", errorCount)
	} else {
		t.Log("Stress test completed successfully")
	}
}

// TestGPUConfigurationValidation validates GPU configuration handling
func TestGPUConfigurationValidation(t *testing.T) {
	// Test valid configurations
	validConfigs := []gpu.GPUConfig{
		{
			Enabled:              true,
			PreferCUDA:           true,
			MinMemoryGB:          0.5,
			MinComputeCapability: gpu.ComputeCapability{Major: 3, Minor: 0},
		},
		{
			Enabled:              true,
			PreferCUDA:           false,
			MinMemoryGB:          1.0,
			MinComputeCapability: gpu.ComputeCapability{Major: 6, Minor: 0},
		},
	}

	manager := gpu.NewManager(gpu.DefaultGPUConfig(), log.Default())

	for i, cfg := range validConfigs {
		t.Run(fmt.Sprintf("ValidConfig%d", i), func(t *testing.T) {
			selectedGPU, err := manager.SelectBestGPU(cfg)
			if err != nil {
				t.Fatalf("Valid config %d failed: %v", i, err)
			}
			if selectedGPU == nil {
				t.Fatalf("Valid config %d returned nil GPU", i)
			}
			t.Logf("Config %d selected: %s", i, selectedGPU.Name)
		})
	}

	// Test invalid configurations (should fallback gracefully)
	invalidConfigs := []gpu.GPUConfig{
		{
			Enabled:              true,
			MinMemoryGB:          999.0,                                      // Impossibly high
			MinComputeCapability: gpu.ComputeCapability{Major: 99, Minor: 0}, // Impossibly high
		},
		{
			Enabled:              true,
			MaxMemoryUtilization: -10, // Invalid memory utilization percentage
		},
	}

	for i, cfg := range invalidConfigs {
		t.Run(fmt.Sprintf("InvalidConfig%d", i), func(t *testing.T) {
			// First try validation
			err := cfg.Validate()
			if err != nil {
				t.Logf("Config %d correctly failed validation: %v", i, err)
				return
			}

			// If validation passes, selection should either work or fallback gracefully
			selectedGPU, err := manager.SelectBestGPU(cfg)
			if err != nil {
				// This is expected for impossible requirements
				t.Logf("Config %d selection failed as expected: %v", i, err)

				// Try to find a fallback
				gpus, err2 := manager.GetAvailableGPUs()
				if err2 == nil && len(gpus) > 0 {
					t.Logf("Config %d can fallback to: %s", i, gpus[len(gpus)-1].Name)
				}
			} else if selectedGPU != nil {
				t.Logf("Config %d unexpectedly found compatible GPU: %s", i, selectedGPU.Name)
			} else {
				t.Errorf("Config %d returned nil GPU without error", i)
			}
		})
	}
}
