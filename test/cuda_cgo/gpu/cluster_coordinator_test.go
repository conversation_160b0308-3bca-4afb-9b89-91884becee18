package gpu

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"neuralmetergo/internal/gpu"
)

func TestNewGPUResourceDiscovery(t *testing.T) {
	config := gpu.DefaultDiscoveryConfig()
	logger := log.Default()
	discovery, err := gpu.NewGPUResourceDiscovery(config, logger)

	assert.NoError(t, err)
	assert.NotNil(t, discovery)
}

func TestDefaultDiscoveryConfig(t *testing.T) {
	config := gpu.DefaultDiscoveryConfig()

	assert.Equal(t, "***************", config.MulticastAddress)
	assert.Equal(t, 19132, config.MulticastPort)
	assert.Equal(t, 19133, config.UnicastPort)
	assert.Equal(t, time.Second*30, config.DiscoveryInterval)
	assert.Equal(t, time.Second*10, config.HeartbeatInterval)
	assert.Equal(t, time.Minute*2, config.NodeTimeout)
	assert.True(t, config.SubnetScanEnabled)
	assert.Contains(t, config.ScanSubnets, "***********/16")
	assert.Contains(t, config.AllowedNodeTypes, "gpu_worker")
	assert.Equal(t, int64(1<<30), config.MinGPUMemory)
}

func TestGPUResourceDiscovery_StartStop(t *testing.T) {
	config := gpu.DefaultDiscoveryConfig()
	// Use different ports for testing to avoid conflicts
	config.MulticastPort = 19142
	config.UnicastPort = 19143

	logger := log.Default()
	discovery, err := gpu.NewGPUResourceDiscovery(config, logger)
	require.NoError(t, err)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	// Test Start
	err = discovery.Start(ctx)
	assert.NoError(t, err)

	// Test Stop
	err = discovery.Stop()
	assert.NoError(t, err)
}

func TestGPUResourceDiscovery_NodeRetrieval(t *testing.T) {
	config := gpu.DefaultDiscoveryConfig()
	config.MulticastPort = 19144
	config.UnicastPort = 19145

	logger := log.Default()
	discovery, err := gpu.NewGPUResourceDiscovery(config, logger)
	require.NoError(t, err)

	// Test getting nodes (should be empty initially)
	nodes := discovery.GetNodes()
	assert.Len(t, nodes, 0)

	// Test getting non-existent node
	_, err = discovery.GetNode("non-existent")
	assert.Error(t, err)

	// Test getting active nodes (should be empty initially)
	activeNodes := discovery.GetActiveNodes()
	assert.Len(t, activeNodes, 0)

	// Test getting available GPUs (should be empty initially)
	gpus := discovery.GetAvailableGPUs()
	assert.Len(t, gpus, 0)
}

func TestGPUResourceDiscovery_Callbacks(t *testing.T) {
	config := gpu.DefaultDiscoveryConfig()
	config.MulticastPort = 19146
	config.UnicastPort = 19147

	logger := log.Default()
	discovery, err := gpu.NewGPUResourceDiscovery(config, logger)
	require.NoError(t, err)

	// Set callbacks
	discovery.SetCallbacks(
		func(node *gpu.ClusterNode) {
			// Node added callback
		},
		func(node *gpu.ClusterNode) {
			// Node updated callback
		},
		func(nodeID string) {
			// Node removed callback
		},
	)

	// Test that callbacks are set (no direct way to verify, but this ensures no panic)
	assert.NotNil(t, discovery)
}

func TestDiscoveryMessage_Serialization(t *testing.T) {
	message := &gpu.DiscoveryMessage{
		Type:      "node_announcement",
		NodeID:    "test-node",
		Timestamp: time.Now(),
		AuthToken: "test-token",
		Data: map[string]interface{}{
			"hostname": "test-host",
			"port":     19133,
		},
	}

	// Test JSON serialization
	data, err := json.Marshal(message)
	assert.NoError(t, err)

	// Test JSON deserialization
	var decoded gpu.DiscoveryMessage
	err = json.Unmarshal(data, &decoded)
	assert.NoError(t, err)
	assert.Equal(t, message.Type, decoded.Type)
	assert.Equal(t, message.NodeID, decoded.NodeID)
	assert.Equal(t, message.AuthToken, decoded.AuthToken)
}

func TestClusterNode_Structure(t *testing.T) {
	node := createMockClusterNode("test-node", "*************")

	// Test node structure
	assert.Equal(t, "test-node", node.ID)
	assert.Equal(t, "*************", node.Address)
	assert.Equal(t, 19133, node.Port)
	assert.Equal(t, gpu.NodeStatusActive, node.Status)
	assert.Len(t, node.GPUDevices, 2)
	assert.NotNil(t, node.SystemInfo)
	assert.NotNil(t, node.WorkloadMetrics)
	assert.NotNil(t, node.NetworkInfo)
}

func TestGPUUtilizationTracking(t *testing.T) {
	mockNode := createMockClusterNode("util-node", "*************")

	// Test initial utilization
	gpu1 := mockNode.GPUDevices[0]
	assert.NotNil(t, gpu1.Utilization)
	assert.Equal(t, 25.5, gpu1.Utilization.ComputePercent)
	assert.Equal(t, 60.0, gpu1.Utilization.MemoryPercent)

	// Test GPU status changes
	gpu1.Status = gpu.GPUStatusBusy
	gpu1.Utilization.ComputePercent = 95.0
	gpu1.Utilization.MemoryPercent = 85.0

	assert.Equal(t, gpu.GPUStatusBusy, gpu1.Status)
	assert.Equal(t, 95.0, gpu1.Utilization.ComputePercent)
}

func TestNodeSystemInfoCollection(t *testing.T) {
	mockNode := createMockClusterNode("system-node", "*************")

	sysInfo := mockNode.SystemInfo
	assert.NotNil(t, sysInfo)
	assert.Equal(t, "linux", sysInfo.OS)
	assert.Equal(t, "x86_64", sysInfo.Architecture)
	assert.Equal(t, 8, sysInfo.CPUCount)
	assert.Equal(t, int64(32<<30), sysInfo.TotalMemory) // 32GB
	assert.Greater(t, sysInfo.AvailableMemory, int64(0))
	assert.GreaterOrEqual(t, sysInfo.LoadAverage, 0.0)
}

func TestWorkloadMetricsTracking(t *testing.T) {
	mockNode := createMockClusterNode("workload-node", "*************")

	metrics := mockNode.WorkloadMetrics
	assert.NotNil(t, metrics)
	assert.Equal(t, 3, metrics.ActiveTasks)
	assert.Equal(t, int64(150), metrics.CompletedTasks)
	assert.Equal(t, int64(5), metrics.FailedTasks)
	assert.Equal(t, 2.5, metrics.AverageTaskTime)
	assert.Equal(t, 10.5, metrics.ThroughputTasksPerSec)
	assert.Equal(t, 8, metrics.QueueLength)
}

func TestNetworkInfoCollection(t *testing.T) {
	mockNode := createMockClusterNode("network-node", "*************")

	netInfo := mockNode.NetworkInfo
	assert.NotNil(t, netInfo)
	assert.Equal(t, 1000.0, netInfo.Bandwidth)
	assert.Equal(t, 1.5, netInfo.Latency)
	assert.Equal(t, 0.1, netInfo.PacketLoss)
	assert.Len(t, netInfo.Interfaces, 2)
	assert.Equal(t, "*************", netInfo.PrivateIP)

	// Test network interface details
	eth0 := netInfo.Interfaces[0]
	assert.Equal(t, "eth0", eth0.Name)
	assert.Equal(t, "*************", eth0.IP)
	assert.True(t, eth0.IsActive)
	assert.Equal(t, int64(1000), eth0.Speed)
}

func TestGPUDeviceStatus(t *testing.T) {
	// Test GPU device status constants
	assert.Equal(t, gpu.GPUDeviceStatus("available"), gpu.GPUStatusAvailable)
	assert.Equal(t, gpu.GPUDeviceStatus("busy"), gpu.GPUStatusBusy)
	assert.Equal(t, gpu.GPUDeviceStatus("maintenance"), gpu.GPUStatusMaintenance)
	assert.Equal(t, gpu.GPUDeviceStatus("error"), gpu.GPUStatusError)
	assert.Equal(t, gpu.GPUDeviceStatus("offline"), gpu.GPUStatusOffline)
}

func TestNodeStatus(t *testing.T) {
	// Test node status constants
	assert.Equal(t, gpu.NodeStatus("active"), gpu.NodeStatusActive)
	assert.Equal(t, gpu.NodeStatus("inactive"), gpu.NodeStatusInactive)
	assert.Equal(t, gpu.NodeStatus("unavailable"), gpu.NodeStatusUnavailable)
	assert.Equal(t, gpu.NodeStatus("maintenance"), gpu.NodeStatusMaintenance)
	assert.Equal(t, gpu.NodeStatus("failed"), gpu.NodeStatusFailed)
}

func TestDiscoveryConfig_Validation(t *testing.T) {
	config := gpu.DefaultDiscoveryConfig()

	// Test that default config has reasonable values
	assert.Greater(t, config.DiscoveryInterval, time.Duration(0))
	assert.Greater(t, config.HeartbeatInterval, time.Duration(0))
	assert.Greater(t, config.NodeTimeout, time.Duration(0))
	assert.Greater(t, config.MulticastPort, 0)
	assert.Greater(t, config.UnicastPort, 0)
	assert.NotEmpty(t, config.MulticastAddress)
	assert.NotEmpty(t, config.ScanSubnets)
	assert.Len(t, config.PortRange, 2)
	assert.Less(t, config.PortRange[0], config.PortRange[1])
}

// Helper function to create mock cluster nodes for testing
func createMockClusterNode(nodeID, address string) *gpu.ClusterNode {
	return &gpu.ClusterNode{
		ID:       nodeID,
		Address:  address,
		Port:     19133,
		Hostname: "test-host-" + nodeID,
		LastSeen: time.Now(),
		Status:   gpu.NodeStatusActive,
		GPUDevices: []*gpu.ClusterGPUDevice{
			{
				DeviceID:        "gpu-0",
				NodeID:          nodeID,
				Name:            "NVIDIA GeForce RTX 4090",
				Type:            gpu.GPUTypeCUDA,
				Vendor:          "NVIDIA",
				Architecture:    "Ada Lovelace",
				TotalMemory:     24 << 30, // 24GB
				AvailableMemory: 20 << 30, // 20GB available
				ComputeCapability: gpu.ComputeCapability{
					Major: 8,
					Minor: 9,
				},
				CoreCount:       16384,
				ClockRate:       2520,
				MemoryBandwidth: 1008.0,
				Status:          gpu.GPUStatusAvailable,
				Utilization: &gpu.GPUUtilization{
					ComputePercent: 25.5,
					MemoryPercent:  60.0,
				},
				Temperature: 65,
				PowerUsage:  320.5,
				LastUpdate:  time.Now(),
				Features:    []string{"cuda", "tensor", "ray_tracing"},
			},
			{
				DeviceID:        "gpu-1",
				NodeID:          nodeID,
				Name:            "NVIDIA GeForce RTX 4080",
				Type:            gpu.GPUTypeCUDA,
				Vendor:          "NVIDIA",
				Architecture:    "Ada Lovelace",
				TotalMemory:     16 << 30, // 16GB
				AvailableMemory: 14 << 30, // 14GB available
				ComputeCapability: gpu.ComputeCapability{
					Major: 8,
					Minor: 9,
				},
				CoreCount:       9728,
				ClockRate:       2505,
				MemoryBandwidth: 717.0,
				Status:          gpu.GPUStatusAvailable,
				Utilization: &gpu.GPUUtilization{
					ComputePercent: 15.2,
					MemoryPercent:  45.0,
				},
				Temperature: 58,
				PowerUsage:  280.0,
				LastUpdate:  time.Now(),
				Features:    []string{"cuda", "tensor", "ray_tracing"},
			},
		},
		SystemInfo: &gpu.NodeSystemInfo{
			OS:              "linux",
			Architecture:    "x86_64",
			CPUCount:        8,
			TotalMemory:     32 << 30, // 32GB
			AvailableMemory: 28 << 30, // 28GB available
			LoadAverage:     1.5,
			Uptime:          86400, // 1 day
			KernelVersion:   "5.15.0",
			DriverVersions: map[string]string{
				"nvidia": "535.104.05",
				"cuda":   "12.2",
			},
		},
		Capabilities: []string{"cuda", "compute", "tensor", "distributed"},
		WorkloadMetrics: &gpu.NodeWorkloadMetrics{
			ActiveTasks:           3,
			CompletedTasks:        150,
			FailedTasks:           5,
			AverageTaskTime:       2.5,
			ThroughputTasksPerSec: 10.5,
			QueueLength:           8,
			LastTaskTime:          time.Now().Add(-time.Minute * 5),
		},
		NetworkInfo: &gpu.NodeNetworkInfo{
			Bandwidth:  1000.0, // 1Gbps
			Latency:    1.5,    // 1.5ms
			PacketLoss: 0.1,    // 0.1%
			Interfaces: []gpu.NetworkInterface{
				{
					Name:     "eth0",
					IP:       "*************",
					MAC:      "00:11:22:33:44:55",
					MTU:      1500,
					Speed:    1000,
					IsActive: true,
				},
				{
					Name:     "lo",
					IP:       "127.0.0.1",
					MAC:      "00:00:00:00:00:00",
					MTU:      65536,
					Speed:    0,
					IsActive: true,
				},
			},
			PublicIP:  "*************",
			PrivateIP: "*************",
		},
	}
}

// Benchmark tests for performance validation
func BenchmarkNodeDiscovery(b *testing.B) {
	config := gpu.DefaultDiscoveryConfig()
	config.MulticastPort = 19160
	config.UnicastPort = 19161

	logger := log.Default()
	_, err := gpu.NewGPUResourceDiscovery(config, logger)
	require.NoError(b, err)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Create mock nodes for benchmarking
		_ = createMockClusterNode(fmt.Sprintf("bench-node-%d", i), fmt.Sprintf("192.168.1.%d", 100+i%50))
	}
}

func BenchmarkNodeRetrieval(b *testing.B) {
	config := gpu.DefaultDiscoveryConfig()
	logger := log.Default()
	discovery, err := gpu.NewGPUResourceDiscovery(config, logger)
	require.NoError(b, err)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		discovery.GetActiveNodes()
	}
}

func BenchmarkGPURetrieval(b *testing.B) {
	config := gpu.DefaultDiscoveryConfig()
	logger := log.Default()
	discovery, err := gpu.NewGPUResourceDiscovery(config, logger)
	require.NoError(b, err)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		discovery.GetAvailableGPUs()
	}
}
