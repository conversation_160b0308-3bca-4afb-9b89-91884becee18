package gpu_test

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"neuralmetergo/internal/gpu"
)

// mockCheckpointStorage implements CheckpointStorage for testing
type mockCheckpointStorage struct {
	checkpoints map[string]map[int]*gpu.Checkpoint // taskID -> version -> checkpoint
}

func newMockCheckpointStorage() *mockCheckpointStorage {
	return &mockCheckpointStorage{
		checkpoints: make(map[string]map[int]*gpu.Checkpoint),
	}
}

func (mcs *mockCheckpointStorage) StoreCheckpoint(checkpoint *gpu.Checkpoint) error {
	if mcs.checkpoints[checkpoint.TaskID] == nil {
		mcs.checkpoints[checkpoint.TaskID] = make(map[int]*gpu.Checkpoint)
	}
	mcs.checkpoints[checkpoint.TaskID][checkpoint.Version] = checkpoint
	return nil
}

func (mcs *mockCheckpointStorage) LoadCheckpoint(taskID string, version int) (*gpu.Checkpoint, error) {
	if taskCheckpoints, exists := mcs.checkpoints[taskID]; exists {
		if checkpoint, exists := taskCheckpoints[version]; exists {
			return checkpoint, nil
		}
	}
	return nil, fmt.Errorf("checkpoint v%d for task %s not found", version, taskID)
}

func (mcs *mockCheckpointStorage) ListCheckpoints(taskID string) ([]*gpu.CheckpointMetadata, error) {
	var metadata []*gpu.CheckpointMetadata
	if taskCheckpoints, exists := mcs.checkpoints[taskID]; exists {
		for _, checkpoint := range taskCheckpoints {
			metadata = append(metadata, &gpu.CheckpointMetadata{
				TaskID:      checkpoint.TaskID,
				Version:     checkpoint.Version,
				Timestamp:   checkpoint.Timestamp,
				Size:        checkpoint.OriginalSize,
				Hash:        checkpoint.Hash,
				Incremental: checkpoint.Incremental,
			})
		}
	}
	return metadata, nil
}

func (mcs *mockCheckpointStorage) DeleteCheckpoint(taskID string, version int) error {
	if taskCheckpoints, exists := mcs.checkpoints[taskID]; exists {
		delete(taskCheckpoints, version)
	}
	return nil
}

func (mcs *mockCheckpointStorage) GetLatestCheckpoint(taskID string) (*gpu.Checkpoint, error) {
	if taskCheckpoints, exists := mcs.checkpoints[taskID]; exists {
		var latest *gpu.Checkpoint
		for _, checkpoint := range taskCheckpoints {
			if latest == nil || checkpoint.Version > latest.Version {
				latest = checkpoint
			}
		}
		if latest != nil {
			return latest, nil
		}
	}
	return nil, fmt.Errorf("no checkpoints found for task %s", taskID)
}

// Define a mock error for checkpoint not found
// var ErrCheckpointNotFound = &gpu.CheckpointError{Message: "checkpoint not found"}

func TestDefaultIncrementalCheckpointConfig(t *testing.T) {
	config := gpu.DefaultIncrementalCheckpointConfig()

	assert.True(t, config.Enabled)
	assert.Equal(t, int64(50*1024*1024), config.MaxDeltaSize)
	assert.Equal(t, 6, config.DeltaCompressionLevel)
	assert.Equal(t, 30*time.Minute, config.FullCheckpointInterval)
	assert.Equal(t, 10, config.MaxIncrementalChain)
	assert.True(t, config.AsyncDeltaCalculation)
	assert.Equal(t, 4, config.ParallelDeltaWorkers)
	assert.Equal(t, int64(10*1024*1024), config.MemoryBufferSize)
	assert.Equal(t, 0.001, config.TensorDiffThreshold)
	assert.True(t, config.SkipUnchangedTensors)
	assert.True(t, config.UseBlockLevelDelta)
	assert.Equal(t, 4096, config.BlockSize)
}

func TestNewIncrementalCheckpointManager(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)
	storage := newMockCheckpointStorage()
	incrementalConfig := gpu.DefaultIncrementalCheckpointConfig()

	manager := gpu.NewIncrementalCheckpointManager(serializer, storage, incrementalConfig, logger)

	assert.NotNil(t, manager)
}

func TestCreateFirstIncrementalCheckpoint(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)
	storage := newMockCheckpointStorage()
	incrementalConfig := gpu.DefaultIncrementalCheckpointConfig()

	manager := gpu.NewIncrementalCheckpointManager(serializer, storage, incrementalConfig, logger)

	// Create first checkpoint (should be full checkpoint)
	taskID := "test_task_1"
	checkpoint, err := manager.CreateIncrementalCheckpoint(taskID, 0, "test")

	require.NoError(t, err)
	assert.NotNil(t, checkpoint)
	assert.Equal(t, taskID, checkpoint.TaskID)
	assert.False(t, checkpoint.IsIncremental) // First checkpoint should be full
	assert.Equal(t, 1, checkpoint.Version)
	assert.Equal(t, 0, checkpoint.ChainLength)
	assert.NotEmpty(t, checkpoint.Hash)
	assert.Greater(t, checkpoint.DeltaSize, int64(0))

	// Verify operation completed
	op, exists := manager.GetOperationStatus(taskID)
	require.True(t, exists)
	assert.Equal(t, gpu.IncrementalPhaseCompleted, op.Phase)
	assert.Equal(t, 1.0, op.Progress)
}

func TestCreateIncrementalCheckpointWithTensorChanges(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)
	storage := newMockCheckpointStorage()
	incrementalConfig := gpu.DefaultIncrementalCheckpointConfig()

	manager := gpu.NewIncrementalCheckpointManager(serializer, storage, incrementalConfig, logger)

	taskID := "test_task_2"

	// Create initial full checkpoint
	checkpoint1, err := manager.CreateIncrementalCheckpoint(taskID, 0, "test")
	require.NoError(t, err)
	assert.False(t, checkpoint1.IsIncremental)

	// Add a tensor to create changes
	shape := gpu.TensorShape{2, 2}
	tensor, err := gpu.NewTensor(shape, gpu.TensorFloat32, gpu.DeviceCPU, 0)
	require.NoError(t, err)
	defer tensor.Free()

	err = tensor.Fill(float32(1.0))
	require.NoError(t, err)

	serializer.RegisterTensor("test_tensor", tensor)

	// Create second checkpoint (should be incremental)
	checkpoint2, err := manager.CreateIncrementalCheckpoint(taskID, 0, "test")
	require.NoError(t, err)
	assert.True(t, checkpoint2.IsIncremental) // Should be incremental
	assert.Equal(t, 2, checkpoint2.Version)
	assert.Equal(t, 1, checkpoint2.ChainLength)
	assert.Greater(t, len(checkpoint2.Changes), 0) // Should have changes

	// Verify delta tracker state
	tracker, exists := manager.GetDeltaTracker(taskID)
	require.True(t, exists)
	assert.Equal(t, 1, tracker.IncrementalCount)
	assert.Greater(t, len(tracker.ChangeLog), 0)
}

func TestMaxIncrementalChainForcing(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)
	storage := newMockCheckpointStorage()
	incrementalConfig := gpu.DefaultIncrementalCheckpointConfig()
	incrementalConfig.MaxIncrementalChain = 2 // Force full checkpoint after 2 incrementals

	manager := gpu.NewIncrementalCheckpointManager(serializer, storage, incrementalConfig, logger)

	taskID := "test_task_chain"

	// Create initial full checkpoint
	checkpoint1, err := manager.CreateIncrementalCheckpoint(taskID, 0, "test")
	require.NoError(t, err)
	assert.False(t, checkpoint1.IsIncremental)

	// Create tensor for changes
	shape := gpu.TensorShape{1, 1}
	tensor, err := gpu.NewTensor(shape, gpu.TensorFloat32, gpu.DeviceCPU, 0)
	require.NoError(t, err)
	defer tensor.Free()

	// Create incremental checkpoints
	for i := 0; i < 3; i++ {
		// Modify tensor to create changes
		err = tensor.Fill(float32(i + 1))
		require.NoError(t, err)
		serializer.RegisterTensor("changing_tensor", tensor)

		checkpoint, err := manager.CreateIncrementalCheckpoint(taskID, 0, "test")
		require.NoError(t, err)

		if i < 2 {
			// First two should be incremental
			assert.True(t, checkpoint.IsIncremental)
			assert.Equal(t, i+1, checkpoint.ChainLength)
		} else {
			// Third should force a full checkpoint
			assert.False(t, checkpoint.IsIncremental)
			assert.Equal(t, 0, checkpoint.ChainLength)
		}
	}
}

func TestDeltaTrackerReset(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)
	storage := newMockCheckpointStorage()
	incrementalConfig := gpu.DefaultIncrementalCheckpointConfig()

	manager := gpu.NewIncrementalCheckpointManager(serializer, storage, incrementalConfig, logger)

	taskID := "test_task_reset"

	// Create checkpoint to initialize tracker
	_, err := manager.CreateIncrementalCheckpoint(taskID, 0, "test")
	require.NoError(t, err)

	// Verify tracker exists
	_, exists := manager.GetDeltaTracker(taskID)
	assert.True(t, exists)

	// Reset tracker
	manager.ResetDeltaTracker(taskID)

	// Verify tracker is removed
	_, exists = manager.GetDeltaTracker(taskID)
	assert.False(t, exists)
}

func TestOperationStatusTracking(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)
	storage := newMockCheckpointStorage()
	incrementalConfig := gpu.DefaultIncrementalCheckpointConfig()

	manager := gpu.NewIncrementalCheckpointManager(serializer, storage, incrementalConfig, logger)

	taskID := "test_task_status"

	// Create checkpoint
	_, err := manager.CreateIncrementalCheckpoint(taskID, 0, "test")
	require.NoError(t, err)

	// Check operation status
	op, exists := manager.GetOperationStatus(taskID)
	require.True(t, exists)
	assert.Equal(t, taskID, op.TaskID)
	assert.Equal(t, gpu.IncrementalPhaseCompleted, op.Phase)
	assert.Equal(t, 1.0, op.Progress)

	// Clear completed operations
	manager.ClearCompletedOperations()

	// Verify operation is cleared
	_, exists = manager.GetOperationStatus(taskID)
	assert.False(t, exists)
}

func TestDisabledIncrementalCheckpointing(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)
	storage := newMockCheckpointStorage()
	incrementalConfig := gpu.DefaultIncrementalCheckpointConfig()
	incrementalConfig.Enabled = false // Disable incremental checkpointing

	manager := gpu.NewIncrementalCheckpointManager(serializer, storage, incrementalConfig, logger)

	taskID := "test_task_disabled"

	// Attempt to create checkpoint
	checkpoint, err := manager.CreateIncrementalCheckpoint(taskID, 0, "test")

	assert.Error(t, err)
	assert.Nil(t, checkpoint)
	assert.Contains(t, err.Error(), "incremental checkpointing is disabled")
}

func TestBlockLevelDeltaCalculation(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)
	storage := newMockCheckpointStorage()
	incrementalConfig := gpu.DefaultIncrementalCheckpointConfig()
	incrementalConfig.UseBlockLevelDelta = true
	incrementalConfig.BlockSize = 4 // Small block size for testing

	manager := gpu.NewIncrementalCheckpointManager(serializer, storage, incrementalConfig, logger)

	taskID := "test_task_blocks"

	// Create initial checkpoint
	checkpoint1, err := manager.CreateIncrementalCheckpoint(taskID, 0, "test")
	require.NoError(t, err)
	assert.False(t, checkpoint1.IsIncremental) // Verify first checkpoint is full

	// Create and modify tensor
	shape := gpu.TensorShape{4, 1} // 16 bytes for float32
	tensor, err := gpu.NewTensor(shape, gpu.TensorFloat32, gpu.DeviceCPU, 0)
	require.NoError(t, err)
	defer tensor.Free()

	err = tensor.Fill(float32(2.0))
	require.NoError(t, err)
	serializer.RegisterTensor("block_test_tensor", tensor)

	// Create incremental checkpoint
	checkpoint2, err := manager.CreateIncrementalCheckpoint(taskID, 0, "test")
	require.NoError(t, err)
	assert.True(t, checkpoint2.IsIncremental)
	assert.Greater(t, len(checkpoint2.Changes), 0)

	// Verify changes include tensor modification
	found := false
	for _, change := range checkpoint2.Changes {
		if change.Type == gpu.DeltaChangeTensorAdded && change.TensorID == "block_test_tensor" {
			found = true
			break
		}
	}
	assert.True(t, found, "Should find tensor addition in changes")
}

func BenchmarkIncrementalCheckpointCreation(b *testing.B) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)
	storage := newMockCheckpointStorage()
	incrementalConfig := gpu.DefaultIncrementalCheckpointConfig()

	manager := gpu.NewIncrementalCheckpointManager(serializer, storage, incrementalConfig, logger)

	// Create initial tensor
	shape := gpu.TensorShape{100, 100}
	tensor, err := gpu.NewTensor(shape, gpu.TensorFloat32, gpu.DeviceCPU, 0)
	require.NoError(b, err)
	defer tensor.Free()

	err = tensor.Fill(float32(1.0))
	require.NoError(b, err)
	serializer.RegisterTensor("benchmark_tensor", tensor)

	taskID := "benchmark_task"

	// Create initial full checkpoint
	_, err = manager.CreateIncrementalCheckpoint(taskID, 0, "test")
	require.NoError(b, err)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Modify tensor slightly
		err = tensor.Fill(float32(i + 2))
		require.NoError(b, err)
		serializer.RegisterTensor("benchmark_tensor", tensor)

		// Create incremental checkpoint
		_, err := manager.CreateIncrementalCheckpoint(taskID, 0, "test")
		require.NoError(b, err)
	}
}

func TestLargeDeltaForcingFullCheckpoint(t *testing.T) {
	logger := &mockLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)
	storage := newMockCheckpointStorage()
	incrementalConfig := gpu.DefaultIncrementalCheckpointConfig()
	incrementalConfig.MaxDeltaSize = 100 // Very small max delta size

	manager := gpu.NewIncrementalCheckpointManager(serializer, storage, incrementalConfig, logger)

	taskID := "test_task_large_delta"

	// Create initial checkpoint
	checkpoint1, err := manager.CreateIncrementalCheckpoint(taskID, 0, "test")
	require.NoError(t, err)
	assert.False(t, checkpoint1.IsIncremental)

	// Create large tensor to force delta size over limit
	shape := gpu.TensorShape{100, 100} // Large tensor
	tensor, err := gpu.NewTensor(shape, gpu.TensorFloat32, gpu.DeviceCPU, 0)
	require.NoError(t, err)
	defer tensor.Free()

	err = tensor.Fill(float32(1.0))
	require.NoError(t, err)
	serializer.RegisterTensor("large_tensor", tensor)

	// Create second checkpoint - should be forced to full due to large delta
	checkpoint2, err := manager.CreateIncrementalCheckpoint(taskID, 0, "test")
	require.NoError(t, err)
	assert.False(t, checkpoint2.IsIncremental) // Should be forced to full checkpoint
	assert.Equal(t, 0, checkpoint2.ChainLength)
}
