package gpu

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"testing"
	"time"

	"neuralmetergo/internal/gpu"
)

func TestDistributedCheckpointCoordinatorCreation(t *testing.T) {
	// Create mock dependencies
	communicationManager := createMockCommunicationManager()
	checkpointManager := createMockCheckpointManager()
	heartbeatManager := createMockHeartbeatManager()

	config := gpu.DistributedCheckpointConfig{
		NodeID:                    "test-node-1",
		MinQuorumSize:             3,
		ConsensusTimeout:          time.Second * 30,
		BarrierTimeout:            time.Second * 10,
		LeaderElectionTimeout:     time.Second * 5,
		PartitionDetectionEnabled: true,
		AdaptiveFrequency:         true,
		MaxConcurrentCheckpoints:  5,
		ValidationQuorum:          0.67,
	}

	coordinator, err := gpu.NewDistributedCheckpointCoordinator(
		"test-node-1",
		communicationManager,
		checkpointManager,
		heartbeatManager,
		config,
		log.Default(),
	)

	if err != nil {
		t.Fatalf("Failed to create distributed checkpoint coordinator: %v", err)
	}

	if coordinator == nil {
		t.<PERSON>al("Coordinator is nil")
	}

	// Test initial state
	if coordinator.<PERSON><PERSON>eader() {
		t.<PERSON>rror("New coordinator should not be leader initially")
	}

	if coordinator.GetLeaderID() != "" {
		t.Error("New coordinator should not have a leader ID initially")
	}
}

func TestDistributedCheckpointCoordinatorStartStop(t *testing.T) {
	coordinator := createTestCoordinator(t, "test-node-1")

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	// Test start
	err := coordinator.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start coordinator: %v", err)
	}

	// Give it a moment to start up
	time.Sleep(100 * time.Millisecond)

	// Test double start (should fail)
	err = coordinator.Start(ctx)
	if err == nil {
		t.Error("Expected error when starting already running coordinator")
	}

	// Test stop
	err = coordinator.Stop()
	if err != nil {
		t.Fatalf("Failed to stop coordinator: %v", err)
	}

	// Test double stop (should not fail)
	err = coordinator.Stop()
	if err != nil {
		t.Errorf("Unexpected error when stopping already stopped coordinator: %v", err)
	}
}

func TestNonLeaderCheckpointInitiation(t *testing.T) {
	coordinator := createTestCoordinator(t, "test-node-1")

	// Ensure coordinator is not leader (default state)
	participantNodes := []string{"node-1", "node-2", "node-3"}
	metadata := map[string]interface{}{}

	_, err := coordinator.InitiateGlobalCheckpoint(participantNodes, metadata)
	if err == nil {
		t.Error("Expected error when non-leader tries to initiate checkpoint")
	}

	expectedError := "only leader can initiate global checkpoints"
	if !strings.Contains(err.Error(), expectedError) {
		t.Errorf("Expected error containing '%s', got: %s", expectedError, err.Error())
	}
}

func TestActiveCoordinationsManagement(t *testing.T) {
	coordinator := createTestCoordinator(t, "test-node-1")

	// Initially no active coordinations
	active := coordinator.GetActiveCoordinations()
	if len(active) != 0 {
		t.Errorf("Expected 0 active coordinations, got %d", len(active))
	}

	// Test that we can call the methods without errors
	_, exists := coordinator.GetCoordinationStatus("non-existent")
	if exists {
		t.Error("Expected non-existent coordination to not exist")
	}
}

func TestCoordinatorLeadershipMethods(t *testing.T) {
	coordinator := createTestCoordinator(t, "test-node-1")

	// Test initial non-leader state
	if coordinator.IsLeader() {
		t.Error("New coordinator should not be leader initially")
	}

	if coordinator.GetLeaderID() != "" {
		t.Error("New coordinator should not have a leader ID initially")
	}
}

func TestDistributedCheckpointConfigValidation(t *testing.T) {
	tests := []struct {
		name    string
		config  gpu.DistributedCheckpointConfig
		wantErr bool
	}{
		{
			name: "valid config",
			config: gpu.DistributedCheckpointConfig{
				NodeID:                    "test-node",
				MinQuorumSize:             2,
				ConsensusTimeout:          time.Second * 10,
				BarrierTimeout:            time.Second * 5,
				LeaderElectionTimeout:     time.Second * 3,
				PartitionDetectionEnabled: true,
				AdaptiveFrequency:         false,
				MaxConcurrentCheckpoints:  3,
				ValidationQuorum:          0.6,
			},
			wantErr: false,
		},
		{
			name: "empty node ID",
			config: gpu.DistributedCheckpointConfig{
				NodeID:        "",
				MinQuorumSize: 2,
			},
			wantErr: false, // Constructor doesn't validate empty node ID
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := gpu.NewDistributedCheckpointCoordinator(
				tt.config.NodeID,
				createMockCommunicationManager(),
				createMockCheckpointManager(),
				createMockHeartbeatManager(),
				tt.config,
				log.Default(),
			)

			if (err != nil) != tt.wantErr {
				t.Errorf("NewDistributedCheckpointCoordinator() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCoordinationLifecycle(t *testing.T) {
	coordinator := createTestCoordinator(t, "test-node-1")

	// Start coordinator
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	err := coordinator.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start coordinator: %v", err)
	}
	defer coordinator.Stop()

	// Test that coordinator is running and responds to method calls
	if coordinator.IsLeader() {
		t.Log("Coordinator reports as leader")
	} else {
		t.Log("Coordinator reports as follower")
	}

	// Test active coordinations retrieval
	active := coordinator.GetActiveCoordinations()
	if active == nil {
		t.Error("GetActiveCoordinations should not return nil")
	}
}

// Helper functions for testing

func createTestCoordinator(t *testing.T, nodeID string) *gpu.DistributedCheckpointCoordinator {
	config := gpu.DistributedCheckpointConfig{
		NodeID:                    nodeID,
		MinQuorumSize:             2,
		ConsensusTimeout:          time.Second * 5,
		BarrierTimeout:            time.Second * 3,
		LeaderElectionTimeout:     time.Second * 2,
		PartitionDetectionEnabled: false, // Disable for testing
		AdaptiveFrequency:         false,
		MaxConcurrentCheckpoints:  5,
		ValidationQuorum:          0.5,
	}

	coordinator, err := gpu.NewDistributedCheckpointCoordinator(
		nodeID,
		createMockCommunicationManager(),
		createMockCheckpointManager(),
		createMockHeartbeatManager(),
		config,
		log.Default(),
	)

	if err != nil {
		t.Fatalf("Failed to create test coordinator: %v", err)
	}

	return coordinator
}

func createMockCommunicationManager() *gpu.ClusterCommunicationManager {
	config := gpu.DefaultCommunicationConfig()
	config.NodeID = "test-node"
	config.NodeAddr = "localhost"
	manager, _ := gpu.NewClusterCommunicationManager(config, log.Default())
	return manager
}

func createMockCheckpointManager() *gpu.CheckpointManager {
	storage := &MockCheckpointStorage{
		checkpoints: make(map[string]*gpu.Checkpoint),
	}
	config := gpu.CheckpointConfig{
		Enabled:             true,
		Interval:            time.Minute,
		MaxVersions:         5,
		CompressionEnabled:  false,
		IncrementalEnabled:  false,
		ReplicationFactor:   1,
		StoragePath:         "/tmp/test",
		MaxCheckpointSize:   1024 * 1024,
		AsyncCheckpointing:  false,
		VerificationEnabled: false,
	}
	return gpu.NewCheckpointManager(storage, config, log.Default())
}

func createMockHeartbeatManager() *gpu.HeartbeatManager {
	config := gpu.HeartbeatConfig{
		BaseInterval:        time.Second,
		MaxInterval:         time.Second * 10,
		MinInterval:         time.Millisecond * 500,
		AdaptiveEnabled:     false,
		PhiThreshold:        8.0,
		IntervalHistorySize: 10,
		JitterTolerance:     0.1,
		MissedBeatThreshold: 3,
	}
	return gpu.NewHeartbeatManager(config, log.Default())
}

// MockCheckpointStorage implements CheckpointStorage for testing
type MockCheckpointStorage struct {
	checkpoints map[string]*gpu.Checkpoint
	mu          sync.RWMutex
}

func (m *MockCheckpointStorage) StoreCheckpoint(checkpoint *gpu.Checkpoint) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	key := fmt.Sprintf("%s-%d", checkpoint.TaskID, checkpoint.Version)
	m.checkpoints[key] = checkpoint
	return nil
}

func (m *MockCheckpointStorage) LoadCheckpoint(taskID string, version int) (*gpu.Checkpoint, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	key := fmt.Sprintf("%s-%d", taskID, version)
	checkpoint, exists := m.checkpoints[key]
	if !exists {
		return nil, fmt.Errorf("checkpoint not found")
	}
	return checkpoint, nil
}

func (m *MockCheckpointStorage) ListCheckpoints(taskID string) ([]*gpu.CheckpointMetadata, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var metadata []*gpu.CheckpointMetadata
	for key, checkpoint := range m.checkpoints {
		if strings.HasPrefix(key, taskID+"-") {
			metadata = append(metadata, &gpu.CheckpointMetadata{
				TaskID:    checkpoint.TaskID,
				Version:   checkpoint.Version,
				Timestamp: checkpoint.Timestamp,
				NodeID:    checkpoint.NodeID,
				Size:      checkpoint.OriginalSize,
				Hash:      checkpoint.Hash,
			})
		}
	}
	return metadata, nil
}

func (m *MockCheckpointStorage) DeleteCheckpoint(taskID string, version int) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	key := fmt.Sprintf("%s-%d", taskID, version)
	delete(m.checkpoints, key)
	return nil
}

func (m *MockCheckpointStorage) GetLatestCheckpoint(taskID string) (*gpu.Checkpoint, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var latest *gpu.Checkpoint
	latestVersion := -1

	for key, checkpoint := range m.checkpoints {
		if strings.HasPrefix(key, taskID+"-") && checkpoint.Version > latestVersion {
			latest = checkpoint
			latestVersion = checkpoint.Version
		}
	}

	if latest == nil {
		return nil, fmt.Errorf("no checkpoints found for task %s", taskID)
	}

	return latest, nil
}
