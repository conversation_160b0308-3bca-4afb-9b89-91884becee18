package gpu

import (
	"context"
	"log"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"neuralmetergo/internal/gpu"
)

func TestSimpleClusterCommunicator(t *testing.T) {
	config := gpu.DefaultSimpleCommunicationConfig()
	config.Port = 8082 // Use different port to avoid conflicts

	logger := log.Default()

	// Create communicator
	comm := gpu.NewSimpleClusterCommunicator("test-node-simple", config, logger)
	require.NotNil(t, comm)
	assert.Equal(t, "test-node-simple", comm.GetNodeID())
	assert.False(t, comm.IsRunning())

	// Start communicator
	err := comm.Start()
	require.NoError(t, err)
	assert.True(t, comm.IsRunning())

	// Give it a moment to start
	time.Sleep(100 * time.Millisecond)

	// Stop communicator
	err = comm.Stop()
	require.NoError(t, err)
	assert.False(t, comm.IsRunning())
}

func TestSimpleMessageCreation(t *testing.T) {
	// Test task assignment message
	taskData := &gpu.TaskAssignmentData{
		TaskID:            "task-123",
		TargetNodeID:      "node-456",
		Priority:          5,
		MemoryRequirement: 2048 * 1024 * 1024, // 2GB
		ComputeIntensity:  "high",
		DataLocation:      []string{"node-1", "node-2"},
		DataSize:          1024 * 1024 * 1024, // 1GB
		Deadline:          time.Now().Add(time.Hour),
	}

	msg := gpu.CreateTaskAssignmentMessage("task-123", "node-456", taskData)
	assert.NotNil(t, msg)
	assert.Equal(t, gpu.MessageTypeTaskAssignment, msg.Type)
	assert.Equal(t, "node-456", msg.Target)
	assert.Equal(t, 5, msg.Priority)
	assert.Contains(t, msg.ID, "task-assign-task-123")

	// Verify data fields
	assert.Equal(t, "task-123", msg.Data["task_id"])
	assert.Equal(t, "node-456", msg.Data["target_node_id"])
	assert.Equal(t, 5, msg.Data["priority"])
	assert.Equal(t, int64(2048*1024*1024), msg.Data["memory_requirement"])
	assert.Equal(t, "high", msg.Data["compute_intensity"])

	// Test node status message
	statusData := &gpu.NodeStatusData{
		NodeID:           "node-789",
		Status:           "active",
		CPUUsage:         45.5,
		MemoryUsage:      60.2,
		GPUUsage:         []float64{80.0, 75.5},
		NetworkBandwidth: 1000.0,
		ActiveTasks:      3,
		QueueLength:      2,
		LastHeartbeat:    time.Now(),
	}

	statusMsg := gpu.CreateNodeStatusMessage("node-789", statusData)
	assert.NotNil(t, statusMsg)
	assert.Equal(t, gpu.MessageTypeNodeStatus, statusMsg.Type)
	assert.Equal(t, 1, statusMsg.Priority)
	assert.Contains(t, statusMsg.ID, "node-status-node-789")

	// Verify status data fields
	assert.Equal(t, "node-789", statusMsg.Data["node_id"])
	assert.Equal(t, "active", statusMsg.Data["status"])
	assert.Equal(t, 45.5, statusMsg.Data["cpu_usage"])
	assert.Equal(t, 60.2, statusMsg.Data["memory_usage"])
	assert.Equal(t, []float64{80.0, 75.5}, statusMsg.Data["gpu_usage"])

	// Test result message
	resultData := &gpu.ResultData{
		TaskID:        "task-456",
		SourceNodeID:  "node-123",
		Success:       true,
		ResultData:    []byte("computation result"),
		ExecutionTime: 120 * time.Second,
		ErrorMessage:  "",
		Metadata:      map[string]interface{}{"accuracy": 0.95},
	}

	resultMsg := gpu.CreateResultMessage("task-456", "node-123", resultData)
	assert.NotNil(t, resultMsg)
	assert.Equal(t, gpu.MessageTypeResult, resultMsg.Type)
	assert.Equal(t, 2, resultMsg.Priority)
	assert.Contains(t, resultMsg.ID, "result-task-456")

	// Verify result data fields
	assert.Equal(t, "task-456", resultMsg.Data["task_id"])
	assert.Equal(t, "node-123", resultMsg.Data["source_node_id"])
	assert.Equal(t, true, resultMsg.Data["success"])
	assert.Equal(t, []byte("computation result"), resultMsg.Data["result_data"])

	// Test heartbeat message
	heartbeatMsg := gpu.CreateHeartbeatMessage("node-heartbeat")
	assert.NotNil(t, heartbeatMsg)
	assert.Equal(t, gpu.MessageTypeHeartbeat, heartbeatMsg.Type)
	assert.Equal(t, 0, heartbeatMsg.Priority)
	assert.Contains(t, heartbeatMsg.ID, "heartbeat-node-heartbeat")
	assert.Equal(t, "node-heartbeat", heartbeatMsg.Data["node_id"])
}

func TestSimpleCommunicationConfig(t *testing.T) {
	config := gpu.DefaultSimpleCommunicationConfig()

	assert.Equal(t, 8080, config.Port)
	assert.Equal(t, 4*1024*1024, config.MaxMessageSize)
	assert.Equal(t, 10*time.Second, config.ConnectionTimeout)
	assert.Equal(t, 10*time.Second, config.HeartbeatInterval)
	assert.Equal(t, 3, config.RetryAttempts)
	assert.Equal(t, time.Second, config.RetryBackoff)
}

// MockMessageHandler implements MessageHandler for testing
type MockMessageHandler struct {
	ReceivedMessages []*gpu.ClusterMessage
	ShouldError      bool
}

func (m *MockMessageHandler) HandleMessage(ctx context.Context, msg *gpu.ClusterMessage) error {
	m.ReceivedMessages = append(m.ReceivedMessages, msg)
	if m.ShouldError {
		return assert.AnError
	}
	return nil
}

func TestMessageHandlerRegistration(t *testing.T) {
	config := gpu.DefaultSimpleCommunicationConfig()
	config.Port = 8083 // Use different port

	logger := log.Default()
	comm := gpu.NewSimpleClusterCommunicator("test-handler-node", config, logger)

	// Create mock handler
	handler := &MockMessageHandler{}

	// Register handler
	comm.RegisterHandler(gpu.MessageTypeTaskAssignment, handler)

	// Start communicator
	err := comm.Start()
	require.NoError(t, err)
	defer comm.Stop()

	// Verify handler is registered (this is internal state, so we just verify no errors)
	assert.True(t, comm.IsRunning())
	assert.Equal(t, "test-handler-node", comm.GetNodeID())
}
