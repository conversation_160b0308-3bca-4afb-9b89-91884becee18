package gpu

import (
	"context"
	"log"
	"os"
	"path/filepath"
	"testing"
	"time"

	"neuralmetergo/internal/gpu"
)

func TestEnhancedFaultToleranceCreation(t *testing.T) {
	// Create a mock distributor
	distributor := &gpu.ClusterWorkloadDistributor{}

	// Create enhanced fault tolerance manager
	cft, err := gpu.NewClusterFaultTolerance(distributor, log.Default())
	if err != nil {
		t.Fatalf("Failed to create cluster fault tolerance: %v", err)
	}

	// Test that all components are initialized
	if cft.GetHeartbeatManager() == nil {
		t.Error("Heartbeat manager not initialized")
	}

	if cft.GetCheckpointManager() == nil {
		t.<PERSON>rror("Checkpoint manager not initialized")
	}

	if cft.GetResultAggregator() == nil {
		t.Error("Result aggregator not initialized")
	}

	if cft.GetMigrationCoordinator() == nil {
		t.<PERSON>r("Migration coordinator not initialized")
	}

	if cft.GetAdaptiveTimeouts() == nil {
		t.Error("Adaptive timeout manager not initialized")
	}

	// Test configuration
	config := cft.GetConfig()
	if !config.HeartbeatConfig.AdaptiveEnabled {
		t.Error("Adaptive heartbeat should be enabled")
	}

	if !config.CheckpointConfig.Enabled {
		t.Error("Checkpointing should be enabled")
	}

	if !config.ResultAggregationConfig.Enabled {
		t.Error("Result aggregation should be enabled")
	}

	if !config.MigrationConfig.Enabled {
		t.Error("Task migration should be enabled")
	}

	t.Logf("Enhanced fault tolerance creation test passed")
}

func TestHeartbeatManagerBasic(t *testing.T) {
	config := gpu.HeartbeatConfig{
		BaseInterval:        time.Second * 5,
		MaxInterval:         time.Second * 30,
		MinInterval:         time.Second * 2,
		AdaptiveEnabled:     true,
		PhiThreshold:        8.0,
		IntervalHistorySize: 10,
		JitterTolerance:     0.2,
		MissedBeatThreshold: 3,
	}

	hm := gpu.NewHeartbeatManager(config, log.Default())
	if hm == nil {
		t.Fatal("Failed to create heartbeat manager")
	}

	// Test basic heartbeat processing
	heartbeat := &gpu.Heartbeat{
		NodeID:         "test_node",
		Timestamp:      time.Now(),
		SequenceNumber: 1,
		NodeStatus:     gpu.NodeHealthHealthy,
		NetworkMetrics: gpu.NetworkMetrics{
			Latency: time.Millisecond * 10,
		},
	}

	// Process heartbeat (should not panic)
	hm.ProcessHeartbeat(heartbeat)

	// Check node suspicion
	suspicious, level := hm.CheckNodeSuspicion("test_node")
	t.Logf("Node suspicion: %v, level: %f", suspicious, level)

	// Test adaptive timeout
	timeout := hm.GetAdaptiveTimeout("test_node")
	if timeout <= 0 {
		t.Error("Adaptive timeout should be positive")
	}

	t.Logf("Heartbeat manager basic test passed")
}

func TestCheckpointStorageBasic(t *testing.T) {
	// Create temporary directory for testing
	tempDir := filepath.Join(os.TempDir(), "test_checkpoints")
	defer os.RemoveAll(tempDir)

	storage, err := gpu.NewFileCheckpointStorage(tempDir)
	if err != nil {
		t.Fatalf("Failed to create checkpoint storage: %v", err)
	}

	config := gpu.CheckpointConfig{
		Enabled:             true,
		MaxVersions:         3,
		StoragePath:         tempDir,
		VerificationEnabled: true,
	}

	cm := gpu.NewCheckpointManager(storage, config, log.Default())
	if cm == nil {
		t.Fatal("Failed to create checkpoint manager")
	}

	// Test basic checkpoint creation
	state := gpu.CheckpointState{
		Progress: 0.5,
		Phase:    "testing",
	}

	testData := []byte("test checkpoint data")

	err = cm.CreateCheckpoint("test_task", state, testData)
	if err != nil {
		t.Fatalf("Failed to create checkpoint: %v", err)
	}

	// Test checkpoint restoration
	checkpoint, err := cm.RestoreFromCheckpoint("test_task")
	if err != nil {
		t.Fatalf("Failed to restore checkpoint: %v", err)
	}

	if checkpoint.TaskID != "test_task" {
		t.Errorf("Expected task ID 'test_task', got '%s'", checkpoint.TaskID)
	}

	if checkpoint.State.Progress != 0.5 {
		t.Errorf("Expected progress 0.5, got %f", checkpoint.State.Progress)
	}

	t.Logf("Checkpoint storage basic test passed")
}

func TestPartialResultAggregatorBasic(t *testing.T) {
	config := gpu.ResultAggregationConfig{
		Enabled:           true,
		ValidationEnabled: true,
	}

	pra := gpu.NewPartialResultAggregator(config, log.Default())
	if pra == nil {
		t.Fatal("Failed to create partial result aggregator")
	}

	// Test basic result handling
	result := &gpu.PartialResult{
		TaskID:    "test_task",
		PartID:    "part1",
		NodeID:    "node1",
		Timestamp: time.Now(),
		Data:      "test data",
		Progress:  1.0,
	}

	err := pra.AddPartialResult(result)
	if err != nil {
		t.Fatalf("Failed to add partial result: %v", err)
	}

	// Check if result was stored
	taskResults, exists := pra.GetTaskResults("test_task")
	if !exists {
		t.Fatal("Task results not found")
	}

	if len(taskResults.PartialResults) != 1 {
		t.Errorf("Expected 1 partial result, got %d", len(taskResults.PartialResults))
	}

	t.Logf("Partial result aggregator basic test passed")
}

func TestTaskMigrationCoordinatorBasic(t *testing.T) {
	config := gpu.MigrationConfig{
		Enabled:                 true,
		MaxConcurrentMigrations: 2,
		VerificationEnabled:     true,
	}

	tmc := gpu.NewTaskMigrationCoordinator(config, log.Default())
	if tmc == nil {
		t.Fatal("Failed to create task migration coordinator")
	}

	// Test basic migration
	err := tmc.MigrateTask("test_task", "source_node", "target_node", false)
	if err != nil {
		t.Fatalf("Failed to start migration: %v", err)
	}

	// Check migration status
	migration, exists := tmc.GetMigrationStatus("test_task")
	if !exists {
		t.Fatal("Migration not found")
	}

	if migration.TaskID != "test_task" {
		t.Errorf("Expected task ID 'test_task', got '%s'", migration.TaskID)
	}

	if migration.SourceNodeID != "source_node" {
		t.Errorf("Expected source node 'source_node', got '%s'", migration.SourceNodeID)
	}

	t.Logf("Task migration coordinator basic test passed")
}

func TestFaultToleranceStartStop(t *testing.T) {
	// Create enhanced fault tolerance manager
	distributor := &gpu.ClusterWorkloadDistributor{}
	cft, err := gpu.NewClusterFaultTolerance(distributor, log.Default())
	if err != nil {
		t.Fatalf("Failed to create cluster fault tolerance: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()

	// Test start
	err = cft.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start fault tolerance: %v", err)
	}

	// Let it run briefly
	time.Sleep(time.Millisecond * 100)

	// Test stop
	err = cft.Stop()
	if err != nil {
		t.Fatalf("Failed to stop fault tolerance: %v", err)
	}

	t.Logf("Fault tolerance start/stop test passed")
}

func TestFaultToleranceScenario(t *testing.T) {
	// Create enhanced fault tolerance manager
	distributor := &gpu.ClusterWorkloadDistributor{}
	cft, err := gpu.NewClusterFaultTolerance(distributor, log.Default())
	if err != nil {
		t.Fatalf("Failed to create cluster fault tolerance: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()

	// Start fault tolerance system
	err = cft.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start fault tolerance: %v", err)
	}
	defer cft.Stop()

	// Test heartbeat processing
	hm := cft.GetHeartbeatManager()
	if hm != nil {
		heartbeat := &gpu.Heartbeat{
			NodeID:         "test_node",
			Timestamp:      time.Now(),
			SequenceNumber: 1,
			NodeStatus:     gpu.NodeHealthHealthy,
			NetworkMetrics: gpu.NetworkMetrics{Latency: time.Millisecond * 5},
		}

		// Process several heartbeats
		for i := 0; i < 3; i++ {
			heartbeat.SequenceNumber = int64(i + 1)
			heartbeat.Timestamp = time.Now()
			hm.ProcessHeartbeat(heartbeat)
			time.Sleep(time.Millisecond * 50)
		}

		// Check that node is not suspicious
		suspicious, level := hm.CheckNodeSuspicion("test_node")
		t.Logf("Node suspicious: %v, level: %f", suspicious, level)
	}

	// Test checkpoint creation
	cm := cft.GetCheckpointManager()
	if cm != nil {
		state := gpu.CheckpointState{
			Progress: 0.75,
			Phase:    "computation",
		}

		testData := []byte("scenario checkpoint data")

		err := cm.CreateCheckpoint("scenario_task", state, testData)
		if err == nil {
			t.Logf("Checkpoint created successfully")

			// Try to restore
			checkpoint, err := cm.RestoreFromCheckpoint("scenario_task")
			if err == nil && checkpoint.State.Progress == 0.75 {
				t.Logf("Checkpoint restored successfully - progress: %.1f%%",
					checkpoint.State.Progress*100)
			}
		}
	}

	// Test partial result aggregation
	pra := cft.GetResultAggregator()
	if pra != nil {
		result := &gpu.PartialResult{
			TaskID:    "scenario_task",
			PartID:    "chunk_1",
			NodeID:    "node1",
			Data:      "scenario_result",
			Progress:  1.0,
			Timestamp: time.Now(),
		}

		err := pra.AddPartialResult(result)
		if err == nil {
			t.Logf("Partial result added successfully")
		}
	}

	// Test task migration
	tmc := cft.GetMigrationCoordinator()
	if tmc != nil {
		err := tmc.MigrateTask("scenario_migration", "source", "target", false)
		if err == nil {
			t.Logf("Migration initiated successfully")
		}
	}

	t.Logf("Fault tolerance scenario test completed")
}
