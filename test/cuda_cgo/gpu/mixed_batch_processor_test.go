package gpu

import (
	"context"
	"fmt"
	"log"
	"os"
	"testing"
	"time"

	"neuralmetergo/internal/gpu"
)

func TestMixedBatchProcessor_Creation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultMixedBatchConfig()

	processor := gpu.NewMixedBatchProcessor(config, nil, logger)
	if processor == nil {
		t.Fatal("MixedBatchProcessor should not be nil")
	}

	stats := processor.GetStatistics()
	if stats == nil {
		t.Fatal("Statistics should not be nil")
	}

	if stats.TotalBatches != 0 {
		t.Errorf("Expected 0 total batches, got %d", stats.TotalBatches)
	}
}

func TestBatchNormalizer_Creation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultMixedBatchConfig()

	normalizer := gpu.NewBatchNormalizer(config, logger)
	if normalizer == nil {
		t.Fatal("BatchNormalizer should not be nil")
	}

	stats := normalizer.GetNormalizationStatistics()
	if stats == nil {
		t.Fatal("Statistics should not be nil")
	}

	totalNorm, ok := stats["total_normalizations"].(int)
	if !ok || totalNorm != 0 {
		t.Errorf("Expected 0 total normalizations, got %v", stats["total_normalizations"])
	}
}

func TestPaddingStrategy_String(t *testing.T) {
	tests := []struct {
		strategy gpu.PaddingStrategy
		expected string
	}{
		{gpu.PaddingZero, "zero"},
		{gpu.PaddingEdge, "edge"},
		{gpu.PaddingReflection, "reflection"},
		{gpu.PaddingMirror, "mirror"},
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			if got := tt.strategy.String(); got != tt.expected {
				t.Errorf("PaddingStrategy.String() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestBatchCompatibilityLevel_String(t *testing.T) {
	tests := []struct {
		level    gpu.BatchCompatibilityLevel
		expected string
	}{
		{gpu.CompatibilityExact, "exact"},
		{gpu.CompatibilityHigh, "high"},
		{gpu.CompatibilityMedium, "medium"},
		{gpu.CompatibilityLow, "low"},
		{gpu.CompatibilityIncompatible, "incompatible"},
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			if got := tt.level.String(); got != tt.expected {
				t.Errorf("BatchCompatibilityLevel.String() = %v, want %v", got, tt.expected)
			}
		})
	}
}

// Helper function to create mock inference requests
func createMockRequests(shapes [][]int, modelIDs []string) []*gpu.InferenceRequest {
	var requests []*gpu.InferenceRequest

	for i := 0; i < len(shapes); i++ {
		modelID := "test-model"
		if i < len(modelIDs) {
			modelID = modelIDs[i]
		}

		request := &gpu.InferenceRequest{
			ID:          fmt.Sprintf("req-%d", i),
			ModelID:     modelID,
			InputShape:  shapes[i],
			Priority:    gpu.RequestPriorityNormal,
			SubmittedAt: time.Now(),
		}
		requests = append(requests, request)
	}

	return requests
}

func TestMixedBatchProcessor_HomogeneousBatch(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultMixedBatchConfig()
	processor := gpu.NewMixedBatchProcessor(config, nil, logger)

	// Create requests with identical shapes
	shapes := [][]int{
		{224, 224, 3},
		{224, 224, 3},
		{224, 224, 3},
	}
	requests := createMockRequests(shapes, []string{"model1", "model1", "model1"})

	ctx := context.Background()
	groups, err := processor.ProcessMixedBatch(ctx, requests)

	if err != nil {
		t.Fatalf("Failed to process homogeneous batch: %v", err)
	}

	if len(groups) != 1 {
		t.Errorf("Expected 1 group for homogeneous batch, got %d", len(groups))
	}

	group := groups[0]
	if group.CompatibilityLevel != gpu.CompatibilityExact {
		t.Errorf("Expected exact compatibility, got %s", group.CompatibilityLevel.String())
	}

	if group.PaddingOverhead != 0.0 {
		t.Errorf("Expected 0 padding overhead for homogeneous batch, got %.2f", group.PaddingOverhead)
	}

	if len(group.Requests) != 3 {
		t.Errorf("Expected 3 requests in group, got %d", len(group.Requests))
	}
}

func TestMixedBatchProcessor_MixedShapesBatch(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultMixedBatchConfig()
	processor := gpu.NewMixedBatchProcessor(config, nil, logger)

	// Create requests with different but compatible shapes
	shapes := [][]int{
		{224, 224, 3},
		{256, 256, 3},
		{200, 200, 3},
	}
	requests := createMockRequests(shapes, []string{"model1", "model1", "model1"})

	ctx := context.Background()
	groups, err := processor.ProcessMixedBatch(ctx, requests)

	if err != nil {
		t.Fatalf("Failed to process mixed shapes batch: %v", err)
	}

	if len(groups) == 0 {
		t.Fatal("Expected at least 1 group for mixed shapes batch")
	}

	// Check that groups were created (might be multiple groups if compatibility is low)
	totalRequests := 0
	for _, group := range groups {
		totalRequests += len(group.Requests)

		// Verify target shape is largest or rounded
		if len(group.TargetShape) != 3 {
			t.Errorf("Expected target shape with 3 dimensions, got %d", len(group.TargetShape))
		}
	}

	if totalRequests != 3 {
		t.Errorf("Expected 3 total requests across all groups, got %d", totalRequests)
	}
}

func TestMixedBatchProcessor_IncompatibleBatch(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultMixedBatchConfig()
	processor := gpu.NewMixedBatchProcessor(config, nil, logger)

	// Create requests with very different shapes (different ranks)
	shapes := [][]int{
		{224, 224, 3}, // 3D
		{100, 50},     // 2D
		{1000},        // 1D
	}
	requests := createMockRequests(shapes, []string{"model1", "model2", "model3"})

	ctx := context.Background()
	groups, err := processor.ProcessMixedBatch(ctx, requests)

	if err != nil {
		t.Fatalf("Failed to process incompatible batch: %v", err)
	}

	// Should create separate groups for incompatible shapes
	if len(groups) < 2 {
		t.Errorf("Expected at least 2 groups for incompatible shapes, got %d", len(groups))
	}

	// Verify all requests are accounted for
	totalRequests := 0
	for _, group := range groups {
		totalRequests += len(group.Requests)
	}

	if totalRequests != 3 {
		t.Errorf("Expected 3 total requests across all groups, got %d", totalRequests)
	}
}

func TestBatchNormalizer_NormalizeBatchShapes(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultMixedBatchConfig()
	config.EnableShapeRounding = false // Disable shape rounding for this test to reduce padding overhead
	normalizer := gpu.NewBatchNormalizer(config, logger)

	// Create requests with different but normalizable shapes (closer sizes to reduce padding overhead)
	shapes := [][]int{
		{222, 222, 3},
		{224, 224, 3},
		{223, 223, 3},
	}
	requests := createMockRequests(shapes, []string{"model1", "model1", "model1"})

	result, err := normalizer.NormalizeBatchShapes(requests)

	if err != nil {
		t.Fatalf("Failed to normalize batch shapes: %v", err)
	}

	if result == nil {
		t.Fatal("Normalization result should not be nil")
	}

	// Check normalized shape
	if len(result.NormalizedShape) != 3 {
		t.Errorf("Expected normalized shape with 3 dimensions, got %d", len(result.NormalizedShape))
	}

	// Target shape should be at least as large as the largest input
	expectedMinSize := []int{224, 224, 3} // largest from input
	for i, dim := range result.NormalizedShape {
		if dim < expectedMinSize[i] {
			t.Errorf("Normalized dimension %d (%d) should be at least %d", i, dim, expectedMinSize[i])
		}
	}

	// Check padding info
	if len(result.PaddingInfo) != 3 {
		t.Errorf("Expected padding info for 3 requests, got %d", len(result.PaddingInfo))
	}

	// Check that we have reasonable padding overhead
	if result.PaddingOverhead < 0 {
		t.Errorf("Padding overhead should be non-negative, got %.2f", result.PaddingOverhead)
	}

	// Check processing efficiency
	if result.ProcessingEfficiency <= 0 || result.ProcessingEfficiency > 1 {
		t.Errorf("Processing efficiency should be between 0 and 1, got %.2f", result.ProcessingEfficiency)
	}
}

func TestBatchNormalizer_IncompatibleShapes(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultMixedBatchConfig()
	normalizer := gpu.NewBatchNormalizer(config, logger)

	// Create requests with incompatible shapes (different ranks)
	shapes := [][]int{
		{224, 224, 3}, // 3D
		{100, 50},     // 2D - incompatible rank
	}
	requests := createMockRequests(shapes, []string{"model1", "model1"})

	result, err := normalizer.NormalizeBatchShapes(requests)

	// Should fail for incompatible ranks
	if err == nil {
		t.Fatal("Expected error for incompatible shapes, got nil")
	}

	if result != nil {
		t.Fatal("Expected nil result for incompatible shapes")
	}
}

func TestDefaultMixedBatchConfig(t *testing.T) {
	config := gpu.DefaultMixedBatchConfig()

	if config == nil {
		t.Fatal("Default config should not be nil")
	}

	// Test default values
	if config.MaxPaddingOverhead != 0.30 {
		t.Errorf("Expected max padding overhead 0.30, got %.2f", config.MaxPaddingOverhead)
	}

	if config.MaxShapeVariance != 0.25 {
		t.Errorf("Expected max shape variance 0.25, got %.2f", config.MaxShapeVariance)
	}

	if config.PreferredPaddingStrategy != gpu.PaddingZero {
		t.Errorf("Expected zero padding strategy, got %s", config.PreferredPaddingStrategy.String())
	}

	if !config.EnableShapeRounding {
		t.Error("Expected shape rounding to be enabled")
	}

	if !config.EnableDynamicGrouping {
		t.Error("Expected dynamic grouping to be enabled")
	}
}

func TestMixedBatchProcessor_Statistics(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultMixedBatchConfig()
	processor := gpu.NewMixedBatchProcessor(config, nil, logger)

	// Process a homogeneous batch
	shapes := [][]int{
		{224, 224, 3},
		{224, 224, 3},
	}
	requests := createMockRequests(shapes, []string{"model1", "model1"})

	ctx := context.Background()
	_, err := processor.ProcessMixedBatch(ctx, requests)
	if err != nil {
		t.Fatalf("Failed to process batch: %v", err)
	}

	// Check statistics
	stats := processor.GetStatistics()
	if stats.TotalBatches != 1 {
		t.Errorf("Expected 1 total batch, got %d", stats.TotalBatches)
	}

	if stats.HomogeneousBatches != 1 {
		t.Errorf("Expected 1 homogeneous batch, got %d", stats.HomogeneousBatches)
	}

	if stats.MixedBatches != 0 {
		t.Errorf("Expected 0 mixed batches, got %d", stats.MixedBatches)
	}

	// Process a mixed batch
	mixedShapes := [][]int{
		{200, 200, 3},
		{256, 256, 3},
	}
	mixedRequests := createMockRequests(mixedShapes, []string{"model1", "model1"})

	_, err = processor.ProcessMixedBatch(ctx, mixedRequests)
	if err != nil {
		t.Fatalf("Failed to process mixed batch: %v", err)
	}

	// Check updated statistics
	stats = processor.GetStatistics()
	if stats.TotalBatches != 2 {
		t.Errorf("Expected 2 total batches, got %d", stats.TotalBatches)
	}

	if stats.MixedBatches != 1 {
		t.Errorf("Expected 1 mixed batch, got %d", stats.MixedBatches)
	}
}

func TestMixedBatchProcessor_EmptyBatch(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := gpu.DefaultMixedBatchConfig()
	processor := gpu.NewMixedBatchProcessor(config, nil, logger)

	ctx := context.Background()
	groups, err := processor.ProcessMixedBatch(ctx, []*gpu.InferenceRequest{})

	if err == nil {
		t.Fatal("Expected error for empty batch, got nil")
	}

	if groups != nil {
		t.Fatal("Expected nil groups for empty batch")
	}
}

func TestRequestGroupKey_String(t *testing.T) {
	key := gpu.RequestGroupKey{
		ModelID:    "test-model",
		DataType:   gpu.TensorFloat32,
		Rank:       3,
		ShapeClass: "normalized_[224,224,3]",
	}

	expected := "test-model_float32_3_normalized_[224,224,3]"
	if got := key.String(); got != expected {
		t.Errorf("RequestGroupKey.String() = %v, want %v", got, expected)
	}
}
