package gpu

import (
	"testing"
	"time"

	"neuralmetergo/internal/gpu"
)

func TestFeatureEngineer_ComputeTemporalFeatures(t *testing.T) {
	fe := gpu.NewFeatureEngineer()

	// Test case 1: Weekend detection
	weekendTime := time.Date(2024, 1, 6, 14, 30, 0, 0, time.UTC) // Saturday
	features := fe.ComputeTemporalFeatures(weekendTime)

	if !features.IsWeekend {
		t.Error("Expected IsWeekend to be true for Saturday")
	}

	if features.HourOfDay != 14 {
		t.<PERSON><PERSON><PERSON>("Expected HourOfDay to be 14, got %d", features.HourOfDay)
	}

	if features.DayOfWeek != 6 {
		t.<PERSON>rrorf("Expected DayOfWeek to be 6 (Saturday), got %d", features.DayOfWeek)
	}

	// Test case 2: Weekday detection
	weekdayTime := time.Date(2024, 1, 8, 9, 0, 0, 0, time.UTC) // Monday
	features = fe.ComputeTemporalFeatures(weekdayTime)

	if features.IsWeekend {
		t.Error("Expected IsWeekend to be false for Monday")
	}

	// Test case 3: Cyclical encoding
	if features.HourSin == 0 && features.HourCos == 0 {
		t.Error("Expected non-zero cyclical encoding values")
	}

	// Test case 4: Holiday detection
	fe.AddHoliday("2024-01-01")
	holidayTime := time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)
	features = fe.ComputeTemporalFeatures(holidayTime)

	if !features.IsHoliday {
		t.Error("Expected IsHoliday to be true for New Year's Day")
	}
}

func TestFeatureEngineer_ComputeRollingStatistics(t *testing.T) {
	fe := gpu.NewFeatureEngineer()

	// Create test data with varying queue lengths and utilization
	currentTime := time.Now()
	data := []gpu.WorkloadDataPoint{
		{Timestamp: currentTime.Add(-2 * time.Hour), QueueLength: 10, AvgUtilization: 0.5},
		{Timestamp: currentTime.Add(-90 * time.Minute), QueueLength: 15, AvgUtilization: 0.6},
		{Timestamp: currentTime.Add(-30 * time.Minute), QueueLength: 20, AvgUtilization: 0.7},
		{Timestamp: currentTime.Add(-10 * time.Minute), QueueLength: 25, AvgUtilization: 0.8},
	}

	stats := fe.ComputeRollingStatistics(data, currentTime)

	// Check that 1-hour window includes recent data
	if stats.QueueLengthMA1h == 0 {
		t.Error("Expected non-zero 1-hour moving average for queue length")
	}

	if stats.UtilizationMA1h == 0 {
		t.Error("Expected non-zero 1-hour moving average for utilization")
	}

	// Check that 24-hour window includes all data
	if stats.QueueLengthMA24h == 0 {
		t.Error("Expected non-zero 24-hour moving average for queue length")
	}

	// Check standard deviation calculation
	if stats.UtilizationStd1h == 0 {
		t.Error("Expected non-zero standard deviation for utilization")
	}
}

func TestFeatureEngineer_ComputeLagFeatures(t *testing.T) {
	fe := gpu.NewFeatureEngineer()

	currentTime := time.Now()
	data := []gpu.WorkloadDataPoint{
		{Timestamp: currentTime.Add(-25 * time.Hour), QueueLength: 5, AvgUtilization: 0.3},
		{Timestamp: currentTime.Add(-7 * time.Hour), QueueLength: 10, AvgUtilization: 0.5},
		{Timestamp: currentTime.Add(-1 * time.Hour), QueueLength: 15, AvgUtilization: 0.7},
		{Timestamp: currentTime.Add(-10 * time.Minute), QueueLength: 20, AvgUtilization: 0.8},
	}

	lagFeatures := fe.ComputeLagFeatures(data, currentTime)

	// Check that lag features are populated
	if lagFeatures.QueueLengthLag1h == 0 {
		t.Error("Expected non-zero 1-hour lag queue length")
	}

	if lagFeatures.UtilizationLag1h == 0 {
		t.Error("Expected non-zero 1-hour lag utilization")
	}

	// The 1-hour lag should be closer to the 1-hour ago data point
	expectedLag1h := 15 // From the data point 1 hour ago
	if lagFeatures.QueueLengthLag1h != expectedLag1h {
		t.Errorf("Expected 1-hour lag queue length to be %d, got %d", expectedLag1h, lagFeatures.QueueLengthLag1h)
	}
}

func TestFeatureEngineer_ComputeRateOfChange(t *testing.T) {
	fe := gpu.NewFeatureEngineer()

	currentTime := time.Now()
	data := []gpu.WorkloadDataPoint{
		{Timestamp: currentTime.Add(-2 * time.Hour), QueueLength: 10, AvgUtilization: 0.5, ActiveTasks: 5},
		{Timestamp: currentTime.Add(-1 * time.Hour), QueueLength: 15, AvgUtilization: 0.6, ActiveTasks: 8},
		{Timestamp: currentTime.Add(-10 * time.Minute), QueueLength: 20, AvgUtilization: 0.8, ActiveTasks: 12},
	}

	rateFeatures := fe.ComputeRateOfChange(data, currentTime)

	// Check that rate of change is calculated
	if rateFeatures.QueueLengthRateOfChange == 0 {
		t.Error("Expected non-zero queue length rate of change")
	}

	if rateFeatures.UtilizationRateOfChange == 0 {
		t.Error("Expected non-zero utilization rate of change")
	}

	if rateFeatures.ActiveTasksRateOfChange == 0 {
		t.Error("Expected non-zero active tasks rate of change")
	}

	// Rate should be positive (increasing trend)
	if rateFeatures.QueueLengthRateOfChange < 0 {
		t.Error("Expected positive queue length rate of change for increasing data")
	}
}

func TestFeatureEngineer_ComputeFeatureInteractions(t *testing.T) {
	fe := gpu.NewFeatureEngineer()

	queueLength := 50
	utilization := 0.8
	activeTasks := 40
	nodesActive := 5

	interactions := fe.ComputeFeatureInteractions(queueLength, utilization, activeTasks, nodesActive)

	// Check queue-utilization product
	expectedProduct := float64(queueLength) * utilization
	if interactions.QueueUtilizationProduct != expectedProduct {
		t.Errorf("Expected queue-utilization product to be %f, got %f", expectedProduct, interactions.QueueUtilizationProduct)
	}

	// Check tasks per node ratio
	expectedRatio := float64(activeTasks) / float64(nodesActive)
	if interactions.TasksPerNodeRatio != expectedRatio {
		t.Errorf("Expected tasks per node ratio to be %f, got %f", expectedRatio, interactions.TasksPerNodeRatio)
	}

	// Check that other interaction features are computed
	if interactions.MemoryPressureIndex == 0 {
		t.Error("Expected non-zero memory pressure index")
	}

	if interactions.LoadVariabilityIndex == 0 {
		t.Error("Expected non-zero load variability index")
	}
}

func TestFeatureEngineer_ComputePolynomialFeatures(t *testing.T) {
	fe := gpu.NewFeatureEngineer()

	utilization := 0.8
	queueLength := 25

	polyFeatures := fe.ComputePolynomialFeatures(utilization, queueLength)

	// Check utilization squared
	expectedUtilSquared := utilization * utilization
	if polyFeatures.UtilizationSquared != expectedUtilSquared {
		t.Errorf("Expected utilization squared to be %f, got %f", expectedUtilSquared, polyFeatures.UtilizationSquared)
	}

	// Check queue length squared
	expectedQueueSquared := float64(queueLength * queueLength)
	if polyFeatures.QueueLengthSquared != expectedQueueSquared {
		t.Errorf("Expected queue length squared to be %f, got %f", expectedQueueSquared, polyFeatures.QueueLengthSquared)
	}

	// Check queue length cubed
	expectedQueueCubed := float64(queueLength * queueLength * queueLength)
	if polyFeatures.QueueLengthCubed != expectedQueueCubed {
		t.Errorf("Expected queue length cubed to be %f, got %f", expectedQueueCubed, polyFeatures.QueueLengthCubed)
	}
}

func TestFeatureEngineer_ComputeTimeSeriesDecomposition(t *testing.T) {
	fe := gpu.NewFeatureEngineer()

	// Create test data with trend and seasonality
	currentTime := time.Now()
	data := []gpu.WorkloadDataPoint{
		{Timestamp: currentTime.Add(-5 * time.Hour), AvgUtilization: 0.4},
		{Timestamp: currentTime.Add(-4 * time.Hour), AvgUtilization: 0.5},
		{Timestamp: currentTime.Add(-3 * time.Hour), AvgUtilization: 0.6},
		{Timestamp: currentTime.Add(-2 * time.Hour), AvgUtilization: 0.7},
		{Timestamp: currentTime.Add(-1 * time.Hour), AvgUtilization: 0.8},
	}

	currentValue := 0.85
	decomposition := fe.ComputeTimeSeriesDecomposition(data, currentValue)

	// Check that decomposition components are calculated
	if decomposition.TrendComponent == 0 {
		t.Error("Expected non-zero trend component")
	}

	// For this increasing data, trend should be positive
	if decomposition.TrendComponent < 0 {
		t.Error("Expected positive trend component for increasing data")
	}

	// Seasonal and residual components should be calculated
	// Note: These might be zero for simple test data, which is acceptable
}

func TestFeatureEngineer_ExtractAllFeatures(t *testing.T) {
	fe := gpu.NewFeatureEngineer()

	currentTime := time.Now()
	basePoint := gpu.WorkloadDataPoint{
		Timestamp:      currentTime,
		QueueLength:    30,
		ActiveTasks:    25,
		AvgUtilization: 0.75,
		NodesActive:    4,
	}

	historicalData := []gpu.WorkloadDataPoint{
		{Timestamp: currentTime.Add(-2 * time.Hour), QueueLength: 15, AvgUtilization: 0.5, ActiveTasks: 12},
		{Timestamp: currentTime.Add(-1 * time.Hour), QueueLength: 20, AvgUtilization: 0.6, ActiveTasks: 18},
		{Timestamp: currentTime.Add(-30 * time.Minute), QueueLength: 25, AvgUtilization: 0.7, ActiveTasks: 22},
	}

	enrichedPoint := fe.ExtractAllFeatures(basePoint, historicalData)

	// Check that temporal features are populated
	if enrichedPoint.HourOfDay < 0 || enrichedPoint.HourOfDay > 23 {
		t.Error("Invalid hour of day in temporal features")
	}

	// Check that rolling statistics are computed
	if enrichedPoint.QueueLengthMA1h == 0 {
		t.Error("Expected non-zero 1-hour moving average")
	}

	// Check that lag features are populated
	if enrichedPoint.QueueLengthLag1h == 0 {
		t.Error("Expected non-zero 1-hour lag feature")
	}

	// Check that rate of change features are computed
	if enrichedPoint.QueueLengthRateOfChange == 0 {
		t.Error("Expected non-zero rate of change feature")
	}

	// Check that interaction features are computed
	if enrichedPoint.QueueUtilizationProduct == 0 {
		t.Error("Expected non-zero queue-utilization product")
	}

	// Check that polynomial features are computed
	if enrichedPoint.UtilizationSquared == 0 {
		t.Error("Expected non-zero utilization squared")
	}

	// Check that time series decomposition is computed
	if enrichedPoint.TrendComponent == 0 {
		t.Error("Expected non-zero trend component")
	}
}

func TestFeatureEngineer_ExtractFeatureVector(t *testing.T) {
	fe := gpu.NewFeatureEngineer()

	dataPoint := gpu.WorkloadDataPoint{
		QueueLength:             30,
		AvgUtilization:          0.75,
		ActiveTasks:             25,
		NodesActive:             4,
		HourOfDay:               14,
		DayOfWeek:               1,
		IsWeekend:               false,
		IsHoliday:               false,
		HourSin:                 0.5,
		HourCos:                 0.866,
		QueueLengthMA1h:         28.0,
		UtilizationMA1h:         0.73,
		QueueLengthLag1h:        25,
		UtilizationLag1h:        0.70,
		QueueUtilizationProduct: 22.5,
		UtilizationSquared:      0.5625,
		TrendComponent:          0.1,
	}

	featureVector := fe.ExtractFeatureVector(dataPoint)

	// Check that feature vector has expected length
	expectedLength := len(fe.GetFeatureNames())
	if len(featureVector) != expectedLength {
		t.Errorf("Expected feature vector length %d, got %d", expectedLength, len(featureVector))
	}

	// Check that basic features are correctly extracted
	if featureVector[0] != float64(dataPoint.QueueLength) {
		t.Errorf("Expected first feature to be queue length %d, got %f", dataPoint.QueueLength, featureVector[0])
	}

	if featureVector[1] != dataPoint.AvgUtilization {
		t.Errorf("Expected second feature to be utilization %f, got %f", dataPoint.AvgUtilization, featureVector[1])
	}

	// Check that boolean features are converted correctly
	weekendIndex := 6 // Position of is_weekend in feature vector
	if featureVector[weekendIndex] != 0.0 {
		t.Error("Expected is_weekend to be 0.0 for false")
	}
}

func TestFeatureEngineer_NormalizeFeatures(t *testing.T) {
	fe := gpu.NewFeatureEngineer()

	// Create test data with varying scales
	data := []gpu.WorkloadDataPoint{
		{QueueLength: 10, AvgUtilization: 0.2},
		{QueueLength: 50, AvgUtilization: 0.8},
		{QueueLength: 30, AvgUtilization: 0.5},
	}

	normalized := fe.NormalizeFeatures(data)

	// Check that data is normalized
	if len(normalized) != len(data) {
		t.Error("Normalized data should have same length as input")
	}

	// Verify that normalization was applied (values should be different)
	originalSum := 0
	normalizedSum := 0
	for i := range data {
		originalSum += data[i].QueueLength
		normalizedSum += normalized[i].QueueLength
	}

	if originalSum == normalizedSum {
		t.Error("Expected normalization to change queue length values")
	}
}

func TestFeatureEngineer_NormalizeFeatureVector(t *testing.T) {
	fe := gpu.NewFeatureEngineer()

	features := []float64{10.0, 50.0, 30.0, 20.0}
	mins := []float64{10.0, 40.0, 20.0, 15.0}
	maxs := []float64{50.0, 60.0, 40.0, 25.0}

	normalized := fe.NormalizeFeatureVector(features, mins, maxs)

	// Check that normalization is applied correctly
	expectedFirst := (10.0 - 10.0) / (50.0 - 10.0) // Should be 0.0
	if normalized[0] != expectedFirst {
		t.Errorf("Expected first normalized value to be %f, got %f", expectedFirst, normalized[0])
	}

	expectedSecond := (50.0 - 40.0) / (60.0 - 40.0) // Should be 0.5
	if normalized[1] != expectedSecond {
		t.Errorf("Expected second normalized value to be %f, got %f", expectedSecond, normalized[1])
	}
}

func TestFeatureEngineer_StandardizeFeatureVector(t *testing.T) {
	fe := gpu.NewFeatureEngineer()

	features := []float64{10.0, 50.0, 30.0, 20.0}
	means := []float64{20.0, 40.0, 25.0, 15.0}
	stds := []float64{5.0, 10.0, 8.0, 3.0}

	standardized := fe.StandardizeFeatureVector(features, means, stds)

	// Check that standardization is applied correctly
	expectedFirst := (10.0 - 20.0) / 5.0 // Should be -2.0
	if standardized[0] != expectedFirst {
		t.Errorf("Expected first standardized value to be %f, got %f", expectedFirst, standardized[0])
	}

	expectedSecond := (50.0 - 40.0) / 10.0 // Should be 1.0
	if standardized[1] != expectedSecond {
		t.Errorf("Expected second standardized value to be %f, got %f", expectedSecond, standardized[1])
	}
}

func TestFeatureEngineer_GetFeatureNames(t *testing.T) {
	fe := gpu.NewFeatureEngineer()

	featureNames := fe.GetFeatureNames()

	// Check that feature names are returned
	if len(featureNames) == 0 {
		t.Error("Expected non-empty feature names list")
	}

	// Check that basic features are included
	expectedFeatures := []string{"queue_length", "avg_utilization", "active_tasks", "nodes_active"}
	for _, expected := range expectedFeatures {
		found := false
		for _, name := range featureNames {
			if name == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected feature name '%s' not found in feature names list", expected)
		}
	}
}

// Benchmark tests for performance
func BenchmarkFeatureEngineer_ExtractAllFeatures(b *testing.B) {
	fe := gpu.NewFeatureEngineer()

	currentTime := time.Now()
	basePoint := gpu.WorkloadDataPoint{
		Timestamp:      currentTime,
		QueueLength:    30,
		ActiveTasks:    25,
		AvgUtilization: 0.75,
		NodesActive:    4,
	}

	// Create larger historical dataset for benchmarking
	historicalData := make([]gpu.WorkloadDataPoint, 100)
	for i := range historicalData {
		historicalData[i] = gpu.WorkloadDataPoint{
			Timestamp:      currentTime.Add(-time.Duration(i) * time.Minute),
			QueueLength:    15 + i%20,
			AvgUtilization: 0.5 + float64(i%30)/100,
			ActiveTasks:    10 + i%15,
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		fe.ExtractAllFeatures(basePoint, historicalData)
	}
}

func BenchmarkFeatureEngineer_ExtractFeatureVector(b *testing.B) {
	fe := gpu.NewFeatureEngineer()

	dataPoint := gpu.WorkloadDataPoint{
		QueueLength:    30,
		AvgUtilization: 0.75,
		ActiveTasks:    25,
		NodesActive:    4,
		HourOfDay:      14,
		DayOfWeek:      1,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		fe.ExtractFeatureVector(dataPoint)
	}
}
