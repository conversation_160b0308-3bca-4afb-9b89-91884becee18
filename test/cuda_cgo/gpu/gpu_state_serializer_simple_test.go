package gpu_test

import (
	"log"
	"os"
	"testing"

	"neuralmetergo/internal/gpu"
)

// Simple logger for testing
type simpleLogger struct{}

func (l *simpleLogger) Printf(format string, args ...interface{}) {
	log.Printf(format, args...)
}

func (l *simpleLogger) Errorf(format string, args ...interface{}) {
	log.Printf("ERROR: "+format, args...)
}

func TestGPUStateSerializerCreation(t *testing.T) {
	logger := &simpleLogger{}
	config := gpu.DefaultSerializerConfig()

	serializer := gpu.NewGPUStateSerializer(config, logger)
	if serializer == nil {
		t.Fatal("Failed to create GPU state serializer")
	}

	// Test configuration
	if !config.CompressionEnabled {
		t.Error("Expected compression to be enabled by default")
	}

	if config.CompressionLevel != 6 {
		t.Errorf("Expected compression level 6, got %d", config.CompressionLevel)
	}

	if !config.ChecksumEnabled {
		t.Error("Expected checksum to be enabled by default")
	}

	// Test tensor registration
	tensors := serializer.GetRegisteredTensors()
	if len(tensors) != 0 {
		t.Errorf("Expected 0 registered tensors, got %d", len(tensors))
	}

	t.Log("GPU state serializer creation test passed")
}

func TestDefaultCompressorFunctionality(t *testing.T) {
	compressor := gpu.NewDefaultCompressor(6)
	if compressor == nil {
		t.Fatal("Failed to create default compressor")
	}

	// Use larger, more repetitive test data that will compress well
	testData := []byte(`This is test data for compression testing. It should be compressed effectively.
This is test data for compression testing. It should be compressed effectively.
This is test data for compression testing. It should be compressed effectively.
This is test data for compression testing. It should be compressed effectively.
This is test data for compression testing. It should be compressed effectively.
This is test data for compression testing. It should be compressed effectively.
This is test data for compression testing. It should be compressed effectively.
This is test data for compression testing. It should be compressed effectively.`)

	// Test compression
	compressed, err := compressor.Compress(testData)
	if err != nil {
		t.Fatalf("Compression failed: %v", err)
	}

	// For small data, compression might not always reduce size due to overhead
	// Just verify compression works without size assumption
	t.Logf("Original size: %d bytes, Compressed size: %d bytes", len(testData), len(compressed))

	// Test decompression
	decompressed, err := compressor.Decompress(compressed)
	if err != nil {
		t.Fatalf("Decompression failed: %v", err)
	}

	if string(decompressed) != string(testData) {
		t.Error("Decompressed data doesn't match original")
	}

	t.Log("Default compressor functionality test passed")
}

func TestGPUStateSnapshotCreation(t *testing.T) {
	logger := &simpleLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)

	// Test capturing state without any registered components
	snapshot, err := serializer.CaptureGPUState(0, "test")
	if err != nil {
		t.Fatalf("Failed to capture GPU state: %v", err)
	}

	if snapshot == nil {
		t.Fatal("Snapshot should not be nil")
	}

	if snapshot.DeviceID != 0 {
		t.Errorf("Expected device ID 0, got %d", snapshot.DeviceID)
	}

	if snapshot.APIType != "test" {
		t.Errorf("Expected API type 'test', got %s", snapshot.APIType)
	}

	if snapshot.Version != "1.0" {
		t.Errorf("Expected version '1.0', got %s", snapshot.Version)
	}

	if len(snapshot.TensorStates) != 0 {
		t.Errorf("Expected 0 tensor states, got %d", len(snapshot.TensorStates))
	}

	t.Log("GPU state snapshot creation test passed")
}

func TestSnapshotSerialization(t *testing.T) {
	logger := &simpleLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)

	// Create a test snapshot
	snapshot, err := serializer.CaptureGPUState(0, "test")
	if err != nil {
		t.Fatalf("Failed to capture GPU state: %v", err)
	}

	// Serialize the snapshot
	data, err := serializer.SerializeSnapshot(snapshot)
	if err != nil {
		t.Fatalf("Failed to serialize snapshot: %v", err)
	}

	if len(data) == 0 {
		t.Error("Serialized data should not be empty")
	}

	// Deserialize the snapshot
	deserializedSnapshot, err := serializer.DeserializeSnapshot(data)
	if err != nil {
		t.Fatalf("Failed to deserialize snapshot: %v", err)
	}

	if deserializedSnapshot.DeviceID != snapshot.DeviceID {
		t.Errorf("Device ID mismatch: expected %d, got %d", snapshot.DeviceID, deserializedSnapshot.DeviceID)
	}

	if deserializedSnapshot.APIType != snapshot.APIType {
		t.Errorf("API type mismatch: expected %s, got %s", snapshot.APIType, deserializedSnapshot.APIType)
	}

	t.Log("Snapshot serialization test passed")
}

func TestCacheManagement(t *testing.T) {
	logger := &simpleLogger{}
	config := gpu.DefaultSerializerConfig()
	serializer := gpu.NewGPUStateSerializer(config, logger)

	// Test initial state
	tensors := serializer.GetRegisteredTensors()
	if len(tensors) != 0 {
		t.Errorf("Expected 0 registered tensors initially, got %d", len(tensors))
	}

	// Test clear cache (should not fail on empty cache)
	serializer.ClearCache()

	// Verify cache is still empty
	tensors = serializer.GetRegisteredTensors()
	if len(tensors) != 0 {
		t.Errorf("Expected 0 registered tensors after clear, got %d", len(tensors))
	}

	t.Log("Cache management test passed")
}

// Run this test to verify everything works
func TestMain(m *testing.M) {
	// Setup
	log.SetOutput(os.Stdout)
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	// Run tests
	code := m.Run()

	// Cleanup
	os.Exit(code)
}
