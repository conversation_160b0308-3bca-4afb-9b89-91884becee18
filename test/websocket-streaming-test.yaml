name: WebSocket Streaming Test
description: Test WebSocket streaming functionality with NeuralMeter CLI
version: 1.0

duration: 15s
concurrency: 2
ramp_up: 3s

scenarios:
  - name: websocket_stream_test
    description: Test real-time result streaming via WebSocket
    weight: 100
    requests:
      - name: api_load_with_streaming
        method: GET
        url: http://*************:8011/api/data
        headers:
          User-Agent: NeuralMeter-WebSocket-Test/1.0
        timeout: 5s
        
      - name: health_check_with_streaming
        method: GET
        url: http://*************:8021/health
        headers:
          User-Agent: NeuralMeter-Health-Stream/1.0
        timeout: 5s

global:
  timeout: 10s

output:
  format: 
    - json
    - stream
  detailed: true