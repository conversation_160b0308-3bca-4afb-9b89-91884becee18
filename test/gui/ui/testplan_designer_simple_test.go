//go:build gui
// +build gui

package ui

import (
	"log"
	"os"
	"testing"
	"time"

	"neuralmetergo/internal/parser"
	"neuralmetergo/internal/ui"
	"neuralmetergo/internal/validation"
)

// Test helper functions
func createTestLoggerSimple() *log.Logger {
	return log.New(os.Stdout, "[TEST] ", log.LstdFlags)
}

func createTestValidationEngineSimple() *validation.ValidationEngine {
	return validation.NewValidationEngine()
}

func createSampleTestPlanSimple() *parser.TestPlan {
	return &parser.TestPlan{
		Version:     "1.0",
		Name:        "Test Plan",
		Description: "A test plan for testing",
		Duration:    parser.Duration{Duration: 30 * time.Second},
		Concurrency: 1,
		Global: parser.Global{
			BaseURL: "https://api.test.com",
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
		},
		Scenarios: []parser.Scenario{
			{
				Name:        "Test Scenario",
				Description: "A test scenario",
				Requests: []parser.Request{
					{
						Name:   "Test Request",
						Method: "GET",
						URL:    "/test",
						Assertions: []parser.Assertion{
							{
								Type:     "status_code",
								Operator: "eq",
								Value:    200,
							},
						},
					},
				},
			},
		},
		Variables: []parser.Variable{
			{
				Name:  "testVar",
				Type:  "static",
				Value: "testValue",
			},
		},
		Output: parser.Output{
			Format: []string{"json"},
		},
	}
}

// Unit Tests for ChangeTracker (No UI dependencies)
func TestChangeTracker_BasicFunctionality(t *testing.T) {
	tracker := ui.NewChangeTracker(10)

	if tracker == nil {
		t.Fatal("Expected ChangeTracker to be created, got nil")
	}

	// Test initial state
	if tracker.CanUndo() {
		t.Error("Expected CanUndo to be false initially")
	}

	if tracker.CanRedo() {
		t.Error("Expected CanRedo to be false initially")
	}

	// Record a change
	change := &ui.Change{
		Type:     ui.ChangeTypeFieldUpdate,
		NodeID:   "test-node",
		Field:    "name",
		OldValue: "old",
		NewValue: "new",
	}

	tracker.RecordChange(change)

	// Test after recording change
	if !tracker.CanUndo() {
		t.Error("Expected CanUndo to be true after recording change")
	}

	if tracker.CanRedo() {
		t.Error("Expected CanRedo to be false after recording change")
	}

	// Test undo
	undoChange := tracker.Undo()
	if undoChange == nil {
		t.Error("Expected undo to return a change")
	}

	if undoChange.NodeID != change.NodeID {
		t.Errorf("Expected undo change NodeID %s, got %s", change.NodeID, undoChange.NodeID)
	}

	// Test redo
	if !tracker.CanRedo() {
		t.Error("Expected CanRedo to be true after undo")
	}

	redoChange := tracker.Redo()
	if redoChange == nil {
		t.Error("Expected redo to return a change")
	}

	if redoChange.NodeID != change.NodeID {
		t.Errorf("Expected redo change NodeID %s, got %s", change.NodeID, redoChange.NodeID)
	}
}

func TestChangeTracker_MaxLevels(t *testing.T) {
	maxLevels := 3
	tracker := ui.NewChangeTracker(maxLevels)

	// Add more changes than max levels
	for i := 0; i < maxLevels+2; i++ {
		change := &ui.Change{
			Type:     ui.ChangeTypeFieldUpdate,
			NodeID:   "test-node",
			Field:    "name",
			OldValue: "old",
			NewValue: "new",
		}
		tracker.RecordChange(change)
	}

	// Verify max levels is respected
	undoCount := 0
	for tracker.CanUndo() {
		tracker.Undo()
		undoCount++
	}

	if undoCount > maxLevels {
		t.Errorf("Expected max %d undo levels, got %d", maxLevels, undoCount)
	}
}

func TestChangeTracker_Clear(t *testing.T) {
	tracker := ui.NewChangeTracker(10)

	// Add some changes
	for i := 0; i < 5; i++ {
		change := &ui.Change{
			Type:     ui.ChangeTypeFieldUpdate,
			NodeID:   "test-node",
			Field:    "name",
			OldValue: "old",
			NewValue: "new",
		}
		tracker.RecordChange(change)
	}

	// Verify we can undo
	if !tracker.CanUndo() {
		t.Error("Expected to be able to undo before clear")
	}

	// Clear the tracker
	tracker.Clear()

	// Verify we can't undo after clear
	if tracker.CanUndo() {
		t.Error("Expected CanUndo to be false after clear")
	}

	if tracker.CanRedo() {
		t.Error("Expected CanRedo to be false after clear")
	}
}

// Unit Tests for TemplateManager (No UI dependencies)
func TestTemplateManager_Creation(t *testing.T) {
	logger := createTestLoggerSimple()
	templateManager := ui.NewTemplateManager(logger)

	if templateManager == nil {
		t.Fatal("Expected TemplateManager to be created, got nil")
	}

	// Test getting template names
	names := templateManager.GetTemplateNames()
	if len(names) == 0 {
		t.Error("Expected template manager to have default templates")
	}
}

func TestTemplateManager_CreateFromTemplate(t *testing.T) {
	logger := createTestLoggerSimple()
	templateManager := ui.NewTemplateManager(logger)

	// Test creating from default template
	testPlan := templateManager.CreateFromTemplate("basic")
	if testPlan == nil {
		t.Error("Expected template manager to create test plan from template")
	}

	if testPlan.Name == "" {
		t.Error("Expected created test plan to have a name")
	}

	// Test creating from non-existent template (should fallback)
	fallbackPlan := templateManager.CreateFromTemplate("non-existent")
	if fallbackPlan == nil {
		t.Error("Expected template manager to create fallback test plan")
	}
}

func TestTemplateManager_SaveAndLoadTemplate(t *testing.T) {
	logger := createTestLoggerSimple()
	templateManager := ui.NewTemplateManager(logger)

	testPlan := createSampleTestPlanSimple()
	metadata := &ui.TemplateMetadata{
		Name:        "test-template",
		Description: "A test template",
		Category:    "testing",
		Tags:        []string{"test", "example"},
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Version:     "1.0.0",
		Author:      "test-author",
	}

	// Save template
	err := templateManager.SaveTemplate("test-template", testPlan, metadata)
	if err != nil {
		t.Fatalf("Failed to save template: %v", err)
	}

	// Load template
	loadedPlan, loadedMetadata, err := templateManager.GetTemplate("test-template")
	if err != nil {
		t.Fatalf("Failed to load template: %v", err)
	}

	if loadedPlan == nil {
		t.Error("Expected loaded template to not be nil")
	}

	if loadedMetadata == nil {
		t.Error("Expected loaded metadata to not be nil")
	}

	if loadedMetadata.Name != metadata.Name {
		t.Errorf("Expected metadata name %s, got %s", metadata.Name, loadedMetadata.Name)
	}
}

func TestTemplateManager_GetCategories(t *testing.T) {
	logger := createTestLoggerSimple()
	templateManager := ui.NewTemplateManager(logger)

	categories := templateManager.GetCategories()
	if len(categories) == 0 {
		t.Error("Expected template manager to have categories")
	}

	// Check that "basic" category exists (from default templates)
	found := false
	for _, category := range categories {
		if category == "basic" {
			found = true
			break
		}
	}
	if !found {
		t.Error("Expected to find 'basic' category in template manager")
	}
}

// Unit Tests for TreeNode (No UI dependencies)
func TestTreeNode_BasicOperations(t *testing.T) {
	// Create parent node
	parent := ui.NewTreeNode("parent", ui.NodeTypeTestPlan, "Parent Node", nil)

	if parent == nil {
		t.Fatal("Expected parent node to be created, got nil")
	}

	// Test basic properties
	if parent.GetID() != "parent" {
		t.Errorf("Expected node ID 'parent', got %s", parent.GetID())
	}

	if parent.GetDisplayName() != "Parent Node" {
		t.Errorf("Expected node display name 'Parent Node', got %s", parent.GetDisplayName())
	}

	// Create child node
	child := ui.NewTreeNode("child", ui.NodeTypeScenario, "Child Node", nil)

	// Add child to parent
	parent.AddChild(child)

	// Test parent-child relationship
	children := parent.GetChildren()
	if len(children) != 1 {
		t.Errorf("Expected parent to have 1 child, got %d", len(children))
	}

	if children[0] != child {
		t.Error("Expected first child to be the added child")
	}

	if child.Parent != parent {
		t.Error("Expected child's parent to be the parent node")
	}

	// Test removing child
	parent.RemoveChild(child)

	children = parent.GetChildren()
	if len(children) != 0 {
		t.Errorf("Expected parent to have 0 children after removal, got %d", len(children))
	}

	if child.Parent != nil {
		t.Error("Expected child's parent to be nil after removal")
	}
}

func TestTreeNode_ValidationState(t *testing.T) {
	node := ui.NewTreeNode("test", ui.NodeTypeTestPlan, "Test Node", nil)

	// Test initial validation state
	if node.HasErrors {
		t.Error("Expected node to not have errors initially")
	}

	if node.HasWarnings {
		t.Error("Expected node to not have warnings initially")
	}

	// Test setting validation state
	node.SetValidationState(true, false)
	if !node.HasErrors {
		t.Error("Expected node to have errors after setting validation state")
	}

	if node.HasWarnings {
		t.Error("Expected node to not have warnings")
	}

	// Test setting warnings
	node.SetValidationState(false, true)
	if node.HasErrors {
		t.Error("Expected node to not have errors")
	}

	if !node.HasWarnings {
		t.Error("Expected node to have warnings after setting validation state")
	}
}

func TestTreeNode_HierarchyOperations(t *testing.T) {
	// Create a more complex hierarchy
	root := ui.NewTreeNode("root", ui.NodeTypeTestPlan, "Root", nil)
	scenario1 := ui.NewTreeNode("scenario1", ui.NodeTypeScenario, "Scenario 1", nil)
	scenario2 := ui.NewTreeNode("scenario2", ui.NodeTypeScenario, "Scenario 2", nil)
	request1 := ui.NewTreeNode("request1", ui.NodeTypeRequest, "Request 1", nil)
	request2 := ui.NewTreeNode("request2", ui.NodeTypeRequest, "Request 2", nil)

	// Build hierarchy
	root.AddChild(scenario1)
	root.AddChild(scenario2)
	scenario1.AddChild(request1)
	scenario1.AddChild(request2)

	// Test hierarchy
	if len(root.GetChildren()) != 2 {
		t.Errorf("Expected root to have 2 children, got %d", len(root.GetChildren()))
	}

	if len(scenario1.GetChildren()) != 2 {
		t.Errorf("Expected scenario1 to have 2 children, got %d", len(scenario1.GetChildren()))
	}

	if len(scenario2.GetChildren()) != 0 {
		t.Errorf("Expected scenario2 to have 0 children, got %d", len(scenario2.GetChildren()))
	}

	// Test parent relationships
	if scenario1.Parent != root {
		t.Error("Expected scenario1's parent to be root")
	}

	if request1.Parent != scenario1 {
		t.Error("Expected request1's parent to be scenario1")
	}
}

// Unit Tests for FormBuilder (Minimal UI dependencies)
func TestFormBuilder_Creation(t *testing.T) {
	logger := createTestLoggerSimple()
	validator := createTestValidationEngineSimple()

	formBuilder := ui.NewFormBuilder(validator, logger)

	if formBuilder == nil {
		t.Fatal("Expected FormBuilder to be created, got nil")
	}
}

func TestFormBuilder_ValidateForm(t *testing.T) {
	logger := createTestLoggerSimple()
	validator := createTestValidationEngineSimple()
	formBuilder := ui.NewFormBuilder(validator, logger)

	testPlan := createSampleTestPlanSimple()
	formBuilder.BuildForm(testPlan)

	// Test form validation
	isValid, errors := formBuilder.ValidateForm()
	if !isValid {
		t.Errorf("Expected form to be valid, got errors: %v", errors)
	}
}

// Unit Tests for Advanced Editing Features (No UI dependencies)
func TestCompletionProviders(t *testing.T) {
	// Test YAML completion provider
	yamlProvider := ui.NewYAMLCompletionProvider()
	if yamlProvider == nil {
		t.Error("Expected YAML completion provider to be created")
	}

	completions := yamlProvider.GetCompletions("na", "")
	if len(completions) == 0 {
		t.Error("Expected YAML completions for 'na' prefix")
	}

	help := yamlProvider.GetHelp("name")
	if help == "" {
		t.Error("Expected help for 'name' keyword")
	}

	// Test JSON completion provider
	jsonProvider := ui.NewJSONCompletionProvider()
	if jsonProvider == nil {
		t.Error("Expected JSON completion provider to be created")
	}

	completions = jsonProvider.GetCompletions("tr", "")
	if len(completions) == 0 {
		t.Error("Expected JSON completions for 'tr' prefix")
	}

	// Test JavaScript completion provider
	jsProvider := ui.NewJavaScriptCompletionProvider()
	if jsProvider == nil {
		t.Error("Expected JavaScript completion provider to be created")
	}

	completions = jsProvider.GetCompletions("fu", "")
	if len(completions) == 0 {
		t.Error("Expected JavaScript completions for 'fu' prefix")
	}
}

func TestHelpProviders(t *testing.T) {
	// Test YAML help provider
	yamlHelp := ui.NewYAMLHelpProvider()
	if yamlHelp == nil {
		t.Error("Expected YAML help provider to be created")
	}

	help := yamlHelp.GetHelp("scenarios")
	if help == "" {
		t.Error("Expected help for 'scenarios' keyword")
	}

	// Test JSON help provider
	jsonHelp := ui.NewJSONHelpProvider()
	if jsonHelp == nil {
		t.Error("Expected JSON help provider to be created")
	}

	help = jsonHelp.GetHelp("object")
	if help == "" {
		t.Error("Expected help for 'object' keyword")
	}

	// Test JavaScript help provider
	jsHelp := ui.NewJavaScriptHelpProvider()
	if jsHelp == nil {
		t.Error("Expected JavaScript help provider to be created")
	}

	help = jsHelp.GetHelp("function")
	if help == "" {
		t.Error("Expected help for 'function' keyword")
	}
}

// Performance Tests
func BenchmarkChangeTracker_RecordChange(b *testing.B) {
	tracker := ui.NewChangeTracker(100)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		change := &ui.Change{
			Type:     ui.ChangeTypeFieldUpdate,
			NodeID:   "test-node",
			Field:    "name",
			OldValue: "old",
			NewValue: "new",
		}
		tracker.RecordChange(change)
	}
}

func BenchmarkTemplateManager_CreateFromTemplate(b *testing.B) {
	logger := createTestLoggerSimple()
	templateManager := ui.NewTemplateManager(logger)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		testPlan := templateManager.CreateFromTemplate("basic")
		if testPlan == nil {
			b.Error("Expected test plan to be created from template")
		}
	}
}

func BenchmarkTreeNode_AddRemoveChild(b *testing.B) {
	parent := ui.NewTreeNode("parent", ui.NodeTypeTestPlan, "Parent", nil)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		child := ui.NewTreeNode("child", ui.NodeTypeScenario, "Child", nil)
		parent.AddChild(child)
		parent.RemoveChild(child)
	}
}

// Integration Tests (Minimal UI dependencies)
func TestTemplateManager_Integration(t *testing.T) {
	logger := createTestLoggerSimple()
	templateManager := ui.NewTemplateManager(logger)

	// Create a test plan
	testPlan := createSampleTestPlanSimple()

	// Save as template
	metadata := &ui.TemplateMetadata{
		Name:        "integration-test",
		Description: "Integration test template",
		Category:    "testing",
		Tags:        []string{"integration", "test"},
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Version:     "1.0.0",
		Author:      "test-suite",
	}

	err := templateManager.SaveTemplate("integration-test", testPlan, metadata)
	if err != nil {
		t.Fatalf("Failed to save template: %v", err)
	}

	// Verify it appears in template list
	names := templateManager.GetTemplateNames()
	found := false
	for _, name := range names {
		if name == "integration-test" {
			found = true
			break
		}
	}
	if !found {
		t.Error("Expected to find saved template in template list")
	}

	// Load and verify
	loadedPlan, loadedMetadata, err := templateManager.GetTemplate("integration-test")
	if err != nil {
		t.Fatalf("Failed to load template: %v", err)
	}

	if loadedPlan.Name != testPlan.Name {
		t.Errorf("Expected loaded plan name %s, got %s", testPlan.Name, loadedPlan.Name)
	}

	if loadedMetadata.Category != metadata.Category {
		t.Errorf("Expected loaded metadata category %s, got %s", metadata.Category, loadedMetadata.Category)
	}

	// Clean up
	err = templateManager.DeleteTemplate("integration-test")
	if err != nil {
		t.Fatalf("Failed to delete template: %v", err)
	}
}

func TestChangeTracker_Integration(t *testing.T) {
	tracker := ui.NewChangeTracker(5)

	// Simulate a series of field updates
	fields := []string{"name", "description", "method", "url", "body"}

	for i, field := range fields {
		change := &ui.Change{
			Type:     ui.ChangeTypeFieldUpdate,
			NodeID:   "request-1",
			Field:    field,
			OldValue: "old-" + field,
			NewValue: "new-" + field,
		}
		tracker.RecordChange(change)

		if !tracker.CanUndo() {
			t.Errorf("Expected to be able to undo after change %d", i+1)
		}
	}

	// Undo all changes
	undoCount := 0
	for tracker.CanUndo() {
		change := tracker.Undo()
		if change == nil {
			t.Error("Expected undo to return a change")
		}
		undoCount++
	}

	if undoCount != len(fields) {
		t.Errorf("Expected to undo %d changes, got %d", len(fields), undoCount)
	}

	// Redo all changes
	redoCount := 0
	for tracker.CanRedo() {
		change := tracker.Redo()
		if change == nil {
			t.Error("Expected redo to return a change")
		}
		redoCount++
	}

	if redoCount != len(fields) {
		t.Errorf("Expected to redo %d changes, got %d", len(fields), redoCount)
	}
}
