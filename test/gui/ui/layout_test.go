//go:build gui
// +build gui

package ui

import (
	"log"
	"testing"

	"neuralmetergo/internal/ui"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/test"
	"fyne.io/fyne/v2/widget"
)

func TestNewLayoutManager(t *testing.T) {
	// Create test app
	testApp := test.NewApp()
	defer testApp.Quit()

	// Create NeuralMeterApp instance
	config := ui.DefaultAppConfig()
	logger := log.Default()

	neuralApp, err := ui.NewNeuralMeterApp(config, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeterApp: %v", err)
	}

	// Test layout manager creation
	layoutManager := ui.NewLayoutManager(neuralApp, logger)
	if layoutManager == nil {
		t.Fatal("LayoutManager should not be nil")
	}

	// Test that main container is created
	mainContainer := layoutManager.GetMainContainer()
	if mainContainer == nil {
		t.Fatal("Main container should not be nil")
	}

	// Test that navigation tabs are created
	navTabs := layoutManager.GetNavigationTabs()
	if navTabs == nil {
		t.Fatal("Navigation tabs should not be nil")
	}
}

func TestLayoutManagerPanels(t *testing.T) {
	// Create test app and layout manager
	testApp := test.NewApp()
	defer testApp.Quit()

	config := ui.DefaultAppConfig()
	logger := log.Default()

	neuralApp, err := ui.NewNeuralMeterApp(config, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeterApp: %v", err)
	}

	layoutManager := ui.NewLayoutManager(neuralApp, logger)

	// Test adding a custom panel
	testPanel := &ui.DockablePanel{
		ID:          "test-panel",
		Title:       "Test Panel",
		Content:     widget.NewLabel("Test content"),
		Position:    ui.PanelPositionLeft,
		Size:        fyne.NewSize(200, 300),
		IsVisible:   true,
		IsCollapsed: false,
		MinSize:     fyne.NewSize(150, 200),
		MaxSize:     fyne.NewSize(400, 600),
	}

	layoutManager.AddPanel("test-panel", testPanel)

	// Test retrieving the panel
	retrievedPanel := layoutManager.GetPanel("test-panel")
	if retrievedPanel == nil {
		t.Fatal("Retrieved panel should not be nil")
	}

	if retrievedPanel.ID != "test-panel" {
		t.Errorf("Expected panel ID 'test-panel', got '%s'", retrievedPanel.ID)
	}

	// Test removing the panel
	layoutManager.RemovePanel("test-panel")
	removedPanel := layoutManager.GetPanel("test-panel")
	if removedPanel != nil {
		t.Error("Panel should be nil after removal")
	}
}

func TestLayoutManagerTabs(t *testing.T) {
	// Create test app and layout manager
	testApp := test.NewApp()
	defer testApp.Quit()

	config := ui.DefaultAppConfig()
	logger := log.Default()

	neuralApp, err := ui.NewNeuralMeterApp(config, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeterApp: %v", err)
	}

	layoutManager := ui.NewLayoutManager(neuralApp, logger)
	navTabs := layoutManager.GetNavigationTabs()

	initialTabCount := len(navTabs.Items)

	// Test adding a tab
	testContent := widget.NewLabel("Test tab content")
	layoutManager.AddTab("Test Tab", testContent)

	if len(navTabs.Items) != initialTabCount+1 {
		t.Errorf("Expected %d tabs, got %d", initialTabCount+1, len(navTabs.Items))
	}

	// Test setting active tab
	layoutManager.SetActiveTab(0)
	if navTabs.SelectedIndex() != 0 {
		t.Errorf("Expected active tab index 0, got %d", navTabs.SelectedIndex())
	}

	// Test removing a tab
	layoutManager.RemoveTab(0)
	if len(navTabs.Items) != initialTabCount {
		t.Errorf("Expected %d tabs after removal, got %d", initialTabCount, len(navTabs.Items))
	}
}

func TestLayoutManagerResponsive(t *testing.T) {
	// Create test app and layout manager
	testApp := test.NewApp()
	defer testApp.Quit()

	config := ui.DefaultAppConfig()
	logger := log.Default()

	neuralApp, err := ui.NewNeuralMeterApp(config, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeterApp: %v", err)
	}

	layoutManager := ui.NewLayoutManager(neuralApp, logger)

	// Test different layout types
	testCases := []struct {
		layoutType ui.LayoutType
		name       string
	}{
		{ui.LayoutTypeDesktop, "Desktop"},
		{ui.LayoutTypeLaptop, "Laptop"},
		{ui.LayoutTypeTablet, "Tablet"},
		{ui.LayoutTypeCompact, "Compact"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			layoutManager.ApplyLayout(tc.layoutType)
			// Layout should apply without errors
			// In a real test, we would check panel visibility states
		})
	}
}

func TestLayoutManagerBreadcrumbs(t *testing.T) {
	// Create test app and layout manager
	testApp := test.NewApp()
	defer testApp.Quit()

	config := ui.DefaultAppConfig()
	logger := log.Default()

	neuralApp, err := ui.NewNeuralMeterApp(config, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeterApp: %v", err)
	}

	layoutManager := ui.NewLayoutManager(neuralApp, logger)

	// Test updating breadcrumbs
	breadcrumbs := []ui.BreadcrumbItem{
		{Label: "Home", Callback: func() {}},
		{Label: "Test Plans", Callback: func() {}},
		{Label: "Current Test", Callback: func() {}},
	}

	layoutManager.UpdateBreadcrumbs(breadcrumbs)
	// Breadcrumbs should update without errors
}

func TestLayoutManagerStatusBar(t *testing.T) {
	// Create test app and layout manager
	testApp := test.NewApp()
	defer testApp.Quit()

	config := ui.DefaultAppConfig()
	logger := log.Default()

	neuralApp, err := ui.NewNeuralMeterApp(config, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeterApp: %v", err)
	}

	layoutManager := ui.NewLayoutManager(neuralApp, logger)

	// Test updating status
	layoutManager.UpdateStatus("Test status message")

	// Test updating progress
	layoutManager.UpdateProgress(0.5, true)
	layoutManager.UpdateProgress(1.0, false)

	// Test updating info labels
	layoutManager.UpdateInfoLabel("memory", "Memory: 512 MB")
	layoutManager.UpdateInfoLabel("tasks", "Tasks: 5")
	layoutManager.UpdateInfoLabel("connection", "Connected")

	// Status bar updates should complete without errors
}

func TestLayoutManagerFullscreen(t *testing.T) {
	// Create test app and layout manager
	testApp := test.NewApp()
	defer testApp.Quit()

	config := ui.DefaultAppConfig()
	logger := log.Default()

	neuralApp, err := ui.NewNeuralMeterApp(config, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeterApp: %v", err)
	}

	layoutManager := ui.NewLayoutManager(neuralApp, logger)

	// Test fullscreen toggle
	layoutManager.ToggleFullscreen()
	// Should toggle without errors

	// Toggle back
	layoutManager.ToggleFullscreen()
	// Should toggle back without errors
}

func TestLayoutManagerStatePersistence(t *testing.T) {
	// Create test app and layout manager
	testApp := test.NewApp()
	defer testApp.Quit()

	config := ui.DefaultAppConfig()
	logger := log.Default()

	neuralApp, err := ui.NewNeuralMeterApp(config, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeterApp: %v", err)
	}

	layoutManager := ui.NewLayoutManager(neuralApp, logger)

	// Test saving layout state
	err = layoutManager.SaveLayoutState()
	if err != nil {
		t.Errorf("SaveLayoutState should not return error: %v", err)
	}

	// Test loading layout state
	err = layoutManager.LoadLayoutState()
	if err != nil {
		t.Errorf("LoadLayoutState should not return error: %v", err)
	}
}

// Benchmark tests for performance
func BenchmarkLayoutManagerCreation(b *testing.B) {
	testApp := test.NewApp()
	defer testApp.Quit()

	config := ui.DefaultAppConfig()
	logger := log.Default()

	neuralApp, err := ui.NewNeuralMeterApp(config, logger)
	if err != nil {
		b.Fatalf("Failed to create NeuralMeterApp: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		layoutManager := ui.NewLayoutManager(neuralApp, logger)
		_ = layoutManager
	}
}

func BenchmarkLayoutSwitch(b *testing.B) {
	testApp := test.NewApp()
	defer testApp.Quit()

	config := ui.DefaultAppConfig()
	logger := log.Default()

	neuralApp, err := ui.NewNeuralMeterApp(config, logger)
	if err != nil {
		b.Fatalf("Failed to create NeuralMeterApp: %v", err)
	}

	layoutManager := ui.NewLayoutManager(neuralApp, logger)

	layouts := []ui.LayoutType{
		ui.LayoutTypeDesktop,
		ui.LayoutTypeLaptop,
		ui.LayoutTypeTablet,
		ui.LayoutTypeCompact,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		layoutManager.ApplyLayout(layouts[i%len(layouts)])
	}
}

func TestLayoutTypes(t *testing.T) {
	// Test layout type constants
	if ui.LayoutTypeDesktop != 0 {
		t.Errorf("Expected LayoutTypeDesktop to be 0, got %d", ui.LayoutTypeDesktop)
	}
	if ui.LayoutTypeLaptop != 1 {
		t.Errorf("Expected LayoutTypeLaptop to be 1, got %d", ui.LayoutTypeLaptop)
	}
	if ui.LayoutTypeTablet != 2 {
		t.Errorf("Expected LayoutTypeTablet to be 2, got %d", ui.LayoutTypeTablet)
	}
	if ui.LayoutTypeCompact != 3 {
		t.Errorf("Expected LayoutTypeCompact to be 3, got %d", ui.LayoutTypeCompact)
	}
}

func TestPanelPositions(t *testing.T) {
	// Test panel position constants
	if ui.PanelPositionLeft != 0 {
		t.Errorf("Expected PanelPositionLeft to be 0, got %d", ui.PanelPositionLeft)
	}
	if ui.PanelPositionRight != 1 {
		t.Errorf("Expected PanelPositionRight to be 1, got %d", ui.PanelPositionRight)
	}
	if ui.PanelPositionBottom != 2 {
		t.Errorf("Expected PanelPositionBottom to be 2, got %d", ui.PanelPositionBottom)
	}
	if ui.PanelPositionTop != 3 {
		t.Errorf("Expected PanelPositionTop to be 3, got %d", ui.PanelPositionTop)
	}
	if ui.PanelPositionFloating != 4 {
		t.Errorf("Expected PanelPositionFloating to be 4, got %d", ui.PanelPositionFloating)
	}
}

func TestDockablePanelStruct(t *testing.T) {
	// Test DockablePanel struct creation
	panel := &ui.DockablePanel{
		ID:          "test-panel",
		Title:       "Test Panel",
		Content:     widget.NewLabel("Test content"),
		Position:    ui.PanelPositionLeft,
		Size:        fyne.NewSize(200, 300),
		IsVisible:   true,
		IsCollapsed: false,
		MinSize:     fyne.NewSize(150, 200),
		MaxSize:     fyne.NewSize(400, 600),
	}

	if panel.ID != "test-panel" {
		t.Errorf("Expected panel ID 'test-panel', got '%s'", panel.ID)
	}

	if panel.Title != "Test Panel" {
		t.Errorf("Expected panel title 'Test Panel', got '%s'", panel.Title)
	}

	if panel.Position != ui.PanelPositionLeft {
		t.Errorf("Expected panel position %d, got %d", ui.PanelPositionLeft, panel.Position)
	}

	if !panel.IsVisible {
		t.Error("Expected panel to be visible")
	}

	if panel.IsCollapsed {
		t.Error("Expected panel to not be collapsed")
	}
}

func TestBreadcrumbItem(t *testing.T) {
	// Test BreadcrumbItem struct
	callbackCalled := false
	breadcrumb := ui.BreadcrumbItem{
		Label: "Home",
		Callback: func() {
			callbackCalled = true
		},
	}

	if breadcrumb.Label != "Home" {
		t.Errorf("Expected breadcrumb label 'Home', got '%s'", breadcrumb.Label)
	}

	// Test callback
	breadcrumb.Callback()
	if !callbackCalled {
		t.Error("Expected callback to be called")
	}
}

func TestBreakpointTypes(t *testing.T) {
	// Test breakpoint type constants
	if ui.BreakpointDesktop != 0 {
		t.Errorf("Expected BreakpointDesktop to be 0, got %d", ui.BreakpointDesktop)
	}
	if ui.BreakpointLaptop != 1 {
		t.Errorf("Expected BreakpointLaptop to be 1, got %d", ui.BreakpointLaptop)
	}
	if ui.BreakpointTablet != 2 {
		t.Errorf("Expected BreakpointTablet to be 2, got %d", ui.BreakpointTablet)
	}
	if ui.BreakpointCompact != 3 {
		t.Errorf("Expected BreakpointCompact to be 3, got %d", ui.BreakpointCompact)
	}
}

// Test configuration validation
func TestAppConfigForLayout(t *testing.T) {
	config := ui.DefaultAppConfig()

	// Verify window dimensions are suitable for layout system
	if config.WindowWidth < 800 {
		t.Errorf("Window width %f is too small for layout system", config.WindowWidth)
	}

	if config.WindowHeight < 600 {
		t.Errorf("Window height %f is too small for layout system", config.WindowHeight)
	}

	if !config.WindowResizable {
		t.Error("Window should be resizable for responsive layout")
	}
}

// Test that layout components can be created without GUI
func TestLayoutComponentCreation(t *testing.T) {
	// Test that we can create basic components without initializing GUI
	label := widget.NewLabel("Test")
	if label == nil {
		t.Fatal("Failed to create label widget")
	}

	// Test size creation
	size := fyne.NewSize(200, 300)
	if size.Width != 200 || size.Height != 300 {
		t.Errorf("Expected size 200x300, got %fx%f", size.Width, size.Height)
	}
}

// Integration test placeholder
func TestLayoutIntegration(t *testing.T) {
	// This test verifies that the layout system integrates properly
	// without actually creating GUI components

	logger := log.Default()
	if logger == nil {
		t.Fatal("Logger should not be nil")
	}

	// Test that we can create the basic structures needed for layout
	config := ui.DefaultAppConfig()
	if config == nil {
		t.Fatal("Config should not be nil")
	}

	// Verify all required config fields are present
	if config.AppID == "" {
		t.Error("AppID should not be empty")
	}

	if config.AppName == "" {
		t.Error("AppName should not be empty")
	}

	if config.AppVersion == "" {
		t.Error("AppVersion should not be empty")
	}
}

// Test layout state persistence data structures
func TestLayoutStatePersistence(t *testing.T) {
	// Test that panel state can be represented for persistence
	panel := &ui.DockablePanel{
		ID:          "test-panel",
		Title:       "Test Panel",
		Position:    ui.PanelPositionLeft,
		Size:        fyne.NewSize(250, 400),
		IsVisible:   true,
		IsCollapsed: false,
		MinSize:     fyne.NewSize(200, 300),
		MaxSize:     fyne.NewSize(400, 800),
	}

	// Verify panel state can be serialized (basic validation)
	if panel.ID == "" {
		t.Error("Panel ID should not be empty for persistence")
	}

	// Test size conversion for persistence
	width := float64(panel.Size.Width)
	height := float64(panel.Size.Height)

	if width <= 0 || height <= 0 {
		t.Error("Panel dimensions should be positive for persistence")
	}

	// Test conversion back
	restoredSize := fyne.NewSize(float32(width), float32(height))
	if restoredSize.Width != panel.Size.Width || restoredSize.Height != panel.Size.Height {
		t.Error("Size conversion for persistence should be reversible")
	}
}

// Benchmark test for layout switching logic
func BenchmarkLayoutTypeSwitch(b *testing.B) {
	layouts := []ui.LayoutType{
		ui.LayoutTypeDesktop,
		ui.LayoutTypeLaptop,
		ui.LayoutTypeTablet,
		ui.LayoutTypeCompact,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		layoutType := layouts[i%len(layouts)]

		// Simulate layout type determination logic
		var result string
		switch layoutType {
		case ui.LayoutTypeDesktop:
			result = "desktop"
		case ui.LayoutTypeLaptop:
			result = "laptop"
		case ui.LayoutTypeTablet:
			result = "tablet"
		case ui.LayoutTypeCompact:
			result = "compact"
		}

		// Prevent optimization
		_ = result
	}
}
