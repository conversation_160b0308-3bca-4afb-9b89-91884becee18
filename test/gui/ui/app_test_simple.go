//go:build gui
// +build gui

package ui

import (
	"testing"

	"neuralmetergo/internal/ui"

	"fyne.io/fyne/v2/test"
)

func TestDefaultAppConfig_Simple(t *testing.T) {
	config := ui.DefaultAppConfig()

	if config == nil {
		t.Fatal("DefaultAppConfig should not return nil")
	}

	if config.AppID != "com.neuralmeter.loadtester" {
		t.<PERSON><PERSON><PERSON>("Expected AppID 'com.neuralmeter.loadtester', got '%s'", config.AppID)
	}

	if config.AppName != "NeuralMeter" {
		t.<PERSON><PERSON><PERSON>("Expected AppName 'NeuralMeter', got '%s'", config.AppName)
	}
}

func TestThemeManager_Simple(t *testing.T) {
	// Use test app instead of real app
	testApp := test.NewApp()
	defer testApp.Quit()

	tm := ui.NewThemeManager(testApp, nil)

	if tm == nil {
		t.<PERSON>al("NewThemeManager should not return nil")
	}

	themes := tm.GetAvailableThemes()
	if len(themes) == 0 {
		t.Error("Should have at least one available theme")
	}

	// Test setting a theme
	err := tm.SetTheme("light")
	if err != nil {
		t.Errorf("Failed to set light theme: %v", err)
	}

	if tm.GetCurrentTheme() != "light" {
		t.Errorf("Expected current theme to be 'light', got '%s'", tm.GetCurrentTheme())
	}
}

func TestThemeManager_ThemeSwitcher(t *testing.T) {
	testApp := test.NewApp()
	defer testApp.Quit()

	tm := ui.NewThemeManager(testApp, nil)
	switcher := tm.CreateThemeSwitcher()

	if switcher == nil {
		t.Fatal("CreateThemeSwitcher should not return nil")
	}

	if len(switcher.Options) == 0 {
		t.Error("Theme switcher should have options")
	}
}

func TestCustomThemes_Structure(t *testing.T) {
	// Test that custom themes implement the interface properly
	themes := []interface{}{
		&ui.MaterialDarkTheme{},
		&ui.MaterialLightTheme{},
		&ui.NeuralMeterTheme{},
	}

	for i, theme := range themes {
		if theme == nil {
			t.Errorf("Theme %d should not be nil", i)
		}
	}
}

func TestAppConfig_Validation(t *testing.T) {
	config := &ui.AppConfig{
		AppID:           "com.test.app",
		AppName:         "TestApp",
		AppVersion:      "2.0.0",
		DefaultTheme:    "light",
		WindowWidth:     800,
		WindowHeight:    600,
		WindowCentered:  false,
		WindowResizable: false,
	}

	if config.WindowWidth <= 0 {
		t.Error("Window width should be positive")
	}

	if config.WindowHeight <= 0 {
		t.Error("Window height should be positive")
	}

	if config.AppName == "" {
		t.Error("App name should not be empty")
	}
}
