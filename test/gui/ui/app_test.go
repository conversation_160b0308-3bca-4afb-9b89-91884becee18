//go:build gui
// +build gui

package ui

import (
	"log"
	"os"
	"testing"

	"neuralmetergo/internal/ui"

	"fyne.io/fyne/v2/test"
)

func TestDefaultAppConfig(t *testing.T) {
	config := ui.DefaultAppConfig()

	if config == nil {
		t.Fatal("DefaultAppConfig should not return nil")
	}

	if config.AppID != "com.neuralmeter.loadtester" {
		t.<PERSON><PERSON>("Expected AppID 'com.neuralmeter.loadtester', got '%s'", config.AppID)
	}

	if config.AppName != "NeuralMeter" {
		t.<PERSON>rf("Expected AppName 'NeuralMeter', got '%s'", config.AppName)
	}

	if config.WindowWidth != 1200 {
		t.Errorf("Expected WindowWidth 1200, got %f", config.WindowWidth)
	}

	if config.WindowHeight != 800 {
		t.<PERSON>rrorf("Expected WindowHeight 800, got %f", config.WindowHeight)
	}

	if !config.WindowCentered {
		t.Error("Expected WindowCentered to be true")
	}

	if !config.WindowResizable {
		t.Error("Expected WindowResizable to be true")
	}
}

func TestNewNeuralMeterApp_WithDefaults(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	app, err := ui.NewNeuralMeterApp(nil, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeter app: %v", err)
	}

	if app == nil {
		t.Fatal("NewNeuralMeterApp should not return nil")
	}

	if app.GetApp() == nil {
		t.Fatal("App should have a valid Fyne app")
	}

	if app.GetWindow() == nil {
		t.Fatal("App should have a valid window")
	}

	if app.IsRunning() {
		t.Error("App should not be running initially")
	}
}

func TestNewNeuralMeterApp_WithCustomConfig(t *testing.T) {
	config := &ui.AppConfig{
		AppID:           "com.test.app",
		AppName:         "TestApp",
		AppVersion:      "2.0.0",
		DefaultTheme:    "light",
		WindowWidth:     800,
		WindowHeight:    600,
		WindowCentered:  false,
		WindowResizable: false,
	}

	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	app, err := ui.NewNeuralMeterApp(config, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeter app: %v", err)
	}

	if app.Config.AppName != "TestApp" {
		t.Errorf("Expected AppName 'TestApp', got '%s'", app.Config.AppName)
	}

	if app.Config.AppVersion != "2.0.0" {
		t.Errorf("Expected AppVersion '2.0.0', got '%s'", app.Config.AppVersion)
	}
}

func TestNewNeuralMeterApp_WithNilLogger(t *testing.T) {
	config := ui.DefaultAppConfig()

	// Should not panic with nil logger
	app, err := ui.NewNeuralMeterApp(config, nil)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeter app with nil logger: %v", err)
	}

	if app == nil {
		t.Fatal("NewNeuralMeterApp should not return nil even with nil logger")
	}
}

func TestNeuralMeterApp_WindowConfiguration(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	app, err := ui.NewNeuralMeterApp(nil, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeter app: %v", err)
	}

	window := app.GetWindow()
	if window == nil {
		t.Fatal("Window should not be nil")
	}

	// Test window size
	size := window.Canvas().Size()
	expectedWidth := float32(1200)
	expectedHeight := float32(800)

	if size.Width != expectedWidth {
		t.Errorf("Expected window width %f, got %f", expectedWidth, size.Width)
	}

	if size.Height != expectedHeight {
		t.Errorf("Expected window height %f, got %f", expectedHeight, size.Height)
	}
}

func TestNeuralMeterApp_ThemeManager(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	app, err := ui.NewNeuralMeterApp(nil, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeter app: %v", err)
	}

	if app.Themes == nil {
		t.Fatal("ThemeManager should not be nil")
	}

	// Test theme switching
	availableThemes := app.Themes.GetAvailableThemes()
	if len(availableThemes) == 0 {
		t.Error("Should have at least one available theme")
	}

	// Test setting a theme
	err = app.Themes.SetTheme("light")
	if err != nil {
		t.Errorf("Failed to set light theme: %v", err)
	}

	if app.Themes.GetCurrentTheme() != "light" {
		t.Errorf("Expected current theme to be 'light', got '%s'", app.Themes.GetCurrentTheme())
	}
}

func TestNeuralMeterApp_StateManagement(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	app, err := ui.NewNeuralMeterApp(nil, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeter app: %v", err)
	}

	// Initial state
	if app.IsStarted {
		t.Error("App should not be started initially")
	}

	if app.IsStopped {
		t.Error("App should not be stopped initially")
	}

	if app.IsRunning() {
		t.Error("App should not be running initially")
	}
}

func TestNeuralMeterApp_ApplicationLifecycle(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	app, err := ui.NewNeuralMeterApp(nil, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeter app: %v", err)
	}

	// Test quit functionality
	app.Quit()

	if !app.IsStopped {
		t.Error("App should be stopped after Quit()")
	}
}

func TestNeuralMeterApp_WindowContent(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	app, err := ui.NewNeuralMeterApp(nil, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeter app: %v", err)
	}

	window := app.GetWindow()
	content := window.Content()

	if content == nil {
		t.Fatal("Window content should not be nil")
	}

	// The content should be a Card widget
	// We can't easily test the specific content structure without more complex UI testing
	// but we can verify it exists and is not nil
}

// Integration test using Fyne's test driver
func TestNeuralMeterApp_Integration(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	// Create app with test driver
	app, err := ui.NewNeuralMeterApp(nil, logger)
	if err != nil {
		t.Fatalf("Failed to create NeuralMeter app: %v", err)
	}

	// Use Fyne's test driver for UI testing
	testApp := test.NewApp()
	testWindow := testApp.NewWindow("Test")
	testWindow.SetContent(app.GetWindow().Content())

	// Test that content is properly rendered
	content := testWindow.Content()
	if content == nil {
		t.Fatal("Test window content should not be nil")
	}

	// Cleanup
	testWindow.Close()
}
