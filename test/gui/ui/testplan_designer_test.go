//go:build gui
// +build gui

package ui

import (
	"fmt"
	"log"
	"os"
	"testing"
	"time"

	"neuralmetergo/internal/parser"
	"neuralmetergo/internal/ui"
	"neuralmetergo/internal/validation"

	"fyne.io/fyne/v2/test"
)

// Test helper functions
func createTestLogger() *log.Logger {
	return log.New(os.Stdout, "[TEST] ", log.LstdFlags)
}

func createTestApp() *ui.NeuralMeterApp {
	config := ui.DefaultAppConfig()
	mockApp, _ := ui.NewNeuralMeterApp(config, createTestLogger())
	return mockApp
}

func createTestValidationEngine() *validation.ValidationEngine {
	engine := validation.NewValidationEngine()
	return engine
}

func createSampleTestPlan() *parser.TestPlan {
	return &parser.TestPlan{
		Name:        "Comprehensive Test Plan",
		Description: "A comprehensive test plan for testing",
		Global: parser.Global{
			BaseURL: "https://api.test.com",
			Headers: map[string]string{
				"Content-Type": "application/json",
				"Accept":       "application/json",
			},
		},
		Scenarios: []parser.Scenario{
			{
				Name:        "Authentication Scenario",
				Description: "Test authentication endpoints",
				Requests: []parser.Request{
					{
						Name:   "Login Request",
						Method: "POST",
						URL:    "/auth/login",
						Headers: map[string]string{
							"Content-Type": "application/json",
						},
						Body: `{"username": "test", "password": "test123"}`,
						Assertions: []parser.Assertion{
							{
								Type:     "status",
								Operator: "equals",
								Value:    "200",
							},
							{
								Type:     "jsonpath",
								Operator: "exists",
								Value:    "$.token",
							},
						},
						Extract: []parser.Extract{
							{
								Name: "auth_token",
								Type: "json_path",
								Path: "$.token",
							},
						},
					},
					{
						Name:   "Profile Request",
						Method: "GET",
						URL:    "/user/profile",
						Headers: map[string]string{
							"Authorization": "Bearer {{auth_token}}",
						},
						Assertions: []parser.Assertion{
							{
								Type:     "status",
								Operator: "equals",
								Value:    "200",
							},
						},
					},
				},
			},
			{
				Name:        "Data Operations",
				Description: "Test CRUD operations",
				Requests: []parser.Request{
					{
						Name:   "Create Item",
						Method: "POST",
						URL:    "/items",
						Body:   `{"name": "Test Item", "description": "A test item"}`,
						Assertions: []parser.Assertion{
							{
								Type:     "status",
								Operator: "equals",
								Value:    "201",
							},
						},
					},
				},
			},
		},
		Variables: []parser.Variable{
			{
				Name:  "baseUrl",
				Value: "https://api.test.com",
			},
			{
				Name:  "timeout",
				Value: "30s",
			},
		},
		Output: parser.Output{
			Format: []string{"json", "junit"},
		},
	}
}

// TestTestPlanDesigner_Creation tests basic creation and initialization
func TestTestPlanDesigner_Creation(t *testing.T) {
	// Create test app
	testApp := test.NewApp()
	defer testApp.Quit()

	// Create logger
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	// Create mock NeuralMeterApp
	config := ui.DefaultAppConfig()
	mockApp, err := ui.NewNeuralMeterApp(config, logger)
	if err != nil {
		t.Fatalf("Failed to create mock app: %v", err)
	}

	// Create designer
	designer := ui.NewTestPlanDesigner(mockApp, logger)

	if designer == nil {
		t.Fatal("Expected designer to be created, got nil")
	}

	// Verify container is created
	container := designer.GetContainer()
	if container == nil {
		t.Error("Expected container to be created, got nil")
	}

	// Test initial state
	if designer.IsModified() {
		t.Error("Expected designer to not be modified initially")
	}

	if designer.CanUndo() {
		t.Error("Expected CanUndo to be false initially")
	}

	if designer.CanRedo() {
		t.Error("Expected CanRedo to be false initially")
	}
}

// TestTestPlanDesigner_LoadTestPlan tests loading a test plan
func TestTestPlanDesigner_LoadTestPlan(t *testing.T) {
	// Create test app
	testApp := test.NewApp()
	defer testApp.Quit()

	// Create logger
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	// Create mock NeuralMeterApp
	config := ui.DefaultAppConfig()
	mockApp, err := ui.NewNeuralMeterApp(config, logger)
	if err != nil {
		t.Fatalf("Failed to create mock app: %v", err)
	}

	// Create designer
	designer := ui.NewTestPlanDesigner(mockApp, logger)

	// Create test plan
	testPlan := createSampleTestPlan()

	// Load test plan
	err = designer.LoadTestPlan(testPlan)
	if err != nil {
		t.Fatalf("Failed to load test plan: %v", err)
	}

	// Verify test plan is loaded
	loadedPlan := designer.GetTestPlan()
	if loadedPlan == nil {
		t.Error("Expected test plan to be loaded, got nil")
	}

	if loadedPlan.Name != testPlan.Name {
		t.Errorf("Expected test plan name %s, got %s", testPlan.Name, loadedPlan.Name)
	}

	if len(loadedPlan.Scenarios) != len(testPlan.Scenarios) {
		t.Errorf("Expected %d scenarios, got %d", len(testPlan.Scenarios), len(loadedPlan.Scenarios))
	}
}

// TestTestPlanDesigner_EventCallbacks tests event callback functionality
func TestTestPlanDesigner_EventCallbacks(t *testing.T) {
	app := createTestApp()
	logger := createTestLogger()
	designer := ui.NewTestPlanDesigner(app, logger)

	var testPlanChanged *parser.TestPlan
	// var selectionChanged *ui.TreeNode
	// var validationUpdated *validation.ValidationResult

	// Set up callbacks
	designer.SetOnTestPlanChanged(func(plan *parser.TestPlan) {
		testPlanChanged = plan
	})

	designer.SetOnSelectionChanged(func(node *ui.TreeNode) {
		// selectionChanged = node
	})

	designer.SetOnValidationUpdate(func(result *validation.ValidationResult) {
		// validationUpdated = result
	})

	// Load test plan to trigger callbacks
	testPlan := createSampleTestPlan()
	err := designer.LoadTestPlan(testPlan)
	if err != nil {
		t.Fatalf("Failed to load test plan: %v", err)
	}

	// Check if callbacks were triggered
	if testPlanChanged == nil {
		t.Error("Expected test plan changed callback to be triggered")
	}

	if testPlanChanged != nil && testPlanChanged.Name != testPlan.Name {
		t.Errorf("Expected callback test plan name %s, got %s", testPlan.Name, testPlanChanged.Name)
	}
}

// TestTestPlanDesigner_TreeWidget tests tree widget functionality
func TestTestPlanDesigner_TreeWidget(t *testing.T) {
	// Create test app
	testApp := test.NewApp()
	defer testApp.Quit()

	// Create logger
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	// Create tree widget
	treeWidget := ui.NewTestPlanTreeWidget(logger)

	if treeWidget == nil {
		t.Fatal("Expected tree widget to be created, got nil")
	}

	// Create test plan
	testPlan := createSampleTestPlan()

	// Load test plan into tree
	err := treeWidget.LoadTestPlan(testPlan)
	if err != nil {
		t.Fatalf("Failed to load test plan into tree: %v", err)
	}

	// Verify container is created
	container := treeWidget.GetContainer()
	if container == nil {
		t.Error("Expected tree container to be created, got nil")
	}
}

// TestFormBuilder tests form builder functionality
func TestFormBuilder(t *testing.T) {
	// Create logger
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	// Create validation engine
	validator := createTestValidationEngine()

	// Create form builder with correct signature
	formBuilder := ui.NewFormBuilder(validator, logger)

	if formBuilder == nil {
		t.Fatal("Expected form builder to be created, got nil")
	}

	// Test building form with test plan
	testPlan := createSampleTestPlan()
	container := formBuilder.BuildForm(testPlan)

	if container == nil {
		t.Error("Expected form container to be created, got nil")
	}

	// Verify container has content
	if len(container.Objects) == 0 {
		t.Error("Expected form container to have content")
	}
}

// TestFormBuilder_FieldValidation tests form field validation
func TestFormBuilder_FieldValidation(t *testing.T) {
	logger := createTestLogger()
	validator := createTestValidationEngine()
	formBuilder := ui.NewFormBuilder(validator, logger)

	// var fieldChanged string
	// var valueChanged interface{}
	// var validationResult bool
	// var validationErrors []string

	// Set up callbacks
	formBuilder.SetFieldChangeCallback(func(field string, value interface{}) {
		// fieldChanged = field
		// valueChanged = value
	})

	formBuilder.SetValidationChangeCallback(func(valid bool, errors []string) {
		// validationResult = valid
		// validationErrors = errors
	})

	testPlan := createSampleTestPlan()
	formBuilder.BuildForm(testPlan)

	// Test form validation
	isValid, errors := formBuilder.ValidateForm()
	if !isValid {
		t.Errorf("Expected form to be valid, got errors: %v", errors)
	}
}

// TestTreeFormAdapter tests the TreeFormAdapter functionality
func TestTreeFormAdapter(t *testing.T) {
	logger := createTestLogger()
	validator := createTestValidationEngine()

	// Create TreeFormAdapter
	adapter := ui.NewTreeFormAdapter(validator, logger)
	if adapter == nil {
		t.Fatal("Expected TreeFormAdapter to be created, got nil")
	}

	// Create test node
	testNode := ui.NewTreeNode("test-node", ui.NodeTypeTestPlan, "Test Node", createSampleTestPlan())

	// Load node into adapter
	adapter.LoadNode(testNode)

	// Verify container is created
	container := adapter.GetContainer()
	if container == nil {
		t.Error("Expected adapter container to be created, got nil")
	}

	// Test callbacks
	// var fieldChanged string
	// var valueChanged interface{}
	// var validationCalled bool

	adapter.SetOnChange(func(field string, value interface{}) {
		// fieldChanged = field
		// valueChanged = value
	})

	adapter.SetOnValidation(func(valid bool, errors []string) {
		// validationCalled = true
	})
}

// TestChangeTracker tests change tracking functionality
func TestChangeTracker(t *testing.T) {
	// Create change tracker
	tracker := ui.NewChangeTracker(10)

	if tracker == nil {
		t.Fatal("Expected change tracker to be created, got nil")
	}

	// Test initial state
	if tracker.CanUndo() {
		t.Error("Expected CanUndo to be false initially")
	}

	if tracker.CanRedo() {
		t.Error("Expected CanRedo to be false initially")
	}

	// Record a change
	change := &ui.Change{
		Type:     ui.ChangeTypeFieldUpdate,
		NodeID:   "test-node",
		Field:    "name",
		OldValue: "old",
		NewValue: "new",
	}

	tracker.RecordChange(change)

	// Test after recording change
	if !tracker.CanUndo() {
		t.Error("Expected CanUndo to be true after recording change")
	}

	if tracker.CanRedo() {
		t.Error("Expected CanRedo to be false after recording change")
	}

	// Test undo
	undoChange := tracker.Undo()
	if undoChange == nil {
		t.Error("Expected undo to return a change")
	}

	if undoChange.NodeID != change.NodeID {
		t.Errorf("Expected undo change NodeID %s, got %s", change.NodeID, undoChange.NodeID)
	}

	// Test redo
	if !tracker.CanRedo() {
		t.Error("Expected CanRedo to be true after undo")
	}

	redoChange := tracker.Redo()
	if redoChange == nil {
		t.Error("Expected redo to return a change")
	}

	if redoChange.NodeID != change.NodeID {
		t.Errorf("Expected redo change NodeID %s, got %s", change.NodeID, redoChange.NodeID)
	}
}

// TestChangeTracker_MaxLevels tests change tracker max levels functionality
func TestChangeTracker_MaxLevels(t *testing.T) {
	maxLevels := 3
	tracker := ui.NewChangeTracker(maxLevels)

	// Add more changes than max levels
	for i := 0; i < maxLevels+2; i++ {
		change := &ui.Change{
			Type:     ui.ChangeTypeFieldUpdate,
			NodeID:   "test-node",
			Field:    "name",
			OldValue: "old",
			NewValue: "new",
		}
		tracker.RecordChange(change)
	}

	// Verify max levels is respected
	undoCount := 0
	for tracker.CanUndo() {
		tracker.Undo()
		undoCount++
	}

	if undoCount > maxLevels {
		t.Errorf("Expected max %d undo levels, got %d", maxLevels, undoCount)
	}
}

// TestTemplateManager tests template management functionality
func TestTemplateManager(t *testing.T) {
	// Create logger
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	// Create template manager
	templateManager := ui.NewTemplateManager(logger)

	if templateManager == nil {
		t.Fatal("Expected template manager to be created, got nil")
	}

	// Test getting template names
	names := templateManager.GetTemplateNames()
	if len(names) == 0 {
		t.Error("Expected template manager to have default templates")
	}

	// Test creating from template
	testPlan := templateManager.CreateFromTemplate("basic")
	if testPlan == nil {
		t.Error("Expected template manager to create test plan from template")
	}

	if testPlan.Name == "" {
		t.Error("Expected created test plan to have a name")
	}

	// Test creating from non-existent template (should fallback to basic)
	fallbackPlan := templateManager.CreateFromTemplate("non-existent")
	if fallbackPlan == nil {
		t.Error("Expected template manager to create fallback test plan")
	}
}

// TestTemplateManager_SaveAndLoad tests template save and load functionality
func TestTemplateManager_SaveAndLoad(t *testing.T) {
	logger := createTestLogger()
	templateManager := ui.NewTemplateManager(logger)

	testPlan := createSampleTestPlan()
	metadata := &ui.TemplateMetadata{
		Name:        "test-template",
		Description: "A test template",
		Category:    "testing",
		Tags:        []string{"test", "example"},
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Version:     "1.0.0",
		Author:      "test-author",
	}

	// Save template
	err := templateManager.SaveTemplate("test-template", testPlan, metadata)
	if err != nil {
		t.Fatalf("Failed to save template: %v", err)
	}

	// Load template
	loadedPlan, loadedMetadata, err := templateManager.GetTemplate("test-template")
	if err != nil {
		t.Fatalf("Failed to load template: %v", err)
	}

	if loadedPlan == nil {
		t.Error("Expected loaded template to not be nil")
	}

	if loadedMetadata == nil {
		t.Error("Expected loaded metadata to not be nil")
	}

	if loadedMetadata.Name != metadata.Name {
		t.Errorf("Expected metadata name %s, got %s", metadata.Name, loadedMetadata.Name)
	}
}

// TestTreeNode tests tree node functionality
func TestTreeNode(t *testing.T) {
	// Create parent node
	parent := ui.NewTreeNode("parent", ui.NodeTypeTestPlan, "Parent Node", nil)

	if parent == nil {
		t.Fatal("Expected parent node to be created, got nil")
	}

	// Test basic properties
	if parent.GetID() != "parent" {
		t.Errorf("Expected node ID 'parent', got %s", parent.GetID())
	}

	if parent.GetDisplayName() != "Parent Node" {
		t.Errorf("Expected node display name 'Parent Node', got %s", parent.GetDisplayName())
	}

	// Create child node
	child := ui.NewTreeNode("child", ui.NodeTypeScenario, "Child Node", nil)

	// Add child to parent
	parent.AddChild(child)

	// Test parent-child relationship
	children := parent.GetChildren()
	if len(children) != 1 {
		t.Errorf("Expected parent to have 1 child, got %d", len(children))
	}

	if children[0] != child {
		t.Error("Expected first child to be the added child")
	}

	if child.Parent != parent {
		t.Error("Expected child's parent to be the parent node")
	}

	// Test removing child
	parent.RemoveChild(child)

	children = parent.GetChildren()
	if len(children) != 0 {
		t.Errorf("Expected parent to have 0 children after removal, got %d", len(children))
	}

	if child.Parent != nil {
		t.Error("Expected child's parent to be nil after removal")
	}

	// Test validation state
	parent.SetValidationState(true, false)
	if !parent.HasErrors {
		t.Error("Expected node to have errors after setting validation state")
	}

	if parent.HasWarnings {
		t.Error("Expected node to not have warnings")
	}
}

// TestSyntaxHighlightEntry tests advanced editing features
func TestSyntaxHighlightEntry(t *testing.T) {
	// Test YAML entry
	yamlEntry := ui.NewSyntaxHighlightEntry(ui.LanguageYAML)
	if yamlEntry == nil {
		t.Error("Expected YAML entry to be created")
	}

	// Test JSON entry
	jsonEntry := ui.NewSyntaxHighlightEntry(ui.LanguageJSON)
	if jsonEntry == nil {
		t.Error("Expected JSON entry to be created")
	}

	// Test JavaScript entry
	jsEntry := ui.NewSyntaxHighlightEntry(ui.LanguageJavaScript)
	if jsEntry == nil {
		t.Error("Expected JavaScript entry to be created")
	}

	// Test XPath entry
	xpathEntry := ui.NewSyntaxHighlightEntry(ui.LanguageXPath)
	if xpathEntry == nil {
		t.Error("Expected XPath entry to be created")
	}

	// Test Regex entry
	regexEntry := ui.NewSyntaxHighlightEntry(ui.LanguageRegex)
	if regexEntry == nil {
		t.Error("Expected Regex entry to be created")
	}
}

// Integration Tests
func TestTestPlanDesigner_FullWorkflow(t *testing.T) {
	app := createTestApp()
	logger := createTestLogger()
	designer := ui.NewTestPlanDesigner(app, logger)

	// Load test plan
	testPlan := createSampleTestPlan()
	err := designer.LoadTestPlan(testPlan)
	if err != nil {
		t.Fatalf("Failed to load test plan: %v", err)
	}

	// Verify initial state
	if !designer.IsModified() {
		t.Error("Expected designer to be modified after loading test plan")
	}

	// Test undo/redo functionality
	if !designer.CanUndo() {
		t.Error("Expected to be able to undo after loading test plan")
	}

	// Test getting test plan
	retrievedPlan := designer.GetTestPlan()
	if retrievedPlan == nil {
		t.Error("Expected to retrieve test plan")
	}

	if retrievedPlan.Name != testPlan.Name {
		t.Errorf("Expected test plan name %s, got %s", testPlan.Name, retrievedPlan.Name)
	}
}

// Performance Tests
func BenchmarkTestPlanDesigner_LoadTestPlan(b *testing.B) {
	// Create test app
	testApp := test.NewApp()
	defer testApp.Quit()

	// Create logger
	logger := log.New(os.Stdout, "[BENCH] ", log.LstdFlags)

	// Create mock NeuralMeterApp
	config := ui.DefaultAppConfig()
	mockApp, err := ui.NewNeuralMeterApp(config, logger)
	if err != nil {
		b.Fatalf("Failed to create mock app: %v", err)
	}

	// Create designer
	designer := ui.NewTestPlanDesigner(mockApp, logger)

	// Create test plan
	testPlan := createSampleTestPlan()

	b.ResetTimer()

	// Benchmark loading test plan
	for i := 0; i < b.N; i++ {
		err := designer.LoadTestPlan(testPlan)
		if err != nil {
			b.Fatalf("Failed to load test plan: %v", err)
		}
	}
}

func BenchmarkFormBuilder_BuildForm(b *testing.B) {
	logger := createTestLogger()
	validator := createTestValidationEngine()
	formBuilder := ui.NewFormBuilder(validator, logger)

	testPlan := createSampleTestPlan()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		container := formBuilder.BuildForm(testPlan)
		if container == nil {
			b.Error("Expected form container to be created")
		}
	}
}

func BenchmarkChangeTracker_RecordChange(b *testing.B) {
	tracker := ui.NewChangeTracker(100)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		change := &ui.Change{
			Type:     ui.ChangeTypeFieldUpdate,
			NodeID:   "test-node",
			Field:    "name",
			OldValue: "old",
			NewValue: "new",
		}
		tracker.RecordChange(change)
	}
}

func BenchmarkTemplateManager_CreateFromTemplate(b *testing.B) {
	logger := createTestLogger()
	templateManager := ui.NewTemplateManager(logger)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		testPlan := templateManager.CreateFromTemplate("basic")
		if testPlan == nil {
			b.Error("Expected test plan to be created from template")
		}
	}
}

// Test helper to create large test plans for performance testing
func createLargeTestPlan() *parser.TestPlan {
	testPlan := createSampleTestPlan()

	// Add many scenarios and requests
	for i := 0; i < 50; i++ {
		scenario := parser.Scenario{
			Name:        fmt.Sprintf("Scenario %d", i),
			Description: fmt.Sprintf("Test scenario %d", i),
			Requests:    make([]parser.Request, 0),
		}

		for j := 0; j < 20; j++ {
			request := parser.Request{
				Name:   fmt.Sprintf("Request %d-%d", i, j),
				Method: "GET",
				URL:    fmt.Sprintf("/test/%d/%d", i, j),
				Assertions: []parser.Assertion{
					{
						Type:     "status",
						Operator: "equals",
						Value:    "200",
					},
				},
			}
			scenario.Requests = append(scenario.Requests, request)
		}

		testPlan.Scenarios = append(testPlan.Scenarios, scenario)
	}

	return testPlan
}

func BenchmarkTestPlanDesigner_LoadLargeTestPlan(b *testing.B) {
	app := createTestApp()
	logger := createTestLogger()
	designer := ui.NewTestPlanDesigner(app, logger)

	largeTestPlan := createLargeTestPlan()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		err := designer.LoadTestPlan(largeTestPlan)
		if err != nil {
			b.Fatalf("Failed to load large test plan: %v", err)
		}
	}
}
