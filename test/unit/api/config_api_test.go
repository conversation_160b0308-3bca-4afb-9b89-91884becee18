package api

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"neuralmetergo/internal/api"
	"neuralmetergo/internal/config"
)

func TestConfigurationAPI_GetLoadTestConfig(t *testing.T) {
	// Create a test config manager with a dummy path
	configManager, err := config.NewConfigManager("test.yaml")
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Set up test configuration (would be used in a full integration test)
	_ = &config.Config{
		LoadTest: config.LoadTestConfig{
			DefaultDuration:    config.Duration(30 * time.Second),
			DefaultConcurrency: 10,
			MaxConcurrency:     100,
			RampUpDuration:     config.Duration(10 * time.Second),
			RampDownDuration:   config.Duration(10 * time.Second),
		},
	}

	// Create API instance
	configAPI := api.NewConfigurationAPI(configManager)

	// Create test request
	req, err := http.NewRequest("GET", "/api/config/load-test", nil)
	if err != nil {
		t.Fatal(err)
	}

	// Create response recorder
	rr := httptest.NewRecorder()

	// Call the handler
	configAPI.GetLoadTestConfig(rr, req)

	// Check status code
	if status := rr.Code; status != http.StatusInternalServerError {
		// We expect internal server error because config is not loaded
		t.Logf("Expected status %d for unloaded config, got %d", http.StatusInternalServerError, status)
	}

	// Test with a loaded config would require setting up the config manager properly
	// For now, we'll test the method signature and basic error handling
}

func TestConfigurationAPI_UpdateLoadTestConfig(t *testing.T) {
	// Create a test config manager
	configManager, err := config.NewConfigManager("test.yaml")
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Create API instance
	configAPI := api.NewConfigurationAPI(configManager)

	// Test data
	testLoadTestConfig := config.LoadTestConfig{
		DefaultDuration:    config.Duration(60 * time.Second),
		DefaultConcurrency: 20,
		MaxConcurrency:     200,
		RampUpDuration:     config.Duration(15 * time.Second),
		RampDownDuration:   config.Duration(15 * time.Second),
	}

	// Convert to JSON
	jsonData, err := json.Marshal(testLoadTestConfig)
	if err != nil {
		t.Fatal(err)
	}

	// Create test request
	req, err := http.NewRequest("POST", "/api/config/load-test", bytes.NewBuffer(jsonData))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	rr := httptest.NewRecorder()

	// Call the handler
	configAPI.UpdateLoadTestConfig(rr, req)

	// Check status code - should be error because config is not loaded
	if status := rr.Code; status != http.StatusInternalServerError {
		t.Logf("Expected status %d for unloaded config, got %d", http.StatusInternalServerError, status)
	}
}

func TestConfigurationAPI_GetConfigSchema(t *testing.T) {
	// Create a test config manager
	configManager, err := config.NewConfigManager("test.yaml")
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Create API instance
	configAPI := api.NewConfigurationAPI(configManager)

	// Create test request
	req, err := http.NewRequest("GET", "/api/config/schema", nil)
	if err != nil {
		t.Fatal(err)
	}

	// Create response recorder
	rr := httptest.NewRecorder()

	// Call the handler
	configAPI.GetConfigSchema(rr, req)

	// Check status code
	if status := rr.Code; status != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, status)
	}

	// Check content type
	expected := "application/json"
	if ct := rr.Header().Get("Content-Type"); ct != expected {
		t.Errorf("Expected content type %s, got %s", expected, ct)
	}

	// Parse response
	var response api.ConfigResponse
	if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to parse response: %v", err)
	}

	// Check response structure
	if !response.Success {
		t.Errorf("Expected successful response, got: %s", response.Error)
	}

	// Check that schema data exists
	if response.Data == nil {
		t.Error("Expected schema data, got nil")
	}

	// Verify schema structure
	schemaData, ok := response.Data.(map[string]interface{})
	if !ok {
		t.Error("Expected schema data to be a map")
	} else {
		// Check for required schema fields
		if _, exists := schemaData["$schema"]; !exists {
			t.Error("Expected $schema field in response")
		}
		if _, exists := schemaData["properties"]; !exists {
			t.Error("Expected properties field in response")
		}
	}
}

func TestConfigurationAPI_ValidateConfig(t *testing.T) {
	// Create a test config manager
	configManager, err := config.NewConfigManager("test.yaml")
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Create API instance
	configAPI := api.NewConfigurationAPI(configManager)

	// Test data - valid config
	validConfigData := map[string]interface{}{
		"server": map[string]interface{}{
			"host": "localhost",
			"port": 8080,
		},
		"load_test": map[string]interface{}{
			"default_duration":    "30s",
			"default_concurrency": 10,
			"max_concurrency":     100,
			"ramp_up_duration":    "10s",
			"ramp_down_duration":  "10s",
		},
	}

	validationRequest := api.ConfigValidationRequest{
		ConfigData: validConfigData,
		Format:     "json",
	}

	// Convert to JSON
	jsonData, err := json.Marshal(validationRequest)
	if err != nil {
		t.Fatal(err)
	}

	// Create test request
	req, err := http.NewRequest("POST", "/api/config/validate", bytes.NewBuffer(jsonData))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	rr := httptest.NewRecorder()

	// Call the handler
	configAPI.ValidateConfig(rr, req)

	// Check status code
	if status := rr.Code; status != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, status)
	}

	// Parse response
	var response api.ConfigResponse
	if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to parse response: %v", err)
	}

	// Check response structure
	if !response.Success {
		t.Errorf("Expected successful response, got: %s", response.Error)
	}
}

func TestConfigurationAPI_SubscribeToChanges(t *testing.T) {
	// Create a test config manager
	configManager, err := config.NewConfigManager("test.yaml")
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Create API instance
	configAPI := api.NewConfigurationAPI(configManager)

	// Create test request
	req, err := http.NewRequest("GET", "/api/config/subscribe", nil)
	if err != nil {
		t.Fatal(err)
	}

	// Create response recorder
	rr := httptest.NewRecorder()

	// Call the handler
	configAPI.SubscribeToChanges(rr, req)

	// Check status code
	if status := rr.Code; status != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, status)
	}

	// Parse response
	var response api.ConfigResponse
	if err := json.Unmarshal(rr.Body.Bytes(), &response); err != nil {
		t.Fatalf("Failed to parse response: %v", err)
	}

	// Check response structure
	if !response.Success {
		t.Errorf("Expected successful response, got: %s", response.Error)
	}

	// Check that subscription info exists
	if response.Data == nil {
		t.Error("Expected subscription data, got nil")
	}
}

func TestConfigurationAPI_RegisterRoutes(t *testing.T) {
	// Create a test config manager
	configManager, err := config.NewConfigManager("test.yaml")
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Create API instance
	configAPI := api.NewConfigurationAPI(configManager)

	// Create a new ServeMux
	mux := http.NewServeMux()

	// Register routes
	configAPI.RegisterRoutes(mux)

	// Test that routes are registered by making requests
	testRoutes := []struct {
		method string
		path   string
	}{
		{"GET", "/api/config/load-test"},
		{"POST", "/api/config/load-test"},
		{"GET", "/api/config/schema"},
		{"POST", "/api/config/validate"},
		{"GET", "/api/config/subscribe"},
	}

	for _, route := range testRoutes {
		var req *http.Request
		var err error

		// For POST requests, provide a body to avoid nil pointer dereference
		if route.method == "POST" {
			req, err = http.NewRequest(route.method, route.path, bytes.NewBufferString("{}"))
			if err != nil {
				t.Fatal(err)
			}
			req.Header.Set("Content-Type", "application/json")
		} else {
			req, err = http.NewRequest(route.method, route.path, nil)
			if err != nil {
				t.Fatal(err)
			}
		}

		rr := httptest.NewRecorder()
		mux.ServeHTTP(rr, req)

		// We expect some response (not 404) indicating the route is registered
		if status := rr.Code; status == http.StatusNotFound {
			t.Errorf("Route %s %s not registered, got 404", route.method, route.path)
		}
	}
}

func TestConfigurationAPI_MethodNotAllowed(t *testing.T) {
	// Create a test config manager
	configManager, err := config.NewConfigManager("test.yaml")
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Create API instance
	configAPI := api.NewConfigurationAPI(configManager)

	// Test cases for method not allowed
	testCases := []struct {
		method   string
		path     string
		handler  func(http.ResponseWriter, *http.Request)
		expected int
	}{
		{"PUT", "/api/config/load-test", configAPI.GetLoadTestConfig, http.StatusMethodNotAllowed},
		{"DELETE", "/api/config/load-test", configAPI.UpdateLoadTestConfig, http.StatusMethodNotAllowed},
		{"POST", "/api/config/schema", configAPI.GetConfigSchema, http.StatusMethodNotAllowed},
		{"GET", "/api/config/validate", configAPI.ValidateConfig, http.StatusMethodNotAllowed},
	}

	for _, tc := range testCases {
		req, err := http.NewRequest(tc.method, tc.path, nil)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		tc.handler(rr, req)

		if status := rr.Code; status != tc.expected {
			t.Errorf("Expected status %d for %s %s, got %d", tc.expected, tc.method, tc.path, status)
		}
	}
}

func TestConfigurationAPI_InvalidJSON(t *testing.T) {
	// Create a test config manager
	configManager, err := config.NewConfigManager("test.yaml")
	if err != nil {
		t.Fatalf("Failed to create config manager: %v", err)
	}

	// Create API instance
	configAPI := api.NewConfigurationAPI(configManager)

	// Test invalid JSON for UpdateLoadTestConfig
	req, err := http.NewRequest("POST", "/api/config/load-test", bytes.NewBufferString("invalid json"))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", "application/json")

	rr := httptest.NewRecorder()
	configAPI.UpdateLoadTestConfig(rr, req)

	if status := rr.Code; status != http.StatusBadRequest {
		t.Errorf("Expected status %d for invalid JSON, got %d", http.StatusBadRequest, status)
	}

	// Test invalid JSON for ValidateConfig
	req2, err := http.NewRequest("POST", "/api/config/validate", bytes.NewBufferString("invalid json"))
	if err != nil {
		t.Fatal(err)
	}
	req2.Header.Set("Content-Type", "application/json")

	rr2 := httptest.NewRecorder()
	configAPI.ValidateConfig(rr2, req2)

	if status := rr2.Code; status != http.StatusBadRequest {
		t.Errorf("Expected status %d for invalid JSON, got %d", http.StatusBadRequest, status)
	}
}
