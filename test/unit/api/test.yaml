server:
  host: "localhost"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"
  tls:
    enabled: false
    cert_file: ""
    key_file: ""

load_test:
  default_duration: "30s"
  default_concurrency: 10
  max_concurrency: 100
  ramp_up_duration: "10s"
  ramp_down_duration: "10s"

metrics:
  enabled: true
  collection_interval: "1s"
  buffer_size: 1000
  retention_period: "24h"
  export_interval: "60s"

output:
  format: "json"
  file: ""
  console: true
  verbose: false
  templates: {}
  compression: false

dashboard:
  enabled: false
  host: "localhost"
  port: 9090
  refresh_rate: 1
  history_limit: 100

worker:
  pool_size: 4
  queue_size: 100
  max_retries: 3
  retry_delay: "1s"
  shutdown_timeout: "30s"

gpu:
  enabled: false
  prefer_cuda: true
  min_memory_gb: 1.0
  min_compute_capability:
    major: 3
    minor: 5
  max_temperature: 85
  max_memory_utilization: 80.0
  device_id: 0
  monitoring_interval: "5s"
  resource_limits:
    max_gpu_utilization: 90.0
    max_memory_usage: 85.0
    max_power_consumption: 250.0
    throttle_on_overheat: true
    throttle_on_high_memory: true
    throttle_on_power_limit: true
  performance_thresholds:
    warning_temperature: 75
    warning_memory_usage: 70.0
    warning_gpu_utilization: 80.0
    critical_temperature: 85
    critical_memory_usage: 85.0
    critical_gpu_utilization: 95.0
  allow_fallback: true
  profile_mode: "disabled"
  log_gpu_events: false

global:
  log_level: "info"
  config_dir: "./"
  data_dir: "./data"
  temp_dir: "./tmp"
  environment: "development"
  debug: false
  profiles_enabled: false
  variables: {} 