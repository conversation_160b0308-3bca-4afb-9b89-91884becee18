package export_test

import (
	"bytes"
	"strings"
	"testing"
	"time"

	"neuralmetergo/internal/metrics/export"
	"neuralmetergo/internal/metrics/export/formats"
)

// createTestExportData creates sample export data for testing
func createTestExportData() *export.ExportData {
	now := time.Now()

	return &export.ExportData{
		Timestamp:   now,
		Source:      "test-source",
		Version:     "1.0.0",
		Description: "Test metrics export",
		MetricSeries: []export.MetricSeries{
			{
				Name: "http_requests_total",
				Help: "Total number of HTTP requests",
				Type: "counter",
				Unit: "requests",
				Labels: map[string]string{
					"service": "api",
					"region":  "us-east-1",
				},
				DataPoints: []export.MetricDataPoint{
					{
						Name:      "http_requests_total",
						Value:     100.0,
						Timestamp: now.Add(-time.Minute),
						Tags: map[string]string{
							"method": "GET",
							"status": "200",
						},
						Unit: "requests",
					},
					{
						Name:      "http_requests_total",
						Value:     25.0,
						Timestamp: now,
						Tags: map[string]string{
							"method": "POST",
							"status": "201",
						},
						Unit: "requests",
					},
				},
			},
			{
				Name: "response_time_seconds",
				Help: "HTTP response time in seconds",
				Type: "gauge",
				Unit: "seconds",
				DataPoints: []export.MetricDataPoint{
					{
						Name:      "response_time_seconds",
						Value:     0.123,
						Timestamp: now,
						Tags: map[string]string{
							"endpoint": "/api/users",
						},
						Unit: "seconds",
					},
				},
			},
		},
		Metadata: map[string]interface{}{
			"test_run_id": "12345",
			"environment": "test",
		},
	}
}

// TestExportManager tests the basic export manager functionality
func TestExportManager(t *testing.T) {
	manager := export.NewExportManager()

	// Test that manager is created with no registered exporters
	registeredFormats := manager.GetRegisteredFormats()
	if len(registeredFormats) != 0 {
		t.Errorf("Expected 0 registered formats, got %d", len(registeredFormats))
	}

	// Register a JSON exporter
	jsonExporter := formats.NewJSONExporter(true)
	manager.RegisterExporter(export.FormatJSON, jsonExporter)

	// Test that exporter is registered
	registeredFormats = manager.GetRegisteredFormats()
	if len(registeredFormats) != 1 {
		t.Errorf("Expected 1 registered format, got %d", len(registeredFormats))
	}

	if registeredFormats[0] != export.FormatJSON {
		t.Errorf("Expected JSON format, got %s", registeredFormats[0])
	}
}

// TestJSONExporter tests JSON export functionality
func TestJSONExporter(t *testing.T) {
	exporter := formats.NewJSONExporter(true)
	data := createTestExportData()

	// Test basic export
	var buf bytes.Buffer
	err := exporter.Export(data, &buf)
	if err != nil {
		t.Fatalf("Failed to export JSON: %v", err)
	}

	output := buf.String()

	// Check that output contains expected content
	expectedFields := []string{
		`"timestamp"`,
		`"source": "test-source"`,
		`"version": "1.0.0"`,
		`"http_requests_total"`,
		`"response_time_seconds"`,
		`"method": "GET"`,
		`"status": "200"`,
	}

	for _, field := range expectedFields {
		if !strings.Contains(output, field) {
			t.Errorf("JSON output missing expected field: %s", field)
		}
	}

	// Test content type and file extension
	if exporter.GetContentType() != "application/json" {
		t.Errorf("Expected application/json content type, got %s", exporter.GetContentType())
	}

	if exporter.GetFileExtension() != ".json" {
		t.Errorf("Expected .json extension, got %s", exporter.GetFileExtension())
	}

	// Test validation
	if err := exporter.Validate(data); err != nil {
		t.Errorf("Valid data failed validation: %v", err)
	}
}

// TestCSVExporter tests CSV export functionality
func TestCSVExporter(t *testing.T) {
	exporter := formats.NewCSVExporter()
	data := createTestExportData()

	// Test basic export
	var buf bytes.Buffer
	err := exporter.Export(data, &buf)
	if err != nil {
		t.Fatalf("Failed to export CSV: %v", err)
	}

	output := buf.String()
	lines := strings.Split(strings.TrimSpace(output), "\n")

	// Should have header + 3 data rows
	if len(lines) < 4 {
		t.Errorf("Expected at least 4 lines in CSV output, got %d", len(lines))
	}

	// Check header line
	header := lines[0]
	expectedHeaders := []string{"timestamp", "metric_name", "value", "type", "unit"}
	for _, expectedHeader := range expectedHeaders {
		if !strings.Contains(header, expectedHeader) {
			t.Errorf("CSV header missing expected field: %s", expectedHeader)
		}
	}

	// Check data content
	if !strings.Contains(output, "http_requests_total") {
		t.Error("CSV output missing metric name")
	}

	if !strings.Contains(output, "100") {
		t.Error("CSV output missing metric value")
	}

	// Test content type and file extension
	if exporter.GetContentType() != "text/csv" {
		t.Errorf("Expected text/csv content type, got %s", exporter.GetContentType())
	}

	if exporter.GetFileExtension() != ".csv" {
		t.Errorf("Expected .csv extension, got %s", exporter.GetFileExtension())
	}
}

// TestPrometheusExporter tests Prometheus export functionality
func TestPrometheusExporter(t *testing.T) {
	exporter := formats.NewPrometheusExporter()
	data := createTestExportData()

	// Test basic export
	var buf bytes.Buffer
	err := exporter.Export(data, &buf)
	if err != nil {
		t.Fatalf("Failed to export Prometheus: %v", err)
	}

	output := buf.String()

	// Check for Prometheus format elements
	expectedElements := []string{
		"# HELP http_requests_total",
		"# TYPE http_requests_total",
		"http_requests_total{",
		"method=\"GET\"",
		"status=\"200\"",
		"} 100",
		"response_time_seconds",
	}

	for _, element := range expectedElements {
		if !strings.Contains(output, element) {
			t.Errorf("Prometheus output missing expected element: %s", element)
		}
	}

	// Test content type and file extension
	expectedContentType := "text/plain; version=0.0.4; charset=utf-8"
	if exporter.GetContentType() != expectedContentType {
		t.Errorf("Expected %s content type, got %s", expectedContentType, exporter.GetContentType())
	}

	if exporter.GetFileExtension() != ".prom" {
		t.Errorf("Expected .prom extension, got %s", exporter.GetFileExtension())
	}
}

// TestInfluxDBExporter tests InfluxDB line protocol export functionality
func TestInfluxDBExporter(t *testing.T) {
	exporter := formats.NewInfluxDBExporter()
	data := createTestExportData()

	// Test basic export
	var buf bytes.Buffer
	err := exporter.Export(data, &buf)
	if err != nil {
		t.Fatalf("Failed to export InfluxDB: %v", err)
	}

	output := buf.String()
	lines := strings.Split(strings.TrimSpace(output), "\n")

	// Should have 3 data lines (2 from first metric, 1 from second)
	if len(lines) != 3 {
		t.Errorf("Expected 3 lines in InfluxDB output, got %d", len(lines))
	}

	// Check line protocol format
	firstLine := lines[0]

	// Should contain measurement name, tags, fields, and timestamp
	if !strings.Contains(firstLine, "http_requests_total") {
		t.Error("InfluxDB output missing measurement name")
	}

	if !strings.Contains(firstLine, "method=GET") {
		t.Error("InfluxDB output missing tag")
	}

	if !strings.Contains(firstLine, "value=100") {
		t.Error("InfluxDB output missing field value")
	}

	// Test content type and file extension
	if exporter.GetContentType() != "text/plain; charset=utf-8" {
		t.Errorf("Expected text/plain content type, got %s", exporter.GetContentType())
	}

	if exporter.GetFileExtension() != ".influx" {
		t.Errorf("Expected .influx extension, got %s", exporter.GetFileExtension())
	}
}

// TestExportManagerWithFiltering tests export with filtering
func TestExportManagerWithFiltering(t *testing.T) {
	manager := export.NewExportManager()
	jsonExporter := formats.NewJSONExporter(false)
	manager.RegisterExporter(export.FormatJSON, jsonExporter)

	data := createTestExportData()

	// Test filtering by metric name
	config := export.ExportConfig{
		Format: export.FormatJSON,
		FilterCriteria: &export.FilterCriteria{
			MetricNames: []string{"http_requests_total"},
		},
	}

	var buf bytes.Buffer
	err := manager.Export(data, config, &buf)
	if err != nil {
		t.Fatalf("Failed to export with filtering: %v", err)
	}

	output := buf.String()

	// Should contain the filtered metric
	if !strings.Contains(output, "http_requests_total") {
		t.Error("Filtered output missing included metric")
	}

	// Should not contain the excluded metric
	if strings.Contains(output, "response_time_seconds") {
		t.Error("Filtered output contains excluded metric")
	}
}

// TestExportManagerWithRetry tests export retry functionality
func TestExportManagerWithRetry(t *testing.T) {
	manager := export.NewExportManager()
	jsonExporter := formats.NewJSONExporter(true)
	manager.RegisterExporter(export.FormatJSON, jsonExporter)

	data := createTestExportData()

	config := export.ExportConfig{
		Format: export.FormatJSON,
		RetryConfig: &export.RetryConfig{
			MaxAttempts:     2,
			InitialDelay:    time.Millisecond, // Very fast for testing
			MaxDelay:        time.Millisecond * 10,
			BackoffMultiple: 1.5,
		},
	}

	var buf bytes.Buffer
	err := manager.ExportWithRetry(data, config, &buf)
	if err != nil {
		t.Fatalf("Failed to export with retry: %v", err)
	}

	// Should succeed on first attempt
	if !strings.Contains(buf.String(), "http_requests_total") {
		t.Error("Retry export missing expected content")
	}
}

// TestExportStats tests export statistics tracking
func TestExportStats(t *testing.T) {
	manager := export.NewExportManager()
	jsonExporter := formats.NewJSONExporter(true)
	manager.RegisterExporter(export.FormatJSON, jsonExporter)

	data := createTestExportData()
	config := export.ExportConfig{Format: export.FormatJSON}

	// Initial stats should be zero
	stats := manager.GetStats()
	if stats.TotalExports != 0 || stats.SuccessfulExports != 0 {
		t.Error("Initial stats should be zero")
	}

	// Perform export
	var buf bytes.Buffer
	err := manager.Export(data, config, &buf)
	if err != nil {
		t.Fatalf("Failed to export: %v", err)
	}

	// Check updated stats
	stats = manager.GetStats()
	if stats.TotalExports != 1 || stats.SuccessfulExports != 1 {
		t.Errorf("Expected 1 total and 1 successful export, got %d total, %d successful",
			stats.TotalExports, stats.SuccessfulExports)
	}

	if stats.AverageExportTime <= 0 {
		t.Error("Average export time should be positive")
	}
}

// TestValidation tests data validation across different formats
func TestValidation(t *testing.T) {
	exporters := map[string]export.Exporter{
		"JSON":       formats.NewJSONExporter(true),
		"CSV":        formats.NewCSVExporter(),
		"Prometheus": formats.NewPrometheusExporter(),
		"InfluxDB":   formats.NewInfluxDBExporter(),
	}

	// Test with empty data
	emptyData := &export.ExportData{}

	for name, exporter := range exporters {
		err := exporter.Validate(emptyData)
		if err == nil {
			t.Errorf("%s exporter should reject empty data", name)
		}
	}

	// Test with valid data
	validData := createTestExportData()

	for name, exporter := range exporters {
		err := exporter.Validate(validData)
		if err != nil {
			t.Errorf("%s exporter should accept valid data: %v", name, err)
		}
	}
}

// TestStreamingSupport tests streaming support across formats
func TestStreamingSupport(t *testing.T) {
	exporters := map[string]export.Exporter{
		"JSON":       formats.NewJSONExporter(true),
		"CSV":        formats.NewCSVExporter(),
		"Prometheus": formats.NewPrometheusExporter(),
		"InfluxDB":   formats.NewInfluxDBExporter(),
	}

	for name, exporter := range exporters {
		if !exporter.SupportsStreaming() {
			t.Errorf("%s exporter should support streaming", name)
		}
	}
}

// BenchmarkJSONExport benchmarks JSON export performance
func BenchmarkJSONExport(b *testing.B) {
	exporter := formats.NewJSONExporter(false) // Compact for performance
	data := createTestExportData()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var buf bytes.Buffer
		err := exporter.Export(data, &buf)
		if err != nil {
			b.Fatalf("Export failed: %v", err)
		}
	}
}

// BenchmarkCSVExport benchmarks CSV export performance
func BenchmarkCSVExport(b *testing.B) {
	exporter := formats.NewCSVExporter()
	data := createTestExportData()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var buf bytes.Buffer
		err := exporter.Export(data, &buf)
		if err != nil {
			b.Fatalf("Export failed: %v", err)
		}
	}
}

// BenchmarkPrometheusExport benchmarks Prometheus export performance
func BenchmarkPrometheusExport(b *testing.B) {
	exporter := formats.NewPrometheusExporter()
	data := createTestExportData()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var buf bytes.Buffer
		err := exporter.Export(data, &buf)
		if err != nil {
			b.Fatalf("Export failed: %v", err)
		}
	}
}

// BenchmarkInfluxDBExport benchmarks InfluxDB export performance
func BenchmarkInfluxDBExport(b *testing.B) {
	exporter := formats.NewInfluxDBExporter()
	data := createTestExportData()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var buf bytes.Buffer
		err := exporter.Export(data, &buf)
		if err != nil {
			b.Fatalf("Export failed: %v", err)
		}
	}
}
