package export_test

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"

	"neuralmetergo/internal/metrics/export"
	"neuralmetergo/internal/metrics/export/formats"
)

// TestJobManager tests basic job management functionality
func TestJobManager(t *testing.T) {
	manager := export.NewExportManager()
	jsonExporter := formats.NewJSONExporter(true)
	manager.RegisterExporter(export.FormatJSON, jsonExporter)

	jobManager := export.NewJobManager(manager)

	// Test creating a job
	job := &export.ExportJob{
		ID:     "test-job-1",
		Name:   "Test Job",
		Format: export.FormatJSON,
		Destination: export.ExportDestination{
			Type:     "file",
			Location: "/tmp/test-export.json",
		},
		Schedule: export.Schedule{
			Type:     export.ScheduleTypeInterval,
			Interval: time.Minute,
		},
	}

	err := jobManager.CreateJob(job)
	if err != nil {
		t.Fatalf("Failed to create job: %v", err)
	}

	// Test retrieving the job
	retrievedJob, err := jobManager.GetJob("test-job-1")
	if err != nil {
		t.Fatalf("Failed to get job: %v", err)
	}

	if retrievedJob.Name != "Test Job" {
		t.Errorf("Expected job name 'Test Job', got '%s'", retrievedJob.Name)
	}

	// Test listing jobs
	jobs := jobManager.ListJobs("")
	if len(jobs) != 1 {
		t.Errorf("Expected 1 job, got %d", len(jobs))
	}

	// Test deleting job
	err = jobManager.DeleteJob("test-job-1")
	if err != nil {
		t.Fatalf("Failed to delete job: %v", err)
	}

	// Verify job is deleted
	jobs = jobManager.ListJobs("")
	if len(jobs) != 0 {
		t.Errorf("Expected 0 jobs after deletion, got %d", len(jobs))
	}
}

// TestExportScheduler tests the basic scheduler functionality
func TestExportScheduler(t *testing.T) {
	manager := export.NewExportManager()
	jsonExporter := formats.NewJSONExporter(true)
	manager.RegisterExporter(export.FormatJSON, jsonExporter)

	jobManager := export.NewJobManager(manager)

	// Create scheduler with Mac-friendly config
	config := &export.SchedulerConfig{
		Workers:       2,                      // Fewer workers for Mac
		CheckInterval: 100 * time.Millisecond, // Fast checking for tests
		MaxRetries:    1,                      // Fewer retries for faster tests
		Logger:        nil,                    // Disable logging for tests
	}

	scheduler := export.NewExportScheduler(jobManager, manager, config)

	// Test scheduler start/stop
	if scheduler.IsRunning() {
		t.Error("Scheduler should not be running initially")
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()

	err := scheduler.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start scheduler: %v", err)
	}

	if !scheduler.IsRunning() {
		t.Error("Scheduler should be running after start")
	}

	// Let it run briefly
	time.Sleep(200 * time.Millisecond)

	err = scheduler.Stop()
	if err != nil {
		t.Fatalf("Failed to stop scheduler: %v", err)
	}

	if scheduler.IsRunning() {
		t.Error("Scheduler should not be running after stop")
	}

	// Test stats
	stats := scheduler.GetStats()
	if stats.StartTime.IsZero() {
		t.Error("Expected start time to be set")
	}
}

// TestJobExecution tests immediate job execution
func TestJobExecution(t *testing.T) {
	manager := export.NewExportManager()
	jsonExporter := formats.NewJSONExporter(true)
	manager.RegisterExporter(export.FormatJSON, jsonExporter)

	jobManager := export.NewJobManager(manager)

	config := &export.SchedulerConfig{
		Workers:       1,
		CheckInterval: time.Second,
		MaxRetries:    1,
		Logger:        nil,
	}

	scheduler := export.NewExportScheduler(jobManager, manager, config)

	// Create a job with buffer destination for testing
	job := &export.ExportJob{
		ID:     "exec-test-job",
		Name:   "Execution Test Job",
		Format: export.FormatJSON,
		Destination: export.ExportDestination{
			Type:     "buffer",
			Location: "memory",
		},
		Schedule: export.Schedule{
			Type:     export.ScheduleTypeInterval,
			Interval: time.Hour, // Long interval so it doesn't auto-run
		},
	}

	err := jobManager.CreateJob(job)
	if err != nil {
		t.Fatalf("Failed to create job: %v", err)
	}

	// Execute job immediately
	err = scheduler.ExecuteJobNow("exec-test-job")
	if err != nil {
		t.Fatalf("Failed to execute job: %v", err)
	}

	// Check job statistics
	stats, err := jobManager.GetJobStats("exec-test-job")
	if err != nil {
		t.Fatalf("Failed to get job stats: %v", err)
	}

	if stats.TotalExecutions == 0 {
		t.Error("Expected at least one execution")
	}

	// Clean up
	jobManager.DeleteJob("exec-test-job")
}

// TestScheduleTypes tests different schedule types
func TestScheduleTypes(t *testing.T) {
	manager := export.NewExportManager()
	jsonExporter := formats.NewJSONExporter(true)
	manager.RegisterExporter(export.FormatJSON, jsonExporter)

	jobManager := export.NewJobManager(manager)

	// Test interval scheduling
	intervalJob := &export.ExportJob{
		ID:     "interval-job",
		Name:   "Interval Job",
		Format: export.FormatJSON,
		Destination: export.ExportDestination{
			Type:     "buffer",
			Location: "memory",
		},
		Schedule: export.Schedule{
			Type:     export.ScheduleTypeInterval,
			Interval: time.Minute * 5,
		},
	}

	err := jobManager.CreateJob(intervalJob)
	if err != nil {
		t.Fatalf("Failed to create interval job: %v", err)
	}

	// Verify job has next run time set
	job, _ := jobManager.GetJob("interval-job")
	if job.NextRunAt == nil {
		t.Error("Expected NextRunAt to be set for interval job")
	}

	// Test one-time scheduling
	futureTime := time.Now().Add(time.Hour)
	oneTimeJob := &export.ExportJob{
		ID:     "onetime-job",
		Name:   "One Time Job",
		Format: export.FormatJSON,
		Destination: export.ExportDestination{
			Type:     "buffer",
			Location: "memory",
		},
		Schedule: export.Schedule{
			Type:      export.ScheduleTypeOneTime,
			StartTime: &futureTime,
		},
	}

	err = jobManager.CreateJob(oneTimeJob)
	if err != nil {
		t.Fatalf("Failed to create one-time job: %v", err)
	}

	// Clean up
	jobManager.DeleteJob("interval-job")
	jobManager.DeleteJob("onetime-job")
}

// BenchmarkJobCreation benchmarks job creation performance
func BenchmarkJobCreation(b *testing.B) {
	manager := export.NewExportManager()
	jsonExporter := formats.NewJSONExporter(false)
	manager.RegisterExporter(export.FormatJSON, jsonExporter)

	jobManager := export.NewJobManager(manager)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		job := &export.ExportJob{
			ID:     fmt.Sprintf("bench-job-%d", i),
			Name:   fmt.Sprintf("Bench Job %d", i),
			Format: export.FormatJSON,
			Destination: export.ExportDestination{
				Type:     "buffer",
				Location: "memory",
			},
			Schedule: export.Schedule{
				Type:     export.ScheduleTypeInterval,
				Interval: time.Hour,
			},
		}

		err := jobManager.CreateJob(job)
		if err != nil {
			b.Fatalf("Failed to create job: %v", err)
		}
	}
}

// TestJobValidation tests job configuration validation
func TestJobValidation(t *testing.T) {
	manager := export.NewExportManager()
	jsonExporter := formats.NewJSONExporter(true)
	manager.RegisterExporter(export.FormatJSON, jsonExporter)

	jobManager := export.NewJobManager(manager)

	// Test invalid jobs
	testCases := []struct {
		name        string
		job         *export.ExportJob
		expectError bool
		errorMsg    string
	}{
		{
			name: "Empty ID",
			job: &export.ExportJob{
				Name: "Test",
			},
			expectError: true,
			errorMsg:    "job ID cannot be empty",
		},
		{
			name: "Empty Name",
			job: &export.ExportJob{
				ID: "test-1",
			},
			expectError: true,
			errorMsg:    "job name cannot be empty",
		},
		{
			name: "Invalid Format",
			job: &export.ExportJob{
				ID:     "test-1",
				Name:   "Test",
				Format: "invalid-format",
				Destination: export.ExportDestination{
					Type:     "file",
					Location: "/tmp/test.json",
				},
				Schedule: export.Schedule{
					Type:     export.ScheduleTypeInterval,
					Interval: time.Minute,
				},
			},
			expectError: true,
			errorMsg:    "export format invalid-format is not supported",
		},
		{
			name: "Invalid Schedule",
			job: &export.ExportJob{
				ID:     "test-1",
				Name:   "Test",
				Format: export.FormatJSON,
				Destination: export.ExportDestination{
					Type:     "file",
					Location: "/tmp/test.json",
				},
				Schedule: export.Schedule{
					Type:     export.ScheduleTypeInterval,
					Interval: 0, // Invalid zero interval
				},
			},
			expectError: true,
			errorMsg:    "interval must be positive",
		},
	}

	for _, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			err := jobManager.CreateJob(testCase.job)
			if err == nil && testCase.expectError {
				t.Errorf("Expected error for %s, but got no error", testCase.name)
			} else if err != nil && !testCase.expectError {
				t.Errorf("Unexpected error for %s: %v", testCase.name, err)
			} else if err != nil && testCase.expectError {
				if !strings.Contains(err.Error(), testCase.errorMsg) {
					t.Errorf("Expected error message '%s' for %s, but got '%v'", testCase.errorMsg, testCase.name, err)
				}
			}
		})
	}
}
