package engine

import (
	"sync"
	"testing"
	"time"

	"neuralmetergo/internal/engine"
	"neuralmetergo/internal/parser"
)

// MAC DEVELOPMENT TEST FILE
// This file contains lightweight tests designed specifically for Mac development environments.
// These tests avoid production-scale operations that can hang on Mac systems.

// Development-safe test configuration
func createDevelopmentTestPlan() *parser.TestPlan {
	return &parser.TestPlan{
		Version:     "1.0",
		Name:        "Development Test Plan",
		Description: "Fast test for Mac development",
		Duration:    parser.Duration{Duration: 50 * time.Millisecond}, // Ultra-fast
		Concurrency: 1,                                                // Single worker for Mac dev
		RampUp:      parser.Duration{Duration: 10 * time.Millisecond}, // Very fast ramp
		Scenarios: []parser.Scenario{
			{
				Name:        "Dev Scenario",
				Description: "Basic development test",
				Weight:      1,
				Requests: []parser.Request{
					{
						Name:   "Dev Request",
						Method: "GET",
						URL:    "http://localhost:8080/health", // Local endpoint
						Headers: map[string]string{
							"User-Agent": "NeuralMeterGo-Dev",
						},
					},
				},
			},
		},
		Global: parser.Global{
			BaseURL: "http://localhost:8080",
			Headers: map[string]string{
				"Accept": "application/json",
			},
			Timeout: parser.Duration{Duration: 100 * time.Millisecond}, // Fast timeout
		},
	}
}

// TestExecutionEngine_DevelopmentSafe tests basic functionality without timeouts
func TestExecutionEngine_DevelopmentSafe(t *testing.T) {
	testPlan := createDevelopmentTestPlan()

	config := &engine.EngineConfig{
		MaxWorkers:          1,
		WorkerQueueCapacity: 1,
		MetricsInterval:     10 * time.Millisecond,
		StopTimeout:         50 * time.Millisecond, // Very fast for Mac dev
		EnableDebugLogs:     true,                  // Enable for debugging
	}

	execEngine := engine.NewExecutionEngine(testPlan, config)
	if execEngine == nil {
		t.Fatal("Expected non-nil execution engine")
	}

	// Test initialization (safe operation)
	err := execEngine.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize engine: %v", err)
	}

	// Test state after initialization
	state := execEngine.GetState()
	if state.Status != engine.StatusInitializing {
		t.Errorf("Expected status to be %s after initialization, got %s",
			engine.StatusInitializing, state.Status)
	}

	// Test metrics (safe operation)
	metrics := execEngine.GetMetrics()
	if metrics.StartTime.IsZero() {
		t.Error("Expected non-zero start time in metrics after initialization")
	}
}

// TestExecutionEngine_MacDevelopment_StateTransitions tests state management
func TestExecutionEngine_MacDevelopment_StateTransitions(t *testing.T) {
	testPlan := createDevelopmentTestPlan()

	config := &engine.EngineConfig{
		MaxWorkers:          1,
		WorkerQueueCapacity: 1,
		MetricsInterval:     10 * time.Millisecond,
		StopTimeout:         50 * time.Millisecond,
		EnableDebugLogs:     true,
	}

	execEngine := engine.NewExecutionEngine(testPlan, config)

	// Test: Pending -> Initializing
	initialState := execEngine.GetState()
	if initialState.Status != engine.StatusPending {
		t.Errorf("Expected initial status to be %s, got %s", engine.StatusPending, initialState.Status)
	}

	// Test: Initialize
	err := execEngine.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize engine: %v", err)
	}

	// Test: Initializing state
	initState := execEngine.GetState()
	if initState.Status != engine.StatusInitializing {
		t.Errorf("Expected status to be %s after initialization, got %s",
			engine.StatusInitializing, initState.Status)
	}

	// Test: Metrics are available and valid
	metrics := execEngine.GetMetrics()
	if metrics.StartTime.IsZero() {
		t.Error("Expected non-zero start time after initialization")
	}

	// Test: Cannot initialize twice
	err = execEngine.Initialize()
	if err == nil {
		t.Error("Expected error when initializing already initialized engine")
	}
}

// TestExecutionEngine_MacDevelopment_ConfigValidation tests configuration validation
func TestExecutionEngine_MacDevelopment_ConfigValidation(t *testing.T) {
	testPlan := createDevelopmentTestPlan()

	// Test with valid minimal config
	validConfig := &engine.EngineConfig{
		MaxWorkers:          1,
		WorkerQueueCapacity: 1,
		MetricsInterval:     10 * time.Millisecond,
		StopTimeout:         50 * time.Millisecond,
		EnableDebugLogs:     false,
	}

	execEngine := engine.NewExecutionEngine(testPlan, validConfig)
	if execEngine == nil {
		t.Fatal("Expected non-nil execution engine with valid config")
	}

	err := execEngine.Initialize()
	if err != nil {
		t.Errorf("Expected no error with valid config, got: %v", err)
	}

	// Test configuration access
	state := execEngine.GetState()
	if state.Status != engine.StatusInitializing {
		t.Errorf("Expected status to be %s with valid config, got %s",
			engine.StatusInitializing, state.Status)
	}
}

// TestExecutionEngine_MacDevelopment_MetricsAccess tests metrics access safely
func TestExecutionEngine_MacDevelopment_MetricsAccess(t *testing.T) {
	testPlan := createDevelopmentTestPlan()

	config := &engine.EngineConfig{
		MaxWorkers:          1,
		WorkerQueueCapacity: 1,
		MetricsInterval:     10 * time.Millisecond,
		StopTimeout:         50 * time.Millisecond,
		EnableDebugLogs:     false,
	}

	execEngine := engine.NewExecutionEngine(testPlan, config)

	// Test metrics before initialization
	metrics := execEngine.GetMetrics()
	if !metrics.StartTime.IsZero() {
		t.Error("Expected zero start time before initialization")
	}

	// Initialize and test metrics
	err := execEngine.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize: %v", err)
	}

	// Test metrics after initialization
	metrics = execEngine.GetMetrics()
	if metrics.StartTime.IsZero() {
		t.Error("Expected non-zero start time after initialization")
	}
}

// TestExecutionEngine_MacDevelopment_ConcurrentOperations tests thread safety without worker pools
func TestExecutionEngine_MacDevelopment_ConcurrentOperations(t *testing.T) {
	testPlan := createDevelopmentTestPlan()

	config := &engine.EngineConfig{
		MaxWorkers:          1,
		WorkerQueueCapacity: 1,
		MetricsInterval:     10 * time.Millisecond,
		StopTimeout:         50 * time.Millisecond,
		EnableDebugLogs:     false,
	}

	execEngine := engine.NewExecutionEngine(testPlan, config)

	// Initialize once
	err := execEngine.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize: %v", err)
	}

	// Test concurrent read access (safe operations only)
	var wg sync.WaitGroup
	numGoroutines := 5 // Reduced for Mac development

	// Concurrent state reads
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < 5; j++ { // Reduced iterations
				_ = execEngine.GetState()
				time.Sleep(time.Millisecond)
			}
		}()
	}

	// Concurrent metrics reads
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < 5; j++ { // Reduced iterations
				_ = execEngine.GetMetrics()
				time.Sleep(time.Millisecond)
			}
		}()
	}

	// Wait for all goroutines with timeout
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// Success - all concurrent operations completed
	case <-time.After(500 * time.Millisecond): // Mac development timeout
		t.Error("Concurrent operations test timed out - may indicate thread safety issues")
	}
}

// TestExecutionEngine_MacDevelopment_ErrorScenarios tests error handling without hanging
func TestExecutionEngine_MacDevelopment_ErrorScenarios(t *testing.T) {
	testPlan := createDevelopmentTestPlan()

	config := &engine.EngineConfig{
		MaxWorkers:          1,
		WorkerQueueCapacity: 1,
		MetricsInterval:     10 * time.Millisecond,
		StopTimeout:         50 * time.Millisecond,
		EnableDebugLogs:     false,
	}

	// Test 1: Starting without initialization
	execEngine1 := engine.NewExecutionEngine(testPlan, config)
	err := execEngine1.Start()
	if err == nil {
		t.Error("Expected error when starting without initialization")
	}

	// Test 2: Double initialization
	execEngine2 := engine.NewExecutionEngine(testPlan, config)
	err = execEngine2.Initialize()
	if err != nil {
		t.Fatalf("First initialization failed: %v", err)
	}

	err = execEngine2.Initialize()
	if err == nil {
		t.Error("Expected error on double initialization")
	}

	// Test 3: Stop without start (should be safe)
	execEngine3 := engine.NewExecutionEngine(testPlan, config)
	err = execEngine3.Initialize()
	if err != nil {
		t.Fatalf("Third engine initialization failed: %v", err)
	}

	err = execEngine3.Stop()
	if err != nil {
		t.Logf("Stop without start returned error (may be expected): %v", err)
	}

	// Test 4: Invalid configuration handling
	invalidConfig := &engine.EngineConfig{
		MaxWorkers:          0, // Invalid
		WorkerQueueCapacity: 1,
		MetricsInterval:     10 * time.Millisecond,
		StopTimeout:         50 * time.Millisecond,
		EnableDebugLogs:     false,
	}

	execEngine4 := engine.NewExecutionEngine(testPlan, invalidConfig)
	if execEngine4 == nil {
		t.Error("Expected engine to be created even with invalid config (engine may handle validation)")
	}
}

// TestExecutionEngine_MacDevelopment_ContextHandling tests context behavior safely
func TestExecutionEngine_MacDevelopment_ContextHandling(t *testing.T) {
	testPlan := createDevelopmentTestPlan()

	config := &engine.EngineConfig{
		MaxWorkers:          1,
		WorkerQueueCapacity: 1,
		MetricsInterval:     10 * time.Millisecond,
		StopTimeout:         50 * time.Millisecond,
		EnableDebugLogs:     false,
	}

	execEngine := engine.NewExecutionEngine(testPlan, config)

	// Test initialization context
	err := execEngine.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize engine: %v", err)
	}

	// Test state consistency across operations
	state1 := execEngine.GetState()
	metrics1 := execEngine.GetMetrics()
	state2 := execEngine.GetState()
	metrics2 := execEngine.GetMetrics()

	// Verify context consistency
	if state1.Status != state2.Status {
		t.Errorf("State status changed unexpectedly: %s -> %s", state1.Status, state2.Status)
	}

	if metrics1.StartTime != metrics2.StartTime {
		t.Error("Metrics start time changed unexpectedly between calls")
	}

	// Test that engine maintains state during multiple quick operations
	for i := 0; i < 10; i++ {
		state := execEngine.GetState()
		if state.Status != engine.StatusInitializing {
			t.Errorf("Expected consistent status during rapid calls, got %s on iteration %d", state.Status, i)
		}
		time.Sleep(time.Millisecond)
	}
}
