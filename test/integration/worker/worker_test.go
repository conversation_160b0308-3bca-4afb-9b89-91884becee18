package worker_test

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"neuralmetergo/internal/worker"
)

func TestNewJobQueue(t *testing.T) {
	tests := []struct {
		name     string
		capacity int
		expected int
	}{
		{"positive capacity", 100, 100},
		{"zero capacity", 0, 1000},       // Should use default
		{"negative capacity", -50, 1000}, // Should use default
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jq := worker.NewJobQueue(tt.capacity)
			if jq.Capacity() != tt.expected {
				t.<PERSON>rf("Expected capacity %d, got %d", tt.expected, jq.Capacity())
			}
			if jq.<PERSON>ze() != 0 {
				t.<PERSON><PERSON><PERSON>("Expected initial size 0, got %d", jq.<PERSON>ze())
			}
			if jq.IsClosed() {
				t.<PERSON>r("Expected queue to be open initially")
			}
		})
	}
}

func TestJobQueue_Enqueue(t *testing.T) {
	jq := worker.NewJobQueue(2)

	// Test successful enqueue
	job1 := worker.Job{
		ID:   "job-1",
		Type: "test",
		Payload: map[string]interface{}{
			"data": "test data",
		},
	}

	err := jq.Enqueue(job1)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if jq.Size() != 1 {
		t.Errorf("Expected size 1, got %d", jq.Size())
	}

	// Test second enqueue
	job2 := worker.Job{
		ID:   "job-2",
		Type: "test",
		Payload: map[string]interface{}{
			"data": "test data 2",
		},
	}

	err = jq.Enqueue(job2)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if jq.Size() != 2 {
		t.Errorf("Expected size 2, got %d", jq.Size())
	}

	// Test queue full scenario
	job3 := worker.Job{
		ID:   "job-3",
		Type: "test",
		Payload: map[string]interface{}{
			"data": "test data 3",
		},
	}

	err = jq.Enqueue(job3)
	if err != worker.ErrQueueFull {
		t.Errorf("Expected ErrQueueFull, got %v", err)
	}
}

func TestJobQueue_EnqueueValidation(t *testing.T) {
	jq := worker.NewJobQueue(10)

	tests := []struct {
		name        string
		job         worker.Job
		expectedErr error
	}{
		{
			name: "missing ID",
			job: worker.Job{
				Type:    "test",
				Payload: map[string]interface{}{},
			},
			expectedErr: worker.ErrInvalidJob,
		},
		{
			name: "missing Type",
			job: worker.Job{
				ID:      "job-1",
				Payload: map[string]interface{}{},
			},
			expectedErr: worker.ErrInvalidJob,
		},
		{
			name: "valid job",
			job: worker.Job{
				ID:      "job-1",
				Type:    "test",
				Payload: map[string]interface{}{},
			},
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := jq.Enqueue(tt.job)
			if err != tt.expectedErr {
				t.Errorf("Expected error %v, got %v", tt.expectedErr, err)
			}
		})
	}
}

func TestJobQueue_Dequeue(t *testing.T) {
	jq := worker.NewJobQueue(10)

	// Test dequeue from empty queue
	_, err := jq.Dequeue()
	if err != worker.ErrQueueEmpty {
		t.Errorf("Expected ErrQueueEmpty, got %v", err)
	}

	// Add jobs and test dequeue
	job1 := worker.Job{
		ID:   "job-1",
		Type: "test",
		Payload: map[string]interface{}{
			"data": "test data 1",
		},
	}

	job2 := worker.Job{
		ID:   "job-2",
		Type: "test",
		Payload: map[string]interface{}{
			"data": "test data 2",
		},
	}

	jq.Enqueue(job1)
	jq.Enqueue(job2)

	// Dequeue first job
	dequeuedJob, err := jq.Dequeue()
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if dequeuedJob.ID != job1.ID {
		t.Errorf("Expected job ID %s, got %s", job1.ID, dequeuedJob.ID)
	}

	if jq.Size() != 1 {
		t.Errorf("Expected size 1, got %d", jq.Size())
	}

	// Dequeue second job
	dequeuedJob, err = jq.Dequeue()
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if dequeuedJob.ID != job2.ID {
		t.Errorf("Expected job ID %s, got %s", job2.ID, dequeuedJob.ID)
	}

	if jq.Size() != 0 {
		t.Errorf("Expected size 0, got %d", jq.Size())
	}
}

func TestJobQueue_EnqueueWithTimeout(t *testing.T) {
	jq := worker.NewJobQueue(1)

	job1 := worker.Job{
		ID:      "job-1",
		Type:    "test",
		Payload: map[string]interface{}{},
	}

	job2 := worker.Job{
		ID:      "job-2",
		Type:    "test",
		Payload: map[string]interface{}{},
	}

	// Fill the queue
	err := jq.Enqueue(job1)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Test timeout on full queue
	start := time.Now()
	err = jq.EnqueueWithTimeout(job2, 100*time.Millisecond)
	duration := time.Since(start)

	if err != worker.ErrQueueFull {
		t.Errorf("Expected ErrQueueFull, got %v", err)
	}

	if duration < 100*time.Millisecond {
		t.Errorf("Expected timeout to take at least 100ms, took %v", duration)
	}
}

func TestJobQueue_DequeueWithTimeout(t *testing.T) {
	jq := worker.NewJobQueue(10)

	// Test timeout on empty queue
	start := time.Now()
	_, err := jq.DequeueWithTimeout(100 * time.Millisecond)
	duration := time.Since(start)

	if err != worker.ErrQueueEmpty {
		t.Errorf("Expected ErrQueueEmpty, got %v", err)
	}

	if duration < 100*time.Millisecond {
		t.Errorf("Expected timeout to take at least 100ms, took %v", duration)
	}
}

func TestJobQueue_Results(t *testing.T) {
	jq := worker.NewJobQueue(10)

	result := worker.JobResult{
		JobID:    "job-1",
		Success:  true,
		Duration: 1000,
		Data: map[string]interface{}{
			"result": "success",
		},
	}

	// Test enqueue result
	err := jq.EnqueueResult(result)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Test dequeue result
	dequeuedResult, err := jq.DequeueResult()
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if dequeuedResult.JobID != result.JobID {
		t.Errorf("Expected job ID %s, got %s", result.JobID, dequeuedResult.JobID)
	}

	if dequeuedResult.Success != result.Success {
		t.Errorf("Expected success %v, got %v", result.Success, dequeuedResult.Success)
	}
}

func TestJobQueue_Metrics(t *testing.T) {
	jq := worker.NewJobQueue(10)

	job := worker.Job{
		ID:      "job-1",
		Type:    "test",
		Payload: map[string]interface{}{},
	}

	// Initial metrics
	metrics := jq.GetMetrics()
	if metrics.EnqueuedTotal != 0 {
		t.Errorf("Expected EnqueuedTotal 0, got %d", metrics.EnqueuedTotal)
	}

	// Enqueue job
	jq.Enqueue(job)
	metrics = jq.GetMetrics()
	if metrics.EnqueuedTotal != 1 {
		t.Errorf("Expected EnqueuedTotal 1, got %d", metrics.EnqueuedTotal)
	}
	if metrics.CurrentSize != 1 {
		t.Errorf("Expected CurrentSize 1, got %d", metrics.CurrentSize)
	}
	if metrics.PeakSize != 1 {
		t.Errorf("Expected PeakSize 1, got %d", metrics.PeakSize)
	}

	// Dequeue job
	jq.Dequeue()
	metrics = jq.GetMetrics()
	if metrics.DequeuedTotal != 1 {
		t.Errorf("Expected DequeuedTotal 1, got %d", metrics.DequeuedTotal)
	}
	if metrics.CurrentSize != 0 {
		t.Errorf("Expected CurrentSize 0, got %d", metrics.CurrentSize)
	}
	if metrics.PeakSize != 1 {
		t.Errorf("Expected PeakSize to remain 1, got %d", metrics.PeakSize)
	}
}

func TestJobQueue_Close(t *testing.T) {
	jq := worker.NewJobQueue(10)

	job := worker.Job{
		ID:      "job-1",
		Type:    "test",
		Payload: map[string]interface{}{},
	}

	// Enqueue before close
	err := jq.Enqueue(job)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Close queue
	jq.Close()

	if !jq.IsClosed() {
		t.Error("Expected queue to be closed")
	}

	// Try to enqueue after close
	err = jq.Enqueue(job)
	if err != worker.ErrQueueClosed {
		t.Errorf("Expected ErrQueueClosed, got %v", err)
	}

	// Try to enqueue result after close
	result := worker.JobResult{
		JobID:   "job-1",
		Success: true,
	}
	err = jq.EnqueueResult(result)
	if err != worker.ErrQueueClosed {
		t.Errorf("Expected ErrQueueClosed, got %v", err)
	}
}

func TestJobQueue_Clear(t *testing.T) {
	jq := worker.NewJobQueue(10)

	// Add multiple jobs
	for i := 0; i < 5; i++ {
		job := worker.Job{
			ID:      fmt.Sprintf("job-%d", i),
			Type:    "test",
			Payload: map[string]interface{}{},
		}
		jq.Enqueue(job)
	}

	if jq.Size() != 5 {
		t.Errorf("Expected size 5, got %d", jq.Size())
	}

	// Clear queue
	jq.Clear()

	if jq.Size() != 0 {
		t.Errorf("Expected size 0 after clear, got %d", jq.Size())
	}

	if !jq.IsEmpty() {
		t.Error("Expected queue to be empty after clear")
	}
}

// Concurrency Tests
func TestJobQueue_ConcurrentEnqueueDequeue(t *testing.T) {
	jq := worker.NewJobQueue(1000)
	numGoroutines := 10
	jobsPerGoroutine := 100

	var wg sync.WaitGroup

	// Concurrent enqueue
	wg.Add(numGoroutines)
	for i := 0; i < numGoroutines; i++ {
		go func(workerID int) {
			defer wg.Done()
			for j := 0; j < jobsPerGoroutine; j++ {
				job := worker.Job{
					ID:   fmt.Sprintf("worker-%d-job-%d", workerID, j),
					Type: "test",
					Payload: map[string]interface{}{
						"worker": workerID,
						"job":    j,
					},
				}
				if err := jq.Enqueue(job); err != nil {
					t.Errorf("Failed to enqueue job: %v", err)
				}
			}
		}(i)
	}

	// Concurrent dequeue
	wg.Add(numGoroutines)
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < jobsPerGoroutine; j++ {
				for {
					_, err := jq.Dequeue()
					if err == nil {
						break
					}
					if err != worker.ErrQueueEmpty {
						t.Errorf("Unexpected dequeue error: %v", err)
						return
					}
					time.Sleep(1 * time.Millisecond) // Brief wait for jobs
				}
			}
		}()
	}

	wg.Wait()

	// Verify final state
	if jq.Size() != 0 {
		t.Errorf("Expected final size 0, got %d", jq.Size())
	}

	metrics := jq.GetMetrics()
	expectedTotal := int64(numGoroutines * jobsPerGoroutine)
	if metrics.EnqueuedTotal != expectedTotal {
		t.Errorf("Expected EnqueuedTotal %d, got %d", expectedTotal, metrics.EnqueuedTotal)
	}
	if metrics.DequeuedTotal != expectedTotal {
		t.Errorf("Expected DequeuedTotal %d, got %d", expectedTotal, metrics.DequeuedTotal)
	}
}

func TestJobQueue_HighConcurrency(t *testing.T) {
	jq := worker.NewJobQueue(10000)
	numWorkers := 100
	jobsPerWorker := 1000

	var wg sync.WaitGroup

	// High concurrency test
	wg.Add(numWorkers * 2) // Both enqueuers and dequeuers

	// Enqueuers
	for i := 0; i < numWorkers; i++ {
		go func(workerID int) {
			defer wg.Done()
			for j := 0; j < jobsPerWorker; j++ {
				job := worker.Job{
					ID:   fmt.Sprintf("w%d-j%d", workerID, j),
					Type: "load-test",
					Payload: map[string]interface{}{
						"worker": workerID,
						"job":    j,
					},
				}
				for {
					if err := jq.Enqueue(job); err == nil {
						break
					}
					time.Sleep(1 * time.Microsecond)
				}
			}
		}(i)
	}

	// Dequeuers
	for i := 0; i < numWorkers; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < jobsPerWorker; j++ {
				for {
					if _, err := jq.Dequeue(); err == nil {
						break
					}
					time.Sleep(1 * time.Microsecond)
				}
			}
		}()
	}

	wg.Wait()

	// Verify metrics
	metrics := jq.GetMetrics()
	expectedTotal := int64(numWorkers * jobsPerWorker)
	if metrics.EnqueuedTotal != expectedTotal {
		t.Errorf("Expected EnqueuedTotal %d, got %d", expectedTotal, metrics.EnqueuedTotal)
	}
	if metrics.DequeuedTotal != expectedTotal {
		t.Errorf("Expected DequeuedTotal %d, got %d", expectedTotal, metrics.DequeuedTotal)
	}
}

// Benchmark Tests
func BenchmarkJobQueue_Enqueue(b *testing.B) {
	jq := worker.NewJobQueue(b.N)
	job := worker.Job{
		ID:      "benchmark-job",
		Type:    "benchmark",
		Payload: map[string]interface{}{"data": "test"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		if err := jq.Enqueue(job); err != nil {
			b.Fatalf("Failed to enqueue: %v", err)
		}
	}
}

func BenchmarkJobQueue_Dequeue(b *testing.B) {
	jq := worker.NewJobQueue(b.N)
	job := worker.Job{
		ID:      "benchmark-job",
		Type:    "benchmark",
		Payload: map[string]interface{}{"data": "test"},
	}

	// Pre-fill queue
	for i := 0; i < b.N; i++ {
		jq.Enqueue(job)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		if _, err := jq.Dequeue(); err != nil {
			b.Fatalf("Failed to dequeue: %v", err)
		}
	}
}

func BenchmarkJobQueue_EnqueueDequeue(b *testing.B) {
	jq := worker.NewJobQueue(1000)
	job := worker.Job{
		ID:      "benchmark-job",
		Type:    "benchmark",
		Payload: map[string]interface{}{"data": "test"},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		jq.Enqueue(job)
		jq.Dequeue()
	}
}

func BenchmarkJobQueue_ConcurrentOperations(b *testing.B) {
	jq := worker.NewJobQueue(10000)
	job := worker.Job{
		ID:      "benchmark-job",
		Type:    "benchmark",
		Payload: map[string]interface{}{"data": "test"},
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			jq.Enqueue(job)
			jq.Dequeue()
		}
	})
}

// Worker Tests

func TestNewWorker(t *testing.T) {
	jq := worker.NewJobQueue(10)
	pool := worker.NewWorkerPool(5, jq)

	w := worker.NewWorker(1, jq, pool)

	if w == nil {
		t.Fatal("Expected worker to be created, got nil")
	}

	if w.ID != 1 {
		t.Errorf("Expected worker ID 1, got %d", w.ID)
	}

	if w.IsRunning() {
		t.Error("Expected worker to not be running initially")
	}

	metrics := w.GetMetrics()
	if metrics.JobsProcessed != 0 {
		t.Errorf("Expected 0 jobs processed initially, got %d", metrics.JobsProcessed)
	}
}

func TestWorker_StartStop(t *testing.T) {
	jq := worker.NewJobQueue(10)
	pool := worker.NewWorkerPool(5, jq)
	w := worker.NewWorker(1, jq, pool)

	// Test starting worker
	w.Start()
	if !w.IsRunning() {
		t.Error("Expected worker to be running after Start()")
	}

	// Wait a moment for goroutine to start
	time.Sleep(10 * time.Millisecond)

	// Test stopping worker
	w.Stop()

	// Wait for goroutine to stop
	time.Sleep(50 * time.Millisecond)

	if w.IsRunning() {
		t.Error("Expected worker to not be running after Stop()")
	}
}

func TestWorker_ProcessDelayJob(t *testing.T) {
	jq := worker.NewJobQueue(10)
	pool := worker.NewWorkerPool(5, jq)
	w := worker.NewWorker(1, jq, pool)

	// Start the worker
	w.Start()
	defer w.Stop()

	// Create a delay job
	job := worker.Job{
		ID:   "delay-job-1",
		Type: "delay",
		Payload: map[string]interface{}{
			"delay_ms": float64(50), // 50ms delay
		},
	}

	// Enqueue the job
	err := jq.Enqueue(job)
	if err != nil {
		t.Fatalf("Failed to enqueue job: %v", err)
	}

	// Wait for job to be processed
	time.Sleep(200 * time.Millisecond)

	// Check result
	result, err := jq.DequeueResult()
	if err != nil {
		t.Fatalf("Failed to get result: %v", err)
	}

	if result.JobID != job.ID {
		t.Errorf("Expected result job ID %s, got %s", job.ID, result.JobID)
	}

	if !result.Success {
		t.Errorf("Expected successful result, got error: %s", result.Error)
	}

	if result.WorkerID != w.ID {
		t.Errorf("Expected worker ID %d, got %d", w.ID, result.WorkerID)
	}

	// Check that duration is reasonable (should be at least 50ms)
	if result.Duration < 45 {
		t.Errorf("Expected duration >= 45ms, got %dms", result.Duration)
	}

	// Check worker metrics
	metrics := w.GetMetrics()
	if metrics.JobsProcessed != 1 {
		t.Errorf("Expected 1 job processed, got %d", metrics.JobsProcessed)
	}

	if metrics.JobsSuccessful != 1 {
		t.Errorf("Expected 1 successful job, got %d", metrics.JobsSuccessful)
	}

	if metrics.JobsFailed != 0 {
		t.Errorf("Expected 0 failed jobs, got %d", metrics.JobsFailed)
	}
}

func TestWorker_ProcessInvalidJob(t *testing.T) {
	jq := worker.NewJobQueue(10)
	pool := worker.NewWorkerPool(5, jq)
	w := worker.NewWorker(1, jq, pool)

	// Start the worker
	w.Start()
	defer w.Stop()

	// Create an invalid job (unknown type)
	job := worker.Job{
		ID:   "invalid-job-1",
		Type: "unknown_type",
		Payload: map[string]interface{}{
			"data": "test",
		},
	}

	// Enqueue the job
	err := jq.Enqueue(job)
	if err != nil {
		t.Fatalf("Failed to enqueue job: %v", err)
	}

	// Wait for job to be processed
	time.Sleep(100 * time.Millisecond)

	// Check result
	result, err := jq.DequeueResult()
	if err != nil {
		t.Fatalf("Failed to get result: %v", err)
	}

	if result.Success {
		t.Error("Expected failed result for unknown job type")
	}

	if result.Error == "" {
		t.Error("Expected error message for unknown job type")
	}

	// Check worker metrics
	metrics := w.GetMetrics()
	if metrics.JobsProcessed != 1 {
		t.Errorf("Expected 1 job processed, got %d", metrics.JobsProcessed)
	}

	if metrics.JobsSuccessful != 0 {
		t.Errorf("Expected 0 successful jobs, got %d", metrics.JobsSuccessful)
	}

	if metrics.JobsFailed != 1 {
		t.Errorf("Expected 1 failed job, got %d", metrics.JobsFailed)
	}
}

func TestWorker_HTTPJobInvalidPayload(t *testing.T) {
	jq := worker.NewJobQueue(10)
	pool := worker.NewWorkerPool(5, jq)
	w := worker.NewWorker(1, jq, pool)

	// Start the worker
	w.Start()
	defer w.Stop()

	// Create an HTTP job with missing URL
	job := worker.Job{
		ID:   "http-job-invalid",
		Type: "http_request",
		Payload: map[string]interface{}{
			"method": "GET",
			// Missing URL
		},
	}

	// Enqueue the job
	err := jq.Enqueue(job)
	if err != nil {
		t.Fatalf("Failed to enqueue job: %v", err)
	}

	// Wait for job to be processed
	time.Sleep(100 * time.Millisecond)

	// Check result
	result, err := jq.DequeueResult()
	if err != nil {
		t.Fatalf("Failed to get result: %v", err)
	}

	if result.Success {
		t.Error("Expected failed result for HTTP job with missing URL")
	}

	if result.Error == "" {
		t.Error("Expected error message for missing URL")
	}

	// Check worker metrics
	metrics := w.GetMetrics()
	if metrics.JobsFailed != 1 {
		t.Errorf("Expected 1 failed job, got %d", metrics.JobsFailed)
	}
}

func TestWorker_ConcurrentJobs(t *testing.T) {
	jq := worker.NewJobQueue(100)
	pool := worker.NewWorkerPool(5, jq)

	// Create multiple workers
	var workers []*worker.Worker
	for i := 0; i < 3; i++ {
		w := worker.NewWorker(i+1, jq, pool)
		w.Start()
		workers = append(workers, w)
	}

	// Cleanup
	defer func() {
		for _, w := range workers {
			w.Stop()
		}
	}()

	// Enqueue multiple delay jobs
	const numJobs = 10
	for i := 0; i < numJobs; i++ {
		job := worker.Job{
			ID:   fmt.Sprintf("concurrent-job-%d", i),
			Type: "delay",
			Payload: map[string]interface{}{
				"delay_ms": float64(10), // 10ms delay
			},
		}

		err := jq.Enqueue(job)
		if err != nil {
			t.Fatalf("Failed to enqueue job %d: %v", i, err)
		}
	}

	// Wait for all jobs to be processed
	time.Sleep(500 * time.Millisecond)

	// Check that all jobs were processed
	processedJobs := 0
	successfulJobs := 0

	for _, w := range workers {
		metrics := w.GetMetrics()
		processedJobs += int(metrics.JobsProcessed)
		successfulJobs += int(metrics.JobsSuccessful)
	}

	if processedJobs != numJobs {
		t.Errorf("Expected %d jobs processed, got %d", numJobs, processedJobs)
	}

	if successfulJobs != numJobs {
		t.Errorf("Expected %d successful jobs, got %d", numJobs, successfulJobs)
	}

	// Check that results are available
	resultsCount := 0
	for i := 0; i < numJobs; i++ {
		_, err := jq.DequeueResult()
		if err == nil {
			resultsCount++
		}
	}

	if resultsCount != numJobs {
		t.Errorf("Expected %d results, got %d", numJobs, resultsCount)
	}
}

func TestWorker_GracefulShutdown(t *testing.T) {
	jq := worker.NewJobQueue(10)
	pool := worker.NewWorkerPool(5, jq)
	w := worker.NewWorker(1, jq, pool)

	// Start the worker
	w.Start()
	if !w.IsRunning() {
		t.Fatal("Worker should be running")
	}

	// Add a job that takes some time
	job := worker.Job{
		ID:   "shutdown-test-job",
		Type: "delay",
		Payload: map[string]interface{}{
			"delay_ms": float64(50), // 50ms delay
		},
	}

	err := jq.Enqueue(job)
	if err != nil {
		t.Fatalf("Failed to enqueue job: %v", err)
	}

	// Wait a moment for the job to be picked up
	time.Sleep(50 * time.Millisecond)

	// Stop the worker
	w.Stop()

	// Wait for shutdown
	time.Sleep(150 * time.Millisecond)

	if w.IsRunning() {
		t.Error("Worker should have stopped")
	}

	// The job should have been processed since it was already dequeued
	metrics := w.GetMetrics()
	if metrics.JobsProcessed == 0 {
		t.Log("No jobs were processed during shutdown - this is acceptable for graceful shutdown test")
	} else {
		t.Logf("Jobs processed during shutdown: %d", metrics.JobsProcessed)
	}
}

func BenchmarkWorker_ProcessDelayJobs(b *testing.B) {
	jq := worker.NewJobQueue(10000) // Larger queue for benchmark
	pool := worker.NewWorkerPool(5, jq)

	// Create multiple workers for better performance
	var workers []*worker.Worker
	for i := 0; i < 5; i++ {
		w := worker.NewWorker(i+1, jq, pool)
		w.Start()
		workers = append(workers, w)
	}

	defer func() {
		for _, w := range workers {
			w.Stop()
		}
	}()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		job := worker.Job{
			ID:   fmt.Sprintf("bench-job-%d", i),
			Type: "delay",
			Payload: map[string]interface{}{
				"delay_ms": float64(1), // 1ms delay
			},
		}

		// Use timeout to avoid blocking
		err := jq.EnqueueWithTimeout(job, 10*time.Millisecond)
		if err != nil {
			// Skip if queue is full - this is expected under load
			continue
		}
	}

	// Wait for processing to complete
	time.Sleep(200 * time.Millisecond)
}
