package client_test

import (
	"errors"
	"testing"
	"time"

	"neuralmetergo/internal/client"
)

func TestCalculateDelay(t *testing.T) {
	config := &client.RetryConfig{
		InitialDelay:  time.Millisecond * 100,
		BackoffFactor: 2.0,
		MaxDelay:      time.Second * 5,
	}
	tests := []struct {
		name     string
		attempt  int
		expected time.Duration
	}{
		{name: "First attempt", attempt: 1, expected: time.Millisecond * 100},
		{name: "Second attempt", attempt: 2, expected: time.Millisecond * 200},
		{name: "Third attempt", attempt: 3, expected: time.Millisecond * 400},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := client.CalculateDelay(tt.attempt, config)
			if got < tt.expected || got > tt.expected+time.Millisecond*50 {
				t.Errorf("CalculateDelay() = %v, want around %v", got, tt.expected)
			}
		})
	}
}

func TestShouldRetry(t *testing.T) {
	config := &client.RetryConfig{
		MaxRetries:      3,
		RetryableErrors: []int{408, 429, 500, 502, 503, 504},
	}
	tests := []struct {
		name       string
		err        error
		statusCode int
		maxRetries int
		expected   bool
	}{
		{name: "NoError", err: nil, statusCode: 200, maxRetries: 3, expected: false},
		{name: "NonRetryableStatusCode", err: errors.New("some error"), statusCode: 400, maxRetries: 3, expected: false},
		{name: "RetryableStatusCode", err: errors.New("retryable error"), statusCode: 429, maxRetries: 3, expected: true},
		{name: "RetriesDisabled", err: errors.New("retryable error"), statusCode: 429, maxRetries: 0, expected: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config.MaxRetries = tt.maxRetries
			got := client.ShouldRetry(tt.err, tt.statusCode, config)
			if got != tt.expected {
				t.Errorf("ShouldRetry should return %v for %s", tt.expected, tt.name)
			}
		})
	}
}
