package client

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"neuralmetergo/internal/client"
)

// TestComprehensiveTimeoutIntegration performs end-to-end testing of the timeout management system
func TestComprehensiveTimeoutIntegration(t *testing.T) {
	// Create test server with configurable delays
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		delay := r.URL.Query().Get("delay")
		if delay != "" {
			if duration, err := time.ParseDuration(delay); err == nil {
				time.Sleep(duration)
			}
		}

		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"message": "success"}`))
	}))
	defer server.Close()

	// Configure client with comprehensive timeout monitoring
	config := client.DefaultConfig()
	config.ResponseHeaderTimeout = 100 * time.Millisecond
	config.TimeoutStrategy = client.NewBalancedTimeoutStrategy()
	config.TimeoutMonitoring = client.DefaultTimeoutMonitoringConfig()
	config.TimeoutMonitoring.Enabled = true

	httpClient := client.NewHTTPClient(config)
	httpClient.DisableRetries() // Disable retries for predictable timeout testing
	defer httpClient.Close()

	ctx := context.Background()

	// Test 1: Successful requests within timeout
	t.Run("SuccessfulRequests", func(t *testing.T) {
		for i := 0; i < 10; i++ {
			_, err := httpClient.Get(ctx, server.URL+"?delay=50ms", nil)
			if err != nil {
				t.Errorf("Expected successful request, got error: %v", err)
			}
		}
	})

	// Test 2: Requests that should timeout
	t.Run("TimeoutRequests", func(t *testing.T) {
		// Reset stats for this test
		httpClient.ResetHTTPMethodStats()

		for i := 0; i < 5; i++ {
			_, err := httpClient.Get(ctx, server.URL+"?delay=300ms", nil) // Use longer delay to ensure timeout
			if err == nil {
				t.Error("Expected timeout error, got success")
			}
		}
	})

	// Test 3: Verify metrics accuracy
	t.Run("MetricsAccuracy", func(t *testing.T) {
		metrics := httpClient.GetHTTPMethodStats()
		totalRequests := metrics.GetTotalRequests()

		t.Logf("Total requests: %d", totalRequests)
		t.Logf("Timeout errors: %d", metrics.TimeoutErrors)
		t.Logf("Network errors: %d", metrics.NetworkErrors)
		t.Logf("Status2xx: %d", metrics.Status2xx)
		t.Logf("Status4xx: %d", metrics.Status4xx)
		t.Logf("Status5xx: %d", metrics.Status5xx)

		timeoutBreakdown := metrics.GetTimeoutBreakdown()
		t.Logf("Timeout breakdown: %+v", timeoutBreakdown)

		// Should only count requests from the timeout test (5 requests)
		if totalRequests != 5 {
			t.Errorf("Expected 5 total requests after reset, got %d", totalRequests)
		}

		if metrics.TimeoutErrors == 0 {
			t.Error("Expected timeout errors to be recorded")
		}

		// Verify timeout breakdown
		if timeoutBreakdown["total"] == 0 {
			t.Error("Expected timeout breakdown to show recorded timeouts")
		}
	})
}

// TestTimeoutMetricsAccuracy verifies the accuracy of timeout metrics and reporting
func TestTimeoutMetricsAccuracy(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Always timeout by sleeping longer than client timeout
		time.Sleep(200 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("too late"))
	}))
	defer server.Close()

	config := client.DefaultConfig()
	config.ResponseHeaderTimeout = 50 * time.Millisecond
	config.TimeoutMonitoring.Enabled = true

	httpClient := client.NewHTTPClient(config)
	httpClient.DisableRetries()
	defer httpClient.Close()

	ctx := context.Background()

	// Make several requests that will timeout
	expectedTimeouts := 5
	for i := 0; i < expectedTimeouts; i++ {
		_, err := httpClient.Get(ctx, server.URL, nil)
		if err == nil {
			t.Error("Expected timeout error")
		}
	}

	// Verify metrics accuracy
	metrics := httpClient.GetHTTPMethodStats()
	totalRequests := int(metrics.GetTotalRequests())

	if totalRequests != expectedTimeouts {
		t.Errorf("Expected %d total requests, got %d", expectedTimeouts, totalRequests)
	}

	timeoutErrors := int(metrics.TimeoutErrors)
	if timeoutErrors != expectedTimeouts {
		t.Errorf("Expected %d timeout errors, got %d", expectedTimeouts, timeoutErrors)
	}

	// Check timeout breakdown
	breakdown := metrics.GetTimeoutBreakdown()
	if breakdown["total"] != int64(expectedTimeouts) {
		t.Errorf("Expected %d total timeouts in breakdown, got %d", expectedTimeouts, breakdown["total"])
	}
}

// TestTimeoutRecoveryScenarios tests timeout recovery and resilience scenarios
func TestTimeoutRecoveryScenarios(t *testing.T) {
	requestCount := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		requestCount++
		// First few requests timeout, then succeed
		if requestCount <= 3 {
			time.Sleep(150 * time.Millisecond)
		} else {
			time.Sleep(20 * time.Millisecond)
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("response"))
	}))
	defer server.Close()

	config := client.DefaultConfig()
	config.ResponseHeaderTimeout = 100 * time.Millisecond
	config.TimeoutMonitoring.Enabled = true

	retryConfig := client.DefaultRetryConfig()
	retryConfig.MaxRetries = 0 // Disable retries for this test

	httpClient := client.NewHTTPClientWithRetry(config, retryConfig)
	defer httpClient.Close()

	ctx := context.Background()

	// Make requests - first should timeout, later should succeed
	timeouts := 0
	successes := 0

	for i := 0; i < 6; i++ {
		_, err := httpClient.Get(ctx, server.URL, nil)
		if err != nil {
			timeouts++
		} else {
			successes++
		}
	}

	if timeouts == 0 {
		t.Error("Expected some timeout errors")
	}
	if successes == 0 {
		t.Error("Expected some successful requests")
	}
}

// TestTimeoutDistributionTracking tests timeout value distribution tracking
func TestTimeoutDistributionTracking(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(200 * time.Millisecond) // Always timeout
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	config := client.DefaultConfig()
	config.ResponseHeaderTimeout = 100 * time.Millisecond
	config.TimeoutMonitoring.Enabled = true
	config.TimeoutMonitoring.DistributionBuckets = []time.Duration{
		50 * time.Millisecond,
		100 * time.Millisecond,
		200 * time.Millisecond,
		500 * time.Millisecond,
	}

	httpClient := client.NewHTTPClient(config)
	httpClient.DisableRetries()
	defer httpClient.Close()

	ctx := context.Background()

	// Make requests with timeouts
	for i := 0; i < 5; i++ {
		httpClient.Get(ctx, server.URL, nil)
	}

	// Check timeout distribution
	metrics := httpClient.GetHTTPMethodStats()
	distribution := metrics.GetTimeoutDistribution()

	if len(distribution) == 0 {
		t.Error("Expected timeout distribution to be populated")
	}
}

// TestTimeoutEdgeCases tests various edge cases and error conditions
func TestTimeoutEdgeCases(t *testing.T) {
	// Test with zero timeout
	t.Run("ZeroTimeout", func(t *testing.T) {
		config := client.DefaultConfig()
		config.ResponseHeaderTimeout = 0 // Zero timeout

		httpClient := client.NewHTTPClient(config)
		defer httpClient.Close()

		// This should still work (uses default timeouts)
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		}))
		defer server.Close()

		ctx := context.Background()
		_, err := httpClient.Get(ctx, server.URL, nil)
		if err != nil {
			t.Errorf("Unexpected error with zero timeout: %v", err)
		}
	})

	// Test with very large timeout
	t.Run("LargeTimeout", func(t *testing.T) {
		config := client.DefaultConfig()
		config.ResponseHeaderTimeout = 10 * time.Second // Very large timeout

		httpClient := client.NewHTTPClient(config)
		defer httpClient.Close()

		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			time.Sleep(50 * time.Millisecond)
			w.WriteHeader(http.StatusOK)
		}))
		defer server.Close()

		ctx := context.Background()
		_, err := httpClient.Get(ctx, server.URL, nil)
		if err != nil {
			t.Errorf("Unexpected error with large timeout: %v", err)
		}
	})

	// Test with cancelled context
	t.Run("CancelledContext", func(t *testing.T) {
		config := client.DefaultConfig()
		httpClient := client.NewHTTPClient(config)
		defer httpClient.Close()

		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			time.Sleep(200 * time.Millisecond)
			w.WriteHeader(http.StatusOK)
		}))
		defer server.Close()

		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		_, err := httpClient.Get(ctx, server.URL, nil)
		if err == nil {
			t.Error("Expected error with cancelled context")
		}
		if !strings.Contains(err.Error(), "context canceled") {
			t.Errorf("Expected context cancellation error, got: %v", err)
		}
	})
}

// TestTimeoutWithRetryInteraction tests timeout behavior with retry logic
func TestTimeoutWithRetryInteraction(t *testing.T) {
	requestAttempts := 0
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		requestAttempts++
		// Always timeout to test retry with timeout escalation
		time.Sleep(200 * time.Millisecond)
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	config := client.DefaultConfig()
	config.ResponseHeaderTimeout = 100 * time.Millisecond
	config.TimeoutStrategy = client.NewBalancedTimeoutStrategy()

	retryConfig := client.DefaultRetryConfig()
	retryConfig.MaxRetries = 2

	httpClient := client.NewHTTPClientWithRetry(config, retryConfig)
	defer httpClient.Close()

	ctx := context.Background()
	_, err := httpClient.Get(ctx, server.URL, nil)

	// Should have error due to timeouts
	if err == nil {
		t.Error("Expected timeout error even with retries")
	}

	// Should have attempted multiple requests due to retries
	if requestAttempts <= 1 {
		t.Errorf("Expected multiple retry attempts, got %d", requestAttempts)
	}

	// Check retry metrics
	metrics := httpClient.GetHTTPMethodStats()
	if metrics.RetryAttempts == 0 {
		t.Error("Expected retry attempts to be recorded")
	}
}

// TestConcurrentTimeoutHandling tests timeout handling under concurrent load
func TestConcurrentTimeoutHandling(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Mix of fast and slow responses
		delay := r.URL.Query().Get("delay")
		if delay == "slow" {
			time.Sleep(200 * time.Millisecond) // Will timeout
		} else {
			time.Sleep(20 * time.Millisecond) // Will succeed
		}
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("response"))
	}))
	defer server.Close()

	config := client.DefaultConfig()
	config.ResponseHeaderTimeout = 100 * time.Millisecond
	config.TimeoutMonitoring.Enabled = true

	httpClient := client.NewHTTPClient(config)
	httpClient.DisableRetries()
	defer httpClient.Close()

	// Run concurrent requests
	concurrency := 10
	done := make(chan bool, concurrency)

	for i := 0; i < concurrency; i++ {
		go func(index int) {
			defer func() { done <- true }()

			ctx := context.Background()
			url := server.URL
			if index%2 == 0 {
				url += "?delay=slow" // Half will timeout
			}

			httpClient.Get(ctx, url, nil)
		}(i)
	}

	// Wait for all to complete
	for i := 0; i < concurrency; i++ {
		<-done
	}

	// Verify metrics under concurrent load
	metrics := httpClient.GetHTTPMethodStats()
	totalRequests := int(metrics.GetTotalRequests())

	if totalRequests != concurrency {
		t.Errorf("Expected %d total requests, got %d", concurrency, totalRequests)
	}

	// Should have some timeouts and some successes
	if metrics.TimeoutErrors == 0 {
		t.Error("Expected some timeout errors under concurrent load")
	}

	successfulRequests := totalRequests - int(metrics.TimeoutErrors)
	if successfulRequests == 0 {
		t.Error("Expected some successful requests under concurrent load")
	}
}

// BenchmarkTimeoutOverhead benchmarks the performance overhead of timeout monitoring
func BenchmarkTimeoutOverhead(b *testing.B) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("fast response"))
	}))
	defer server.Close()

	b.Run("WithoutTimeoutMonitoring", func(b *testing.B) {
		config := client.DefaultConfig()
		config.TimeoutMonitoring.Enabled = false

		httpClient := client.NewHTTPClient(config)
		defer httpClient.Close()

		ctx := context.Background()

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			httpClient.Get(ctx, server.URL, nil)
		}
	})

	b.Run("WithTimeoutMonitoring", func(b *testing.B) {
		config := client.DefaultConfig()
		config.TimeoutMonitoring.Enabled = true

		httpClient := client.NewHTTPClient(config)
		defer httpClient.Close()

		ctx := context.Background()

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			httpClient.Get(ctx, server.URL, nil)
		}
	})

	b.Run("WithDynamicTimeoutAdjustment", func(b *testing.B) {
		config := client.DefaultConfig()
		config.TimeoutMonitoring.Enabled = true
		config.TimeoutStrategy = client.NewAdaptiveTimeoutStrategy(client.TimeoutStrategyBalanced)

		httpClient := client.NewHTTPClient(config)
		defer httpClient.Close()

		ctx := context.Background()

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			httpClient.Get(ctx, server.URL, nil)
		}
	})
}
