# Development-Safe Testing Strategy for NeuralMeterGo Metrics

## Overview

This directory contains a development-appropriate testing system designed to validate sliding window functionality without overwhelming development hardware. The system provides configurable test scales to ensure tests run safely on local Mac development environments while still validating core functionality.

## Problem Solved

The original sliding window tests were causing performance issues on development systems:

- **TestSlidingWindowConcurrency**: 0.71s (10 goroutines × 100 values = 1000 operations)
- **TestStorageConcurrency**: 0.20s (heavy concurrent storage operations)
- **TestCollectionSourceEnableDisable**: 0.45s (long-running collection tests)
- **Benchmark tests**: Could run millions of iterations overwhelming system resources

## Solution Architecture

### Test Scale Modes

The system provides three configurable test scales:

#### 1. **Development Scale** (Default)
- **Capacity**: 50 elements max
- **Concurrency**: 3 goroutines max
- **Values per worker**: 20 values max
- **Timeout**: 1 second
- **Statistics**: Disabled (expensive calculations skipped)
- **Async operations**: Disabled
- **Use case**: Local Mac development, safe for any hardware

#### 2. **Integration Scale**
- **Capacity**: 200 elements max
- **Concurrency**: 5 goroutines max
- **Values per worker**: 50 values max
- **Timeout**: 3 seconds
- **Statistics**: Enabled
- **Async operations**: Enabled
- **Use case**: CI environments, moderate hardware

#### 3. **Performance Scale**
- **Capacity**: 1000 elements (production scale)
- **Concurrency**: 10 goroutines max
- **Values per worker**: 100 values max
- **Timeout**: 10 seconds
- **Statistics**: Enabled (full calculations)
- **Async operations**: Enabled
- **Use case**: Cloud/server environments only

## Usage

### Running Tests

```bash
# Development mode (default) - Safe for Mac
go test ./test/unit/metrics -v -run TestSafe*

# Integration mode - For CI environments
NEURALMETER_TEST_MODE=integration go test ./test/unit/metrics -v -run TestSafe*

# Performance mode - For cloud/server only
NEURALMETER_TEST_MODE=performance go test ./test/unit/metrics -v -run TestSafe*
```

### Environment Variables

- `NEURALMETER_TEST_MODE`: Set to "development", "integration", or "performance"
- `RUN_BENCHMARKS`: Set to any value to force benchmark tests in development mode

### Test Types and Skipping Logic

The system automatically skips resource-intensive tests based on the current mode:

```go
// Example: Heavy concurrency tests skip in development mode
if testConfig.ShouldSkipTest("concurrent-heavy") {
    t.Skip("Skipping heavy concurrency test in development mode")
}

// Example: Statistics tests skip when statistics are disabled
if testConfig.ShouldSkipTest("statistics-heavy") {
    t.Skip("Skipping statistics test - disabled in current mode")
}

// Example: Benchmarks skip in development mode unless forced
if testConfig.ShouldSkipTest("benchmark") {
    b.Skip("Skipping benchmark in development mode")
}
```

## Test Files

### Core Files
- **`test_config.go`**: Configuration system and test scale definitions
- **`sliding_window_development_test.go`**: Development-safe sliding window tests

### Test Categories

#### Configuration Tests
- `TestDevelopmentModeConfiguration`: Validates test configuration system
- Ensures development mode uses safe parameters

#### Functional Tests  
- `TestSafeSlidingWindowCountBased`: Count-based windows with small datasets
- `TestSafeSlidingWindowTimeBased`: Time-based windows with short durations
- `TestSafeMemoryUsage`: Memory validation with reasonable limits

#### Performance Tests
- `TestSafeConcurrentAccess`: Concurrent operations with controlled parameters
- `TestSafeStatisticsCalculation`: Statistics validation with small datasets
- `TestSafeBenchmarkScaling`: Controlled benchmark iterations

#### Utility Tests
- `TestConfigurableTimeout`: Demonstrates timeout handling
- `BenchmarkSafeSlidingWindowAdd`: Safe benchmark practices

## Sample Output

### Development Mode (Default)
```bash
=== RUN   TestDevelopmentModeConfiguration
    sliding_window_development_test.go:17: Test scale: DEVELOPMENT (Mac-safe, minimal resources)
--- PASS: TestDevelopmentModeConfiguration (0.00s)

=== RUN   TestSafeConcurrentAccess
    sliding_window_development_test.go:125: Skipping heavy concurrency test in development mode
--- SKIP: TestSafeConcurrentAccess (0.00s)

=== RUN   TestSafeStatisticsCalculation
    sliding_window_development_test.go:198: Skipping statistics test - disabled in current mode
--- SKIP: TestSafeStatisticsCalculation (0.00s)
```

### Integration Mode
```bash
=== RUN   TestSafeConcurrentAccess
    sliding_window_development_test.go:137: Safe concurrency test: 5 goroutines, 50 values each
    sliding_window_development_test.go:189: Final window size: 200 (capacity: 200)
--- PASS: TestSafeConcurrentAccess (0.01s)

=== RUN   TestSafeStatisticsCalculation
    sliding_window_development_test.go:259: Statistics: Mean=5.50, P50=1.04, P95=1.09
--- PASS: TestSafeStatisticsCalculation (0.00s)
```

## Configuration Customization

### Manual Override for Edge Cases
```go
config := GetCurrentTestConfig()
override := TestConfigOverride{
    Capacity:    &customCapacity,
    Concurrency: &customConcurrency,
}
customConfig := config.ApplyOverride(override)
```

### Creating Custom Sliding Window Configs
```go
testConfig := GetCurrentTestConfig()
windowConfig := testConfig.CreateDevelopmentSlidingWindowConfig(metrics.WindowTypeCount)
```

## Performance Comparison

| Test Type | Original Duration | Development Mode | Integration Mode |
|-----------|-------------------|------------------|------------------|
| Concurrency | 0.71s | SKIP | 0.01s |
| Statistics | FAIL | SKIP | 0.00s |
| Time-based | 0.15s | 0.20s | 0.20s |
| Basic operations | 0.00s | 0.00s | 0.00s |

## Architecture Benefits

1. **Safety First**: Development mode prevents system overload
2. **Scalable Testing**: Higher modes available when needed
3. **Automatic Configuration**: Environment-based mode detection
4. **Selective Skipping**: Intelligent test filtering
5. **Configurable Parameters**: Fine-tuned resource limits
6. **Clear Documentation**: Obvious test intent and behavior

## Adding New Development-Safe Tests

When adding new tests that might be resource-intensive:

1. Use `GetCurrentTestConfig()` to get current configuration
2. Check `ShouldSkipTest()` for appropriate test types
3. Use configuration parameters for limits (capacity, concurrency, etc.)
4. Add appropriate logging to show what configuration is being used
5. Ensure tests pass in all three modes

```go
func TestMyNewFeature(t *testing.T) {
    testConfig := GetCurrentTestConfig()
    
    if testConfig.ShouldSkipTest("concurrent-heavy") {
        t.Skip("Skipping in development mode")
    }
    
    config := testConfig.CreateDevelopmentSlidingWindowConfig(metrics.WindowTypeCount)
    // Use config.Capacity, testConfig.MaxConcurrency, etc.
}
```

## Future Enhancements

- Add GPU/CPU detection for auto-scaling
- Implement memory usage monitoring
- Add test duration tracking and warnings
- Create performance regression detection
- Add cloud environment auto-detection 