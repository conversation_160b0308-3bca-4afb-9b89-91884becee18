package metrics

import (
	"fmt"
	"testing"
	"time"

	"neuralmetergo/internal/metrics"
)

// TestDevelopmentSafeTimeBucketing tests time bucketing with development-safe configurations
func TestDevelopmentSafeTimeBucketing(t *testing.T) {
	mode := GetCurrentTestConfig()
	if mode.Mode != DevelopmentScale {
		t.Skip("Skipping development-safe test in non-development mode")
	}

	t.Run("BasicTimeBucketCreation", func(t *testing.T) {
		testBasicTimeBucketCreation(t)
	})

	t.Run("TimeBucketAggregation", func(t *testing.T) {
		testTimeBucketAggregation(t, mode)
	})

	t.Run("TimeBucketManager", func(t *testing.T) {
		testTimeBucketManager(t, mode)
	})

	t.Run("ClockSkewHandling", func(t *testing.T) {
		testClockSkewHandling(t, mode)
	})

	t.Run("DownsamplingUpsampling", func(t *testing.T) {
		testDownsamplingUpsampling(t, mode)
	})
}

func testBasicTimeBucketCreation(t *testing.T) {
	startTime := time.Now().Truncate(time.Minute)
	bucket := metrics.NewTimeBucket(startTime, metrics.GranularityMinute)

	if bucket.StartTime != startTime {
		t.Errorf("Expected start time %v, got %v", startTime, bucket.StartTime)
	}

	expectedEndTime := startTime.Add(time.Minute)
	if bucket.EndTime != expectedEndTime {
		t.Errorf("Expected end time %v, got %v", expectedEndTime, bucket.EndTime)
	}

	if bucket.Granularity != metrics.GranularityMinute {
		t.Errorf("Expected granularity %v, got %v", metrics.GranularityMinute, bucket.Granularity)
	}

	if !bucket.IsEmpty() {
		t.Error("New bucket should be empty")
	}
}

func testTimeBucketAggregation(t *testing.T, mode TestModeConfig) {
	startTime := time.Now().Truncate(time.Minute)
	bucket := metrics.NewTimeBucket(startTime, metrics.GranularityMinute)

	// Add development-safe number of entries
	testValues := []float64{10.0, 20.0, 30.0, 40.0, 50.0}
	metricID := "test_metric"

	for i, value := range testValues {
		entry := metrics.TimeBucketEntry{
			Timestamp: startTime.Add(time.Duration(i) * time.Second * 10), // Spread across minute
			Value:     value,
			MetricID:  metricID,
			Source:    "test",
			Tags:      map[string]string{"test": "true"},
		}

		err := bucket.AddEntry(entry)
		if err != nil {
			t.Fatalf("Failed to add entry: %v", err)
		}
	}

	// Verify aggregation
	agg, exists := bucket.GetAggregation(metricID)
	if !exists {
		t.Fatal("Aggregation should exist for test metric")
	}

	if agg.Count != uint64(len(testValues)) {
		t.Errorf("Expected count %d, got %d", len(testValues), agg.Count)
	}

	expectedSum := 150.0 // 10+20+30+40+50
	if agg.Sum != expectedSum {
		t.Errorf("Expected sum %f, got %f", expectedSum, agg.Sum)
	}

	expectedMean := 30.0 // 150/5
	if agg.Mean != expectedMean {
		t.Errorf("Expected mean %f, got %f", expectedMean, agg.Mean)
	}

	if agg.Min != 10.0 {
		t.Errorf("Expected min 10.0, got %f", agg.Min)
	}

	if agg.Max != 50.0 {
		t.Errorf("Expected max 50.0, got %f", agg.Max)
	}

	// Check percentiles (approximate)
	if agg.P50 < 25.0 || agg.P50 > 35.0 {
		t.Errorf("P50 %f seems incorrect for values 10,20,30,40,50", agg.P50)
	}
}

func testTimeBucketManager(t *testing.T, mode TestModeConfig) {
	config := metrics.DefaultTimeBucketConfig(metrics.GranularitySecond)

	// Use development-safe settings
	config.RetentionBuckets = mode.BenchmarkIterations / 100 // Use reasonable bucket count
	config.MaxMemoryUsage = int64(1024 * 1024)               // 1MB limit for development
	config.EnableAsync = false                               // Synchronous for testing

	manager, err := metrics.NewTimeBucketManager(config)
	if err != nil {
		t.Fatalf("Failed to create bucket manager: %v", err)
	}

	err = manager.Start()
	if err != nil {
		t.Fatalf("Failed to start bucket manager: %v", err)
	}
	defer manager.Stop()

	// Add entries across multiple seconds
	baseTime := time.Now().Truncate(time.Second)
	for i := 0; i < mode.MaxConcurrency; i++ {
		entry := metrics.TimeBucketEntry{
			Timestamp: baseTime.Add(time.Duration(i) * time.Second),
			Value:     float64(i * 10),
			MetricID:  "test_metric",
			Source:    "test",
			Tags:      map[string]string{"iteration": fmt.Sprintf("%d", i)},
		}

		err := manager.AddEntry(entry)
		if err != nil {
			t.Errorf("Failed to add entry %d: %v", i, err)
		}
	}

	// Verify buckets were created
	stats := manager.GetStats()
	if stats.TotalBuckets <= 0 {
		t.Error("Expected at least one bucket to be created")
	}

	if stats.TotalEntries != int64(mode.MaxConcurrency) {
		t.Errorf("Expected %d entries, got %d", mode.MaxConcurrency, stats.TotalEntries)
	}

	// Test bucket retrieval
	bucket, exists := manager.GetBucket(baseTime)
	if !exists {
		t.Error("Expected bucket to exist for base time")
	}

	if bucket.EntryCount() == 0 {
		t.Error("Expected bucket to contain entries")
	}

	// Test latest buckets retrieval
	latestBuckets := manager.GetLatestBuckets(3)
	if len(latestBuckets) == 0 {
		t.Error("Expected to retrieve latest buckets")
	}
}

func testClockSkewHandling(t *testing.T, mode TestModeConfig) {
	config := metrics.DefaultTimeBucketConfig(metrics.GranularitySecond)
	config.MaxClockSkew = time.Second * 2 // Allow 2 seconds of skew
	config.EnableAsync = false

	manager, err := metrics.NewTimeBucketManager(config)
	if err != nil {
		t.Fatalf("Failed to create bucket manager: %v", err)
	}

	err = manager.Start()
	if err != nil {
		t.Fatalf("Failed to start bucket manager: %v", err)
	}
	defer manager.Stop()

	now := time.Now()

	// Test entry within acceptable skew
	validEntry := metrics.TimeBucketEntry{
		Timestamp: now.Add(-time.Second), // 1 second ago, within 2 second limit
		Value:     10.0,
		MetricID:  "test_metric",
		Source:    "test",
	}

	err = manager.AddEntry(validEntry)
	if err != nil {
		t.Errorf("Valid entry should be accepted: %v", err)
	}

	// Test entry beyond acceptable skew
	invalidEntry := metrics.TimeBucketEntry{
		Timestamp: now.Add(-time.Second * 5), // 5 seconds ago, beyond 2 second limit
		Value:     20.0,
		MetricID:  "test_metric",
		Source:    "test",
	}

	err = manager.AddEntry(invalidEntry)
	if err == nil {
		t.Error("Invalid entry should be rejected due to clock skew")
	}

	// Test future entry beyond acceptable skew
	futureEntry := metrics.TimeBucketEntry{
		Timestamp: now.Add(time.Second * 5), // 5 seconds in future, beyond 2 second limit
		Value:     30.0,
		MetricID:  "test_metric",
		Source:    "test",
	}

	err = manager.AddEntry(futureEntry)
	if err == nil {
		t.Error("Future entry should be rejected due to clock skew")
	}

	// Verify stats show dropped entries
	stats := manager.GetStats()
	if stats.LateDataDropped < 1 {
		t.Error("Expected at least one entry to be dropped due to clock skew")
	}
}

func testDownsamplingUpsampling(t *testing.T, mode TestModeConfig) {
	config := metrics.DefaultTimeBucketConfig(metrics.GranularitySecond)
	config.EnableDownsampling = true
	config.EnableUpsampling = true
	config.EnableAsync = false

	manager, err := metrics.NewTimeBucketManager(config)
	if err != nil {
		t.Fatalf("Failed to create bucket manager: %v", err)
	}

	err = manager.Start()
	if err != nil {
		t.Fatalf("Failed to start bucket manager: %v", err)
	}
	defer manager.Stop()

	// Create multiple second-granularity buckets
	baseTime := time.Now().Truncate(time.Minute)
	var sourceBuckets []*metrics.TimeBucket

	for i := 0; i < 5; i++ { // 5 seconds worth of data
		bucketTime := baseTime.Add(time.Duration(i) * time.Second)
		bucket := metrics.NewTimeBucket(bucketTime, metrics.GranularitySecond)

		// Add an entry to each bucket
		entry := metrics.TimeBucketEntry{
			Timestamp: bucketTime.Add(time.Millisecond * 500), // Middle of the second
			Value:     float64(i * 10),
			MetricID:  "test_metric",
			Source:    "test",
		}

		err := bucket.AddEntry(entry)
		if err != nil {
			t.Fatalf("Failed to add entry to bucket: %v", err)
		}

		sourceBuckets = append(sourceBuckets, bucket)
	}

	// Test downsampling (second -> minute)
	downsampledBucket, err := manager.DownsampleBucket(sourceBuckets, metrics.GranularityMinute)
	if err != nil {
		t.Fatalf("Failed to downsample buckets: %v", err)
	}

	if downsampledBucket.EntryCount() != 5 {
		t.Errorf("Expected 5 entries in downsampled bucket, got %d", downsampledBucket.EntryCount())
	}

	if downsampledBucket.Granularity != metrics.GranularityMinute {
		t.Errorf("Expected minute granularity, got %v", downsampledBucket.Granularity)
	}

	// Test upsampling (minute -> second)
	minuteBucket := metrics.NewTimeBucket(baseTime, metrics.GranularityMinute)

	// Add entries spanning the full minute
	for i := 0; i < 5; i++ {
		entry := metrics.TimeBucketEntry{
			Timestamp: baseTime.Add(time.Duration(i) * time.Second * 10), // Every 10 seconds
			Value:     float64(i * 20),
			MetricID:  "test_metric",
			Source:    "test",
		}
		err := minuteBucket.AddEntry(entry)
		if err != nil {
			t.Fatalf("Failed to add entry to minute bucket: %v", err)
		}
	}

	upsampledBuckets, err := manager.UpsampleBucket(minuteBucket, metrics.GranularitySecond)
	if err != nil {
		t.Fatalf("Failed to upsample bucket: %v", err)
	}

	if len(upsampledBuckets) != 60 { // 60 seconds in a minute
		t.Errorf("Expected 60 upsampled buckets, got %d", len(upsampledBuckets))
	}

	// Verify all upsampled buckets have correct granularity
	for i, bucket := range upsampledBuckets {
		if bucket.Granularity != metrics.GranularitySecond {
			t.Errorf("Upsampled bucket %d has wrong granularity: %v", i, bucket.Granularity)
		}
	}
}

// TestTimeBucketGranularityDurations tests that granularity durations are correct
func TestTimeBucketGranularityDurations(t *testing.T) {
	testCases := []struct {
		granularity      metrics.TimeBucketGranularity
		expectedDuration time.Duration
		expectedString   string
	}{
		{metrics.GranularitySecond, time.Second, "second"},
		{metrics.GranularityMinute, time.Minute, "minute"},
		{metrics.GranularityHour, time.Hour, "hour"},
		{metrics.GranularityDay, time.Hour * 24, "day"},
		{metrics.GranularityWeek, time.Hour * 24 * 7, "week"},
	}

	for _, tc := range testCases {
		t.Run(tc.expectedString, func(t *testing.T) {
			if tc.granularity.Duration() != tc.expectedDuration {
				t.Errorf("Expected duration %v, got %v", tc.expectedDuration, tc.granularity.Duration())
			}

			if tc.granularity.String() != tc.expectedString {
				t.Errorf("Expected string %s, got %s", tc.expectedString, tc.granularity.String())
			}
		})
	}
}

// TestTimeBucketErrorConditions tests error handling
func TestTimeBucketErrorConditions(t *testing.T) {
	// Test invalid bucket configurations
	invalidConfigs := []metrics.TimeBucketConfig{
		{
			Granularity:      -1, // Invalid granularity
			RetentionBuckets: 10,
		},
		{
			Granularity:      metrics.GranularitySecond,
			RetentionBuckets: 0, // Invalid retention
		},
	}

	for i, config := range invalidConfigs {
		t.Run(fmt.Sprintf("InvalidConfig%d", i), func(t *testing.T) {
			_, err := metrics.NewTimeBucketManager(config)
			if err == nil {
				t.Error("Expected error for invalid configuration")
			}
		})
	}

	// Test adding entry outside bucket range
	startTime := time.Now().Truncate(time.Minute)
	bucket := metrics.NewTimeBucket(startTime, metrics.GranularityMinute)

	outsideEntry := metrics.TimeBucketEntry{
		Timestamp: startTime.Add(time.Minute * 2), // 2 minutes later, outside 1-minute bucket
		Value:     10.0,
		MetricID:  "test",
		Source:    "test",
	}

	err := bucket.AddEntry(outsideEntry)
	if err == nil {
		t.Error("Expected error when adding entry outside bucket range")
	}
}

// BenchmarkTimeBucketOperations benchmarks basic time bucket operations with development-safe settings
func BenchmarkTimeBucketOperations(b *testing.B) {
	mode := GetCurrentTestConfig()
	if mode.Mode != DevelopmentScale {
		b.Skip("Skipping development benchmark in non-development mode")
	}

	startTime := time.Now().Truncate(time.Minute)
	bucket := metrics.NewTimeBucket(startTime, metrics.GranularityMinute)

	entry := metrics.TimeBucketEntry{
		Timestamp: startTime.Add(time.Second * 30),
		Value:     10.0,
		MetricID:  "benchmark_metric",
		Source:    "test",
		Tags:      map[string]string{"benchmark": "true"},
	}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		localEntry := entry // Copy for parallel access
		for pb.Next() {
			localEntry.Timestamp = localEntry.Timestamp.Add(time.Nanosecond) // Slight variation
			localEntry.Value += 0.1                                          // Slight variation
			_ = bucket.AddEntry(localEntry)
		}
	})
}

// TestMemoryUsageEstimation tests memory usage calculations
func TestMemoryUsageEstimation(t *testing.T) {
	startTime := time.Now().Truncate(time.Minute)
	bucket := metrics.NewTimeBucket(startTime, metrics.GranularityMinute)

	initialMemory := bucket.MemoryUsage()

	entry := metrics.TimeBucketEntry{
		Timestamp: startTime.Add(time.Second * 30),
		Value:     10.0,
		MetricID:  "test_metric_with_long_name",
		Source:    "test_source",
		Tags: map[string]string{
			"environment": "test",
			"service":     "neuralmetergo",
		},
	}

	err := bucket.AddEntry(entry)
	if err != nil {
		t.Fatalf("Failed to add entry: %v", err)
	}

	finalMemory := bucket.MemoryUsage()
	if finalMemory <= initialMemory {
		t.Error("Memory usage should increase after adding entry")
	}

	memoryIncrease := finalMemory - initialMemory
	if memoryIncrease <= 0 {
		t.Errorf("Expected positive memory increase, got %d", memoryIncrease)
	}

	// Clear and verify memory resets
	bucket.Clear()
	clearedMemory := bucket.MemoryUsage()
	if clearedMemory != 0 {
		t.Errorf("Expected 0 memory usage after clear, got %d", clearedMemory)
	}
}
