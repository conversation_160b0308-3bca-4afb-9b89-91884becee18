package metrics

import (
	"sync"
	"testing"
	"time"

	. "neuralmetergo/internal/metrics"
)

func TestNewMetricAggregator(t *testing.T) {
	aggregator := NewMetricAggregator()

	if aggregator == nil {
		t.Fatal("NewMetricAggregator returned nil")
	}

	config := aggregator.GetAggregationConfig()
	if config.Interval != DefaultAggregationConfig.Interval {
		t.Errorf("Expected interval %v, got %v", DefaultAggregationConfig.Interval, config.Interval)
	}

	if config.Type != DefaultAggregationConfig.Type {
		t.Errorf("Expected type %v, got %v", DefaultAggregationConfig.Type, config.Type)
	}

	if aggregator.IsRunning() {
		t.<PERSON>rror("Aggregator should not be running initially")
	}
}

func TestNewMetricAggregatorWithConfig(t *testing.T) {
	customConfig := AggregationConfig{
		Interval:    time.Second * 5,
		Type:        AggregationSum,
		Percentile:  0.99,
		BufferSize:  500,
		EnableAsync: false,
	}

	aggregator := NewMetricAggregatorWithConfig(customConfig)

	if aggregator == nil {
		t.Fatal("NewMetricAggregatorWithConfig returned nil")
	}

	config := aggregator.GetAggregationConfig()
	if config.Interval != customConfig.Interval {
		t.Errorf("Expected interval %v, got %v", customConfig.Interval, config.Interval)
	}

	if config.Type != customConfig.Type {
		t.Errorf("Expected type %v, got %v", customConfig.Type, config.Type)
	}

	if config.Percentile != customConfig.Percentile {
		t.Errorf("Expected percentile %v, got %v", customConfig.Percentile, config.Percentile)
	}

	if config.BufferSize != customConfig.BufferSize {
		t.Errorf("Expected buffer size %v, got %v", customConfig.BufferSize, config.BufferSize)
	}

	if config.EnableAsync != customConfig.EnableAsync {
		t.Errorf("Expected EnableAsync %v, got %v", customConfig.EnableAsync, config.EnableAsync)
	}
}

func TestRegisterCounter(t *testing.T) {
	aggregator := NewMetricAggregator()
	counter := NewCounter()

	aggregator.RegisterCounter("test_counter", counter)

	metrics := aggregator.GetRegisteredMetrics()
	found := false
	for _, name := range metrics {
		if name == "test_counter" {
			found = true
			break
		}
	}

	if !found {
		t.Error("Counter was not registered properly")
	}
}

func TestRegisterGauge(t *testing.T) {
	aggregator := NewMetricAggregator()
	gauge := NewGauge()

	aggregator.RegisterGauge("test_gauge", gauge)

	metrics := aggregator.GetRegisteredMetrics()
	found := false
	for _, name := range metrics {
		if name == "test_gauge" {
			found = true
			break
		}
	}

	if !found {
		t.Error("Gauge was not registered properly")
	}
}

func TestRegisterHistogram(t *testing.T) {
	aggregator := NewMetricAggregator()
	histogram := NewHistogram()

	aggregator.RegisterHistogram("test_histogram", histogram)

	metrics := aggregator.GetRegisteredMetrics()
	found := false
	for _, name := range metrics {
		if name == "test_histogram" {
			found = true
			break
		}
	}

	if !found {
		t.Error("Histogram was not registered properly")
	}
}

func TestStartStop(t *testing.T) {
	config := AggregationConfig{
		Interval:    time.Millisecond * 100,
		Type:        AggregationAverage,
		Percentile:  0.95,
		BufferSize:  100,
		EnableAsync: true,
	}

	aggregator := NewMetricAggregatorWithConfig(config)

	// Test start
	err := aggregator.Start()
	if err != nil {
		t.Fatalf("Failed to start aggregator: %v", err)
	}

	if !aggregator.IsRunning() {
		t.Error("Aggregator should be running after Start()")
	}

	// Test double start (should not error)
	err = aggregator.Start()
	if err != nil {
		t.Errorf("Double start should not error: %v", err)
	}

	// Test stop
	err = aggregator.Stop()
	if err != nil {
		t.Fatalf("Failed to stop aggregator: %v", err)
	}

	if aggregator.IsRunning() {
		t.Error("Aggregator should not be running after Stop()")
	}

	// Test double stop (should not error)
	err = aggregator.Stop()
	if err != nil {
		t.Errorf("Double stop should not error: %v", err)
	}
}

func TestPerformAggregationCounter(t *testing.T) {
	aggregator := NewMetricAggregator()
	counter := NewCounter()

	// Add some values
	counter.Add(10)
	counter.Add(20)
	counter.Add(30)

	aggregator.RegisterCounter("test_counter", counter)

	// Perform manual aggregation
	aggregator.PerformAggregation()

	// Check aggregated values
	values := aggregator.GetAggregatedValues("test_counter")
	if len(values) != 1 {
		t.Fatalf("Expected 1 aggregated value, got %d", len(values))
	}

	if values[0].Value != 60.0 {
		t.Errorf("Expected aggregated value 60.0, got %f", values[0].Value)
	}

	if values[0].Count != 1 {
		t.Errorf("Expected count 1, got %d", values[0].Count)
	}
}

func TestPerformAggregationGauge(t *testing.T) {
	aggregator := NewMetricAggregator()
	gauge := NewGauge()

	// Set gauge value
	err := gauge.Set(42.5)
	if err != nil {
		t.Fatalf("Failed to set gauge value: %v", err)
	}

	aggregator.RegisterGauge("test_gauge", gauge)

	// Perform manual aggregation
	aggregator.PerformAggregation()

	// Check aggregated values
	values := aggregator.GetAggregatedValues("test_gauge")
	if len(values) != 1 {
		t.Fatalf("Expected 1 aggregated value, got %d", len(values))
	}

	if values[0].Value != 42.5 {
		t.Errorf("Expected aggregated value 42.5, got %f", values[0].Value)
	}

	if values[0].Count != 1 {
		t.Errorf("Expected count 1, got %d", values[0].Count)
	}
}

func TestPerformAggregationHistogram(t *testing.T) {
	aggregator := NewMetricAggregator()
	histogram := NewHistogram()

	// Add observations
	histogram.Observe(0.1)
	histogram.Observe(0.2)
	histogram.Observe(0.3)
	histogram.Observe(0.4)
	histogram.Observe(0.5)

	aggregator.RegisterHistogram("test_histogram", histogram)

	// Perform manual aggregation
	aggregator.PerformAggregation()

	// Check aggregated values
	values := aggregator.GetAggregatedValues("test_histogram")
	if len(values) != 1 {
		t.Fatalf("Expected 1 aggregated value, got %d", len(values))
	}

	// For average aggregation, should be sum/count = 1.5/5 = 0.3
	expected := 0.3
	if values[0].Value != expected {
		t.Errorf("Expected aggregated value %f, got %f", expected, values[0].Value)
	}

	if values[0].Count != 5 {
		t.Errorf("Expected count 5, got %d", values[0].Count)
	}
}

func TestAggregationTypes(t *testing.T) {
	histogram := NewHistogram()
	histogram.Observe(1.0)
	histogram.Observe(2.0)
	histogram.Observe(3.0)
	histogram.Observe(4.0)
	histogram.Observe(5.0)

	testCases := []struct {
		aggType  AggregationType
		expected float64
	}{
		{AggregationSum, 15.0},    // 1+2+3+4+5
		{AggregationAverage, 3.0}, // 15/5
		{AggregationCount, 5.0},   // 5 observations
	}

	for _, tc := range testCases {
		config := AggregationConfig{
			Interval:    time.Second,
			Type:        tc.aggType,
			Percentile:  0.95,
			BufferSize:  100,
			EnableAsync: false,
		}

		aggregator := NewMetricAggregatorWithConfig(config)
		aggregator.RegisterHistogram("test_histogram", histogram)

		aggregator.PerformAggregation()

		values := aggregator.GetAggregatedValues("test_histogram")
		if len(values) != 1 {
			t.Fatalf("Expected 1 aggregated value for type %v, got %d", tc.aggType, len(values))
		}

		if values[0].Value != tc.expected {
			t.Errorf("For aggregation type %v, expected %f, got %f", tc.aggType, tc.expected, values[0].Value)
		}
	}
}

func TestBufferSizeLimit(t *testing.T) {
	config := AggregationConfig{
		Interval:    time.Millisecond,
		Type:        AggregationAverage,
		Percentile:  0.95,
		BufferSize:  3, // Small buffer for testing
		EnableAsync: false,
	}

	aggregator := NewMetricAggregatorWithConfig(config)
	counter := NewCounter()
	aggregator.RegisterCounter("test_counter", counter)

	// Perform multiple aggregations to exceed buffer size
	for i := 0; i < 5; i++ {
		counter.Add(int64(i + 1))
		aggregator.PerformAggregation()
	}

	values := aggregator.GetAggregatedValues("test_counter")

	// Should only have 3 values due to buffer size limit
	if len(values) != 3 {
		t.Errorf("Expected 3 values due to buffer limit, got %d", len(values))
	}

	// Should have the latest 3 values (counter accumulates: 6, 10, 15)
	expectedValues := []float64{6.0, 10.0, 15.0}
	for i, expected := range expectedValues {
		if values[i].Value != expected {
			t.Errorf("Expected value %f at index %d, got %f", expected, i, values[i].Value)
		}
	}
}

func TestGetLatestAggregatedValue(t *testing.T) {
	aggregator := NewMetricAggregator()
	counter := NewCounter()

	counter.Add(10)
	aggregator.RegisterCounter("test_counter", counter)
	aggregator.PerformAggregation()

	counter.Add(20)
	aggregator.PerformAggregation()

	latest := aggregator.GetLatestAggregatedValue("test_counter")
	if latest == nil {
		t.Fatal("GetLatestAggregatedValue returned nil")
	}

	if latest.Value != 30.0 {
		t.Errorf("Expected latest value 30.0, got %f", latest.Value)
	}

	// Test non-existent metric
	latest = aggregator.GetLatestAggregatedValue("non_existent")
	if latest != nil {
		t.Error("Expected nil for non-existent metric")
	}
}

func TestGetAllAggregatedValues(t *testing.T) {
	aggregator := NewMetricAggregator()
	counter := NewCounter()
	gauge := NewGauge()

	counter.Add(10)
	gauge.Set(42.0)

	aggregator.RegisterCounter("test_counter", counter)
	aggregator.RegisterGauge("test_gauge", gauge)

	aggregator.PerformAggregation()

	allValues := aggregator.GetAllAggregatedValues()

	if len(allValues) != 2 {
		t.Errorf("Expected 2 metrics, got %d", len(allValues))
	}

	if _, exists := allValues["test_counter"]; !exists {
		t.Error("test_counter not found in aggregated values")
	}

	if _, exists := allValues["test_gauge"]; !exists {
		t.Error("test_gauge not found in aggregated values")
	}
}

func TestClearAggregatedValues(t *testing.T) {
	aggregator := NewMetricAggregator()
	counter := NewCounter()

	counter.Add(10)
	aggregator.RegisterCounter("test_counter", counter)
	aggregator.PerformAggregation()

	// Verify values exist
	values := aggregator.GetAggregatedValues("test_counter")
	if len(values) != 1 {
		t.Fatalf("Expected 1 value before clear, got %d", len(values))
	}

	// Clear specific metric
	aggregator.ClearAggregatedValues("test_counter")

	values = aggregator.GetAggregatedValues("test_counter")
	if values != nil {
		t.Error("Expected nil after clearing values")
	}
}

func TestClearAllAggregatedValues(t *testing.T) {
	aggregator := NewMetricAggregator()
	counter := NewCounter()
	gauge := NewGauge()

	counter.Add(10)
	gauge.Set(42.0)

	aggregator.RegisterCounter("test_counter", counter)
	aggregator.RegisterGauge("test_gauge", gauge)

	aggregator.PerformAggregation()

	// Verify values exist
	allValues := aggregator.GetAllAggregatedValues()
	if len(allValues) != 2 {
		t.Fatalf("Expected 2 metrics before clear, got %d", len(allValues))
	}

	// Clear all
	aggregator.ClearAllAggregatedValues()

	allValues = aggregator.GetAllAggregatedValues()
	if len(allValues) != 0 {
		t.Errorf("Expected 0 metrics after clear all, got %d", len(allValues))
	}
}

func TestSetAggregationConfig(t *testing.T) {
	aggregator := NewMetricAggregator()

	newConfig := AggregationConfig{
		Interval:    time.Second * 30,
		Type:        AggregationMax,
		Percentile:  0.99,
		BufferSize:  2000,
		EnableAsync: false,
	}

	aggregator.SetAggregationConfig(newConfig)

	config := aggregator.GetAggregationConfig()

	if config.Interval != newConfig.Interval {
		t.Errorf("Expected interval %v, got %v", newConfig.Interval, config.Interval)
	}

	if config.Type != newConfig.Type {
		t.Errorf("Expected type %v, got %v", newConfig.Type, config.Type)
	}

	if config.Percentile != newConfig.Percentile {
		t.Errorf("Expected percentile %v, got %v", newConfig.Percentile, config.Percentile)
	}

	if config.BufferSize != newConfig.BufferSize {
		t.Errorf("Expected buffer size %v, got %v", newConfig.BufferSize, config.BufferSize)
	}

	if config.EnableAsync != newConfig.EnableAsync {
		t.Errorf("Expected EnableAsync %v, got %v", newConfig.EnableAsync, config.EnableAsync)
	}
}

func TestConcurrentAccess(t *testing.T) {
	aggregator := NewMetricAggregator()
	counter := NewCounter()
	aggregator.RegisterCounter("test_counter", counter)

	var wg sync.WaitGroup

	// Start multiple goroutines performing operations
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			// Add to counter
			counter.Add(int64(id))

			// Perform aggregation
			aggregator.PerformAggregation()

			// Read aggregated values
			values := aggregator.GetAggregatedValues("test_counter")
			if values == nil {
				t.Errorf("Goroutine %d: Expected values, got nil", id)
				return
			}
		}(i)
	}

	wg.Wait()

	// Verify final state
	values := aggregator.GetAggregatedValues("test_counter")
	if values == nil {
		t.Fatal("Expected final aggregated values, got nil")
	}

	if len(values) == 0 {
		t.Fatal("Expected at least one aggregated value")
	}
}

func TestAsyncAggregation(t *testing.T) {
	config := AggregationConfig{
		Interval:    time.Millisecond * 50,
		Type:        AggregationAverage,
		Percentile:  0.95,
		BufferSize:  100,
		EnableAsync: true,
	}

	aggregator := NewMetricAggregatorWithConfig(config)
	counter := NewCounter()
	aggregator.RegisterCounter("test_counter", counter)

	// Start aggregator
	err := aggregator.Start()
	if err != nil {
		t.Fatalf("Failed to start aggregator: %v", err)
	}
	defer aggregator.Stop()

	// Add some values
	counter.Add(10)
	counter.Add(20)
	counter.Add(30)

	// Wait for a few aggregation cycles
	time.Sleep(time.Millisecond * 200)

	// Check that aggregation happened
	values := aggregator.GetAggregatedValues("test_counter")
	if len(values) == 0 {
		t.Error("Expected aggregated values from async aggregation")
	}

	// Values should be 60 (since counter accumulates)
	if values[len(values)-1].Value != 60.0 {
		t.Errorf("Expected final aggregated value 60.0, got %f", values[len(values)-1].Value)
	}
}

func TestContextCancellation(t *testing.T) {
	config := AggregationConfig{
		Interval:    time.Millisecond * 10,
		Type:        AggregationAverage,
		Percentile:  0.95,
		BufferSize:  100,
		EnableAsync: true,
	}

	aggregator := NewMetricAggregatorWithConfig(config)

	// Start aggregator
	err := aggregator.Start()
	if err != nil {
		t.Fatalf("Failed to start aggregator: %v", err)
	}

	// Let it run briefly
	time.Sleep(time.Millisecond * 50)

	// Stop should clean up properly
	err = aggregator.Stop()
	if err != nil {
		t.Fatalf("Failed to stop aggregator: %v", err)
	}

	if aggregator.IsRunning() {
		t.Error("Aggregator should not be running after stop")
	}
}
