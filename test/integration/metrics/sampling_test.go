package metrics

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"neuralmetergo/internal/metrics"
)

// Test data helpers
func createTestSample(name, metricType string, value interface{}) *metrics.MetricSample {
	return &metrics.MetricSample{
		Timestamp:  time.Now(),
		MetricName: name,
		MetricType: metricType,
		Value:      value,
		Tags:       map[string]string{"service": "test", "env": "dev"},
		Weight:     1.0,
	}
}

// =============================================================================
// Uniform Sampler Tests
// =============================================================================

func TestUniformSampler_Creation(t *testing.T) {
	config := metrics.SamplerConfig{
		SamplerType: "uniform",
		SampleRate:  0.5,
		MaxSamples:  100,
		RandomSeed:  12345,
	}

	sampler := metrics.NewUniformSampler(config)
	if sampler == nil {
		t.Fatal("Failed to create uniform sampler")
	}

	stats := sampler.GetStats()
	if stats.SamplerType != "uniform" {
		t.Errorf("Expected sampler type 'uniform', got %s", stats.SamplerType)
	}

	if stats.SamplingRate != 0.5 {
		t.Errorf("Expected sampling rate 0.5, got %f", stats.SamplingRate)
	}
}

func TestUniformSampler_SamplingBehavior(t *testing.T) {
	config := metrics.SamplerConfig{
		SamplerType: "uniform",
		SampleRate:  0.5,
		MaxSamples:  1000,
		RandomSeed:  12345, // Fixed seed for reproducible tests
	}

	sampler := metrics.NewUniformSampler(config)

	// Test sampling decision
	totalTests := 1000
	sampledCount := 0

	for i := 0; i < totalTests; i++ {
		if sampler.ShouldSample(fmt.Sprintf("metric_%d", i), "counter", nil) {
			sampledCount++
		}
	}

	// With fixed seed and 50% rate, we should get approximately 50% sampling
	expectedRange := []int{400, 600} // Allow some variance
	if sampledCount < expectedRange[0] || sampledCount > expectedRange[1] {
		t.Errorf("Expected sampled count between %d-%d, got %d", expectedRange[0], expectedRange[1], sampledCount)
	}

	stats := sampler.GetStats()
	if stats.TotalMetrics != uint64(totalTests) {
		t.Errorf("Expected total metrics %d, got %d", totalTests, stats.TotalMetrics)
	}
}

func TestUniformSampler_AddSample(t *testing.T) {
	config := metrics.SamplerConfig{
		SamplerType: "uniform",
		SampleRate:  1.0, // 100% sampling for testing
		MaxSamples:  10,
		MemoryLimit: 1024 * 1024, // 1MB
	}

	sampler := metrics.NewUniformSampler(config)

	// Add samples
	for i := 0; i < 5; i++ {
		sample := createTestSample(fmt.Sprintf("metric_%d", i), "counter", int64(i))
		success := sampler.AddSample(sample)
		if !success {
			t.Errorf("Failed to add sample %d", i)
		}
	}

	samples := sampler.GetSamples()
	if len(samples) != 5 {
		t.Errorf("Expected 5 samples, got %d", len(samples))
	}

	// Verify sample weights
	for _, sample := range samples {
		if sample.Weight != 1.0 {
			t.Errorf("Expected sample weight 1.0, got %f", sample.Weight)
		}
		if sample.SamplerID != "uniform" {
			t.Errorf("Expected sampler ID 'uniform', got %s", sample.SamplerID)
		}
	}
}

func TestUniformSampler_MaxSamplesLimit(t *testing.T) {
	config := metrics.SamplerConfig{
		SamplerType: "uniform",
		SampleRate:  1.0,
		MaxSamples:  3, // Small limit for testing
		MemoryLimit: 1024 * 1024,
	}

	sampler := metrics.NewUniformSampler(config)

	// Add more samples than the limit
	for i := 0; i < 5; i++ {
		sample := createTestSample(fmt.Sprintf("metric_%d", i), "counter", int64(i))
		sampler.AddSample(sample)
	}

	samples := sampler.GetSamples()
	if len(samples) != 3 {
		t.Errorf("Expected 3 samples (max limit), got %d", len(samples))
	}

	// Verify that we kept the latest samples (FIFO removal)
	expectedNames := []string{"metric_2", "metric_3", "metric_4"}
	for i, sample := range samples {
		if sample.MetricName != expectedNames[i] {
			t.Errorf("Expected sample %d to be %s, got %s", i, expectedNames[i], sample.MetricName)
		}
	}
}

// =============================================================================
// Reservoir Sampler Tests
// =============================================================================

func TestReservoirSampler_Creation(t *testing.T) {
	config := metrics.SamplerConfig{
		SamplerType: "reservoir",
		MaxSamples:  100,
		RandomSeed:  12345,
	}

	sampler := metrics.NewReservoirSampler(config)
	if sampler == nil {
		t.Fatal("Failed to create reservoir sampler")
	}

	stats := sampler.GetStats()
	if stats.SamplerType != "reservoir" {
		t.Errorf("Expected sampler type 'reservoir', got %s", stats.SamplerType)
	}
}

func TestReservoirSampler_AlwaysSamples(t *testing.T) {
	config := metrics.SamplerConfig{
		SamplerType: "reservoir",
		MaxSamples:  100,
		RandomSeed:  12345,
	}

	sampler := metrics.NewReservoirSampler(config)

	// Reservoir sampler should always return true for ShouldSample
	for i := 0; i < 10; i++ {
		if !sampler.ShouldSample(fmt.Sprintf("metric_%d", i), "counter", nil) {
			t.Errorf("Reservoir sampler should always return true for ShouldSample")
		}
	}
}

func TestReservoirSampler_ReservoirBehavior(t *testing.T) {
	config := metrics.SamplerConfig{
		SamplerType: "reservoir",
		MaxSamples:  5, // Small reservoir for testing
		RandomSeed:  12345,
		MemoryLimit: 10240,
	}

	sampler := metrics.NewReservoirSampler(config)

	// Fill reservoir
	for i := 0; i < 5; i++ {
		sample := createTestSample(fmt.Sprintf("metric_%d", i), "counter", int64(i))
		success := sampler.AddSample(sample)
		if !success {
			t.Errorf("Failed to add sample %d to reservoir", i)
		}
	}

	samples := sampler.GetSamples()
	if len(samples) != 5 {
		t.Errorf("Expected 5 samples in reservoir, got %d", len(samples))
	}

	// Add more samples (should replace existing ones randomly)
	for i := 5; i < 20; i++ {
		sample := createTestSample(fmt.Sprintf("metric_%d", i), "counter", int64(i))
		sampler.AddSample(sample)
	}

	samples = sampler.GetSamples()
	if len(samples) != 5 {
		t.Errorf("Expected reservoir to maintain 5 samples, got %d", len(samples))
	}

	// Verify sampling rate calculation
	stats := sampler.GetStats()
	expectedRate := 5.0 / 20.0 // 5 samples out of 20 total
	if stats.SamplingRate != expectedRate {
		t.Errorf("Expected sampling rate %f, got %f", expectedRate, stats.SamplingRate)
	}
}

// =============================================================================
// Adaptive Sampler Tests
// =============================================================================

func TestAdaptiveSampler_Creation(t *testing.T) {
	config := metrics.SamplerConfig{
		SamplerType:     "adaptive",
		SampleRate:      0.5,
		MaxSamples:      100,
		AdaptiveEnabled: true,
		RandomSeed:      12345,
	}

	sampler := metrics.NewAdaptiveSampler(config)
	if sampler == nil {
		t.Fatal("Failed to create adaptive sampler")
	}

	stats := sampler.GetStats()
	if stats.SamplerType != "adaptive" {
		t.Errorf("Expected sampler type 'adaptive', got %s", stats.SamplerType)
	}
}

func TestAdaptiveSampler_FrequencyAdaptation(t *testing.T) {
	config := metrics.SamplerConfig{
		SamplerType:     "adaptive",
		SampleRate:      0.5,
		MaxSamples:      1000,
		AdaptiveEnabled: true,
		RandomSeed:      12345,
	}

	sampler := metrics.NewAdaptiveSampler(config)

	// Test high-frequency metric (should reduce sampling rate)
	highFreqMetric := "high_freq_metric"
	highFreqSampled := 0
	for i := 0; i < 2000; i++ { // High frequency
		if sampler.ShouldSample(highFreqMetric, "counter", nil) {
			highFreqSampled++
		}
	}

	// Test low-frequency metric (should increase sampling rate)
	lowFreqMetric := "low_freq_metric"
	lowFreqSampled := 0
	for i := 0; i < 5; i++ { // Low frequency
		if sampler.ShouldSample(lowFreqMetric, "counter", nil) {
			lowFreqSampled++
		}
	}

	// High frequency metric should have lower sampling rate than low frequency
	highFreqRate := float64(highFreqSampled) / 2000.0
	lowFreqRate := float64(lowFreqSampled) / 5.0

	if highFreqRate >= lowFreqRate {
		t.Errorf("Expected high frequency metric to have lower sampling rate. High: %f, Low: %f", highFreqRate, lowFreqRate)
	}
}

// =============================================================================
// Stratified Sampler Tests
// =============================================================================

func TestStratifiedSampler_Creation(t *testing.T) {
	config := metrics.SamplerConfig{
		SamplerType: "stratified",
		SampleRate:  0.5,
		MaxSamples:  100,
		RandomSeed:  12345,
	}

	sampler := metrics.NewStratifiedSampler(config)
	if sampler == nil {
		t.Fatal("Failed to create stratified sampler")
	}

	stats := sampler.GetStats()
	if stats.SamplerType != "stratified" {
		t.Errorf("Expected sampler type 'stratified', got %s", stats.SamplerType)
	}
}

func TestStratifiedSampler_StrataCreation(t *testing.T) {
	config := metrics.SamplerConfig{
		SamplerType: "stratified",
		SampleRate:  1.0, // 100% for testing
		MaxSamples:  90,  // Will be divided among strata
		RandomSeed:  12345,
		MemoryLimit: 10240, // 10KB memory limit
	}

	sampler := metrics.NewStratifiedSampler(config)

	// Add samples of different types
	metricTypes := []string{"counter", "gauge", "histogram"}
	for _, metricType := range metricTypes {
		for i := 0; i < 10; i++ {
			metricName := fmt.Sprintf("%s_metric_%d", metricType, i)
			shouldSample := sampler.ShouldSample(metricName, metricType, nil)
			if shouldSample {
				sample := createTestSample(metricName, metricType, int64(i))
				sampler.AddSample(sample)
			}
		}
	}

	samples := sampler.GetSamples()

	// With 100% sampling rate and proper memory limits, we should get all 30 samples
	if len(samples) != 30 {
		t.Errorf("Expected 30 samples with 100%% sampling rate, got %d", len(samples))
	}

	// Count samples by type
	typeCounts := make(map[string]int)
	for _, sample := range samples {
		typeCounts[sample.MetricType]++
	}

	// Each type should have samples (10 each)
	for _, metricType := range metricTypes {
		if typeCounts[metricType] != 10 {
			t.Errorf("Expected 10 samples for metric type %s, got %d", metricType, typeCounts[metricType])
		}
	}
}

// =============================================================================
// Sampling Manager Tests
// =============================================================================

func TestSamplingManager_Creation(t *testing.T) {
	manager := metrics.NewSamplingManager()
	if manager == nil {
		t.Fatal("Failed to create sampling manager")
	}

	if manager.IsRunning() {
		t.Error("Expected manager to not be running initially")
	}
}

func TestSamplingManager_StartStop(t *testing.T) {
	manager := metrics.NewSamplingManager()

	// Start manager
	err := manager.Start()
	if err != nil {
		t.Fatalf("Failed to start sampling manager: %v", err)
	}

	if !manager.IsRunning() {
		t.Error("Expected manager to be running after start")
	}

	// Stop manager
	err = manager.Stop()
	if err != nil {
		t.Fatalf("Failed to stop sampling manager: %v", err)
	}

	if manager.IsRunning() {
		t.Error("Expected manager to not be running after stop")
	}
}

func TestSamplingManager_SamplerRegistration(t *testing.T) {
	manager := metrics.NewSamplingManager()

	config := metrics.SamplerConfig{
		SamplerType: "uniform",
		SampleRate:  0.5,
		MaxSamples:  100,
		MemoryLimit: 10240,
	}

	sampler := metrics.NewUniformSampler(config)

	// Register sampler
	err := manager.RegisterSampler("test_sampler", sampler)
	if err != nil {
		t.Fatalf("Failed to register sampler: %v", err)
	}

	// Get sampler
	retrieved := manager.GetSampler("test_sampler")
	if retrieved == nil {
		t.Error("Failed to retrieve registered sampler")
	}

	// Unregister sampler
	manager.UnregisterSampler("test_sampler")
	retrieved = manager.GetSampler("test_sampler")
	// After unregistering, GetSampler should return the default sampler, not the named one
	if retrieved == sampler {
		t.Error("Expected named sampler to be unregistered (should return default sampler)")
	}
}

func TestSamplingManager_DefaultSampler(t *testing.T) {
	manager := metrics.NewSamplingManager()

	// Test with default sampler
	result := manager.ShouldSample("test_metric", "counter", nil, "")
	// Should not crash and should return a boolean
	_ = result

	sample := createTestSample("test_metric", "counter", int64(123))
	success := manager.AddSample(sample, "")
	if !success {
		t.Error("Failed to add sample to default sampler")
	}
}

func TestSamplingManager_NamedSampler(t *testing.T) {
	manager := metrics.NewSamplingManager()

	// Register a custom sampler
	config := metrics.SamplerConfig{
		SamplerType: "uniform",
		SampleRate:  1.0, // 100% for testing
		MaxSamples:  100,
		MemoryLimit: 10240, // 10KB memory limit
	}
	sampler := metrics.NewUniformSampler(config)
	manager.RegisterSampler("custom", sampler)

	// Use named sampler
	result := manager.ShouldSample("test_metric", "counter", nil, "custom")
	if !result {
		t.Error("Expected 100% sampling rate to return true")
	}

	sample := createTestSample("test_metric", "counter", int64(123))
	success := manager.AddSample(sample, "custom")
	if !success {
		t.Error("Failed to add sample to named sampler")
	}

	// Verify sample was added
	allSamples := manager.GetAllSamples()
	if len(allSamples["custom"]) != 1 {
		t.Errorf("Expected 1 sample in custom sampler, got %d", len(allSamples["custom"]))
	}
}

func TestSamplingManager_Stats(t *testing.T) {
	manager := metrics.NewSamplingManager()

	// Register samplers
	configs := map[string]metrics.SamplerConfig{
		"uniform": {
			SamplerType: "uniform",
			SampleRate:  0.5,
			MaxSamples:  100,
			MemoryLimit: 10240,
		},
		"reservoir": {
			SamplerType: "reservoir",
			MaxSamples:  50,
			MemoryLimit: 10240,
		},
	}

	for name, config := range configs {
		sampler, _ := metrics.NewSampler(config)
		manager.RegisterSampler(name, sampler)
	}

	stats := manager.GetStats()
	// Should have 2 named samplers + 1 default sampler = 3 total
	if stats.ActiveSamplers != 3 {
		t.Errorf("Expected 3 active samplers (2 named + 1 default), got %d", stats.ActiveSamplers)
	}

	if stats.SamplerStats == nil {
		t.Error("Expected sampler stats to be initialized")
	}
}

func TestSamplingManager_ConfigUpdate(t *testing.T) {
	manager := metrics.NewSamplingManager()

	originalConfig := manager.GetConfig()
	newConfig := originalConfig
	newConfig.CleanupInterval = time.Minute * 10

	err := manager.UpdateConfig(newConfig)
	if err != nil {
		t.Fatalf("Failed to update config: %v", err)
	}

	updatedConfig := manager.GetConfig()
	if updatedConfig.CleanupInterval != time.Minute*10 {
		t.Errorf("Expected cleanup interval to be updated to 10 minutes, got %v", updatedConfig.CleanupInterval)
	}
}

// =============================================================================
// NewSampler Factory Tests
// =============================================================================

func TestNewSampler_AllTypes(t *testing.T) {
	samplerTypes := []string{"uniform", "reservoir", "adaptive", "stratified"}

	for _, samplerType := range samplerTypes {
		config := metrics.SamplerConfig{
			SamplerType: samplerType,
			SampleRate:  0.5,
			MaxSamples:  100,
			RandomSeed:  12345,
			MemoryLimit: 10240,
		}

		sampler, err := metrics.NewSampler(config)
		if err != nil {
			t.Errorf("Failed to create %s sampler: %v", samplerType, err)
			continue
		}

		if sampler == nil {
			t.Errorf("Expected non-nil sampler for type %s", samplerType)
			continue
		}

		stats := sampler.GetStats()
		if stats.SamplerType != samplerType {
			t.Errorf("Expected sampler type %s, got %s", samplerType, stats.SamplerType)
		}
	}
}

func TestNewSampler_InvalidType(t *testing.T) {
	config := metrics.SamplerConfig{
		SamplerType: "invalid_type",
		SampleRate:  0.5,
		MaxSamples:  100,
	}

	sampler, err := metrics.NewSampler(config)
	if err == nil {
		t.Error("Expected error for invalid sampler type")
	}

	if sampler != nil {
		t.Error("Expected nil sampler for invalid type")
	}
}

// =============================================================================
// Concurrent Access Tests
// =============================================================================

func TestSamplers_ConcurrentAccess(t *testing.T) {
	samplerTypes := []string{"uniform", "reservoir", "adaptive", "stratified"}

	for _, samplerType := range samplerTypes {
		t.Run(fmt.Sprintf("Concurrent_%s", samplerType), func(t *testing.T) {
			config := metrics.SamplerConfig{
				SamplerType: samplerType,
				SampleRate:  0.5,
				MaxSamples:  1000,
				RandomSeed:  12345,
				MemoryLimit: 102400, // 100KB for concurrent test
			}

			sampler, err := metrics.NewSampler(config)
			if err != nil {
				t.Fatalf("Failed to create %s sampler: %v", samplerType, err)
			}

			// Concurrent operations
			var wg sync.WaitGroup
			numGoroutines := 10
			operationsPerGoroutine := 100

			// Concurrent ShouldSample calls
			for i := 0; i < numGoroutines; i++ {
				wg.Add(1)
				go func(id int) {
					defer wg.Done()
					for j := 0; j < operationsPerGoroutine; j++ {
						sampler.ShouldSample(fmt.Sprintf("metric_%d_%d", id, j), "counter", nil)
					}
				}(i)
			}

			// Concurrent AddSample calls
			for i := 0; i < numGoroutines; i++ {
				wg.Add(1)
				go func(id int) {
					defer wg.Done()
					for j := 0; j < operationsPerGoroutine; j++ {
						sample := createTestSample(fmt.Sprintf("metric_%d_%d", id, j), "counter", int64(j))
						sampler.AddSample(sample)
					}
				}(i)
			}

			// Concurrent GetSamples calls
			for i := 0; i < numGoroutines; i++ {
				wg.Add(1)
				go func() {
					defer wg.Done()
					for j := 0; j < operationsPerGoroutine; j++ {
						sampler.GetSamples()
						sampler.GetStats()
					}
				}()
			}

			wg.Wait()

			// Verify final state
			stats := sampler.GetStats()
			if stats.TotalMetrics == 0 {
				t.Error("Expected some metrics to be processed")
			}

			samples := sampler.GetSamples()
			if len(samples) > config.MaxSamples {
				t.Errorf("Expected max %d samples, got %d", config.MaxSamples, len(samples))
			}
		})
	}
}

func TestSamplingManager_ConcurrentAccess(t *testing.T) {
	manager := metrics.NewSamplingManager()
	manager.Start()
	defer manager.Stop()

	var wg sync.WaitGroup
	numGoroutines := 10
	operationsPerGoroutine := 50

	// Concurrent sampler registration/unregistration
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			config := metrics.SamplerConfig{
				SamplerType: "uniform",
				SampleRate:  0.5,
				MaxSamples:  100,
			}
			sampler := metrics.NewUniformSampler(config)

			samplerName := fmt.Sprintf("sampler_%d", id)
			manager.RegisterSampler(samplerName, sampler)

			// Use the sampler
			for j := 0; j < operationsPerGoroutine; j++ {
				manager.ShouldSample(fmt.Sprintf("metric_%d_%d", id, j), "counter", nil, samplerName)
				sample := createTestSample(fmt.Sprintf("metric_%d_%d", id, j), "counter", int64(j))
				manager.AddSample(sample, samplerName)
			}

			manager.UnregisterSampler(samplerName)
		}(i)
	}

	// Concurrent stats access
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < operationsPerGoroutine; j++ {
				manager.GetStats()
				manager.GetAllSamples()
			}
		}()
	}

	wg.Wait()

	// Verify manager state
	if !manager.IsRunning() {
		t.Error("Expected manager to still be running")
	}
}

// =============================================================================
// Memory Usage Tests
// =============================================================================

func TestEstimateMemoryUsage(t *testing.T) {
	sample := &metrics.MetricSample{
		Timestamp:  time.Now(),
		MetricName: "test_metric",
		MetricType: "counter",
		Value:      int64(123),
		Tags:       map[string]string{"service": "test", "env": "prod"},
		SamplerID:  "uniform",
		Weight:     1.0,
	}

	size := metrics.EstimateMemoryUsage(sample)
	if size <= 0 {
		t.Error("Expected positive memory usage estimate")
	}

	// Test with different value types
	testCases := []struct {
		name  string
		value interface{}
	}{
		{"int64", int64(123)},
		{"float64", float64(123.45)},
		{"string", "test_value"},
		{"float_slice", []float64{1.0, 2.0, 3.0}},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			sample.Value = tc.value
			size := metrics.EstimateMemoryUsage(sample)
			if size <= 0 {
				t.Errorf("Expected positive memory usage for %s", tc.name)
			}
		})
	}
}

func TestSampler_MemoryLimit(t *testing.T) {
	config := metrics.SamplerConfig{
		SamplerType: "uniform",
		SampleRate:  1.0,
		MaxSamples:  1000,
		MemoryLimit: 1024, // Very small limit for testing
	}

	sampler := metrics.NewUniformSampler(config)

	// Add samples until memory limit is reached
	addedSamples := 0
	for i := 0; i < 100; i++ {
		sample := createTestSample(fmt.Sprintf("large_metric_%d", i), "counter", make([]byte, 200))
		if sampler.AddSample(sample) {
			addedSamples++
		} else {
			break // Memory limit reached
		}
	}

	stats := sampler.GetStats()
	if stats.MemoryUsage > config.MemoryLimit {
		t.Errorf("Expected memory usage to not exceed limit %d, got %d", config.MemoryLimit, stats.MemoryUsage)
	}

	if addedSamples == 100 {
		t.Error("Expected memory limit to prevent adding all samples")
	}
}

// =============================================================================
// Performance Tests
// =============================================================================

func BenchmarkUniformSampler_ShouldSample(b *testing.B) {
	config := metrics.SamplerConfig{
		SamplerType: "uniform",
		SampleRate:  0.5,
		MaxSamples:  1000,
	}
	sampler := metrics.NewUniformSampler(config)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		sampler.ShouldSample("benchmark_metric", "counter", nil)
	}
}

func BenchmarkReservoirSampler_AddSample(b *testing.B) {
	config := metrics.SamplerConfig{
		SamplerType: "reservoir",
		MaxSamples:  1000,
	}
	sampler := metrics.NewReservoirSampler(config)

	samples := make([]*metrics.MetricSample, b.N)
	for i := 0; i < b.N; i++ {
		samples[i] = createTestSample(fmt.Sprintf("metric_%d", i), "counter", int64(i))
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		sampler.AddSample(samples[i])
	}
}

func BenchmarkSamplingManager_ShouldSample(b *testing.B) {
	manager := metrics.NewSamplingManager()
	manager.Start()
	defer manager.Stop()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		manager.ShouldSample("benchmark_metric", "counter", nil, "")
	}
}
