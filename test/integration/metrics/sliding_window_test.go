package metrics

import (
	"math"
	"sync"
	"testing"
	"time"

	"neuralmetergo/internal/metrics"
)

// TestCircularBuffer tests the circular buffer implementation
func TestCircularBuffer(t *testing.T) {
	tests := []struct {
		name        string
		capacity    int
		elements    []float64
		expectError bool
	}{
		{
			name:        "Valid capacity",
			capacity:    5,
			elements:    []float64{1.0, 2.0, 3.0},
			expectError: false,
		},
		{
			name:        "Zero capacity",
			capacity:    0,
			expectError: true,
		},
		{
			name:        "Negative capacity",
			capacity:    -1,
			expectError: true,
		},
		{
			name:        "Capacity overflow",
			capacity:    3,
			elements:    []float64{1.0, 2.0, 3.0, 4.0, 5.0}, // More than capacity
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			buffer, err := metrics.NewCircularBuffer(tt.capacity)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for capacity %d, got nil", tt.capacity)
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			// Test capacity
			if buffer.Capacity() != tt.capacity {
				t.Errorf("Expected capacity %d, got %d", tt.capacity, buffer.Capacity())
			}

			// Test initial size
			if buffer.Size() != 0 {
				t.Errorf("Expected initial size 0, got %d", buffer.Size())
			}

			// Add elements
			for i, value := range tt.elements {
				element := metrics.WindowElement{
					Value:     value,
					Timestamp: time.Now().Add(time.Duration(i) * time.Second),
				}
				buffer.Add(element)
			}

			// Test size after additions
			expectedSize := len(tt.elements)
			if expectedSize > tt.capacity {
				expectedSize = tt.capacity
			}

			if buffer.Size() != expectedSize {
				t.Errorf("Expected size %d, got %d", expectedSize, buffer.Size())
			}

			// Test GetAll
			elements := buffer.GetAll()
			if len(elements) != expectedSize {
				t.Errorf("Expected %d elements, got %d", expectedSize, len(elements))
			}

			// Test clear
			buffer.Clear()
			if buffer.Size() != 0 {
				t.Errorf("Expected size 0 after clear, got %d", buffer.Size())
			}
		})
	}
}

// TestTimeBasedWindow tests the time-based window implementation
func TestTimeBasedWindow(t *testing.T) {
	tests := []struct {
		name        string
		duration    time.Duration
		initialSize int
		cleanupTick time.Duration
		expectError bool
	}{
		{
			name:        "Valid configuration",
			duration:    time.Second * 10,
			initialSize: 100,
			cleanupTick: time.Second,
			expectError: false,
		},
		{
			name:        "Zero duration",
			duration:    0,
			expectError: true,
		},
		{
			name:        "Negative duration",
			duration:    -time.Second,
			expectError: true,
		},
		{
			name:        "Zero initial size (should default)",
			duration:    time.Second * 5,
			initialSize: 0,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			window, err := metrics.NewTimeBasedWindow(tt.duration, tt.initialSize, tt.cleanupTick)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error, got nil")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			// Test duration
			if window.Duration() != tt.duration {
				t.Errorf("Expected duration %v, got %v", tt.duration, window.Duration())
			}

			// Test initial size
			if window.Size() != 0 {
				t.Errorf("Expected initial size 0, got %d", window.Size())
			}

			// Add elements
			now := time.Now()
			elements := []metrics.WindowElement{
				{Value: 1.0, Timestamp: now.Add(-time.Second * 15)}, // Outside window
				{Value: 2.0, Timestamp: now.Add(-time.Second * 5)},  // Inside window
				{Value: 3.0, Timestamp: now},                        // Inside window
			}

			for _, element := range elements {
				window.Add(element)
			}

			// Only elements within the window should remain
			allElements := window.GetAll()
			expectedInWindow := 0
			for _, element := range elements {
				if now.Sub(element.Timestamp) <= tt.duration {
					expectedInWindow++
				}
			}

			if len(allElements) != expectedInWindow {
				t.Errorf("Expected %d elements in window, got %d", expectedInWindow, len(allElements))
			}

			// Test clear
			window.Clear()
			if window.Size() != 0 {
				t.Errorf("Expected size 0 after clear, got %d", window.Size())
			}
		})
	}
}

// TestTimeBasedWindowAsyncCleanup tests asynchronous cleanup functionality
func TestTimeBasedWindowAsyncCleanup(t *testing.T) {
	// Use a longer window (1 second) so current element doesn't get evicted during test delay
	window, err := metrics.NewTimeBasedWindow(time.Second, 10, time.Millisecond*50)
	if err != nil {
		t.Fatalf("Failed to create time-based window: %v", err)
	}

	// Start async cleanup
	window.StartAsyncCleanup()
	defer window.StopAsyncCleanup()

	// Add old element (way outside window)
	oldElement := metrics.WindowElement{
		Value:     1.0,
		Timestamp: time.Now().Add(-time.Second * 2), // Way outside 1-second window
	}
	window.Add(oldElement)

	// Add current element
	currentElement := metrics.WindowElement{
		Value:     2.0,
		Timestamp: time.Now(),
	}
	window.Add(currentElement)

	// Wait for cleanup to occur (less than window duration)
	time.Sleep(time.Millisecond * 200)

	// Only current element should remain
	elements := window.GetAll()
	if len(elements) != 1 {
		t.Errorf("Expected 1 element after cleanup, got %d", len(elements))
	}

	if len(elements) > 0 && elements[0].Value != 2.0 {
		t.Errorf("Expected remaining element to have value 2.0, got %f", elements[0].Value)
	}
}

// TestRunningStat tests the running statistics implementation
func TestRunningStat(t *testing.T) {
	stat := metrics.NewRunningStat()

	// Test initial state
	if stat.Count() != 0 {
		t.Errorf("Expected initial count 0, got %d", stat.Count())
	}

	if !math.IsNaN(stat.Mean()) {
		t.Errorf("Expected initial mean to be NaN, got %f", stat.Mean())
	}

	if !math.IsNaN(stat.Min()) {
		t.Errorf("Expected initial min to be NaN, got %f", stat.Min())
	}

	if !math.IsNaN(stat.Max()) {
		t.Errorf("Expected initial max to be NaN, got %f", stat.Max())
	}

	// Test with values
	values := []float64{1.0, 2.0, 3.0, 4.0, 5.0}
	for _, value := range values {
		stat.Update(value)
	}

	if stat.Count() != uint64(len(values)) {
		t.Errorf("Expected count %d, got %d", len(values), stat.Count())
	}

	expectedSum := 15.0
	if stat.Sum() != expectedSum {
		t.Errorf("Expected sum %f, got %f", expectedSum, stat.Sum())
	}

	expectedMean := 3.0
	if stat.Mean() != expectedMean {
		t.Errorf("Expected mean %f, got %f", expectedMean, stat.Mean())
	}

	expectedMin := 1.0
	if stat.Min() != expectedMin {
		t.Errorf("Expected min %f, got %f", expectedMin, stat.Min())
	}

	expectedMax := 5.0
	if stat.Max() != expectedMax {
		t.Errorf("Expected max %f, got %f", expectedMax, stat.Max())
	}

	// Test reset
	stat.Reset()
	if stat.Count() != 0 {
		t.Errorf("Expected count 0 after reset, got %d", stat.Count())
	}

	// Test invalid values
	stat.Update(math.NaN())
	stat.Update(math.Inf(1))
	stat.Update(math.Inf(-1))

	if stat.Count() != 0 {
		t.Errorf("Expected count 0 after invalid values, got %d", stat.Count())
	}
}

// TestSlidingWindowCountBased tests count-based sliding windows
func TestSlidingWindowCountBased(t *testing.T) {
	config := metrics.DefaultSlidingWindowConfig(metrics.WindowTypeCount)
	config.Capacity = 5
	config.EnableStats = true

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		t.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	// Test initial state
	if !window.IsEmpty() {
		t.Error("Expected empty window initially")
	}

	if window.Size() != 0 {
		t.Errorf("Expected size 0, got %d", window.Size())
	}

	// Add values
	values := []float64{1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0} // More than capacity

	for _, value := range values {
		err := window.Add(value)
		if err != nil {
			t.Errorf("Failed to add value %f: %v", value, err)
		}
	}

	// Should only have last 5 values
	if window.Size() != 5 {
		t.Errorf("Expected size 5, got %d", window.Size())
	}

	expectedValues := []float64{3.0, 4.0, 5.0, 6.0, 7.0}
	actualValues := window.GetValues()

	for i, expected := range expectedValues {
		if i >= len(actualValues) || actualValues[i] != expected {
			t.Errorf("Expected value[%d] = %f, got %f", i, expected, actualValues[i])
		}
	}

	// Test statistics
	expectedSum := 25.0 // 3+4+5+6+7
	if window.Sum() != expectedSum {
		t.Errorf("Expected sum %f, got %f", expectedSum, window.Sum())
	}

	expectedMean := 5.0
	if window.Mean() != expectedMean {
		t.Errorf("Expected mean %f, got %f", expectedMean, window.Mean())
	}

	expectedMin := 3.0
	if window.Min() != expectedMin {
		t.Errorf("Expected min %f, got %f", expectedMin, window.Min())
	}

	expectedMax := 7.0
	if window.Max() != expectedMax {
		t.Errorf("Expected max %f, got %f", expectedMax, window.Max())
	}

	// Test clear
	window.Clear()
	if !window.IsEmpty() {
		t.Error("Expected empty window after clear")
	}
}

// TestSlidingWindowTimeBased tests time-based sliding windows
func TestSlidingWindowTimeBased(t *testing.T) {
	config := metrics.DefaultSlidingWindowConfig(metrics.WindowTypeTime)
	config.Duration = time.Millisecond * 100
	config.EnableStats = true
	config.EnableAsync = false // Disable for predictable testing

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		t.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	// Add current values
	currentValues := []float64{1.0, 2.0, 3.0}
	for _, value := range currentValues {
		err := window.Add(value)
		if err != nil {
			t.Errorf("Failed to add value %f: %v", value, err)
		}
	}

	if window.Size() != len(currentValues) {
		t.Errorf("Expected size %d, got %d", len(currentValues), window.Size())
	}

	// Wait for values to expire
	time.Sleep(time.Millisecond * 150)

	// Add new value to trigger cleanup
	err = window.Add(4.0)
	if err != nil {
		t.Errorf("Failed to add value: %v", err)
	}

	// Only the new value should remain
	if window.Size() != 1 {
		t.Errorf("Expected size 1 after expiration, got %d", window.Size())
	}

	values := window.GetValues()
	if len(values) > 0 && values[0] != 4.0 {
		t.Errorf("Expected remaining value 4.0, got %f", values[0])
	}

	// Test rate calculation
	rate := window.Rate()
	expectedRate := 1.0 / config.Duration.Seconds() // 1 value per duration
	if math.Abs(rate-expectedRate) > 0.01 {
		t.Errorf("Expected rate approximately %f, got %f", expectedRate, rate)
	}
}

// TestSlidingWindowStatistics tests the comprehensive statistics functionality
func TestSlidingWindowStatistics(t *testing.T) {
	config := metrics.DefaultSlidingWindowConfig(metrics.WindowTypeCount)
	config.Capacity = 10
	config.EnableStats = true

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		t.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	// Add test data with known statistical properties
	values := []float64{1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0}
	for _, value := range values {
		window.Add(value)
	}

	stats := window.GetStatistics()

	// Test basic statistics
	if stats.Count != 10 {
		t.Errorf("Expected count 10, got %d", stats.Count)
	}

	if stats.Sum != 55.0 {
		t.Errorf("Expected sum 55.0, got %f", stats.Sum)
	}

	if stats.Mean != 5.5 {
		t.Errorf("Expected mean 5.5, got %f", stats.Mean)
	}

	if stats.Min != 1.0 {
		t.Errorf("Expected min 1.0, got %f", stats.Min)
	}

	if stats.Max != 10.0 {
		t.Errorf("Expected max 10.0, got %f", stats.Max)
	}

	if stats.Median != 5.5 {
		t.Errorf("Expected median 5.5, got %f", stats.Median)
	}

	// Test percentiles (approximate checks due to interpolation)
	if math.Abs(stats.P50-5.5) > 0.1 {
		t.Errorf("Expected P50 approximately 5.5, got %f", stats.P50)
	}

	if math.Abs(stats.P95-9.5) > 0.5 {
		t.Errorf("Expected P95 approximately 9.5, got %f", stats.P95)
	}

	// Test metadata
	if stats.WindowType != "count" {
		t.Errorf("Expected window type 'count', got '%s'", stats.WindowType)
	}

	if stats.WindowCapacity != config.Capacity {
		t.Errorf("Expected window capacity %d, got %d", config.Capacity, stats.WindowCapacity)
	}
}

// TestSlidingWindowConcurrency tests thread safety
func TestSlidingWindowConcurrency(t *testing.T) {
	config := metrics.DefaultSlidingWindowConfig(metrics.WindowTypeCount)
	config.Capacity = 1000
	config.EnableStats = true

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		t.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	const numGoroutines = 10
	const valuesPerGoroutine = 100

	var wg sync.WaitGroup

	// Concurrent writers
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()
			for j := 0; j < valuesPerGoroutine; j++ {
				value := float64(goroutineID*valuesPerGoroutine + j)
				window.Add(value)
			}
		}(i)
	}

	// Concurrent readers
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < valuesPerGoroutine; j++ {
				_ = window.GetValues()
				_ = window.GetStatistics()
				_ = window.Size()
				_ = window.Mean()
			}
		}()
	}

	wg.Wait()

	// Verify final state
	finalSize := window.Size()
	expectedSize := config.Capacity // Should be at capacity
	if finalSize != expectedSize {
		t.Errorf("Expected final size %d, got %d", expectedSize, finalSize)
	}

	// Verify statistics are reasonable
	stats := window.GetStatistics()
	if stats.Count != uint64(expectedSize) {
		t.Errorf("Expected count %d, got %d", expectedSize, stats.Count)
	}

	if math.IsNaN(stats.Mean) {
		t.Error("Expected valid mean, got NaN")
	}
}

// TestSlidingWindowInvalidValues tests handling of invalid values
func TestSlidingWindowInvalidValues(t *testing.T) {
	config := metrics.DefaultSlidingWindowConfig(metrics.WindowTypeCount)
	config.Capacity = 5

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		t.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	// Test invalid values
	invalidValues := []float64{
		math.NaN(),
		math.Inf(1),
		math.Inf(-1),
	}

	for _, value := range invalidValues {
		err := window.Add(value)
		if err == nil {
			t.Errorf("Expected error for invalid value %f, got nil", value)
		}
	}

	// Should still be empty
	if !window.IsEmpty() {
		t.Error("Expected window to remain empty after invalid values")
	}

	// Add valid values
	validValues := []float64{1.0, 2.0, 3.0}
	for _, value := range validValues {
		err := window.Add(value)
		if err != nil {
			t.Errorf("Failed to add valid value %f: %v", value, err)
		}
	}

	if window.Size() != len(validValues) {
		t.Errorf("Expected size %d, got %d", len(validValues), window.Size())
	}
}

// TestSlidingWindowMemoryUsage tests memory usage calculation
func TestSlidingWindowMemoryUsage(t *testing.T) {
	config := metrics.DefaultSlidingWindowConfig(metrics.WindowTypeCount)
	config.Capacity = 100

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		t.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	memUsage := window.MemoryUsage()
	if memUsage <= 0 {
		t.Error("Expected positive memory usage")
	}

	// Memory usage should be reasonable (not gigabytes for 100 elements)
	if memUsage > 1024*1024 { // 1MB
		t.Errorf("Memory usage seems too high: %d bytes", memUsage)
	}
}

// TestSlidingWindowConfig tests various configuration options
func TestSlidingWindowConfig(t *testing.T) {
	tests := []struct {
		name        string
		config      metrics.SlidingWindowConfig
		expectError bool
	}{
		{
			name:   "Valid count config",
			config: metrics.DefaultSlidingWindowConfig(metrics.WindowTypeCount),
		},
		{
			name:   "Valid time config",
			config: metrics.DefaultSlidingWindowConfig(metrics.WindowTypeTime),
		},
		{
			name: "Invalid count capacity",
			config: metrics.SlidingWindowConfig{
				Type:     metrics.WindowTypeCount,
				Capacity: 0,
			},
			expectError: true,
		},
		{
			name: "Invalid time duration",
			config: metrics.SlidingWindowConfig{
				Type:     metrics.WindowTypeTime,
				Duration: 0,
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			window, err := metrics.NewSlidingWindow(tt.config)

			if tt.expectError {
				if err == nil {
					t.Error("Expected error, got nil")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if window == nil {
				t.Error("Expected valid window, got nil")
				return
			}

			window.Close()
		})
	}
}

// BenchmarkSlidingWindowAdd benchmarks the Add operation
func BenchmarkSlidingWindowAdd(b *testing.B) {
	config := metrics.DefaultSlidingWindowConfig(metrics.WindowTypeCount)
	config.Capacity = 1000
	config.EnableStats = true

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		b.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		window.Add(float64(i))
	}
}

// BenchmarkSlidingWindowGetValues benchmarks the GetValues operation
func BenchmarkSlidingWindowGetValues(b *testing.B) {
	config := metrics.DefaultSlidingWindowConfig(metrics.WindowTypeCount)
	config.Capacity = 1000

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		b.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	// Fill window
	for i := 0; i < 1000; i++ {
		window.Add(float64(i))
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = window.GetValues()
	}
}

// BenchmarkSlidingWindowStatistics benchmarks the GetStatistics operation
func BenchmarkSlidingWindowStatistics(b *testing.B) {
	config := metrics.DefaultSlidingWindowConfig(metrics.WindowTypeCount)
	config.Capacity = 1000
	config.EnableStats = true

	window, err := metrics.NewSlidingWindow(config)
	if err != nil {
		b.Fatalf("Failed to create sliding window: %v", err)
	}
	defer window.Close()

	// Fill window
	for i := 0; i < 1000; i++ {
		window.Add(float64(i))
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = window.GetStatistics()
	}
}
