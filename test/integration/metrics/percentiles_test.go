package metrics

import (
	"math"
	"testing"

	"neuralmetergo/internal/metrics"
)

func TestExactPercentileCalculator(t *testing.T) {
	calc := metrics.NewExactPercentileCalculator()

	// Test empty dataset
	_, err := calc.Quantile(0.5)
	if err != metrics.ErrEmptyDataset {
		t.<PERSON>rf("Expected ErrEmptyDataset for empty dataset, got %v", err)
	}

	// Test single value
	calc.Add(42.0)
	if result, err := calc.Quantile(0.5); err != nil || result != 42.0 {
		t.<PERSON><PERSON><PERSON>("Expected 42.0 for single value median, got %v, err %v", result, err)
	}

	// Test multiple values
	calc.Reset()
	values := []float64{1, 2, 3, 4, 5}
	for _, v := range values {
		calc.Add(v)
	}

	// Test median (50th percentile)
	if result, err := calc.Quantile(0.5); err != nil || result != 3.0 {
		t.<PERSON><PERSON><PERSON>("Expected 3.0 for median, got %v, err %v", result, err)
	}

	// Test minimum (0th percentile)
	if result, err := calc.Quantile(0.0); err != nil || result != 1.0 {
		t.Errorf("Expected 1.0 for minimum, got %v, err %v", result, err)
	}

	// Test maximum (100th percentile)
	if result, err := calc.Quantile(1.0); err != nil || result != 5.0 {
		t.Errorf("Expected 5.0 for maximum, got %v, err %v", result, err)
	}

	// Test 25th percentile
	if result, err := calc.Quantile(0.25); err != nil || result != 2.0 {
		t.Errorf("Expected 2.0 for 25th percentile, got %v, err %v", result, err)
	}

	// Test invalid quantile
	if _, err := calc.Quantile(-0.1); err != metrics.ErrInvalidQuantile {
		t.Errorf("Expected ErrInvalidQuantile for negative quantile, got %v", err)
	}

	if _, err := calc.Quantile(1.1); err != metrics.ErrInvalidQuantile {
		t.Errorf("Expected ErrInvalidQuantile for quantile > 1, got %v", err)
	}

	// Test NaN and Inf values are ignored
	calc.Reset()
	calc.Add(1.0)
	calc.Add(math.NaN())
	calc.Add(2.0)
	calc.Add(math.Inf(1))
	calc.Add(3.0)

	if result, err := calc.Quantile(0.5); err != nil || result != 2.0 {
		t.Errorf("Expected 2.0 for median with NaN/Inf filtered, got %v, err %v", result, err)
	}

	// Verify count excludes NaN/Inf
	if calc.Count() != 3 {
		t.Errorf("Expected count 3 (excluding NaN/Inf), got %d", calc.Count())
	}
}

func TestTDigestPercentileCalculator(t *testing.T) {
	calc := metrics.NewTDigestPercentileCalculator(100)

	// Test empty dataset
	_, err := calc.Quantile(0.5)
	if err != metrics.ErrEmptyDataset {
		t.Errorf("Expected ErrEmptyDataset for empty dataset, got %v", err)
	}

	// Test single value
	calc.Add(42.0)
	if result, err := calc.Quantile(0.5); err != nil || result != 42.0 {
		t.Errorf("Expected 42.0 for single value median, got %v, err %v", result, err)
	}

	// Test with known data
	calc.Reset()
	for i := 1; i <= 100; i++ {
		calc.Add(float64(i))
	}

	// Test median should be around 50.5
	if result, err := calc.Quantile(0.5); err != nil {
		t.Errorf("Error calculating median: %v", err)
	} else if math.Abs(result-50.5) > 2.0 { // Allow some approximation error
		t.Errorf("Expected median around 50.5, got %v", result)
	}

	// Test 95th percentile should be around 95
	if result, err := calc.Quantile(0.95); err != nil {
		t.Errorf("Error calculating 95th percentile: %v", err)
	} else if math.Abs(result-95.0) > 3.0 { // Allow some approximation error
		t.Errorf("Expected 95th percentile around 95, got %v", result)
	}

	// Test algorithm type
	if calc.Algorithm() != metrics.AlgorithmTDigest {
		t.Errorf("Expected AlgorithmTDigest, got %v", calc.Algorithm())
	}

	// Test memory usage is reasonable
	memUsage := calc.MemoryUsage()
	if memUsage <= 0 || memUsage > 10000 { // Should be small but positive
		t.Errorf("Unexpected memory usage: %d bytes", memUsage)
	}
}

func TestP2PercentileCalculator(t *testing.T) {
	calc := metrics.NewP2PercentileCalculator(0.5) // Median

	// Test empty dataset
	_, err := calc.Quantile(0.5)
	if err != metrics.ErrEmptyDataset {
		t.Errorf("Expected ErrEmptyDataset for empty dataset, got %v", err)
	}

	// Test single value
	calc.Add(42.0)
	if result, err := calc.Quantile(0.5); err != nil || result != 42.0 {
		t.Errorf("Expected 42.0 for single value median, got %v, err %v", result, err)
	}

	// Test with small dataset (< 5 values)
	calc.Reset()
	values := []float64{1, 3, 2}
	for _, v := range values {
		calc.Add(v)
	}

	if result, err := calc.Quantile(0.5); err != nil || result != 2.0 {
		t.Errorf("Expected 2.0 for small dataset median, got %v, err %v", result, err)
	}

	// Test with larger dataset
	calc.Reset()
	for i := 1; i <= 1000; i++ {
		calc.Add(float64(i))
	}

	// Test median should be around 500.5
	if result, err := calc.Quantile(0.5); err != nil {
		t.Errorf("Error calculating median: %v", err)
	} else if math.Abs(result-500.5) > 50.0 { // P² has more approximation error
		t.Errorf("Expected median around 500.5, got %v", result)
	}

	// Test that P² only calculates its target quantile
	if _, err := calc.Quantile(0.95); err == nil {
		t.Errorf("Expected error when querying non-target quantile")
	}

	// Test algorithm type
	if calc.Algorithm() != metrics.AlgorithmP2 {
		t.Errorf("Expected AlgorithmP2, got %v", calc.Algorithm())
	}

	// Test memory usage is very small
	memUsage := calc.MemoryUsage()
	if memUsage <= 0 || memUsage > 500 { // Should be very small
		t.Errorf("Unexpected memory usage for P²: %d bytes", memUsage)
	}
}

func TestPercentileCalculatorFactory(t *testing.T) {
	// Test exact calculator creation
	calc, err := metrics.NewPercentileCalculator(metrics.AlgorithmExact)
	if err != nil {
		t.Errorf("Error creating exact calculator: %v", err)
	}
	if calc.Algorithm() != metrics.AlgorithmExact {
		t.Errorf("Expected AlgorithmExact, got %v", calc.Algorithm())
	}

	// Test T-Digest calculator creation with default compression
	calc, err = metrics.NewPercentileCalculator(metrics.AlgorithmTDigest)
	if err != nil {
		t.Errorf("Error creating T-Digest calculator: %v", err)
	}
	if calc.Algorithm() != metrics.AlgorithmTDigest {
		t.Errorf("Expected AlgorithmTDigest, got %v", calc.Algorithm())
	}

	// Test T-Digest calculator creation with custom compression
	calc, err = metrics.NewPercentileCalculator(metrics.AlgorithmTDigest, 200.0)
	if err != nil {
		t.Errorf("Error creating T-Digest calculator with compression: %v", err)
	}

	// Test P² calculator creation with default quantile
	calc, err = metrics.NewPercentileCalculator(metrics.AlgorithmP2)
	if err != nil {
		t.Errorf("Error creating P² calculator: %v", err)
	}
	if calc.Algorithm() != metrics.AlgorithmP2 {
		t.Errorf("Expected AlgorithmP2, got %v", calc.Algorithm())
	}

	// Test P² calculator creation with custom quantile
	calc, err = metrics.NewPercentileCalculator(metrics.AlgorithmP2, 0.95)
	if err != nil {
		t.Errorf("Error creating P² calculator with quantile: %v", err)
	}

	// Test unsupported algorithm
	_, err = metrics.NewPercentileCalculator(metrics.AlgorithmQuantileTree)
	if err != metrics.ErrAlgorithmNotSupported {
		t.Errorf("Expected ErrAlgorithmNotSupported, got %v", err)
	}
}

func TestMultiQuantileCalculator(t *testing.T) {
	quantiles := []float64{0.25, 0.5, 0.75, 0.95}

	// Test with T-Digest algorithm
	calc, err := metrics.NewMultiQuantileCalculator(metrics.AlgorithmTDigest, quantiles)
	if err != nil {
		t.Errorf("Error creating multi-quantile calculator: %v", err)
	}

	// Add test data
	for i := 1; i <= 100; i++ {
		calc.Add(float64(i))
	}

	// Test individual quantile access
	if result, err := calc.Quantile(0.5); err != nil {
		t.Errorf("Error calculating median: %v", err)
	} else if math.Abs(result-50.5) > 5.0 {
		t.Errorf("Expected median around 50.5, got %v", result)
	}

	// Test all quantiles at once
	results, err := calc.AllQuantiles()
	if err != nil {
		t.Errorf("Error calculating all quantiles: %v", err)
	}

	if len(results) != len(quantiles) {
		t.Errorf("Expected %d results, got %d", len(quantiles), len(results))
	}

	for _, q := range quantiles {
		if _, exists := results[q]; !exists {
			t.Errorf("Missing result for quantile %v", q)
		}
	}

	// Test accessing non-configured quantile
	if _, err := calc.Quantile(0.99); err == nil {
		t.Errorf("Expected error for non-configured quantile")
	}

	// Test memory usage
	totalMem := calc.TotalMemoryUsage()
	if totalMem <= 0 {
		t.Errorf("Expected positive memory usage, got %d", totalMem)
	}

	// Test with invalid quantiles
	invalidQuantiles := []float64{-0.1, 1.5}
	_, err = metrics.NewMultiQuantileCalculator(metrics.AlgorithmTDigest, invalidQuantiles)
	if err != metrics.ErrInvalidQuantile {
		t.Errorf("Expected ErrInvalidQuantile for invalid quantiles, got %v", err)
	}
}

func TestPercentileAlgorithmComparison(t *testing.T) {
	// Generate test data
	data := make([]float64, 1000)
	for i := 0; i < 1000; i++ {
		data[i] = float64(i + 1)
	}

	// Create calculators
	exact := metrics.NewExactPercentileCalculator()
	tdigest := metrics.NewTDigestPercentileCalculator(100)

	// Add data to all calculators
	for _, value := range data {
		exact.Add(value)
		tdigest.Add(value)
	}

	// Compare results for median
	exactMedian, _ := exact.Quantile(0.5)
	tdigestMedian, _ := tdigest.Quantile(0.5)

	// T-Digest should be reasonably close to exact
	if math.Abs(exactMedian-tdigestMedian) > 10.0 {
		t.Errorf("T-Digest median too far from exact: exact=%v, tdigest=%v", exactMedian, tdigestMedian)
	}

	// Compare memory usage
	exactMem := exact.MemoryUsage()
	tdigestMem := tdigest.MemoryUsage()

	t.Logf("Memory usage comparison - Exact: %d bytes, T-Digest: %d bytes", exactMem, tdigestMem)
}

func TestPercentileEdgeCases(t *testing.T) {
	// Test with very small values
	calc := metrics.NewExactPercentileCalculator()
	calc.Add(1e-100)
	calc.Add(2e-100)
	calc.Add(3e-100)

	if result, err := calc.Quantile(0.5); err != nil || result != 2e-100 {
		t.Errorf("Failed with very small values: result=%v, err=%v", result, err)
	}

	// Test with very large values
	calc.Reset()
	calc.Add(1e100)
	calc.Add(2e100)
	calc.Add(3e100)

	if result, err := calc.Quantile(0.5); err != nil || result != 2e100 {
		t.Errorf("Failed with very large values: result=%v, err=%v", result, err)
	}

	// Test with negative values
	calc.Reset()
	calc.Add(-100)
	calc.Add(-50)
	calc.Add(0)
	calc.Add(50)
	calc.Add(100)

	if result, err := calc.Quantile(0.5); err != nil || result != 0 {
		t.Errorf("Failed with negative values: result=%v, err=%v", result, err)
	}
}
