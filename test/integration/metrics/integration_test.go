package metrics

import (
	"testing"
	"time"

	"neuralmetergo/internal/metrics"
)

// TestMetricsIntegrator_Basic tests basic functionality of the metrics integrator
func TestMetricsIntegrator_Basic(t *testing.T) {
	// Use small-scale configuration for development testing
	config := metrics.IntegratorConfig{
		CollectionConfig: metrics.CollectionConfig{
			WorkerCount:     2,                      // Minimal workers
			CollectionRate:  100 * time.Millisecond, // Fast collection for testing
			ChannelBuffer:   10,                     // Small buffer
			BatchSize:       5,
			EnableBatching:  false, // Disable batching for simplicity
			MaxRetries:      3,
			RetryDelay:      10 * time.Millisecond,
			EnableMetrics:   true,
			ShutdownTimeout: 1 * time.Second,
		},
		AggregationConfig: metrics.AggregationConfig{
			Interval:    200 * time.Millisecond, // Fast aggregation for testing
			Type:        metrics.AggregationSlidingWindow,
			Percentile:  0.95,
			BufferSize:  20, // Small buffer for testing
			EnableAsync: true,
			WindowConfig: metrics.SlidingWindowConfig{
				Type:        metrics.WindowTypeCount,
				Capacity:    10, // Small window for testing
				EnableStats: true,
				EnableAsync: false, // Synchronous for predictable testing
			},
			BucketConfig: metrics.TimeBucketConfig{
				Granularity:        metrics.GranularitySecond, // Fine granularity for testing
				RetentionBuckets:   60,                        // 1 minute retention
				MaxClockSkew:       1 * time.Second,
				EnableDownsampling: false,
				EnableUpsampling:   false,
				CleanupInterval:    5 * time.Second,
				EnableAsync:        false, // Synchronous for testing
				EnableCompression:  false,
				MaxMemoryUsage:     1024 * 1024, // 1MB limit
				FlushOnShutdown:    true,
			},
			EnableAdvancedStats: true,
		},
		EnableEnhanced:   true,
		EnableCollection: true,
		EnableBasicAgg:   true,
	}

	integrator, err := metrics.NewMetricsIntegrator(config)
	if err != nil {
		t.Fatalf("Failed to create metrics integrator: %v", err)
	}

	// Test starting the integrator
	if err := integrator.Start(); err != nil {
		t.Fatalf("Failed to start metrics integrator: %v", err)
	}

	// Verify it's running
	if !integrator.IsRunning() {
		t.Error("Integrator should be running after Start()")
	}

	// Test stopping the integrator
	if err := integrator.Stop(); err != nil {
		t.Fatalf("Failed to stop metrics integrator: %v", err)
	}

	// Verify it's stopped
	if integrator.IsRunning() {
		t.Error("Integrator should not be running after Stop()")
	}
}

// TestMetricsIntegrator_MetricRegistration tests metric registration
func TestMetricsIntegrator_MetricRegistration(t *testing.T) {
	config := getTestConfig()
	integrator, err := metrics.NewMetricsIntegrator(config)
	if err != nil {
		t.Fatalf("Failed to create metrics integrator: %v", err)
	}

	if err := integrator.Start(); err != nil {
		t.Fatalf("Failed to start metrics integrator: %v", err)
	}
	defer integrator.Stop()

	// Create test metrics
	counter := metrics.NewCounter()
	gauge := metrics.NewGauge()
	histogram := metrics.NewHistogram()

	tags := map[string]string{"test": "true", "env": "development"}

	// Register metrics
	integrator.RegisterCounter("test_counter", counter, tags)
	integrator.RegisterGauge("test_gauge", gauge, tags)
	integrator.RegisterHistogram("test_histogram", histogram, tags)

	// Add some test data
	counter.Add(5)
	gauge.Set(42.5)
	histogram.Observe(1.5)
	histogram.Observe(0.8)
	histogram.Observe(3.2)

	// Give some time for collection and aggregation
	time.Sleep(500 * time.Millisecond)

	// Test direct metric value addition to enhanced aggregator
	if err := integrator.AddMetricValue("direct_metric", 123.45, tags); err != nil {
		t.Errorf("Failed to add metric value: %v", err)
	}

	// Verify metrics are being tracked
	allMetrics := integrator.GetAllMetrics()
	if len(allMetrics) == 0 {
		t.Error("Expected some metrics to be tracked")
	}

	t.Logf("Tracked metrics: %v", allMetrics)
}

// TestMetricsIntegrator_SlidingWindow tests sliding window functionality
func TestMetricsIntegrator_SlidingWindow(t *testing.T) {
	config := getTestConfig()
	integrator, err := metrics.NewMetricsIntegrator(config)
	if err != nil {
		t.Fatalf("Failed to create metrics integrator: %v", err)
	}

	if err := integrator.Start(); err != nil {
		t.Fatalf("Failed to start metrics integrator: %v", err)
	}
	defer integrator.Stop()

	metricName := "sliding_window_test"
	tags := map[string]string{"test": "sliding_window"}

	// Add test values to sliding window
	testValues := []float64{1.0, 2.0, 3.0, 4.0, 5.0}
	for _, value := range testValues {
		if err := integrator.AddMetricValue(metricName, value, tags); err != nil {
			t.Errorf("Failed to add metric value %f: %v", value, err)
		}
		time.Sleep(10 * time.Millisecond) // Small delay between values
	}

	// Give time for processing
	time.Sleep(100 * time.Millisecond)

	// Get sliding window stats
	stats, err := integrator.GetSlidingWindowStats(metricName)
	if err != nil {
		t.Errorf("Failed to get sliding window stats: %v", err)
	} else if stats != nil {
		t.Logf("Sliding window stats: Count=%d, Mean=%.2f, Min=%.2f, Max=%.2f",
			stats.Count, stats.Mean, stats.Min, stats.Max)

		if stats.Count == 0 {
			t.Error("Expected sliding window to have some data")
		}
	}
}

// TestMetricsIntegrator_StatisticalSummary tests statistical summary functionality
func TestMetricsIntegrator_StatisticalSummary(t *testing.T) {
	config := getTestConfig()
	integrator, err := metrics.NewMetricsIntegrator(config)
	if err != nil {
		t.Fatalf("Failed to create metrics integrator: %v", err)
	}

	if err := integrator.Start(); err != nil {
		t.Fatalf("Failed to start metrics integrator: %v", err)
	}
	defer integrator.Stop()

	metricName := "stats_summary_test"
	tags := map[string]string{"test": "stats_summary"}

	// Add test values
	testValues := []float64{10.0, 20.0, 30.0, 40.0, 50.0}
	for _, value := range testValues {
		if err := integrator.AddMetricValue(metricName, value, tags); err != nil {
			t.Errorf("Failed to add metric value %f: %v", value, err)
		}
	}

	// Give time for processing
	time.Sleep(100 * time.Millisecond)

	// Get statistical summary
	summary, err := integrator.GetStatisticalSummary(metricName)
	if err != nil {
		t.Errorf("Failed to get statistical summary: %v", err)
	} else if summary != nil {
		t.Logf("Statistical summary: Count=%d, Mean=%.2f, Min=%.2f, Max=%.2f, Variance=%.2f",
			summary.Count, summary.Mean, summary.Min, summary.Max, summary.Variance)

		if summary.Count == 0 {
			t.Error("Expected statistical summary to have some data")
		}

		expectedMean := 30.0 // (10+20+30+40+50)/5
		if summary.Mean != expectedMean {
			t.Errorf("Expected mean %.2f, got %.2f", expectedMean, summary.Mean)
		}
	}
}

// getTestConfig returns a configuration suitable for development testing
func getTestConfig() metrics.IntegratorConfig {
	return metrics.IntegratorConfig{
		CollectionConfig: metrics.CollectionConfig{
			WorkerCount:     1,
			CollectionRate:  50 * time.Millisecond,
			ChannelBuffer:   5,
			BatchSize:       3,
			EnableBatching:  false,
			MaxRetries:      2,
			RetryDelay:      5 * time.Millisecond,
			EnableMetrics:   true,
			ShutdownTimeout: 500 * time.Millisecond,
		},
		AggregationConfig: metrics.AggregationConfig{
			Interval:    100 * time.Millisecond,
			Type:        metrics.AggregationSlidingWindow,
			Percentile:  0.95,
			BufferSize:  10,
			EnableAsync: false, // Synchronous for predictable testing
			WindowConfig: metrics.SlidingWindowConfig{
				Type:        metrics.WindowTypeCount,
				Capacity:    5, // Very small for testing
				EnableStats: true,
				EnableAsync: false,
			},
			BucketConfig: metrics.TimeBucketConfig{
				Granularity:        metrics.GranularitySecond,
				RetentionBuckets:   10,
				MaxClockSkew:       500 * time.Millisecond,
				EnableDownsampling: false,
				EnableUpsampling:   false,
				CleanupInterval:    2 * time.Second,
				EnableAsync:        false,
				EnableCompression:  false,
				MaxMemoryUsage:     512 * 1024, // 512KB
				FlushOnShutdown:    true,
			},
			EnableAdvancedStats: true,
		},
		EnableEnhanced:   true,
		EnableCollection: false, // Disable collection for pure aggregation testing
		EnableBasicAgg:   false, // Focus on enhanced aggregation
	}
}
