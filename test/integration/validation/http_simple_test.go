package validation

import (
	"testing"
	"time"

	"neuralmetergo/internal/client"
	"neuralmetergo/internal/engine"
	"neuralmetergo/internal/parser"
	"neuralmetergo/internal/validation"
)

func TestHTTPValidator_Basic(t *testing.T) {
	// Test valid HTTP configuration
	validPlan := &parser.TestPlan{
		Version:     "1.0",
		Name:        "Test Plan",
		Duration:    parser.Duration{Duration: time.Minute},
		Concurrency: 1,
		Global: parser.Global{
			BaseURL: "https://api.example.com",
		},
		Scenarios: []parser.Scenario{
			{
				Name:   "Valid Test",
				Weight: 100,
				Requests: []parser.Request{
					{
						Name:   "GET Request",
						Method: "GET",
						URL:    "/users",
						Headers: map[string]string{
							"Authorization": "Bearer token",
							"Content-Type":  "application/json",
						},
						Assertions: []parser.Assertion{
							{Type: "status_code", Value: 200},
							{Type: "response_time", Operator: "lt", Value: 1000},
						},
						Extract: []parser.Extract{
							{Name: "user_id", Type: "json_path", Path: "$.id"},
						},
					},
				},
			},
		},
	}

	engine := validation.NewValidationEngine()
	result := engine.ValidateTestPlan(validPlan)

	if !result.IsValid() {
		t.Errorf("Valid plan should pass validation")
		for _, issue := range result.Issues {
			if issue.Severity == validation.SeverityError {
				t.Logf("Error: %s - %s", issue.Code, issue.Message)
			}
		}
	}

	// Test invalid HTTP method
	invalidPlan := &parser.TestPlan{
		Version:     "1.0",
		Name:        "Invalid Test Plan",
		Duration:    parser.Duration{Duration: time.Minute},
		Concurrency: 1,
		Scenarios: []parser.Scenario{
			{
				Name:   "Invalid Test",
				Weight: 100,
				Requests: []parser.Request{
					{
						Name:   "Invalid Method",
						Method: "INVALID",
						URL:    "/test",
					},
				},
			},
		},
	}

	result = engine.ValidateTestPlan(invalidPlan)
	if result.IsValid() {
		t.Errorf("Invalid plan should fail validation")
	}

	// Verify specific error
	found := false
	for _, issue := range result.Issues {
		if issue.Code == "INVALID_HTTP_METHOD" {
			found = true
			break
		}
	}
	if !found {
		t.Errorf("Expected INVALID_HTTP_METHOD error not found")
	}
}

func TestHTTPValidator_URLValidation(t *testing.T) {
	// Test URL validation without base URL
	plan := &parser.TestPlan{
		Version:     "1.0",
		Name:        "URL Test",
		Duration:    parser.Duration{Duration: time.Minute},
		Concurrency: 1,
		Scenarios: []parser.Scenario{
			{
				Name:   "URL Test",
				Weight: 100,
				Requests: []parser.Request{
					{
						Name:   "Relative URL without base",
						Method: "GET",
						URL:    "/users", // Relative URL without base URL should fail
					},
				},
			},
		},
	}

	engine := validation.NewValidationEngine()
	result := engine.ValidateTestPlan(plan)

	// Should find URL validation error
	found := false
	for _, issue := range result.Issues {
		if issue.Code == "INVALID_URL" {
			found = true
			break
		}
	}
	if !found {
		t.Errorf("Expected INVALID_URL error for relative URL without base URL")
		for _, issue := range result.Issues {
			t.Logf("Issue: %s - %s", issue.Code, issue.Message)
		}
	}
}

func TestHTTPValidator_AssertionValidation(t *testing.T) {
	plan := &parser.TestPlan{
		Version:     "1.0",
		Name:        "Assertion Test",
		Duration:    parser.Duration{Duration: time.Minute},
		Concurrency: 1,
		Global: parser.Global{
			BaseURL: "https://api.example.com",
		},
		Scenarios: []parser.Scenario{
			{
				Name:   "Assertion Test",
				Weight: 100,
				Requests: []parser.Request{
					{
						Name:   "Invalid Assertions",
						Method: "GET",
						URL:    "/test",
						Assertions: []parser.Assertion{
							{Type: "invalid_type", Value: 200},
							{Type: "status_code"}, // Missing value
						},
					},
				},
			},
		},
	}

	engine := validation.NewValidationEngine()
	result := engine.ValidateTestPlan(plan)

	// Should find assertion validation errors
	var errorCodes []string
	for _, issue := range result.Issues {
		if issue.Severity == validation.SeverityError {
			errorCodes = append(errorCodes, issue.Code)
		}
	}

	expectedCodes := []string{"INVALID_ASSERTION_TYPE", "MISSING_ASSERTION_VALUE"}
	for _, expected := range expectedCodes {
		found := false
		for _, code := range errorCodes {
			if code == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected error code %s not found. Found: %v", expected, errorCodes)
		}
	}
}

func TestHTTPValidator_ExtractionValidation(t *testing.T) {
	plan := &parser.TestPlan{
		Version:     "1.0",
		Name:        "Extraction Test",
		Duration:    parser.Duration{Duration: time.Minute},
		Concurrency: 1,
		Global: parser.Global{
			BaseURL: "https://api.example.com",
		},
		Scenarios: []parser.Scenario{
			{
				Name:   "Extraction Test",
				Weight: 100,
				Requests: []parser.Request{
					{
						Name:   "Invalid Extractions",
						Method: "GET",
						URL:    "/test",
						Extract: []parser.Extract{
							{Name: "bad_type", Type: "invalid_type", Path: "$.data"},
							{Name: "no_path", Type: "json_path", Path: ""},
							{Name: "bad_regex", Type: "regex", Path: "[invalid regex"},
						},
					},
				},
			},
		},
	}

	engine := validation.NewValidationEngine()
	result := engine.ValidateTestPlan(plan)

	// Should find extraction validation errors
	var errorCodes []string
	for _, issue := range result.Issues {
		if issue.Severity == validation.SeverityError {
			errorCodes = append(errorCodes, issue.Code)
		}
	}

	expectedCodes := []string{"INVALID_EXTRACT_TYPE", "MISSING_EXTRACT_PATH", "INVALID_REGEX"}
	for _, expected := range expectedCodes {
		found := false
		for _, code := range errorCodes {
			if code == expected {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected error code %s not found. Found: %v", expected, errorCodes)
		}
	}
}

func TestHTTPValidator_XPathExtractionIntegration(t *testing.T) {
	plan := &parser.TestPlan{
		Version:     "1.0",
		Name:        "XPath Extraction Integration Test",
		Duration:    parser.Duration{Duration: time.Minute},
		Concurrency: 1,
		Global: parser.Global{
			BaseURL: "https://api.example.com",
		},
		Scenarios: []parser.Scenario{
			{
				Name:   "XPath Extraction",
				Weight: 100,
				Requests: []parser.Request{
					{
						Name:   "Extract Element and Attribute",
						Method: "GET",
						URL:    "/test",
						Extract: []parser.Extract{
							{Name: "item_value", Type: "xpath", Path: "/root/item"},
							{Name: "item_id", Type: "xpath", Path: "/root/item/@id"},
						},
					},
				},
			},
		},
	}

	// Simulate HTTP response
	xmlBody := []byte(`<root><item id="abc">hello</item></root>`)
	response := &client.Response{
		StatusCode: 200,
		Body:       xmlBody,
		Headers:    map[string]string{"Content-Type": "application/xml"},
	}

	coordinator := &engine.RequestCoordinator{}
	request := plan.Scenarios[0].Requests[0]
	result := &engine.RequestResult{Extracted: make(map[string]string)}

	for _, extract := range request.Extract {
		value, err := coordinator.ExtractValue(extract, response)
		if err != nil {
			t.Errorf("Extraction error for %s: %v", extract.Name, err)
		}
		result.Extracted[extract.Name] = value
	}

	if result.Extracted["item_value"] != "hello" {
		t.Errorf("Expected 'hello' for item_value, got: %v", result.Extracted["item_value"])
	}
	if result.Extracted["item_id"] != "abc" {
		t.Errorf("Expected 'abc' for item_id, got: %v", result.Extracted["item_id"])
	}
}

func TestHTTPValidator_XPathExtraction_MultipleValues(t *testing.T) {
	plan := &parser.TestPlan{
		Version:     "1.0",
		Name:        "XPath Multiple Extraction Test",
		Duration:    parser.Duration{Duration: time.Minute},
		Concurrency: 1,
		Global: parser.Global{
			BaseURL: "https://api.example.com",
		},
		Scenarios: []parser.Scenario{
			{
				Name:   "XPath Extraction",
				Weight: 100,
				Requests: []parser.Request{
					{
						Name:   "Extract Multiple Elements and Attributes",
						Method: "GET",
						URL:    "/test",
						Extract: []parser.Extract{
							{Name: "item_value", Type: "xpath", Path: "/root/item"},
							{Name: "item_id", Type: "xpath", Path: "/root/item/@id"},
						},
					},
				},
			},
		},
	}

	// Simulate HTTP response with multiple items
	xmlBody := []byte(`<root><item id="a">foo</item><item id="b">bar</item><item id="c">baz</item></root>`)
	response := &client.Response{
		StatusCode: 200,
		Body:       xmlBody,
		Headers:    map[string]string{"Content-Type": "application/xml"},
	}

	coordinator := &engine.RequestCoordinator{}
	request := plan.Scenarios[0].Requests[0]
	result := &engine.RequestResult{Extracted: make(map[string]string)}

	for _, extract := range request.Extract {
		value, err := coordinator.ExtractValue(extract, response)
		if err != nil {
			t.Errorf("Extraction error for %s: %v", extract.Name, err)
		}
		result.Extracted[extract.Name] = value
	}

	expectedValues := "foo,bar,baz"
	expectedIDs := "a,b,c"

	if result.Extracted["item_value"] != expectedValues {
		t.Errorf("Expected '%s' for item_value, got: %v", expectedValues, result.Extracted["item_value"])
	}
	if result.Extracted["item_id"] != expectedIDs {
		t.Errorf("Expected '%s' for item_id, got: %v", expectedIDs, result.Extracted["item_id"])
	}
}
