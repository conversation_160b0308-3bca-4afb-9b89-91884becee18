// jenkins-test-profiles.groovy
// Add this to your Jenkinsfile parameters section for easy test profile selection

parameters {
    choice(
        name: 'TEST_PROFILE', 
        choices: [
            'quick',
            'standard', 
            'comprehensive',
            'nightly',
            'custom'
        ], 
        description: '''Test Profile Selection:
• quick: connectivity only (2-3 min)
• standard: connectivity + load-basic (7-13 min)  
• comprehensive: connectivity + load-basic + performance (17-28 min)
• nightly: all test suites (60+ min)
• custom: specify individual suites below'''
    )
    
    string(
        name: 'CUSTOM_TEST_SUITES', 
        defaultValue: 'connectivity,load-basic', 
        description: 'Custom test suites (comma-separated). Only used when TEST_PROFILE=custom. Available: connectivity,load-basic,load-advanced,performance,stress,security,integration,regression'
    )
    
    booleanParam(
        name: 'CONTINUE_ON_TEST_FAILURE', 
        defaultValue: false, 
        description: 'Continue running other test suites if one fails'
    )
    
    booleanParam(
        name: 'SKIP_CLI_TESTS', 
        defaultValue: false, 
        description: 'Skip all CLI testing stages'
    )
}

// Usage in pipeline stage:
/*
stage('Run CLI Tests') {
    when {
        not { params.SKIP_CLI_TESTS }
    }
    steps {
        script {
            echo "🧪 Running CLI test profile: ${params.TEST_PROFILE}"
            
            sshagent([JENKINS_CREDENTIALS_ID]) {
                // Set environment variables
                env.CLI_BINARY_PATH = "${DEPLOY_PATH}/${CLI_BINARY_NAME}"
                env.SSH_USER = 'neuro'
                env.TEST_RESULTS_DIR = './test-results'
                
                // Determine test suites based on profile
                def testSuites = ""
                switch(params.TEST_PROFILE) {
                    case 'quick':
                        testSuites = "connectivity"
                        break
                    case 'standard':
                        testSuites = "connectivity load-basic"
                        break
                    case 'comprehensive':
                        testSuites = "connectivity load-basic performance"
                        break
                    case 'nightly':
                        testSuites = "--all"
                        break
                    case 'custom':
                        testSuites = params.CUSTOM_TEST_SUITES.replace(',', ' ')
                        break
                    default:
                        testSuites = "connectivity"
                }
                
                // Build orchestrator command
                def orchestratorCmd = "./test/test-orchestrator.sh"
                if (params.CONTINUE_ON_TEST_FAILURE) {
                    orchestratorCmd += " --continue-on-failure"
                }
                orchestratorCmd += " ${testSuites}"
                
                // Run test orchestrator
                sh orchestratorCmd
            }
            
            echo "✅ CLI tests completed"
        }
    }
    post {
        always {
            archiveArtifacts artifacts: 'test-results/**/*', allowEmptyArchive: true
        }
    }
}
*/