name: "High Concurrency Stress Test"
description: "Stress test with high concurrency and performance limits validation."
target:
  base_url: "http://{{.SystemB_IP}}"
  timeout: "120s"
  max_retries: 1

scenarios:
  - name: "Extreme Load"
    endpoint: "/api/heavy"
    method: "POST"
    expected_status: 200
    concurrency: 5000
    duration: "60s"
    ramp_up: "10s"
    ramp_down: "5s"
    headers:
      Content-Type: "application/json"
    body: '{"data": "large_payload_simulated"}'

validation:
  max_response_time: "10s"
  max_error_rate: 0.05
  min_throughput: 1000
  p99_latency: "5s"

output:
  format: "json"
  file: "test-results/load-test-stress.json"
  stream_results: ""