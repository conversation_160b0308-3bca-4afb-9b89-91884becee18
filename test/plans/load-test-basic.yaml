name: "Standard Load Test"
description: "Standard load test with moderate concurrency and performance baseline validation."
target:
  base_url: "http://{{.SystemB_IP}}"
  timeout: "60s"
  max_retries: 2

scenarios:
  - name: "Moderate Load"
    endpoint: "/api/data"
    method: "GET"
    expected_status: 200
    concurrency: 10
    duration: "30s"
    headers:
      Content-Type: "application/json"
    body: ""

validation:
  max_response_time: "500ms"
  max_error_rate: 0.005
  min_throughput: 50
  p95_latency: "300ms"

output:
  format: "json"
  file: "test-results/load-test-basic.json"
  stream_results: ""