name: "Basic Connectivity Test"
description: "Test basic connectivity to System B endpoints"
target:
  base_url: "http://{{.SystemB_IP}}"
  timeout: "30s"
  max_retries: 3

scenarios:
  - name: "Health Check"
    endpoint: "/health"
    method: "GET"
    expected_status: 200
    concurrency: 1
    duration: "10s"
    
  - name: "API Endpoint"
    endpoint: "/api/"
    method: "GET"
    expected_status: 200
    concurrency: 5
    duration: "30s"

validation:
  max_response_time: "2s"
  max_error_rate: 0.01
  min_throughput: 10

output:
  format: "json"
  file: "test-results/connectivity-test.json"
  stream_results: "ws://{{.SystemB_IP}}:8080/stream"