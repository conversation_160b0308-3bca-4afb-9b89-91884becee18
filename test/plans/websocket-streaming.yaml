name: "WebSocket Streaming Test"
description: "WebSocket streaming test with real-time results and connection validation."
target:
  base_url: "ws://{{.SystemB_IP}}:8080/ws"
  timeout: "30s"
  max_retries: 3

scenarios:
  - name: "WebSocket Connection"
    endpoint: "" # For WebSocket, endpoint might be empty if base_url is the full path
    method: "GET" # Or "WS" if a custom method is supported
    expected_status: 101 # WebSocket switching protocols
    concurrency: 5
    duration: "20s"
    websocket:
      message_interval: "1s"
      message_count: 10
      message_payload: "Hello NeuralMeter!"
      expected_response_contains: "ACK"

validation:
  max_connection_errors: 0
  min_messages_received: 50
  max_latency: "1s"

output:
  format: "json"
  file: "test-results/websocket-streaming.json"
  stream_results: "ws://{{.SystemB_IP}}:8080/stream"