name: "Advanced HTTP Features Test"
description: "Test advanced HTTP features including HTTP/2, pipelining, compression, and connection management."
target:
  base_url: "http://{{.SystemB_IP}}"
  timeout: "60s"
  max_retries: 2

scenarios:
  # HTTP/2 Test
  - name: "HTTP/2 Test"
    endpoint: "/api/data"
    method: "GET"
    expected_status: 200
    concurrency: 5
    duration: "10s"
    headers:
      Content-Type: "application/json"
    http_version: "2.0"

  # HTTP/1.1 Pipelining Test
  - name: "HTTP/1.1 Pipelining Test"
    endpoint: "/api/data"
    method: "GET"
    expected_status: 200
    concurrency: 5
    duration: "10s"
    headers:
      Content-Type: "application/json"
    http_version: "1.1"
    enable_pipelining: true

  # Compression Test
  - name: "Compression Test"
    endpoint: "/api/compressible"
    method: "GET"
    expected_status: 200
    concurrency: 5
    duration: "10s"
    headers:
      Content-Type: "application/json"
      Accept-Encoding: "gzip, deflate"
    enable_compression: true

  # Keep-Alive Connection Test
  - name: "Keep-Alive Connection Test"
    endpoint: "/api/data"
    method: "GET"
    expected_status: 200
    concurrency: 5
    duration: "10s"
    headers:
      Content-Type: "application/json"
    keep_alive: true

validation:
  max_response_time: "500ms"
  max_error_rate: 0.005
  min_throughput: 20
  p95_latency: "300ms"

output:
  format: "json"
  file: "test-results/advanced-http-features.json"
  stream_results: ""