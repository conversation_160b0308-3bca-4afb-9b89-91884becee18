{"environments": {"development": {"system_a_host": "*************", "system_b_host": "*************", "cli_binary_path": "/home/<USER>/cli/neuralmeter", "ssh_user": "neuro"}, "staging": {"system_a_host": "*************", "system_b_host": "*************", "cli_binary_path": "/home/<USER>/cli/neuralmeter", "ssh_user": "neuro"}}, "test_profiles": {"quick": {"description": "Quick smoke tests for CI/CD", "suites": ["connectivity"], "timeout": 300, "continue_on_failure": false}, "standard": {"description": "Standard test suite for regular builds", "suites": ["connectivity", "load-basic"], "timeout": 900, "continue_on_failure": false}, "comprehensive": {"description": "Full test suite for releases", "suites": ["connectivity", "load-basic", "performance"], "timeout": 1800, "continue_on_failure": true}, "nightly": {"description": "Nightly regression tests", "suites": ["connectivity", "load-basic", "load-advanced", "performance", "stress"], "timeout": 3600, "continue_on_failure": true}}, "test_suites": {"connectivity": {"description": "Basic connectivity and CLI functionality", "estimated_duration": "2-3 minutes", "dependencies": [], "critical": true}, "load-basic": {"description": "Basic load testing scenarios", "estimated_duration": "5-10 minutes", "dependencies": ["connectivity"], "critical": true}, "load-advanced": {"description": "Advanced load testing with GPU acceleration", "estimated_duration": "15-20 minutes", "dependencies": ["connectivity", "load-basic"], "critical": false}, "performance": {"description": "Performance benchmarking and metrics", "estimated_duration": "10-15 minutes", "dependencies": ["connectivity"], "critical": false}, "stress": {"description": "Stress testing and resource limits", "estimated_duration": "20-30 minutes", "dependencies": ["connectivity", "load-basic", "performance"], "critical": false}, "security": {"description": "Security and authentication tests", "estimated_duration": "10-15 minutes", "dependencies": ["connectivity"], "critical": false}, "integration": {"description": "End-to-end integration tests", "estimated_duration": "15-25 minutes", "dependencies": ["connectivity", "load-basic"], "critical": false}, "regression": {"description": "Regression test suite", "estimated_duration": "30-45 minutes", "dependencies": ["connectivity", "load-basic", "performance"], "critical": false}}}