#!/usr/bin/env bash
# test-orchestrator.sh - Modular test orchestration system
# This script manages and executes different test suites in a scalable way

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_SUITES_DIR="$SCRIPT_DIR/test-suites"
TEST_RESULTS_DIR="${TEST_RESULTS_DIR:-./test-results}"
LOG_FILE="$TEST_RESULTS_DIR/test-orchestrator.log"

# Default configuration
SYSTEM_A_HOST="${SYSTEM_A_HOST:-*************}"
SYSTEM_B_HOST="${SYSTEM_B_HOST:-*************}"
CLI_BINARY_PATH="${CLI_BINARY_PATH:-/home/<USER>/cli/neuralmeter}"
SSH_USER="${SSH_USER:-neuro}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Test tracking
TOTAL_SUITES=0
PASSED_SUITES=0
FAILED_SUITES=0

# Available test suites registry
declare -A TEST_SUITES=(
    ["test-framework"]="Test framework validation and setup verification"
    ["connectivity"]="Basic connectivity and CLI functionality tests"
    ["load-basic"]="Basic load testing scenarios"
    ["load-advanced"]="Advanced load testing with GPU acceleration"
    ["performance"]="Performance benchmarking and metrics collection"
    ["stress"]="Stress testing and resource limits"
    ["security"]="Security and authentication tests"
    ["integration"]="End-to-end integration tests"
    ["regression"]="Regression test suite"
)

# Test suite dependencies
declare -A TEST_DEPENDENCIES=(
    ["load-basic"]="connectivity"
    ["load-advanced"]="connectivity,load-basic"
    ["performance"]="connectivity,load-basic"
    ["stress"]="connectivity,load-basic,performance"
    ["integration"]="connectivity,load-basic"
    ["regression"]="connectivity,load-basic,performance"
)

# Usage information
usage() {
    echo "Usage: $0 [OPTIONS] <test-suite1> [test-suite2] ..."
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -l, --list              List available test suites"
    echo "  -a, --all               Run all available test suites"
    echo "  -c, --config FILE       Use custom configuration file"
    echo "  -v, --verbose           Enable verbose output"
    echo "  -d, --dry-run           Show what would be executed without running"
    echo "  --parallel              Run test suites in parallel (experimental)"
    echo "  --continue-on-failure   Continue running other suites if one fails"
    echo ""
    echo "Available Test Suites:"
    for suite in "${!TEST_SUITES[@]}"; do
        echo "  $suite - ${TEST_SUITES[$suite]}"
    done
    echo ""
    echo "Examples:"
    echo "  $0 connectivity                    # Run connectivity tests only"
    echo "  $0 connectivity load-basic         # Run connectivity and basic load tests"
    echo "  $0 --all                          # Run all test suites"
    echo "  $0 --list                         # List available test suites"
}

# List available test suites
list_test_suites() {
    log "Available Test Suites:"
    echo ""
    for suite in "${!TEST_SUITES[@]}"; do
        echo -e "${BLUE}$suite${NC} - ${TEST_SUITES[$suite]}"
        if [[ -n "${TEST_DEPENDENCIES[$suite]}" ]]; then
            echo "  Dependencies: ${TEST_DEPENDENCIES[$suite]}"
        fi
        echo ""
    done
}

# Check if test suite exists
test_suite_exists() {
    local suite="$1"
    [[ -n "${TEST_SUITES[$suite]}" ]]
}

# Get test suite script path
get_test_suite_script() {
    local suite="$1"
    echo "$TEST_SUITES_DIR/${suite}.sh"
}

# Check test suite dependencies
check_dependencies() {
    local suite="$1"
    local deps="${TEST_DEPENDENCIES[$suite]}"
    
    if [[ -z "$deps" ]]; then
        return 0
    fi
    
    IFS=',' read -ra DEP_ARRAY <<< "$deps"
    for dep in "${DEP_ARRAY[@]}"; do
        local dep_script=$(get_test_suite_script "$dep")
        if [[ ! -f "$dep_script" ]]; then
            error "Dependency '$dep' not found for test suite '$suite'"
            return 1
        fi
    done
    
    return 0
}

# Execute a single test suite
execute_test_suite() {
    local suite="$1"
    local script_path=$(get_test_suite_script "$suite")
    
    TOTAL_SUITES=$((TOTAL_SUITES + 1))
    
    log "Executing test suite: $suite"
    log "Description: ${TEST_SUITES[$suite]}"
    log "Script: $script_path"
    log "Current working directory: $(pwd)"
    log "Current user: $(whoami)"
    log "Environment variables:"
    log "  SYSTEM_A_HOST: $SYSTEM_A_HOST"
    log "  SYSTEM_B_HOST: $SYSTEM_B_HOST"
    log "  CLI_BINARY_PATH: $CLI_BINARY_PATH"
    log "  SSH_USER: $SSH_USER"
    log "  TEST_RESULTS_DIR: $TEST_RESULTS_DIR"
    
    # Check if script exists
    if [[ ! -f "$script_path" ]]; then
        error "Test suite script not found: $script_path"
        FAILED_SUITES=$((FAILED_SUITES + 1))
        return 1
    fi
    
    # Check dependencies
    if ! check_dependencies "$suite"; then
        error "Dependency check failed for test suite: $suite"
        FAILED_SUITES=$((FAILED_SUITES + 1))
        return 1
    fi
    
    # Make script executable
    chmod +x "$script_path"
    
    # Set environment variables for the test suite
    export SYSTEM_A_HOST
    export SYSTEM_B_HOST
    export CLI_BINARY_PATH
    export SSH_USER
    export TEST_RESULTS_DIR
    export TEST_SUITE_NAME="$suite"
    
    # Execute the test suite
    local start_time=$(date +%s)
    log "Starting test suite execution at $(date)"
    log "Command: $script_path"
    
    # Show full output from test suite
    echo "=== TEST SUITE OUTPUT START ==="
    if "$script_path"; then
        echo "=== TEST SUITE OUTPUT END ==="
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        success "Test suite '$suite' completed successfully in ${duration}s"
        PASSED_SUITES=$((PASSED_SUITES + 1))
        return 0
    else
        local exit_code=$?
        echo "=== TEST SUITE OUTPUT END ==="
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        error "Test suite '$suite' failed after ${duration}s (exit code: $exit_code)"
        FAILED_SUITES=$((FAILED_SUITES + 1))
        return 1
    fi
}

# Initialize test environment
initialize_test_environment() {
    log "Initializing test environment..."
    
    # Create test results directory
    mkdir -p "$TEST_RESULTS_DIR"
    
    # Clear previous log
    > "$LOG_FILE"
    
    # Create test suites directory if it doesn't exist
    mkdir -p "$TEST_SUITES_DIR"
    
    log "Test environment initialized"
    log "System A: $SYSTEM_A_HOST"
    log "System B: $SYSTEM_B_HOST"
    log "CLI Binary: $CLI_BINARY_PATH"
    log "Results Directory: $TEST_RESULTS_DIR"
}

# Generate test report
generate_test_report() {
    local report_file="$TEST_RESULTS_DIR/orchestrator-report.txt"
    
    cat > "$report_file" << EOF
NeuralMeter Test Orchestrator Report
===================================
Generated: $(date)
Total Test Suites: $TOTAL_SUITES
Passed: $PASSED_SUITES
Failed: $FAILED_SUITES
Success Rate: $(( TOTAL_SUITES > 0 ? PASSED_SUITES * 100 / TOTAL_SUITES : 0 ))%

Environment:
- System A: $SYSTEM_A_HOST
- System B: $SYSTEM_B_HOST
- CLI Binary: $CLI_BINARY_PATH
- SSH User: $SSH_USER

Test Results Directory: $TEST_RESULTS_DIR
Detailed Log: $LOG_FILE
EOF

    log "Test report generated: $report_file"
}

# Main execution function
main() {
    local test_suites=()
    local run_all=false
    local dry_run=false
    local continue_on_failure=false
    local verbose=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                usage
                exit 0
                ;;
            -l|--list)
                list_test_suites
                exit 0
                ;;
            -a|--all)
                run_all=true
                shift
                ;;
            -d|--dry-run)
                dry_run=true
                shift
                ;;
            --continue-on-failure)
                continue_on_failure=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            -*)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
            *)
                test_suites+=("$1")
                shift
                ;;
        esac
    done
    
    # Initialize environment
    initialize_test_environment
    
    # Determine which test suites to run
    if [[ "$run_all" == true ]]; then
        test_suites=($(printf '%s\n' "${!TEST_SUITES[@]}" | sort))
    elif [[ ${#test_suites[@]} -eq 0 ]]; then
        error "No test suites specified. Use --help for usage information."
        exit 1
    fi
    
    # Validate test suites
    for suite in "${test_suites[@]}"; do
        if ! test_suite_exists "$suite"; then
            error "Unknown test suite: $suite"
            log "Available test suites: ${!TEST_SUITES[*]}"
            exit 1
        fi
    done
    
    # Dry run mode
    if [[ "$dry_run" == true ]]; then
        log "DRY RUN MODE - Would execute the following test suites:"
        for suite in "${test_suites[@]}"; do
            echo "  - $suite: ${TEST_SUITES[$suite]}"
        done
        exit 0
    fi
    
    # Execute test suites
    log "Starting test execution..."
    log "Test suites to run: ${test_suites[*]}"
    
    for suite in "${test_suites[@]}"; do
        if ! execute_test_suite "$suite"; then
            if [[ "$continue_on_failure" != true ]]; then
                error "Test suite '$suite' failed. Stopping execution."
                break
            else
                warning "Test suite '$suite' failed. Continuing with next suite."
            fi
        fi
    done
    
    # Generate final report
    generate_test_report
    
    # Final summary
    log "Test execution completed"
    log "Results: $PASSED_SUITES/$TOTAL_SUITES test suites passed"
    
    if [[ $FAILED_SUITES -eq 0 ]]; then
        success "All test suites passed!"
        exit 0
    else
        error "$FAILED_SUITES test suites failed."
        exit 1
    fi
}

# Run main function
main "$@"