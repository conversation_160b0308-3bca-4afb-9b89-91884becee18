#!/usr/bin/env bash
# load-basic.sh - Basic load testing scenarios
# Part of the modular NeuralMeter test framework

set -e

# Test suite configuration
TEST_SUITE_NAME="${TEST_SUITE_NAME:-load-basic}"
TEST_RESULTS_DIR="${TEST_RESULTS_DIR:-./test-results}"
SUITE_RESULTS_DIR="$TEST_RESULTS_DIR/$TEST_SUITE_NAME"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$TEST_SUITE_NAME]${NC} $1"
}

success() {
    echo -e "${GREEN}[$TEST_SUITE_NAME]${NC} $1"
}

error() {
    echo -e "${RED}[$TEST_SUITE_NAME]${NC} $1"
}

# Initialize suite
mkdir -p "$SUITE_RESULTS_DIR"

log "Starting basic load test suite..."

# Create test plans locally (we're already on System A)
mkdir -p "$SUITE_RESULTS_DIR"

# Create basic HTTP load test plan
cat > "$SUITE_RESULTS_DIR/basic-load.yaml" << EOF
name: "Basic Load Test"
description: "Basic HTTP load test"
version: "1.0"

duration: "60s"
concurrency: 10
ramp_up: "10s"
ramp_down: "10s"

scenarios:
  - name: "basic_load"
    description: "Basic load against System B"
    weight: 100
    requests:
      - name: "homepage"
        method: "GET"
        url: "http://$SYSTEM_B_HOST/"
        timeout: "10s"
      - name: "health_check"
        method: "GET"
        url: "http://$SYSTEM_B_HOST/health"
        timeout: "5s"

global:
  timeout: "10s"

output:
  format: ["json", "text"]
  detailed: true
EOF

# Run the load test
cd "$SUITE_RESULTS_DIR"
"$CLI_BINARY_PATH" run basic-load.yaml --output-format json > basic-load-results.json || echo 'Load test completed with warnings'

# Generate summary
echo 'Basic load test completed' > test-summary.txt
date >> test-summary.txt

success "Basic load test suite completed"