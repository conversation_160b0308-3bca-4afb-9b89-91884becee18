#!/usr/bin/env bash
# test-framework.sh - Test the test framework itself
# Part of the modular NeuralMeter test framework

set -e

# Test suite configuration
TEST_SUITE_NAME="${TEST_SUITE_NAME:-test-framework}"
TEST_RESULTS_DIR="${TEST_RESULTS_DIR:-./test-results}"
SUITE_RESULTS_DIR="$TEST_RESULTS_DIR/$TEST_SUITE_NAME"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$TEST_SUITE_NAME]${NC} $1"
}

success() {
    echo -e "${GREEN}[$TEST_SUITE_NAME]${NC} $1"
}

error() {
    echo -e "${RED}[$TEST_SUITE_NAME]${NC} $1"
}

# Initialize suite
mkdir -p "$SUITE_RESULTS_DIR"

log "Starting test framework validation..."

# Test environment variables
log "Environment variables:"
log "  SYSTEM_A_HOST: $SYSTEM_A_HOST"
log "  SYSTEM_B_HOST: $SYSTEM_B_HOST"
log "  CLI_BINARY_PATH: $CLI_BINARY_PATH"
log "  SSH_USER: $SSH_USER"
log "  TEST_RESULTS_DIR: $TEST_RESULTS_DIR"

# Test basic system info
echo "System Information" > "$SUITE_RESULTS_DIR/system-info.txt"
echo "==================" >> "$SUITE_RESULTS_DIR/system-info.txt"
echo "Hostname: $(hostname)" >> "$SUITE_RESULTS_DIR/system-info.txt"
echo "Date: $(date)" >> "$SUITE_RESULTS_DIR/system-info.txt"
echo "User: $(whoami)" >> "$SUITE_RESULTS_DIR/system-info.txt"
echo "Working Directory: $(pwd)" >> "$SUITE_RESULTS_DIR/system-info.txt"
echo "PATH: $PATH" >> "$SUITE_RESULTS_DIR/system-info.txt"

# Test CLI binary exists
if [[ -f "$CLI_BINARY_PATH" ]]; then
    log "✅ CLI binary found at: $CLI_BINARY_PATH"
    echo "CLI Binary: EXISTS" >> "$SUITE_RESULTS_DIR/system-info.txt"
else
    error "❌ CLI binary not found at: $CLI_BINARY_PATH"
    echo "CLI Binary: NOT FOUND" >> "$SUITE_RESULTS_DIR/system-info.txt"
fi

# Test network connectivity to System B
if ping -c 1 -W 5 "$SYSTEM_B_HOST" >/dev/null 2>&1; then
    log "✅ System B ($SYSTEM_B_HOST) is reachable"
    echo "System B Connectivity: OK" >> "$SUITE_RESULTS_DIR/system-info.txt"
else
    error "❌ System B ($SYSTEM_B_HOST) is not reachable"
    echo "System B Connectivity: FAILED" >> "$SUITE_RESULTS_DIR/system-info.txt"
fi

# Test directory structure
log "Test directory structure:"
ls -la /home/<USER>/test/ >> "$SUITE_RESULTS_DIR/system-info.txt" 2>&1 || echo "Test directory not found" >> "$SUITE_RESULTS_DIR/system-info.txt"
ls -la /home/<USER>/cli-scripts/ >> "$SUITE_RESULTS_DIR/system-info.txt" 2>&1 || echo "CLI scripts directory not found" >> "$SUITE_RESULTS_DIR/system-info.txt"

success "Test framework validation completed"