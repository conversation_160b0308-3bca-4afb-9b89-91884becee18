#!/usr/bin/env bash
# performance.sh - Performance benchmarking and metrics collection
# Tests actual CLI load testing performance from System A to System B
# Part of the modular NeuralMeter test framework

set -e

# Test suite configuration
TEST_SUITE_NAME="${TEST_SUITE_NAME:-performance}"
TEST_RESULTS_DIR="${TEST_RESULTS_DIR:-./test-results}"
SUITE_RESULTS_DIR="$TEST_RESULTS_DIR/$TEST_SUITE_NAME"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$TEST_SUITE_NAME]${NC} $1"
}

success() {
    echo -e "${GREEN}[$TEST_SUITE_NAME]${NC} $1"
}

error() {
    echo -e "${RED}[$TEST_SUITE_NAME]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[$TEST_SUITE_NAME]${NC} $1"
}

# Initialize suite
mkdir -p "$SUITE_RESULTS_DIR"
cd "$SUITE_RESULTS_DIR"

log "Starting CLI performance benchmarking against System B..."
log "System A (CLI): $(hostname)"
log "System B (Target): $SYSTEM_B_HOST"
log "CLI Binary: $CLI_BINARY_PATH"

# Performance test configuration
PERFORMANCE_TESTS=(
    "low:5:30s:Low concurrency baseline"
    "medium:20:60s:Medium concurrency test"
    "high:50:90s:High concurrency stress test"
)

# Create performance test report header
cat > performance-report.txt << EOF
NeuralMeter CLI Performance Test Report
======================================
Test Date: $(date)
System A: $(hostname) ($(uname -a))
System B: $SYSTEM_B_HOST
CLI Binary: $CLI_BINARY_PATH

Test Configuration:
- Low Concurrency: 5 concurrent users for 30s
- Medium Concurrency: 20 concurrent users for 60s  
- High Concurrency: 50 concurrent users for 90s

GPU Information:
EOF

# Capture GPU info
nvidia-smi --query-gpu=name,memory.total,driver_version,cuda_version --format=csv,noheader >> performance-report.txt 2>/dev/null || echo "No NVIDIA GPU detected" >> performance-report.txt

echo "" >> performance-report.txt
echo "Performance Test Results:" >> performance-report.txt
echo "========================" >> performance-report.txt

# Function to monitor system resources during test
monitor_resources() {
    local test_name="$1"
    local duration="$2"
    local output_file="$3"
    
    log "Monitoring system resources for $test_name..."
    
    # Monitor CPU, memory, and GPU usage during test
    {
        echo "Resource monitoring for $test_name (every 5 seconds)"
        echo "Time,CPU%,Memory%,GPU%,GPU_Memory_Used,GPU_Power"
        
        local end_time=$(($(date +%s) + duration))
        while [[ $(date +%s) -lt $end_time ]]; do
            local timestamp=$(date '+%H:%M:%S')
            local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
            local mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
            
            # GPU metrics (if available)
            local gpu_util=$(nvidia-smi --query-gpu=utilization.gpu --format=csv,noheader,nounits 2>/dev/null || echo "N/A")
            local gpu_mem=$(nvidia-smi --query-gpu=memory.used --format=csv,noheader,nounits 2>/dev/null || echo "N/A")
            local gpu_power=$(nvidia-smi --query-gpu=power.draw --format=csv,noheader,nounits 2>/dev/null || echo "N/A")
            
            echo "$timestamp,$cpu_usage,$mem_usage,$gpu_util,$gpu_mem,$gpu_power"
            sleep 5
        done
    } > "$output_file" &
    
    local monitor_pid=$!
    echo $monitor_pid
}

# Function to extract performance metrics from CLI output
extract_metrics() {
    local result_file="$1"
    local test_name="$2"
    
    if [[ -f "$result_file" ]]; then
        # Try to extract metrics from JSON output (if available)
        local total_requests=$(grep -o '"total_requests":[0-9]*' "$result_file" 2>/dev/null | cut -d':' -f2 || echo "0")
        local successful_requests=$(grep -o '"successful_requests":[0-9]*' "$result_file" 2>/dev/null | cut -d':' -f2 || echo "0")
        local failed_requests=$(grep -o '"failed_requests":[0-9]*' "$result_file" 2>/dev/null | cut -d':' -f2 || echo "0")
        local avg_latency=$(grep -o '"average_latency":"[^"]*"' "$result_file" 2>/dev/null | cut -d'"' -f4 || echo "N/A")
        local throughput=$(grep -o '"throughput":[0-9.]*' "$result_file" 2>/dev/null | cut -d':' -f2 || echo "0")
        
        # If JSON parsing fails, try to extract from text output
        if [[ "$total_requests" == "0" ]]; then
            total_requests=$(grep -i "total.*request" "$result_file" | grep -o '[0-9]*' | head -1 || echo "N/A")
            successful_requests=$(grep -i "success" "$result_file" | grep -o '[0-9]*' | head -1 || echo "N/A")
            failed_requests=$(grep -i "fail" "$result_file" | grep -o '[0-9]*' | head -1 || echo "N/A")
            avg_latency=$(grep -i "latency\|response.*time" "$result_file" | grep -o '[0-9.]*ms\|[0-9.]*s' | head -1 || echo "N/A")
            throughput=$(grep -i "throughput\|req.*sec\|rps" "$result_file" | grep -o '[0-9.]*' | head -1 || echo "N/A")
        fi
        
        # Write metrics to report
        cat >> performance-report.txt << EOF

$test_name Results:
- Total Requests: $total_requests
- Successful Requests: $successful_requests  
- Failed Requests: $failed_requests
- Average Latency: $avg_latency
- Throughput: $throughput req/s
- Success Rate: $(( total_requests > 0 ? successful_requests * 100 / total_requests : 0 ))%
EOF
    else
        warning "No result file found for $test_name"
        echo "$test_name: FAILED - No results" >> performance-report.txt
    fi
}

# Run performance tests
for test_config in "${PERFORMANCE_TESTS[@]}"; do
    IFS=':' read -r test_name concurrency duration description <<< "$test_config"
    
    log "Running $description..."
    log "Configuration: $concurrency concurrent users for $duration"
    
    # Create test plan for this performance test
    cat > "perf-test-${test_name}.yaml" << EOF
name: "Performance Test - $description"
description: "CLI performance test from System A to System B"
version: "1.0"

duration: "$duration"
concurrency: $concurrency
ramp_up: "10s"
ramp_down: "5s"

scenarios:
  - name: "performance_test_${test_name}"
    description: "$description"
    weight: 100
    requests:
      - name: "homepage_perf"
        method: "GET"
        url: "http://$SYSTEM_B_HOST/"
        headers:
          User-Agent: "NeuralMeter-Performance-Test/1.0"
        timeout: "10s"
      - name: "health_perf"
        method: "GET"
        url: "http://$SYSTEM_B_HOST/health"
        timeout: "5s"
      - name: "api_perf"
        method: "GET"
        url: "http://$SYSTEM_B_HOST/api/"
        timeout: "10s"

global:
  timeout: "10s"

output:
  format: ["json", "text"]
  detailed: true
EOF
    
    # Start resource monitoring
    local duration_seconds
    case $duration in
        *s) duration_seconds=${duration%s} ;;
        *m) duration_seconds=$((${duration%m} * 60)) ;;
        *) duration_seconds=60 ;;
    esac
    
    local monitor_pid=$(monitor_resources "$test_name" $duration_seconds "resource-monitor-${test_name}.csv")
    
    # Run the performance test
    local start_time=$(date +%s)
    log "Starting $test_name performance test at $(date)"
    
    if "$CLI_BINARY_PATH" run "perf-test-${test_name}.yaml" --output-format json > "perf-results-${test_name}.json" 2>&1; then
        success "$test_name performance test completed"
    else
        error "$test_name performance test failed"
        # Still try to extract any partial results
    fi
    
    local end_time=$(date +%s)
    local actual_duration=$((end_time - start_time))
    
    # Stop resource monitoring
    kill $monitor_pid 2>/dev/null || true
    wait $monitor_pid 2>/dev/null || true
    
    # Extract and record metrics
    extract_metrics "perf-results-${test_name}.json" "$test_name"
    
    log "$test_name test completed in ${actual_duration}s"
    
    # Brief pause between tests
    sleep 5
done

# Generate performance summary
log "Generating performance summary..."

cat >> performance-report.txt << EOF

Performance Summary:
===================
Test completed at: $(date)
Total test duration: $(( $(date +%s) - $(date -d "5 minutes ago" +%s) )) seconds

Resource Usage Analysis:
- CPU usage patterns saved in resource-monitor-*.csv files
- GPU utilization data captured during tests
- Memory usage tracked throughout test execution

Files Generated:
- performance-report.txt: This comprehensive report
- perf-test-*.yaml: Test plan configurations
- perf-results-*.json: Raw test results
- resource-monitor-*.csv: System resource monitoring data

Recommendations:
- Review resource-monitor files for bottlenecks
- Compare throughput across different concurrency levels
- Check GPU utilization to verify acceleration is working
- Monitor System B response times and error rates
EOF

# Create a simple CSV summary for easy analysis
cat > performance-summary.csv << EOF
Test,Concurrency,Duration,Total_Requests,Success_Rate,Avg_Latency,Throughput
EOF

for test_config in "${PERFORMANCE_TESTS[@]}"; do
    IFS=':' read -r test_name concurrency duration description <<< "$test_config"
    
    if [[ -f "perf-results-${test_name}.json" ]]; then
        local total=$(grep -o '"total_requests":[0-9]*' "perf-results-${test_name}.json" 2>/dev/null | cut -d':' -f2 || echo "0")
        local success=$(grep -o '"successful_requests":[0-9]*' "perf-results-${test_name}.json" 2>/dev/null | cut -d':' -f2 || echo "0")
        local latency=$(grep -o '"average_latency":"[^"]*"' "perf-results-${test_name}.json" 2>/dev/null | cut -d'"' -f4 || echo "N/A")
        local throughput=$(grep -o '"throughput":[0-9.]*' "perf-results-${test_name}.json" 2>/dev/null | cut -d':' -f2 || echo "0")
        local success_rate=$(( total > 0 ? success * 100 / total : 0 ))
        
        echo "$test_name,$concurrency,$duration,$total,$success_rate%,$latency,$throughput" >> performance-summary.csv
    else
        echo "$test_name,$concurrency,$duration,FAILED,0%,N/A,0" >> performance-summary.csv
    fi
done

# Final system state
log "Capturing final system state..."
echo "" >> performance-report.txt
echo "Final System State:" >> performance-report.txt
echo "==================" >> performance-report.txt
free -h >> performance-report.txt
echo "" >> performance-report.txt
nvidia-smi >> performance-report.txt 2>/dev/null || echo "No NVIDIA GPU" >> performance-report.txt

success "Performance test suite completed successfully"
log "Results saved in: $SUITE_RESULTS_DIR"
log "Main report: performance-report.txt"
log "CSV summary: performance-summary.csv"