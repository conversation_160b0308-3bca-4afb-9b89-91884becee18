#!/usr/bin/env bash
# connectivity.sh - Basic connectivity and CLI functionality tests
# Part of the modular NeuralMeter test framework

set -e

# Test suite configuration
TEST_SUITE_NAME="${TEST_SUITE_NAME:-connectivity}"
TEST_RESULTS_DIR="${TEST_RESULTS_DIR:-./test-results}"
SUITE_RESULTS_DIR="$TEST_RESULTS_DIR/$TEST_SUITE_NAME"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$TEST_SUITE_NAME]${NC} $1"
}

success() {
    echo -e "${GREEN}[$TEST_SUITE_NAME]${NC} $1"
}

error() {
    echo -e "${RED}[$TEST_SUITE_NAME]${NC} $1"
}

# Initialize suite
mkdir -p "$SUITE_RESULTS_DIR"

log "Starting connectivity test suite..."
log "System A: $SYSTEM_A_HOST"
log "System B: $SYSTEM_B_HOST"
log "CLI Binary: $CLI_BINARY_PATH"

# Run the existing connectivity test script locally (we're already on System A)
export SYSTEM_B_HOST
export CLI_BINARY="$CLI_BINARY_PATH"
export TEST_RESULTS_DIR="$SUITE_RESULTS_DIR"

# Create local results directory
mkdir -p "$SUITE_RESULTS_DIR"

# Execute connectivity test locally
cd /home/<USER>

# Set test environment variables
export SYSTEM_B_HOST
export CLI_BINARY="$CLI_BINARY_PATH"
export TEST_RESULTS_DIR="$SUITE_RESULTS_DIR"

# Run the quick connectivity test
./cli-scripts/quick-connectivity-test.sh

success "Connectivity test suite completed"