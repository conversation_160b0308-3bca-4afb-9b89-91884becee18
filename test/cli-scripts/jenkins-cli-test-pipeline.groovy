pipeline {
    agent any
    
    parameters {
        string(name: 'SYSTEM_A_HOST', defaultValue: '*************', description: 'System A (CLI Execution Machine) IP address')
        string(name: 'SYSTEM_B_HOST', defaultValue: '*************', description: 'System B (Target Services) IP address')
        choice(name: 'TEST_SUITE', choices: ['full', 'connectivity', 'basic', 'load'], description: 'Test suite to run')
        booleanParam(name: 'CLEANUP_AFTER_TEST', defaultValue: true, description: 'Clean up test artifacts after completion')
        booleanParam(name: 'BUILD_FIRST', defaultValue: true, description: 'Build and deploy CLI before testing')
    }
    
    environment {
        CLI_BINARY = 'neuralmeter'
        CLI_BINARY_PATH = '/home/<USER>/cli/neuralmeter'
        TEST_RESULTS_DIR = 'test-results'
        SSH_USER = 'neuro'
        JENKINS_CREDENTIALS_ID = 'ssh-credentials'
        DEPLOY_PATH = '/home/<USER>/cli'
    }
    
    stages {
        stage('Preparation') {
            steps {
                script {
                    echo "Starting CLI testing pipeline"
                    echo "System A: ${params.SYSTEM_A_HOST}"
                    echo "System B: ${params.SYSTEM_B_HOST}"
                    echo "Test Suite: ${params.TEST_SUITE}"
                }
                
                // Clean workspace
                cleanWs()
                
                // Checkout code
                checkout scm
            }
        }
        
        stage('Build CLI Binary') {
            when {
                expression { params.BUILD_FIRST == true }
            }
            steps {
                script {
                    echo "🔨 Building NeuralMeter CLI for Linux with CUDA support..."
                    
                    // Build the CLI binary with CUDA 12.9 support (same as main pipeline)
                    sh """
                        cd cmd/neuralmeter
                        export CGO_CFLAGS="-I/usr/include -I/usr/local/cuda-12.9/include"
                        export CGO_LDFLAGS="-L/usr/local/cuda-12.9/targets/x86_64-linux/lib -Wl,-rpath,/usr/local/cuda-12.9/targets/x86_64-linux/lib"
                        go build -tags cuda -o ${CLI_BINARY} .
                        ls -la ${CLI_BINARY}
                    """
                    
                    echo "✅ NeuralMeter CLI built successfully"
                }
            }
        }
        
        stage('Deploy to System A') {
            when {
                expression { params.BUILD_FIRST == true }
            }
            steps {
                script {
                    echo "🚀 Deploying CLI binary to System A..."
                    
                    sshagent([JENKINS_CREDENTIALS_ID]) {
                        // Create deployment directory
                        sh """
                            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${params.SYSTEM_A_HOST} 'mkdir -p ${DEPLOY_PATH}'
                        """
                    
                        // Copy binary to System A
                        sh """
                            scp -o StrictHostKeyChecking=no cmd/neuralmeter/${CLI_BINARY} ${SSH_USER}@${params.SYSTEM_A_HOST}:${DEPLOY_PATH}/
                        """
                    
                        // Set executable permissions
                        sh """
                            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${params.SYSTEM_A_HOST} 'chmod +x ${DEPLOY_PATH}/${CLI_BINARY}'
                        """
                    
                        // Verify deployment
                        sh """
                            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${params.SYSTEM_A_HOST} '${DEPLOY_PATH}/${CLI_BINARY} --version'
                        """
                    }
                    
                    echo "✅ CLI deployed to System A successfully"
                }
            }
        }
        
        stage('Deploy Test Scripts') {
            steps {
                script {
                    echo "📋 Deploying test scripts to System A..."
                    
                    sshagent([JENKINS_CREDENTIALS_ID]) {
                        // Copy test scripts to System A
                        sh """
                            scp -o StrictHostKeyChecking=no -r test/cli-scripts/ ${SSH_USER}@${params.SYSTEM_A_HOST}:/home/<USER>/
                            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${params.SYSTEM_A_HOST} 'chmod +x /home/<USER>/cli-scripts/*.sh'
                        """
                        
                        // Create test results directory on System A
                        sh """
                            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${params.SYSTEM_A_HOST} 'mkdir -p /home/<USER>/test-results'
                        """
                    }
                    
                    echo "✅ Test scripts deployed successfully"
                }
            }
        }
        
        stage('Verify System B Services') {
            steps {
                script {
                    echo "🔍 Verifying System B target services are running..."
                    
                    sshagent([JENKINS_CREDENTIALS_ID]) {
                        // Test nginx service status
                        sh """
                            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${params.SYSTEM_B_HOST} '/bin/systemctl is-active nginx'
                        """
                        
                        // Test web endpoints from System B itself
                        sh """
                            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${params.SYSTEM_B_HOST} '/usr/bin/curl -s http://localhost/ | head -1'
                        """
                    
                        sh """
                            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${params.SYSTEM_B_HOST} '/usr/bin/curl -s http://localhost/api/ | head -1'
                        """
                    
                        sh """
                            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${params.SYSTEM_B_HOST} '/usr/bin/curl -s http://localhost/health | head -1'
                        """
                        
                        // Test connectivity from System A to System B
                        sh """
                            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${params.SYSTEM_A_HOST} 'curl --connect-timeout 10 --max-time 30 -s http://${params.SYSTEM_B_HOST}/ | head -1'
                        """
                    }
                    
                    echo "✅ System B services verified successfully"
                }
            }
        }
        
        stage('Run CLI Tests') {
            steps {
                script {
                    echo "Running CLI tests on System A against System B..."
                    
                    def testCommand = ""
                    
                    switch(params.TEST_SUITE) {
                        case 'connectivity':
                            testCommand = """
                                cd /home/<USER>
                                export SYSTEM_B_HOST=${params.SYSTEM_B_HOST} && 
                                export CLI_BINARY=${CLI_BINARY_PATH} && 
                                ./cli-scripts/quick-connectivity-test.sh
                            """
                            break
                            
                        case 'basic':
                            testCommand = """
                                cd /home/<USER>
                                export SYSTEM_B_HOST=${params.SYSTEM_B_HOST} && 
                                export CLI_BINARY=${CLI_BINARY_PATH} && 
                                export TEST_RESULTS_DIR=/home/<USER>/test-results && 
                                timeout 300 ./cli-scripts/system-a-to-system-b-test.sh
                            """
                            break
                            
                        case 'load':
                            testCommand = """
                                cd /home/<USER>
                                export SYSTEM_B_HOST=${params.SYSTEM_B_HOST} && 
                                export CLI_BINARY=${CLI_BINARY_PATH} && 
                                export TEST_RESULTS_DIR=/home/<USER>/test-results && 
                                timeout 600 ./cli-scripts/system-a-to-system-b-test.sh
                            """
                            break
                            
                        case 'full':
                        default:
                            testCommand = """
                                cd /home/<USER>
                                export SYSTEM_B_HOST=${params.SYSTEM_B_HOST} && 
                                export CLI_BINARY=${CLI_BINARY_PATH} && 
                                export TEST_RESULTS_DIR=/home/<USER>/test-results && 
                                ./cli-scripts/quick-connectivity-test.sh && 
                                timeout 900 ./cli-scripts/system-a-to-system-b-test.sh
                            """
                            break
                    }
                    
                    // Execute tests on System A using sshagent
                    sshagent([JENKINS_CREDENTIALS_ID]) {
                        def testResult = sh(
                            script: """
                                ssh -o StrictHostKeyChecking=no ${SSH_USER}@${params.SYSTEM_A_HOST} '${testCommand}'
                            """,
                            returnStatus: true
                        )
                        
                        if (testResult != 0) {
                            error("CLI tests failed with exit code: ${testResult}")
                        }
                    }
                    
                    if (testResult != 0) {
                        error("CLI tests failed with exit code: ${testResult}")
                    }
                }
            }
        }
        
        stage('Collect Test Results') {
            steps {
                script {
                    echo "Collecting test results from System A..."
                    
                    // Create local results directory
                    sh 'mkdir -p test-results'
                    
                    // Copy test results from System A using sshagent
                    sshagent([JENKINS_CREDENTIALS_ID]) {
                        sh """
                            scp -o StrictHostKeyChecking=no -r ${SSH_USER}@${params.SYSTEM_A_HOST}:/home/<USER>/test-results/* ./test-results/ || echo "No test results to copy"
                            scp -o StrictHostKeyChecking=no ${SSH_USER}@${params.SYSTEM_A_HOST}:/home/<USER>/logs/* ./test-results/ || echo "No logs to copy"
                        """
                    }
                    
                    // List collected results
                    sh 'ls -la test-results/'
                }
            }
        }
        
        stage('Generate Test Report') {
            steps {
                script {
                    echo "Generating test report..."
                    
                    // Create a comprehensive test report
                    sh '''
                        cat > test-results/jenkins-test-report.md << EOF
# NeuralMeter CLI Test Report

## Test Execution Details
- **Build Number**: ${BUILD_NUMBER}
- **Build URL**: ${BUILD_URL}
- **Timestamp**: $(date)
- **System A Host**: ${SYSTEM_A_HOST}
- **System B Host**: ${SYSTEM_B_HOST}
- **Test Suite**: ${TEST_SUITE}

## Test Environment
- **Jenkins Agent**: ${NODE_NAME}
- **Workspace**: ${WORKSPACE}
- **CLI Binary**: neuralmeter-linux
- **SSH User**: ${SSH_USER}

## Test Results Summary
$(if [ -f test-results/test-report.txt ]; then cat test-results/test-report.txt; else echo "Test report not found"; fi)

## System Information
### System A (CLI Execution Machine)
- Host: ${SYSTEM_A_HOST}
- Purpose: NeuralMeter CLI execution with GPU acceleration
- User: ${SSH_USER}

### System B (Target Services)
- Host: ${SYSTEM_B_HOST}
- Purpose: Target services for load testing
- Services: nginx (port 80), Flask API (port 5000)

## Test Artifacts
$(ls -la test-results/)

## Next Steps
- Review test results for any failures
- Check GPU utilization during tests
- Verify load test performance metrics
- Update CLI configuration if needed

---
Generated by Jenkins Pipeline: ${JOB_NAME} #${BUILD_NUMBER}
EOF
                    '''
                }
            }
        }
    }
    
    post {
        always {
            script {
                echo "Post-build actions..."
                
                // Archive test results
                archiveArtifacts artifacts: 'test-results/**/*', allowEmptyArchive: true
                
                // Cleanup on System A if requested
                if (params.CLEANUP_AFTER_TEST) {
                    sshagent([JENKINS_CREDENTIALS_ID]) {
                        sh """
                            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${params.SYSTEM_A_HOST} '
                                rm -rf /home/<USER>/test-results/*
                                rm -rf /home/<USER>/logs/*
                                ${CLI_BINARY_PATH} server stop || true
                            ' || echo "Cleanup completed with warnings"
                        """
                    }
                }
            }
        }
        
        success {
            echo "✅ CLI tests completed successfully!"
            
            // Send success notification if configured
            // emailext (
            //     subject: "✅ NeuralMeter CLI Tests Passed - Build #${BUILD_NUMBER}",
            //     body: "CLI tests on System A against System B completed successfully.\n\nBuild: ${BUILD_URL}",
            //     to: "${env.CHANGE_AUTHOR_EMAIL}"
            // )
        }
        
        failure {
            echo "❌ CLI tests failed!"
            
            // Send failure notification if configured
            // emailext (
            //     subject: "❌ NeuralMeter CLI Tests Failed - Build #${BUILD_NUMBER}",
            //     body: "CLI tests on System A against System B failed.\n\nCheck logs: ${BUILD_URL}console",
            //     to: "${env.CHANGE_AUTHOR_EMAIL}"
            // )
        }
        
        cleanup {
            // Clean workspace
            cleanWs()
        }
    }
}