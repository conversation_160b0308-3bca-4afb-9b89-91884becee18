pipeline {
    agent any
    
    parameters {
        string(name: 'SYSTEM_A_HOST', defaultValue: '*************', description: 'System A (CLI Execution Machine) IP address')
        string(name: 'SYSTEM_B_HOST', defaultValue: '*************', description: 'System B (Target Services) IP address')
        choice(name: 'TEST_LEVEL', choices: ['diagnostic', 'connectivity', 'basic'], description: 'Test level to run')
    }
    
    environment {
        SSH_USER = 'neuro'
        JENKINS_CREDENTIALS_ID = 'ssh-credentials'
    }
    
    stages {
        stage('Diagnostic - Environment Check') {
            steps {
                script {
                    echo "=== DIAGNOSTIC STAGE 1: Environment Check ==="
                    echo "Jenkins Node: ${env.NODE_NAME}"
                    echo "Workspace: ${env.WORKSPACE}"
                    echo "System A Host: ${params.SYSTEM_A_HOST}"
                    echo "System B Host: ${params.SYSTEM_B_HOST}"
                    echo "Test Level: ${params.TEST_LEVEL}"
                    echo "SSH User: ${env.SSH_USER}"
                    
                    // Check basic tools
                    sh 'echo "Current user: $(whoami)"'
                    sh 'echo "Current directory: $(pwd)"'
                    sh 'echo "Available tools:"'
                    sh 'which go || echo "Go not found"'
                    sh 'which ssh || echo "SSH not found"'
                    sh 'which curl || echo "curl not found"'
                    sh 'which scp || echo "scp not found"'
                    
                    echo "✅ Environment check completed"
                }
            }
        }
        
        stage('Diagnostic - Network Connectivity') {
            steps {
                script {
                    echo "=== DIAGNOSTIC STAGE 2: Network Connectivity ==="
                    
                    // Test System A connectivity
                    echo "Testing System A connectivity..."
                    def systemAResult = sh(
                        script: "ping -c 1 -W 5 ${params.SYSTEM_A_HOST}",
                        returnStatus: true
                    )
                    if (systemAResult == 0) {
                        echo "✅ System A (${params.SYSTEM_A_HOST}) is reachable"
                    } else {
                        echo "❌ System A (${params.SYSTEM_A_HOST}) is NOT reachable"
                    }
                    
                    // Test System B connectivity
                    echo "Testing System B connectivity..."
                    def systemBResult = sh(
                        script: "ping -c 1 -W 5 ${params.SYSTEM_B_HOST}",
                        returnStatus: true
                    )
                    if (systemBResult == 0) {
                        echo "✅ System B (${params.SYSTEM_B_HOST}) is reachable"
                    } else {
                        echo "❌ System B (${params.SYSTEM_B_HOST}) is NOT reachable"
                    }
                    
                    // Test System B HTTP service
                    echo "Testing System B HTTP service..."
                    def httpResult = sh(
                        script: "curl --connect-timeout 5 --max-time 10 -s http://${params.SYSTEM_B_HOST}/ > /dev/null",
                        returnStatus: true
                    )
                    if (httpResult == 0) {
                        echo "✅ System B HTTP service is responding"
                    } else {
                        echo "❌ System B HTTP service is NOT responding"
                    }
                    
                    echo "✅ Network connectivity check completed"
                }
            }
        }
        
        stage('Diagnostic - Go Build Test') {
            when {
                expression { params.TEST_LEVEL != 'diagnostic' }
            }
            steps {
                script {
                    echo "=== DIAGNOSTIC STAGE 3: Go Build Test ==="
                    
                    // Check if we can build the CLI
                    echo "Checking Go environment..."
                    sh 'go version'
                    sh 'go env'
                    
                    echo "Attempting to build CLI binary..."
                    def buildResult = sh(
                        script: '''
                            export GOOS=linux
                            export GOARCH=amd64
                            export CGO_ENABLED=1
                            go build -o neuralmeter-linux-test cmd/neuralmeter/main.go
                        ''',
                        returnStatus: true
                    )
                    
                    if (buildResult == 0) {
                        echo "✅ CLI binary built successfully"
                        sh 'ls -la neuralmeter-linux-test'
                        sh 'file neuralmeter-linux-test'
                    } else {
                        echo "❌ CLI binary build failed"
                        error("Build failed - stopping pipeline")
                    }
                    
                    echo "✅ Go build test completed"
                }
            }
        }
        
        stage('Diagnostic - SSH Test') {
            when {
                expression { params.TEST_LEVEL == 'basic' }
            }
            steps {
                script {
                    echo "=== DIAGNOSTIC STAGE 4: SSH Test ==="
                    
                    // This stage will help identify SSH credential issues
                    echo "Testing SSH connectivity to System A..."
                    echo "Note: This stage requires SSH credentials to be configured"
                    
                    // Test SSH connection using sshagent
                    sshagent([JENKINS_CREDENTIALS_ID]) {
                        def sshResult = sh(
                            script: "ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${env.SSH_USER}@${params.SYSTEM_A_HOST} 'echo \"SSH connection successful\"'",
                            returnStatus: true
                        )
                        
                        if (sshResult == 0) {
                            echo "✅ SSH connection to System A successful"
                        } else {
                            echo "❌ SSH connection to System A failed"
                        }
                    }
                    
                    echo "✅ SSH test stage completed"
                }
            }
        }
    }
    
    post {
        always {
            script {
                echo "=== POST ACTIONS ==="
                echo "Pipeline execution completed"
                echo "Check the stages above for any issues"
                
                // Clean up test binary
                sh 'rm -f neuralmeter-linux-test || true'
            }
        }
        
        success {
            echo "✅ Diagnostic pipeline completed successfully!"
            echo "All stages executed without critical errors"
        }
        
        failure {
            echo "❌ Diagnostic pipeline failed!"
            echo "Check the failed stage output above for details"
        }
    }
}