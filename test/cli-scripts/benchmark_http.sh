#!/usr/bin/env bash
# benchmark_http.sh – run a simple HTTP GET load against the NeuralMeter worker’s controller API.

if [ -z "${WORKER:-}" ]; then
  echo "Usage: WORKER=<host:port> $0"
  exit 1
fi

PLAN_PATH="test/plans/http_get.yaml"
if [ ! -f "$PLAN_PATH" ]; then
  echo "❗ $PLAN_PATH not found – create a plan first or adjust the script."
  exit 1
fi

./neuralmeter-cuda run \
  --plan "$PLAN_PATH" \
  --controller "http://$WORKER/api" 