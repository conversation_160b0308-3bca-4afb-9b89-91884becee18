# NeuralMeter CLI Test Configuration
# Configuration for System A to System B testing

# System Configuration
systems:
  system_a:
    description: "NeuralMeter CLI Execution Machine with GPU"
    requirements:
      - "Go runtime >= 1.21"
      - "GPU drivers (NVIDIA/AMD/Intel)"
      - "SSH access configured"
      - "NeuralMeter CLI binary"
    
  system_b:
    description: "Target Services for Load Testing"
    default_host: "*************"
    services:
      - name: "nginx"
        port: 80
        endpoints:
          - "/"
          - "/health"
          - "/api/"
      - name: "flask_api"
        port: 5000
        endpoints:
          - "/"
          - "/api/data"
          - "/api/slow"

# Test Configuration
test_config:
  cli_binary: "./neuralmeter"
  results_dir: "./test-results"
  log_level: "info"
  timeout: 30
  
  # Test categories to run
  test_categories:
    - "basic_functionality"
    - "gpu_detection"
    - "connectivity"
    - "load_testing"
    - "daemon_mode"
    - "validation"

# Test Plans
test_plans:
  basic_http:
    name: "Basic HTTP Test"
    duration: "10s"
    concurrency: 5
    ramp_up: "2s"
    
  api_test:
    name: "API Endpoint Test"
    duration: "15s"
    concurrency: 3
    ramp_up: "3s"
    
  load_test:
    name: "Load Test"
    duration: "30s"
    concurrency: 10
    ramp_up: "5s"
    ramp_down: "5s"
    
  stress_test:
    name: "Stress Test"
    duration: "60s"
    concurrency: 20
    ramp_up: "10s"
    ramp_down: "10s"

# Expected Results
expected_results:
  success_rate_threshold: 95.0
  max_avg_latency_ms: 1000
  min_throughput_rps: 10
  
# GPU Requirements
gpu_requirements:
  nvidia:
    min_cuda_version: "11.8"
    min_memory_gb: 2.0
    min_compute_capability: "3.5"
  
  amd:
    min_rocm_version: "5.0"
    min_memory_gb: 2.0
  
  intel:
    min_opencl_version: "2.0"
    min_memory_gb: 1.0

# Monitoring
monitoring:
  system_resources: true
  gpu_utilization: true
  network_connections: true
  log_performance_metrics: true
  
# Reporting
reporting:
  formats: ["text", "json", "html"]
  include_system_info: true
  include_performance_graphs: false
  save_raw_results: true