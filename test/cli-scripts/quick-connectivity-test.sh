#!/usr/bin/env bash
# quick-connectivity-test.sh - Quick connectivity test between System A and System B
# This script performs basic connectivity checks before running full CLI tests

set -e

# Configuration
SYSTEM_B_HOST="${SYSTEM_B_HOST:-*************}"
CLI_BINARY="${CLI_BINARY:-./neuralmeter}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

run_check() {
    local check_name="$1"
    local check_command="$2"
    
    log "Checking: $check_name"
    log "Command: $check_command"
    
    # Show FULL output - no hiding anything
    echo "--- COMMAND OUTPUT START ---"
    if eval "$check_command"; then
        echo "--- COMMAND OUTPUT END ---"
        success "✓ $check_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        local exit_code=$?
        echo "--- COMMAND OUTPUT END ---"
        error "✗ $check_name (exit code: $exit_code)"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Main connectivity checks
main() {
    log "Starting quick connectivity test..."
    log "System B Host: $SYSTEM_B_HOST"
    log "CLI Binary: $CLI_BINARY"
    echo ""
    
    # CLI Binary Tests
    log "=== CLI Binary Tests ==="
    run_check "CLI binary exists" "test -f $CLI_BINARY"
    run_check "CLI binary is executable" "test -x $CLI_BINARY"
    
    # Test ALL CLI commands with verbose output
    log "=== Testing ALL CLI Commands ==="
    
    # Test version command
    run_check "CLI version command" "$CLI_BINARY version"
    
    # Test help command
    run_check "CLI help command" "$CLI_BINARY --help"
    
    # Test config command
    run_check "CLI config command" "$CLI_BINARY config"
    
    # Test verbose flag
    run_check "CLI verbose flag" "$CLI_BINARY --verbose config"
    
    # Test quiet flag
    run_check "CLI quiet flag" "$CLI_BINARY --quiet config"
    
    # Test log-level flag
    run_check "CLI log-level debug" "$CLI_BINARY --log-level debug config"
    run_check "CLI log-level info" "$CLI_BINARY --log-level info config"
    run_check "CLI log-level warn" "$CLI_BINARY --log-level warn config"
    run_check "CLI log-level error" "$CLI_BINARY --log-level error config"
    
    # CLI Target Connectivity Tests
    log ""
    log "=== CLI Target Connectivity Tests ==="
    
    # Create a simple test plan for connectivity testing (using correct CLI format)
    cat > /tmp/connectivity-test.yaml << EOF
name: "CLI Connectivity Test"
description: "Test CLI can reach System B targets"
version: "1.0"

duration: "10s"
concurrency: 2
ramp_up: "2s"

scenarios:
  - name: "connectivity_check"
    description: "Basic connectivity via CLI"
    weight: 100
    requests:
      - name: "homepage_test"
        method: "GET"
        url: "http://$SYSTEM_B_HOST/"
        headers:
          User-Agent: "NeuralMeter-ConnectivityTest/1.0"
        timeout: "5s"
      - name: "health_test"
        method: "GET"
        url: "http://$SYSTEM_B_HOST/health"
        headers:
          User-Agent: "NeuralMeter-ConnectivityTest/1.0"
        timeout: "5s"

global:
  timeout: "5s"

output:
  format: ["json"]
  detailed: true
EOF
    
    # Test validation with verbose output for debugging
    log "Testing CLI validation..."
    if $CLI_BINARY validate /tmp/connectivity-test.yaml 2>&1; then
        success "✓ CLI can validate test plan"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        
        # Only run execution test if validation passes (CLI expects test plan file directly)
        log "Executing load test and capturing results..."
        if $CLI_BINARY run /tmp/connectivity-test.yaml > /tmp/load-test-output.txt 2>&1; then
            success "✓ CLI can execute test against System B"
            TESTS_PASSED=$((TESTS_PASSED + 1))
            
            log "Load test results:"
            cat /tmp/load-test-output.txt
            
            log "Analyzing load test metrics..."
            echo "=== TRANSACTION ANALYSIS ==="
            grep -i "request\|transaction\|total" /tmp/load-test-output.txt || echo "No transaction metrics found"
            echo "=== PERFORMANCE METRICS ==="
            grep -i "latency\|response.*time\|throughput\|rps" /tmp/load-test-output.txt || echo "No performance metrics found"
            echo "=== SUCCESS/ERROR RATES ==="
            grep -i "success\|error\|fail" /tmp/load-test-output.txt || echo "No success/error metrics found"
            
            rm -f /tmp/load-test-output.txt
        else
            error "✗ CLI can execute test against System B"
            TESTS_FAILED=$((TESTS_FAILED + 1))
            
            log "Load test error output:"
            cat /tmp/load-test-output.txt 2>/dev/null || echo "No error output captured"
            rm -f /tmp/load-test-output.txt
        fi
    else
        error "✗ CLI can validate test plan against System B"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        
        # Show validation error for debugging
        warning "Validation error details:"
        $CLI_BINARY validate /tmp/connectivity-test.yaml 2>&1 || true
        
        # Try with a simpler test plan using httpbin.org as fallback
        warning "Trying fallback test plan with httpbin.org..."
        cat > /tmp/fallback-test.yaml << EOF
name: "CLI Fallback Test"
description: "Fallback connectivity test"
version: "1.0"

duration: "5s"
concurrency: 1
ramp_up: "1s"

scenarios:
  - name: "fallback_test"
    description: "Basic fallback test"
    weight: 100
    requests:
      - name: "httpbin_test"
        method: "GET"
        url: "https://httpbin.org/get"
        headers:
          User-Agent: "NeuralMeter-FallbackTest/1.0"
        timeout: "10s"

global:
  timeout: "10s"

output:
  format: ["json"]
  detailed: true
EOF
        
        run_check "CLI can validate fallback test plan" "$CLI_BINARY validate /tmp/fallback-test.yaml"
        rm -f /tmp/fallback-test.yaml
    fi
    
    # CLI Metrics and Additional Commands
    log ""
    log "=== CLI Metrics and Additional Commands ==="
    
    # Test metrics command
    run_check "CLI metrics help" "$CLI_BINARY metrics --help"
    run_check "CLI metrics command" "$CLI_BINARY metrics || echo 'Metrics command may not be fully implemented'"
    
    # Test completion command
    run_check "CLI completion help" "$CLI_BINARY completion --help"
    run_check "CLI completion bash" "$CLI_BINARY completion bash || echo 'Completion may not be fully implemented'"
    
    # Test with custom config file
    log "Testing custom config file support..."
    echo "# Test config" > /tmp/test-config.yaml
    run_check "CLI with custom config" "$CLI_BINARY --config /tmp/test-config.yaml config || echo 'Custom config test completed'"
    rm -f /tmp/test-config.yaml
    
    # Clean up test files
    rm -f /tmp/connectivity-test.yaml
    
    # CLI GPU Functionality Tests
    log ""
    log "=== CLI GPU Functionality Tests ==="
    
    # Test GPU commands
    run_check "CLI GPU help" "$CLI_BINARY gpu --help"
    run_check "CLI GPU list command" "$CLI_BINARY gpu list"
    run_check "CLI GPU list verbose" "$CLI_BINARY --verbose gpu list"
    
    # Test GPU status for each detected device (if any)
    log "Testing GPU status commands..."
    gpu_devices=$($CLI_BINARY gpu list 2>/dev/null | grep -c "Device" || echo "0")
    if [[ $gpu_devices -gt 0 ]]; then
        success "✓ CLI detected $gpu_devices GPU device(s)"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        
        # Test GPU status for device 0 (if exists)
        run_check "CLI GPU status device 0" "$CLI_BINARY gpu status 0 || echo 'GPU status command may not be fully implemented'"
    else
        warning "CLI did not detect GPU devices (may be normal if no GPU)"
    fi
    
    # CLI Server Mode Tests
    log ""
    log "=== CLI Server Mode Tests ==="
    
    # Test all server commands
    run_check "CLI server help" "$CLI_BINARY server --help"
    run_check "CLI server status (initial)" "$CLI_BINARY server status || echo 'Server not running (expected)'"
    
    # Test server start command
    log "Testing server start/stop cycle..."
    if run_check "CLI server start" "$CLI_BINARY server start --daemon --port 8081"; then
        sleep 3  # Give server time to start
        
        run_check "CLI server status (running)" "$CLI_BINARY server status"
        run_check "CLI server stop" "$CLI_BINARY server stop"
        
        sleep 2  # Give server time to stop
        run_check "CLI server status (stopped)" "$CLI_BINARY server status || echo 'Server stopped (expected)'"
    else
        warning "Server start failed - may be normal in test environment"
    fi
    
    # Test server restart command
    run_check "CLI server restart help" "$CLI_BINARY server restart --help"
    
    # Summary
    echo ""
    log "=== Test Summary ==="
    local total_tests=$((TESTS_PASSED + TESTS_FAILED))
    log "Total checks: $total_tests"
    success "Passed: $TESTS_PASSED"
    
    if [[ $TESTS_FAILED -gt 0 ]]; then
        error "Failed: $TESTS_FAILED"
        echo ""
        error "Some connectivity checks failed. Please resolve issues before running full tests."
        exit 1
    else
        echo ""
        success "All connectivity checks passed! System is ready for full CLI testing."
        echo ""
        log "Next steps:"
        log "1. Run full test suite: ./test/cli-scripts/system-a-to-system-b-test.sh"
        log "2. Or run specific tests as needed"
        exit 0
    fi
}

# Run main function
main "$@"