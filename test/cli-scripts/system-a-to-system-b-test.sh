#!/usr/bin/env bash
# system-a-to-system-b-test.sh - Comprehensive CLI test from System A to System B
# This script tests the NeuralMeter CLI execution on System A against target services on System B

set -e

# Configuration
SYSTEM_B_HOST="${SYSTEM_B_HOST:-*************}"
CLI_BINARY="${CLI_BINARY:-./neuralmeter}"
TEST_RESULTS_DIR="${TEST_RESULTS_DIR:-./test-results}"
LOG_FILE="${LOG_FILE:-${TEST_RESULTS_DIR}/system-a-to-b-test.log}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Test result tracking
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    log "Running test: $test_name"
    
    if eval "$test_command"; then
        success "✓ $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        error "✗ $test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Initialize test environment
initialize_test_env() {
    log "Initializing test environment..."
    
    # Create test results directory
    mkdir -p "$TEST_RESULTS_DIR"
    
    # Clear previous log
    > "$LOG_FILE"
    
    log "Test environment initialized"
    log "System B Host: $SYSTEM_B_HOST"
    log "CLI Binary: $CLI_BINARY"
    log "Results Directory: $TEST_RESULTS_DIR"
}

# Pre-flight checks
preflight_checks() {
    log "Running pre-flight checks..."
    
    # Check if CLI binary exists
    if [[ ! -f "$CLI_BINARY" ]]; then
        error "CLI binary not found at: $CLI_BINARY"
        return 1
    fi
    
    # Check if CLI binary is executable
    if [[ ! -x "$CLI_BINARY" ]]; then
        error "CLI binary is not executable: $CLI_BINARY"
        return 1
    fi
    
    # Check System B connectivity
    if ! ping -c 1 "$SYSTEM_B_HOST" >/dev/null 2>&1; then
        error "Cannot reach System B at: $SYSTEM_B_HOST"
        return 1
    fi
    
    # Check System B HTTP service
    if ! curl -s --connect-timeout 5 "http://$SYSTEM_B_HOST/" >/dev/null; then
        error "System B HTTP service not responding at: http://$SYSTEM_B_HOST/"
        return 1
    fi
    
    success "Pre-flight checks passed"
    return 0
}

# Test CLI basic functionality
test_cli_basic() {
    log "Testing CLI basic functionality..."
    
    run_test "CLI version check" "$CLI_BINARY version"
    run_test "CLI help display" "$CLI_BINARY --help"
    run_test "CLI config display" "$CLI_BINARY config"
}

# Test GPU detection and availability
test_gpu_detection() {
    log "Testing GPU detection..."
    
    run_test "GPU list command" "$CLI_BINARY gpu list"
    
    # Check if NVIDIA GPU is available
    if command -v nvidia-smi >/dev/null 2>&1; then
        run_test "NVIDIA GPU detection" "nvidia-smi --query-gpu=name --format=csv,noheader,nounits"
    else
        warning "NVIDIA GPU tools not available - skipping NVIDIA-specific tests"
    fi
    
    # Check if AMD GPU is available
    if command -v rocminfo >/dev/null 2>&1; then
        run_test "AMD GPU detection" "rocminfo | grep -q 'Device'"
    else
        warning "AMD GPU tools not available - skipping AMD-specific tests"
    fi
}

# Create test plans for System B testing
create_test_plans() {
    log "Creating test plans for System B testing..."
    
    # Basic HTTP GET test plan
    cat > "$TEST_RESULTS_DIR/basic-http-test.yaml" << EOF
name: "System A to System B Basic HTTP Test"
description: "Basic HTTP GET test from System A to System B"
version: "1.0"

duration: "10s"
concurrency: 5
ramp_up: "2s"

scenarios:
  - name: "basic_http_get"
    description: "Basic HTTP GET to System B"
    weight: 100
    requests:
      - name: "get_homepage"
        method: "GET"
        url: "http://$SYSTEM_B_HOST/"
        headers:
          User-Agent: "NeuralMeter-SystemA/1.0"
        timeout: "5s"

global:
  timeout: "5s"

output:
  format: ["json"]
  detailed: true
EOF

    # API endpoint test plan
    cat > "$TEST_RESULTS_DIR/api-test.yaml" << EOF
name: "System A to System B API Test"
description: "API endpoint test from System A to System B"
version: "1.0"

duration: "15s"
concurrency: 3
ramp_up: "3s"

scenarios:
  - name: "api_endpoints"
    description: "Test API endpoints on System B"
    weight: 100
    requests:
      - name: "api_health"
        method: "GET"
        url: "http://$SYSTEM_B_HOST/health"
        timeout: "5s"
      - name: "api_data"
        method: "GET"
        url: "http://$SYSTEM_B_HOST/api/"
        timeout: "5s"

global:
  timeout: "5s"

output:
  format: ["json"]
  detailed: true
EOF

    # Load test plan
    cat > "$TEST_RESULTS_DIR/load-test.yaml" << EOF
name: "System A to System B Load Test"
description: "Load test from System A to System B"
version: "1.0"

duration: "20s"
concurrency: 10
ramp_up: "5s"
ramp_down: "5s"

scenarios:
  - name: "load_test"
    description: "Load test against System B"
    weight: 100
    requests:
      - name: "load_homepage"
        method: "GET"
        url: "http://$SYSTEM_B_HOST/"
        headers:
          User-Agent: "NeuralMeter-LoadTest/1.0"
        timeout: "10s"

global:
  timeout: "10s"

output:
  format: ["json", "text"]
  detailed: true
EOF

    success "Test plans created successfully"
}

# Test CLI execution against System B
test_cli_execution() {
    log "Testing CLI execution against System B..."
    
    # Test plan validation
    run_test "Validate basic HTTP test plan" "$CLI_BINARY validate $TEST_RESULTS_DIR/basic-http-test.yaml"
    run_test "Validate API test plan" "$CLI_BINARY validate $TEST_RESULTS_DIR/api-test.yaml"
    run_test "Validate load test plan" "$CLI_BINARY validate $TEST_RESULTS_DIR/load-test.yaml"
    
    # Execute test plans
    run_test "Execute basic HTTP test" "$CLI_BINARY run $TEST_RESULTS_DIR/basic-http-test.yaml --output-format json > $TEST_RESULTS_DIR/basic-http-results.json"
    run_test "Execute API test" "$CLI_BINARY run $TEST_RESULTS_DIR/api-test.yaml --output-format json > $TEST_RESULTS_DIR/api-results.json"
    run_test "Execute load test" "$CLI_BINARY run $TEST_RESULTS_DIR/load-test.yaml --output-format json > $TEST_RESULTS_DIR/load-results.json"
}

# Test server daemon functionality
test_server_daemon() {
    log "Testing server daemon functionality..."
    
    # Test server status (should not be running initially)
    run_test "Check server status (should be stopped)" "$CLI_BINARY server status || true"
    
    # Start server daemon
    run_test "Start server daemon" "$CLI_BINARY server start --daemon --port 8080"
    
    # Wait for server to start
    sleep 3
    
    # Check server status (should be running now)
    run_test "Check server status (should be running)" "$CLI_BINARY server status"
    
    # Test server connectivity
    run_test "Test server HTTP endpoint" "curl -s http://localhost:8080/health || curl -s http://localhost:8080/ || true"
    
    # Stop server daemon
    run_test "Stop server daemon" "$CLI_BINARY server stop"
    
    # Wait for server to stop
    sleep 2
    
    # Check server status (should be stopped again)
    run_test "Check server status (should be stopped)" "$CLI_BINARY server status || true"
}

# Analyze test results
analyze_results() {
    log "Analyzing test results..."
    
    # Check if result files exist and contain valid JSON
    for result_file in "$TEST_RESULTS_DIR"/*.json; do
        if [[ -f "$result_file" ]]; then
            if jq empty "$result_file" 2>/dev/null; then
                success "Valid JSON result file: $(basename "$result_file")"
            else
                error "Invalid JSON in result file: $(basename "$result_file")"
            fi
        fi
    done
    
    # Display basic statistics from results
    if [[ -f "$TEST_RESULTS_DIR/load-results.json" ]]; then
        log "Load test results summary:"
        if jq -r '.summary // "No summary available"' "$TEST_RESULTS_DIR/load-results.json" 2>/dev/null; then
            success "Load test results parsed successfully"
        else
            warning "Could not parse load test results"
        fi
    fi
}

# System resource monitoring
monitor_system_resources() {
    log "Monitoring system resources during tests..."
    
    # CPU usage
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    log "CPU Usage: ${cpu_usage}%"
    
    # Memory usage
    memory_info=$(free -h | grep "Mem:")
    log "Memory Info: $memory_info"
    
    # GPU usage (if available)
    if command -v nvidia-smi >/dev/null 2>&1; then
        gpu_usage=$(nvidia-smi --query-gpu=utilization.gpu --format=csv,noheader,nounits)
        log "GPU Usage: ${gpu_usage}%"
    fi
    
    # Network connections
    network_connections=$(netstat -an | grep ESTABLISHED | wc -l)
    log "Active Network Connections: $network_connections"
}

# Generate test report
generate_report() {
    log "Generating test report..."
    
    local report_file="$TEST_RESULTS_DIR/test-report.txt"
    
    cat > "$report_file" << EOF
NeuralMeter CLI System A to System B Test Report
================================================
Generated: $(date)
System B Host: $SYSTEM_B_HOST
CLI Binary: $CLI_BINARY

Test Summary:
- Total Tests: $TESTS_TOTAL
- Passed: $TESTS_PASSED
- Failed: $TESTS_FAILED
- Success Rate: $(( TESTS_PASSED * 100 / TESTS_TOTAL ))%

Test Categories:
1. CLI Basic Functionality
2. GPU Detection and Availability
3. Test Plan Validation
4. CLI Execution Against System B
5. Server Daemon Functionality

Results Files:
$(ls -la "$TEST_RESULTS_DIR"/*.json 2>/dev/null || echo "No JSON result files found")

System Information:
- OS: $(uname -a)
- Go Version: $(go version 2>/dev/null || echo "Go not available")
- GPU Info: $(nvidia-smi --query-gpu=name --format=csv,noheader,nounits 2>/dev/null || echo "No NVIDIA GPU detected")

For detailed logs, see: $LOG_FILE
EOF

    success "Test report generated: $report_file"
}

# Cleanup function
cleanup() {
    log "Cleaning up test environment..."
    
    # Stop any running server daemon
    "$CLI_BINARY" server stop 2>/dev/null || true
    
    # Remove temporary files if needed
    # (keeping test results for analysis)
    
    log "Cleanup completed"
}

# Main test execution
main() {
    log "Starting NeuralMeter CLI System A to System B tests..."
    
    # Set up cleanup trap
    trap cleanup EXIT
    
    # Initialize test environment
    initialize_test_env
    
    # Run pre-flight checks
    if ! preflight_checks; then
        error "Pre-flight checks failed. Aborting tests."
        exit 1
    fi
    
    # Monitor system resources
    monitor_system_resources
    
    # Run test suites
    test_cli_basic
    test_gpu_detection
    create_test_plans
    test_cli_execution
    test_server_daemon
    
    # Analyze results
    analyze_results
    
    # Generate final report
    generate_report
    
    # Final summary
    log "Test execution completed"
    log "Results: $TESTS_PASSED/$TESTS_TOTAL tests passed"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        success "All tests passed! System A to System B CLI testing successful."
        exit 0
    else
        error "$TESTS_FAILED tests failed. Check logs for details."
        exit 1
    fi
}

# Run main function
main "$@"