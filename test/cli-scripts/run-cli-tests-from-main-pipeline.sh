#!/usr/bin/env bash
# run-cli-tests-from-main-pipeline.sh
# Script to run CLI tests from the main <PERSON> pipeline after build and deployment

set -e

# Configuration from environment or defaults
SYSTEM_A_HOST="${SYSTEM_A_HOST:-*************}"
SYSTEM_B_HOST="${SYSTEM_B_HOST:-*************}"
CLI_BINARY_PATH="${CLI_BINARY_PATH:-/home/<USER>/cli/neuralmeter}"
TEST_SUITE="${TEST_SUITE:-connectivity}"
SSH_USER="${SSH_USER:-neuro}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[CLI-TEST]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Main function
main() {
    log "Starting CLI tests from main pipeline..."
    log "System A: $SYSTEM_A_HOST"
    log "System B: $SYSTEM_B_HOST"
    log "CLI Binary: $CLI_BINARY_PATH"
    log "Test Suite: $TEST_SUITE"
    
    # Deploy test scripts to System A
    log "Deploying test scripts to System A..."
    scp -o StrictHostKeyChecking=no -r test/cli-scripts/ ${SSH_USER}@${SYSTEM_A_HOST}:/home/<USER>/
    ssh -o StrictHostKeyChecking=no ${SSH_USER}@${SYSTEM_A_HOST} 'chmod +x /home/<USER>/cli-scripts/*.sh'
    
    # Create test results directory
    ssh -o StrictHostKeyChecking=no ${SSH_USER}@${SYSTEM_A_HOST} 'mkdir -p /home/<USER>/test-results'
    
    # Run the appropriate test suite
    case "$TEST_SUITE" in
        "connectivity")
            log "Running connectivity tests..."
            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${SYSTEM_A_HOST} "
                cd /home/<USER>
                export SYSTEM_B_HOST=$SYSTEM_B_HOST && 
                export CLI_BINARY=$CLI_BINARY_PATH && 
                ./cli-scripts/quick-connectivity-test.sh
            "
            ;;
        "basic")
            log "Running basic CLI tests..."
            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${SYSTEM_A_HOST} "
                cd /home/<USER>
                export SYSTEM_B_HOST=$SYSTEM_B_HOST && 
                export CLI_BINARY=$CLI_BINARY_PATH && 
                export TEST_RESULTS_DIR=/home/<USER>/test-results && 
                timeout 300 ./cli-scripts/system-a-to-system-b-test.sh
            "
            ;;
        "load")
            log "Running load tests..."
            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${SYSTEM_A_HOST} "
                cd /home/<USER>
                export SYSTEM_B_HOST=$SYSTEM_B_HOST && 
                export CLI_BINARY=$CLI_BINARY_PATH && 
                export TEST_RESULTS_DIR=/home/<USER>/test-results && 
                timeout 600 ./cli-scripts/system-a-to-system-b-test.sh
            "
            ;;
        "full")
            log "Running full test suite..."
            ssh -o StrictHostKeyChecking=no ${SSH_USER}@${SYSTEM_A_HOST} "
                cd /home/<USER>
                export SYSTEM_B_HOST=$SYSTEM_B_HOST && 
                export CLI_BINARY=$CLI_BINARY_PATH && 
                export TEST_RESULTS_DIR=/home/<USER>/test-results && 
                ./cli-scripts/quick-connectivity-test.sh && 
                timeout 900 ./cli-scripts/system-a-to-system-b-test.sh
            "
            ;;
        *)
            error "Unknown test suite: $TEST_SUITE"
            exit 1
            ;;
    esac
    
    # Collect test results
    log "Collecting test results..."
    mkdir -p ./test-results
    scp -o StrictHostKeyChecking=no -r ${SSH_USER}@${SYSTEM_A_HOST}:/home/<USER>/test-results/* ./test-results/ || echo "No test results to copy"
    
    # Display summary
    if [[ -f "./test-results/test-report.txt" ]]; then
        log "Test Results Summary:"
        cat ./test-results/test-report.txt
    fi
    
    success "CLI tests completed successfully!"
}

# Run main function
main "$@"