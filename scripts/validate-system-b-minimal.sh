#!/bin/bash
# validate-system-b-minimal.sh - Minimal System B Validation (Working Services Only)

set -euo pipefail

# Force environment reset if running as different user
if [ "${SUDO_USER:-}" != "" ] && [ "$(whoami)" != "${SUDO_USER}" ]; then
    export HOME="/home/<USER>"
    export USER="$(whoami)"
    cd "$HOME" 2>/dev/null || cd /tmp
fi

LOG_FILE="/var/log/neuralmeter-validation-minimal.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

echo "=== NeuralMeter System B Minimal Validation Started $(date) ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test result tracking
declare -a FAILED_TEST_NAMES=()

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED_TESTS++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED_TESTS++))
    FAILED_TEST_NAMES+=("$1")
}

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    ((TOTAL_TESTS++))
    log_info "Running test: $test_name"
    
    # Run command and capture output for debugging
    local output
    if output=$(eval "$test_command" 2>&1); then
        log_success "$test_name"
        return 0
    else
        log_error "$test_name - Command failed: $test_command"
        log_error "Error output: $output"
        return 1
    fi
}

# Test only working services
test_core_system() {
    log_info "=== Testing Core System ==="
    
    run_test "Python3 installation" "which python3 && python3 --version"
    run_test "psutil Python package" "python3 -c 'import psutil; print(psutil.__version__)'"
    run_test "curl available" "which curl && curl --version | head -1"
}

test_api_services() {
    log_info "=== Testing API Services ==="
    
    for port in 8011 8012 8013; do
        run_test "API service script exists (port $port)" "[ -f /opt/neuralmeter/bin/simple-api-server.py ]"
        run_test "API service is active (port $port)" "systemctl is-active neuralmeter-api-$port"
        run_test "API service is enabled (port $port)" "systemctl is-enabled neuralmeter-api-$port"
        run_test "API service listening (port $port)" "ss -tuln | grep ':$port'"
        run_test "API health endpoint (port $port)" "curl -s -f http://localhost:$port/health | grep -q 'healthy'"
        run_test "API data endpoint (port $port)" "curl -s -f http://localhost:$port/api/data | grep -q 'data'"
    done
}

test_health_service() {
    log_info "=== Testing Health Service ==="
    
    run_test "Health service script exists" "[ -f /opt/neuralmeter/bin/health-server.py ]"
    run_test "Health service is active" "systemctl is-active neuralmeter-health"
    run_test "Health service is enabled" "systemctl is-enabled neuralmeter-health"
    run_test "Health service listening on port 8021" "ss -tuln | grep ':8021'"
    run_test "Health service basic endpoint" "curl -s -f http://localhost:8021/health | grep -q 'healthy'"
    run_test "Health service detailed endpoint" "curl -s -f http://localhost:8021/health/detailed | grep -q 'system'"
    run_test "Health service system endpoint" "curl -s -f http://localhost:8021/health/system | grep -q 'checks'"
    run_test "Health service services endpoint" "curl -s -f http://localhost:8021/health/services | grep -q 'services'"
}

test_file_permissions() {
    log_info "=== Testing File Permissions and Ownership ==="
    
    run_test "NeuralMeter directory exists" "[ -d /opt/neuralmeter ]"
    run_test "NeuralMeter directory ownership" "[ \$(stat -c '%U' /opt/neuralmeter) = 'neuro' ]"
    run_test "Binary directory permissions" "[ -d /opt/neuralmeter/bin ] && [ \$(stat -c '%a' /opt/neuralmeter/bin) = '755' ]"
    run_test "Service scripts are executable" "[ -x /opt/neuralmeter/bin/simple-api-server.py ]"
}

# Optional tests for services that might not be working
test_optional_services() {
    log_info "=== Testing Optional Services (Non-Critical) ==="
    
    # Test HAProxy if available
    if command -v haproxy >/dev/null 2>&1; then
        if systemctl is-active haproxy >/dev/null 2>&1; then
            log_success "HAProxy is installed and running"
            run_test "HAProxy stats endpoint" "curl -s -f http://localhost:8404/stats | grep -q 'HAProxy' || echo 'HAProxy stats not accessible'"
        else
            log_warning "HAProxy is installed but not running"
        fi
    else
        log_warning "HAProxy not installed (optional for basic testing)"
    fi
    
    # Test WebSocket services if available
    for port in 8080 8081; do
        if systemctl is-active neuralmeter-ws-$port >/dev/null 2>&1; then
            log_success "WebSocket service on port $port is running"
            run_test "WebSocket health endpoint (port $port)" "curl -s -f http://localhost:$port/health | grep -q 'healthy' || echo 'WebSocket health not accessible'"
        else
            log_warning "WebSocket service on port $port not running (optional)"
        fi
    done
    
    # Test Prometheus if available
    if command -v prometheus >/dev/null 2>&1; then
        if systemctl is-active prometheus >/dev/null 2>&1; then
            log_success "Prometheus is installed and running"
            run_test "Prometheus health endpoint" "curl -s -f http://localhost:9090/-/healthy || echo 'Prometheus not accessible'"
        else
            log_warning "Prometheus is installed but not running"
        fi
    else
        log_warning "Prometheus not installed (optional for basic testing)"
    fi
}

# Main execution
main() {
    # Debug current execution context
    log_info "=== EXECUTION CONTEXT DEBUG ==="
    log_info "Current EUID: $EUID"
    log_info "Current USER: ${USER:-unset}"
    log_info "SUDO_USER: ${SUDO_USER:-unset}"
    log_info "Current whoami: $(whoami)"
    log_info "Script path: $0"
    log_info "Script args: $*"
    
    # If running as root, switch to neuro user
    if [ "$EUID" -eq 0 ]; then
        log_info "Running as root (EUID=0), attempting to switch to neuro user..."
        
        # Check if neuro user exists
        if id neuro >/dev/null 2>&1; then
            log_info "Neuro user exists, switching execution context..."
            # Re-execute this script as neuro user
            exec sudo -u neuro -H "$0" "$@"
        else
            log_warning "Neuro user does not exist, continuing as root"
        fi
    else
        log_info "Not running as root, continuing with current user"
    fi
    
    log_info "Starting minimal System B validation (working services only)..."
    log_info "Timestamp: $(TZ='America/New_York' date) (Local time)"
    log_info "Timestamp UTC: $(date -u)"
    log_info "Hostname: $(hostname)"
    log_info "Final execution user: $(whoami)"
    
    # Run core test suites (must pass)
    test_core_system
    test_api_services
    test_health_service
    test_file_permissions
    
    # Run optional tests (warnings only)
    test_optional_services
    
    # Summary
    echo ""
    log_info "=== VALIDATION SUMMARY ==="
    log_info "Total critical tests run: $TOTAL_TESTS"
    log_success "Tests passed: $PASSED_TESTS"
    
    if [ $FAILED_TESTS -gt 0 ]; then
        log_error "Critical tests failed: $FAILED_TESTS"
        echo ""
        log_error "Failed critical tests:"
        for test_name in "${FAILED_TEST_NAMES[@]}"; do
            echo -e "  ${RED}✗${NC} $test_name"
        done
        echo ""
        log_error "System B minimal validation FAILED"
        log_warning "Some core services are not working properly"
        exit 1
    else
        echo ""
        log_success "🎉 All critical tests passed! System B core services are operational."
        log_info "System B is ready for basic load testing with NeuralMeter CLI."
        log_info "Working services:"
        echo "  • API Services (8011-8013) - Ready for load testing ✅"
        echo "  • Health Service (8021) - Monitoring available ✅"
        echo "  • Core Python environment - Functional ✅"
        echo ""
        log_info "Optional services may not be running, but core functionality is available."
        exit 0
    fi
}

# Run main function
main "$@"