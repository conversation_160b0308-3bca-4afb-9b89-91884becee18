#!/bin/bash
# deploy-system-b-comprehensive.sh - Comprehensive System B deployment with extensive logging
# Created after multiple deployment failures - focuses on reliability and debugging

set -euo pipefail

# Comprehensive logging setup
SCRIPT_NAME="$(basename "$0")"
LOG_DIR="/var/log/neuralmeter"
MAIN_LOG="$LOG_DIR/deployment-$(date +%Y%m%d_%H%M%S).log"
ERROR_LOG="$LOG_DIR/deployment-errors-$(date +%Y%m%d_%H%M%S).log"

# Create log directory
mkdir -p "$LOG_DIR"

# Redirect all output to logs while showing on screen
exec 1> >(tee -a "$MAIN_LOG")
exec 2> >(tee -a "$ERROR_LOG" >&2)

echo "=============================================================================="
echo "NEURALMETER SYSTEM B COMPREHENSIVE DEPLOYMENT"
echo "=============================================================================="
echo "Script: $SCRIPT_NAME"
echo "Started: $(date)"
echo "User: $(whoami)"
echo "EUID: $EUID"
echo "PWD: $(pwd)"
echo "Main Log: $MAIN_LOG"
echo "Error Log: $ERROR_LOG"
echo "=============================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging functions with timestamps
log_info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] [INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR]${NC} $1"
}

log_debug() {
    echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')] [DEBUG]${NC} $1"
}

log_step() {
    echo ""
    echo "=============================================================================="
    echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')] STEP: $1${NC}"
    echo "=============================================================================="
}

# Error handling
handle_error() {
    local exit_code=$?
    local line_number=$1
    log_error "Script failed at line $line_number with exit code $exit_code"
    log_error "Last command: $BASH_COMMAND"
    log_error "Check logs: $MAIN_LOG and $ERROR_LOG"
    
    # Show recent error context
    echo ""
    echo "Recent error context:"
    tail -20 "$ERROR_LOG" 2>/dev/null || echo "No error log available"
    
    exit $exit_code
}

trap 'handle_error $LINENO' ERR

# System information gathering
gather_system_info() {
    log_step "GATHERING SYSTEM INFORMATION"
    
    log_info "Operating System Information:"
    cat /etc/os-release | while read line; do log_debug "  $line"; done
    
    log_info "System Resources:"
    log_debug "  Memory: $(free -h | grep '^Mem:' | awk '{print $2 " total, " $3 " used, " $7 " available"}')"
    log_debug "  Disk: $(df -h / | tail -1 | awk '{print $2 " total, " $3 " used, " $4 " available (" $5 " used)"}')"
    log_debug "  CPU: $(nproc) cores"
    log_debug "  Load: $(uptime | awk -F'load average:' '{print $2}')"
    
    log_info "Network Information:"
    log_debug "  Hostname: $(hostname)"
    log_debug "  IP Address: $(hostname -I | awk '{print $1}')"
    
    log_info "User Context:"
    log_debug "  Current User: $(whoami)"
    log_debug "  User ID: $(id)"
    log_debug "  Groups: $(groups)"
    log_debug "  Home Directory: $HOME"
    log_debug "  Shell: $SHELL"
    
    log_info "Python Environment:"
    log_debug "  Python3 Version: $(python3 --version 2>/dev/null || echo 'Not installed')"
    log_debug "  Pip3 Version: $(pip3 --version 2>/dev/null || echo 'Not installed')"
    log_debug "  Python3 Path: $(which python3 2>/dev/null || echo 'Not found')"
    
    log_info "System Services:"
    log_debug "  Systemd Version: $(systemctl --version | head -1)"
    log_debug "  Active Services: $(systemctl list-units --type=service --state=active | wc -l)"
    
    log_success "System information gathered successfully"
}

# Pre-deployment validation
pre_deployment_validation() {
    log_step "PRE-DEPLOYMENT VALIDATION"
    
    # Check if running with appropriate privileges
    if [ "$EUID" -ne 0 ]; then
        log_error "This script must be run with sudo privileges"
        log_error "Usage: sudo $0"
        exit 1
    fi
    
    log_success "Running with sudo privileges"
    
    # Check if SUDO_USER is set
    if [ -z "${SUDO_USER:-}" ]; then
        log_warning "SUDO_USER not set - script may be running as root directly"
        log_warning "This is not recommended for security reasons"
    else
        log_info "Original user: $SUDO_USER"
    fi
    
    # Check available disk space (need at least 1GB)
    available_space=$(df / | tail -1 | awk '{print $4}')
    if [ "$available_space" -lt 1048576 ]; then  # 1GB in KB
        log_error "Insufficient disk space. Need at least 1GB available"
        log_error "Available: $(df -h / | tail -1 | awk '{print $4}')"
        exit 1
    fi
    log_success "Sufficient disk space available: $(df -h / | tail -1 | awk '{print $4}')"
    
    # Check internet connectivity
    if ! ping -c 1 ******* >/dev/null 2>&1; then
        log_error "No internet connectivity - cannot download packages"
        exit 1
    fi
    log_success "Internet connectivity verified"
    
    # Check if ports are available
    local required_ports=(8011 8012 8013 8021)
    for port in "${required_ports[@]}"; do
        if ss -tuln | grep -q ":$port "; then
            log_warning "Port $port is already in use"
            log_debug "  Process using port $port: $(sudo lsof -i :$port 2>/dev/null | tail -1 || echo 'Unknown')"
        else
            log_success "Port $port is available"
        fi
    done
    
    log_success "Pre-deployment validation completed"
}

# User management
setup_user_environment() {
    log_step "SETTING UP USER ENVIRONMENT"
    
    # Create neuro user if it doesn't exist
    if ! id neuro >/dev/null 2>&1; then
        log_info "Creating neuro user..."
        useradd -m -s /bin/bash neuro
        log_success "Neuro user created"
    else
        log_info "Neuro user already exists"
    fi
    
    # Add neuro to sudo group (for service management)
    if ! groups neuro | grep -q sudo; then
        log_info "Adding neuro user to sudo group..."
        usermod -aG sudo neuro
        log_success "Neuro user added to sudo group"
    else
        log_info "Neuro user already in sudo group"
    fi
    
    # Set up neuro user environment
    log_info "Setting up neuro user environment..."
    sudo -u neuro bash -c 'echo "export PATH=/usr/local/bin:$PATH" >> ~/.bashrc'
    
    log_info "Neuro user information:"
    log_debug "  User ID: $(id neuro)"
    log_debug "  Home Directory: $(eval echo ~neuro)"
    log_debug "  Shell: $(getent passwd neuro | cut -d: -f7)"
    log_debug "  Groups: $(groups neuro)"
    
    log_success "User environment setup completed"
}

# Package installation with detailed logging
install_system_packages() {
    log_step "INSTALLING SYSTEM PACKAGES"
    
    log_info "Updating package lists..."
    if apt update 2>&1 | tee -a "$MAIN_LOG"; then
        log_success "Package lists updated successfully"
    else
        log_error "Failed to update package lists"
        exit 1
    fi
    
    # Core packages needed for basic functionality
    local core_packages=(
        "python3"
        "python3-pip" 
        "curl"
        "wget"
        "net-tools"
        "systemd"
        "sudo"
    )
    
    log_info "Installing core packages: ${core_packages[*]}"
    for package in "${core_packages[@]}"; do
        log_info "Installing $package..."
        if apt install -y "$package" 2>&1 | tee -a "$MAIN_LOG"; then
            log_success "$package installed successfully"
            # Verify installation
            if dpkg -l | grep -q "^ii  $package "; then
                local version=$(dpkg -l | grep "^ii  $package " | awk '{print $3}')
                log_debug "  $package version: $version"
            fi
        else
            log_error "Failed to install $package"
            exit 1
        fi
    done
    
    # Install Python packages
    log_info "Installing Python packages..."
    local python_packages=("psutil")
    for package in "${python_packages[@]}"; do
        log_info "Installing Python package: $package"
        if pip3 install "$package" 2>&1 | tee -a "$MAIN_LOG"; then
            log_success "Python package $package installed successfully"
            # Verify installation
            local version=$(pip3 show "$package" 2>/dev/null | grep Version | awk '{print $2}' || echo "unknown")
            log_debug "  $package version: $version"
        else
            log_error "Failed to install Python package: $package"
            exit 1
        fi
    done
    
    log_success "All system packages installed successfully"
}

# Directory structure setup
setup_directory_structure() {
    log_step "SETTING UP DIRECTORY STRUCTURE"
    
    local directories=(
        "/opt/neuralmeter"
        "/opt/neuralmeter/bin"
        "/opt/neuralmeter/logs"
        "/opt/neuralmeter/config"
        "/var/log/neuralmeter"
        "/etc/neuralmeter"
    )
    
    for dir in "${directories[@]}"; do
        log_info "Creating directory: $dir"
        if mkdir -p "$dir"; then
            log_success "Directory created: $dir"
            log_debug "  Permissions: $(ls -ld "$dir" | awk '{print $1}')"
        else
            log_error "Failed to create directory: $dir"
            exit 1
        fi
    done
    
    # Set ownership to neuro user
    log_info "Setting ownership of /opt/neuralmeter to neuro user..."
    if chown -R neuro:neuro /opt/neuralmeter; then
        log_success "Ownership set successfully"
        log_debug "  Owner: $(ls -ld /opt/neuralmeter | awk '{print $3":"$4}')"
    else
        log_error "Failed to set ownership"
        exit 1
    fi
    
    # Set appropriate permissions
    log_info "Setting directory permissions..."
    chmod 755 /opt/neuralmeter
    chmod 755 /opt/neuralmeter/bin
    chmod 755 /opt/neuralmeter/logs
    chmod 755 /opt/neuralmeter/config
    
    log_success "Directory structure setup completed"
}

# Create API server with extensive logging
create_api_server() {
    log_step "CREATING API SERVER"
    
    local api_server_path="/opt/neuralmeter/bin/simple-api-server.py"
    
    log_info "Creating API server script at: $api_server_path"
    
    cat > "$api_server_path" << 'EOF'
#!/usr/bin/env python3
"""
NeuralMeter Simple API Server
Created for System B load testing
Provides basic HTTP endpoints for API testing
"""

import json
import os
import sys
import time
import signal
import logging
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('/opt/neuralmeter/logs/api-server.log')
    ]
)
logger = logging.getLogger('neuralmeter-api')

class APIHandler(BaseHTTPRequestHandler):
    def log_message(self, format, *args):
        """Override to use our logger"""
        logger.info(f"{self.address_string()} - {format % args}")
    
    def do_GET(self):
        """Handle GET requests"""
        try:
            parsed_path = urlparse(self.path)
            path = parsed_path.path
            query_params = parse_qs(parsed_path.query)
            
            logger.info(f"GET request: {path} from {self.client_address[0]}")
            
            if path == '/health':
                self._handle_health()
            elif path.startswith('/api/'):
                self._handle_api(path, query_params)
            elif path == '/':
                self._handle_root()
            else:
                self._handle_not_found()
                
        except Exception as e:
            logger.error(f"Error handling GET request: {e}")
            self._handle_error(str(e))
    
    def do_POST(self):
        """Handle POST requests"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length).decode('utf-8')
            
            logger.info(f"POST request: {self.path} from {self.client_address[0]}")
            logger.debug(f"POST data: {post_data}")
            
            if self.path.startswith('/api/'):
                self._handle_api_post(self.path, post_data)
            else:
                self._handle_not_found()
                
        except Exception as e:
            logger.error(f"Error handling POST request: {e}")
            self._handle_error(str(e))
    
    def _handle_health(self):
        """Health check endpoint"""
        response = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "port": os.environ.get('PORT', '8011'),
            "service": "neuralmeter-api",
            "version": "1.0.0",
            "uptime": time.time() - start_time
        }
        self._send_json_response(200, response)
    
    def _handle_api(self, path, query_params):
        """Handle API endpoints"""
        if path == '/api/data':
            response = {
                "data": f"API response from port {os.environ.get('PORT', '8011')}",
                "timestamp": int(time.time()),
                "endpoint": path,
                "query_params": query_params,
                "server_info": {
                    "port": os.environ.get('PORT', '8011'),
                    "pid": os.getpid(),
                    "uptime": time.time() - start_time
                }
            }
            self._send_json_response(200, response)
        elif path == '/api/status':
            response = {
                "status": "operational",
                "port": os.environ.get('PORT', '8011'),
                "timestamp": datetime.now().isoformat(),
                "requests_handled": getattr(self.server, 'request_count', 0)
            }
            self._send_json_response(200, response)
        else:
            self._handle_not_found()
    
    def _handle_api_post(self, path, data):
        """Handle POST API requests"""
        try:
            json_data = json.loads(data) if data else {}
        except json.JSONDecodeError:
            json_data = {"raw_data": data}
        
        response = {
            "message": "POST request processed",
            "endpoint": path,
            "received_data": json_data,
            "timestamp": datetime.now().isoformat(),
            "port": os.environ.get('PORT', '8011')
        }
        self._send_json_response(200, response)
    
    def _handle_root(self):
        """Handle root endpoint"""
        html_response = f"""
        <html>
        <head><title>NeuralMeter API Server</title></head>
        <body>
            <h1>NeuralMeter API Server</h1>
            <p>Port: {os.environ.get('PORT', '8011')}</p>
            <p>Status: Operational</p>
            <p>Timestamp: {datetime.now().isoformat()}</p>
            <h2>Available Endpoints:</h2>
            <ul>
                <li><a href="/health">/health</a> - Health check</li>
                <li><a href="/api/data">/api/data</a> - API data endpoint</li>
                <li><a href="/api/status">/api/status</a> - API status</li>
            </ul>
        </body>
        </html>
        """
        self._send_html_response(200, html_response)
    
    def _handle_not_found(self):
        """Handle 404 errors"""
        response = {
            "error": "Not Found",
            "message": f"Endpoint {self.path} not found",
            "timestamp": datetime.now().isoformat(),
            "port": os.environ.get('PORT', '8011')
        }
        self._send_json_response(404, response)
    
    def _handle_error(self, error_message):
        """Handle server errors"""
        response = {
            "error": "Internal Server Error",
            "message": error_message,
            "timestamp": datetime.now().isoformat(),
            "port": os.environ.get('PORT', '8011')
        }
        self._send_json_response(500, response)
    
    def _send_json_response(self, status_code, data):
        """Send JSON response"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2).encode('utf-8'))
    
    def _send_html_response(self, status_code, html):
        """Send HTML response"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'text/html')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

class APIServer:
    def __init__(self, port):
        self.port = port
        self.server = None
        self.running = False
        
    def start(self):
        """Start the API server"""
        try:
            logger.info(f"Starting API server on port {self.port}")
            self.server = HTTPServer(('', self.port), APIHandler)
            self.server.request_count = 0
            self.running = True
            
            logger.info(f"API server listening on port {self.port}")
            logger.info(f"Process ID: {os.getpid()}")
            logger.info(f"Server ready to handle requests")
            
            self.server.serve_forever()
            
        except OSError as e:
            if e.errno == 98:  # Address already in use
                logger.error(f"Port {self.port} is already in use")
                sys.exit(1)
            else:
                logger.error(f"Failed to start server: {e}")
                sys.exit(1)
        except KeyboardInterrupt:
            logger.info("Received interrupt signal")
            self.stop()
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            sys.exit(1)
    
    def stop(self):
        """Stop the API server"""
        if self.server and self.running:
            logger.info("Shutting down API server...")
            self.server.shutdown()
            self.server.server_close()
            self.running = False
            logger.info("API server stopped")

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"Received signal {signum}")
    if 'api_server' in globals():
        api_server.stop()
    sys.exit(0)

# Global start time for uptime calculation
start_time = time.time()

def main():
    """Main function"""
    # Set up signal handlers
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    # Get port from environment
    port = int(os.environ.get('PORT', 8011))
    
    logger.info("="*60)
    logger.info("NeuralMeter API Server Starting")
    logger.info("="*60)
    logger.info(f"Port: {port}")
    logger.info(f"PID: {os.getpid()}")
    logger.info(f"Python Version: {sys.version}")
    logger.info(f"Start Time: {datetime.now().isoformat()}")
    logger.info("="*60)
    
    # Create and start server
    global api_server
    api_server = APIServer(port)
    api_server.start()

if __name__ == '__main__':
    main()
EOF

    # Set permissions and ownership
    chmod +x "$api_server_path"
    chown neuro:neuro "$api_server_path"
    
    log_success "API server script created successfully"
    log_debug "  Path: $api_server_path"
    log_debug "  Size: $(ls -lh "$api_server_path" | awk '{print $5}')"
    log_debug "  Permissions: $(ls -l "$api_server_path" | awk '{print $1}')"
    log_debug "  Owner: $(ls -l "$api_server_path" | awk '{print $3":"$4}')"
    
    # Test syntax
    log_info "Testing API server syntax..."
    if sudo -u neuro python3 -m py_compile "$api_server_path"; then
        log_success "API server syntax is valid"
    else
        log_error "API server syntax error"
        exit 1
    fi
}

# Create health server
create_health_server() {
    log_step "CREATING HEALTH SERVER"
    
    local health_server_path="/opt/neuralmeter/bin/health-server.py"
    
    log_info "Creating health server script at: $health_server_path"
    
    cat > "$health_server_path" << 'EOF'
#!/usr/bin/env python3
"""
NeuralMeter Health Monitoring Server
Provides comprehensive system health monitoring
"""

import json
import os
import sys
import time
import signal
import logging
import psutil
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('/opt/neuralmeter/logs/health-server.log')
    ]
)
logger = logging.getLogger('neuralmeter-health')

class HealthHandler(BaseHTTPRequestHandler):
    def log_message(self, format, *args):
        """Override to use our logger"""
        logger.info(f"{self.address_string()} - {format % args}")
    
    def do_GET(self):
        """Handle GET requests"""
        try:
            logger.info(f"Health check request: {self.path} from {self.client_address[0]}")
            
            if self.path == '/health':
                self._handle_basic_health()
            elif self.path == '/health/detailed':
                self._handle_detailed_health()
            elif self.path == '/health/system':
                self._handle_system_health()
            elif self.path == '/health/services':
                self._handle_services_health()
            else:
                self._handle_basic_health()
                
        except Exception as e:
            logger.error(f"Error handling health request: {e}")
            self._handle_error(str(e))
    
    def _handle_basic_health(self):
        """Basic health check"""
        response = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "service": "neuralmeter-health",
            "version": "1.0.0",
            "uptime": time.time() - start_time
        }
        self._send_json_response(200, response)
    
    def _handle_detailed_health(self):
        """Detailed health information"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            response = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "system": {
                    "cpu_percent": cpu_percent,
                    "memory": {
                        "total": memory.total,
                        "available": memory.available,
                        "percent": memory.percent,
                        "used": memory.used
                    },
                    "disk": {
                        "total": disk.total,
                        "used": disk.used,
                        "free": disk.free,
                        "percent": (disk.used / disk.total) * 100
                    },
                    "load_average": os.getloadavg(),
                    "uptime": time.time() - start_time
                },
                "checks": {
                    "cpu_ok": cpu_percent < 90,
                    "memory_ok": memory.percent < 90,
                    "disk_ok": (disk.used / disk.total) * 100 < 90
                }
            }
            self._send_json_response(200, response)
        except Exception as e:
            logger.error(f"Error getting detailed health: {e}")
            self._handle_error(str(e))
    
    def _handle_system_health(self):
        """System-level health checks"""
        try:
            response = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "checks": {
                    "filesystem_writable": self._check_filesystem_writable(),
                    "network_connectivity": self._check_network_connectivity(),
                    "required_ports": self._check_required_ports(),
                    "system_load": self._check_system_load()
                }
            }
            self._send_json_response(200, response)
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            self._handle_error(str(e))
    
    def _handle_services_health(self):
        """Check health of related services"""
        try:
            services = {
                "neuralmeter-api-8011": self._check_service_health("neuralmeter-api-8011", 8011),
                "neuralmeter-api-8012": self._check_service_health("neuralmeter-api-8012", 8012),
                "neuralmeter-api-8013": self._check_service_health("neuralmeter-api-8013", 8013)
            }
            
            response = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "services": services
            }
            self._send_json_response(200, response)
        except Exception as e:
            logger.error(f"Error getting services health: {e}")
            self._handle_error(str(e))
    
    def _check_filesystem_writable(self):
        """Check if filesystem is writable"""
        try:
            test_file = "/tmp/neuralmeter_health_test"
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            return True
        except:
            return False
    
    def _check_network_connectivity(self):
        """Check basic network connectivity"""
        try:
            import socket
            socket.create_connection(("*******", 53), timeout=3)
            return True
        except:
            return False
    
    def _check_required_ports(self):
        """Check if required ports are available"""
        required_ports = [8011, 8012, 8013, 8021]
        port_status = {}
        
        for port in required_ports:
            try:
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                result = sock.connect_ex(('localhost', port))
                port_status[str(port)] = result == 0  # True if port is open
                sock.close()
            except:
                port_status[str(port)] = False
        
        return port_status
    
    def _check_system_load(self):
        """Check system load"""
        try:
            load_avg = os.getloadavg()
            cpu_count = psutil.cpu_count()
            return {
                "load_1min": load_avg[0],
                "load_5min": load_avg[1],
                "load_15min": load_avg[2],
                "cpu_count": cpu_count,
                "load_ok": load_avg[0] < cpu_count * 2  # Load should be less than 2x CPU count
            }
        except:
            return {"load_ok": False}
    
    def _check_service_health(self, service_name, port):
        """Check health of a specific service"""
        try:
            import subprocess
            import socket
            
            # Check if systemd service is active
            result = subprocess.run(['systemctl', 'is-active', service_name], 
                                  capture_output=True, text=True)
            service_active = result.stdout.strip() == 'active'
            
            # Check if port is responding
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            port_open = sock.connect_ex(('localhost', port)) == 0
            sock.close()
            
            return {
                "service_active": service_active,
                "port_responding": port_open,
                "healthy": service_active and port_open
            }
        except:
            return {
                "service_active": False,
                "port_responding": False,
                "healthy": False
            }
    
    def _handle_error(self, error_message):
        """Handle server errors"""
        response = {
            "status": "error",
            "error": error_message,
            "timestamp": datetime.now().isoformat()
        }
        self._send_json_response(500, response)
    
    def _send_json_response(self, status_code, data):
        """Send JSON response"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2).encode('utf-8'))

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"Received signal {signum}")
    sys.exit(0)

# Global start time
start_time = time.time()

def main():
    """Main function"""
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    port = 8021
    
    logger.info("="*60)
    logger.info("NeuralMeter Health Server Starting")
    logger.info("="*60)
    logger.info(f"Port: {port}")
    logger.info(f"PID: {os.getpid()}")
    logger.info(f"Start Time: {datetime.now().isoformat()}")
    logger.info("="*60)
    
    try:
        server = HTTPServer(('', port), HealthHandler)
        logger.info(f"Health server listening on port {port}")
        server.serve_forever()
    except OSError as e:
        if e.errno == 98:
            logger.error(f"Port {port} is already in use")
            sys.exit(1)
        else:
            logger.error(f"Failed to start server: {e}")
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
        sys.exit(0)

if __name__ == '__main__':
    main()
EOF

    # Set permissions and ownership
    chmod +x "$health_server_path"
    chown neuro:neuro "$health_server_path"
    
    log_success "Health server script created successfully"
    log_debug "  Path: $health_server_path"
    log_debug "  Size: $(ls -lh "$health_server_path" | awk '{print $5}')"
    
    # Test syntax
    log_info "Testing health server syntax..."
    if sudo -u neuro python3 -m py_compile "$health_server_path"; then
        log_success "Health server syntax is valid"
    else
        log_error "Health server syntax error"
        exit 1
    fi
}

# Create systemd services with comprehensive configuration
create_systemd_services() {
    log_step "CREATING SYSTEMD SERVICES"
    
    # Create API services
    for port in 8011 8012 8013; do
        log_info "Creating systemd service for API server on port $port"
        
        local service_file="/etc/systemd/system/neuralmeter-api-$port.service"
        
        cat > "$service_file" << EOF
[Unit]
Description=NeuralMeter API Server (Port $port)
Documentation=https://github.com/neuralmeter/system-b
After=network.target network-online.target
Wants=network-online.target
StartLimitIntervalSec=30
StartLimitBurst=3

[Service]
Type=simple
User=neuro
Group=neuro
Environment=PORT=$port
Environment=PYTHONUNBUFFERED=1
Environment=PYTHONPATH=/opt/neuralmeter/bin
ExecStart=/usr/bin/python3 /opt/neuralmeter/bin/simple-api-server.py
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=5
TimeoutStartSec=30
TimeoutStopSec=30
KillMode=mixed
KillSignal=SIGTERM

# Working directory
WorkingDirectory=/opt/neuralmeter

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=neuralmeter-api-$port

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/neuralmeter/logs /tmp
PrivateTmp=true

# Resource limits
LimitNOFILE=10000
LimitNPROC=100
MemoryMax=256M

[Install]
WantedBy=multi-user.target
EOF
        
        log_success "Created systemd service file: $service_file"
        log_debug "  Service: neuralmeter-api-$port"
        log_debug "  Port: $port"
        log_debug "  User: neuro"
    done
    
    # Create health service
    log_info "Creating systemd service for health server"
    
    local health_service_file="/etc/systemd/system/neuralmeter-health.service"
    
    cat > "$health_service_file" << 'EOF'
[Unit]
Description=NeuralMeter Health Monitoring Server
Documentation=https://github.com/neuralmeter/system-b
After=network.target network-online.target
Wants=network-online.target
StartLimitIntervalSec=30
StartLimitBurst=3

[Service]
Type=simple
User=neuro
Group=neuro
Environment=PYTHONUNBUFFERED=1
Environment=PYTHONPATH=/opt/neuralmeter/bin
ExecStart=/usr/bin/python3 /opt/neuralmeter/bin/health-server.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
TimeoutStartSec=30
TimeoutStopSec=30
KillMode=mixed
KillSignal=SIGTERM

# Working directory
WorkingDirectory=/opt/neuralmeter

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=neuralmeter-health

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/neuralmeter/logs /tmp
PrivateTmp=true

# Resource limits
LimitNOFILE=10000
LimitNPROC=100
MemoryMax=256M

[Install]
WantedBy=multi-user.target
EOF
    
    log_success "Created systemd service file: $health_service_file"
    
    # Reload systemd
    log_info "Reloading systemd daemon..."
    if systemctl daemon-reload; then
        log_success "Systemd daemon reloaded successfully"
    else
        log_error "Failed to reload systemd daemon"
        exit 1
    fi
    
    log_success "All systemd services created successfully"
}

# Start and enable services with comprehensive monitoring
start_services() {
    log_step "STARTING AND ENABLING SERVICES"
    
    local services=("neuralmeter-api-8011" "neuralmeter-api-8012" "neuralmeter-api-8013" "neuralmeter-health")
    local failed_services=()
    
    for service in "${services[@]}"; do
        log_info "Enabling and starting service: $service"
        
        # Enable service
        if systemctl enable "$service"; then
            log_success "Service $service enabled successfully"
        else
            log_error "Failed to enable service: $service"
            failed_services+=("$service")
            continue
        fi
        
        # Start service
        if systemctl start "$service"; then
            log_success "Service $service started successfully"
        else
            log_error "Failed to start service: $service"
            failed_services+=("$service")
            continue
        fi
        
        # Wait for service to be ready
        log_info "Waiting for service $service to be ready..."
        sleep 3
        
        # Check service status
        if systemctl is-active --quiet "$service"; then
            log_success "Service $service is active and running"
            
            # Get detailed status
            local status_output=$(systemctl status "$service" --no-pager -l)
            log_debug "Service $service status:"
            echo "$status_output" | while read line; do
                log_debug "  $line"
            done
            
        else
            log_error "Service $service is not active"
            failed_services+=("$service")
            
            # Show service logs for debugging
            log_error "Service $service logs:"
            journalctl -u "$service" --no-pager -n 20 | while read line; do
                log_debug "  $line"
            done
        fi
    done
    
    if [ ${#failed_services[@]} -eq 0 ]; then
        log_success "All services started successfully"
    else
        log_error "Failed to start services: ${failed_services[*]}"
        exit 1
    fi
}

# Comprehensive service testing
test_services() {
    log_step "TESTING SERVICES"
    
    local test_results=()
    
    # Wait for services to be fully ready
    log_info "Waiting for services to be fully ready..."
    sleep 5
    
    # Test API services
    for port in 8011 8012 8013; do
        log_info "Testing API service on port $port..."
        
        # Test port listening
        if ss -tuln | grep -q ":$port "; then
            log_success "Port $port is listening"
        else
            log_error "Port $port is not listening"
            test_results+=("API-$port-port-not-listening")
            continue
        fi
        
        # Test health endpoint
        log_info "Testing health endpoint for port $port..."
        local health_response
        if health_response=$(curl -s -f --connect-timeout 10 --max-time 30 "http://localhost:$port/health"); then
            log_success "Health endpoint for port $port is responding"
            log_debug "Health response: $health_response"
            
            # Validate JSON response
            if echo "$health_response" | python3 -m json.tool >/dev/null 2>&1; then
                log_success "Health response for port $port is valid JSON"
            else
                log_warning "Health response for port $port is not valid JSON"
            fi
        else
            log_error "Health endpoint for port $port is not responding"
            test_results+=("API-$port-health-not-responding")
        fi
        
        # Test API data endpoint
        log_info "Testing API data endpoint for port $port..."
        local api_response
        if api_response=$(curl -s -f --connect-timeout 10 --max-time 30 "http://localhost:$port/api/data"); then
            log_success "API data endpoint for port $port is responding"
            log_debug "API response: $api_response"
        else
            log_error "API data endpoint for port $port is not responding"
            test_results+=("API-$port-data-not-responding")
        fi
    done
    
    # Test health service
    log_info "Testing health service on port 8021..."
    
    # Test port listening
    if ss -tuln | grep -q ":8021 "; then
        log_success "Port 8021 is listening"
    else
        log_error "Port 8021 is not listening"
        test_results+=("Health-port-not-listening")
    fi
    
    # Test health endpoints
    local health_endpoints=("/health" "/health/detailed" "/health/system" "/health/services")
    for endpoint in "${health_endpoints[@]}"; do
        log_info "Testing health service endpoint: $endpoint"
        local response
        if response=$(curl -s -f --connect-timeout 10 --max-time 30 "http://localhost:8021$endpoint"); then
            log_success "Health service endpoint $endpoint is responding"
            log_debug "Response: $response"
        else
            log_error "Health service endpoint $endpoint is not responding"
            test_results+=("Health$endpoint-not-responding")
        fi
    done
    
    # Summary
    if [ ${#test_results[@]} -eq 0 ]; then
        log_success "All service tests passed successfully"
        return 0
    else
        log_error "Some service tests failed:"
        for result in "${test_results[@]}"; do
            log_error "  - $result"
        done
        return 1
    fi
}

# Final validation and summary
final_validation() {
    log_step "FINAL VALIDATION AND SUMMARY"
    
    log_info "Performing final system validation..."
    
    # Check all services are running
    local services=("neuralmeter-api-8011" "neuralmeter-api-8012" "neuralmeter-api-8013" "neuralmeter-health")
    local all_services_ok=true
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service"; then
            log_success "Service $service is running"
        else
            log_error "Service $service is not running"
            all_services_ok=false
        fi
    done
    
    # Check all ports are listening
    local ports=(8011 8012 8013 8021)
    local all_ports_ok=true
    
    for port in "${ports[@]}"; do
        if ss -tuln | grep -q ":$port "; then
            log_success "Port $port is listening"
        else
            log_error "Port $port is not listening"
            all_ports_ok=false
        fi
    done
    
    # Final summary
    echo ""
    echo "=============================================================================="
    echo "DEPLOYMENT SUMMARY"
    echo "=============================================================================="
    echo "Deployment completed at: $(date)"
    echo "Total deployment time: $(($(date +%s) - deployment_start_time)) seconds"
    echo ""
    
    if $all_services_ok && $all_ports_ok; then
        log_success "🎉 DEPLOYMENT SUCCESSFUL!"
        echo ""
        echo "System B is now fully operational and ready for load testing."
        echo ""
        echo "Available Services:"
        echo "• API Service 1: http://localhost:8011/health"
        echo "• API Service 2: http://localhost:8012/health"
        echo "• API Service 3: http://localhost:8013/health"
        echo "• Health Service: http://localhost:8021/health"
        echo ""
        echo "API Endpoints:"
        echo "• Data endpoints: http://localhost:801[1-3]/api/data"
        echo "• Status endpoints: http://localhost:801[1-3]/api/status"
        echo ""
        echo "Health Monitoring:"
        echo "• Basic health: http://localhost:8021/health"
        echo "• Detailed health: http://localhost:8021/health/detailed"
        echo "• System health: http://localhost:8021/health/system"
        echo "• Services health: http://localhost:8021/health/services"
        echo ""
        echo "Service Management:"
        echo "• Check status: sudo systemctl status neuralmeter-api-8011"
        echo "• View logs: sudo journalctl -u neuralmeter-api-8011 -f"
        echo "• Restart service: sudo systemctl restart neuralmeter-api-8011"
        echo ""
        echo "Log Files:"
        echo "• Deployment log: $MAIN_LOG"
        echo "• Error log: $ERROR_LOG"
        echo "• Service logs: /opt/neuralmeter/logs/"
        echo ""
        return 0
    else
        log_error "❌ DEPLOYMENT FAILED"
        echo ""
        echo "Some services or ports are not working correctly."
        echo "Check the logs for detailed error information:"
        echo "• Main log: $MAIN_LOG"
        echo "• Error log: $ERROR_LOG"
        echo ""
        echo "Service debugging commands:"
        for service in "${services[@]}"; do
            echo "• sudo systemctl status $service"
            echo "• sudo journalctl -u $service --no-pager"
        done
        echo ""
        return 1
    fi
}

# Cleanup function
cleanup() {
    log_info "Performing cleanup..."
    # Add any cleanup tasks here if needed
}

# Main execution
main() {
    local deployment_start_time=$(date +%s)
    
    # Set trap for cleanup
    trap cleanup EXIT
    
    # Execute deployment steps
    gather_system_info
    pre_deployment_validation
    setup_user_environment
    install_system_packages
    setup_directory_structure
    create_api_server
    create_health_server
    create_systemd_services
    start_services
    test_services
    
    # Final validation
    if final_validation; then
        log_success "System B deployment completed successfully"
        exit 0
    else
        log_error "System B deployment failed"
        exit 1
    fi
}

# Record deployment start time
deployment_start_time=$(date +%s)

# Run main function
main "$@"