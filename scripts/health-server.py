#!/usr/bin/env python3
"""
health-server.py - Python Health Check Service for System B
Port 8021 - Comprehensive health monitoring for NeuralMeter test environment
"""

import json
import time
import psutil
import socket
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import os
import sys

class HealthHandler(BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.start_time = getattr(HealthHandler, 'start_time', time.time())
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        query_params = parse_qs(parsed_path.query)
        
        if path == '/health':
            self.handle_basic_health()
        elif path == '/health/detailed':
            self.handle_detailed_health()
        elif path == '/health/system':
            self.handle_system_health()
        elif path == '/health/services':
            self.handle_services_health()
        else:
            self.send_error(404, "Health endpoint not found")
    
    def handle_basic_health(self):
        """Basic health check endpoint"""
        health_data = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "service": "neuralmeter-health-check",
            "port": 8021,
            "uptime_seconds": int(time.time() - self.start_time)
        }
        
        self.send_json_response(health_data)
    
    def handle_detailed_health(self):
        """Detailed health check with system metrics"""
        try:
            # CPU and Memory info
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Network info
            network_stats = psutil.net_io_counters()
            
            health_data = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "service": "neuralmeter-health-check",
                "port": 8021,
                "uptime_seconds": int(time.time() - self.start_time),
                "system": {
                    "cpu": {
                        "usage_percent": cpu_percent,
                        "count": psutil.cpu_count(),
                        "count_logical": psutil.cpu_count(logical=True)
                    },
                    "memory": {
                        "total_gb": round(memory.total / (1024**3), 2),
                        "available_gb": round(memory.available / (1024**3), 2),
                        "used_percent": memory.percent
                    },
                    "disk": {
                        "total_gb": round(disk.total / (1024**3), 2),
                        "free_gb": round(disk.free / (1024**3), 2),
                        "used_percent": round((disk.used / disk.total) * 100, 2)
                    },
                    "network": {
                        "bytes_sent": network_stats.bytes_sent,
                        "bytes_recv": network_stats.bytes_recv,
                        "packets_sent": network_stats.packets_sent,
                        "packets_recv": network_stats.packets_recv
                    }
                }
            }
            
            self.send_json_response(health_data)
            
        except Exception as e:
            error_data = {
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
            self.send_json_response(error_data, status_code=500)
    
    def handle_system_health(self):
        """System-level health checks"""
        try:
            # Check system load
            load_avg = os.getloadavg()
            
            # Check disk space
            disk = psutil.disk_usage('/')
            disk_health = "healthy" if disk.percent < 90 else "warning" if disk.percent < 95 else "critical"
            
            # Check memory
            memory = psutil.virtual_memory()
            memory_health = "healthy" if memory.percent < 80 else "warning" if memory.percent < 90 else "critical"
            
            # Overall health status
            overall_status = "healthy"
            if disk_health == "critical" or memory_health == "critical":
                overall_status = "critical"
            elif disk_health == "warning" or memory_health == "warning":
                overall_status = "warning"
            
            health_data = {
                "status": overall_status,
                "timestamp": datetime.now().isoformat(),
                "checks": {
                    "load_average": {
                        "1min": load_avg[0],
                        "5min": load_avg[1],
                        "15min": load_avg[2]
                    },
                    "disk_space": {
                        "status": disk_health,
                        "used_percent": round(disk.percent, 2),
                        "free_gb": round(disk.free / (1024**3), 2)
                    },
                    "memory": {
                        "status": memory_health,
                        "used_percent": memory.percent,
                        "available_gb": round(memory.available / (1024**3), 2)
                    }
                }
            }
            
            status_code = 200 if overall_status == "healthy" else 503
            self.send_json_response(health_data, status_code=status_code)
            
        except Exception as e:
            error_data = {
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
            self.send_json_response(error_data, status_code=500)
    
    def handle_services_health(self):
        """Check health of other NeuralMeter services"""
        services_to_check = [
            ("nginx", 80),
            ("api-8011", 8011),
            ("api-8012", 8012),
            ("api-8013", 8013),
            ("websocket-8080", 8080),
            ("websocket-8081", 8081),
            ("haproxy-stats", 8404)
        ]
        
        service_statuses = {}
        overall_healthy = True
        
        for service_name, port in services_to_check:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex(('localhost', port))
                sock.close()
                
                if result == 0:
                    service_statuses[service_name] = {
                        "status": "healthy",
                        "port": port,
                        "reachable": True
                    }
                else:
                    service_statuses[service_name] = {
                        "status": "unhealthy",
                        "port": port,
                        "reachable": False
                    }
                    overall_healthy = False
                    
            except Exception as e:
                service_statuses[service_name] = {
                    "status": "error",
                    "port": port,
                    "error": str(e)
                }
                overall_healthy = False
        
        health_data = {
            "status": "healthy" if overall_healthy else "degraded",
            "timestamp": datetime.now().isoformat(),
            "services": service_statuses,
            "summary": {
                "total_services": len(services_to_check),
                "healthy_services": sum(1 for s in service_statuses.values() if s["status"] == "healthy"),
                "unhealthy_services": sum(1 for s in service_statuses.values() if s["status"] != "healthy")
            }
        }
        
        status_code = 200 if overall_healthy else 503
        self.send_json_response(health_data, status_code=status_code)
    
    def send_json_response(self, data, status_code=200):
        """Send JSON response with proper headers"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Cache-Control', 'no-cache')
        self.end_headers()
        
        json_data = json.dumps(data, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def log_message(self, format, *args):
        """Custom log format"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def main():
    # Set start time for uptime calculation
    HealthHandler.start_time = time.time()
    
    port = int(os.environ.get('PORT', 8021))
    
    try:
        server = HTTPServer(('0.0.0.0', port), HealthHandler)
        print(f"NeuralMeter Health Check Server starting on port {port}")
        print(f"Available endpoints:")
        print(f"  http://localhost:{port}/health - Basic health check")
        print(f"  http://localhost:{port}/health/detailed - Detailed system metrics")
        print(f"  http://localhost:{port}/health/system - System health status")
        print(f"  http://localhost:{port}/health/services - Service connectivity check")
        
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\nShutting down health check server...")
        server.shutdown()
        sys.exit(0)
    except Exception as e:
        print(f"Error starting health check server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()