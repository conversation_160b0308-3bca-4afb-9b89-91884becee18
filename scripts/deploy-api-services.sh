#!/bin/bash
# deploy-api-services.sh - Deploy simple Python-based API services for System B

set -euo pipefail

LOG_FILE="/var/log/neuralmeter-api-deploy.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

echo "=== NeuralMeter API Services Deployment Started $(date) ==="

NEURALMETER_DIR="/opt/neuralmeter"
BIN_DIR="$NEURALMETER_DIR/bin"

# Create directories
echo "Creating service directories..."
mkdir -p "$BIN_DIR" "$NEURALMETER_DIR/logs"

# Create simple Python API server
echo "Creating Python API server..."
cat > "$BIN_DIR/simple-api-server.py" << 'EOF'
#!/usr/bin/env python3
import json
import time
import os
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse
from datetime import datetime

class APIHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/health' or path == '/api/health':
            self.send_json_response({
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "port": os.environ.get('PORT', '8011'),
                "service": "simple-api-server"
            })
        elif path.startswith('/api/data'):
            self.send_json_response({
                "id": f"data-{int(time.time())}",
                "message": "Test data from System B",
                "timestamp": datetime.now().isoformat(),
                "port": os.environ.get('PORT', '8011')
            })
        elif path.startswith('/load/'):
            load_type = path.split('/')[-1]
            if load_type == 'fast':
                response_data = {"status": "ok", "type": "fast", "timestamp": int(time.time())}
            elif load_type == 'slow':
                time.sleep(0.1)  # Simulate slow response
                response_data = {"status": "ok", "type": "slow", "timestamp": int(time.time())}
            else:
                response_data = {"status": "ok", "type": "default", "timestamp": int(time.time())}
            self.send_json_response(response_data)
        else:
            self.send_json_response({
                "message": "Simple API Server",
                "port": os.environ.get('PORT', '8011'),
                "timestamp": int(time.time())
            })
    
    def do_POST(self):
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        self.send_json_response({
            "status": "received",
            "timestamp": datetime.now().isoformat(),
            "port": os.environ.get('PORT', '8011'),
            "received_bytes": len(post_data)
        }, status_code=201)
    
    def send_json_response(self, data, status_code=200):
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        json_data = json.dumps(data, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def log_message(self, format, *args):
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def main():
    port = int(os.environ.get('PORT', 8011))
    server = HTTPServer(('0.0.0.0', port), APIHandler)
    print(f"Simple API server starting on port {port}")
    server.serve_forever()

if __name__ == '__main__':
    main()
EOF

chmod +x "$BIN_DIR/simple-api-server.py"

# Set ownership to neuro user
chown -R neuro:neuro "$NEURALMETER_DIR"

echo "=== Creating systemd services ==="

# Create systemd service files for API servers
for port in 8011 8012 8013; do
    echo "Creating systemd service for API server on port $port..."
    cat > /etc/systemd/system/neuralmeter-api-$port.service << EOF
[Unit]
Description=NeuralMeter Simple API Server (Port $port)
After=network.target
Wants=network.target

[Service]
Type=simple
User=neuro
Group=neuro
Environment=PORT=$port
ExecStart=/usr/bin/python3 $BIN_DIR/simple-api-server.py
Restart=always
RestartSec=3
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true

# Resource limits
LimitNOFILE=10000
LimitNPROC=100

[Install]
WantedBy=multi-user.target
EOF
done

# Reload systemd and enable services
echo "Enabling and starting API services..."
systemctl daemon-reload

for port in 8011 8012 8013; do
    echo "Starting neuralmeter-api-$port service..."
    systemctl enable neuralmeter-api-$port
    systemctl restart neuralmeter-api-$port
    
    # Wait a moment for service to start
    sleep 2
    
    # Check service status
    if systemctl is-active --quiet neuralmeter-api-$port; then
        echo "✅ neuralmeter-api-$port service is running"
    else
        echo "❌ neuralmeter-api-$port service failed to start"
        systemctl status neuralmeter-api-$port --no-pager
    fi
done

# Test API endpoints
echo "Testing API endpoints..."
sleep 5

for port in 8011 8012 8013; do
    echo "Testing API server on port $port..."
    if curl -s -f "http://localhost:$port/health" > /dev/null; then
        echo "✅ API server on port $port is responding"
    else
        echo "❌ API server on port $port is not responding"
    fi
done

echo "=== API Services Deployment Completed $(date) ==="
echo "Simple Python API services are running on ports 8011, 8012, 8013"
echo "Health check endpoints: http://localhost:801X/health"
echo "API endpoints: http://localhost:801X/api/data"