#!/bin/bash
# validate-system-b.sh - Comprehensive System B Validation Script

set -euo pipefail

# Force environment reset if running as different user
if [ "${SUDO_USER:-}" != "" ] && [ "$(whoami)" != "${SUDO_USER}" ]; then
    export HOME="/home/<USER>"
    export USER="$(whoami)"
    cd "$HOME" 2>/dev/null || cd /tmp
fi

LOG_FILE="/var/log/neuralmeter-validation.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

echo "=== NeuralMeter System B Validation Started $(date) ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test result tracking
declare -a FAILED_TEST_NAMES=()

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED_TESTS++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED_TESTS++))
    FAILED_TEST_NAMES+=("$1")
}

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    ((TOTAL_TESTS++))
    log_info "Running test: $test_name"
    
    # Run command and capture output for debugging
    local output
    if output=$(eval "$test_command" 2>&1); then
        log_success "$test_name"
        return 0
    else
        log_error "$test_name - Command failed: $test_command"
        log_error "Error output: $output"
        return 1
    fi
}

# Test functions
test_system_packages() {
    log_info "=== Testing System Packages ==="
    
    run_test "HAProxy installation" "which haproxy && haproxy -v"
    run_test "Nginx installation" "which nginx && nginx -v"
    run_test "Prometheus installation" "which prometheus && prometheus --version"
    run_test "Python3 installation" "which python3 && python3 --version"
    run_test "psutil Python package" "python3 -c 'import psutil; print(psutil.__version__)'"
}

test_ssl_certificates() {
    log_info "=== Testing SSL/TLS Certificates ==="
    
    run_test "Certificate directory exists" "[ -d /etc/neuralmeter/ssl ]"
    run_test "CA certificate exists" "[ -f /etc/neuralmeter/ssl/ca.crt ]"
    run_test "Server certificate exists" "[ -f /etc/neuralmeter/ssl/server.crt ]"
    run_test "Server private key exists" "[ -f /etc/neuralmeter/ssl/server.key ]"
    run_test "HAProxy combined certificate exists" "[ -f /etc/neuralmeter/ssl/server.pem ]"
    run_test "Certificate permissions" "[ \$(stat -c '%a' /etc/neuralmeter/ssl/server.key) = '600' ]"
    run_test "Certificate ownership" "[ \$(stat -c '%U' /etc/neuralmeter/ssl/server.crt) = 'neuro' ]"
}

test_haproxy_configuration() {
    log_info "=== Testing HAProxy Configuration ==="
    
    run_test "HAProxy configuration exists" "[ -f /etc/haproxy/haproxy.cfg ]"
    run_test "HAProxy configuration is valid" "haproxy -c -f /etc/haproxy/haproxy.cfg"
    run_test "HAProxy service is active" "systemctl is-active haproxy"
    run_test "HAProxy service is enabled" "systemctl is-enabled haproxy"
    run_test "HAProxy stats port listening" "ss -tuln | grep ':8404'"
}

test_nginx_configuration() {
    log_info "=== Testing Nginx Configuration ==="
    
    run_test "Nginx configuration is valid" "nginx -t"
    run_test "Nginx service is active" "systemctl is-active nginx"
    run_test "Nginx service is enabled" "systemctl is-enabled nginx"
    run_test "Nginx neuralmeter site enabled" "[ -L /etc/nginx/sites-enabled/neuralmeter ]"
    run_test "Nginx listening on port 8001" "ss -tuln | grep ':8001'"
    run_test "Nginx listening on port 8002" "ss -tuln | grep ':8002'"
    run_test "Nginx listening on port 8003" "ss -tuln | grep ':8003'"
}

test_api_services() {
    log_info "=== Testing Go API Services ==="
    
    for port in 8011 8012 8013; do
        run_test "API service script exists (port $port)" "[ -f /opt/neuralmeter/bin/simple-api-server.py ]"
        run_test "API service is active (port $port)" "systemctl is-active neuralmeter-api-$port"
        run_test "API service is enabled (port $port)" "systemctl is-enabled neuralmeter-api-$port"
        run_test "API service listening (port $port)" "ss -tuln | grep ':$port'"
    done
}

test_websocket_services() {
    log_info "=== Testing WebSocket Services ==="
    
    for port in 8080 8081; do
        run_test "WebSocket service script exists (port $port)" "[ -f /opt/neuralmeter/bin/simple-websocket-server.py ]"
        run_test "WebSocket service is active (port $port)" "systemctl is-active neuralmeter-ws-$port"
        run_test "WebSocket service is enabled (port $port)" "systemctl is-enabled neuralmeter-ws-$port"
        run_test "WebSocket service listening (port $port)" "ss -tuln | grep ':$port'"
    done
}

test_health_service() {
    log_info "=== Testing Python Health Service ==="
    
    run_test "Health service script exists" "[ -f /opt/neuralmeter/bin/health-server.py ]"
    run_test "Health service is active" "systemctl is-active neuralmeter-health"
    run_test "Health service is enabled" "systemctl is-enabled neuralmeter-health"
    run_test "Health service listening on port 8021" "ss -tuln | grep ':8021'"
}

test_prometheus_configuration() {
    log_info "=== Testing Prometheus Configuration ==="
    
    run_test "Prometheus configuration exists" "[ -f /etc/prometheus/prometheus.yml ]"
    run_test "Prometheus rules exist" "[ -f /etc/prometheus/neuralmeter_rules.yml ]"
    run_test "Prometheus service is active" "systemctl is-active prometheus"
    run_test "Prometheus service is enabled" "systemctl is-enabled prometheus"
    run_test "Prometheus listening on port 9090" "ss -tuln | grep ':9090'"
}

test_service_endpoints() {
    log_info "=== Testing Service Endpoints ==="
    
    # Basic HTTP endpoints
    run_test "Nginx root endpoint" "curl -s -f http://localhost:8001/ | grep -q 'NeuralMeter'"
    run_test "Nginx health endpoint" "curl -s -f http://localhost:8001/health | grep -q 'healthy'"
    
    # API service endpoints
    for port in 8011 8012 8013; do
        run_test "API health endpoint (port $port)" "curl -s -f http://localhost:$port/health | grep -q 'healthy'"
        run_test "API data endpoint (port $port)" "curl -s -f http://localhost:$port/api/data | grep -q 'data'"
    done
    
    # WebSocket health endpoints (on the same ports as WebSocket servers)
    for port in 8080 8081; do
        run_test "WebSocket health endpoint (port $port)" "curl -s -f http://localhost:$port/health | grep -q 'healthy'"
    done
    
    # Health service endpoints
    run_test "Health service basic endpoint" "curl -s -f http://localhost:8021/health | grep -q 'healthy'"
    run_test "Health service detailed endpoint" "curl -s -f http://localhost:8021/health/detailed | grep -q 'system'"
    run_test "Health service system endpoint" "curl -s -f http://localhost:8021/health/system | grep -q 'checks'"
    run_test "Health service services endpoint" "curl -s -f http://localhost:8021/health/services | grep -q 'services'"
    
    # HAProxy stats
    run_test "HAProxy stats endpoint" "curl -s -f http://localhost:8404/stats | grep -q 'HAProxy'"
    
    # Prometheus endpoints
    run_test "Prometheus health endpoint" "curl -s -f http://localhost:9090/-/healthy"
    run_test "Prometheus metrics endpoint" "curl -s -f http://localhost:9090/metrics | grep -q 'prometheus_'"
}

test_load_balancer_routing() {
    log_info "=== Testing Load Balancer Routing ==="
    
    # Test HAProxy routing (if configured to route to nginx)
    run_test "HAProxy HTTP routing" "curl -s -f http://localhost/ | grep -q 'NeuralMeter' || curl -s -f http://localhost:80/ | grep -q 'NeuralMeter'"
    
    # Test API routing through load balancer
    run_test "API routing through load balancer" "curl -s -f http://localhost/api/health | grep -q 'healthy' || echo 'Load balancer API routing not configured'"
}

test_file_permissions() {
    log_info "=== Testing File Permissions and Ownership ==="
    
    run_test "NeuralMeter directory ownership" "[ \$(stat -c '%U' /opt/neuralmeter) = 'neuro' ]"
    run_test "Binary directory permissions" "[ -d /opt/neuralmeter/bin ] && [ \$(stat -c '%a' /opt/neuralmeter/bin) = '755' ]"
    run_test "SSL certificate permissions" "[ \$(stat -c '%a' /etc/neuralmeter/ssl/server.key) = '600' ]"
    run_test "Service scripts are executable" "[ -x /opt/neuralmeter/bin/simple-api-server.py ]"
}

# Main execution
main() {
    # Debug current execution context
    log_info "=== EXECUTION CONTEXT DEBUG ==="
    log_info "Current EUID: $EUID"
    log_info "Current USER: ${USER:-unset}"
    log_info "SUDO_USER: ${SUDO_USER:-unset}"
    log_info "Current whoami: $(whoami)"
    log_info "Script path: $0"
    log_info "Script args: $*"
    
    # If running as root, switch to neuro user
    if [ "$EUID" -eq 0 ]; then
        log_info "Running as root (EUID=0), attempting to switch to neuro user..."
        
        # Check if neuro user exists
        if id neuro >/dev/null 2>&1; then
            log_info "Neuro user exists, switching execution context..."
            # Re-execute this script as neuro user
            exec sudo -u neuro -H "$0" "$@"
        else
            log_warning "Neuro user does not exist, continuing as root"
        fi
    else
        log_info "Not running as root, continuing with current user"
    fi
    
    log_info "Starting comprehensive System B validation..."
    log_info "Timestamp: $(TZ='America/New_York' date) (Local time)"
    log_info "Timestamp UTC: $(date -u)"
    log_info "Hostname: $(hostname)"
    log_info "Final execution user: $(whoami)"
    
    # Run all test suites
    test_system_packages
    test_ssl_certificates
    test_haproxy_configuration
    test_nginx_configuration
    test_api_services
    test_websocket_services
    test_health_service
    test_prometheus_configuration
    test_service_endpoints
    test_load_balancer_routing
    test_file_permissions
    
    # Summary
    echo ""
    log_info "=== VALIDATION SUMMARY ==="
    log_info "Total tests run: $TOTAL_TESTS"
    log_success "Tests passed: $PASSED_TESTS"
    
    if [ $FAILED_TESTS -gt 0 ]; then
        log_error "Tests failed: $FAILED_TESTS"
        echo ""
        log_error "Failed tests:"
        for test_name in "${FAILED_TEST_NAMES[@]}"; do
            echo -e "  ${RED}✗${NC} $test_name"
        done
        echo ""
        log_error "System B validation FAILED"
        exit 1
    else
        echo ""
        log_success "🎉 All tests passed! System B is fully operational and ready for NeuralMeter CLI testing."
        log_info "System B services are configured and running correctly."
        log_info "Ready to receive traffic from NeuralMeter CLI (System A)."
        exit 0
    fi
}

# Run main function
main "$@"