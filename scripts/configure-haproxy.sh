#!/bin/bash
# configure-haproxy.sh - HAProxy Load Balancer Configuration for System B

set -euo pipefail

LOG_FILE="/var/log/neuralmeter-haproxy.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

echo "=== NeuralMeter HAProxy Configuration Started $(date) ==="

# HAProxy should already be installed from base system setup
if ! command -v haproxy &> /dev/null; then
    echo "Installing HAProxy..."
    apt update && apt install -y haproxy
fi

# Ensure HAProxy directory exists
mkdir -p /etc/haproxy

# Backup existing configuration
if [ -f /etc/haproxy/haproxy.cfg ]; then
    echo "Backing up existing HAProxy configuration..."
    cp /etc/haproxy/haproxy.cfg /etc/haproxy/haproxy.cfg.backup.$(date +%Y%m%d_%H%M%S)
fi

# Create HAProxy configuration
echo "Creating HAProxy configuration..."
cat > /etc/haproxy/haproxy.cfg << 'EOF'
global
    log stdout local0
    chroot /var/lib/haproxy
    stats socket /run/haproxy/admin.sock mode 660 level admin
    stats timeout 30s
    user haproxy
    group haproxy
    daemon
    
    # Tuning for high performance
    maxconn 100000
    nbproc 1
    nbthread 8
    
    # SSL Configuration
    ssl-default-bind-ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256
    ssl-default-bind-options ssl-min-ver TLSv1.2 no-tls-tickets

defaults
    mode http
    log global
    option httplog
    option dontlognull
    option http-server-close
    option forwardfor except *********/8
    option redispatch
    retries 3
    timeout http-request 10s
    timeout queue 1m
    timeout connect 10s
    timeout client 1m
    timeout server 1m
    timeout http-keep-alive 10s
    timeout check 10s
    maxconn 50000

# Statistics interface
listen stats
    bind *:8404
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE

# HTTP Frontend (Subtasks 6.1, 6.3, 6.4, 6.6, 6.10)
frontend http_frontend
    bind *:80
    bind *:443 ssl crt /etc/neuralmeter/ssl/server.pem
    redirect scheme https code 301 if !{ ssl_fc }
    
    # Rate limiting per IP
    stick-table type ip size 100k expire 30s store http_req_rate(10s)
    http-request track-sc0 src
    http-request reject if { sc_http_req_rate(0) gt 1000 }
    
    # Route to appropriate backend
    acl is_api path_beg /api/
    acl is_health path_beg /health
    acl is_metrics path_beg /metrics
    acl is_websocket hdr(Upgrade) -i websocket
    
    use_backend websocket_backend if is_websocket
    use_backend api_backend if is_api
    use_backend health_backend if is_health
    use_backend metrics_backend if is_metrics
    default_backend web_backend

# Web Backend (nginx)
backend web_backend
    balance roundrobin
    option httpchk GET /health
    server nginx1 127.0.0.1:8001 check
    server nginx2 127.0.0.1:8002 check
    server nginx3 127.0.0.1:8003 check

# API Backend (Subtasks 6.3, 6.4)
backend api_backend
    balance leastconn
    option httpchk GET /api/health
    server api1 127.0.0.1:8011 check
    server api2 127.0.0.1:8012 check
    server api3 127.0.0.1:8013 check

# Health Check Backend
backend health_backend
    server health1 127.0.0.1:8021 check

# Metrics Backend (Subtask 6.8)
backend metrics_backend
    server prometheus 127.0.0.1:9090 check

# WebSocket Backend (Subtask 6.7)
backend websocket_backend
    balance source
    option httpchk GET /ws/health
    server ws1 127.0.0.1:8080 check
    server ws2 127.0.0.1:8081 check
EOF

# Test HAProxy configuration
echo "Testing HAProxy configuration..."
haproxy -c -f /etc/haproxy/haproxy.cfg

# Enable and restart HAProxy
echo "Enabling and starting HAProxy service..."
systemctl enable haproxy
systemctl restart haproxy
systemctl status haproxy --no-pager

echo "=== HAProxy Configuration Completed $(date) ==="
echo "HAProxy is now configured and running"
echo "- Statistics available at: http://localhost:8404/stats"
echo "- HTTP traffic on port 80 (redirects to HTTPS)"
echo "- HTTPS traffic on port 443"