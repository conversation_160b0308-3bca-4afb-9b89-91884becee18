#!/bin/bash
# deploy-websocket-addon.sh - Add WebSocket services to existing System B deployment

set -euo pipefail

echo "=== Adding WebSocket Services to System B $(date) ==="

# Check if running with sudo
if [ "$EUID" -ne 0 ]; then
    echo "This script must be run with sudo"
    exit 1
fi

# Install websockets library
echo "Installing WebSocket dependencies..."
pip3 install websockets asyncio

# Create proper WebSocket server
echo "Creating WebSocket server..."
cat > /opt/neuralmeter/bin/websocket-server.py << 'EOF'
#!/usr/bin/env python3
"""
Simple WebSocket server for NeuralMeter testing
Provides real-time streaming capabilities for load test results
"""

import asyncio
import websockets
import json
import logging
import signal
import sys
from datetime import datetime
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('websocket-server')

class WebSocketServer:
    def __init__(self, port=8080):
        self.port = port
        self.clients = set()
        self.running = False
        
    async def register_client(self, websocket, path):
        """Register a new client connection"""
        self.clients.add(websocket)
        logger.info(f"Client connected from {websocket.remote_address}. Total clients: {len(self.clients)}")
        
        try:
            # Send welcome message
            welcome_msg = {
                "type": "welcome",
                "message": "Connected to NeuralMeter WebSocket server",
                "timestamp": datetime.now().isoformat(),
                "server_port": self.port
            }
            await websocket.send(json.dumps(welcome_msg))
            
            # Keep connection alive and handle messages
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(websocket, data)
                except json.JSONDecodeError:
                    error_msg = {
                        "type": "error",
                        "message": "Invalid JSON format",
                        "timestamp": datetime.now().isoformat()
                    }
                    await websocket.send(json.dumps(error_msg))
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client {websocket.remote_address} disconnected")
        except Exception as e:
            logger.error(f"Error handling client {websocket.remote_address}: {e}")
        finally:
            self.clients.discard(websocket)
            logger.info(f"Client removed. Total clients: {len(self.clients)}")
    
    async def handle_message(self, websocket, data):
        """Handle incoming messages from clients"""
        msg_type = data.get('type', 'unknown')
        
        if msg_type == 'ping':
            # Respond to ping with pong
            pong_msg = {
                "type": "pong",
                "timestamp": datetime.now().isoformat(),
                "original_message": data
            }
            await websocket.send(json.dumps(pong_msg))
            
        elif msg_type == 'test_stream':
            # Simulate streaming test results
            await self.simulate_test_stream(websocket, data)
            
        elif msg_type == 'status':
            # Send server status
            status_msg = {
                "type": "status",
                "server_port": self.port,
                "connected_clients": len(self.clients),
                "timestamp": datetime.now().isoformat(),
                "uptime": "running"
            }
            await websocket.send(json.dumps(status_msg))
            
        else:
            # Echo unknown messages
            echo_msg = {
                "type": "echo",
                "original_message": data,
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(echo_msg))
    
    async def simulate_test_stream(self, websocket, request_data):
        """Simulate streaming load test results"""
        duration = request_data.get('duration', 10)  # seconds
        interval = request_data.get('interval', 1)   # seconds between updates
        
        logger.info(f"Starting test stream simulation for {duration}s")
        
        start_time = datetime.now()
        for i in range(int(duration / interval)):
            # Simulate load test metrics
            metrics = {
                "type": "test_metrics",
                "timestamp": datetime.now().isoformat(),
                "elapsed_time": i * interval,
                "requests_sent": (i + 1) * 10,
                "responses_received": (i + 1) * 9,
                "avg_response_time": 150 + (i * 5),  # Simulated increasing response time
                "current_rps": 10 - (i * 0.1),      # Simulated decreasing RPS
                "errors": i // 5,                    # Occasional errors
                "active_connections": min(50, (i + 1) * 5)
            }
            
            try:
                await websocket.send(json.dumps(metrics))
                await asyncio.sleep(interval)
            except websockets.exceptions.ConnectionClosed:
                logger.info("Client disconnected during stream simulation")
                break
        
        # Send completion message
        completion_msg = {
            "type": "test_complete",
            "timestamp": datetime.now().isoformat(),
            "total_duration": duration,
            "final_metrics": {
                "total_requests": duration * 10,
                "total_responses": duration * 9,
                "avg_response_time": 175,
                "total_errors": duration // 5
            }
        }
        
        try:
            await websocket.send(json.dumps(completion_msg))
        except websockets.exceptions.ConnectionClosed:
            pass
    
    async def broadcast_message(self, message):
        """Broadcast message to all connected clients"""
        if self.clients:
            await asyncio.gather(
                *[client.send(json.dumps(message)) for client in self.clients],
                return_exceptions=True
            )
    
    def start_server(self):
        """Start the WebSocket server"""
        logger.info(f"Starting WebSocket server on port {self.port}")
        
        # Set up signal handlers
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            self.running = False
            sys.exit(0)
        
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
        
        self.running = True
        
        # Start the server
        start_server = websockets.serve(
            self.register_client,
            "0.0.0.0",
            self.port,
            ping_interval=30,
            ping_timeout=10
        )
        
        logger.info(f"WebSocket server listening on ws://0.0.0.0:{self.port}")
        logger.info("Server ready to accept connections")
        
        # Run the server
        asyncio.get_event_loop().run_until_complete(start_server)
        asyncio.get_event_loop().run_forever()

def main():
    port = int(os.environ.get('PORT', 8080))
    
    logger.info("="*60)
    logger.info("NeuralMeter WebSocket Server Starting")
    logger.info("="*60)
    logger.info(f"Port: {port}")
    logger.info(f"PID: {os.getpid()}")
    logger.info(f"Start Time: {datetime.now().isoformat()}")
    logger.info("="*60)
    
    server = WebSocketServer(port)
    server.start_server()

if __name__ == '__main__':
    main()
EOF

chmod +x /opt/neuralmeter/bin/websocket-server.py
chown neuro:neuro /opt/neuralmeter/bin/websocket-server.py

# Check if WebSocket services are already running
echo "Checking existing WebSocket services..."
for port in 8080 8081; do
    if systemctl is-active --quiet neuralmeter-ws-$port 2>/dev/null; then
        echo "✅ WebSocket service on port $port is already running"
        continue
    fi
    
    if ss -tuln | grep -q ":$port "; then
        echo "⚠️ Port $port is in use by another service"
        echo "Process using port $port: $(sudo lsof -i :$port 2>/dev/null | tail -1 || echo 'Unknown')"
    fi
done

# Create systemd services for WebSocket servers
echo "Creating WebSocket systemd services..."
for port in 8080 8081; do
    # Skip if service is already active
    if systemctl is-active --quiet neuralmeter-ws-$port 2>/dev/null; then
        echo "Skipping service creation for port $port - already active"
        continue
    fi
    
    cat > /etc/systemd/system/neuralmeter-ws-$port.service << EOF
[Unit]
Description=NeuralMeter WebSocket Server (Port $port)
After=network.target

[Service]
Type=simple
User=neuro
Group=neuro
Environment=PORT=$port
ExecStart=/usr/bin/python3 /opt/neuralmeter/bin/websocket-server.py
Restart=always
RestartSec=3
WorkingDirectory=/opt/neuralmeter

[Install]
WantedBy=multi-user.target
EOF
done

# Reload systemd and start services
systemctl daemon-reload

for port in 8080 8081; do
    # Skip if already running
    if systemctl is-active --quiet neuralmeter-ws-$port 2>/dev/null; then
        echo "✅ WebSocket service on port $port is already running - skipping start"
        continue
    fi
    
    echo "Starting WebSocket service on port $port..."
    systemctl enable neuralmeter-ws-$port
    systemctl restart neuralmeter-ws-$port
    
    sleep 3
    
    if systemctl is-active --quiet neuralmeter-ws-$port; then
        echo "✅ WebSocket service on port $port is running"
    else
        echo "❌ WebSocket service on port $port failed to start"
        echo "Service status:"
        systemctl status neuralmeter-ws-$port --no-pager
        echo "Service logs:"
        journalctl -u neuralmeter-ws-$port --no-pager -n 10
        echo "Port check:"
        ss -tuln | grep ":$port " || echo "Port $port not listening"
    fi
done

# Test WebSocket services
echo "Testing WebSocket services..."
sleep 3

for port in 8080 8081; do
    echo "Testing WebSocket server on port $port..."
    if ss -tuln | grep -q ":$port "; then
        echo "✅ WebSocket server on port $port is listening"
    else
        echo "❌ WebSocket server on port $port is not listening"
    fi
done

echo "=== WebSocket Services Deployment Completed $(date) ==="
echo "WebSocket servers are running on ports 8080, 8081"
echo "Test connection: ws://localhost:8080/"
echo "Test connection: ws://localhost:8081/"