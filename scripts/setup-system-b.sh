#!/bin/bash

# System B Setup Script - Target Services
# This script sets up target services for NeuralMotion CLI load testing

set -e

echo "Setting up System B (Target Services) for NeuralMotion CLI load testing..."

# Update system packages
echo "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install essential tools
echo "Installing essential tools..."
sudo apt install -y curl wget htop iotop nethogs net-tools

# Install web server (nginx) for target services
echo "Installing nginx web server..."
sudo apt install -y nginx

# Configure nginx for load testing
echo "Configuring nginx for load testing..."
sudo tee /etc/nginx/sites-available/loadtest << 'EOF'
server {
    listen 80;
    server_name _;
    
    location / {
        return 200 "Hello from target service!";
        add_header Content-Type text/plain;
    }
    
    location /api/ {
        return 200 '{"status": "ok", "message": "API endpoint ready for load testing"}';
        add_header Content-Type application/json;
    }
    
    location /health {
        return 200 "healthy";
        add_header Content-Type text/plain;
    }
}
EOF

# Enable the site
sudo ln -sf /etc/nginx/sites-available/loadtest /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Start and enable nginx
sudo systemctl enable nginx
sudo systemctl start nginx

# Install additional target services (optional)
echo "Installing additional target services..."

# Install a simple HTTP server for testing
sudo apt install -y python3-pip
pip3 install flask

# Create a simple Flask API for load testing
sudo tee /home/<USER>/simple_api.py << 'EOF'
from flask import Flask, jsonify
import time

app = Flask(__name__)

@app.route('/')
def home():
    return jsonify({"message": "Flask API ready for load testing", "timestamp": time.time()})

@app.route('/api/data')
def get_data():
    return jsonify({"data": "sample data", "count": 42})

@app.route('/api/slow')
def slow_endpoint():
    time.sleep(0.1)  # Simulate processing time
    return jsonify({"message": "slow endpoint", "processed": True})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
EOF

# Create neuro user if it doesn't exist
if ! id "neuro" &>/dev/null; then
    echo "Creating neuro user..."
    sudo useradd -m -s /bin/bash neuro
    sudo usermod -aG sudo neuro
    echo "neuro:5FKWd9Dz" | sudo chpasswd
else
    echo "neuro user already exists"
fi

# Configure sudo access for neuro user (password required)
echo "Configuring sudo access for neuro user..."
if ! sudo grep -q "neuro ALL=(ALL) ALL" /etc/sudoers; then
    echo "neuro ALL=(ALL) ALL" | sudo tee -a /etc/sudoers
    echo "Sudo access configured for neuro user (password required)"
else
    echo "Sudo access already configured for neuro user"
fi

# Set up SSH for neuro user
echo "Setting up SSH for neuro user..."
sudo -u neuro mkdir -p /home/<USER>/.ssh
sudo -u neuro chmod 700 /home/<USER>/.ssh

# Create authorized_keys file (will be populated by Jenkins)
sudo -u neuro touch /home/<USER>/.ssh/authorized_keys
sudo -u neuro chmod 600 /home/<USER>/.ssh/authorized_keys

# Create service directories
echo "Creating service directories..."
sudo mkdir -p /var/www/loadtest
sudo chown neuro:neuro /var/www/loadtest
sudo -u neuro mkdir -p /home/<USER>/services
sudo -u neuro chmod 755 /home/<USER>/services

# Create a simple test page
sudo tee /var/www/loadtest/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Load Test Target</title>
</head>
<body>
    <h1>Load Test Target Service</h1>
    <p>This service is ready for NeuralMotion CLI load testing.</p>
    <p>Timestamp: <span id="timestamp"></span></p>
    <script>
        document.getElementById('timestamp').textContent = new Date().toISOString();
    </script>
</body>
</html>
EOF

# Test service status
echo "Testing service status..."
echo "Nginx status:"
sudo systemctl status nginx --no-pager -l

echo "Service ports:"
netstat -tlnp | grep :80 || echo "Port 80 not listening"
netstat -tlnp | grep :443 || echo "Port 443 not listening"

# Test system resources
echo "System resources:"
free -h
df -h /

echo "System B (Target Services) setup completed successfully!"
echo ""
echo "Available target services:"
echo "- Nginx web server on port 80"
echo "- Flask API on port 5000 (run manually: python3 /home/<USER>/simple_api.py)"
echo ""
echo "Next steps:"
echo "1. Configure SSH keys for neuro user"
echo "2. Test service accessibility from System A"
echo "3. Verify services can handle load test traffic"
echo "4. Configure load test targets in NeuralMotion CLI configuration" 