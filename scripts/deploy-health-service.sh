#!/bin/bash
# deploy-health-service.sh - Deploy Python Health Check Service for System B

set -euo pipefail

LOG_FILE="/var/log/neuralmeter-health-deploy.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

echo "=== NeuralMeter Health Service Deployment Started $(date) ==="

NEURALMETER_DIR="/opt/neuralmeter"
BIN_DIR="$NEURALMETER_DIR/bin"

# Ensure directories exist
mkdir -p "$BIN_DIR"

# Install Python dependencies
echo "Installing Python dependencies..."
pip3 install psutil

# Copy health server script
echo "Copying health server script..."
cp "$(dirname "$0")/health-server.py" "$BIN_DIR/"
chmod +x "$BIN_DIR/health-server.py"

# Set ownership to neuro user
chown -R neuro:neuro "$NEURALMETER_DIR"

echo "=== Creating systemd service ==="

# Create systemd service file for health check server
cat > /etc/systemd/system/neuralmeter-health.service << EOF
[Unit]
Description=NeuralMeter Health Check Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=neuro
Group=neuro
Environment=PORT=8021
Environment=PYTHONPATH=/opt/neuralmeter/bin
ExecStart=/usr/bin/python3 $BIN_DIR/health-server.py
Restart=always
RestartSec=5
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true

# Resource limits
LimitNOFILE=10000
LimitNPROC=100

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and enable service
echo "Enabling and starting health service..."
systemctl daemon-reload
systemctl enable neuralmeter-health
systemctl restart neuralmeter-health

# Wait a moment for service to start
sleep 3

# Check service status
if systemctl is-active --quiet neuralmeter-health; then
    echo "✅ neuralmeter-health service is running"
else
    echo "❌ neuralmeter-health service failed to start"
    systemctl status neuralmeter-health --no-pager
fi

# Test health endpoints
echo "Testing health endpoints..."
sleep 2

endpoints=(
    "http://localhost:8021/health"
    "http://localhost:8021/health/detailed"
    "http://localhost:8021/health/system"
    "http://localhost:8021/health/services"
)

for endpoint in "${endpoints[@]}"; do
    echo "Testing $endpoint..."
    if curl -s -f "$endpoint" > /dev/null; then
        echo "✅ $endpoint is responding"
    else
        echo "❌ $endpoint is not responding"
    fi
done

echo "=== Health Service Deployment Completed $(date) ==="
echo "Health service is running on port 8021"
echo "Available endpoints:"
echo "- http://localhost:8021/health - Basic health check"
echo "- http://localhost:8021/health/detailed - Detailed system metrics"
echo "- http://localhost:8021/health/system - System health status"
echo "- http://localhost:8021/health/services - Service connectivity check"