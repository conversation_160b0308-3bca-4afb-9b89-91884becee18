#!/bin/bash

# Task 39 - Metrics Aggregation Logic Testing Script
# This script runs development-appropriate tests for all Task 39 subtasks

set -e

echo "🧪 Task 39 - Metrics Aggregation Logic Testing"
echo "=============================================="
echo ""

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "go.mod" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_status "Starting Task 39 development-appropriate testing..."
echo ""

# Test 1: Basic Statistical Functions (Subtask 39.1)
print_status "Testing Subtask 39.1 - Basic Statistical Functions"
if go test -v ./test/unit/metrics/ -run TestTask39_BasicStatisticalFunctions -timeout 30s; then
    print_success "✅ Basic statistical functions tests passed"
else
    print_error "❌ Basic statistical functions tests failed"
    exit 1
fi
echo ""

# Test 2: Percentile Calculations (Subtask 39.2)
print_status "Testing Subtask 39.2 - Percentile Calculation Algorithms"
if go test -v ./test/unit/metrics/ -run TestTask39_PercentileCalculations -timeout 30s; then
    print_success "✅ Percentile calculation tests passed"
else
    print_error "❌ Percentile calculation tests failed"
    exit 1
fi
echo ""

# Test 3: Sliding Window Aggregation (Subtask 39.3)
print_status "Testing Subtask 39.3 - Sliding Window Aggregation Logic"
if go test -v ./test/unit/metrics/ -run TestTask39_SlidingWindowAggregation -timeout 30s; then
    print_success "✅ Sliding window aggregation tests passed"
else
    print_error "❌ Sliding window aggregation tests failed"
    exit 1
fi
echo ""

# Test 4: Time-Based Bucketing (Subtask 39.4)
print_status "Testing Subtask 39.4 - Time-Based Bucketing Logic"
if go test -v ./test/unit/metrics/ -run TestTask39_TimeBucketingLogic -timeout 30s; then
    print_success "✅ Time-based bucketing tests passed"
else
    print_error "❌ Time-based bucketing tests failed"
    exit 1
fi
echo ""

# Test 5: Metrics Integration (Subtask 39.5)
print_status "Testing Subtask 39.5 - Metrics Collection System Integration"
if go test -v ./test/unit/metrics/ -run TestTask39_MetricsIntegration -timeout 30s; then
    print_success "✅ Metrics integration tests passed"
else
    print_error "❌ Metrics integration tests failed"
    exit 1
fi
echo ""

# Test 6: Development Resource Usage (Subtask 39.6)
print_status "Testing Subtask 39.6 - Development-Appropriate Resource Usage"
if go test -v ./test/unit/metrics/ -run TestTask39_DevelopmentResourceUsage -timeout 30s; then
    print_success "✅ Development resource usage tests passed"
else
    print_error "❌ Development resource usage tests failed"
    exit 1
fi
echo ""

# Run all Task 39 tests together
print_status "Running all Task 39 tests together..."
if go test -v ./test/unit/metrics/ -run TestTask39 -timeout 60s; then
    print_success "✅ All Task 39 tests passed together"
else
    print_error "❌ Some Task 39 tests failed when run together"
    exit 1
fi
echo ""

# Optional: Run a quick benchmark to ensure performance is development-appropriate
print_status "Running development-appropriate benchmarks..."
if go test -bench=BenchmarkMetricsIntegrator -benchtime=1s -timeout 30s ./test/benchmarks/ 2>/dev/null; then
    print_success "✅ Development benchmarks completed successfully"
else
    print_warning "⚠️  Benchmarks not available or failed (this is optional)"
fi
echo ""

# Summary
echo "🎉 Task 39 - Metrics Aggregation Logic Testing Complete!"
echo "======================================================="
echo ""
echo "All subtasks tested successfully:"
echo "✅ Subtask 39.1 - Basic Statistical Functions"
echo "✅ Subtask 39.2 - Percentile Calculation Algorithms"
echo "✅ Subtask 39.3 - Sliding Window Aggregation Logic"
echo "✅ Subtask 39.4 - Time-Based Bucketing Logic"
echo "✅ Subtask 39.5 - Metrics Collection System Integration"
echo "✅ Subtask 39.6 - Development-Appropriate Testing"
echo ""
echo "🏆 Task 39 implementation is complete and ready for production!"
echo ""
echo "Development Notes:"
echo "• All tests use small datasets (5-20 data points)"
echo "• Memory usage is limited (100KB-1MB per test)"
echo "• Test timeouts are short (30-60 seconds)"
echo "• No overwhelming system resource usage"
echo "• Tests are deterministic and repeatable"
echo ""
echo "Next Steps:"
echo "• Task 39 is complete - proceed to Task 40"
echo "• Integration tests can be scaled up in cloud environments"
echo "• Performance tests can use larger datasets on server hardware"
