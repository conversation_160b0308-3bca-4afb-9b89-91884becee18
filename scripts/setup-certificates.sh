#!/bin/bash
# setup-certificates.sh - SSL/TLS Certificate Setup for System B

set -euo pipefail

LOG_FILE="/var/log/neuralmeter-certificates.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

echo "=== NeuralMeter SSL/TLS Certificate Setup Started $(date) ==="

# Create certificate directory
echo "Creating certificate directory..."
mkdir -p /etc/neuralmeter/ssl
cd /etc/neuralmeter/ssl

# Generate root CA
echo "Generating root CA..."
openssl genrsa -out ca.key 4096
openssl req -new -x509 -days 3650 -key ca.key \
    -out ca.crt \
    -subj "/C=US/ST=Test/L=Test/O=NeuralMeter/CN=NeuralMeter-CA"

# Generate server certificate
echo "Generating server certificate..."
openssl genrsa -out server.key 2048
openssl req -new -key server.key \
    -out server.csr \
    -subj "/C=US/ST=Test/L=Test/O=NeuralMeter/CN=neuralmeter.local"

# Create server certificate with SAN
echo "Creating server certificate configuration..."
cat > server.conf << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = Test
L = Test
O = NeuralMeter
CN = neuralmeter.local

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = neuralmeter.local
DNS.2 = system-b
DNS.3 = localhost
IP.1 = 127.0.0.1
IP.2 = *************
EOF

echo "Signing server certificate..."
openssl x509 -req -in server.csr \
    -CA ca.crt \
    -CAkey ca.key \
    -CAcreateserial -out server.crt \
    -days 365 -extensions v3_req \
    -extfile server.conf

# Create combined certificate for HAProxy
echo "Creating combined certificate for HAProxy..."
cat server.crt server.key > server.pem

# Set permissions
echo "Setting certificate permissions..."
chmod 600 *.key *.pem
chmod 644 *.crt *.conf *.csr
chown -R neuro:neuro /etc/neuralmeter/ssl

# Verify certificates
echo "Verifying certificates..."
openssl x509 -in server.crt -text -noout | grep -E "(Subject:|DNS:|IP Address:)" || true

echo "=== SSL/TLS Certificate Setup Completed $(date) ==="
echo "Certificates created in /etc/neuralmeter/ssl/"
echo "- ca.crt: Root CA certificate"
echo "- server.crt: Server certificate"
echo "- server.key: Server private key"
echo "- server.pem: Combined certificate for HAProxy"