#!/bin/bash
# configure-prometheus.sh - Configure Prometheus monitoring for System B

set -euo pipefail

LOG_FILE="/var/log/neuralmeter-prometheus.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

echo "=== NeuralMeter Prometheus Configuration Started $(date) ==="

# Prometheus should already be installed from base system setup
if ! command -v prometheus &> /dev/null; then
    echo "ERROR: Prometheus not found. Run base system setup first."
    exit 1
fi

PROMETHEUS_CONFIG_DIR="/etc/prometheus"
PROMETHEUS_DATA_DIR="/var/lib/prometheus"

# Ensure Prometheus directories exist
mkdir -p "$PROMETHEUS_CONFIG_DIR"
mkdir -p "$PROMETHEUS_DATA_DIR"

# Backup existing configuration
if [ -f "$PROMETHEUS_CONFIG_DIR/prometheus.yml" ]; then
    echo "Backing up existing Prometheus configuration..."
    cp "$PROMETHEUS_CONFIG_DIR/prometheus.yml" "$PROMETHEUS_CONFIG_DIR/prometheus.yml.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Create Prometheus configuration
echo "Creating Prometheus configuration..."
cat > "$PROMETHEUS_CONFIG_DIR/prometheus.yml" << 'EOF'
# NeuralMeter System B Prometheus Configuration
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'neuralmeter-system-b'
    environment: 'test'

# Alertmanager configuration (optional)
alerting:
  alertmanagers:
    - static_configs:
        - targets: []

# Load rules once and periodically evaluate them
rule_files:
  - "neuralmeter_rules.yml"

# Scrape configuration
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node Exporter (system metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
    scrape_interval: 15s

  # NeuralMeter API Services
  - job_name: 'neuralmeter-api'
    static_configs:
      - targets: 
          - 'localhost:8011'
          - 'localhost:8012'
          - 'localhost:8013'
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s

  # NeuralMeter WebSocket Services
  - job_name: 'neuralmeter-websocket'
    static_configs:
      - targets:
          - 'localhost:8080'
          - 'localhost:8081'
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 5s

  # NeuralMeter Health Service
  - job_name: 'neuralmeter-health'
    static_configs:
      - targets: ['localhost:8021']
    scrape_interval: 30s
    metrics_path: /health/detailed
    scrape_timeout: 10s

  # HAProxy Stats
  - job_name: 'haproxy'
    static_configs:
      - targets: ['localhost:8404']
    scrape_interval: 15s
    metrics_path: /stats
    scrape_timeout: 5s

  # Nginx (if nginx-prometheus-exporter is installed)
  - job_name: 'nginx'
    static_configs:
      - targets: ['localhost:9113']
    scrape_interval: 15s
    scrape_timeout: 5s
EOF

# Create Prometheus rules file
echo "Creating Prometheus rules..."
cat > "$PROMETHEUS_CONFIG_DIR/neuralmeter_rules.yml" << 'EOF'
groups:
  - name: neuralmeter_system_b
    rules:
      # Service availability rules
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} on {{ $labels.instance }} has been down for more than 1 minute."

      # High CPU usage
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 5 minutes on {{ $labels.instance }}"

      # High memory usage
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% for more than 5 minutes on {{ $labels.instance }}"

      # Disk space usage
      - alert: DiskSpaceUsage
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes * 100 > 90
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Disk space usage is high"
          description: "Disk usage is above 90% on {{ $labels.instance }} filesystem {{ $labels.mountpoint }}"

      # API response time
      - alert: HighAPIResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="neuralmeter-api"}[5m])) > 1
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "High API response time"
          description: "95th percentile response time is above 1 second for {{ $labels.job }}"

      # WebSocket connection issues
      - alert: WebSocketConnectionDrop
        expr: increase(websocket_connections_total{job="neuralmeter-websocket"}[5m]) < 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "WebSocket connections dropping"
          description: "WebSocket connections are dropping on {{ $labels.instance }}"

  - name: neuralmeter_load_testing
    rules:
      # Load test specific metrics
      - record: neuralmeter:request_rate_5m
        expr: rate(http_requests_total[5m])

      - record: neuralmeter:error_rate_5m
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])

      - record: neuralmeter:response_time_p95_5m
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))

      - record: neuralmeter:response_time_p99_5m
        expr: histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))
EOF

# Set proper ownership and permissions
echo "Setting Prometheus configuration permissions..."
chown -R prometheus:prometheus "$PROMETHEUS_CONFIG_DIR"
chmod 644 "$PROMETHEUS_CONFIG_DIR/prometheus.yml"
chmod 644 "$PROMETHEUS_CONFIG_DIR/neuralmeter_rules.yml"

# Ensure data directory exists with proper permissions
mkdir -p "$PROMETHEUS_DATA_DIR"
chown -R prometheus:prometheus "$PROMETHEUS_DATA_DIR"

# Validate Prometheus configuration
echo "Validating Prometheus configuration..."
if promtool check config "$PROMETHEUS_CONFIG_DIR/prometheus.yml" 2>/dev/null; then
    echo "✅ Prometheus configuration is valid"
else
    echo "⚠️ Prometheus configuration validation skipped (promtool not available)"
fi

# Enable and restart Prometheus
echo "Enabling and starting Prometheus service..."
systemctl enable prometheus
systemctl restart prometheus

# Wait for service to start
sleep 5

# Check service status
if systemctl is-active --quiet prometheus; then
    echo "✅ Prometheus service is running"
else
    echo "❌ Prometheus service failed to start"
    systemctl status prometheus --no-pager
    exit 1
fi

# Test Prometheus endpoint
echo "Testing Prometheus endpoint..."
if curl -s -f "http://localhost:9090/-/healthy" > /dev/null; then
    echo "✅ Prometheus is responding on port 9090"
else
    echo "❌ Prometheus is not responding on port 9090"
fi

# Test metrics endpoint
echo "Testing Prometheus metrics..."
if curl -s -f "http://localhost:9090/metrics" | grep -q "prometheus_"; then
    echo "✅ Prometheus metrics endpoint is working"
else
    echo "❌ Prometheus metrics endpoint is not working"
fi

echo "=== Prometheus Configuration Completed $(date) ==="
echo "Prometheus is now configured and running"
echo "- Web UI available at: http://localhost:9090"
echo "- Metrics endpoint: http://localhost:9090/metrics"
echo "- Health check: http://localhost:9090/-/healthy"
echo "- Configuration file: $PROMETHEUS_CONFIG_DIR/prometheus.yml"
echo "- Rules file: $PROMETHEUS_CONFIG_DIR/neuralmeter_rules.yml"