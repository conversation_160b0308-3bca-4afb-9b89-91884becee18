#!/bin/bash
# deploy-websocket-services.sh - Deploy simple WebSocket services for System B

set -euo pipefail

LOG_FILE="/var/log/neuralmeter-websocket-deploy.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

echo "=== NeuralMeter WebSocket Services Deployment Started $(date) ==="

NEURALMETER_DIR="/opt/neuralmeter"
BIN_DIR="$NEURALMETER_DIR/bin"

# Ensure directories exist
mkdir -p "$BIN_DIR"

# Install websocket dependencies
echo "Installing WebSocket dependencies..."
pip3 install websockets

# Create simple Python WebSocket server
echo "Creating Python WebSocket server..."
cat > "$BIN_DIR/simple-websocket-server.py" << 'EOF'
#!/usr/bin/env python3
import json
import os
import time
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import socketserver

class SimpleWebSocketHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            response = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "port": os.environ.get('PORT', '8080'),
                "service": "simple-websocket-server"
            }
            self.wfile.write(json.dumps(response).encode('utf-8'))
        elif self.path == '/ws':
            # Simple WebSocket-like response for testing
            self.send_response(200)
            self.send_header('Content-Type', 'text/plain')
            self.end_headers()
            self.wfile.write(b'WebSocket endpoint available')
        else:
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            response = {
                "message": "Simple WebSocket-like server",
                "port": os.environ.get('PORT', '8080'),
                "timestamp": int(time.time())
            }
            self.wfile.write(json.dumps(response).encode('utf-8'))
    
    def log_message(self, format, *args):
        # Suppress HTTP server logs
        pass

def main():
    port = int(os.environ.get('PORT', 8080))
    
    try:
        print(f"Simple WebSocket-like server starting on port {port}")
        
        with socketserver.TCPServer(("", port), SimpleWebSocketHandler) as httpd:
            print(f"Server running on port {port}")
            httpd.serve_forever()
            
    except Exception as e:
        print(f"Error starting server: {e}")
        exit(1)

if __name__ == '__main__':
    main()
EOF

chmod +x "$BIN_DIR/simple-websocket-server.py"

# Set ownership to neuro user
chown -R neuro:neuro "$NEURALMETER_DIR"

echo "=== Creating systemd services ==="

# Create systemd service files for WebSocket servers
for port in 8080 8081; do
    echo "Creating systemd service for WebSocket server on port $port..."
    cat > /etc/systemd/system/neuralmeter-ws-$port.service << EOF
[Unit]
Description=NeuralMeter Simple WebSocket Server (Port $port)
After=network.target
Wants=network.target

[Service]
Type=simple
User=neuro
Group=neuro
Environment=PORT=$port
ExecStart=/usr/bin/python3 $BIN_DIR/simple-websocket-server.py
Restart=always
RestartSec=3
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true

# Resource limits
LimitNOFILE=10000
LimitNPROC=100

[Install]
WantedBy=multi-user.target
EOF
done

# Reload systemd and enable services
echo "Enabling and starting WebSocket services..."
systemctl daemon-reload

for port in 8080 8081; do
    echo "Starting neuralmeter-ws-$port service..."
    systemctl enable neuralmeter-ws-$port
    systemctl restart neuralmeter-ws-$port
    
    # Wait a moment for service to start
    sleep 2
    
    # Check service status
    if systemctl is-active --quiet neuralmeter-ws-$port; then
        echo "✅ neuralmeter-ws-$port service is running"
    else
        echo "❌ neuralmeter-ws-$port service failed to start"
        systemctl status neuralmeter-ws-$port --no-pager
    fi
done

# Test WebSocket health endpoints (on the same ports as WebSocket servers)
echo "Testing WebSocket health endpoints..."
sleep 5

for port in 8080 8081; do
    echo "Testing WebSocket health endpoint on port $port..."
    if curl -s -f "http://localhost:$port/health" > /dev/null; then
        echo "✅ WebSocket server on port $port is responding to health checks"
    else
        echo "❌ WebSocket server on port $port is not responding to health checks"
        echo "Checking service logs..."
        systemctl status neuralmeter-ws-$port --no-pager || true
    fi
done

echo "=== WebSocket Services Deployment Completed $(date) ==="
echo "Simple Python WebSocket services are running on ports 8080, 8081"
echo "Health check endpoints: http://localhost:908X/health (port+1000)"
echo "WebSocket endpoints: ws://localhost:808X/"