#!/bin/bash
# validate-system-b-comprehensive.sh - Comprehensive validation with extensive logging
# Created to address repeated validation failures

set -euo pipefail

# Comprehensive logging setup
SCRIPT_NAME="$(basename "$0")"
LOG_DIR="/var/log/neuralmeter"
VALIDATION_LOG="$LOG_DIR/validation-$(date +%Y%m%d_%H%M%S).log"

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Redirect output to log while showing on screen
exec 1> >(tee -a "$VALIDATION_LOG")
exec 2>&1

echo "=============================================================================="
echo "NEURALMETER SYSTEM B COMPREHENSIVE VALIDATION"
echo "=============================================================================="
echo "Script: $SCRIPT_NAME"
echo "Started: $(date)"
echo "Local Time: $(TZ='America/New_York' date)"
echo "UTC Time: $(date -u)"
echo "User: $(whoami)"
echo "EUID: $EUID"
echo "SUDO_USER: ${SUDO_USER:-unset}"
echo "PWD: $(pwd)"
echo "Validation Log: $VALIDATION_LOG"
echo "=============================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
WARNING_TESTS=0

# Test tracking
declare -a FAILED_TEST_NAMES=()
declare -a WARNING_TEST_NAMES=()

# Logging functions with timestamps
log_info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] [INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [PASS]${NC} $1"
    ((PASSED_TESTS++))
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [WARN]${NC} $1"
    ((WARNING_TESTS++))
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [FAIL]${NC} $1"
    ((FAILED_TESTS++))
}

log_debug() {
    echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')] [DEBUG]${NC} $1"
}

log_step() {
    echo ""
    echo "=============================================================================="
    echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')] STEP: $1${NC}"
    echo "=============================================================================="
}

# Enhanced test function with detailed logging
run_test() {
    local test_name="$1"
    local test_command="$2"
    local is_critical="${3:-true}"  # Default to critical
    
    ((TOTAL_TESTS++))
    log_info "Running test: $test_name"
    log_debug "Command: $test_command"
    log_debug "Critical: $is_critical"
    
    # Run command and capture both stdout and stderr
    local output
    local exit_code
    
    if output=$(eval "$test_command" 2>&1); then
        exit_code=0
        log_success "$test_name"
        if [ -n "$output" ]; then
            log_debug "Output: $output"
        fi
        return 0
    else
        exit_code=$?
        if [ "$is_critical" = "true" ]; then
            log_error "$test_name"
            FAILED_TEST_NAMES+=("$test_name")
        else
            log_warning "$test_name (non-critical)"
            WARNING_TEST_NAMES+=("$test_name")
        fi
        
        log_debug "Exit code: $exit_code"
        if [ -n "$output" ]; then
            log_debug "Error output: $output"
        fi
        return $exit_code
    fi
}

# User context validation
validate_user_context() {
    log_step "VALIDATING USER CONTEXT"
    
    log_info "Current execution context:"
    log_debug "  User: $(whoami)"
    log_debug "  UID: $(id -u)"
    log_debug "  GID: $(id -g)"
    log_debug "  Groups: $(groups)"
    log_debug "  Home: $HOME"
    log_debug "  Shell: $SHELL"
    log_debug "  SUDO_USER: ${SUDO_USER:-unset}"
    
    # Check if neuro user exists
    run_test "Neuro user exists" "id neuro >/dev/null 2>&1"
    
    if id neuro >/dev/null 2>&1; then
        log_info "Neuro user information:"
        log_debug "  Neuro UID: $(id -u neuro)"
        log_debug "  Neuro GID: $(id -g neuro)"
        log_debug "  Neuro Groups: $(groups neuro)"
        log_debug "  Neuro Home: $(eval echo ~neuro)"
    fi
    
    log_success "User context validation completed"
}

# System information gathering
gather_system_info() {
    log_step "GATHERING SYSTEM INFORMATION"
    
    log_info "Operating System:"
    if [ -f /etc/os-release ]; then
        while read line; do
            log_debug "  $line"
        done < /etc/os-release
    fi
    
    log_info "System Resources:"
    log_debug "  Memory: $(free -h | grep '^Mem:' | awk '{print $2 " total, " $3 " used, " $7 " available"}')"
    log_debug "  Disk: $(df -h / | tail -1 | awk '{print $2 " total, " $3 " used, " $4 " available"}')"
    log_debug "  CPU: $(nproc) cores"
    log_debug "  Load: $(uptime | awk -F'load average:' '{print $2}')"
    
    log_info "Network:"
    log_debug "  Hostname: $(hostname)"
    log_debug "  IP: $(hostname -I | awk '{print $1}' || echo 'unknown')"
    
    log_success "System information gathered"
}

# Test core system components
test_core_system() {
    log_step "TESTING CORE SYSTEM COMPONENTS"
    
    # Test Python installation
    run_test "Python3 installation" "which python3 && python3 --version"
    
    # Test pip installation
    run_test "Pip3 installation" "which pip3 && pip3 --version"
    
    # Test psutil package
    run_test "Psutil Python package" "python3 -c 'import psutil; print(f\"psutil version: {psutil.__version__}\")'"
    
    # Test curl
    run_test "Curl installation" "which curl && curl --version | head -1"
    
    # Test systemctl
    run_test "Systemctl available" "which systemctl && systemctl --version | head -1"
    
    log_success "Core system components tested"
}

# Test directory structure
test_directory_structure() {
    log_step "TESTING DIRECTORY STRUCTURE"
    
    local required_dirs=(
        "/opt/neuralmeter"
        "/opt/neuralmeter/bin"
        "/opt/neuralmeter/logs"
        "/var/log/neuralmeter"
    )
    
    for dir in "${required_dirs[@]}"; do
        run_test "Directory exists: $dir" "[ -d '$dir' ]"
        
        if [ -d "$dir" ]; then
            local permissions=$(ls -ld "$dir" | awk '{print $1}')
            local owner=$(ls -ld "$dir" | awk '{print $3":"$4}')
            log_debug "  $dir - Permissions: $permissions, Owner: $owner"
        fi
    done
    
    log_success "Directory structure tested"
}

# Test service scripts
test_service_scripts() {
    log_step "TESTING SERVICE SCRIPTS"
    
    local scripts=(
        "/opt/neuralmeter/bin/simple-api-server.py"
        "/opt/neuralmeter/bin/health-server.py"
    )
    
    for script in "${scripts[@]}"; do
        run_test "Script exists: $(basename $script)" "[ -f '$script' ]"
        run_test "Script is executable: $(basename $script)" "[ -x '$script' ]"
        
        if [ -f "$script" ]; then
            local permissions=$(ls -l "$script" | awk '{print $1}')
            local owner=$(ls -l "$script" | awk '{print $3":"$4}')
            local size=$(ls -lh "$script" | awk '{print $5}')
            log_debug "  $script - Size: $size, Permissions: $permissions, Owner: $owner"
            
            # Test syntax
            run_test "Script syntax valid: $(basename $script)" "python3 -m py_compile '$script'" "false"
        fi
    done
    
    log_success "Service scripts tested"
}

# Test systemd services
test_systemd_services() {
    log_step "TESTING SYSTEMD SERVICES"
    
    local services=("neuralmeter-api-8011" "neuralmeter-api-8012" "neuralmeter-api-8013" "neuralmeter-health")
    
    for service in "${services[@]}"; do
        log_info "Testing service: $service"
        
        # Check if service file exists
        run_test "Service file exists: $service" "[ -f '/etc/systemd/system/$service.service' ]"
        
        # Check if service is enabled
        run_test "Service is enabled: $service" "systemctl is-enabled '$service' >/dev/null 2>&1"
        
        # Check if service is active
        run_test "Service is active: $service" "systemctl is-active '$service' >/dev/null 2>&1"
        
        # Get detailed service status
        if systemctl is-active "$service" >/dev/null 2>&1; then
            local status_info=$(systemctl status "$service" --no-pager -l 2>/dev/null | head -10)
            log_debug "Service $service status:"
            echo "$status_info" | while read line; do
                log_debug "  $line"
            done
        else
            log_debug "Service $service is not active - checking logs:"
            local logs=$(journalctl -u "$service" --no-pager -n 5 2>/dev/null || echo "No logs available")
            echo "$logs" | while read line; do
                log_debug "  $line"
            done
        fi
    done
    
    log_success "Systemd services tested"
}

# Test network ports
test_network_ports() {
    log_step "TESTING NETWORK PORTS"
    
    local ports=(8011 8012 8013 8021)
    
    for port in "${ports[@]}"; do
        log_info "Testing port: $port"
        
        # Check if port is listening
        run_test "Port $port is listening" "ss -tuln | grep -q ':$port '"
        
        # Show what's listening on the port
        if ss -tuln | grep -q ":$port "; then
            local port_info=$(ss -tuln | grep ":$port " | head -1)
            log_debug "  Port $port info: $port_info"
            
            # Try to identify the process
            local process_info=$(sudo lsof -i ":$port" 2>/dev/null | tail -1 || echo "Process info not available")
            log_debug "  Process: $process_info"
        fi
    done
    
    log_success "Network ports tested"
}

# Test HTTP endpoints
test_http_endpoints() {
    log_step "TESTING HTTP ENDPOINTS"
    
    # Test API services
    for port in 8011 8012 8013; do
        log_info "Testing API service endpoints on port $port"
        
        # Test health endpoint
        run_test "API health endpoint (port $port)" "curl -s -f --connect-timeout 10 --max-time 30 'http://localhost:$port/health' >/dev/null"
        
        if curl -s -f --connect-timeout 10 --max-time 30 "http://localhost:$port/health" >/dev/null 2>&1; then
            local health_response=$(curl -s --connect-timeout 10 --max-time 30 "http://localhost:$port/health" 2>/dev/null || echo "No response")
            log_debug "  Health response (port $port): $health_response"
        fi
        
        # Test API data endpoint
        run_test "API data endpoint (port $port)" "curl -s -f --connect-timeout 10 --max-time 30 'http://localhost:$port/api/data' >/dev/null"
        
        # Test root endpoint
        run_test "API root endpoint (port $port)" "curl -s -f --connect-timeout 10 --max-time 30 'http://localhost:$port/' >/dev/null" "false"
    done
    
    # Test health service
    log_info "Testing health service endpoints on port 8021"
    
    local health_endpoints=("/health" "/health/detailed" "/health/system" "/health/services")
    
    for endpoint in "${health_endpoints[@]}"; do
        run_test "Health service endpoint $endpoint" "curl -s -f --connect-timeout 10 --max-time 30 'http://localhost:8021$endpoint' >/dev/null"
        
        if curl -s -f --connect-timeout 10 --max-time 30 "http://localhost:8021$endpoint" >/dev/null 2>&1; then
            local response=$(curl -s --connect-timeout 10 --max-time 30 "http://localhost:8021$endpoint" 2>/dev/null | head -c 200 || echo "No response")
            log_debug "  Response $endpoint: ${response}..."
        fi
    done
    
    log_success "HTTP endpoints tested"
}

# Test service integration
test_service_integration() {
    log_step "TESTING SERVICE INTEGRATION"
    
    # Test if health service can check API services
    log_info "Testing health service integration with API services"
    
    run_test "Health service can check API services" "curl -s -f 'http://localhost:8021/health/services' | grep -q 'neuralmeter-api'" "false"
    
    # Test load balancing (if multiple API services respond differently)
    log_info "Testing API service responses"
    
    local api_responses=()
    for port in 8011 8012 8013; do
        if curl -s -f "http://localhost:$port/api/data" >/dev/null 2>&1; then
            local response=$(curl -s "http://localhost:$port/api/data" 2>/dev/null | grep -o '"port":"[0-9]*"' || echo "no-port")
            api_responses+=("$response")
            log_debug "  API response from port $port: $response"
        fi
    done
    
    if [ ${#api_responses[@]} -ge 2 ]; then
        log_success "Multiple API services are responding with different port identifiers"
    else
        log_warning "Not all API services are responding correctly"
    fi
    
    log_success "Service integration tested"
}

# Performance and resource tests
test_performance() {
    log_step "TESTING PERFORMANCE AND RESOURCES"
    
    # Test system load
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk -F',' '{print $1}' | tr -d ' ')
    local cpu_count=$(nproc)
    
    log_info "System load test"
    log_debug "  Load average (1min): $load_avg"
    log_debug "  CPU count: $cpu_count"
    
    if (( $(echo "$load_avg < $cpu_count" | bc -l 2>/dev/null || echo "0") )); then
        log_success "System load is acceptable ($load_avg < $cpu_count)"
    else
        log_warning "System load is high ($load_avg >= $cpu_count)"
    fi
    
    # Test memory usage
    local memory_info=$(free | grep '^Mem:')
    local total_mem=$(echo $memory_info | awk '{print $2}')
    local used_mem=$(echo $memory_info | awk '{print $3}')
    local mem_percent=$(( (used_mem * 100) / total_mem ))
    
    log_info "Memory usage test"
    log_debug "  Memory usage: ${mem_percent}%"
    
    if [ $mem_percent -lt 90 ]; then
        log_success "Memory usage is acceptable (${mem_percent}%)"
    else
        log_warning "Memory usage is high (${mem_percent}%)"
    fi
    
    # Test disk space
    local disk_info=$(df / | tail -1)
    local disk_percent=$(echo $disk_info | awk '{print $5}' | tr -d '%')
    
    log_info "Disk space test"
    log_debug "  Disk usage: ${disk_percent}%"
    
    if [ $disk_percent -lt 90 ]; then
        log_success "Disk usage is acceptable (${disk_percent}%)"
    else
        log_warning "Disk usage is high (${disk_percent}%)"
    fi
    
    log_success "Performance and resource tests completed"
}

# Generate comprehensive validation report
generate_validation_report() {
    log_step "GENERATING VALIDATION REPORT"
    
    local report_file="$LOG_DIR/validation-report-$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
NEURALMETER SYSTEM B VALIDATION REPORT
=====================================

Validation Date: $(date)
Validation Script: $SCRIPT_NAME
Validation Log: $VALIDATION_LOG

SUMMARY
-------
Total Tests: $TOTAL_TESTS
Passed Tests: $PASSED_TESTS
Failed Tests: $FAILED_TESTS
Warning Tests: $WARNING_TESTS

SYSTEM INFORMATION
-----------------
Hostname: $(hostname)
Operating System: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)
Kernel: $(uname -r)
Architecture: $(uname -m)
CPU Cores: $(nproc)
Memory: $(free -h | grep '^Mem:' | awk '{print $2}')
Disk Space: $(df -h / | tail -1 | awk '{print $2}')

USER CONTEXT
-----------
Validation User: $(whoami)
Neuro User Exists: $(id neuro >/dev/null 2>&1 && echo "Yes" || echo "No")

SERVICE STATUS
-------------
EOF

    # Add service status to report
    local services=("neuralmeter-api-8011" "neuralmeter-api-8012" "neuralmeter-api-8013" "neuralmeter-health")
    for service in "${services[@]}"; do
        local status=$(systemctl is-active "$service" 2>/dev/null || echo "inactive")
        echo "$service: $status" >> "$report_file"
    done
    
    cat >> "$report_file" << EOF

PORT STATUS
----------
EOF

    # Add port status to report
    local ports=(8011 8012 8013 8021)
    for port in "${ports[@]}"; do
        local listening=$(ss -tuln | grep -q ":$port " && echo "Listening" || echo "Not Listening")
        echo "Port $port: $listening" >> "$report_file"
    done
    
    if [ ${#FAILED_TEST_NAMES[@]} -gt 0 ]; then
        cat >> "$report_file" << EOF

FAILED TESTS
-----------
EOF
        for test in "${FAILED_TEST_NAMES[@]}"; do
            echo "- $test" >> "$report_file"
        done
    fi
    
    if [ ${#WARNING_TEST_NAMES[@]} -gt 0 ]; then
        cat >> "$report_file" << EOF

WARNING TESTS
------------
EOF
        for test in "${WARNING_TEST_NAMES[@]}"; do
            echo "- $test" >> "$report_file"
        done
    fi
    
    cat >> "$report_file" << EOF

VALIDATION RESULT
----------------
EOF
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo "PASSED - System B is operational" >> "$report_file"
    else
        echo "FAILED - System B has issues that need attention" >> "$report_file"
    fi
    
    log_success "Validation report generated: $report_file"
    
    # Display report summary
    log_info "Report Summary:"
    cat "$report_file" | while read line; do
        log_debug "  $line"
    done
}

# Main validation function
main() {
    local validation_start_time=$(date +%s)
    
    log_info "Starting comprehensive System B validation..."
    
    # Run all validation steps
    validate_user_context
    gather_system_info
    test_core_system
    test_directory_structure
    test_service_scripts
    test_systemd_services
    test_network_ports
    test_http_endpoints
    test_service_integration
    test_performance
    generate_validation_report
    
    # Final summary
    local validation_end_time=$(date +%s)
    local validation_duration=$((validation_end_time - validation_start_time))
    
    echo ""
    echo "=============================================================================="
    echo "VALIDATION SUMMARY"
    echo "=============================================================================="
    echo "Validation completed at: $(date)"
    echo "Total validation time: ${validation_duration} seconds"
    echo "Total tests run: $TOTAL_TESTS"
    echo "Tests passed: $PASSED_TESTS"
    echo "Tests failed: $FAILED_TESTS"
    echo "Tests with warnings: $WARNING_TESTS"
    echo ""
    
    if [ $FAILED_TESTS -eq 0 ]; then
        log_success "🎉 VALIDATION SUCCESSFUL!"
        echo ""
        echo "System B is fully operational and ready for load testing."
        echo ""
        echo "Available Services:"
        echo "• API Service 1: http://localhost:8011/health"
        echo "• API Service 2: http://localhost:8012/health"
        echo "• API Service 3: http://localhost:8013/health"
        echo "• Health Service: http://localhost:8021/health"
        echo ""
        echo "Quick Test Commands:"
        echo "curl http://localhost:8011/health"
        echo "curl http://localhost:8011/api/data"
        echo "curl http://localhost:8021/health/services"
        echo ""
        return 0
    else
        log_error "❌ VALIDATION FAILED"
        echo ""
        echo "Failed tests:"
        for test in "${FAILED_TEST_NAMES[@]}"; do
            echo "• $test"
        done
        echo ""
        echo "Check the validation log for detailed information:"
        echo "$VALIDATION_LOG"
        echo ""
        echo "Service debugging commands:"
        echo "sudo systemctl status neuralmeter-api-8011"
        echo "sudo journalctl -u neuralmeter-api-8011 --no-pager"
        echo ""
        return 1
    fi
}

# Run main function
main "$@"