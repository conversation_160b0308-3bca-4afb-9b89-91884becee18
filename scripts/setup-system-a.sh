#!/bin/bash

# System A Setup Script - NeuralMeter CLI Execution Machine with GPU
# This script sets up a Linux machine with GPU capabilities to run the NeuralMotion CLI

set -e

echo "Setting up System A (NeuralMeter CLI Execution Machine with GPU)..."

# Update system packages
echo "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install essential tools
echo "Installing essential tools..."
sudo apt install -y curl wget htop iotop nethogs net-tools

# Detect GPU type and install appropriate drivers
echo "Detecting GPU hardware..."
if lspci | grep -i nvidia > /dev/null; then
    echo "NVIDIA GPU detected - installing NVIDIA drivers and CUDA..."
    
    # Install NVIDIA drivers
    sudo apt install -y nvidia-driver-535 nvidia-utils-535
    
    # Install CUDA toolkit 12.9
    wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/cuda-keyring_1.1-1_all.deb
    sudo dpkg -i cuda-keyring_1.1-1_all.deb
    sudo apt update
    sudo apt install -y cuda-toolkit-12-9
    
    # Set up CUDA environment
    if ! grep -q "export PATH=\$PATH:/usr/local/cuda-12.9/bin" ~/.bashrc; then
        echo 'export PATH=$PATH:/usr/local/cuda-12.9/bin' >> ~/.bashrc
        echo 'export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/local/cuda-12.9/targets/x86_64-linux/lib' >> ~/.bashrc
    fi
    
elif lspci | grep -i amd > /dev/null; then
    echo "AMD GPU detected - installing ROCm..."
    # Add ROCm repository and install
    sudo apt install -y wget gnupg2
    wget -q -O - https://repo.radeon.com/rocm/rocm.gpg.key | sudo apt-key add -
    echo 'deb [arch=amd64] https://repo.radeon.com/rocm/apt/debian ubuntu main' | sudo tee /etc/apt/sources.list.d/rocm.list
    sudo apt update
    sudo apt install -y rocm-hip-sdk
    
elif lspci | grep -i intel > /dev/null; then
    echo "Intel GPU detected - installing OpenCL..."
    sudo apt install -y intel-opencl-icd intel-media-va-driver-non-free
    
else
    echo "No supported GPU detected - proceeding with CPU-only setup"
fi

# Install monitoring tools
echo "Installing monitoring tools..."
sudo apt install -y nvtop  # GPU monitoring
sudo apt install -y stress-ng  # Stress testing

# Install Go for running the CLI
echo "Installing Go for CLI execution..."
GO_VERSION="1.21.5"
GO_ARCH="linux-amd64"
GO_TAR="go${GO_VERSION}.${GO_ARCH}.tar.gz"

if [ ! -d "/usr/local/go" ]; then
    echo "Downloading and installing Go ${GO_VERSION}..."
    wget -q "https://golang.org/dl/${GO_TAR}"
    sudo tar -C /usr/local -xzf "${GO_TAR}"
    rm "${GO_TAR}"
else
    echo "Go is already installed at /usr/local/go"
fi

# Set up Go environment variables in bashrc
echo "Setting up Go environment variables..."
if ! grep -q "export PATH=\$PATH:/usr/local/go/bin" ~/.bashrc; then
    echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
    echo 'export GOPATH=$HOME/go' >> ~/.bashrc
    echo 'export GOBIN=$GOPATH/bin' >> ~/.bashrc
    echo 'export PATH=$PATH:$GOBIN' >> ~/.bashrc
    echo "Go environment variables added to ~/.bashrc"
else
    echo "Go environment variables already present in ~/.bashrc"
fi

# Export Go environment variables for current session
echo "Exporting Go environment variables for current session..."
export PATH=$PATH:/usr/local/go/bin
export GOPATH=$HOME/go
export GOBIN=$GOPATH/bin
export PATH=$PATH:$GOBIN

# Verify Go installation and availability
echo "Verifying Go installation..."
if command -v go >/dev/null 2>&1; then
    echo "✓ Go is available in PATH: $(go version)"
else
    echo "✗ Go is not available in PATH. Checking installation..."
    if [ -f "/usr/local/go/bin/go" ]; then
        echo "Go binary exists at /usr/local/go/bin/go"
        echo "Current PATH: $PATH"
        echo "Please run: source ~/.bashrc"
        exit 1
    else
        echo "Go installation failed"
        exit 1
    fi
fi

# Create neuro user if it doesn't exist
if ! id "neuro" &>/dev/null; then
    echo "Creating neuro user..."
    sudo useradd -m -s /bin/bash neuro
    sudo usermod -aG sudo neuro
    echo "neuro:5FKWd9Dz" | sudo chpasswd
else
    echo "neuro user already exists"
fi

# Configure sudo access for neuro user (password required)
echo "Configuring sudo access for neuro user..."
if ! sudo grep -q "neuro ALL=(ALL) ALL" /etc/sudoers; then
    echo "neuro ALL=(ALL) ALL" | sudo tee -a /etc/sudoers
    echo "Sudo access configured for neuro user (password required)"
else
    echo "Sudo access already configured for neuro user"
fi

# Set up SSH for neuro user
echo "Setting up SSH for neuro user..."
sudo -u neuro mkdir -p /home/<USER>/.ssh
sudo -u neuro chmod 700 /home/<USER>/.ssh

# Create authorized_keys file (will be populated by Jenkins)
sudo -u neuro touch /home/<USER>/.ssh/authorized_keys
sudo -u neuro chmod 600 /home/<USER>/.ssh/authorized_keys

# Create CLI execution directories
echo "Creating CLI execution directories..."
sudo -u neuro mkdir -p /home/<USER>/cli
sudo -u neuro mkdir -p /home/<USER>/logs
sudo -u neuro mkdir -p /home/<USER>/results
sudo -u neuro chmod 755 /home/<USER>/cli /home/<USER>/logs /home/<USER>/results

# Test GPU detection
echo "Testing GPU detection..."
if command -v nvidia-smi &> /dev/null; then
    echo "NVIDIA GPU test:"
    nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader,nounits
elif command -v rocminfo &> /dev/null; then
    echo "AMD GPU test:"
    rocminfo | grep -A 5 "Device"
else
    echo "No GPU monitoring tools found - CPU-only mode"
fi

# Test Go environment
echo "Testing Go environment..."
cd /tmp
cat > test.go << 'EOF'
package main

import "fmt"

func main() {
    fmt.Println("Go environment test successful!")
}
EOF

go run test.go
rm test.go

# Final verification
echo ""
echo "=== System A Setup Verification ==="
echo "✓ Go version: $(go version)"
echo "✓ Go binary location: $(which go)"
echo "✓ Go environment variables:"
echo "  - GOPATH: $GOPATH"
echo "  - GOBIN: $GOBIN"
echo "  - Go in PATH: $(command -v go)"

# GPU verification
if command -v nvidia-smi &> /dev/null; then
    echo "✓ NVIDIA GPU detected:"
    nvidia-smi --query-gpu=name --format=csv,noheader,nounits
elif command -v rocminfo &> /dev/null; then
    echo "✓ AMD GPU detected"
else
    echo "⚠ No GPU detected - CLI will run in CPU-only mode"
fi

echo ""
echo "System A (CLI Execution Machine) setup completed successfully!"
echo ""
echo "Environment is ready for NeuralMeter CLI execution with GPU acceleration."
echo ""
echo "Next steps:"
echo "1. If running in a new shell, reload environment: source ~/.bashrc"
echo "2. Deploy NeuralMeter CLI binary to this system"
echo "3. Configure load test targets (System B)"
echo "4. Test CLI execution with GPU acceleration" 