#!/bin/bash
# setup-system-b-base.sh - Base System B Setup Script (from md_docs/systemB-setup.md)

set -euo pipefail

LOG_FILE="/var/log/neuralmeter-base-setup.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

echo "=== NeuralMeter System B Base Setup Started $(date) ==="

# Update system and install base packages
echo "Updating system packages..."
apt update && apt upgrade -y

echo "Installing base packages..."
apt install -y \
    curl wget git vim htop iotop \
    build-essential software-properties-common \
    certbot nginx-full \
    python3 python3-pip python3-venv \
    prometheus prometheus-node-exporter \
    haproxy \
    ssl-cert \
    jq ncdu tree \
    net-tools

echo "=== Base system setup completed $(date) ==="
echo "Installed packages:"
echo "- nginx-full: $(nginx -v 2>&1)"
echo "- haproxy: $(haproxy -v 2>&1 | head -1)"
echo "- prometheus: $(prometheus --version 2>&1 | head -1)"
echo "- python3: $(python3 --version)"