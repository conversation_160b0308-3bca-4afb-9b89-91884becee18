#!/bin/bash
# deploy-system-b-simple.sh - Simple System B deployment without complex sudo requirements

set -euo pipefail

echo "=== Simple System B Deployment Started $(date) ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create user directories (no sudo needed)
log_info "Setting up user directories..."
mkdir -p /home/<USER>/neuralmeter/{bin,logs,config}

# Deploy Python health service (no sudo needed for user space)
log_info "Deploying Python health service..."
cp health-server.py /home/<USER>/neuralmeter/bin/
chmod +x /home/<USER>/neuralmeter/bin/health-server.py

# Start health service in background (no systemd needed for testing)
log_info "Starting health service..."
pkill -f health-server.py || true  # Kill any existing instance
cd /home/<USER>/neuralmeter/bin
PORT=8021 python3 health-server.py > /home/<USER>/neuralmeter/logs/health.log 2>&1 &
sleep 2

# Test health service
if curl -s -f http://localhost:8021/health > /dev/null; then
    log_success "Health service is running on port 8021"
else
    log_error "Health service failed to start"
fi

# Deploy simple API services
log_info "Deploying API services..."
for port in 8011 8012 8013; do
    log_info "Starting API service on port $port..."
    pkill -f "PORT=$port" || true  # Kill any existing instance
    PORT=$port python3 simple-api-server.py > /home/<USER>/neuralmeter/logs/api-$port.log 2>&1 &
    sleep 1
    
    # Test API service
    if curl -s -f http://localhost:$port/health > /dev/null; then
        log_success "API service is running on port $port"
    else
        log_error "API service failed to start on port $port"
    fi
done

# Deploy WebSocket services (if websockets is available)
log_info "Checking WebSocket dependencies..."
if python3 -c "import websockets" 2>/dev/null; then
    log_info "Deploying WebSocket services..."
    for port in 8080 8081; do
        log_info "Starting WebSocket service on port $port..."
        pkill -f "PORT=$port.*websocket" || true  # Kill any existing instance
        PORT=$port python3 simple-websocket-server.py > /home/<USER>/neuralmeter/logs/ws-$port.log 2>&1 &
        sleep 2
        
        # Test WebSocket health (health server runs on port+1000)
        health_port=$((port + 1000))
        if curl -s -f http://localhost:$health_port/health > /dev/null; then
            log_success "WebSocket service is running on port $port (health on $health_port)"
        else
            log_error "WebSocket service failed to start on port $port"
        fi
    done
else
    log_info "WebSocket dependencies not available, installing..."
    pip3 install --user websockets
    log_info "WebSocket dependencies installed, services will be available after restart"
fi

# Basic nginx test (assuming it's already running from previous setup)
log_info "Testing existing nginx setup..."
if systemctl is-active nginx >/dev/null 2>&1; then
    log_success "Nginx is already running"
    
    # Test basic endpoints
    for port in 8001 8002 8003; do
        if curl -s -f http://localhost:$port/ > /dev/null; then
            log_success "Nginx responding on port $port"
        else
            log_info "Nginx not responding on port $port (may need configuration)"
        fi
    done
else
    log_info "Nginx not running (may need to be started with sudo)"
fi

# Summary
echo ""
log_info "=== DEPLOYMENT SUMMARY ==="
log_success "Simple System B deployment completed"
log_info "Services running in user space:"
echo "• Health Service: http://localhost:8021/health"
echo "• API Services: http://localhost:8011-8013/health"
echo "• WebSocket Services: ws://localhost:8080-8081/ (if dependencies available)"
echo "• Log files: /home/<USER>/neuralmeter/logs/"

log_info "To test the deployment:"
echo "curl http://localhost:8021/health"
echo "curl http://localhost:8011/health"
echo "curl http://localhost:8012/api/data"

echo ""
log_success "System B is ready for basic load testing from System A"