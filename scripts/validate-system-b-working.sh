#!/bin/bash
# validate-system-b-working.sh - Simple validation that actually works

set -euo pipefail

echo "=== System B Validation Started $(date) ==="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[PASS]${NC} $1"; }
log_error() { echo -e "${RED}[FAIL]${NC} $1"; }

# Debug info
log_info "Validation running as user: $(whoami)"
log_info "Timestamp: $(date)"
log_info "Hostname: $(hostname)"

failed_tests=0

# Test function
test_service() {
    local name="$1"
    local port="$2"
    local endpoint="$3"
    
    log_info "Testing $name..."
    
    # Check if service is running
    if systemctl is-active --quiet "neuralmeter-$name-$port" 2>/dev/null || systemctl is-active --quiet "neuralmeter-$name" 2>/dev/null; then
        log_success "$name service is running"
    else
        log_error "$name service is not running"
        ((failed_tests++))
        return 1
    fi
    
    # Check if port is listening
    if ss -tuln | grep -q ":$port "; then
        log_success "$name is listening on port $port"
    else
        log_error "$name is not listening on port $port"
        ((failed_tests++))
        return 1
    fi
    
    # Check HTTP endpoint
    if curl -s -f "http://localhost:$port$endpoint" >/dev/null; then
        log_success "$name HTTP endpoint is responding"
    else
        log_error "$name HTTP endpoint is not responding"
        ((failed_tests++))
        return 1
    fi
    
    return 0
}

# Test API services
log_info "=== Testing API Services ==="
for port in 8011 8012 8013; do
    test_service "api" "$port" "/health"
done

# Test health service
log_info "=== Testing Health Service ==="
test_service "health" "8021" "/health"

# Summary
echo ""
log_info "=== Validation Summary ==="
if [ $failed_tests -eq 0 ]; then
    log_success "🎉 All tests passed! System B is working correctly."
    echo ""
    echo "Working endpoints:"
    echo "• http://localhost:8011/health"
    echo "• http://localhost:8012/health" 
    echo "• http://localhost:8013/health"
    echo "• http://localhost:8021/health"
    echo ""
    echo "API endpoints:"
    echo "• http://localhost:8011/api/data"
    echo "• http://localhost:8012/api/data"
    echo "• http://localhost:8013/api/data"
    echo ""
    exit 0
else
    log_error "❌ $failed_tests tests failed"
    echo ""
    echo "Check service status with:"
    echo "sudo systemctl status neuralmeter-api-8011"
    echo "sudo systemctl status neuralmeter-health"
    echo ""
    exit 1
fi