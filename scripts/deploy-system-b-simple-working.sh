#!/bin/bash
# deploy-system-b-simple-working.sh - Simple System B deployment that actually works

set -euo pipefail

echo "=== Simple System B Deployment Started $(date) ==="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

# Check if running with sudo
if [ "$EUID" -ne 0 ]; then
    log_error "This script must be run with sudo"
    exit 1
fi

# Create neuro user if it doesn't exist
if ! id neuro >/dev/null 2>&1; then
    log_info "Creating neuro user..."
    useradd -m -s /bin/bash neuro
    usermod -aG sudo neuro
fi

# Create directories
log_info "Creating directories..."
mkdir -p /opt/neuralmeter/bin
mkdir -p /var/log/neuralmeter

# Install basic dependencies
log_info "Installing dependencies..."
apt update
apt install -y python3 python3-pip curl nginx

# Create simple API server
log_info "Creating API server..."
cat > /opt/neuralmeter/bin/simple-api-server.py << 'EOF'
#!/usr/bin/env python3
import json
import os
import time
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading

class APIHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            response = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "port": os.environ.get('PORT', '8011'),
                "service": "neuralmeter-api"
            }
            self.wfile.write(json.dumps(response).encode())
        elif self.path.startswith('/api/'):
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            response = {
                "data": f"API response from port {os.environ.get('PORT', '8011')}",
                "timestamp": int(time.time()),
                "endpoint": self.path
            }
            self.wfile.write(json.dumps(response).encode())
        else:
            self.send_response(200)
            self.send_header('Content-Type', 'text/html')
            self.end_headers()
            self.wfile.write(b'<h1>NeuralMeter API Server</h1>')
    
    def log_message(self, format, *args):
        pass  # Suppress logs

def main():
    port = int(os.environ.get('PORT', 8011))
    server = HTTPServer(('', port), APIHandler)
    print(f"API server starting on port {port}")
    server.serve_forever()

if __name__ == '__main__':
    main()
EOF

chmod +x /opt/neuralmeter/bin/simple-api-server.py

# Create health server
log_info "Creating health server..."
cat > /opt/neuralmeter/bin/health-server.py << 'EOF'
#!/usr/bin/env python3
import json
import psutil
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler

class HealthHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            response = {"status": "healthy", "timestamp": datetime.now().isoformat()}
            self.wfile.write(json.dumps(response).encode())
        elif self.path == '/health/system':
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            response = {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage('/').percent,
                "timestamp": datetime.now().isoformat()
            }
            self.wfile.write(json.dumps(response).encode())
        else:
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            response = {"status": "healthy", "service": "health-monitor"}
            self.wfile.write(json.dumps(response).encode())
    
    def log_message(self, format, *args):
        pass

def main():
    server = HTTPServer(('', 8021), HealthHandler)
    print("Health server starting on port 8021")
    server.serve_forever()

if __name__ == '__main__':
    main()
EOF

chmod +x /opt/neuralmeter/bin/health-server.py

# Install psutil for health monitoring
pip3 install psutil

# Set ownership
chown -R neuro:neuro /opt/neuralmeter

# Create systemd services for API servers
log_info "Creating API services..."
for port in 8011 8012 8013; do
    cat > /etc/systemd/system/neuralmeter-api-$port.service << EOF
[Unit]
Description=NeuralMeter API Server (Port $port)
After=network.target

[Service]
Type=simple
User=neuro
Group=neuro
Environment=PORT=$port
ExecStart=/usr/bin/python3 /opt/neuralmeter/bin/simple-api-server.py
Restart=always
RestartSec=3
WorkingDirectory=/opt/neuralmeter

[Install]
WantedBy=multi-user.target
EOF
done

# Create health service
log_info "Creating health service..."
cat > /etc/systemd/system/neuralmeter-health.service << 'EOF'
[Unit]
Description=NeuralMeter Health Service
After=network.target

[Service]
Type=simple
User=neuro
Group=neuro
ExecStart=/usr/bin/python3 /opt/neuralmeter/bin/health-server.py
Restart=always
RestartSec=3
WorkingDirectory=/opt/neuralmeter

[Install]
WantedBy=multi-user.target
EOF

# Start services
log_info "Starting services..."
systemctl daemon-reload

# Start API services
for port in 8011 8012 8013; do
    systemctl enable neuralmeter-api-$port
    systemctl restart neuralmeter-api-$port
    sleep 1
    if systemctl is-active --quiet neuralmeter-api-$port; then
        log_success "API service on port $port is running"
    else
        log_error "API service on port $port failed to start"
    fi
done

# Start health service
systemctl enable neuralmeter-health
systemctl restart neuralmeter-health
sleep 1
if systemctl is-active --quiet neuralmeter-health; then
    log_success "Health service is running"
else
    log_error "Health service failed to start"
fi

# Test services
log_info "Testing services..."
sleep 3

all_working=true

# Test API services
for port in 8011 8012 8013; do
    if curl -s -f "http://localhost:$port/health" >/dev/null; then
        log_success "API service on port $port is responding"
    else
        log_error "API service on port $port is not responding"
        all_working=false
    fi
done

# Test health service
if curl -s -f "http://localhost:8021/health" >/dev/null; then
    log_success "Health service is responding"
else
    log_error "Health service is not responding"
    all_working=false
fi

# Final result
echo ""
if $all_working; then
    log_success "🎉 System B deployment successful!"
    echo ""
    echo "Working services:"
    echo "• API Services: http://localhost:8011, 8012, 8013"
    echo "• Health Service: http://localhost:8021/health"
    echo ""
    echo "Test commands:"
    echo "curl http://localhost:8011/health"
    echo "curl http://localhost:8011/api/data"
    echo "curl http://localhost:8021/health"
    echo ""
    exit 0
else
    log_error "Some services failed to start"
    exit 1
fi