#!/bin/bash

# System B Cleanup Script
# This script removes unnecessary packages that were installed by the incorrect setup script

set -e

echo "Cleaning up System B - removing unnecessary packages..."

# First, fix any dpkg issues
echo "Fixing dpkg state..."
sudo dpkg --configure -a

# Function to check if package is installed
check_package() {
    local package=$1
    if dpkg -l | grep -q "^ii.*$package"; then
        echo "❌ WARNING: $package is still installed"
        return 1
    else
        echo "✅ $package is not installed"
        return 0
    fi
}

# Function to check if command exists
check_command() {
    local command=$1
    if command -v $command &> /dev/null; then
        echo "❌ WARNING: $command is still available"
        return 1
    else
        echo "✅ $command is not available"
        return 0
    fi
}

echo ""
echo "=== PRE-CLEANUP VERIFICATION ==="
echo "Checking for packages that should be removed..."

# Check for GPU-related packages
echo ""
echo "GPU-related packages:"
check_package "nvidia-driver-535" || true
check_package "nvidia-utils-535" || true
check_package "cuda-toolkit-12-3" || true
check_package "rocm-hip-sdk" || true
check_package "intel-opencl-icd" || true
check_package "nvtop" || true
check_package "stress-ng" || true

# Check for build tools
echo ""
echo "Build tools:"
check_package "build-essential" || true
check_package "pkg-config" || true
check_package "libssl-dev" || true

# Check for Go installation
echo ""
echo "Go installation:"
if [ -d "/usr/local/go" ]; then
    echo "❌ WARNING: Go installation still exists at /usr/local/go"
else
    echo "✅ Go installation not found"
fi

echo ""
echo "=== STARTING CLEANUP ==="

# Remove GPU-related packages
echo "Removing GPU-related packages..."
sudo apt remove -y nvidia-driver-535 nvidia-utils-535 2>/dev/null || echo "NVIDIA packages not found"
sudo apt remove -y cuda-toolkit-12-3 2>/dev/null || echo "CUDA toolkit not found"
sudo apt remove -y rocm-hip-sdk 2>/dev/null || echo "ROCm packages not found"
sudo apt remove -y intel-opencl-icd intel-media-va-driver-non-free 2>/dev/null || echo "Intel GPU packages not found"

# Remove GPU monitoring tools
echo "Removing GPU monitoring tools..."
sudo apt remove -y nvtop 2>/dev/null || echo "nvtop not found"
sudo apt remove -y stress-ng 2>/dev/null || echo "stress-ng not found"

# Remove build tools (not needed for target services)
echo "Removing build tools..."
sudo apt remove -y build-essential 2>/dev/null || echo "build-essential not found"
sudo apt remove -y pkg-config 2>/dev/null || echo "pkg-config not found"
sudo apt remove -y libssl-dev 2>/dev/null || echo "libssl-dev not found"

# Remove Go (not needed for target services)
echo "Removing Go installation..."
if [ -d "/usr/local/go" ]; then
    sudo rm -rf /usr/local/go
    echo "Go installation removed"
fi

# Clean up Go environment variables from bashrc
echo "Cleaning up Go environment variables..."
sed -i '/export PATH=\$PATH:\/usr\/local\/go\/bin/d' ~/.bashrc
sed -i '/export GOPATH=\$HOME\/go/d' ~/.bashrc
sed -i '/export GOBIN=\$GOPATH\/bin/d' ~/.bashrc

# Remove CUDA environment variables
echo "Cleaning up CUDA environment variables..."
sed -i '/export PATH=\$PATH:\/usr\/local\/cuda\/bin/d' ~/.bashrc
sed -i '/export LD_LIBRARY_PATH=\$LD_LIBRARY_PATH:\/usr\/local\/cuda\/lib64/d' ~/.bashrc

# Clean up package cache
echo "Cleaning up package cache..."
sudo apt autoremove -y
sudo apt autoclean

# Remove any CUDA repositories
echo "Removing CUDA repositories..."
sudo rm -f /etc/apt/sources.list.d/cuda*
sudo rm -f /etc/apt/sources.list.d/rocm*

# Update package lists
echo "Updating package lists..."
sudo apt update

echo ""
echo "=== POST-CLEANUP VERIFICATION ==="
echo "Verifying that cleanup was successful..."

# Verify GPU packages are removed
echo ""
echo "GPU-related packages verification:"
check_package "nvidia-driver-535"
check_package "nvidia-utils-535"
check_package "cuda-toolkit-12-3"
check_package "rocm-hip-sdk"
check_package "intel-opencl-icd"
check_package "nvtop"
check_package "stress-ng"

# Verify build tools are removed
echo ""
echo "Build tools verification:"
check_package "build-essential"
check_package "pkg-config"
check_package "libssl-dev"

# Verify commands are not available
echo ""
echo "Command verification:"
check_command "nvidia-smi"
check_command "nvcc"
check_command "rocminfo"
check_command "nvtop"
check_command "stress-ng"
check_command "go"

# Verify Go installation is removed
echo ""
echo "Go installation verification:"
if [ -d "/usr/local/go" ]; then
    echo "❌ ERROR: Go installation still exists at /usr/local/go"
    exit 1
else
    echo "✅ Go installation successfully removed"
fi

# Verify environment variables are cleaned
echo ""
echo "Environment variables verification:"
if grep -q "export PATH.*go" ~/.bashrc; then
    echo "❌ WARNING: Go environment variables still in ~/.bashrc"
else
    echo "✅ Go environment variables cleaned from ~/.bashrc"
fi

if grep -q "export PATH.*cuda" ~/.bashrc; then
    echo "❌ WARNING: CUDA environment variables still in ~/.bashrc"
else
    echo "✅ CUDA environment variables cleaned from ~/.bashrc"
fi

echo ""
echo "=== CLEANUP SUMMARY ==="
echo "System B cleanup completed!"
echo ""
echo "Removed packages:"
echo "- GPU drivers and CUDA/ROCm/OpenCL"
echo "- GPU monitoring tools"
echo "- Build tools"
echo "- Go installation"
echo ""
echo "Next steps:"
echo "1. Run the corrected setup script: sudo ./setup-system-b.sh"
echo "2. This will install only the necessary target services (nginx, etc.)"
echo ""
echo "If any packages are still showing as installed, run:"
echo "sudo apt purge <package-name>"
echo "sudo apt autoremove" 