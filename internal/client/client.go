// Package client provides HTTP client functionality for load testing
package client

import (
	"bytes"
	"compress/gzip"
	"context"
	"crypto/tls"
	"encoding/base64"
	"fmt"
	"io"
	"math/rand"
	"net"
	"net/http"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

// Compression level constants
const (
	CompressionLevelDefault = 5
	CompressionLevelBestSpeed = 1
	CompressionLevelBestCompression = 9
)

// CompressionConfig holds compression configuration
type compressionConfig struct {
	level int // Compression level (1-9)
}

const (
	// AuthTypeBasic represents HTTP Basic Authentication (username/password)
	AuthTypeBasic AuthType = iota
	// AuthTypeBearer represents Bearer token authentication (e.g., JWT, OAuth)
	AuthTypeBearer
	// AuthTypeCustom represents custom header-based authentication
	AuthTypeCustom
)

// String returns the string representation of the authentication type
func (a AuthType) String() string {
	switch a {
	case AuthTypeBasic:
		return "Basic"
	case AuthTypeBearer:
		return "Bearer"
	case AuthTypeCustom:
		return "Custom"
	default:
		return "Unknown"
	}
}

// AuthConfig holds authentication configuration for HTTP requests
type AuthConfig struct {
	// Type specifies the authentication method to use
	Type AuthType `json:"type"`

	// Enabled controls whether authentication is active
	Enabled bool `json:"enabled"`

	// Basic authentication fields
	Username string `json:"username,omitempty"`
	Password string `json:"password,omitempty"`

	// Bearer token authentication field
	Token string `json:"token,omitempty"`

	// Custom headers for flexible authentication
	CustomHeaders map[string]string `json:"custom_headers,omitempty"`

	// Mutex for thread-safe access
	mu sync.RWMutex `json:"-"`
}

// NewBasicAuth creates a new AuthConfig for HTTP Basic authentication
func NewBasicAuth(username, password string) *AuthConfig {
	return &AuthConfig{
		Type:     AuthTypeBasic,
		Enabled:  true,
		Username: username,
		Password: password,
	}
}

// NewBearerAuth creates a new AuthConfig for Bearer token authentication
func NewBearerAuth(token string) *AuthConfig {
	return &AuthConfig{
		Type:    AuthTypeBearer,
		Enabled: true,
		Token:   token,
	}
}

// NewCustomAuth creates a new AuthConfig for custom header-based authentication
func NewCustomAuth(headers map[string]string) *AuthConfig {
	return &AuthConfig{
		Type:          AuthTypeCustom,
		Enabled:       true,
		CustomHeaders: headers,
	}
}

// Validate checks if the authentication configuration is valid
func (ac *AuthConfig) Validate() error {
	if ac == nil {
		return fmt.Errorf("auth config cannot be nil")
	}

	ac.mu.RLock()
	defer ac.mu.RUnlock()

	if !ac.Enabled {
		return nil // No validation needed if disabled
	}

	switch ac.Type {
	case AuthTypeBasic:
		if ac.Username == "" || ac.Password == "" {
			return fmt.Errorf("basic auth requires both username and password")
		}
	case AuthTypeBearer:
		if ac.Token == "" {
			return fmt.Errorf("bearer auth requires a token")
		}
	case AuthTypeCustom:
		if len(ac.CustomHeaders) == 0 {
			return fmt.Errorf("custom auth requires at least one header")
		}
		// Validate header names are not empty
		for name, value := range ac.CustomHeaders {
			if name == "" {
				return fmt.Errorf("custom auth header name cannot be empty")
			}
			if value == "" {
				return fmt.Errorf("custom auth header value for '%s' cannot be empty", name)
			}
		}
	default:
		return fmt.Errorf("unsupported auth type: %s", ac.Type)
	}

	return nil
}

// Clone creates a deep copy of the AuthConfig for thread-safe operations
func (ac *AuthConfig) Clone() *AuthConfig {
	if ac == nil {
		return nil
	}

	ac.mu.RLock()
	defer ac.mu.RUnlock()

	clone := &AuthConfig{
		Type:     ac.Type,
		Enabled:  ac.Enabled,
		Username: ac.Username,
		Password: ac.Password,
		Token:    ac.Token,
	}

	// Deep copy custom headers
	if ac.CustomHeaders != nil {
		clone.CustomHeaders = make(map[string]string, len(ac.CustomHeaders))
		for k, v := range ac.CustomHeaders {
			clone.CustomHeaders[k] = v
		}
	}

	return clone
}

// ApplyToHeaders applies the authentication configuration to the provided headers map
func (ac *AuthConfig) ApplyToHeaders(headers map[string]string) error {
	if ac == nil || !ac.Enabled {
		return nil
	}

	if err := ac.Validate(); err != nil {
		return fmt.Errorf("invalid auth config: %w", err)
	}

	ac.mu.RLock()
	defer ac.mu.RUnlock()

	switch ac.Type {
	case AuthTypeBasic:
		// Create base64 encoded credentials for Basic auth
		credentials := fmt.Sprintf("%s:%s", ac.Username, ac.Password)
		encodedCredentials := fmt.Sprintf("Basic %s",
			base64.StdEncoding.EncodeToString([]byte(credentials)))
		headers["Authorization"] = encodedCredentials

	case AuthTypeBearer:
		headers["Authorization"] = fmt.Sprintf("Bearer %s", ac.Token)

	case AuthTypeCustom:
		for name, value := range ac.CustomHeaders {
			headers[name] = value
		}
	}

	return nil
}

// Update allows thread-safe updates to the authentication configuration
func (ac *AuthConfig) Update(updater func(*AuthConfig)) {
	if ac == nil {
		return
	}

	ac.mu.Lock()
	defer ac.mu.Unlock()

	updater(ac)
}

// SetEnabled enables or disables authentication
func (ac *AuthConfig) SetEnabled(enabled bool) {
	if ac == nil {
		return
	}

	ac.mu.Lock()
	defer ac.mu.Unlock()

	ac.Enabled = enabled
}

// IsEnabled returns whether authentication is enabled
func (ac *AuthConfig) IsEnabled() bool {
	if ac == nil {
		return false
	}

	ac.mu.RLock()
	defer ac.mu.RUnlock()

	return ac.Enabled
}

// Config represents the configuration for the HTTP client
type Config struct {
	// HTTP version to use (1.1 or 2.0)
	HTTPVersion string `json:"http_version,omitempty"`

	// Enable HTTP/1.1 request pipelining
	EnablePipelining bool `json:"enable_pipelining,omitempty"`

	// Enable compression (gzip, deflate)
	EnableCompression bool `json:"enable_compression,omitempty"`

	// Compression level for gzip (1-9, default 5)
	CompressionLevel int `json:"compression_level,omitempty"`

	// Other existing config fields...
}

// HTTPClient represents the HTTP client for load testing
type HTTPClient struct {
	client            *http.Client
	config            *Config
	transport         *http.Transport
	stats             *ConnectionStats
	httpMethodStats   *HTTPMethodStats
	retryConfig       *RetryConfig
	authConfig        *AuthConfig
	mu                sync.RWMutex
	tlsConfig         *tls.Config                           // Added for HTTP/2 support
	pipelinedRequests map[*http.Request]chan *http.Response // Track pipelined requests
	compression       *compressionConfig                    // Compression configuration
}

// ConnectionStats represents connection-level statistics for monitoring and debugging
type ConnectionStats struct {
	// Connection reuse metrics
	TotalRequests     int64 // Total HTTP requests made
	ConnectionsReused int64 // Number of times connections were reused
	NewConnections    int64 // Number of new connections created

	// Connection pool state
	ActiveConnections int64 // Currently active connections
	IdleConnections   int64 // Currently idle connections

	// Performance metrics
	AvgConnectionTime time.Duration // Average time to establish new connections
	AvgReuseTime      time.Duration // Average time to reuse existing connections

	// Error tracking
	ConnectionErrors int64 // Connection-related errors
	TimeoutErrors    int64 // Timeout-related errors

	// Timestamps
	LastConnectionTime time.Time // Last time a new connection was created
	LastReuseTime      time.Time // Last time a connection was reused
}

// HTTPMethodStats represents detailed HTTP method-level statistics
type HTTPMethodStats struct {
	// Request counts by method
	GetRequests     int64 `json:"get_requests"`
	PostRequests    int64 `json:"post_requests"`
	PutRequests     int64 `json:"put_requests"`
	DeleteRequests  int64 `json:"delete_requests"`
	HeadRequests    int64 `json:"head_requests"`
	OptionsRequests int64 `json:"options_requests"`
	PatchRequests   int64 `json:"patch_requests"`
	OtherRequests   int64 `json:"other_requests"`

	// Response status code tracking
	Status2xx int64 `json:"status_2xx"` // Success responses
	Status3xx int64 `json:"status_3xx"` // Redirection responses
	Status4xx int64 `json:"status_4xx"` // Client error responses
	Status5xx int64 `json:"status_5xx"` // Server error responses

	// Response time metrics
	TotalResponseTime time.Duration `json:"total_response_time"`
	MinResponseTime   time.Duration `json:"min_response_time"`
	MaxResponseTime   time.Duration `json:"max_response_time"`
	AvgResponseTime   time.Duration `json:"avg_response_time"`

	// Throughput metrics
	TotalBytesReceived int64 `json:"total_bytes_received"`
	TotalBytesSent     int64 `json:"total_bytes_sent"`

	// Error tracking
	NetworkErrors     int64 `json:"network_errors"`
	TimeoutErrors     int64 `json:"timeout_errors"` // Total timeout errors (for backward compatibility)
	RetryAttempts     int64 `json:"retry_attempts"`
	SuccessfulRetries int64 `json:"successful_retries"`
	FailedRetries     int64 `json:"failed_retries"`

	// Granular timeout tracking
	DialTimeouts           int64 `json:"dial_timeouts"`            // Connection establishment timeouts
	TLSHandshakeTimeouts   int64 `json:"tls_handshake_timeouts"`   // TLS handshake timeouts
	ResponseHeaderTimeouts int64 `json:"response_header_timeouts"` // Response header wait timeouts
	TotalRequestTimeouts   int64 `json:"total_request_timeouts"`   // Overall request timeouts

	// Timeout distribution tracking
	TimeoutDistribution map[string]int64 `json:"timeout_distribution"` // Histogram of timeout values

	// Timeout recovery metrics
	TimeoutRecoveries      int64         `json:"timeout_recoveries"`        // Successful retries after timeout
	TimeoutRecoveryRate    float64       `json:"timeout_recovery_rate"`     // Rate of successful timeout recoveries
	AvgTimeoutRecoveryTime time.Duration `json:"avg_timeout_recovery_time"` // Average time to recover from timeout

	// Timestamps
	FirstRequestTime time.Time `json:"first_request_time"`
	LastRequestTime  time.Time `json:"last_request_time"`

	// Mutex for thread-safe updates to non-atomic fields
	mu *sync.RWMutex `json:"-"`
}

// NewHTTPMethodStats creates a new HTTPMethodStats instance with initialized timeout distribution
func NewHTTPMethodStats() *HTTPMethodStats {
	return &HTTPMethodStats{
		TimeoutDistribution: make(map[string]int64),
		MinResponseTime:     time.Duration(^uint64(0) >> 1), // Initialize to max duration
		mu:                  &sync.RWMutex{},
	}
}

// HTTPMetrics represents comprehensive HTTP client metrics
type HTTPMetrics struct {
	ConnectionStats ConnectionStats `json:"connection_stats"`
	HTTPMethodStats HTTPMethodStats `json:"http_method_stats"`
	StartTime       time.Time       `json:"start_time"`
	LastUpdatedTime time.Time       `json:"last_updated_time"`
}

// TimeoutStrategyType represents the type of timeout strategy to use
type TimeoutStrategyType int

const (
	TimeoutStrategyConservative TimeoutStrategyType = iota // Conservative timeouts with longer delays
	TimeoutStrategyBalanced                                // Balanced timeouts for general use
	TimeoutStrategyAggressive                              // Aggressive timeouts for high-performance scenarios
	TimeoutStrategyCustom                                  // Custom user-defined timeout strategy
)

// String returns the string representation of the timeout strategy type
func (t TimeoutStrategyType) String() string {
	switch t {
	case TimeoutStrategyConservative:
		return "Conservative"
	case TimeoutStrategyBalanced:
		return "Balanced"
	case TimeoutStrategyAggressive:
		return "Aggressive"
	case TimeoutStrategyCustom:
		return "Custom"
	default:
		return "Unknown"
	}
}

// TimeoutPolicy defines timeout escalation policies and operation-specific settings
type TimeoutPolicy struct {
	// Base timeout multipliers for different operation types
	BaseTimeoutMultiplier    float64 // Base multiplier for all operations (default: 1.0)
	ReadOperationMultiplier  float64 // Multiplier for read operations (GET, HEAD, OPTIONS)
	WriteOperationMultiplier float64 // Multiplier for write operations (POST, PUT, PATCH, DELETE)

	// Retry escalation settings
	RetryEscalationFactor     float64 // Factor to increase timeout on each retry (default: 1.5)
	MaxRetryTimeoutMultiplier float64 // Maximum timeout multiplier for retries (default: 5.0)

	// Jitter settings
	EnableJitter bool    // Enable random jitter to prevent thundering herd
	JitterFactor float64 // Jitter factor (0.0 to 1.0, default: 0.1)

	// Operation priority settings
	PriorityMultipliers map[string]float64 // Custom multipliers for specific operations
}

// TimeoutStrategy encapsulates timeout strategy configuration
type TimeoutStrategy struct {
	Type            TimeoutStrategyType     // Strategy type
	Policy          TimeoutPolicy           // Policy configuration for the strategy
	DynamicAdjuster *DynamicTimeoutAdjuster // Optional dynamic timeout adjuster
}

// DynamicTimeoutConfig defines configuration for adaptive timeout adjustment
type DynamicTimeoutConfig struct {
	// Enable/disable dynamic adjustment
	Enabled bool // Enable dynamic timeout adjustment

	// Adjustment sensitivity settings
	AdjustmentSensitivity float64 // How quickly to adjust timeouts (0.1 = conservative, 1.0 = aggressive)
	DampeningFactor       float64 // Factor to dampen rapid changes (0.1 = heavy dampening, 1.0 = no dampening)

	// Performance thresholds for adjustment
	TargetSuccessRate  float64       // Target success rate (0.95 = 95% success rate)
	ErrorRateThreshold float64       // Error rate threshold to trigger adjustment (0.1 = 10% error rate)
	ResponseTimeP95    time.Duration // Target P95 response time
	ResponseTimeP99    time.Duration // Target P99 response time

	// Adjustment limits
	MinTimeoutMultiplier float64 // Minimum timeout multiplier (0.5 = 50% of base)
	MaxTimeoutMultiplier float64 // Maximum timeout multiplier (5.0 = 500% of base)
	MaxAdjustmentStep    float64 // Maximum adjustment per step (0.2 = 20% change)

	// Moving average settings
	SampleWindow     int           // Number of recent samples to consider
	MinSampleCount   int           // Minimum samples before making adjustments
	AdjustmentWindow time.Duration // Time window for adjustment decisions
}

// ResponseTimeTracker tracks response time percentiles for dynamic adjustment
type ResponseTimeTracker struct {
	samples    []time.Duration // Ring buffer of recent response times
	sampleIdx  int             // Current index in the ring buffer
	sampleSize int             // Current number of samples
	maxSamples int             // Maximum number of samples to track
	mu         sync.RWMutex    // Mutex for thread-safe access
}

// DynamicTimeoutAdjuster manages adaptive timeout adjustment based on performance metrics
type DynamicTimeoutAdjuster struct {
	config              DynamicTimeoutConfig // Configuration for dynamic adjustment
	responseTimeTracker *ResponseTimeTracker // Response time percentile tracker
	currentMultiplier   float64              // Current timeout multiplier
	lastAdjustment      time.Time            // Last time adjustment was made
	adjustmentCount     int64                // Number of adjustments made
	mu                  sync.RWMutex         // Mutex for thread-safe access
}

// TimeoutMonitoringConfig defines configuration for timeout monitoring and alerting
type TimeoutMonitoringConfig struct {
	// Enable/disable timeout monitoring
	Enabled bool `json:"enabled"`

	// Alert thresholds by timeout type (percentage rates that trigger alerts)
	AlertThresholds map[string]float64 `json:"alert_thresholds"` // e.g., "dial": 0.05 for 5% dial timeout rate

	// Timeout distribution tracking
	DistributionBuckets []time.Duration `json:"distribution_buckets"` // Buckets for timeout value histogram

	// Monitoring window settings
	MonitoringWindow time.Duration `json:"monitoring_window"` // Time window for threshold calculations
	MinSampleSize    int           `json:"min_sample_size"`   // Minimum samples before triggering alerts

	// Alert callback function (optional)
	AlertCallback func(timeoutType string, currentRate float64, threshold float64) `json:"-"`
}

// TimeoutType represents different types of timeouts for categorization
type TimeoutType int

const (
	TimeoutTypeDial TimeoutType = iota
	TimeoutTypeTLS
	TimeoutTypeResponseHeader
	TimeoutTypeTotalRequest
	TimeoutTypeUnknown
)

// String returns the string representation of the timeout type
func (t TimeoutType) String() string {
	switch t {
	case TimeoutTypeDial:
		return "dial"
	case TimeoutTypeTLS:
		return "tls"
	case TimeoutTypeResponseHeader:
		return "response_header"
	case TimeoutTypeTotalRequest:
		return "total_request"
	default:
		return "unknown"
	}
}

// Validate checks if the timeout strategy configuration is valid
func (ts *TimeoutStrategy) Validate() error {
	// Validate policy values
	if ts.Policy.BaseTimeoutMultiplier <= 0 {
		return fmt.Errorf("BaseTimeoutMultiplier must be positive, got %f", ts.Policy.BaseTimeoutMultiplier)
	}
	if ts.Policy.ReadOperationMultiplier <= 0 {
		return fmt.Errorf("ReadOperationMultiplier must be positive, got %f", ts.Policy.ReadOperationMultiplier)
	}
	if ts.Policy.WriteOperationMultiplier <= 0 {
		return fmt.Errorf("WriteOperationMultiplier must be positive, got %f", ts.Policy.WriteOperationMultiplier)
	}
	if ts.Policy.RetryEscalationFactor <= 1.0 {
		return fmt.Errorf("RetryEscalationFactor must be greater than 1.0, got %f", ts.Policy.RetryEscalationFactor)
	}
	if ts.Policy.MaxRetryTimeoutMultiplier <= 1.0 {
		return fmt.Errorf("MaxRetryTimeoutMultiplier must be greater than 1.0, got %f", ts.Policy.MaxRetryTimeoutMultiplier)
	}
	if ts.Policy.JitterFactor < 0 || ts.Policy.JitterFactor > 1.0 {
		return fmt.Errorf("JitterFactor must be between 0.0 and 1.0, got %f", ts.Policy.JitterFactor)
	}

	// Validate priority multipliers
	for operation, multiplier := range ts.Policy.PriorityMultipliers {
		if multiplier <= 0 {
			return fmt.Errorf("PriorityMultiplier for operation '%s' must be positive, got %f", operation, multiplier)
		}
	}

	// Validate dynamic timeout config if present
	if ts.DynamicAdjuster != nil {
		if err := ts.DynamicAdjuster.config.Validate(); err != nil {
			return fmt.Errorf("invalid dynamic timeout config: %w", err)
		}
	}

	return nil
}

// Validate checks if the dynamic timeout configuration is valid
func (dtc *DynamicTimeoutConfig) Validate() error {
	if dtc.AdjustmentSensitivity <= 0 || dtc.AdjustmentSensitivity > 2.0 {
		return fmt.Errorf("AdjustmentSensitivity must be between 0.0 and 2.0, got %f", dtc.AdjustmentSensitivity)
	}
	if dtc.DampeningFactor <= 0 || dtc.DampeningFactor > 1.0 {
		return fmt.Errorf("DampeningFactor must be between 0.0 and 1.0, got %f", dtc.DampeningFactor)
	}
	if dtc.TargetSuccessRate <= 0 || dtc.TargetSuccessRate > 1.0 {
		return fmt.Errorf("TargetSuccessRate must be between 0.0 and 1.0, got %f", dtc.TargetSuccessRate)
	}
	if dtc.ErrorRateThreshold < 0 || dtc.ErrorRateThreshold > 1.0 {
		return fmt.Errorf("ErrorRateThreshold must be between 0.0 and 1.0, got %f", dtc.ErrorRateThreshold)
	}
	if dtc.MinTimeoutMultiplier <= 0 {
		return fmt.Errorf("MinTimeoutMultiplier must be positive, got %f", dtc.MinTimeoutMultiplier)
	}
	if dtc.MaxTimeoutMultiplier <= dtc.MinTimeoutMultiplier {
		return fmt.Errorf("MaxTimeoutMultiplier (%f) must be greater than MinTimeoutMultiplier (%f)", dtc.MaxTimeoutMultiplier, dtc.MinTimeoutMultiplier)
	}
	if dtc.MaxAdjustmentStep <= 0 || dtc.MaxAdjustmentStep > 1.0 {
		return fmt.Errorf("MaxAdjustmentStep must be between 0.0 and 1.0, got %f", dtc.MaxAdjustmentStep)
	}
	if dtc.SampleWindow <= 0 {
		return fmt.Errorf("SampleWindow must be positive, got %d", dtc.SampleWindow)
	}
	if dtc.MinSampleCount <= 0 || dtc.MinSampleCount > dtc.SampleWindow {
		return fmt.Errorf("MinSampleCount must be positive and <= SampleWindow, got %d (SampleWindow: %d)", dtc.MinSampleCount, dtc.SampleWindow)
	}
	if dtc.AdjustmentWindow <= 0 {
		return fmt.Errorf("AdjustmentWindow must be positive, got %v", dtc.AdjustmentWindow)
	}
	return nil
}

type Config struct {
	// Connection pool settings
	MaxIdleConns        int           // Maximum number of idle connections across all hosts
	MaxIdleConnsPerHost int           // Maximum number of idle connections per host
	MaxConnsPerHost     int           // Maximum number of total connections per host (0 = unlimited)
	IdleConnTimeout     time.Duration // How long an idle connection remains idle before closing

	// Timeout settings
	DialTimeout           time.Duration // Maximum time to wait for a dial to complete
	ResponseHeaderTimeout time.Duration // Maximum time to wait for response headers
	TLSHandshakeTimeout   time.Duration // Maximum time to wait for TLS handshake
	ExpectContinueTimeout time.Duration // Maximum time to wait for 100-continue response

	// Timeout strategy configuration
	TimeoutStrategy TimeoutStrategy // Timeout strategy and escalation policies

	// Timeout monitoring configuration
	TimeoutMonitoring TimeoutMonitoringConfig // Timeout monitoring and alerting configuration

	// Performance settings
	DisableKeepAlives  bool // Disable HTTP keep-alive connections
	DisableCompression bool // Disable automatic gzip decompression

	// Protocol settings
	EnableHTTP2 bool // Enable HTTP/2 support
}

// NewConservativeTimeoutStrategy creates a conservative timeout strategy
func NewConservativeTimeoutStrategy() TimeoutStrategy {
	return TimeoutStrategy{
		Type: TimeoutStrategyConservative,
		Policy: TimeoutPolicy{
			BaseTimeoutMultiplier:     1.5, // 50% longer base timeouts
			ReadOperationMultiplier:   1.0, // Standard for read operations
			WriteOperationMultiplier:  1.2, // 20% longer for write operations
			RetryEscalationFactor:     1.8, // Generous escalation
			MaxRetryTimeoutMultiplier: 8.0, // High maximum multiplier
			EnableJitter:              true,
			JitterFactor:              0.15, // 15% jitter
			PriorityMultipliers:       make(map[string]float64),
		},
	}
}

// NewBalancedTimeoutStrategy creates a balanced timeout strategy
func NewBalancedTimeoutStrategy() TimeoutStrategy {
	return TimeoutStrategy{
		Type: TimeoutStrategyBalanced,
		Policy: TimeoutPolicy{
			BaseTimeoutMultiplier:     1.0, // Standard base timeouts
			ReadOperationMultiplier:   1.0, // Standard for read operations
			WriteOperationMultiplier:  1.1, // 10% longer for write operations
			RetryEscalationFactor:     1.5, // Moderate escalation
			MaxRetryTimeoutMultiplier: 5.0, // Moderate maximum multiplier
			EnableJitter:              true,
			JitterFactor:              0.1, // 10% jitter
			PriorityMultipliers:       make(map[string]float64),
		},
	}
}

// NewAggressiveTimeoutStrategy creates an aggressive timeout strategy
func NewAggressiveTimeoutStrategy() TimeoutStrategy {
	return TimeoutStrategy{
		Type: TimeoutStrategyAggressive,
		Policy: TimeoutPolicy{
			BaseTimeoutMultiplier:     0.7, // 30% shorter base timeouts
			ReadOperationMultiplier:   0.8, // Shorter for read operations
			WriteOperationMultiplier:  1.0, // Standard for write operations
			RetryEscalationFactor:     1.3, // Modest escalation
			MaxRetryTimeoutMultiplier: 3.0, // Lower maximum multiplier
			EnableJitter:              true,
			JitterFactor:              0.05, // 5% jitter
			PriorityMultipliers:       make(map[string]float64),
		},
	}
}

// NewCustomTimeoutStrategy creates a custom timeout strategy with user-defined policy
func NewCustomTimeoutStrategy(policy TimeoutPolicy) TimeoutStrategy {
	return TimeoutStrategy{
		Type:   TimeoutStrategyCustom,
		Policy: policy,
	}
}

// NewTimeoutStrategyWithDynamicAdjustment creates a timeout strategy with dynamic adjustment
func NewTimeoutStrategyWithDynamicAdjustment(strategyType TimeoutStrategyType, dynamicConfig DynamicTimeoutConfig) TimeoutStrategy {
	var policy TimeoutPolicy

	switch strategyType {
	case TimeoutStrategyConservative:
		policy = TimeoutPolicy{
			BaseTimeoutMultiplier:     1.5,
			ReadOperationMultiplier:   1.0,
			WriteOperationMultiplier:  1.2,
			RetryEscalationFactor:     2.0,
			MaxRetryTimeoutMultiplier: 8.0,
			EnableJitter:              true,
			JitterFactor:              0.1,
		}
	case TimeoutStrategyBalanced:
		policy = TimeoutPolicy{
			BaseTimeoutMultiplier:     1.0,
			ReadOperationMultiplier:   1.0,
			WriteOperationMultiplier:  1.1,
			RetryEscalationFactor:     1.5,
			MaxRetryTimeoutMultiplier: 5.0,
			EnableJitter:              true,
			JitterFactor:              0.1,
		}
	case TimeoutStrategyAggressive:
		policy = TimeoutPolicy{
			BaseTimeoutMultiplier:     0.8,
			ReadOperationMultiplier:   1.0,
			WriteOperationMultiplier:  1.0,
			RetryEscalationFactor:     1.2,
			MaxRetryTimeoutMultiplier: 3.0,
			EnableJitter:              true,
			JitterFactor:              0.05,
		}
	default:
		// Default to balanced
		policy = TimeoutPolicy{
			BaseTimeoutMultiplier:     1.0,
			ReadOperationMultiplier:   1.0,
			WriteOperationMultiplier:  1.1,
			RetryEscalationFactor:     1.5,
			MaxRetryTimeoutMultiplier: 5.0,
			EnableJitter:              true,
			JitterFactor:              0.1,
		}
	}

	return TimeoutStrategy{
		Type:            strategyType,
		Policy:          policy,
		DynamicAdjuster: NewDynamicTimeoutAdjuster(dynamicConfig),
	}
}

// NewAdaptiveTimeoutStrategy creates a timeout strategy with default dynamic adjustment
func NewAdaptiveTimeoutStrategy(strategyType TimeoutStrategyType) TimeoutStrategy {
	return NewTimeoutStrategyWithDynamicAdjustment(strategyType, DefaultDynamicTimeoutConfig())
}

// DefaultTimeoutMonitoringConfig returns the default timeout monitoring configuration
func DefaultTimeoutMonitoringConfig() TimeoutMonitoringConfig {
	return TimeoutMonitoringConfig{
		Enabled: true,
		AlertThresholds: map[string]float64{
			"dial":            0.05, // 5% dial timeout rate
			"tls":             0.03, // 3% TLS timeout rate
			"response_header": 0.10, // 10% response header timeout rate
			"total_request":   0.15, // 15% total request timeout rate
		},
		DistributionBuckets: []time.Duration{
			100 * time.Millisecond,
			500 * time.Millisecond,
			1 * time.Second,
			2 * time.Second,
			5 * time.Second,
			10 * time.Second,
			30 * time.Second,
			60 * time.Second,
		},
		MonitoringWindow: 5 * time.Minute,
		MinSampleSize:    100,
		AlertCallback:    nil, // Can be set by user
	}
}

func DefaultConfig() *Config {
	return &Config{
		// Connection pool settings - optimized for high throughput
		MaxIdleConns:        200,              // Increased from 100 for better connection reuse
		MaxIdleConnsPerHost: 50,               // Increased from 10 for better per-host throughput
		MaxConnsPerHost:     100,              // Limit total connections per host to prevent resource exhaustion
		IdleConnTimeout:     90 * time.Second, // Standard HTTP keep-alive timeout

		// Timeout settings - balanced for performance and reliability
		DialTimeout:           10 * time.Second, // Connection establishment timeout
		ResponseHeaderTimeout: 30 * time.Second, // Response header timeout
		TLSHandshakeTimeout:   10 * time.Second, // TLS handshake timeout
		ExpectContinueTimeout: 1 * time.Second,  // HTTP/1.1 100-continue optimization

		// Timeout strategy - balanced approach
		TimeoutStrategy: NewBalancedTimeoutStrategy(),

		// Timeout monitoring - enable comprehensive monitoring
		TimeoutMonitoring: DefaultTimeoutMonitoringConfig(),

		// Performance settings - optimized for load testing
		DisableKeepAlives:  false, // Enable keep-alive for better performance
		DisableCompression: false, // Enable compression for bandwidth efficiency

		// Protocol settings
		EnableHTTP2: true, // Enable HTTP/2 by default
	}
}

// HighThroughputConfig returns configuration optimized for maximum throughput
func HighThroughputConfig() *Config {
	return &Config{
		MaxIdleConns:          500,
		MaxIdleConnsPerHost:   100,
		MaxConnsPerHost:       200,
		IdleConnTimeout:       60 * time.Second,
		DialTimeout:           5 * time.Second,
		ResponseHeaderTimeout: 15 * time.Second,
		TLSHandshakeTimeout:   5 * time.Second,
		ExpectContinueTimeout: 500 * time.Millisecond,
		TimeoutStrategy:       NewAggressiveTimeoutStrategy(),   // Aggressive strategy for throughput
		TimeoutMonitoring:     DefaultTimeoutMonitoringConfig(), // Enable monitoring for throughput optimization
		DisableKeepAlives:     false,
		DisableCompression:    true, // Disable compression to reduce CPU overhead
	}
}

// LowLatencyConfig returns configuration optimized for low latency
func LowLatencyConfig() *Config {
	return &Config{
		MaxIdleConns:          100,
		MaxIdleConnsPerHost:   20,
		MaxConnsPerHost:       50,
		IdleConnTimeout:       30 * time.Second,
		DialTimeout:           3 * time.Second,
		ResponseHeaderTimeout: 10 * time.Second,
		TLSHandshakeTimeout:   3 * time.Second,
		ExpectContinueTimeout: 200 * time.Millisecond,
		TimeoutStrategy:       NewAggressiveTimeoutStrategy(),   // Aggressive strategy for low latency
		TimeoutMonitoring:     DefaultTimeoutMonitoringConfig(), // Enable monitoring for latency optimization
		DisableKeepAlives:     false,
		DisableCompression:    false,
	}
}

// Validate checks if the configuration values are valid and returns an error if not
func (c *Config) Validate() error {
	if c.MaxIdleConns < 0 {
		return fmt.Errorf("MaxIdleConns must be non-negative, got %d", c.MaxIdleConns)
	}
	if c.MaxIdleConnsPerHost < 0 {
		return fmt.Errorf("MaxIdleConnsPerHost must be non-negative, got %d", c.MaxIdleConnsPerHost)
	}
	if c.MaxConnsPerHost < 0 {
		return fmt.Errorf("MaxConnsPerHost must be non-negative, got %d", c.MaxConnsPerHost)
	}
	if c.MaxIdleConnsPerHost > c.MaxIdleConns && c.MaxIdleConns > 0 {
		return fmt.Errorf("MaxIdleConnsPerHost (%d) cannot be greater than MaxIdleConns (%d)",
			c.MaxIdleConnsPerHost, c.MaxIdleConns)
	}
	if c.IdleConnTimeout < 0 {
		return fmt.Errorf("IdleConnTimeout must be non-negative, got %v", c.IdleConnTimeout)
	}
	if c.DialTimeout < 0 {
		return fmt.Errorf("DialTimeout must be non-negative, got %v", c.DialTimeout)
	}
	if c.ResponseHeaderTimeout < 0 {
		return fmt.Errorf("ResponseHeaderTimeout must be non-negative, got %v", c.ResponseHeaderTimeout)
	}
	if c.TLSHandshakeTimeout < 0 {
		return fmt.Errorf("TLSHandshakeTimeout must be non-negative, got %v", c.TLSHandshakeTimeout)
	}
	if c.ExpectContinueTimeout < 0 {
		return fmt.Errorf("ExpectContinueTimeout must be non-negative, got %v", c.ExpectContinueTimeout)
	}

	// Validate timeout strategy
	if err := c.TimeoutStrategy.Validate(); err != nil {
		return fmt.Errorf("invalid timeout strategy: %w", err)
	}

	return nil
}

// GetConnectionStats returns a copy of current connection statistics
func (c *HTTPClient) GetConnectionStats() ConnectionStats {
	c.mu.RLock()
	defer c.mu.RUnlock()

	return ConnectionStats{
		TotalRequests:      atomic.LoadInt64(&c.stats.TotalRequests),
		ConnectionsReused:  atomic.LoadInt64(&c.stats.ConnectionsReused),
		NewConnections:     atomic.LoadInt64(&c.stats.NewConnections),
		ActiveConnections:  atomic.LoadInt64(&c.stats.ActiveConnections),
		IdleConnections:    atomic.LoadInt64(&c.stats.IdleConnections),
		AvgConnectionTime:  c.stats.AvgConnectionTime,
		AvgReuseTime:       c.stats.AvgReuseTime,
		ConnectionErrors:   atomic.LoadInt64(&c.stats.ConnectionErrors),
		TimeoutErrors:      atomic.LoadInt64(&c.stats.TimeoutErrors),
		LastConnectionTime: c.stats.LastConnectionTime,
		LastReuseTime:      c.stats.LastReuseTime,
	}
}

// GetConnectionReuseRatio returns the ratio of reused connections to total requests
func (c *HTTPClient) GetConnectionReuseRatio() float64 {
	totalRequests := atomic.LoadInt64(&c.stats.TotalRequests)
	if totalRequests == 0 {
		return 0.0
	}
	connectionsReused := atomic.LoadInt64(&c.stats.ConnectionsReused)
	return float64(connectionsReused) / float64(totalRequests)
}

// PrewarmConnections establishes connections to the specified hosts proactively
func (c *HTTPClient) PrewarmConnections(ctx context.Context, hosts []string, connectionsPerHost int) error {
	if len(hosts) == 0 {
		return fmt.Errorf("no hosts provided for prewarming")
	}

	if connectionsPerHost <= 0 {
		connectionsPerHost = 1
	}

	// Limit prewarming to prevent resource exhaustion
	maxPrewarmConnections := c.config.MaxConnsPerHost / 2
	if connectionsPerHost > maxPrewarmConnections {
		connectionsPerHost = maxPrewarmConnections
	}

	var wg sync.WaitGroup
	errChan := make(chan error, len(hosts)*connectionsPerHost)

	for _, host := range hosts {
		for i := 0; i < connectionsPerHost; i++ {
			wg.Add(1)
			go func(targetHost string) {
				defer wg.Done()

				// Create a simple HEAD request to establish connection
				req, err := http.NewRequestWithContext(ctx, "HEAD", targetHost, nil)
				if err != nil {
					errChan <- fmt.Errorf("failed to create prewarm request for %s: %w", targetHost, err)
					return
				}

				start := time.Now()
				resp, err := c.client.Do(req)
				if err != nil {
					atomic.AddInt64(&c.stats.ConnectionErrors, 1)
					errChan <- fmt.Errorf("failed to prewarm connection to %s: %w", targetHost, err)
					return
				}
				defer resp.Body.Close()

				// Update connection statistics
				atomic.AddInt64(&c.stats.NewConnections, 1)
				c.mu.Lock()
				c.stats.LastConnectionTime = time.Now()
				connectionTime := time.Since(start)
				if c.stats.AvgConnectionTime == 0 {
					c.stats.AvgConnectionTime = connectionTime
				} else {
					// Simple moving average
					c.stats.AvgConnectionTime = (c.stats.AvgConnectionTime + connectionTime) / 2
				}
				c.mu.Unlock()

			}(host)
		}
	}

	// Wait for all prewarming to complete
	wg.Wait()
	close(errChan)

	// Collect any errors
	var errors []error
	for err := range errChan {
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return fmt.Errorf("prewarming completed with %d errors: first error: %w", len(errors), errors[0])
	}

	return nil
}

// ValidateConnectionHealth checks if the connection pool is healthy
func (c *HTTPClient) ValidateConnectionHealth() error {
	stats := c.GetConnectionStats()

	// Check for excessive errors
	if stats.TotalRequests > 100 {
		errorRate := float64(stats.ConnectionErrors+stats.TimeoutErrors) / float64(stats.TotalRequests)
		if errorRate > 0.1 { // More than 10% error rate
			return fmt.Errorf("connection pool unhealthy: error rate %.2f%% exceeds threshold", errorRate*100)
		}
	}

	// Check connection reuse efficiency
	if stats.TotalRequests > 10 {
		reuseRatio := c.GetConnectionReuseRatio()
		if reuseRatio < 0.3 { // Less than 30% reuse rate
			return fmt.Errorf("connection pool inefficient: reuse ratio %.2f%% below threshold", reuseRatio*100)
		}
	}

	return nil
}

// FlushIdleConnections closes all idle connections in the pool
func (c *HTTPClient) FlushIdleConnections() {
	c.transport.CloseIdleConnections()
	atomic.StoreInt64(&c.stats.IdleConnections, 0)
}

// instrumentedTransport wraps http.Transport to track connection metrics
type instrumentedTransport struct {
	*http.Transport
	stats *ConnectionStats
	mu    *sync.RWMutex
}

// RoundTrip implements http.RoundTripper interface with metrics tracking
func (t *instrumentedTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	start := time.Now()

	// Increment total requests
	atomic.AddInt64(&t.stats.TotalRequests, 1)

	// Execute the request
	resp, err := t.Transport.RoundTrip(req)

	if err != nil {
		// Track different types of errors
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			atomic.AddInt64(&t.stats.TimeoutErrors, 1)
		} else {
			atomic.AddInt64(&t.stats.ConnectionErrors, 1)
		}
		return nil, err
	}

	// Determine if connection was reused by checking response headers or timing
	duration := time.Since(start)

	t.mu.Lock()
	if duration < 10*time.Millisecond { // Likely a reused connection
		atomic.AddInt64(&t.stats.ConnectionsReused, 1)
		t.stats.LastReuseTime = time.Now()
		if t.stats.AvgReuseTime == 0 {
			t.stats.AvgReuseTime = duration
		} else {
			t.stats.AvgReuseTime = (t.stats.AvgReuseTime + duration) / 2
		}
	} else { // Likely a new connection
		atomic.AddInt64(&t.stats.NewConnections, 1)
		t.stats.LastConnectionTime = time.Now()
		if t.stats.AvgConnectionTime == 0 {
			t.stats.AvgConnectionTime = duration
		} else {
			t.stats.AvgConnectionTime = (t.stats.AvgConnectionTime + duration) / 2
		}
	}
	t.mu.Unlock()

	return resp, nil
}

func NewHTTPClient(config *Config) *HTTPClient {
	if config == nil {
		config = DefaultConfig()
	}

	// Validate configuration before using it
	if err := config.Validate(); err != nil {
		// For now, fall back to default config if validation fails
		// In production, you might want to return an error instead
		config = DefaultConfig()
	}

	// Create custom transport with connection pool settings
	transport := &http.Transport{
		// Connection pool settings
		MaxIdleConns:        config.MaxIdleConns,
		MaxIdleConnsPerHost: config.MaxIdleConnsPerHost,
		MaxConnsPerHost:     config.MaxConnsPerHost,
		IdleConnTimeout:     config.IdleConnTimeout,

		// Timeout settings
		DialContext: (&net.Dialer{
			Timeout: config.DialTimeout,
		}).DialContext,
		ResponseHeaderTimeout: config.ResponseHeaderTimeout,
		TLSHandshakeTimeout:   config.TLSHandshakeTimeout,
		ExpectContinueTimeout: config.ExpectContinueTimeout,

		// Performance settings
		DisableKeepAlives:  config.DisableKeepAlives,
		DisableCompression: config.DisableCompression,

		// Additional optimizations for load testing
		ForceAttemptHTTP2: config.EnableHTTP2, // Enable HTTP/2 based on config
	}

	// Initialize connection statistics
	stats := &ConnectionStats{}

	// Create instrumented transport for metrics tracking
	instrumentedTransport := &instrumentedTransport{
		Transport: transport,
		stats:     stats,
		mu:        &sync.RWMutex{},
	}

	// Create HTTP client with the instrumented transport
	client := &http.Client{
		Transport: instrumentedTransport,
		// Note: We don't set a client-level timeout here as it should be
		// configured per request based on the specific test requirements
	}

	return &HTTPClient{
		client:          client,
		config:          config,
		transport:       transport,
		stats:           stats,
		httpMethodStats: NewHTTPMethodStats(), // Use proper initialization function
		retryConfig:     DefaultRetryConfig(), // Initialize with default retry configuration
		authConfig:      nil,                  // No authentication by default
		mu:              sync.RWMutex{},
	}
}

// NewHTTPClientWithRetry creates a new HTTP client with custom retry configuration
func NewHTTPClientWithRetry(config *Config, retryConfig *RetryConfig) *HTTPClient {
	client := NewHTTPClient(config)
	if retryConfig != nil {
		client.retryConfig = retryConfig
	}
	return client
}

// GetClient returns the underlying http.Client for direct use if needed
func (c *HTTPClient) GetClient() *http.Client {
	return c.client
}

// GetConfig returns the configuration used by this HTTP client
func (c *HTTPClient) GetConfig() *Config {
	return c.config
}

// Close gracefully closes idle connections in the connection pool
func (c *HTTPClient) Close() {
	c.FlushIdleConnections()
}

// Request represents an HTTP request with performance tracking
type Request struct {
	Method  string            `json:"method"`
	URL     string            `json:"url"`
	Headers map[string]string `json:"headers,omitempty"`
	Body    []byte            `json:"body,omitempty"`
	Timeout time.Duration     `json:"timeout,omitempty"`
}

// Validate checks if the request parameters are valid
func (r *Request) Validate() error {
	if r.Method == "" {
		return fmt.Errorf("request method cannot be empty")
	}
	if r.URL == "" {
		return fmt.Errorf("request URL cannot be empty")
	}
	if r.Timeout < 0 {
		return fmt.Errorf("request timeout must be non-negative, got %v", r.Timeout)
	}
	return nil
}

// Response represents an HTTP response with performance metrics
type Response struct {
	StatusCode    int               `json:"status_code"`
	Status        string            `json:"status"`
	Headers       map[string]string `json:"headers,omitempty"`
	Body          []byte            `json:"body,omitempty"`
	ContentLength int64             `json:"content_length"`
	Duration      time.Duration     `json:"duration"`
	Timestamp     time.Time         `json:"timestamp"`
}

// Get performs an HTTP GET request
func (c *HTTPClient) Get(ctx context.Context, url string, headers map[string]string) (*Response, error) {
	req := &Request{
		Method:  "GET",
		URL:     url,
		Headers: headers,
	}
	return c.Execute(ctx, req)
}

// Post performs an HTTP POST request
func (c *HTTPClient) Post(ctx context.Context, url string, body []byte, headers map[string]string) (*Response, error) {
	req := &Request{
		Method:  "POST",
		URL:     url,
		Headers: headers,
		Body:    body,
	}
	return c.Execute(ctx, req)
}

// Put performs an HTTP PUT request
func (c *HTTPClient) Put(ctx context.Context, url string, body []byte, headers map[string]string) (*Response, error) {
	req := &Request{
		Method:  "PUT",
		URL:     url,
		Headers: headers,
		Body:    body,
	}
	return c.Execute(ctx, req)
}

// Delete performs an HTTP DELETE request
func (c *HTTPClient) Delete(ctx context.Context, url string, headers map[string]string) (*Response, error) {
	req := &Request{
		Method:  "DELETE",
		URL:     url,
		Headers: headers,
	}
	return c.Execute(ctx, req)
}

// Head performs an HTTP HEAD request
func (c *HTTPClient) Head(ctx context.Context, url string, headers map[string]string) (*Response, error) {
	req := &Request{
		Method:  "HEAD",
		URL:     url,
		Headers: headers,
	}
	return c.Execute(ctx, req)
}

// Options performs an HTTP OPTIONS request
func (c *HTTPClient) Options(ctx context.Context, url string, headers map[string]string) (*Response, error) {
	req := &Request{
		Method:  "OPTIONS",
		URL:     url,
		Headers: headers,
	}
	return c.Execute(ctx, req)
}

// Patch performs an HTTP PATCH request
func (c *HTTPClient) Patch(ctx context.Context, url string, body []byte, headers map[string]string) (*Response, error) {
	req := &Request{
		Method:  "PATCH",
		URL:     url,
		Headers: headers,
		Body:    body,
	}
	return c.Execute(ctx, req)
}

// Execute performs an HTTP request with the specified parameters
func (c *HTTPClient) Execute(ctx context.Context, req *Request) (*Response, error) {
	return c.executeWithRetry(ctx, req)
}

// ExecuteWithoutRetry executes an HTTP request without retry logic
func (c *HTTPClient) ExecuteWithoutRetry(ctx context.Context, req *Request) (*Response, error) {
	resp, err := c.executeOnceWithRetryInfo(ctx, req, 0) // 0 retries for no retry execution
	c.recordRequestMetrics(req, resp, err, 0)
	return resp, err
}

// SetRetryConfig updates the retry configuration
func (c *HTTPClient) SetRetryConfig(config *RetryConfig) {
	c.mu.Lock()
	defer c.mu.Unlock()
	if config != nil {
		c.retryConfig = config
	}
}

// GetRetryConfig returns the current retry configuration
func (c *HTTPClient) GetRetryConfig() *RetryConfig {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.retryConfig
}

// DisableRetries disables retry logic for this client
func (c *HTTPClient) DisableRetries() {
	c.SetRetryConfig(&RetryConfig{MaxRetries: 0})
}

// EnableAggressiveRetries enables aggressive retry configuration
func (c *HTTPClient) EnableAggressiveRetries() {
	c.SetRetryConfig(AggressiveRetryConfig())
}

// EnableConservativeRetries enables conservative retry configuration
func (c *HTTPClient) EnableConservativeRetries() {
	c.SetRetryConfig(ConservativeRetryConfig())
}

// ErrorType represents different categories of HTTP errors
type ErrorType int

const (
	ErrorTypeNetwork ErrorType = iota
	ErrorTypeTimeout
	ErrorTypeHTTP
	ErrorTypeRetryable
	ErrorTypeNonRetryable
)

// HTTPError represents a categorized HTTP error
type HTTPError struct {
	Type       ErrorType
	StatusCode int
	Message    string
	Underlying error
	Retryable  bool
}

func (e *HTTPError) Error() string {
	if e.StatusCode > 0 {
		return fmt.Sprintf("HTTP %d: %s", e.StatusCode, e.Message)
	}
	return e.Message
}

func (e *HTTPError) Unwrap() error {
	return e.Underlying
}

// RetryConfig defines retry behavior configuration
type RetryConfig struct {
	MaxRetries      int           // Maximum number of retry attempts
	InitialDelay    time.Duration // Initial delay between retries
	MaxDelay        time.Duration // Maximum delay between retries
	BackoffFactor   float64       // Exponential backoff multiplier
	Jitter          bool          // Add random jitter to delays
	RetryableErrors []int         // HTTP status codes that should be retried
}

// DefaultRetryConfig returns a sensible default retry configuration
func DefaultRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      5 * time.Second,
		BackoffFactor: 2.0,
		Jitter:        true,
		RetryableErrors: []int{
			http.StatusRequestTimeout,      // 408
			http.StatusTooManyRequests,     // 429
			http.StatusInternalServerError, // 500
			http.StatusBadGateway,          // 502
			http.StatusServiceUnavailable,  // 503
			http.StatusGatewayTimeout,      // 504
		},
	}
}

// AggressiveRetryConfig returns a more aggressive retry configuration for load testing
func AggressiveRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxRetries:    5,
		InitialDelay:  50 * time.Millisecond,
		MaxDelay:      3 * time.Second,
		BackoffFactor: 1.5,
		Jitter:        true,
		RetryableErrors: []int{
			http.StatusRequestTimeout,      // 408
			http.StatusTooManyRequests,     // 429
			http.StatusInternalServerError, // 500
			http.StatusBadGateway,          // 502
			http.StatusServiceUnavailable,  // 503
			http.StatusGatewayTimeout,      // 504
			http.StatusInsufficientStorage, // 507
		},
	}
}

// ConservativeRetryConfig returns a conservative retry configuration
func ConservativeRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxRetries:    2,
		InitialDelay:  200 * time.Millisecond,
		MaxDelay:      10 * time.Second,
		BackoffFactor: 3.0,
		Jitter:        false,
		RetryableErrors: []int{
			http.StatusTooManyRequests,     // 429
			http.StatusInternalServerError, // 500
			http.StatusBadGateway,          // 502
			http.StatusServiceUnavailable,  // 503
			http.StatusGatewayTimeout,      // 504
		},
	}
}

// isTransientError determines if an error is likely transient and retryable
func isTransientError(err error) bool {
	if err == nil {
		return false
	}

	// Check for network errors
	if netErr, ok := err.(net.Error); ok {
		return netErr.Temporary() || netErr.Timeout()
	}

	// Check for common transient error patterns
	errStr := strings.ToLower(err.Error())

	// Non-retryable patterns (configuration/permanent errors)
	nonRetryablePatterns := []string{
		"invalid port",
		"no such host",       // DNS resolution failure - usually not transient
		"connection refused", // Server not running - usually not transient for load testing
	}

	for _, pattern := range nonRetryablePatterns {
		if strings.Contains(errStr, pattern) {
			return false
		}
	}

	// Retryable patterns (transient network issues)
	transientPatterns := []string{
		"connection reset by peer",
		"connection timed out",
		"network is unreachable",
		"broken pipe",
		"i/o timeout",
		"eof",
		"context deadline exceeded",
	}

	for _, pattern := range transientPatterns {
		if strings.Contains(errStr, pattern) {
			return true
		}
	}

	return false
}

// isRetryableHTTPStatus checks if an HTTP status code should be retried
func (c *HTTPClient) isRetryableHTTPStatus(statusCode int) bool {
	for _, retryableCode := range c.retryConfig.RetryableErrors {
		if statusCode == retryableCode {
			return true
		}
	}
	return false
}

// categorizeError categorizes an error for better handling and timeout classification
func (c *HTTPClient) categorizeError(err error, statusCode int) *HTTPError {
	if err == nil && statusCode >= 200 && statusCode < 300 {
		return nil // No error
	}

	httpErr := &HTTPError{
		StatusCode: statusCode,
		Underlying: err,
	}

	// Categorize based on error type
	if err != nil {
		if netErr, ok := err.(net.Error); ok {
			if netErr.Timeout() {
				httpErr.Type = ErrorTypeTimeout
				httpErr.Message = err.Error() // Preserve original error message for context cancellation
				httpErr.Retryable = true
			} else {
				httpErr.Type = ErrorTypeNetwork
				httpErr.Message = "Network error"
				httpErr.Retryable = isTransientError(err)
			}
		} else {
			httpErr.Type = ErrorTypeNetwork
			httpErr.Message = err.Error()
			httpErr.Retryable = isTransientError(err)
		}
	} else if statusCode >= 400 {
		// HTTP status error
		httpErr.Type = ErrorTypeHTTP
		httpErr.Message = http.StatusText(statusCode)
		httpErr.Retryable = c.isRetryableHTTPStatus(statusCode)
	}

	// Final classification
	if httpErr.Retryable {
		httpErr.Type = ErrorTypeRetryable
	} else {
		httpErr.Type = ErrorTypeNonRetryable
	}

	return httpErr
}

// classifyTimeoutError classifies the specific type of timeout error
func (c *HTTPClient) classifyTimeoutError(err error) TimeoutType {
	if err == nil {
		return TimeoutTypeUnknown
	}

	errStr := strings.ToLower(err.Error())

	// Check for specific timeout patterns
	if strings.Contains(errStr, "dial") && strings.Contains(errStr, "timeout") {
		return TimeoutTypeDial
	}
	if strings.Contains(errStr, "tls") && strings.Contains(errStr, "timeout") {
		return TimeoutTypeTLS
	}
	if strings.Contains(errStr, "response header") && strings.Contains(errStr, "timeout") {
		return TimeoutTypeResponseHeader
	}
	if strings.Contains(errStr, "context deadline exceeded") || strings.Contains(errStr, "request timeout") {
		return TimeoutTypeTotalRequest
	}

	// Default for timeout errors
	if strings.Contains(errStr, "timeout") {
		return TimeoutTypeTotalRequest
	}

	return TimeoutTypeUnknown
}

// Note: calculateRetryDelay functionality moved to standalone CalculateDelay function in retry.go

// executeWithRetry executes an HTTP request with retry logic
func (c *HTTPClient) executeWithRetry(ctx context.Context, req *Request) (*Response, error) {
	var lastErr error
	var resp *Response
	retryAttempts := int64(0)

	for attempt := 0; attempt <= c.retryConfig.MaxRetries; attempt++ {
		// Add retry attempt delay (except for first attempt)
		if attempt > 0 {
			retryAttempts++
			delay := CalculateDelay(attempt-1, c.retryConfig)

			// Check if context is still valid before sleeping
			select {
			case <-ctx.Done():
				contextErr := &HTTPError{
					Type:       ErrorTypeNonRetryable,
					Message:    "Context cancelled during retry delay",
					Underlying: ctx.Err(),
					Retryable:  false,
				}
				// Record metrics for the failed request
				c.recordRequestMetrics(req, resp, contextErr, retryAttempts)
				return nil, contextErr
			case <-time.After(delay):
				// Continue with retry
			}
		}

		// Execute the request with retry attempt information
		resp, lastErr = c.executeOnceWithRetryInfo(ctx, req, attempt)

		// If successful (no error), return immediately
		if lastErr == nil {
			// Record metrics for successful request
			c.recordRequestMetrics(req, resp, nil, retryAttempts)
			return resp, nil
		}

		// Check if error is retryable using standalone function
		statusCode := 0
		if resp != nil {
			statusCode = resp.StatusCode
		}
		if !ShouldRetry(lastErr, statusCode, c.retryConfig) {
			// Non-retryable error, return immediately
			c.recordRequestMetrics(req, resp, lastErr, retryAttempts)
			return resp, lastErr
		}

		// If we've exhausted all retries, break
		if attempt == c.retryConfig.MaxRetries {
			break
		}
	}

	// All retries exhausted, return retry exhaustion error
	retryExhaustionErr := &HTTPError{
		Type:       ErrorTypeNonRetryable,
		Message:    fmt.Sprintf("Request failed after %d retries. Last error: %v", c.retryConfig.MaxRetries, lastErr),
		Underlying: lastErr,
		Retryable:  false,
	}

	// Record metrics for the failed request
	c.recordRequestMetrics(req, resp, retryExhaustionErr, retryAttempts)
	return resp, retryExhaustionErr
}

// executeOnceWithRetryInfo performs a single HTTP request execution with retry attempt information
func (c *HTTPClient) executeOnceWithRetryInfo(ctx context.Context, req *Request, retryAttempt int) (*Response, error) {
	start := time.Now()

	// Validate request parameters
	if err := req.Validate(); err != nil {
		validationErr := &HTTPError{
			Type:       ErrorTypeNonRetryable,
			Message:    fmt.Sprintf("Request validation failed: %v", err),
			Underlying: err,
			Retryable:  false,
		}
		return nil, validationErr
	}

	// Calculate timeout using strategy if Request.Timeout is not specified
	var requestCtx context.Context
	var cancel context.CancelFunc
	if req.Timeout > 0 {
		// Use explicit request timeout
		requestCtx, cancel = context.WithTimeout(ctx, req.Timeout)
		defer cancel()
	} else {
		// Use timeout strategy to calculate timeout based on config and retry attempt
		strategyTimeout := c.config.TimeoutStrategy.CalculateTimeout(
			c.config.ResponseHeaderTimeout, // Use ResponseHeaderTimeout as base
			req.Method,
			retryAttempt,
		)
		requestCtx, cancel = context.WithTimeout(ctx, strategyTimeout)
		defer cancel()
	}

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(requestCtx, req.Method, req.URL, bytes.NewReader(req.Body))
	if err != nil {
		requestErr := &HTTPError{
			Type:       ErrorTypeNonRetryable,
			Message:    fmt.Sprintf("Failed to create HTTP request: %v", err),
			Underlying: err,
			Retryable:  false,
		}
		return nil, requestErr
	}

	// Set headers
	for key, value := range req.Headers {
		httpReq.Header.Set(key, value)
	}

	// Apply authentication if configured
	if c.authConfig != nil && c.authConfig.IsEnabled() {
		// Create a headers map from the current request headers for authentication application
		authHeaders := make(map[string]string)
		for key, values := range httpReq.Header {
			if len(values) > 0 {
				authHeaders[key] = values[0]
			}
		}

		// Apply authentication to the headers map
		if err := c.authConfig.ApplyToHeaders(authHeaders); err != nil {
			authErr := &HTTPError{
				Type:       ErrorTypeNonRetryable,
				Message:    fmt.Sprintf("Failed to apply authentication: %v", err),
				Underlying: err,
				Retryable:  false,
			}
			return nil, authErr
		}

		// Update the HTTP request headers with authentication
		for key, value := range authHeaders {
			httpReq.Header.Set(key, value)
		}
	}

	// Set default headers if not provided
	if httpReq.Header.Get("User-Agent") == "" {
		httpReq.Header.Set("User-Agent", "NeuralMeterGo/1.0")
	}

	// Set Content-Type for requests with body
	if len(req.Body) > 0 && httpReq.Header.Get("Content-Type") == "" {
		httpReq.Header.Set("Content-Type", "application/json")
	}

	// Execute request
	httpResp, err := c.client.Do(httpReq)
	duration := time.Since(start)

	if err != nil {
		// Categorize the error
		categorizedErr := c.categorizeError(err, 0)
		return nil, categorizedErr
	}
	defer httpResp.Body.Close()

	// Read response body
	body, err := io.ReadAll(httpResp.Body)
	if err != nil {
		bodyReadErr := &HTTPError{
			Type:       ErrorTypeNonRetryable,
			Message:    fmt.Sprintf("Failed to read response body: %v", err),
			Underlying: err,
			Retryable:  false,
		}
		return nil, bodyReadErr
	}

	// Create response
	response := &Response{
		StatusCode:    httpResp.StatusCode,
		Status:        httpResp.Status,
		Headers:       make(map[string]string),
		Body:          body,
		ContentLength: httpResp.ContentLength,
		Duration:      duration,
		Timestamp:     time.Now(),
	}

	// Copy headers
	for key, values := range httpResp.Header {
		if len(values) > 0 {
			response.Headers[key] = values[0]
		}
	}

	// Check for HTTP error status codes
	if httpResp.StatusCode >= 400 {
		httpErr := &HTTPError{
			Type:       ErrorTypeHTTP,
			StatusCode: httpResp.StatusCode,
			Message:    fmt.Sprintf("HTTP request failed with status %d: %s", httpResp.StatusCode, httpResp.Status),
			Retryable:  c.isRetryableHTTPStatus(httpResp.StatusCode),
		}
		return response, httpErr
	}

	return response, nil
}

// UpdateMethodCount atomically increments the counter for the given HTTP method
func (h *HTTPMethodStats) UpdateMethodCount(method string) {
	switch strings.ToUpper(method) {
	case "GET":
		atomic.AddInt64(&h.GetRequests, 1)
	case "POST":
		atomic.AddInt64(&h.PostRequests, 1)
	case "PUT":
		atomic.AddInt64(&h.PutRequests, 1)
	case "DELETE":
		atomic.AddInt64(&h.DeleteRequests, 1)
	case "HEAD":
		atomic.AddInt64(&h.HeadRequests, 1)
	case "OPTIONS":
		atomic.AddInt64(&h.OptionsRequests, 1)
	case "PATCH":
		atomic.AddInt64(&h.PatchRequests, 1)
	default:
		atomic.AddInt64(&h.OtherRequests, 1)
	}
}

// UpdateStatusCode atomically increments the counter for the given status code category
func (h *HTTPMethodStats) UpdateStatusCode(statusCode int) {
	switch {
	case statusCode >= 200 && statusCode < 300:
		atomic.AddInt64(&h.Status2xx, 1)
	case statusCode >= 300 && statusCode < 400:
		atomic.AddInt64(&h.Status3xx, 1)
	case statusCode >= 400 && statusCode < 500:
		atomic.AddInt64(&h.Status4xx, 1)
	case statusCode >= 500 && statusCode < 600:
		atomic.AddInt64(&h.Status5xx, 1)
	}
}

// UpdateResponseTime updates response time metrics in a thread-safe manner
func (h *HTTPMethodStats) UpdateResponseTime(duration time.Duration) {
	h.mu.Lock()
	defer h.mu.Unlock()

	// Update total response time
	h.TotalResponseTime += duration

	// Update min/max response times
	if h.MinResponseTime == 0 || duration < h.MinResponseTime {
		h.MinResponseTime = duration
	}
	if duration > h.MaxResponseTime {
		h.MaxResponseTime = duration
	}

	// Calculate average response time
	totalRequests := h.GetTotalRequests()
	if totalRequests > 0 {
		h.AvgResponseTime = h.TotalResponseTime / time.Duration(totalRequests)
	}
}

// UpdateBytesTransferred atomically updates bytes sent and received
func (h *HTTPMethodStats) UpdateBytesTransferred(bytesSent, bytesReceived int64) {
	atomic.AddInt64(&h.TotalBytesSent, bytesSent)
	atomic.AddInt64(&h.TotalBytesReceived, bytesReceived)
}

// UpdateErrorCounts atomically increments error counters
func (h *HTTPMethodStats) UpdateErrorCounts(isNetworkError, isTimeoutError bool) {
	if isNetworkError {
		atomic.AddInt64(&h.NetworkErrors, 1)
	}
	if isTimeoutError {
		atomic.AddInt64(&h.TimeoutErrors, 1)
	}
}

// UpdateTimeoutCounts updates granular timeout statistics
func (h *HTTPMethodStats) UpdateTimeoutCounts(timeoutType TimeoutType) {
	// Note: Overall timeout count is already updated by UpdateErrorCounts
	// to avoid double counting. Only update specific timeout type counts here.

	// Update specific timeout type counts
	switch timeoutType {
	case TimeoutTypeDial:
		atomic.AddInt64(&h.DialTimeouts, 1)
	case TimeoutTypeTLS:
		atomic.AddInt64(&h.TLSHandshakeTimeouts, 1)
	case TimeoutTypeResponseHeader:
		atomic.AddInt64(&h.ResponseHeaderTimeouts, 1)
	case TimeoutTypeTotalRequest:
		atomic.AddInt64(&h.TotalRequestTimeouts, 1)
	}
}

// UpdateTimeoutDistribution updates the timeout distribution histogram
func (h *HTTPMethodStats) UpdateTimeoutDistribution(timeoutValue time.Duration, buckets []time.Duration) {
	h.mu.Lock()
	defer h.mu.Unlock()

	if h.TimeoutDistribution == nil {
		h.TimeoutDistribution = make(map[string]int64)
	}

	// Find the appropriate bucket for this timeout value
	bucketKey := "infinity"
	for _, bucket := range buckets {
		if timeoutValue <= bucket {
			bucketKey = bucket.String()
			break
		}
	}

	h.TimeoutDistribution[bucketKey]++
}

// UpdateTimeoutRecovery updates timeout recovery metrics
func (h *HTTPMethodStats) UpdateTimeoutRecovery(recovered bool, recoveryTime time.Duration) {
	if recovered {
		atomic.AddInt64(&h.TimeoutRecoveries, 1)

		h.mu.Lock()
		defer h.mu.Unlock()

		// Update average recovery time
		recoveries := atomic.LoadInt64(&h.TimeoutRecoveries)
		if recoveries == 1 {
			h.AvgTimeoutRecoveryTime = recoveryTime
		} else {
			// Calculate running average
			oldAvg := h.AvgTimeoutRecoveryTime
			h.AvgTimeoutRecoveryTime = time.Duration(
				(int64(oldAvg)*(recoveries-1) + int64(recoveryTime)) / recoveries,
			)
		}

		// Update recovery rate
		totalTimeouts := atomic.LoadInt64(&h.TimeoutErrors)
		if totalTimeouts > 0 {
			h.TimeoutRecoveryRate = float64(recoveries) / float64(totalTimeouts)
		}
	}
}

// GetTimeoutBreakdown returns a breakdown of timeout types
func (h *HTTPMethodStats) GetTimeoutBreakdown() map[string]int64 {
	return map[string]int64{
		"total":           atomic.LoadInt64(&h.TimeoutErrors),
		"dial":            atomic.LoadInt64(&h.DialTimeouts),
		"tls":             atomic.LoadInt64(&h.TLSHandshakeTimeouts),
		"response_header": atomic.LoadInt64(&h.ResponseHeaderTimeouts),
		"total_request":   atomic.LoadInt64(&h.TotalRequestTimeouts),
	}
}

// GetTimeoutDistribution returns a copy of the timeout distribution
func (h *HTTPMethodStats) GetTimeoutDistribution() map[string]int64 {
	h.mu.RLock()
	defer h.mu.RUnlock()

	if h.TimeoutDistribution == nil {
		return make(map[string]int64)
	}

	distribution := make(map[string]int64)
	for bucket, count := range h.TimeoutDistribution {
		distribution[bucket] = count
	}
	return distribution
}

// UpdateRetryMetrics atomically updates retry-related metrics
func (h *HTTPMethodStats) UpdateRetryMetrics(attempts int64, successful bool) {
	atomic.AddInt64(&h.RetryAttempts, attempts)
	if successful {
		atomic.AddInt64(&h.SuccessfulRetries, 1)
	} else {
		atomic.AddInt64(&h.FailedRetries, 1)
	}
}

// UpdateTimestamps updates first and last request timestamps
func (h *HTTPMethodStats) UpdateTimestamps(requestTime time.Time) {
	h.mu.Lock()
	defer h.mu.Unlock()

	if h.FirstRequestTime.IsZero() {
		h.FirstRequestTime = requestTime
	}
	h.LastRequestTime = requestTime
}

// GetTotalRequests returns the total number of requests across all methods
func (h *HTTPMethodStats) GetTotalRequests() int64 {
	return atomic.LoadInt64(&h.GetRequests) +
		atomic.LoadInt64(&h.PostRequests) +
		atomic.LoadInt64(&h.PutRequests) +
		atomic.LoadInt64(&h.DeleteRequests) +
		atomic.LoadInt64(&h.HeadRequests) +
		atomic.LoadInt64(&h.OptionsRequests) +
		atomic.LoadInt64(&h.PatchRequests) +
		atomic.LoadInt64(&h.OtherRequests)
}

// GetSuccessRate returns the success rate as a percentage (2xx responses / total responses)
func (h *HTTPMethodStats) GetSuccessRate() float64 {
	totalResponses := atomic.LoadInt64(&h.Status2xx) +
		atomic.LoadInt64(&h.Status3xx) +
		atomic.LoadInt64(&h.Status4xx) +
		atomic.LoadInt64(&h.Status5xx)

	if totalResponses == 0 {
		return 0.0
	}

	successResponses := atomic.LoadInt64(&h.Status2xx)
	return (float64(successResponses) / float64(totalResponses)) * 100.0
}

// GetErrorRate returns the error rate as a percentage (4xx + 5xx responses / total responses)
func (h *HTTPMethodStats) GetErrorRate() float64 {
	totalResponses := atomic.LoadInt64(&h.Status2xx) +
		atomic.LoadInt64(&h.Status3xx) +
		atomic.LoadInt64(&h.Status4xx) +
		atomic.LoadInt64(&h.Status5xx)

	if totalResponses == 0 {
		return 0.0
	}

	errorResponses := atomic.LoadInt64(&h.Status4xx) + atomic.LoadInt64(&h.Status5xx)
	return (float64(errorResponses) / float64(totalResponses)) * 100.0
}

// GetThroughput returns requests per second based on time elapsed
func (h *HTTPMethodStats) GetThroughput() float64 {
	h.mu.RLock()
	defer h.mu.RUnlock()

	if h.FirstRequestTime.IsZero() || h.LastRequestTime.IsZero() {
		return 0.0
	}

	elapsed := h.LastRequestTime.Sub(h.FirstRequestTime)
	if elapsed <= 0 {
		return 0.0
	}

	totalRequests := h.GetTotalRequests()
	return float64(totalRequests) / elapsed.Seconds()
}

// Reset clears all metrics (useful for starting new test runs)
func (h *HTTPMethodStats) Reset() {
	h.mu.Lock()
	defer h.mu.Unlock()

	// Reset atomic counters
	atomic.StoreInt64(&h.GetRequests, 0)
	atomic.StoreInt64(&h.PostRequests, 0)
	atomic.StoreInt64(&h.PutRequests, 0)
	atomic.StoreInt64(&h.DeleteRequests, 0)
	atomic.StoreInt64(&h.HeadRequests, 0)
	atomic.StoreInt64(&h.OptionsRequests, 0)
	atomic.StoreInt64(&h.PatchRequests, 0)
	atomic.StoreInt64(&h.OtherRequests, 0)

	atomic.StoreInt64(&h.Status2xx, 0)
	atomic.StoreInt64(&h.Status3xx, 0)
	atomic.StoreInt64(&h.Status4xx, 0)
	atomic.StoreInt64(&h.Status5xx, 0)

	atomic.StoreInt64(&h.TotalBytesReceived, 0)
	atomic.StoreInt64(&h.TotalBytesSent, 0)
	atomic.StoreInt64(&h.NetworkErrors, 0)
	atomic.StoreInt64(&h.TimeoutErrors, 0)
	atomic.StoreInt64(&h.RetryAttempts, 0)
	atomic.StoreInt64(&h.SuccessfulRetries, 0)
	atomic.StoreInt64(&h.FailedRetries, 0)

	// Reset non-atomic fields
	h.TotalResponseTime = 0
	h.MinResponseTime = 0
	h.MaxResponseTime = 0
	h.AvgResponseTime = 0
	h.FirstRequestTime = time.Time{}
	h.LastRequestTime = time.Time{}
}

// GetHTTPMethodStats returns a snapshot of current HTTP method statistics
func (c *HTTPClient) GetHTTPMethodStats() HTTPMethodStats {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// Create a copy of the current stats
	stats := HTTPMethodStats{
		GetRequests:     atomic.LoadInt64(&c.httpMethodStats.GetRequests),
		PostRequests:    atomic.LoadInt64(&c.httpMethodStats.PostRequests),
		PutRequests:     atomic.LoadInt64(&c.httpMethodStats.PutRequests),
		DeleteRequests:  atomic.LoadInt64(&c.httpMethodStats.DeleteRequests),
		HeadRequests:    atomic.LoadInt64(&c.httpMethodStats.HeadRequests),
		OptionsRequests: atomic.LoadInt64(&c.httpMethodStats.OptionsRequests),
		PatchRequests:   atomic.LoadInt64(&c.httpMethodStats.PatchRequests),
		OtherRequests:   atomic.LoadInt64(&c.httpMethodStats.OtherRequests),

		Status2xx: atomic.LoadInt64(&c.httpMethodStats.Status2xx),
		Status3xx: atomic.LoadInt64(&c.httpMethodStats.Status3xx),
		Status4xx: atomic.LoadInt64(&c.httpMethodStats.Status4xx),
		Status5xx: atomic.LoadInt64(&c.httpMethodStats.Status5xx),

		TotalBytesReceived: atomic.LoadInt64(&c.httpMethodStats.TotalBytesReceived),
		TotalBytesSent:     atomic.LoadInt64(&c.httpMethodStats.TotalBytesSent),
		NetworkErrors:      atomic.LoadInt64(&c.httpMethodStats.NetworkErrors),
		TimeoutErrors:      atomic.LoadInt64(&c.httpMethodStats.TimeoutErrors),
		RetryAttempts:      atomic.LoadInt64(&c.httpMethodStats.RetryAttempts),
		SuccessfulRetries:  atomic.LoadInt64(&c.httpMethodStats.SuccessfulRetries),
		FailedRetries:      atomic.LoadInt64(&c.httpMethodStats.FailedRetries),
	}

	// Copy non-atomic fields safely
	c.httpMethodStats.mu.RLock()
	stats.TotalResponseTime = c.httpMethodStats.TotalResponseTime
	stats.MinResponseTime = c.httpMethodStats.MinResponseTime
	stats.MaxResponseTime = c.httpMethodStats.MaxResponseTime
	stats.AvgResponseTime = c.httpMethodStats.AvgResponseTime
	stats.FirstRequestTime = c.httpMethodStats.FirstRequestTime
	stats.LastRequestTime = c.httpMethodStats.LastRequestTime
	c.httpMethodStats.mu.RUnlock()

	return stats
}

// GetHTTPMetrics returns comprehensive HTTP client metrics
func (c *HTTPClient) GetHTTPMetrics() HTTPMetrics {
	return HTTPMetrics{
		ConnectionStats: c.GetConnectionStats(),
		HTTPMethodStats: c.GetHTTPMethodStats(),
		StartTime:       c.httpMethodStats.FirstRequestTime,
		LastUpdatedTime: c.httpMethodStats.LastRequestTime,
	}
}

// ResetHTTPMethodStats resets all HTTP method statistics
func (c *HTTPClient) ResetHTTPMethodStats() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.httpMethodStats.Reset()
}

// GetMetricsSummary returns a human-readable summary of key metrics
func (c *HTTPClient) GetMetricsSummary() map[string]interface{} {
	httpStats := c.GetHTTPMethodStats()
	connStats := c.GetConnectionStats()

	// Basic metrics
	summary := map[string]interface{}{
		"total_requests":     httpStats.GetTotalRequests(),
		"success_rate":       fmt.Sprintf("%.2f%%", httpStats.GetSuccessRate()),
		"error_rate":         fmt.Sprintf("%.2f%%", httpStats.GetErrorRate()),
		"avg_response_time":  httpStats.AvgResponseTime.String(),
		"min_response_time":  httpStats.MinResponseTime.String(),
		"max_response_time":  httpStats.MaxResponseTime.String(),
		"throughput":         fmt.Sprintf("%.2f req/s", httpStats.GetThroughput()),
		"total_bytes_sent":   httpStats.TotalBytesSent,
		"total_bytes_recv":   httpStats.TotalBytesReceived,
		"network_errors":     httpStats.NetworkErrors,
		"timeout_errors":     httpStats.TimeoutErrors,
		"retry_attempts":     httpStats.RetryAttempts,
		"successful_retries": httpStats.SuccessfulRetries,
		"connection_reuse":   fmt.Sprintf("%.2f%%", c.GetConnectionReuseRatio()*100),
		"active_connections": connStats.ActiveConnections,
	}

	// Enhanced timeout monitoring if enabled
	if c.config.TimeoutMonitoring.Enabled {
		timeoutBreakdown := httpStats.GetTimeoutBreakdown()
		totalRequests := httpStats.GetTotalRequests()

		// Calculate overall timeout rate
		var overallTimeoutRate float64
		if totalRequests > 0 {
			overallTimeoutRate = float64(timeoutBreakdown["total"]) / float64(totalRequests) * 100
		}

		// Add timeout monitoring summary
		summary["timeout_monitoring"] = map[string]interface{}{
			"enabled":                  true,
			"overall_timeout_rate":     fmt.Sprintf("%.2f%%", overallTimeoutRate),
			"dial_timeouts":            timeoutBreakdown["Dial"],
			"tls_timeouts":             timeoutBreakdown["TLSHandshake"],
			"response_header_timeouts": timeoutBreakdown["ResponseHeader"],
			"total_request_timeouts":   timeoutBreakdown["TotalRequest"],
			"timeout_recoveries":       httpStats.TimeoutRecoveries,
			"recovery_rate":            fmt.Sprintf("%.2f%%", httpStats.TimeoutRecoveryRate*100),
			"top_timeout_type":         c.getTopTimeoutType(timeoutBreakdown),
		}

		// Add dynamic timeout stats if available
		if c.config.TimeoutStrategy.DynamicAdjuster != nil {
			summary["dynamic_timeouts"] = c.GetDynamicTimeoutStats()
		}
	} else {
		summary["timeout_monitoring"] = map[string]interface{}{
			"enabled": false,
			"message": "Enable timeout monitoring for detailed timeout metrics",
		}
	}

	return summary
}

// recordRequestMetrics records metrics for a completed request
func (c *HTTPClient) recordRequestMetrics(req *Request, resp *Response, err error, retryAttempts int64) {
	now := time.Now()

	// Update method count
	c.httpMethodStats.UpdateMethodCount(req.Method)

	// Update timestamps
	c.httpMethodStats.UpdateTimestamps(now)

	if resp != nil {
		// Update status code
		c.httpMethodStats.UpdateStatusCode(resp.StatusCode)

		// Update response time
		c.httpMethodStats.UpdateResponseTime(resp.Duration)

		// Feed response time to dynamic timeout adjuster if configured
		if c.config.TimeoutStrategy.DynamicAdjuster != nil {
			c.config.TimeoutStrategy.DynamicAdjuster.AddResponseTime(resp.Duration)
		}

		// Update bytes transferred
		bytesSent := int64(len(req.Body))
		bytesReceived := resp.ContentLength
		if bytesReceived <= 0 {
			bytesReceived = int64(len(resp.Body))
		}
		c.httpMethodStats.UpdateBytesTransferred(bytesSent, bytesReceived)
	}

	if err != nil {
		// Categorize errors
		isNetworkError := false
		isTimeoutError := false

		if httpErr, ok := err.(*HTTPError); ok {
			switch httpErr.Type {
			case ErrorTypeNetwork:
				isNetworkError = true
			case ErrorTypeTimeout:
				isTimeoutError = true
			case ErrorTypeRetryable:
				// Check if this retryable error is actually a timeout error
				// Timeout errors are often classified as retryable but we need to detect them
				if httpErr.Underlying != nil {
					if netErr, ok := httpErr.Underlying.(net.Error); ok && netErr.Timeout() {
						isTimeoutError = true
					} else {
						// Check for timeout patterns in the underlying error message
						errStr := strings.ToLower(httpErr.Underlying.Error())
						isTimeoutError = strings.Contains(errStr, "timeout") ||
							strings.Contains(errStr, "context deadline exceeded")
					}
				}
				// Also check the HTTPError message for timeout patterns
				if !isTimeoutError {
					errStr := strings.ToLower(httpErr.Message)
					isTimeoutError = strings.Contains(errStr, "timeout") ||
						strings.Contains(errStr, "context deadline exceeded")
				}
			case ErrorTypeNonRetryable:
				// Check if this non-retryable error is actually a timeout error after retry exhaustion
				// When retries are exhausted, timeout errors are wrapped as non-retryable
				if httpErr.Underlying != nil {
					if netErr, ok := httpErr.Underlying.(net.Error); ok && netErr.Timeout() {
						isTimeoutError = true
					} else {
						// Check for timeout patterns in the underlying error message
						errStr := strings.ToLower(httpErr.Underlying.Error())
						isTimeoutError = strings.Contains(errStr, "timeout") ||
							strings.Contains(errStr, "context deadline exceeded")
					}
				}
				// Also check the HTTPError message for timeout patterns
				if !isTimeoutError {
					errStr := strings.ToLower(httpErr.Message)
					isTimeoutError = strings.Contains(errStr, "timeout") ||
						strings.Contains(errStr, "context deadline exceeded")
				}
			}
		} else {
			// Check for network/timeout patterns in error string
			errStr := strings.ToLower(err.Error())
			isNetworkError = strings.Contains(errStr, "network") ||
				strings.Contains(errStr, "connection") ||
				strings.Contains(errStr, "dial")
			isTimeoutError = strings.Contains(errStr, "timeout") ||
				strings.Contains(errStr, "context deadline exceeded")
		}

		c.httpMethodStats.UpdateErrorCounts(isNetworkError, isTimeoutError)

		// Enhanced timeout monitoring - classify timeout types and track distribution
		if isTimeoutError {
			timeoutType := c.classifyTimeoutError(err)
			c.httpMethodStats.UpdateTimeoutCounts(timeoutType)

			// Calculate timeout value from request context or strategy
			var timeoutValue time.Duration
			if req.Timeout > 0 {
				timeoutValue = req.Timeout
			} else {
				// Calculate the timeout that was used based on strategy
				timeoutValue = c.config.TimeoutStrategy.CalculateTimeout(
					c.config.ResponseHeaderTimeout,
					req.Method,
					0, // Use 0 for initial attempt in metrics recording
				)
			}

			// Update timeout distribution if monitoring is enabled
			if c.config.TimeoutMonitoring.Enabled {
				c.httpMethodStats.UpdateTimeoutDistribution(timeoutValue, c.config.TimeoutMonitoring.DistributionBuckets)

				// Check if we should trigger alerts for timeout rates
				c.checkTimeoutAlerts(timeoutType)
			}
		}
	}

	// Update retry metrics if retries were attempted
	if retryAttempts > 0 {
		successful := err == nil && resp != nil && resp.StatusCode < 400
		c.httpMethodStats.UpdateRetryMetrics(retryAttempts, successful)

		// Track timeout recovery if this was a successful retry after a timeout
		if successful && retryAttempts > 0 {
			// Check if any of the retries were due to timeouts by checking previous errors
			// For now, we'll assume recovery if we had retries and succeeded
			c.httpMethodStats.UpdateTimeoutRecovery(true, resp.Duration)
		}
	}
}

// checkTimeoutAlerts checks if timeout rates exceed configured thresholds and triggers alerts
func (c *HTTPClient) checkTimeoutAlerts(timeoutType TimeoutType) {
	if !c.config.TimeoutMonitoring.Enabled || c.config.TimeoutMonitoring.AlertCallback == nil {
		return
	}

	// Get timeout breakdown and calculate rates
	timeoutBreakdown := c.httpMethodStats.GetTimeoutBreakdown()
	totalRequests := c.httpMethodStats.GetTotalRequests()

	if totalRequests < int64(c.config.TimeoutMonitoring.MinSampleSize) {
		return // Not enough samples for meaningful alerts
	}

	// Calculate timeout rate for the specific timeout type
	timeoutTypeKey := timeoutType.String()
	timeoutCount, exists := timeoutBreakdown[timeoutTypeKey]
	if !exists {
		return
	}

	timeoutRate := float64(timeoutCount) / float64(totalRequests)

	// Check if rate exceeds threshold
	if threshold, exists := c.config.TimeoutMonitoring.AlertThresholds[timeoutTypeKey]; exists {
		if timeoutRate > threshold {
			// Trigger alert callback
			c.config.TimeoutMonitoring.AlertCallback(timeoutTypeKey, timeoutRate, threshold)
		}
	}
}

// CalculateTimeout calculates the timeout for a request based on strategy, operation type, and retry attempt
func (ts *TimeoutStrategy) CalculateTimeout(baseTimeout time.Duration, method string, retryAttempt int) time.Duration {
	// Apply base timeout multiplier
	timeout := time.Duration(float64(baseTimeout) * ts.Policy.BaseTimeoutMultiplier)

	// Apply operation-specific multiplier
	operationMultiplier := ts.getOperationMultiplier(method)
	timeout = time.Duration(float64(timeout) * operationMultiplier)

	// Apply priority multiplier if configured
	if priorityMultiplier, exists := ts.Policy.PriorityMultipliers[method]; exists {
		timeout = time.Duration(float64(timeout) * priorityMultiplier)
	}

	// Apply retry escalation
	if retryAttempt > 0 {
		escalationFactor := ts.calculateEscalationFactor(retryAttempt)
		timeout = time.Duration(float64(timeout) * escalationFactor)
	}

	// Apply dynamic adjustment if configured
	if ts.DynamicAdjuster != nil && ts.DynamicAdjuster.config.Enabled {
		dynamicMultiplier := ts.DynamicAdjuster.getCurrentMultiplier()
		timeout = time.Duration(float64(timeout) * dynamicMultiplier)
	}

	// Apply jitter if enabled
	if ts.Policy.EnableJitter {
		timeout = ts.applyJitter(timeout)
	}

	return timeout
}

// getOperationMultiplier returns the multiplier for the given HTTP method
func (ts *TimeoutStrategy) getOperationMultiplier(method string) float64 {
	switch strings.ToUpper(method) {
	case "GET", "HEAD", "OPTIONS":
		return ts.Policy.ReadOperationMultiplier
	case "POST", "PUT", "PATCH", "DELETE":
		return ts.Policy.WriteOperationMultiplier
	default:
		return ts.Policy.BaseTimeoutMultiplier
	}
}

// calculateEscalationFactor calculates the escalation factor for retry attempts
func (ts *TimeoutStrategy) calculateEscalationFactor(retryAttempt int) float64 {
	// Calculate exponential backoff with escalation factor
	escalationFactor := 1.0
	for i := 0; i < retryAttempt; i++ {
		escalationFactor *= ts.Policy.RetryEscalationFactor
	}

	// Cap at maximum retry timeout multiplier
	if escalationFactor > ts.Policy.MaxRetryTimeoutMultiplier {
		escalationFactor = ts.Policy.MaxRetryTimeoutMultiplier
	}

	return escalationFactor
}

// applyJitter applies random jitter to the timeout to prevent thundering herd
func (ts *TimeoutStrategy) applyJitter(timeout time.Duration) time.Duration {
	if ts.Policy.JitterFactor <= 0 {
		return timeout
	}

	// Calculate jitter range
	jitterRange := float64(timeout) * ts.Policy.JitterFactor

	// Apply random jitter between -jitterRange/2 and +jitterRange/2
	jitter := (rand.Float64() - 0.5) * jitterRange

	// Ensure timeout doesn't go below a minimum threshold (10% of original)
	minTimeout := float64(timeout) * 0.1
	finalTimeout := float64(timeout) + jitter
	if finalTimeout < minTimeout {
		finalTimeout = minTimeout
	}

	return time.Duration(finalTimeout)
}

// NewResponseTimeTracker creates a new response time tracker
func NewResponseTimeTracker(maxSamples int) *ResponseTimeTracker {
	return &ResponseTimeTracker{
		samples:    make([]time.Duration, maxSamples),
		sampleIdx:  0,
		sampleSize: 0,
		maxSamples: maxSamples,
	}
}

// AddSample adds a new response time sample to the tracker
func (rt *ResponseTimeTracker) AddSample(responseTime time.Duration) {
	rt.mu.Lock()
	defer rt.mu.Unlock()

	rt.samples[rt.sampleIdx] = responseTime
	rt.sampleIdx = (rt.sampleIdx + 1) % rt.maxSamples

	if rt.sampleSize < rt.maxSamples {
		rt.sampleSize++
	}
}

// GetPercentile calculates the specified percentile from current samples
func (rt *ResponseTimeTracker) GetPercentile(percentile float64) time.Duration {
	rt.mu.RLock()
	defer rt.mu.RUnlock()

	if rt.sampleSize == 0 {
		return 0
	}

	// Create a copy of active samples for sorting
	activeSamples := make([]time.Duration, rt.sampleSize)
	if rt.sampleSize < rt.maxSamples {
		copy(activeSamples, rt.samples[:rt.sampleSize])
	} else {
		// Ring buffer is full, need to handle wraparound
		copy(activeSamples[:rt.maxSamples-rt.sampleIdx], rt.samples[rt.sampleIdx:])
		copy(activeSamples[rt.maxSamples-rt.sampleIdx:], rt.samples[:rt.sampleIdx])
	}

	// Sort samples for percentile calculation
	for i := 0; i < len(activeSamples); i++ {
		for j := i + 1; j < len(activeSamples); j++ {
			if activeSamples[i] > activeSamples[j] {
				activeSamples[i], activeSamples[j] = activeSamples[j], activeSamples[i]
			}
		}
	}

	// Calculate percentile index
	index := int(float64(len(activeSamples)-1) * percentile)
	if index < 0 {
		index = 0
	}
	if index >= len(activeSamples) {
		index = len(activeSamples) - 1
	}

	return activeSamples[index]
}

// GetAverage calculates the average response time from current samples
func (rt *ResponseTimeTracker) GetAverage() time.Duration {
	rt.mu.RLock()
	defer rt.mu.RUnlock()

	if rt.sampleSize == 0 {
		return 0
	}

	var total time.Duration
	if rt.sampleSize < rt.maxSamples {
		for i := 0; i < rt.sampleSize; i++ {
			total += rt.samples[i]
		}
	} else {
		for i := 0; i < rt.maxSamples; i++ {
			total += rt.samples[i]
		}
	}

	return time.Duration(int64(total) / int64(rt.sampleSize))
}

// GetSampleCount returns the current number of samples
func (rt *ResponseTimeTracker) GetSampleCount() int {
	rt.mu.RLock()
	defer rt.mu.RUnlock()
	return rt.sampleSize
}

// Reset clears all samples from the tracker
func (rt *ResponseTimeTracker) Reset() {
	rt.mu.Lock()
	defer rt.mu.Unlock()
	rt.sampleIdx = 0
	rt.sampleSize = 0
}

// NewDynamicTimeoutAdjuster creates a new dynamic timeout adjuster
func NewDynamicTimeoutAdjuster(config DynamicTimeoutConfig) *DynamicTimeoutAdjuster {
	return &DynamicTimeoutAdjuster{
		config:              config,
		responseTimeTracker: NewResponseTimeTracker(config.SampleWindow),
		currentMultiplier:   1.0, // Start with base multiplier
		lastAdjustment:      time.Now(),
		adjustmentCount:     0,
	}
}

// DefaultDynamicTimeoutConfig returns a default configuration for dynamic timeout adjustment
func DefaultDynamicTimeoutConfig() DynamicTimeoutConfig {
	return DynamicTimeoutConfig{
		Enabled:               true,
		AdjustmentSensitivity: 0.3,              // Moderate sensitivity
		DampeningFactor:       0.7,              // Moderate dampening
		TargetSuccessRate:     0.95,             // 95% success rate target
		ErrorRateThreshold:    0.1,              // 10% error rate threshold
		ResponseTimeP95:       5 * time.Second,  // 5s P95 target
		ResponseTimeP99:       10 * time.Second, // 10s P99 target
		MinTimeoutMultiplier:  0.5,              // 50% minimum
		MaxTimeoutMultiplier:  5.0,              // 500% maximum
		MaxAdjustmentStep:     0.2,              // 20% max change per step
		SampleWindow:          100,              // Track last 100 samples
		MinSampleCount:        10,               // Need at least 10 samples
		AdjustmentWindow:      30 * time.Second, // Adjust every 30 seconds
	}
}

// AddResponseTime adds a response time sample for dynamic adjustment
func (dta *DynamicTimeoutAdjuster) AddResponseTime(responseTime time.Duration) {
	if !dta.config.Enabled {
		return
	}
	dta.responseTimeTracker.AddSample(responseTime)
}

// ShouldAdjust determines if timeout adjustment should be performed
func (dta *DynamicTimeoutAdjuster) ShouldAdjust() bool {
	dta.mu.RLock()
	defer dta.mu.RUnlock()

	if !dta.config.Enabled {
		return false
	}

	// Check if enough time has passed since last adjustment
	if time.Since(dta.lastAdjustment) < dta.config.AdjustmentWindow {
		return false
	}

	// Check if we have enough samples
	if dta.responseTimeTracker.GetSampleCount() < dta.config.MinSampleCount {
		return false
	}

	return true
}

// CalculateAdjustment calculates the timeout adjustment based on current performance metrics
func (dta *DynamicTimeoutAdjuster) CalculateAdjustment(stats *HTTPMethodStats) float64 {
	if !dta.ShouldAdjust() {
		return dta.getCurrentMultiplier()
	}

	dta.mu.Lock()
	defer dta.mu.Unlock()

	// Get current performance metrics
	errorRate := stats.GetErrorRate()
	successRate := stats.GetSuccessRate()
	p95ResponseTime := dta.responseTimeTracker.GetPercentile(0.95)
	p99ResponseTime := dta.responseTimeTracker.GetPercentile(0.99)

	// Calculate adjustment factor based on performance
	adjustmentFactor := 0.0

	// Adjust based on error rate
	if errorRate > dta.config.ErrorRateThreshold {
		// High error rate, increase timeout
		errorAdjustment := (errorRate - dta.config.ErrorRateThreshold) * dta.config.AdjustmentSensitivity
		adjustmentFactor += errorAdjustment
	}

	// Adjust based on success rate
	if successRate < dta.config.TargetSuccessRate {
		// Low success rate, increase timeout
		successAdjustment := (dta.config.TargetSuccessRate - successRate) * dta.config.AdjustmentSensitivity
		adjustmentFactor += successAdjustment
	}

	// Adjust based on P95 response time
	if dta.config.ResponseTimeP95 > 0 && p95ResponseTime > dta.config.ResponseTimeP95 {
		// P95 too high, increase timeout
		p95Ratio := float64(p95ResponseTime) / float64(dta.config.ResponseTimeP95)
		p95Adjustment := (p95Ratio - 1.0) * dta.config.AdjustmentSensitivity * 0.5
		adjustmentFactor += p95Adjustment
	}

	// Adjust based on P99 response time
	if dta.config.ResponseTimeP99 > 0 && p99ResponseTime > dta.config.ResponseTimeP99 {
		// P99 too high, increase timeout
		p99Ratio := float64(p99ResponseTime) / float64(dta.config.ResponseTimeP99)
		p99Adjustment := (p99Ratio - 1.0) * dta.config.AdjustmentSensitivity * 0.3
		adjustmentFactor += p99Adjustment
	}

	// Apply dampening to prevent rapid oscillations
	adjustmentFactor *= dta.config.DampeningFactor

	// Limit adjustment step size
	if adjustmentFactor > dta.config.MaxAdjustmentStep {
		adjustmentFactor = dta.config.MaxAdjustmentStep
	} else if adjustmentFactor < -dta.config.MaxAdjustmentStep {
		adjustmentFactor = -dta.config.MaxAdjustmentStep
	}

	// Calculate new multiplier
	newMultiplier := dta.currentMultiplier + adjustmentFactor

	// Apply limits
	if newMultiplier < dta.config.MinTimeoutMultiplier {
		newMultiplier = dta.config.MinTimeoutMultiplier
	} else if newMultiplier > dta.config.MaxTimeoutMultiplier {
		newMultiplier = dta.config.MaxTimeoutMultiplier
	}

	// Update state if adjustment was made
	if newMultiplier != dta.currentMultiplier {
		dta.currentMultiplier = newMultiplier
		dta.lastAdjustment = time.Now()
		dta.adjustmentCount++
	}

	return dta.currentMultiplier
}

// getCurrentMultiplier returns the current timeout multiplier (thread-safe)
func (dta *DynamicTimeoutAdjuster) getCurrentMultiplier() float64 {
	dta.mu.RLock()
	defer dta.mu.RUnlock()
	return dta.currentMultiplier
}

// GetAdjustmentStats returns statistics about timeout adjustments
func (dta *DynamicTimeoutAdjuster) GetAdjustmentStats() map[string]interface{} {
	dta.mu.RLock()
	defer dta.mu.RUnlock()

	return map[string]interface{}{
		"enabled":            dta.config.Enabled,
		"current_multiplier": dta.currentMultiplier,
		"adjustment_count":   dta.adjustmentCount,
		"last_adjustment":    dta.lastAdjustment,
		"sample_count":       dta.responseTimeTracker.GetSampleCount(),
		"avg_response_time":  dta.responseTimeTracker.GetAverage(),
		"p95_response_time":  dta.responseTimeTracker.GetPercentile(0.95),
		"p99_response_time":  dta.responseTimeTracker.GetPercentile(0.99),
	}
}

// Reset resets the dynamic timeout adjuster to initial state
func (dta *DynamicTimeoutAdjuster) Reset() {
	dta.mu.Lock()
	defer dta.mu.Unlock()

	dta.currentMultiplier = 1.0
	dta.lastAdjustment = time.Now()
	dta.adjustmentCount = 0
	dta.responseTimeTracker.Reset()
}

// UpdateDynamicTimeoutAdjustment triggers dynamic timeout adjustment based on current performance
func (c *HTTPClient) UpdateDynamicTimeoutAdjustment() {
	if c.config.TimeoutStrategy.DynamicAdjuster != nil && c.config.TimeoutStrategy.DynamicAdjuster.config.Enabled {
		stats := c.GetHTTPMethodStats()
		c.config.TimeoutStrategy.DynamicAdjuster.CalculateAdjustment(&stats)
	}
}

// GetDynamicTimeoutStats returns statistics about dynamic timeout adjustment
func (c *HTTPClient) GetDynamicTimeoutStats() map[string]interface{} {
	if c.config.TimeoutStrategy.DynamicAdjuster != nil {
		return c.config.TimeoutStrategy.DynamicAdjuster.GetAdjustmentStats()
	}
	return map[string]interface{}{
		"enabled": false,
	}
}

// GetTimeoutMonitoringReport returns a comprehensive timeout monitoring report
func (c *HTTPClient) GetTimeoutMonitoringReport() map[string]interface{} {
	if !c.config.TimeoutMonitoring.Enabled {
		return map[string]interface{}{
			"enabled": false,
			"message": "Timeout monitoring is disabled",
		}
	}

	// Get timeout breakdown
	timeoutBreakdown := c.httpMethodStats.GetTimeoutBreakdown()
	timeoutDistribution := c.httpMethodStats.GetTimeoutDistribution()
	totalRequests := c.httpMethodStats.GetTotalRequests()

	// Calculate timeout rates
	timeoutRates := make(map[string]float64)
	if totalRequests > 0 {
		for timeoutType, count := range timeoutBreakdown {
			if timeoutType != "total" { // Skip total as it's computed from others
				rate := float64(count) / float64(totalRequests)
				timeoutRates[timeoutType] = rate
			}
		}
	}

	// Check alert status
	alertStatus := make(map[string]map[string]interface{})
	for timeoutType, rate := range timeoutRates {
		alertInfo := map[string]interface{}{
			"current_rate": rate,
			"alerting":     false,
		}

		if threshold, exists := c.config.TimeoutMonitoring.AlertThresholds[timeoutType]; exists {
			alertInfo["threshold"] = threshold
			alertInfo["alerting"] = rate > threshold
		} else {
			alertInfo["threshold"] = "not_configured"
		}

		alertStatus[timeoutType] = alertInfo
	}

	// Recovery metrics
	recoveryMetrics := map[string]interface{}{
		"recoveries":        atomic.LoadInt64(&c.httpMethodStats.TimeoutRecoveries),
		"recovery_rate":     c.httpMethodStats.TimeoutRecoveryRate,
		"avg_recovery_time": c.httpMethodStats.AvgTimeoutRecoveryTime.String(),
	}

	return map[string]interface{}{
		"enabled":              true,
		"total_requests":       totalRequests,
		"timeout_breakdown":    timeoutBreakdown,
		"timeout_rates":        timeoutRates,
		"timeout_distribution": timeoutDistribution,
		"alert_status":         alertStatus,
		"recovery_metrics":     recoveryMetrics,
		"monitoring_config": map[string]interface{}{
			"alert_thresholds":     c.config.TimeoutMonitoring.AlertThresholds,
			"distribution_buckets": c.config.TimeoutMonitoring.DistributionBuckets,
			"monitoring_window":    c.config.TimeoutMonitoring.MonitoringWindow.String(),
			"min_sample_size":      c.config.TimeoutMonitoring.MinSampleSize,
		},
	}
}

// GetTimeoutBreakdownSummary returns a simplified timeout breakdown for quick monitoring
func (c *HTTPClient) GetTimeoutBreakdownSummary() map[string]interface{} {
	breakdown := c.httpMethodStats.GetTimeoutBreakdown()
	totalRequests := c.httpMethodStats.GetTotalRequests()

	if totalRequests == 0 {
		return map[string]interface{}{
			"total_requests": 0,
			"timeout_rate":   0.0,
			"breakdown":      breakdown,
		}
	}

	overallTimeoutRate := float64(breakdown["total"]) / float64(totalRequests)

	return map[string]interface{}{
		"total_requests":       totalRequests,
		"overall_timeout_rate": overallTimeoutRate,
		"breakdown":            breakdown,
		"top_timeout_type":     c.getTopTimeoutType(breakdown),
	}
}

// getTopTimeoutType returns the timeout type with the highest count
func (c *HTTPClient) getTopTimeoutType(breakdown map[string]int64) string {
	var topType string
	var maxCount int64

	for timeoutType, count := range breakdown {
		if timeoutType != "total" && count > maxCount {
			maxCount = count
			topType = timeoutType
		}
	}

	if topType == "" {
		return "none"
	}
	return topType
}

// SetAuth configures authentication for all HTTP requests made by this client
func (c *HTTPClient) SetAuth(authConfig *AuthConfig) error {
	if authConfig == nil {
		c.mu.Lock()
		c.authConfig = nil
		c.mu.Unlock()
		return nil
	}

	// Validate the authentication configuration
	if err := authConfig.Validate(); err != nil {
		return fmt.Errorf("invalid auth config: %w", err)
	}

	c.mu.Lock()
	c.authConfig = authConfig.Clone()
	c.mu.Unlock()

	return nil
}

// SetHTTPVersion sets the HTTP version to use (1.1 or 2.0)
func (c *HTTPClient) SetHTTPVersion(version string) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if version != "1.1" && version != "2.0" {
		return fmt.Errorf("unsupported HTTP version: %s", version)
	}

	c.config.HTTPVersion = version

	// Update transport based on HTTP version
	if version == "2.0" {
		// Enable HTTP/2
		c.transport = &http.Transport{
			TLSNextProto: make(map[string]func(authority string, c *tls.Conn) http.RoundTripper),
		}
		c.transport.TLSNextProto["h2"] = http2.RoundTripper
	} else {
		// Use default HTTP/1.1 transport
		c.transport = &http.Transport{}
	}

	c.client.Transport = c.transport
	return nil
}

// GetAuth returns a copy of the current authentication configuration
func (c *HTTPClient) GetAuth() *AuthConfig {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if c.authConfig == nil {
		return nil
	}

	return c.authConfig.Clone()
}

// ClearAuth removes authentication configuration from the client
func (c *HTTPClient) ClearAuth() {
	c.mu.Lock()
	c.authConfig = nil
	c.mu.Unlock()
}

// UpdateAuth safely updates the authentication configuration using a callback function
func (c *HTTPClient) UpdateAuth(updater func(*AuthConfig)) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.authConfig == nil {
		return fmt.Errorf("no authentication configuration to update")
	}

	// Create a copy for safe updating
	authCopy := c.authConfig.Clone()
	updater(authCopy)

	// Validate the updated configuration
	if err := authCopy.Validate(); err != nil {
		return fmt.Errorf("invalid updated auth config: %w", err)
	}

	// Apply the validated configuration
	c.authConfig = authCopy
	return nil
}

// SetBasicAuth is a convenience method to set basic authentication
func (c *HTTPClient) SetBasicAuth(username, password string) error {
	authConfig := NewBasicAuth(username, password)
	return c.SetAuth(authConfig)
}

// EnablePipelining enables HTTP/1.1 request pipelining
func (c *HTTPClient) EnablePipelining(enable bool) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.config.HTTPVersion != "1.1" {
		return // Pipelining only supported for HTTP/1.1
	}

	c.config.EnablePipelining = enable
	c.transport.DisableKeepAlives = !enable // Enable keep-alives for pipelining
}

// Do sends an HTTP request using the client's transport and handles pipelining if enabled
func (c *HTTPClient) Do(req *http.Request) (*http.Response, error) {
	c.mu.RLock()
	enablePipelining := c.config.EnablePipelining && c.config.HTTPVersion == "1.1"
	c.mu.RUnlock()

	if enablePipelining {
		// Create a channel to receive the response
		ch := make(chan *http.Response, 1)
		c.mu.Lock()
		c.pipelinedRequests[req] = ch
		c.mu.Unlock()

		// Send the request asynchronously
		go func() {
			resp, err := c.client.Do(req)
			c.mu.Lock()
			if ch == c.pipelinedRequests[req] {
				if err != nil {
					ch <- nil
				} else {
					ch <- resp
				}
				delete(c.pipelinedRequests, req)
			}
			c.mu.Unlock()
		}()

		// Return the response from the channel
		return <-ch, nil
	}

// EnableCompression enables or disables compression for the client
func (c *HTTPClient) EnableCompression(enable bool, level int) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if enable {
		c.compression = &compressionConfig{
			level: level,
		}
	} else {
		c.compression = nil
	}
}

// SetAcceptEncoding sets the Accept-Encoding header based on compression configuration
func (c *HTTPClient) SetAcceptEncoding(req *http.Request) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if c.compression != nil {
		req.Header.Set("Accept-Encoding", "gzip, deflate")
	}
}

// GzipCompress compresses the request body using gzip if compression is enabled
func (c *HTTPClient) GzipCompress(req *http.Request) error {
	c.mu.RLock()
	defer c.mu.RLock()

	if c.compression == nil || req.Body == nil {
		return nil
	}

	var buf bytes.Buffer
	gzipWriter := gzip.NewWriterLevel(&buf, c.compression.level)

	// Create a pipe to read from the original body and write to the gzip writer
	pr, pw := io.Pipe()
	go func() {
		_, err := io.Copy(gzipWriter, req.Body)
		req.Body.Close()
		gzipWriter.Close()
		pw.CloseWithError(err)
	}()

	// Replace the request body with the compressed version
	req.Body = pr
	req.Header.Set("Content-Encoding", "gzip")
	req.ContentLength = int64(buf.Len())

	return nil
}

// Do sends an HTTP request using the client's transport and handles pipelining if enabled
func (c *HTTPClient) Do(req *http.Request) (*http.Response, error) {
	c.mu.RLock()
	enablePipelining := c.config.EnablePipelining && c.config.HTTPVersion == "1.1"
	c.mu.RUnlock()

	// Apply compression if enabled
	if c.compression != nil {
		if err := c.GzipCompress(req); err != nil {
			return nil, fmt.Errorf("failed to compress request: %w", err)
		}
		c.SetAcceptEncoding(req)
	}

	if enablePipelining {
		// Create a channel to receive the response
		ch := make(chan *http.Response, 1)
		c.mu.Lock()
		c.pipelinedRequests[req] = ch
		c.mu.Unlock()

		// Send the request asynchronously
		go func() {
			resp, err := c.client.Do(req)
			c.mu.Lock()
			if ch == c.pipelinedRequests[req] {
				if err != nil {
					ch <- nil
				} else {
					ch <- resp
				}
				delete(c.pipelinedRequests, req)
			}
			c.mu.Unlock()
		}()

		// Return the response from the channel
		return <-ch, nil
	}

	// Fallback to standard Do method for non-pipelined requests
	return c.client.Do(req)
}

// SetBearerAuth is a convenience method to set bearer token authentication
func (c *HTTPClient) SetBearerAuth(token string) error {
	authConfig := NewBearerAuth(token)
	return c.SetAuth(authConfig)
}

// SetCustomAuth is a convenience method to set custom header authentication
func (c *HTTPClient) SetCustomAuth(headers map[string]string) error {
	authConfig := NewCustomAuth(headers)
	return c.SetAuth(authConfig)
}

// IsAuthEnabled returns true if authentication is configured and enabled
func (c *HTTPClient) IsAuthEnabled() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()

	return c.authConfig != nil && c.authConfig.IsEnabled()
}

// EnableAuth enables authentication if a configuration exists
func (c *HTTPClient) EnableAuth() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.authConfig == nil {
		return fmt.Errorf("no authentication configuration to enable")
	}

	c.authConfig.SetEnabled(true)
	return nil
}

// DisableAuth disables authentication without removing the configuration
func (c *HTTPClient) DisableAuth() {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.authConfig != nil {
		c.authConfig.SetEnabled(false)
	}
}

// GetAuthType returns the type of authentication configured, or empty string if none
func (c *HTTPClient) GetAuthType() string {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if c.authConfig == nil {
		return ""
	}

	return c.authConfig.Type.String()
}

// NewHTTPClient creates a new HTTPClient with the given configuration
func NewHTTPClient(config *Config) (*HTTPClient, error) {
	if config == nil {
		config = &Config{}
	}

	// Set default HTTP version to 1.1 if not specified
	if config.HTTPVersion == "" {
		config.HTTPVersion = "1.1"
	}

	// Initialize transport based on HTTP version
	var transport *http.Transport
	if config.HTTPVersion == "2.0" {
		// Enable HTTP/2
		transport = &http.Transport{
			TLSNextProto: make(map[string]func(authority string, c *tls.Conn) http.RoundTripper),
		}
		transport.TLSNextProto["h2"] = http2.RoundTripper
	} else {
		// Use default HTTP/1.1 transport
		transport = &http.Transport{}
	}

	// Initialize compression configuration
	var compression *compressionConfig
	if config.EnableCompression {
		compression = &compressionConfig{
			level: config.CompressionLevel,
		}
	}

	client := &HTTPClient{
		client:            &http.Client{Transport: transport},
		config:            config,
		transport:         transport,
		stats:             &ConnectionStats{},
		httpMethodStats:   &HTTPMethodStats{mu: &sync.RWMutex{}},
		retryConfig:       &RetryConfig{},
		mu:                sync.RWMutex{},
		pipelinedRequests: make(map[*http.Request]chan *http.Response),
		compression:       compression,
	}

	// Configure pipelining if enabled
	if config.EnablePipelining && config.HTTPVersion == "1.1" {
		transport.DisableKeepAlives = false // Keep connections alive for pipelining
		transport.MaxIdleConnsPerHost = 100 // Increase max idle connections for better pipelining
	}

	return client, nil
}
