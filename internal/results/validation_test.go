// Package results provides validation tests that work on development hardware
package results

import (
	"testing"
	"time"
)

// TestResultAggregatorValidation tests core functionality without performance stress
func TestResultAggregatorValidation(t *testing.T) {
	// Test 1: Basic aggregator creation and configuration
	config := DefaultResultAggregatorConfig()
	if config.BufferSize <= 0 {
		t.Error("Default config should have positive buffer size")
	}
	if config.AggregationInterval <= 0 {
		t.<PERSON><PERSON><PERSON>("Default config should have positive aggregation interval")
	}

	aggregator := NewResultAggregator(config)
	if aggregator == nil {
		t.Fatal("NewResultAggregator should not return nil")
	}

	// Test 2: Grouping strategy assignment
	strategy := NewScenarioGroupingStrategy()
	aggregator.SetGroupingStrategy(strategy)

	// Test 3: Manual result processing (no goroutines)
	result1 := createTestResult("worker1", "scenario1", true)
	result2 := createTestResult("worker1", "scenario1", false)
	result3 := createTestResult("worker2", "scenario2", true)

	// Add results manually to buffer
	aggregator.addResultToBuffer(result1)
	aggregator.addResultToBuffer(result2)
	aggregator.addResultToBuffer(result3)

	// Verify buffer contents
	if aggregator.GetResultCount() != 3 {
		t.Errorf("Expected 3 results in buffer, got %d", aggregator.GetResultCount())
	}

	// Test 4: Manual aggregation (no goroutines)
	aggregator.aggregateResults()

	// Verify aggregated results
	allResults := aggregator.GetAllAggregatedResults()
	if len(allResults) != 2 {
		t.Errorf("Expected 2 aggregated groups, got %d", len(allResults))
	}

	// Test scenario1 aggregation
	if agg, exists := allResults["scenario:scenario1"]; exists {
		if agg.TotalCount != 2 {
			t.Errorf("Scenario1: expected total count 2, got %d", agg.TotalCount)
		}
		if agg.SuccessCount != 1 {
			t.Errorf("Scenario1: expected success count 1, got %d", agg.SuccessCount)
		}
		if agg.FailureCount != 1 {
			t.Errorf("Scenario1: expected failure count 1, got %d", agg.FailureCount)
		}
		if agg.SuccessRate != 0.5 {
			t.Errorf("Scenario1: expected success rate 0.5, got %f", agg.SuccessRate)
		}
	} else {
		t.Error("Expected scenario1 aggregated result not found")
	}

	// Test scenario2 aggregation
	if agg, exists := allResults["scenario:scenario2"]; exists {
		if agg.TotalCount != 1 {
			t.Errorf("Scenario2: expected total count 1, got %d", agg.TotalCount)
		}
		if agg.SuccessCount != 1 {
			t.Errorf("Scenario2: expected success count 1, got %d", agg.SuccessCount)
		}
		if agg.SuccessRate != 1.0 {
			t.Errorf("Scenario2: expected success rate 1.0, got %f", agg.SuccessRate)
		}
	} else {
		t.Error("Expected scenario2 aggregated result not found")
	}
}

// TestGroupingStrategiesValidation tests all grouping strategies
func TestGroupingStrategiesValidation(t *testing.T) {
	result := createTestResult("worker1", "scenario1", true)
	result.StatusCode = 404
	result.Tags = map[string]string{"environment": "test"}

	strategies := GetDefaultGroupingStrategies()

	expectedKeys := map[string]string{
		"scenario":    "scenario:scenario1",
		"worker":      "worker:worker1",
		"status":      "status:success",
		"request":     "request:test_request",
		"status_code": "status_code:404",
	}

	for name, strategy := range strategies {
		if expected, exists := expectedKeys[name]; exists {
			key := strategy.GroupKey(result)
			if key != expected {
				t.Errorf("Strategy %s: expected key '%s', got '%s'", name, expected, key)
			}
		}
	}
}

// TestStorageValidation tests storage implementations
func TestStorageValidation(t *testing.T) {
	// Test memory storage
	storage := NewMemoryStorage(5)
	result := createTestAggregatedResult("test_key", "scenario")

	// Test store/retrieve cycle
	err := storage.Store("test_key", result)
	if err != nil {
		t.Errorf("Failed to store result: %v", err)
	}

	retrieved, err := storage.Retrieve("test_key")
	if err != nil {
		t.Errorf("Failed to retrieve result: %v", err)
	}

	if retrieved.GroupKey != result.GroupKey {
		t.Errorf("Expected group key '%s', got '%s'", result.GroupKey, retrieved.GroupKey)
	}

	// Test list functionality
	keys, err := storage.List()
	if err != nil {
		t.Errorf("Failed to list keys: %v", err)
	}

	if len(keys) != 1 || keys[0] != "test_key" {
		t.Errorf("Expected keys ['test_key'], got %v", keys)
	}

	// Test delete functionality
	err = storage.Delete("test_key")
	if err != nil {
		t.Errorf("Failed to delete result: %v", err)
	}

	_, err = storage.Retrieve("test_key")
	if err == nil {
		t.Error("Expected error when retrieving deleted result")
	}
}

// TestStatisticalCalculations tests the statistical calculation logic
func TestStatisticalCalculations(t *testing.T) {
	config := DefaultResultAggregatorConfig()
	aggregator := NewResultAggregator(config)
	aggregator.SetGroupingStrategy(NewScenarioGroupingStrategy())

	// Create results with known durations for predictable statistics
	results := []TestResult{
		createTestResultWithDuration("worker1", "scenario1", true, 100*time.Millisecond),
		createTestResultWithDuration("worker1", "scenario1", true, 200*time.Millisecond),
		createTestResultWithDuration("worker1", "scenario1", true, 300*time.Millisecond),
		createTestResultWithDuration("worker1", "scenario1", false, 400*time.Millisecond),
		createTestResultWithDuration("worker1", "scenario1", true, 500*time.Millisecond),
	}

	// Calculate aggregation manually
	aggregated := aggregator.calculateAggregation("test_group", results)

	// Verify basic counts
	if aggregated.TotalCount != 5 {
		t.Errorf("Expected total count 5, got %d", aggregated.TotalCount)
	}
	if aggregated.SuccessCount != 4 {
		t.Errorf("Expected success count 4, got %d", aggregated.SuccessCount)
	}
	if aggregated.FailureCount != 1 {
		t.Errorf("Expected failure count 1, got %d", aggregated.FailureCount)
	}

	// Verify success rate
	expectedRate := 4.0 / 5.0
	if aggregated.SuccessRate != expectedRate {
		t.Errorf("Expected success rate %f, got %f", expectedRate, aggregated.SuccessRate)
	}

	// Verify duration statistics
	if aggregated.MinDuration != 100*time.Millisecond {
		t.Errorf("Expected min duration 100ms, got %v", aggregated.MinDuration)
	}
	if aggregated.MaxDuration != 500*time.Millisecond {
		t.Errorf("Expected max duration 500ms, got %v", aggregated.MaxDuration)
	}

	// Verify median (middle value of sorted [100, 200, 300, 400, 500] is 300)
	if aggregated.MedianDuration != 300*time.Millisecond {
		t.Errorf("Expected median duration 300ms, got %v", aggregated.MedianDuration)
	}

	// Verify average duration
	expectedAvg := (100 + 200 + 300 + 400 + 500) * time.Millisecond / 5
	if aggregated.AvgDuration != expectedAvg {
		t.Errorf("Expected avg duration %v, got %v", expectedAvg, aggregated.AvgDuration)
	}
}

// TestProductionScaleNote documents the need for production testing
func TestProductionScaleNote(t *testing.T) {
	t.Log("=== PRODUCTION TESTING REQUIRED ===")
	t.Log("This result aggregation system is designed for high-performance scenarios")
	t.Log("Current tests validate core functionality on development hardware")
	t.Log("Production testing should include:")
	t.Log("- High-volume result processing (10k+ results/second)")
	t.Log("- Multiple concurrent workers (100+ workers)")
	t.Log("- Extended duration tests (hours of continuous operation)")
	t.Log("- Memory usage and garbage collection under load")
	t.Log("- Storage backend performance with large datasets")
	t.Log("- Metrics integration performance impact")
	t.Log("=====================================")
}

// Note: Helper functions createTestResultWithDuration and createTestAggregatedResult
// are defined in other test files to avoid duplication
