// Package results provides storage implementations for aggregated results
package results

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// MemoryStorage provides in-memory storage for aggregated results
type MemoryStorage struct {
	mu      sync.RWMutex
	data    map[string]*AggregatedResult
	maxSize int
}

// NewMemoryStorage creates a new in-memory storage
func NewMemoryStorage(maxSize int) *MemoryStorage {
	return &MemoryStorage{
		data:    make(map[string]*AggregatedResult),
		maxSize: maxSize,
	}
}

// Store stores an aggregated result
func (ms *MemoryStorage) Store(key string, result *AggregatedResult) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	// Check size limit
	if len(ms.data) >= ms.maxSize && ms.data[key] == nil {
		return fmt.Errorf("storage is full (max size: %d)", ms.maxSize)
	}

	// Store a copy to avoid external modifications
	resultCopy := *result
	ms.data[key] = &resultCopy
	return nil
}

// Retrieve retrieves an aggregated result
func (ms *MemoryStorage) Retrieve(key string) (*AggregatedResult, error) {
	ms.mu.RLock()
	defer ms.mu.RUnlock()

	if result, exists := ms.data[key]; exists {
		// Return a copy to avoid external modifications
		resultCopy := *result
		return &resultCopy, nil
	}

	return nil, fmt.Errorf("result not found for key: %s", key)
}

// List returns all stored keys
func (ms *MemoryStorage) List() ([]string, error) {
	ms.mu.RLock()
	defer ms.mu.RUnlock()

	keys := make([]string, 0, len(ms.data))
	for key := range ms.data {
		keys = append(keys, key)
	}

	return keys, nil
}

// Delete removes a stored result
func (ms *MemoryStorage) Delete(key string) error {
	ms.mu.Lock()
	defer ms.mu.Unlock()

	if _, exists := ms.data[key]; !exists {
		return fmt.Errorf("result not found for key: %s", key)
	}

	delete(ms.data, key)
	return nil
}

// Close closes the storage (no-op for memory storage)
func (ms *MemoryStorage) Close() error {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	
	// Clear all data
	ms.data = make(map[string]*AggregatedResult)
	return nil
}

// Size returns the current number of stored results
func (ms *MemoryStorage) Size() int {
	ms.mu.RLock()
	defer ms.mu.RUnlock()
	return len(ms.data)
}

// FileStorage provides file-based storage for aggregated results
type FileStorage struct {
	mu       sync.RWMutex
	basePath string
	maxFiles int
}

// NewFileStorage creates a new file-based storage
func NewFileStorage(basePath string, maxFiles int) (*FileStorage, error) {
	// Create directory if it doesn't exist
	if err := os.MkdirAll(basePath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create storage directory: %v", err)
	}

	return &FileStorage{
		basePath: basePath,
		maxFiles: maxFiles,
	}, nil
}

// Store stores an aggregated result to a file
func (fs *FileStorage) Store(key string, result *AggregatedResult) error {
	fs.mu.Lock()
	defer fs.mu.Unlock()

	// Check file count limit
	files, err := fs.listFiles()
	if err != nil {
		return fmt.Errorf("failed to list files: %v", err)
	}

	filename := fs.keyToFilename(key)
	fileExists := false
	for _, file := range files {
		if file == filename {
			fileExists = true
			break
		}
	}

	if len(files) >= fs.maxFiles && !fileExists {
		return fmt.Errorf("storage is full (max files: %d)", fs.maxFiles)
	}

	// Marshal result to JSON
	data, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal result: %v", err)
	}

	// Write to file
	filePath := filepath.Join(fs.basePath, filename)
	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("failed to write file: %v", err)
	}

	return nil
}

// Retrieve retrieves an aggregated result from a file
func (fs *FileStorage) Retrieve(key string) (*AggregatedResult, error) {
	fs.mu.RLock()
	defer fs.mu.RUnlock()

	filename := fs.keyToFilename(key)
	filePath := filepath.Join(fs.basePath, filename)

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("result not found for key: %s", key)
	}

	// Read file
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %v", err)
	}

	// Unmarshal JSON
	var result AggregatedResult
	if err := json.Unmarshal(data, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal result: %v", err)
	}

	return &result, nil
}

// List returns all stored keys
func (fs *FileStorage) List() ([]string, error) {
	fs.mu.RLock()
	defer fs.mu.RUnlock()

	files, err := fs.listFiles()
	if err != nil {
		return nil, err
	}

	keys := make([]string, 0, len(files))
	for _, filename := range files {
		key := fs.filenameToKey(filename)
		keys = append(keys, key)
	}

	return keys, nil
}

// Delete removes a stored result file
func (fs *FileStorage) Delete(key string) error {
	fs.mu.Lock()
	defer fs.mu.Unlock()

	filename := fs.keyToFilename(key)
	filePath := filepath.Join(fs.basePath, filename)

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("result not found for key: %s", key)
	}

	// Remove file
	if err := os.Remove(filePath); err != nil {
		return fmt.Errorf("failed to remove file: %v", err)
	}

	return nil
}

// Close closes the storage (no-op for file storage)
func (fs *FileStorage) Close() error {
	return nil
}

// listFiles returns all JSON files in the storage directory
func (fs *FileStorage) listFiles() ([]string, error) {
	entries, err := os.ReadDir(fs.basePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read directory: %v", err)
	}

	var files []string
	for _, entry := range entries {
		if !entry.IsDir() && filepath.Ext(entry.Name()) == ".json" {
			files = append(files, entry.Name())
		}
	}

	return files, nil
}

// keyToFilename converts a key to a safe filename
func (fs *FileStorage) keyToFilename(key string) string {
	// Replace unsafe characters with underscores
	safe := ""
	for _, r := range key {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') || r == '-' || r == '_' {
			safe += string(r)
		} else {
			safe += "_"
		}
	}
	return safe + ".json"
}

// filenameToKey converts a filename back to a key (best effort)
func (fs *FileStorage) filenameToKey(filename string) string {
	// Remove .json extension
	if filepath.Ext(filename) == ".json" {
		return filename[:len(filename)-5]
	}
	return filename
}

// CompositeStorage combines multiple storage backends
type CompositeStorage struct {
	primary   ResultStorage
	secondary []ResultStorage
}

// NewCompositeStorage creates a new composite storage
func NewCompositeStorage(primary ResultStorage, secondary ...ResultStorage) *CompositeStorage {
	return &CompositeStorage{
		primary:   primary,
		secondary: secondary,
	}
}

// Store stores to primary and all secondary storages
func (cs *CompositeStorage) Store(key string, result *AggregatedResult) error {
	// Store to primary first
	if err := cs.primary.Store(key, result); err != nil {
		return fmt.Errorf("primary storage failed: %v", err)
	}

	// Store to secondary storages (best effort)
	for i, storage := range cs.secondary {
		if err := storage.Store(key, result); err != nil {
			// Log error but don't fail the operation
			fmt.Printf("Secondary storage %d failed: %v\n", i, err)
		}
	}

	return nil
}

// Retrieve retrieves from primary storage
func (cs *CompositeStorage) Retrieve(key string) (*AggregatedResult, error) {
	return cs.primary.Retrieve(key)
}

// List lists from primary storage
func (cs *CompositeStorage) List() ([]string, error) {
	return cs.primary.List()
}

// Delete deletes from primary and all secondary storages
func (cs *CompositeStorage) Delete(key string) error {
	// Delete from primary first
	if err := cs.primary.Delete(key); err != nil {
		return fmt.Errorf("primary storage failed: %v", err)
	}

	// Delete from secondary storages (best effort)
	for i, storage := range cs.secondary {
		if err := storage.Delete(key); err != nil {
			// Log error but don't fail the operation
			fmt.Printf("Secondary storage %d failed: %v\n", i, err)
		}
	}

	return nil
}

// Close closes all storages
func (cs *CompositeStorage) Close() error {
	var errors []error

	if err := cs.primary.Close(); err != nil {
		errors = append(errors, fmt.Errorf("primary storage: %v", err))
	}

	for i, storage := range cs.secondary {
		if err := storage.Close(); err != nil {
			errors = append(errors, fmt.Errorf("secondary storage %d: %v", i, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("storage close errors: %v", errors)
	}

	return nil
}

// TTLStorage wraps another storage with time-to-live functionality
type TTLStorage struct {
	storage ResultStorage
	ttl     time.Duration
	mu      sync.RWMutex
	expiry  map[string]time.Time
}

// NewTTLStorage creates a new TTL storage wrapper
func NewTTLStorage(storage ResultStorage, ttl time.Duration) *TTLStorage {
	return &TTLStorage{
		storage: storage,
		ttl:     ttl,
		expiry:  make(map[string]time.Time),
	}
}

// Store stores with TTL tracking
func (ts *TTLStorage) Store(key string, result *AggregatedResult) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	if err := ts.storage.Store(key, result); err != nil {
		return err
	}

	ts.expiry[key] = time.Now().Add(ts.ttl)
	return nil
}

// Retrieve retrieves if not expired
func (ts *TTLStorage) Retrieve(key string) (*AggregatedResult, error) {
	ts.mu.RLock()
	expireTime, exists := ts.expiry[key]
	ts.mu.RUnlock()

	if !exists || time.Now().After(expireTime) {
		// Clean up expired entry
		ts.mu.Lock()
		delete(ts.expiry, key)
		ts.mu.Unlock()
		
		ts.storage.Delete(key) // Best effort cleanup
		return nil, fmt.Errorf("result expired or not found for key: %s", key)
	}

	return ts.storage.Retrieve(key)
}

// List returns non-expired keys
func (ts *TTLStorage) List() ([]string, error) {
	allKeys, err := ts.storage.List()
	if err != nil {
		return nil, err
	}

	ts.mu.Lock()
	defer ts.mu.Unlock()

	var validKeys []string
	now := time.Now()

	for _, key := range allKeys {
		if expireTime, exists := ts.expiry[key]; exists && now.Before(expireTime) {
			validKeys = append(validKeys, key)
		} else {
			// Clean up expired entry
			delete(ts.expiry, key)
			ts.storage.Delete(key) // Best effort cleanup
		}
	}

	return validKeys, nil
}

// Delete deletes and removes from TTL tracking
func (ts *TTLStorage) Delete(key string) error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	delete(ts.expiry, key)
	return ts.storage.Delete(key)
}

// Close closes the underlying storage
func (ts *TTLStorage) Close() error {
	ts.mu.Lock()
	defer ts.mu.Unlock()
	
	ts.expiry = make(map[string]time.Time)
	return ts.storage.Close()
}
