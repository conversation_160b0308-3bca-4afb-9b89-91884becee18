// Package results provides integration with the metrics system
package results

import (
	"context"
	"fmt"
	"sync"
	"time"

	"neuralmetergo/internal/metrics"
)

// MetricsIntegration provides integration between result aggregation and metrics system
type MetricsIntegration struct {
	aggregator        *ResultAggregator
	metricsIntegrator *metrics.MetricsIntegrator
	config            MetricsIntegrationConfig
	mu                sync.RWMutex
	isRunning         bool
	ctx               context.Context
	cancel            context.CancelFunc
	wg                sync.WaitGroup
	updateTicker      *time.Ticker
}

// MetricsIntegrationConfig defines configuration for metrics integration
type MetricsIntegrationConfig struct {
	UpdateInterval     time.Duration `json:"update_interval"`
	EnableMetricExport bool          `json:"enable_metric_export"`
	MetricPrefix       string        `json:"metric_prefix"`
	ExportTags         bool          `json:"export_tags"`
	EnableAlerts       bool          `json:"enable_alerts"`
	AlertThresholds    AlertConfig   `json:"alert_thresholds"`
}

// AlertConfig defines alert thresholds for result metrics
type AlertConfig struct {
	ErrorRateThreshold     float64       `json:"error_rate_threshold"`
	LatencyP95Threshold    time.Duration `json:"latency_p95_threshold"`
	LatencyP99Threshold    time.Duration `json:"latency_p99_threshold"`
	ThroughputMinThreshold float64       `json:"throughput_min_threshold"`
}

// DefaultMetricsIntegrationConfig returns default configuration
func DefaultMetricsIntegrationConfig() MetricsIntegrationConfig {
	return MetricsIntegrationConfig{
		UpdateInterval:     10 * time.Second,
		EnableMetricExport: true,
		MetricPrefix:       "neuralmeter.results",
		ExportTags:         true,
		EnableAlerts:       false,
		AlertThresholds: AlertConfig{
			ErrorRateThreshold:     0.05, // 5%
			LatencyP95Threshold:    5 * time.Second,
			LatencyP99Threshold:    10 * time.Second,
			ThroughputMinThreshold: 10.0, // requests per second
		},
	}
}

// NewMetricsIntegration creates a new metrics integration
func NewMetricsIntegration(aggregator *ResultAggregator, metricsIntegrator *metrics.MetricsIntegrator, config MetricsIntegrationConfig) *MetricsIntegration {
	ctx, cancel := context.WithCancel(context.Background())

	return &MetricsIntegration{
		aggregator:        aggregator,
		metricsIntegrator: metricsIntegrator,
		config:            config,
		ctx:               ctx,
		cancel:            cancel,
	}
}

// Start begins the metrics integration
func (mi *MetricsIntegration) Start() error {
	mi.mu.Lock()
	defer mi.mu.Unlock()

	if mi.isRunning {
		return fmt.Errorf("metrics integration is already running")
	}

	mi.isRunning = true
	mi.updateTicker = time.NewTicker(mi.config.UpdateInterval)

	// Start metrics export goroutine
	mi.wg.Add(1)
	go mi.exportMetrics()

	return nil
}

// Stop stops the metrics integration
func (mi *MetricsIntegration) Stop() error {
	mi.mu.Lock()
	defer mi.mu.Unlock()

	if !mi.isRunning {
		return fmt.Errorf("metrics integration is not running")
	}

	mi.isRunning = false
	mi.cancel()

	if mi.updateTicker != nil {
		mi.updateTicker.Stop()
	}

	mi.wg.Wait()
	return nil
}

// exportMetrics exports aggregated results as metrics
func (mi *MetricsIntegration) exportMetrics() {
	defer mi.wg.Done()

	for {
		select {
		case <-mi.updateTicker.C:
			mi.performMetricsExport()
		case <-mi.ctx.Done():
			return
		}
	}
}

// performMetricsExport performs the actual metrics export
func (mi *MetricsIntegration) performMetricsExport() {
	if !mi.config.EnableMetricExport {
		return
	}

	// Get all aggregated results
	aggregatedResults := mi.aggregator.GetAllAggregatedResults()

	for groupKey, result := range aggregatedResults {
		mi.exportResultAsMetrics(groupKey, result)
	}
}

// exportResultAsMetrics exports a single aggregated result as metrics
func (mi *MetricsIntegration) exportResultAsMetrics(groupKey string, result *AggregatedResult) {
	prefix := mi.config.MetricPrefix
	if prefix == "" {
		prefix = "neuralmeter.results"
	}

	// Prepare tags
	tags := make(map[string]string)
	if mi.config.ExportTags {
		tags["group_key"] = groupKey
		tags["group_type"] = result.GroupType
	}

	// Export basic metrics
	mi.addMetric(fmt.Sprintf("%s.total_count", prefix), float64(result.TotalCount), tags)
	mi.addMetric(fmt.Sprintf("%s.success_count", prefix), float64(result.SuccessCount), tags)
	mi.addMetric(fmt.Sprintf("%s.failure_count", prefix), float64(result.FailureCount), tags)
	mi.addMetric(fmt.Sprintf("%s.success_rate", prefix), result.SuccessRate, tags)

	// Export duration metrics (convert to milliseconds)
	mi.addMetric(fmt.Sprintf("%s.duration.min_ms", prefix), float64(result.MinDuration.Nanoseconds())/1e6, tags)
	mi.addMetric(fmt.Sprintf("%s.duration.max_ms", prefix), float64(result.MaxDuration.Nanoseconds())/1e6, tags)
	mi.addMetric(fmt.Sprintf("%s.duration.avg_ms", prefix), float64(result.AvgDuration.Nanoseconds())/1e6, tags)
	mi.addMetric(fmt.Sprintf("%s.duration.median_ms", prefix), float64(result.MedianDuration.Nanoseconds())/1e6, tags)
	mi.addMetric(fmt.Sprintf("%s.duration.p90_ms", prefix), float64(result.P90Duration.Nanoseconds())/1e6, tags)
	mi.addMetric(fmt.Sprintf("%s.duration.p95_ms", prefix), float64(result.P95Duration.Nanoseconds())/1e6, tags)
	mi.addMetric(fmt.Sprintf("%s.duration.p99_ms", prefix), float64(result.P99Duration.Nanoseconds())/1e6, tags)

	// Export throughput metrics
	if result.TimeWindow > 0 {
		throughput := float64(result.TotalCount) / result.TimeWindow.Seconds()
		mi.addMetric(fmt.Sprintf("%s.throughput_rps", prefix), throughput, tags)
	}

	// Export byte metrics
	mi.addMetric(fmt.Sprintf("%s.total_bytes", prefix), float64(result.TotalBytes), tags)
	mi.addMetric(fmt.Sprintf("%s.avg_bytes", prefix), result.AvgBytes, tags)

	// Check alerts if enabled
	if mi.config.EnableAlerts {
		mi.checkAlerts(groupKey, result)
	}
}

// addMetric adds a metric value to the metrics system
func (mi *MetricsIntegration) addMetric(metricName string, value float64, tags map[string]string) {
	if mi.metricsIntegrator != nil {
		if err := mi.metricsIntegrator.AddMetricValue(metricName, value, tags); err != nil {
			// Log error but continue processing
			fmt.Printf("Failed to add metric %s: %v\n", metricName, err)
		}
	}
}

// checkAlerts checks if any alert thresholds are exceeded
func (mi *MetricsIntegration) checkAlerts(groupKey string, result *AggregatedResult) {
	alerts := mi.config.AlertThresholds

	// Check error rate
	if result.SuccessRate < (1.0 - alerts.ErrorRateThreshold) {
		mi.triggerAlert("high_error_rate", groupKey, fmt.Sprintf("Error rate %.2f%% exceeds threshold %.2f%%",
			(1.0-result.SuccessRate)*100, alerts.ErrorRateThreshold*100))
	}

	// Check P95 latency
	if result.P95Duration > alerts.LatencyP95Threshold {
		mi.triggerAlert("high_p95_latency", groupKey, fmt.Sprintf("P95 latency %v exceeds threshold %v",
			result.P95Duration, alerts.LatencyP95Threshold))
	}

	// Check P99 latency
	if result.P99Duration > alerts.LatencyP99Threshold {
		mi.triggerAlert("high_p99_latency", groupKey, fmt.Sprintf("P99 latency %v exceeds threshold %v",
			result.P99Duration, alerts.LatencyP99Threshold))
	}

	// Check throughput
	if result.TimeWindow > 0 {
		throughput := float64(result.TotalCount) / result.TimeWindow.Seconds()
		if throughput < alerts.ThroughputMinThreshold {
			mi.triggerAlert("low_throughput", groupKey, fmt.Sprintf("Throughput %.2f RPS below threshold %.2f RPS",
				throughput, alerts.ThroughputMinThreshold))
		}
	}
}

// triggerAlert triggers an alert (placeholder implementation)
func (mi *MetricsIntegration) triggerAlert(alertType, groupKey, message string) {
	// This is a placeholder - in a real implementation, this would integrate
	// with an alerting system like PagerDuty, Slack, email, etc.
	fmt.Printf("ALERT [%s] Group: %s - %s\n", alertType, groupKey, message)

	// Add alert as a metric
	tags := map[string]string{
		"alert_type": alertType,
		"group_key":  groupKey,
	}
	mi.addMetric(fmt.Sprintf("%s.alerts.triggered", mi.config.MetricPrefix), 1.0, tags)
}

// GetMetricsStats returns statistics about the metrics integration
func (mi *MetricsIntegration) GetMetricsStats() MetricsIntegrationStats {
	mi.mu.RLock()
	defer mi.mu.RUnlock()

	return MetricsIntegrationStats{
		IsRunning:            mi.isRunning,
		UpdateInterval:       mi.config.UpdateInterval,
		MetricsExportEnabled: mi.config.EnableMetricExport,
		AlertsEnabled:        mi.config.EnableAlerts,
		LastExportTime:       time.Now(), // This would be tracked in a real implementation
	}
}

// MetricsIntegrationStats provides statistics about the metrics integration
type MetricsIntegrationStats struct {
	IsRunning            bool          `json:"is_running"`
	UpdateInterval       time.Duration `json:"update_interval"`
	MetricsExportEnabled bool          `json:"metrics_export_enabled"`
	AlertsEnabled        bool          `json:"alerts_enabled"`
	LastExportTime       time.Time     `json:"last_export_time"`
}

// IsRunning returns whether the integration is currently running
func (mi *MetricsIntegration) IsRunning() bool {
	mi.mu.RLock()
	defer mi.mu.RUnlock()
	return mi.isRunning
}

// ResultToTestResult converts engine result types to our TestResult format
func ResultToTestResult(workerID string, engineResult interface{}) (*TestResult, error) {
	// This function would convert from the engine's result format to our TestResult format
	// The exact implementation depends on the engine's result structure

	// Placeholder implementation - this would need to be customized based on
	// the actual result types from the engine/coordinator
	result := &TestResult{
		ID:        fmt.Sprintf("result_%d", time.Now().UnixNano()),
		WorkerID:  workerID,
		Timestamp: time.Now(),
		Tags:      make(map[string]string),
		Metadata:  make(map[string]interface{}),
	}

	// TODO: Implement actual conversion logic based on engine result types
	// This would extract fields like:
	// - ScenarioName, RequestName from the engine result
	// - StartTime, EndTime, Duration
	// - Success status, StatusCode, Error message
	// - ResponseSize and other metrics

	return result, nil
}

// CreateResultFromCoordinatorResult creates a TestResult from coordinator result
func CreateResultFromCoordinatorResult(workerID string, coordinatorResult interface{}) (*TestResult, error) {
	// This would be implemented to convert from the coordinator's RequestResult
	// to our TestResult format, integrating with the existing engine types

	// Placeholder implementation
	return &TestResult{
		ID:        fmt.Sprintf("coord_result_%d", time.Now().UnixNano()),
		WorkerID:  workerID,
		Timestamp: time.Now(),
		Tags:      make(map[string]string),
		Metadata:  make(map[string]interface{}),
	}, nil
}
