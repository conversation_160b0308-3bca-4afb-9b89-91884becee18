// Package results provides grouping strategies for result aggregation
package results

import (
	"fmt"
	"strings"
)

// ScenarioGroupingStrategy groups results by scenario name
type ScenarioGroupingStrategy struct{}

// GroupKey returns the group key for scenario-based grouping
func (s *ScenarioGroupingStrategy) GroupKey(result TestResult) string {
	return fmt.Sprintf("scenario:%s", result.ScenarioName)
}

// GroupType returns the group type identifier
func (s *ScenarioGroupingStrategy) GroupType() string {
	return "scenario"
}

// WorkerGroupingStrategy groups results by worker ID
type WorkerGroupingStrategy struct{}

// GroupKey returns the group key for worker-based grouping
func (w *WorkerGroupingStrategy) GroupKey(result TestResult) string {
	return fmt.Sprintf("worker:%s", result.WorkerID)
}

// GroupType returns the group type identifier
func (w *WorkerGroupingStrategy) GroupType() string {
	return "worker"
}

// StatusGroupingStrategy groups results by success/failure status
type StatusGroupingStrategy struct{}

// GroupKey returns the group key for status-based grouping
func (s *StatusGroupingStrategy) GroupKey(result TestResult) string {
	if result.Success {
		return "status:success"
	}
	return "status:failure"
}

// GroupType returns the group type identifier
func (s *StatusGroupingStrategy) GroupType() string {
	return "status"
}

// RequestGroupingStrategy groups results by request name
type RequestGroupingStrategy struct{}

// GroupKey returns the group key for request-based grouping
func (r *RequestGroupingStrategy) GroupKey(result TestResult) string {
	return fmt.Sprintf("request:%s", result.RequestName)
}

// GroupType returns the group type identifier
func (r *RequestGroupingStrategy) GroupType() string {
	return "request"
}

// StatusCodeGroupingStrategy groups results by HTTP status code
type StatusCodeGroupingStrategy struct{}

// GroupKey returns the group key for status code-based grouping
func (s *StatusCodeGroupingStrategy) GroupKey(result TestResult) string {
	return fmt.Sprintf("status_code:%d", result.StatusCode)
}

// GroupType returns the group type identifier
func (s *StatusCodeGroupingStrategy) GroupType() string {
	return "status_code"
}

// TimeWindowGroupingStrategy groups results by time windows
type TimeWindowGroupingStrategy struct {
	WindowSize string // "1m", "5m", "15m", "1h", etc.
}

// GroupKey returns the group key for time window-based grouping
func (t *TimeWindowGroupingStrategy) GroupKey(result TestResult) string {
	// Truncate timestamp to the specified window
	var truncated string
	switch t.WindowSize {
	case "1m":
		truncated = result.Timestamp.Truncate(60).Format("2006-01-02T15:04")
	case "5m":
		truncated = result.Timestamp.Truncate(300).Format("2006-01-02T15:04")
	case "15m":
		truncated = result.Timestamp.Truncate(900).Format("2006-01-02T15:04")
	case "1h":
		truncated = result.Timestamp.Truncate(3600).Format("2006-01-02T15")
	default:
		truncated = result.Timestamp.Truncate(300).Format("2006-01-02T15:04") // Default to 5m
	}
	return fmt.Sprintf("time_window:%s", truncated)
}

// GroupType returns the group type identifier
func (t *TimeWindowGroupingStrategy) GroupType() string {
	return fmt.Sprintf("time_window_%s", t.WindowSize)
}

// TagGroupingStrategy groups results by specific tag values
type TagGroupingStrategy struct {
	TagKey string
}

// GroupKey returns the group key for tag-based grouping
func (t *TagGroupingStrategy) GroupKey(result TestResult) string {
	if value, exists := result.Tags[t.TagKey]; exists {
		return fmt.Sprintf("tag:%s:%s", t.TagKey, value)
	}
	return fmt.Sprintf("tag:%s:untagged", t.TagKey)
}

// GroupType returns the group type identifier
func (t *TagGroupingStrategy) GroupType() string {
	return fmt.Sprintf("tag_%s", t.TagKey)
}

// CompositeGroupingStrategy combines multiple grouping strategies
type CompositeGroupingStrategy struct {
	Strategies []GroupingStrategy
	Separator  string
}

// GroupKey returns the group key for composite grouping
func (c *CompositeGroupingStrategy) GroupKey(result TestResult) string {
	var keys []string
	for _, strategy := range c.Strategies {
		keys = append(keys, strategy.GroupKey(result))
	}
	
	separator := c.Separator
	if separator == "" {
		separator = "|"
	}
	
	return strings.Join(keys, separator)
}

// GroupType returns the group type identifier
func (c *CompositeGroupingStrategy) GroupType() string {
	var types []string
	for _, strategy := range c.Strategies {
		types = append(types, strategy.GroupType())
	}
	return strings.Join(types, "_")
}

// CustomGroupingStrategy allows for custom grouping logic
type CustomGroupingStrategy struct {
	GroupKeyFunc func(TestResult) string
	GroupTypeName string
}

// GroupKey returns the group key using the custom function
func (c *CustomGroupingStrategy) GroupKey(result TestResult) string {
	if c.GroupKeyFunc != nil {
		return c.GroupKeyFunc(result)
	}
	return "custom:unknown"
}

// GroupType returns the group type identifier
func (c *CustomGroupingStrategy) GroupType() string {
	if c.GroupTypeName != "" {
		return c.GroupTypeName
	}
	return "custom"
}

// NewScenarioGroupingStrategy creates a new scenario grouping strategy
func NewScenarioGroupingStrategy() GroupingStrategy {
	return &ScenarioGroupingStrategy{}
}

// NewWorkerGroupingStrategy creates a new worker grouping strategy
func NewWorkerGroupingStrategy() GroupingStrategy {
	return &WorkerGroupingStrategy{}
}

// NewStatusGroupingStrategy creates a new status grouping strategy
func NewStatusGroupingStrategy() GroupingStrategy {
	return &StatusGroupingStrategy{}
}

// NewRequestGroupingStrategy creates a new request grouping strategy
func NewRequestGroupingStrategy() GroupingStrategy {
	return &RequestGroupingStrategy{}
}

// NewStatusCodeGroupingStrategy creates a new status code grouping strategy
func NewStatusCodeGroupingStrategy() GroupingStrategy {
	return &StatusCodeGroupingStrategy{}
}

// NewTimeWindowGroupingStrategy creates a new time window grouping strategy
func NewTimeWindowGroupingStrategy(windowSize string) GroupingStrategy {
	return &TimeWindowGroupingStrategy{WindowSize: windowSize}
}

// NewTagGroupingStrategy creates a new tag grouping strategy
func NewTagGroupingStrategy(tagKey string) GroupingStrategy {
	return &TagGroupingStrategy{TagKey: tagKey}
}

// NewCompositeGroupingStrategy creates a new composite grouping strategy
func NewCompositeGroupingStrategy(strategies []GroupingStrategy, separator string) GroupingStrategy {
	return &CompositeGroupingStrategy{
		Strategies: strategies,
		Separator:  separator,
	}
}

// NewCustomGroupingStrategy creates a new custom grouping strategy
func NewCustomGroupingStrategy(groupKeyFunc func(TestResult) string, groupTypeName string) GroupingStrategy {
	return &CustomGroupingStrategy{
		GroupKeyFunc:  groupKeyFunc,
		GroupTypeName: groupTypeName,
	}
}

// GetDefaultGroupingStrategies returns a set of commonly used grouping strategies
func GetDefaultGroupingStrategies() map[string]GroupingStrategy {
	return map[string]GroupingStrategy{
		"scenario":     NewScenarioGroupingStrategy(),
		"worker":       NewWorkerGroupingStrategy(),
		"status":       NewStatusGroupingStrategy(),
		"request":      NewRequestGroupingStrategy(),
		"status_code":  NewStatusCodeGroupingStrategy(),
		"time_1m":      NewTimeWindowGroupingStrategy("1m"),
		"time_5m":      NewTimeWindowGroupingStrategy("5m"),
		"time_15m":     NewTimeWindowGroupingStrategy("15m"),
		"time_1h":      NewTimeWindowGroupingStrategy("1h"),
	}
}

// CreateGroupingStrategyFromConfig creates a grouping strategy from configuration
func CreateGroupingStrategyFromConfig(strategyType string, config map[string]interface{}) (GroupingStrategy, error) {
	switch strategyType {
	case "scenario":
		return NewScenarioGroupingStrategy(), nil
	case "worker":
		return NewWorkerGroupingStrategy(), nil
	case "status":
		return NewStatusGroupingStrategy(), nil
	case "request":
		return NewRequestGroupingStrategy(), nil
	case "status_code":
		return NewStatusCodeGroupingStrategy(), nil
	case "time_window":
		if windowSize, ok := config["window_size"].(string); ok {
			return NewTimeWindowGroupingStrategy(windowSize), nil
		}
		return nil, fmt.Errorf("time_window strategy requires window_size parameter")
	case "tag":
		if tagKey, ok := config["tag_key"].(string); ok {
			return NewTagGroupingStrategy(tagKey), nil
		}
		return nil, fmt.Errorf("tag strategy requires tag_key parameter")
	case "composite":
		if strategyNames, ok := config["strategies"].([]string); ok {
			var strategies []GroupingStrategy
			for _, name := range strategyNames {
				if strategy, exists := GetDefaultGroupingStrategies()[name]; exists {
					strategies = append(strategies, strategy)
				}
			}
			separator := "|"
			if sep, ok := config["separator"].(string); ok {
				separator = sep
			}
			return NewCompositeGroupingStrategy(strategies, separator), nil
		}
		return nil, fmt.Errorf("composite strategy requires strategies parameter")
	default:
		return nil, fmt.Errorf("unknown grouping strategy type: %s", strategyType)
	}
}
