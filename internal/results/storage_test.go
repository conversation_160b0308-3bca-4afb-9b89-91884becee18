package results

import (
	"os"
	"path/filepath"
	"testing"
	"time"
)

func TestMemoryStorage(t *testing.T) {
	storage := NewMemoryStorage(5)
	
	// Test store and retrieve
	result := createTestAggregatedResult("test_key", "scenario")
	err := storage.Store("test_key", result)
	if err != nil {
		t.<PERSON>("Failed to store result: %v", err)
	}
	
	retrieved, err := storage.Retrieve("test_key")
	if err != nil {
		t.<PERSON>("Failed to retrieve result: %v", err)
	}
	
	if retrieved.GroupKey != result.GroupKey {
		t.Errorf("Expected group key '%s', got '%s'", result.GroupKey, retrieved.GroupKey)
	}
	
	// Test list
	keys, err := storage.List()
	if err != nil {
		t.<PERSON>rrorf("Failed to list keys: %v", err)
	}
	
	if len(keys) != 1 {
		t.<PERSON>rrorf("Expected 1 key, got %d", len(keys))
	}
	
	if keys[0] != "test_key" {
		t.<PERSON><PERSON><PERSON>("Expected key 'test_key', got '%s'", keys[0])
	}
	
	// Test size
	if storage.Size() != 1 {
		t.<PERSON><PERSON><PERSON>("Expected size 1, got %d", storage.Size())
	}
	
	// Test delete
	err = storage.Delete("test_key")
	if err != nil {
		t.Errorf("Failed to delete result: %v", err)
	}
	
	_, err = storage.Retrieve("test_key")
	if err == nil {
		t.Error("Expected error when retrieving deleted result")
	}
	
	// Test retrieve non-existent
	_, err = storage.Retrieve("non_existent")
	if err == nil {
		t.Error("Expected error when retrieving non-existent result")
	}
	
	// Test delete non-existent
	err = storage.Delete("non_existent")
	if err == nil {
		t.Error("Expected error when deleting non-existent result")
	}
}

func TestMemoryStorageMaxSize(t *testing.T) {
	storage := NewMemoryStorage(2)
	
	// Store up to max size
	result1 := createTestAggregatedResult("key1", "scenario")
	result2 := createTestAggregatedResult("key2", "scenario")
	
	err := storage.Store("key1", result1)
	if err != nil {
		t.Errorf("Failed to store result1: %v", err)
	}
	
	err = storage.Store("key2", result2)
	if err != nil {
		t.Errorf("Failed to store result2: %v", err)
	}
	
	// Try to store beyond max size
	result3 := createTestAggregatedResult("key3", "scenario")
	err = storage.Store("key3", result3)
	if err == nil {
		t.Error("Expected error when storing beyond max size")
	}
	
	// Update existing key should work
	updatedResult1 := createTestAggregatedResult("key1", "updated")
	err = storage.Store("key1", updatedResult1)
	if err != nil {
		t.Errorf("Failed to update existing result: %v", err)
	}
}

func TestFileStorage(t *testing.T) {
	// Create temporary directory
	tempDir, err := os.MkdirTemp("", "test_file_storage")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)
	
	storage, err := NewFileStorage(tempDir, 10)
	if err != nil {
		t.Fatalf("Failed to create file storage: %v", err)
	}
	defer storage.Close()
	
	// Test store and retrieve
	result := createTestAggregatedResult("test_key", "scenario")
	err = storage.Store("test_key", result)
	if err != nil {
		t.Errorf("Failed to store result: %v", err)
	}
	
	// Check file exists
	filename := storage.keyToFilename("test_key")
	filePath := filepath.Join(tempDir, filename)
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Error("Expected file to exist after store")
	}
	
	retrieved, err := storage.Retrieve("test_key")
	if err != nil {
		t.Errorf("Failed to retrieve result: %v", err)
	}
	
	if retrieved.GroupKey != result.GroupKey {
		t.Errorf("Expected group key '%s', got '%s'", result.GroupKey, retrieved.GroupKey)
	}
	
	// Test list
	keys, err := storage.List()
	if err != nil {
		t.Errorf("Failed to list keys: %v", err)
	}
	
	if len(keys) != 1 {
		t.Errorf("Expected 1 key, got %d", len(keys))
	}
	
	// Test delete
	err = storage.Delete("test_key")
	if err != nil {
		t.Errorf("Failed to delete result: %v", err)
	}
	
	// Check file is deleted
	if _, err := os.Stat(filePath); !os.IsNotExist(err) {
		t.Error("Expected file to be deleted")
	}
	
	_, err = storage.Retrieve("test_key")
	if err == nil {
		t.Error("Expected error when retrieving deleted result")
	}
}

func TestFileStorageMaxFiles(t *testing.T) {
	// Create temporary directory
	tempDir, err := os.MkdirTemp("", "test_file_storage_max")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)
	
	storage, err := NewFileStorage(tempDir, 2)
	if err != nil {
		t.Fatalf("Failed to create file storage: %v", err)
	}
	defer storage.Close()
	
	// Store up to max files
	result1 := createTestAggregatedResult("key1", "scenario")
	result2 := createTestAggregatedResult("key2", "scenario")
	
	err = storage.Store("key1", result1)
	if err != nil {
		t.Errorf("Failed to store result1: %v", err)
	}
	
	err = storage.Store("key2", result2)
	if err != nil {
		t.Errorf("Failed to store result2: %v", err)
	}
	
	// Try to store beyond max files
	result3 := createTestAggregatedResult("key3", "scenario")
	err = storage.Store("key3", result3)
	if err == nil {
		t.Error("Expected error when storing beyond max files")
	}
	
	// Update existing file should work
	updatedResult1 := createTestAggregatedResult("key1", "updated")
	err = storage.Store("key1", updatedResult1)
	if err != nil {
		t.Errorf("Failed to update existing result: %v", err)
	}
}

func TestCompositeStorage(t *testing.T) {
	// Create primary memory storage
	primary := NewMemoryStorage(10)
	
	// Create secondary memory storage
	secondary := NewMemoryStorage(10)
	
	// Create composite storage
	composite := NewCompositeStorage(primary, secondary)
	defer composite.Close()
	
	// Test store - should store to both
	result := createTestAggregatedResult("test_key", "scenario")
	err := composite.Store("test_key", result)
	if err != nil {
		t.Errorf("Failed to store result: %v", err)
	}
	
	// Check both storages have the result
	_, err = primary.Retrieve("test_key")
	if err != nil {
		t.Errorf("Primary storage should have the result: %v", err)
	}
	
	_, err = secondary.Retrieve("test_key")
	if err != nil {
		t.Errorf("Secondary storage should have the result: %v", err)
	}
	
	// Test retrieve - should retrieve from primary
	retrieved, err := composite.Retrieve("test_key")
	if err != nil {
		t.Errorf("Failed to retrieve result: %v", err)
	}
	
	if retrieved.GroupKey != result.GroupKey {
		t.Errorf("Expected group key '%s', got '%s'", result.GroupKey, retrieved.GroupKey)
	}
	
	// Test list - should list from primary
	keys, err := composite.List()
	if err != nil {
		t.Errorf("Failed to list keys: %v", err)
	}
	
	if len(keys) != 1 {
		t.Errorf("Expected 1 key, got %d", len(keys))
	}
	
	// Test delete - should delete from both
	err = composite.Delete("test_key")
	if err != nil {
		t.Errorf("Failed to delete result: %v", err)
	}
	
	// Check both storages don't have the result
	_, err = primary.Retrieve("test_key")
	if err == nil {
		t.Error("Primary storage should not have the result after delete")
	}
	
	_, err = secondary.Retrieve("test_key")
	if err == nil {
		t.Error("Secondary storage should not have the result after delete")
	}
}

func TestTTLStorage(t *testing.T) {
	// Create base memory storage
	base := NewMemoryStorage(10)
	
	// Create TTL storage with short TTL for testing
	ttl := 100 * time.Millisecond
	storage := NewTTLStorage(base, ttl)
	defer storage.Close()
	
	// Test store and immediate retrieve
	result := createTestAggregatedResult("test_key", "scenario")
	err := storage.Store("test_key", result)
	if err != nil {
		t.Errorf("Failed to store result: %v", err)
	}
	
	retrieved, err := storage.Retrieve("test_key")
	if err != nil {
		t.Errorf("Failed to retrieve result: %v", err)
	}
	
	if retrieved.GroupKey != result.GroupKey {
		t.Errorf("Expected group key '%s', got '%s'", result.GroupKey, retrieved.GroupKey)
	}
	
	// Test list before expiry
	keys, err := storage.List()
	if err != nil {
		t.Errorf("Failed to list keys: %v", err)
	}
	
	if len(keys) != 1 {
		t.Errorf("Expected 1 key, got %d", len(keys))
	}
	
	// Wait for expiry
	time.Sleep(ttl + 50*time.Millisecond)
	
	// Test retrieve after expiry
	_, err = storage.Retrieve("test_key")
	if err == nil {
		t.Error("Expected error when retrieving expired result")
	}
	
	// Test list after expiry
	keys, err = storage.List()
	if err != nil {
		t.Errorf("Failed to list keys after expiry: %v", err)
	}
	
	if len(keys) != 0 {
		t.Errorf("Expected 0 keys after expiry, got %d", len(keys))
	}
}

func TestFileStorageKeyToFilename(t *testing.T) {
	storage := &FileStorage{}
	
	testCases := []struct {
		key      string
		expected string
	}{
		{"simple_key", "simple_key.json"},
		{"key:with:colons", "key_with_colons.json"},
		{"key/with/slashes", "key_with_slashes.json"},
		{"key with spaces", "key_with_spaces.json"},
		{"key-with-dashes", "key-with-dashes.json"},
		{"key_with_underscores", "key_with_underscores.json"},
		{"MixedCaseKey123", "MixedCaseKey123.json"},
	}
	
	for _, tc := range testCases {
		result := storage.keyToFilename(tc.key)
		if result != tc.expected {
			t.Errorf("keyToFilename('%s'): expected '%s', got '%s'", tc.key, tc.expected, result)
		}
	}
}

func TestFileStorageFilenameToKey(t *testing.T) {
	storage := &FileStorage{}
	
	testCases := []struct {
		filename string
		expected string
	}{
		{"simple_key.json", "simple_key"},
		{"key_with_underscores.json", "key_with_underscores"},
		{"MixedCaseKey123.json", "MixedCaseKey123"},
		{"no_extension", "no_extension"},
	}
	
	for _, tc := range testCases {
		result := storage.filenameToKey(tc.filename)
		if result != tc.expected {
			t.Errorf("filenameToKey('%s'): expected '%s', got '%s'", tc.filename, tc.expected, result)
		}
	}
}

// Helper function to create test aggregated results
func createTestAggregatedResult(groupKey, groupType string) *AggregatedResult {
	return &AggregatedResult{
		GroupKey:       groupKey,
		GroupType:      groupType,
		TotalCount:     10,
		SuccessCount:   8,
		FailureCount:   2,
		SuccessRate:    0.8,
		MinDuration:    10 * time.Millisecond,
		MaxDuration:    100 * time.Millisecond,
		AvgDuration:    50 * time.Millisecond,
		MedianDuration: 45 * time.Millisecond,
		P90Duration:    80 * time.Millisecond,
		P95Duration:    90 * time.Millisecond,
		P99Duration:    95 * time.Millisecond,
		TotalBytes:     10240,
		AvgBytes:       1024.0,
		StartTime:      time.Now().Add(-1 * time.Hour),
		EndTime:        time.Now(),
		TimeWindow:     1 * time.Hour,
		LastUpdated:    time.Now(),
	}
}
