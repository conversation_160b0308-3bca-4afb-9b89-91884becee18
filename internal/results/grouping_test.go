package results

import (
	"testing"
	"time"
)

func TestScenarioGroupingStrategy(t *testing.T) {
	strategy := NewScenarioGroupingStrategy()
	
	result := createTestResult("worker1", "test_scenario", true)
	
	groupKey := strategy.GroupKey(result)
	expectedKey := "scenario:test_scenario"
	if groupKey != expectedKey {
		t.<PERSON>("Expected group key '%s', got '%s'", expectedKey, groupKey)
	}
	
	groupType := strategy.GroupType()
	expectedType := "scenario"
	if groupType != expectedType {
		t.<PERSON><PERSON><PERSON>("Expected group type '%s', got '%s'", expectedType, groupType)
	}
}

func TestWorkerGroupingStrategy(t *testing.T) {
	strategy := NewWorkerGroupingStrategy()
	
	result := createTestResult("test_worker", "scenario1", true)
	
	groupKey := strategy.GroupKey(result)
	expectedKey := "worker:test_worker"
	if groupKey != expectedKey {
		t.<PERSON>("Expected group key '%s', got '%s'", expectedKey, groupKey)
	}
	
	groupType := strategy.GroupType()
	expectedType := "worker"
	if groupType != expectedType {
		t.<PERSON><PERSON><PERSON>("Expected group type '%s', got '%s'", expectedType, groupType)
	}
}

func TestStatusGroupingStrategy(t *testing.T) {
	strategy := NewStatusGroupingStrategy()
	
	// Test success result
	successResult := createTestResult("worker1", "scenario1", true)
	groupKey := strategy.GroupKey(successResult)
	expectedKey := "status:success"
	if groupKey != expectedKey {
		t.Errorf("Expected group key '%s', got '%s'", expectedKey, groupKey)
	}
	
	// Test failure result
	failureResult := createTestResult("worker1", "scenario1", false)
	groupKey = strategy.GroupKey(failureResult)
	expectedKey = "status:failure"
	if groupKey != expectedKey {
		t.Errorf("Expected group key '%s', got '%s'", expectedKey, groupKey)
	}
	
	groupType := strategy.GroupType()
	expectedType := "status"
	if groupType != expectedType {
		t.Errorf("Expected group type '%s', got '%s'", expectedType, groupType)
	}
}

func TestRequestGroupingStrategy(t *testing.T) {
	strategy := NewRequestGroupingStrategy()
	
	result := createTestResult("worker1", "scenario1", true)
	result.RequestName = "test_request"
	
	groupKey := strategy.GroupKey(result)
	expectedKey := "request:test_request"
	if groupKey != expectedKey {
		t.Errorf("Expected group key '%s', got '%s'", expectedKey, groupKey)
	}
	
	groupType := strategy.GroupType()
	expectedType := "request"
	if groupType != expectedType {
		t.Errorf("Expected group type '%s', got '%s'", expectedType, groupType)
	}
}

func TestStatusCodeGroupingStrategy(t *testing.T) {
	strategy := NewStatusCodeGroupingStrategy()
	
	result := createTestResult("worker1", "scenario1", true)
	result.StatusCode = 404
	
	groupKey := strategy.GroupKey(result)
	expectedKey := "status_code:404"
	if groupKey != expectedKey {
		t.Errorf("Expected group key '%s', got '%s'", expectedKey, groupKey)
	}
	
	groupType := strategy.GroupType()
	expectedType := "status_code"
	if groupType != expectedType {
		t.Errorf("Expected group type '%s', got '%s'", expectedType, groupType)
	}
}

func TestTimeWindowGroupingStrategy(t *testing.T) {
	// Test 1 minute window
	strategy := NewTimeWindowGroupingStrategy("1m")
	
	// Create result with specific timestamp
	result := createTestResult("worker1", "scenario1", true)
	result.Timestamp = time.Date(2023, 12, 25, 14, 35, 45, 0, time.UTC)
	
	groupKey := strategy.GroupKey(result)
	expectedKey := "time_window:2023-12-25T14:35"
	if groupKey != expectedKey {
		t.Errorf("Expected group key '%s', got '%s'", expectedKey, groupKey)
	}
	
	groupType := strategy.GroupType()
	expectedType := "time_window_1m"
	if groupType != expectedType {
		t.Errorf("Expected group type '%s', got '%s'", expectedType, groupType)
	}
	
	// Test 5 minute window
	strategy5m := NewTimeWindowGroupingStrategy("5m")
	groupKey5m := strategy5m.GroupKey(result)
	expectedKey5m := "time_window:2023-12-25T14:35"
	if groupKey5m != expectedKey5m {
		t.Errorf("Expected 5m group key '%s', got '%s'", expectedKey5m, groupKey5m)
	}
	
	// Test 1 hour window
	strategy1h := NewTimeWindowGroupingStrategy("1h")
	groupKey1h := strategy1h.GroupKey(result)
	expectedKey1h := "time_window:2023-12-25T14"
	if groupKey1h != expectedKey1h {
		t.Errorf("Expected 1h group key '%s', got '%s'", expectedKey1h, groupKey1h)
	}
}

func TestTagGroupingStrategy(t *testing.T) {
	strategy := NewTagGroupingStrategy("environment")
	
	// Test result with tag
	result := createTestResult("worker1", "scenario1", true)
	result.Tags = map[string]string{
		"environment": "production",
		"region":      "us-east-1",
	}
	
	groupKey := strategy.GroupKey(result)
	expectedKey := "tag:environment:production"
	if groupKey != expectedKey {
		t.Errorf("Expected group key '%s', got '%s'", expectedKey, groupKey)
	}
	
	// Test result without tag
	resultNoTag := createTestResult("worker1", "scenario1", true)
	resultNoTag.Tags = map[string]string{
		"region": "us-east-1",
	}
	
	groupKeyNoTag := strategy.GroupKey(resultNoTag)
	expectedKeyNoTag := "tag:environment:untagged"
	if groupKeyNoTag != expectedKeyNoTag {
		t.Errorf("Expected group key '%s', got '%s'", expectedKeyNoTag, groupKeyNoTag)
	}
	
	groupType := strategy.GroupType()
	expectedType := "tag_environment"
	if groupType != expectedType {
		t.Errorf("Expected group type '%s', got '%s'", expectedType, groupType)
	}
}

func TestCompositeGroupingStrategy(t *testing.T) {
	strategies := []GroupingStrategy{
		NewScenarioGroupingStrategy(),
		NewWorkerGroupingStrategy(),
		NewStatusGroupingStrategy(),
	}
	
	strategy := NewCompositeGroupingStrategy(strategies, "|")
	
	result := createTestResult("worker1", "scenario1", true)
	
	groupKey := strategy.GroupKey(result)
	expectedKey := "scenario:scenario1|worker:worker1|status:success"
	if groupKey != expectedKey {
		t.Errorf("Expected group key '%s', got '%s'", expectedKey, groupKey)
	}
	
	groupType := strategy.GroupType()
	expectedType := "scenario_worker_status"
	if groupType != expectedType {
		t.Errorf("Expected group type '%s', got '%s'", expectedType, groupType)
	}
	
	// Test with custom separator
	strategyCustomSep := NewCompositeGroupingStrategy(strategies, ":")
	groupKeyCustom := strategyCustomSep.GroupKey(result)
	expectedKeyCustom := "scenario:scenario1:worker:worker1:status:success"
	if groupKeyCustom != expectedKeyCustom {
		t.Errorf("Expected group key '%s', got '%s'", expectedKeyCustom, groupKeyCustom)
	}
}

func TestCustomGroupingStrategy(t *testing.T) {
	customFunc := func(result TestResult) string {
		return "custom:" + result.ScenarioName + ":" + result.WorkerID
	}
	
	strategy := NewCustomGroupingStrategy(customFunc, "custom_scenario_worker")
	
	result := createTestResult("worker1", "scenario1", true)
	
	groupKey := strategy.GroupKey(result)
	expectedKey := "custom:scenario1:worker1"
	if groupKey != expectedKey {
		t.Errorf("Expected group key '%s', got '%s'", expectedKey, groupKey)
	}
	
	groupType := strategy.GroupType()
	expectedType := "custom_scenario_worker"
	if groupType != expectedType {
		t.Errorf("Expected group type '%s', got '%s'", expectedType, groupType)
	}
	
	// Test with nil function
	strategyNil := NewCustomGroupingStrategy(nil, "")
	groupKeyNil := strategyNil.GroupKey(result)
	expectedKeyNil := "custom:unknown"
	if groupKeyNil != expectedKeyNil {
		t.Errorf("Expected group key '%s', got '%s'", expectedKeyNil, groupKeyNil)
	}
	
	groupTypeNil := strategyNil.GroupType()
	expectedTypeNil := "custom"
	if groupTypeNil != expectedTypeNil {
		t.Errorf("Expected group type '%s', got '%s'", expectedTypeNil, groupTypeNil)
	}
}

func TestGetDefaultGroupingStrategies(t *testing.T) {
	strategies := GetDefaultGroupingStrategies()
	
	expectedStrategies := []string{
		"scenario", "worker", "status", "request", "status_code",
		"time_1m", "time_5m", "time_15m", "time_1h",
	}
	
	for _, expected := range expectedStrategies {
		if _, exists := strategies[expected]; !exists {
			t.Errorf("Expected strategy '%s' not found in default strategies", expected)
		}
	}
	
	if len(strategies) != len(expectedStrategies) {
		t.Errorf("Expected %d default strategies, got %d", len(expectedStrategies), len(strategies))
	}
}

func TestCreateGroupingStrategyFromConfig(t *testing.T) {
	// Test scenario strategy
	strategy, err := CreateGroupingStrategyFromConfig("scenario", nil)
	if err != nil {
		t.Errorf("Failed to create scenario strategy: %v", err)
	}
	if strategy.GroupType() != "scenario" {
		t.Errorf("Expected scenario strategy, got %s", strategy.GroupType())
	}
	
	// Test time_window strategy
	config := map[string]interface{}{
		"window_size": "5m",
	}
	strategy, err = CreateGroupingStrategyFromConfig("time_window", config)
	if err != nil {
		t.Errorf("Failed to create time_window strategy: %v", err)
	}
	if strategy.GroupType() != "time_window_5m" {
		t.Errorf("Expected time_window_5m strategy, got %s", strategy.GroupType())
	}
	
	// Test time_window strategy without config
	_, err = CreateGroupingStrategyFromConfig("time_window", nil)
	if err == nil {
		t.Error("Expected error for time_window strategy without config")
	}
	
	// Test tag strategy
	tagConfig := map[string]interface{}{
		"tag_key": "environment",
	}
	strategy, err = CreateGroupingStrategyFromConfig("tag", tagConfig)
	if err != nil {
		t.Errorf("Failed to create tag strategy: %v", err)
	}
	if strategy.GroupType() != "tag_environment" {
		t.Errorf("Expected tag_environment strategy, got %s", strategy.GroupType())
	}
	
	// Test tag strategy without config
	_, err = CreateGroupingStrategyFromConfig("tag", nil)
	if err == nil {
		t.Error("Expected error for tag strategy without config")
	}
	
	// Test composite strategy
	compositeConfig := map[string]interface{}{
		"strategies": []string{"scenario", "worker"},
		"separator":  ":",
	}
	strategy, err = CreateGroupingStrategyFromConfig("composite", compositeConfig)
	if err != nil {
		t.Errorf("Failed to create composite strategy: %v", err)
	}
	if strategy.GroupType() != "scenario_worker" {
		t.Errorf("Expected scenario_worker strategy, got %s", strategy.GroupType())
	}
	
	// Test composite strategy without config
	_, err = CreateGroupingStrategyFromConfig("composite", nil)
	if err == nil {
		t.Error("Expected error for composite strategy without config")
	}
	
	// Test unknown strategy
	_, err = CreateGroupingStrategyFromConfig("unknown", nil)
	if err == nil {
		t.Error("Expected error for unknown strategy type")
	}
}
