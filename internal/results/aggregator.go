// Package results provides test result aggregation and processing functionality
package results

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"
)

// TestResult represents a single test result from a worker
type TestResult struct {
	ID           string                 `json:"id"`
	WorkerID     string                 `json:"worker_id"`
	ScenarioName string                 `json:"scenario_name"`
	RequestName  string                 `json:"request_name"`
	StartTime    time.Time              `json:"start_time"`
	EndTime      time.Time              `json:"end_time"`
	Duration     time.Duration          `json:"duration"`
	Success      bool                   `json:"success"`
	StatusCode   int                    `json:"status_code"`
	Error        string                 `json:"error,omitempty"`
	ResponseSize int64                  `json:"response_size"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
	Tags         map[string]string      `json:"tags,omitempty"`
	Timestamp    time.Time              `json:"timestamp"`
}

// AggregatedResult represents aggregated statistics for a group of test results
type AggregatedResult struct {
	GroupKey       string                 `json:"group_key"`
	GroupType      string                 `json:"group_type"` // scenario, worker, status, etc.
	TotalCount     int64                  `json:"total_count"`
	SuccessCount   int64                  `json:"success_count"`
	FailureCount   int64                  `json:"failure_count"`
	SuccessRate    float64                `json:"success_rate"`
	MinDuration    time.Duration          `json:"min_duration"`
	MaxDuration    time.Duration          `json:"max_duration"`
	AvgDuration    time.Duration          `json:"avg_duration"`
	MedianDuration time.Duration          `json:"median_duration"`
	P90Duration    time.Duration          `json:"p90_duration"`
	P95Duration    time.Duration          `json:"p95_duration"`
	P99Duration    time.Duration          `json:"p99_duration"`
	TotalBytes     int64                  `json:"total_bytes"`
	AvgBytes       float64                `json:"avg_bytes"`
	StartTime      time.Time              `json:"start_time"`
	EndTime        time.Time              `json:"end_time"`
	TimeWindow     time.Duration          `json:"time_window"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
	LastUpdated    time.Time              `json:"last_updated"`
}

// ResultAggregatorConfig defines configuration for the result aggregator
type ResultAggregatorConfig struct {
	BufferSize          int           `json:"buffer_size"`
	AggregationInterval time.Duration `json:"aggregation_interval"`
	GroupByFields       []string      `json:"group_by_fields"`
	EnableRealTime      bool          `json:"enable_real_time"`
	MaxResults          int           `json:"max_results"`
	RetentionPeriod     time.Duration `json:"retention_period"`
	EnableCompression   bool          `json:"enable_compression"`
	EnablePersistence   bool          `json:"enable_persistence"`
}

// DefaultResultAggregatorConfig returns default configuration
func DefaultResultAggregatorConfig() ResultAggregatorConfig {
	return ResultAggregatorConfig{
		BufferSize:          10000,
		AggregationInterval: 5 * time.Second,
		GroupByFields:       []string{"scenario", "worker", "status"},
		EnableRealTime:      true,
		MaxResults:          100000,
		RetentionPeriod:     24 * time.Hour,
		EnableCompression:   true,
		EnablePersistence:   false,
	}
}

// ResultAggregator handles aggregation of test results from multiple workers
type ResultAggregator struct {
	config            ResultAggregatorConfig
	mu                sync.RWMutex
	results           []TestResult
	aggregatedData    map[string]*AggregatedResult
	groupingStrategy  GroupingStrategy
	storage           ResultStorage
	isRunning         bool
	ctx               context.Context
	cancel            context.CancelFunc
	wg                sync.WaitGroup
	resultChan        chan TestResult
	aggregationTicker *time.Ticker
}

// GroupingStrategy defines how results should be grouped for aggregation
type GroupingStrategy interface {
	GroupKey(result TestResult) string
	GroupType() string
}

// ResultStorage defines interface for storing aggregated results
type ResultStorage interface {
	Store(key string, result *AggregatedResult) error
	Retrieve(key string) (*AggregatedResult, error)
	List() ([]string, error)
	Delete(key string) error
	Close() error
}

// NewResultAggregator creates a new result aggregator
func NewResultAggregator(config ResultAggregatorConfig) *ResultAggregator {
	ctx, cancel := context.WithCancel(context.Background())

	return &ResultAggregator{
		config:         config,
		results:        make([]TestResult, 0, config.BufferSize),
		aggregatedData: make(map[string]*AggregatedResult),
		ctx:            ctx,
		cancel:         cancel,
		resultChan:     make(chan TestResult, config.BufferSize),
	}
}

// SetGroupingStrategy sets the grouping strategy for result aggregation
func (ra *ResultAggregator) SetGroupingStrategy(strategy GroupingStrategy) {
	ra.mu.Lock()
	defer ra.mu.Unlock()
	ra.groupingStrategy = strategy
}

// SetStorage sets the storage backend for aggregated results
func (ra *ResultAggregator) SetStorage(storage ResultStorage) {
	ra.mu.Lock()
	defer ra.mu.Unlock()
	ra.storage = storage
}

// Start begins the result aggregation process
func (ra *ResultAggregator) Start() error {
	ra.mu.Lock()
	defer ra.mu.Unlock()

	if ra.isRunning {
		return fmt.Errorf("result aggregator is already running")
	}

	ra.isRunning = true
	ra.aggregationTicker = time.NewTicker(ra.config.AggregationInterval)

	// Start result processing goroutine
	ra.wg.Add(1)
	go ra.processResults()

	// Start aggregation goroutine
	ra.wg.Add(1)
	go ra.performAggregation()

	return nil
}

// Stop stops the result aggregation process
func (ra *ResultAggregator) Stop() error {
	ra.mu.Lock()
	if !ra.isRunning {
		ra.mu.Unlock()
		return fmt.Errorf("result aggregator is not running")
	}

	ra.isRunning = false
	ra.cancel()

	if ra.aggregationTicker != nil {
		ra.aggregationTicker.Stop()
	}

	close(ra.resultChan)
	ra.mu.Unlock() // Release lock before waiting for goroutines

	// Wait for goroutines to finish (without holding the lock)
	ra.wg.Wait()

	if ra.storage != nil {
		return ra.storage.Close()
	}

	return nil
}

// AddResult adds a test result for aggregation
func (ra *ResultAggregator) AddResult(result TestResult) error {
	if !ra.isRunning {
		return fmt.Errorf("result aggregator is not running")
	}

	select {
	case ra.resultChan <- result:
		return nil
	case <-ra.ctx.Done():
		return fmt.Errorf("result aggregator is shutting down")
	default:
		return fmt.Errorf("result buffer is full")
	}
}

// GetAggregatedResults returns aggregated results for a specific group
func (ra *ResultAggregator) GetAggregatedResults(groupKey string) (*AggregatedResult, error) {
	ra.mu.RLock()
	defer ra.mu.RUnlock()

	if result, exists := ra.aggregatedData[groupKey]; exists {
		// Return a copy to avoid race conditions
		resultCopy := *result
		return &resultCopy, nil
	}

	return nil, fmt.Errorf("no aggregated results found for group: %s", groupKey)
}

// GetAllAggregatedResults returns all aggregated results
func (ra *ResultAggregator) GetAllAggregatedResults() map[string]*AggregatedResult {
	ra.mu.RLock()
	defer ra.mu.RUnlock()

	results := make(map[string]*AggregatedResult)
	for key, result := range ra.aggregatedData {
		resultCopy := *result
		results[key] = &resultCopy
	}

	return results
}

// GetResultCount returns the current number of raw results
func (ra *ResultAggregator) GetResultCount() int {
	ra.mu.RLock()
	defer ra.mu.RUnlock()
	return len(ra.results)
}

// IsRunning returns whether the aggregator is currently running
func (ra *ResultAggregator) IsRunning() bool {
	ra.mu.RLock()
	defer ra.mu.RUnlock()
	return ra.isRunning
}

// processResults processes incoming test results
func (ra *ResultAggregator) processResults() {
	defer ra.wg.Done()

	for {
		select {
		case result, ok := <-ra.resultChan:
			if !ok {
				return // Channel closed
			}
			ra.addResultToBuffer(result)
		case <-ra.ctx.Done():
			return
		}
	}
}

// addResultToBuffer adds a result to the internal buffer
func (ra *ResultAggregator) addResultToBuffer(result TestResult) {
	ra.mu.Lock()
	defer ra.mu.Unlock()

	// Add to buffer
	ra.results = append(ra.results, result)

	// Maintain buffer size limit
	if len(ra.results) > ra.config.BufferSize {
		// Remove oldest results to maintain buffer size
		copy(ra.results, ra.results[1:])
		ra.results = ra.results[:ra.config.BufferSize]
	}
}

// performAggregation performs periodic aggregation of results
func (ra *ResultAggregator) performAggregation() {
	defer ra.wg.Done()

	for {
		select {
		case <-ra.aggregationTicker.C:
			ra.aggregateResults()
		case <-ra.ctx.Done():
			return
		}
	}
}

// aggregateResults performs the actual aggregation logic
func (ra *ResultAggregator) aggregateResults() {
	ra.mu.Lock()
	defer ra.mu.Unlock()

	if ra.groupingStrategy == nil {
		return // No grouping strategy set
	}

	// Group results by the configured strategy
	groups := make(map[string][]TestResult)
	for _, result := range ra.results {
		groupKey := ra.groupingStrategy.GroupKey(result)
		groups[groupKey] = append(groups[groupKey], result)
	}

	// Aggregate each group
	for groupKey, groupResults := range groups {
		aggregated := ra.calculateAggregation(groupKey, groupResults)
		ra.aggregatedData[groupKey] = aggregated

		// Store to persistent storage if enabled
		if ra.storage != nil {
			if err := ra.storage.Store(groupKey, aggregated); err != nil {
				// Log error but continue processing
				fmt.Printf("Failed to store aggregated result for group %s: %v\n", groupKey, err)
			}
		}
	}
}

// calculateAggregation calculates aggregated statistics for a group of results
func (ra *ResultAggregator) calculateAggregation(groupKey string, results []TestResult) *AggregatedResult {
	if len(results) == 0 {
		return &AggregatedResult{
			GroupKey:    groupKey,
			GroupType:   ra.groupingStrategy.GroupType(),
			LastUpdated: time.Now(),
		}
	}

	// Sort results by duration for percentile calculations
	durations := make([]time.Duration, len(results))
	for i, result := range results {
		durations[i] = result.Duration
	}
	sort.Slice(durations, func(i, j int) bool {
		return durations[i] < durations[j]
	})

	// Calculate basic statistics
	var totalDuration time.Duration
	var totalBytes int64
	var successCount, failureCount int64
	var minTime, maxTime time.Time

	for i, result := range results {
		totalDuration += result.Duration
		totalBytes += result.ResponseSize

		if result.Success {
			successCount++
		} else {
			failureCount++
		}

		if i == 0 || result.StartTime.Before(minTime) {
			minTime = result.StartTime
		}
		if i == 0 || result.EndTime.After(maxTime) {
			maxTime = result.EndTime
		}
	}

	totalCount := int64(len(results))
	successRate := float64(successCount) / float64(totalCount)
	avgDuration := totalDuration / time.Duration(totalCount)
	avgBytes := float64(totalBytes) / float64(totalCount)

	// Calculate percentiles
	median := durations[len(durations)/2]
	p90 := durations[int(float64(len(durations))*0.90)]
	p95 := durations[int(float64(len(durations))*0.95)]
	p99 := durations[int(float64(len(durations))*0.99)]

	return &AggregatedResult{
		GroupKey:       groupKey,
		GroupType:      ra.groupingStrategy.GroupType(),
		TotalCount:     totalCount,
		SuccessCount:   successCount,
		FailureCount:   failureCount,
		SuccessRate:    successRate,
		MinDuration:    durations[0],
		MaxDuration:    durations[len(durations)-1],
		AvgDuration:    avgDuration,
		MedianDuration: median,
		P90Duration:    p90,
		P95Duration:    p95,
		P99Duration:    p99,
		TotalBytes:     totalBytes,
		AvgBytes:       avgBytes,
		StartTime:      minTime,
		EndTime:        maxTime,
		TimeWindow:     maxTime.Sub(minTime),
		LastUpdated:    time.Now(),
	}
}
