//go:build windows

package gpu

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"
)

// DirectMLMemoryAwareBatcher handles memory-aware batching for DirectML operations
type DirectMLMemoryAwareBatcher struct {
	mu                  sync.RWMutex
	pendingRequests     []*BatchRequest
	activeBatches       map[string]*MemoryAwareBatch
	memoryTracker       *MemoryTracker
	batchingStrategy    BatchingStrategy
	config              MemoryAwareBatchConfig
	enabled             bool
	running             bool
	stats               BatchingStats
}

// BatchRequest represents a request to be batched
type BatchRequest struct {
	ID              string                 `json:"id"`
	Type            RequestType            `json:"type"`
	Data            interface{}            `json:"data"`
	ModelID         string                 `json:"modelID"`
	Priority        int                    `json:"priority"`
	MemoryRequired  int64                  `json:"memoryRequired"`
	EstimatedTime   time.Duration          `json:"estimatedTime"`
	MaxWaitTime     time.Duration          `json:"maxWaitTime"`
	CreatedAt       time.Time              `json:"createdAt"`
	Metadata        map[string]interface{} `json:"metadata"`
	ResponseChannel chan *BatchResponse    `json:"-"`
}

// RequestType represents different types of requests
type RequestType string

const (
	RequestTypeInference     RequestType = "inference"
	RequestTypeTraining      RequestType = "training"
	RequestTypeValidation    RequestType = "validation"
	RequestTypeOptimization  RequestType = "optimization"
	RequestTypeQuantization  RequestType = "quantization"
)

// MemoryAwareBatch represents a batch optimized for memory usage
type MemoryAwareBatch struct {
	ID                string                 `json:"id"`
	Requests          []*BatchRequest        `json:"requests"`
	TotalMemoryUsage  int64                  `json:"totalMemoryUsage"`
	PeakMemoryUsage   int64                  `json:"peakMemoryUsage"`
	Status            BatchStatus            `json:"status"`
	CreatedAt         time.Time              `json:"createdAt"`
	StartedAt         time.Time              `json:"startedAt"`
	CompletedAt       time.Time              `json:"completedAt"`
	ProcessingTime    time.Duration          `json:"processingTime"`
	MemoryEfficiency  float64                `json:"memoryEfficiency"`
	Results           []*BatchResponse       `json:"results"`
	Metadata          map[string]interface{} `json:"metadata"`
}

// BatchResponse represents the response to a batch request
type BatchResponse struct {
	RequestID    string                 `json:"requestID"`
	Success      bool                   `json:"success"`
	Result       interface{}            `json:"result"`
	Error        string                 `json:"error,omitempty"`
	MemoryUsed   int64                  `json:"memoryUsed"`
	ProcessTime  time.Duration          `json:"processTime"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// MemoryTracker tracks DirectML memory usage
type MemoryTracker struct {
	mu                sync.RWMutex
	totalMemory       int64
	availableMemory   int64
	usedMemory        int64
	reservedMemory    int64
	memoryPools       map[string]*MemoryPool
	allocationHistory []MemoryAllocation
	fragmentationRatio float64
	lastUpdated       time.Time
}

// MemoryPool represents a memory pool for specific operations
type MemoryPool struct {
	ID              string    `json:"id"`
	Type            string    `json:"type"`
	Size            int64     `json:"size"`
	Used            int64     `json:"used"`
	Available       int64     `json:"available"`
	FragmentCount   int       `json:"fragmentCount"`
	LargestFragment int64     `json:"largestFragment"`
	CreatedAt       time.Time `json:"createdAt"`
	LastUsed        time.Time `json:"lastUsed"`
}

// MemoryAllocation represents a memory allocation event
type MemoryAllocation struct {
	ID        string    `json:"id"`
	Size      int64     `json:"size"`
	Type      string    `json:"type"`
	Timestamp time.Time `json:"timestamp"`
	Duration  time.Duration `json:"duration"`
	Success   bool      `json:"success"`
}

// BatchingStrategy represents different batching strategies
type BatchingStrategy string

const (
	StrategyMemoryOptimal     BatchingStrategy = "memory_optimal"
	StrategyTimeOptimal       BatchingStrategy = "time_optimal"
	StrategyBalanced          BatchingStrategy = "balanced"
	StrategyFragmentationAware BatchingStrategy = "fragmentation_aware"
)

// MemoryAwareBatchConfig contains configuration for memory-aware batching
type MemoryAwareBatchConfig struct {
	MaxBatchSize           int               `json:"maxBatchSize"`
	MaxMemoryUsage         int64             `json:"maxMemoryUsage"`
	MemoryUtilizationTarget float64          `json:"memoryUtilizationTarget"`
	BatchingStrategy       BatchingStrategy  `json:"batchingStrategy"`
	MaxWaitTime            time.Duration     `json:"maxWaitTime"`
	MemoryFragmentationThreshold float64     `json:"memoryFragmentationThreshold"`
	EnableMemoryProfiling  bool              `json:"enableMemoryProfiling"`
	EnableDynamicBatching  bool              `json:"enableDynamicBatching"`
	MemoryPressureThreshold float64          `json:"memoryPressureThreshold"`
}

// BatchingStats tracks batching statistics
type BatchingStats struct {
	TotalRequests        int64         `json:"totalRequests"`
	TotalBatches         int64         `json:"totalBatches"`
	AverageBatchSize     float64       `json:"averageBatchSize"`
	AverageMemoryUsage   int64         `json:"averageMemoryUsage"`
	PeakMemoryUsage      int64         `json:"peakMemoryUsage"`
	MemoryEfficiency     float64       `json:"memoryEfficiency"`
	AverageWaitTime      time.Duration `json:"averageWaitTime"`
	AverageProcessTime   time.Duration `json:"averageProcessTime"`
	ThroughputPerSecond  float64       `json:"throughputPerSecond"`
	MemoryFragmentation  float64       `json:"memoryFragmentation"`
	LastBatchedAt        time.Time     `json:"lastBatchedAt"`
}

// NewDirectMLMemoryAwareBatcher creates a new memory-aware batcher
func NewDirectMLMemoryAwareBatcher() *DirectMLMemoryAwareBatcher {
	batcher := &DirectMLMemoryAwareBatcher{
		pendingRequests:  make([]*BatchRequest, 0),
		activeBatches:    make(map[string]*MemoryAwareBatch),
		memoryTracker:    NewMemoryTracker(),
		batchingStrategy: StrategyMemoryOptimal,
		config: MemoryAwareBatchConfig{
			MaxBatchSize:                 32,
			MaxMemoryUsage:               4 * 1024 * 1024 * 1024, // 4GB
			MemoryUtilizationTarget:      0.8,                    // 80%
			BatchingStrategy:             StrategyMemoryOptimal,
			MaxWaitTime:                  100 * time.Millisecond,
			MemoryFragmentationThreshold: 0.3,                    // 30%
			EnableMemoryProfiling:        true,
			EnableDynamicBatching:        true,
			MemoryPressureThreshold:      0.9,                    // 90%
		},
		enabled: true,
		running: false,
		stats:   BatchingStats{},
	}
	
	return batcher
}

// NewMemoryTracker creates a new memory tracker
func NewMemoryTracker() *MemoryTracker {
	return &MemoryTracker{
		totalMemory:       8 * 1024 * 1024 * 1024, // 8GB default
		availableMemory:   8 * 1024 * 1024 * 1024,
		usedMemory:        0,
		reservedMemory:    0,
		memoryPools:       make(map[string]*MemoryPool),
		allocationHistory: make([]MemoryAllocation, 0),
		fragmentationRatio: 0.0,
		lastUpdated:       time.Now(),
	}
}

// SubmitRequest submits a new request for batching
func (dmab *DirectMLMemoryAwareBatcher) SubmitRequest(
	requestType RequestType,
	data interface{},
	modelID string,
	priority int,
	memoryRequired int64,
) (chan *BatchResponse, error) {
	
	if !dmab.enabled {
		return nil, fmt.Errorf("memory-aware batcher is disabled")
	}
	
	// Create request
	request := &BatchRequest{
		ID:              fmt.Sprintf("req_%d", time.Now().UnixNano()),
		Type:            requestType,
		Data:            data,
		ModelID:         modelID,
		Priority:        priority,
		MemoryRequired:  memoryRequired,
		EstimatedTime:   dmab.estimateProcessingTime(requestType, memoryRequired),
		MaxWaitTime:     dmab.config.MaxWaitTime,
		CreatedAt:       time.Now(),
		ResponseChannel: make(chan *BatchResponse, 1),
		Metadata: map[string]interface{}{
			"platform": "windows",
			"api":      "directml",
		},
	}
	
	// Add to pending requests
	dmab.mu.Lock()
	dmab.pendingRequests = append(dmab.pendingRequests, request)
	dmab.stats.TotalRequests++
	dmab.mu.Unlock()
	
	// Start batching if not running
	if !dmab.running {
		go dmab.startBatching()
	}
	
	return request.ResponseChannel, nil
}

// startBatching starts the batching loop
func (dmab *DirectMLMemoryAwareBatcher) startBatching() {
	dmab.mu.Lock()
	if dmab.running {
		dmab.mu.Unlock()
		return
	}
	dmab.running = true
	dmab.mu.Unlock()
	
	defer func() {
		dmab.mu.Lock()
		dmab.running = false
		dmab.mu.Unlock()
	}()
	
	ticker := time.NewTicker(10 * time.Millisecond)
	defer ticker.Stop()
	
	for dmab.enabled {
		select {
		case <-ticker.C:
			dmab.processPendingRequests()
		}
	}
}

// processPendingRequests processes pending requests and creates batches
func (dmab *DirectMLMemoryAwareBatcher) processPendingRequests() {
	dmab.mu.Lock()
	defer dmab.mu.Unlock()
	
	if len(dmab.pendingRequests) == 0 {
		return
	}
	
	// Update memory information
	dmab.memoryTracker.updateMemoryInfo()
	
	// Check for memory pressure
	if dmab.isUnderMemoryPressure() {
		dmab.handleMemoryPressure()
		return
	}
	
	// Create batches based on strategy
	batches := dmab.createOptimalBatches()
	
	// Process each batch
	for _, batch := range batches {
		go dmab.processBatch(batch)
	}
}

// createOptimalBatches creates optimal batches based on the configured strategy
func (dmab *DirectMLMemoryAwareBatcher) createOptimalBatches() []*MemoryAwareBatch {
	var batches []*MemoryAwareBatch
	
	switch dmab.config.BatchingStrategy {
	case StrategyMemoryOptimal:
		batches = dmab.createMemoryOptimalBatches()
	case StrategyTimeOptimal:
		batches = dmab.createTimeOptimalBatches()
	case StrategyBalanced:
		batches = dmab.createBalancedBatches()
	case StrategyFragmentationAware:
		batches = dmab.createFragmentationAwareBatches()
	default:
		batches = dmab.createMemoryOptimalBatches()
	}
	
	return batches
}

// createMemoryOptimalBatches creates batches optimized for memory usage
func (dmab *DirectMLMemoryAwareBatcher) createMemoryOptimalBatches() []*MemoryAwareBatch {
	var batches []*MemoryAwareBatch
	
	// Sort requests by memory requirement (ascending)
	sort.Slice(dmab.pendingRequests, func(i, j int) bool {
		return dmab.pendingRequests[i].MemoryRequired < dmab.pendingRequests[j].MemoryRequired
	})
	
	currentBatch := &MemoryAwareBatch{
		ID:        fmt.Sprintf("batch_%d", time.Now().UnixNano()),
		Requests:  make([]*BatchRequest, 0),
		CreatedAt: time.Now(),
		Metadata: map[string]interface{}{
			"strategy": "memory_optimal",
		},
	}
	
	var processedRequests []*BatchRequest
	
	for _, request := range dmab.pendingRequests {
		// Check if adding this request would exceed memory limits
		if dmab.wouldExceedMemoryLimits(currentBatch, request) {
			// Finalize current batch if it has requests
			if len(currentBatch.Requests) > 0 {
				batches = append(batches, currentBatch)
				
				// Start new batch
				currentBatch = &MemoryAwareBatch{
					ID:        fmt.Sprintf("batch_%d", time.Now().UnixNano()),
					Requests:  make([]*BatchRequest, 0),
					CreatedAt: time.Now(),
					Metadata: map[string]interface{}{
						"strategy": "memory_optimal",
					},
				}
			}
		}
		
		// Check if single request fits in memory
		if request.MemoryRequired <= dmab.memoryTracker.availableMemory {
			currentBatch.Requests = append(currentBatch.Requests, request)
			currentBatch.TotalMemoryUsage += request.MemoryRequired
			processedRequests = append(processedRequests, request)
		}
		
		// Check batch size limit
		if len(currentBatch.Requests) >= dmab.config.MaxBatchSize {
			batches = append(batches, currentBatch)
			currentBatch = &MemoryAwareBatch{
				ID:        fmt.Sprintf("batch_%d", time.Now().UnixNano()),
				Requests:  make([]*BatchRequest, 0),
				CreatedAt: time.Now(),
				Metadata: map[string]interface{}{
					"strategy": "memory_optimal",
				},
			}
		}
	}
	
	// Add final batch if it has requests
	if len(currentBatch.Requests) > 0 {
		batches = append(batches, currentBatch)
	}
	
	// Remove processed requests from pending list
	dmab.removePendingRequests(processedRequests)
	
	return batches
}

// createTimeOptimalBatches creates batches optimized for processing time
func (dmab *DirectMLMemoryAwareBatcher) createTimeOptimalBatches() []*MemoryAwareBatch {
	var batches []*MemoryAwareBatch
	
	// Sort requests by estimated processing time and priority
	sort.Slice(dmab.pendingRequests, func(i, j int) bool {
		if dmab.pendingRequests[i].Priority != dmab.pendingRequests[j].Priority {
			return dmab.pendingRequests[i].Priority > dmab.pendingRequests[j].Priority
		}
		return dmab.pendingRequests[i].EstimatedTime < dmab.pendingRequests[j].EstimatedTime
	})
	
	// Group by similar processing times and memory requirements
	return dmab.createMemoryOptimalBatches() // Simplified implementation
}

// createBalancedBatches creates batches with balanced memory and time optimization
func (dmab *DirectMLMemoryAwareBatcher) createBalancedBatches() []*MemoryAwareBatch {
	// Sort by a combined score of memory and time
	sort.Slice(dmab.pendingRequests, func(i, j int) bool {
		scoreI := dmab.calculateBalancedScore(dmab.pendingRequests[i])
		scoreJ := dmab.calculateBalancedScore(dmab.pendingRequests[j])
		return scoreI > scoreJ
	})
	
	return dmab.createMemoryOptimalBatches() // Simplified implementation
}

// createFragmentationAwareBatches creates batches considering memory fragmentation
func (dmab *DirectMLMemoryAwareBatcher) createFragmentationAwareBatches() []*MemoryAwareBatch {
	// Consider memory fragmentation when creating batches
	dmab.memoryTracker.updateFragmentationInfo()
	
	if dmab.memoryTracker.fragmentationRatio > dmab.config.MemoryFragmentationThreshold {
		// Use smaller batches to reduce fragmentation
		return dmab.createSmallBatches()
	}
	
	return dmab.createMemoryOptimalBatches()
}

// createSmallBatches creates smaller batches to reduce memory fragmentation
func (dmab *DirectMLMemoryAwareBatcher) createSmallBatches() []*MemoryAwareBatch {
	var batches []*MemoryAwareBatch
	
	// Reduce effective batch size
	effectiveBatchSize := dmab.config.MaxBatchSize / 2
	if effectiveBatchSize < 1 {
		effectiveBatchSize = 1
	}
	
	for i := 0; i < len(dmab.pendingRequests); i += effectiveBatchSize {
		end := i + effectiveBatchSize
		if end > len(dmab.pendingRequests) {
			end = len(dmab.pendingRequests)
		}
		
		batch := &MemoryAwareBatch{
			ID:        fmt.Sprintf("batch_%d", time.Now().UnixNano()),
			Requests:  dmab.pendingRequests[i:end],
			CreatedAt: time.Now(),
			Metadata: map[string]interface{}{
				"strategy": "fragmentation_aware",
			},
		}
		
		// Calculate total memory usage
		for _, request := range batch.Requests {
			batch.TotalMemoryUsage += request.MemoryRequired
		}
		
		batches = append(batches, batch)
	}
	
	// Clear pending requests
	dmab.pendingRequests = dmab.pendingRequests[:0]
	
	return batches
}

// processBatch processes a memory-aware batch
func (dmab *DirectMLMemoryAwareBatcher) processBatch(batch *MemoryAwareBatch) {
	// Reserve memory for the batch
	if !dmab.memoryTracker.reserveMemory(batch.TotalMemoryUsage) {
		dmab.handleMemoryReservationFailure(batch)
		return
	}
	
	defer dmab.memoryTracker.releaseMemory(batch.TotalMemoryUsage)
	
	// Update batch status
	batch.Status = BatchStatusProcessing
	batch.StartedAt = time.Now()
	
	// Store active batch
	dmab.mu.Lock()
	dmab.activeBatches[batch.ID] = batch
	dmab.mu.Unlock()
	
	defer func() {
		dmab.mu.Lock()
		delete(dmab.activeBatches, batch.ID)
		dmab.mu.Unlock()
	}()
	
	// Process each request in the batch
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	for _, request := range batch.Requests {
		response := dmab.processRequest(ctx, request)
		batch.Results = append(batch.Results, response)
		
		// Send response back to requester
		select {
		case request.ResponseChannel <- response:
		default:
			// Channel might be closed or full
		}
	}
	
	// Update batch completion
	batch.CompletedAt = time.Now()
	batch.ProcessingTime = batch.CompletedAt.Sub(batch.StartedAt)
	batch.Status = BatchStatusCompleted
	
	// Calculate memory efficiency
	if batch.TotalMemoryUsage > 0 {
		batch.MemoryEfficiency = float64(batch.PeakMemoryUsage) / float64(batch.TotalMemoryUsage)
	}
	
	// Update statistics
	dmab.updateBatchingStats(batch)
}

// processRequest processes a single request
func (dmab *DirectMLMemoryAwareBatcher) processRequest(ctx context.Context, request *BatchRequest) *BatchResponse {
	startTime := time.Now()
	
	// TODO: Implement actual DirectML request processing
	// This would involve:
	// 1. Allocate memory for the request
	// 2. Execute DirectML operation
	// 3. Collect results
	// 4. Track memory usage
	
	// Simulate processing
	time.Sleep(request.EstimatedTime)
	
	response := &BatchResponse{
		RequestID:   request.ID,
		Success:     true,
		Result:      fmt.Sprintf("processed_%s", request.ID),
		MemoryUsed:  request.MemoryRequired,
		ProcessTime: time.Since(startTime),
		Metadata: map[string]interface{}{
			"request_type": string(request.Type),
			"model_id":     request.ModelID,
		},
	}
	
	return response
}

// Helper methods

func (dmab *DirectMLMemoryAwareBatcher) wouldExceedMemoryLimits(batch *MemoryAwareBatch, request *BatchRequest) bool {
	totalMemory := batch.TotalMemoryUsage + request.MemoryRequired
	return totalMemory > dmab.config.MaxMemoryUsage || 
		   totalMemory > dmab.memoryTracker.availableMemory
}

func (dmab *DirectMLMemoryAwareBatcher) isUnderMemoryPressure() bool {
	memoryUtilization := float64(dmab.memoryTracker.usedMemory) / float64(dmab.memoryTracker.totalMemory)
	return memoryUtilization > dmab.config.MemoryPressureThreshold
}

func (dmab *DirectMLMemoryAwareBatcher) handleMemoryPressure() {
	// Delay processing to allow memory to be freed
	time.Sleep(50 * time.Millisecond)
	
	// Consider reducing batch sizes
	if dmab.config.MaxBatchSize > 1 {
		dmab.config.MaxBatchSize = dmab.config.MaxBatchSize / 2
	}
}

func (dmab *DirectMLMemoryAwareBatcher) handleMemoryReservationFailure(batch *MemoryAwareBatch) {
	// Return error responses to all requests in the batch
	for _, request := range batch.Requests {
		response := &BatchResponse{
			RequestID: request.ID,
			Success:   false,
			Error:     "insufficient memory for batch processing",
		}
		
		select {
		case request.ResponseChannel <- response:
		default:
			// Channel might be closed
		}
	}
}

func (dmab *DirectMLMemoryAwareBatcher) estimateProcessingTime(requestType RequestType, memoryRequired int64) time.Duration {
	// Simple estimation based on request type and memory
	baseTime := map[RequestType]time.Duration{
		RequestTypeInference:    10 * time.Millisecond,
		RequestTypeTraining:     100 * time.Millisecond,
		RequestTypeValidation:   20 * time.Millisecond,
		RequestTypeOptimization: 200 * time.Millisecond,
		RequestTypeQuantization: 150 * time.Millisecond,
	}
	
	base := baseTime[requestType]
	if base == 0 {
		base = 50 * time.Millisecond
	}
	
	// Scale by memory requirement
	memoryFactor := float64(memoryRequired) / (1024 * 1024 * 1024) // GB
	return time.Duration(float64(base) * (1 + memoryFactor*0.1))
}

func (dmab *DirectMLMemoryAwareBatcher) calculateBalancedScore(request *BatchRequest) float64 {
	// Combine memory and time factors for balanced scoring
	memoryScore := 1.0 / (1.0 + float64(request.MemoryRequired)/(1024*1024*1024))
	timeScore := 1.0 / (1.0 + float64(request.EstimatedTime.Milliseconds())/1000.0)
	priorityScore := float64(request.Priority) / 10.0
	
	return (memoryScore*0.4 + timeScore*0.4 + priorityScore*0.2)
}

func (dmab *DirectMLMemoryAwareBatcher) removePendingRequests(processed []*BatchRequest) {
	// Create a map for O(1) lookup
	processedMap := make(map[string]bool)
	for _, req := range processed {
		processedMap[req.ID] = true
	}
	
	// Filter out processed requests
	remaining := dmab.pendingRequests[:0]
	for _, req := range dmab.pendingRequests {
		if !processedMap[req.ID] {
			remaining = append(remaining, req)
		}
	}
	dmab.pendingRequests = remaining
}

func (dmab *DirectMLMemoryAwareBatcher) updateBatchingStats(batch *MemoryAwareBatch) {
	dmab.mu.Lock()
	defer dmab.mu.Unlock()
	
	dmab.stats.TotalBatches++
	dmab.stats.LastBatchedAt = time.Now()
	
	// Update average batch size
	batchSize := float64(len(batch.Requests))
	if dmab.stats.AverageBatchSize == 0 {
		dmab.stats.AverageBatchSize = batchSize
	} else {
		dmab.stats.AverageBatchSize = (dmab.stats.AverageBatchSize + batchSize) / 2
	}
	
	// Update memory statistics
	if batch.TotalMemoryUsage > dmab.stats.PeakMemoryUsage {
		dmab.stats.PeakMemoryUsage = batch.TotalMemoryUsage
	}
	
	if dmab.stats.AverageMemoryUsage == 0 {
		dmab.stats.AverageMemoryUsage = batch.TotalMemoryUsage
	} else {
		dmab.stats.AverageMemoryUsage = (dmab.stats.AverageMemoryUsage + batch.TotalMemoryUsage) / 2
	}
	
	// Update timing statistics
	if dmab.stats.AverageProcessTime == 0 {
		dmab.stats.AverageProcessTime = batch.ProcessingTime
	} else {
		dmab.stats.AverageProcessTime = (dmab.stats.AverageProcessTime + batch.ProcessingTime) / 2
	}
	
	// Calculate throughput
	if dmab.stats.TotalBatches > 0 {
		totalTime := time.Since(dmab.stats.LastBatchedAt.Add(-dmab.stats.AverageProcessTime))
		dmab.stats.ThroughputPerSecond = float64(dmab.stats.TotalRequests) / totalTime.Seconds()
	}
}

// MemoryTracker methods

func (mt *MemoryTracker) updateMemoryInfo() {
	mt.mu.Lock()
	defer mt.mu.Unlock()
	
	// TODO: Implement actual DirectML memory querying
	// For now, simulate memory updates
	mt.lastUpdated = time.Now()
	
	// Simulate some memory usage changes
	if mt.usedMemory > 0 {
		mt.availableMemory = mt.totalMemory - mt.usedMemory - mt.reservedMemory
	}
}

func (mt *MemoryTracker) updateFragmentationInfo() {
	mt.mu.Lock()
	defer mt.mu.Unlock()
	
	// TODO: Implement actual fragmentation calculation
	// For now, simulate fragmentation based on allocation history
	if len(mt.allocationHistory) > 10 {
		mt.fragmentationRatio = 0.2 // 20% fragmentation
	} else {
		mt.fragmentationRatio = 0.1 // 10% fragmentation
	}
}

func (mt *MemoryTracker) reserveMemory(size int64) bool {
	mt.mu.Lock()
	defer mt.mu.Unlock()
	
	if mt.availableMemory < size {
		return false
	}
	
	mt.reservedMemory += size
	mt.availableMemory -= size
	
	// Record allocation
	allocation := MemoryAllocation{
		ID:        fmt.Sprintf("alloc_%d", time.Now().UnixNano()),
		Size:      size,
		Type:      "batch_reservation",
		Timestamp: time.Now(),
		Success:   true,
	}
	
	mt.allocationHistory = append(mt.allocationHistory, allocation)
	
	// Limit history size
	if len(mt.allocationHistory) > 1000 {
		mt.allocationHistory = mt.allocationHistory[100:]
	}
	
	return true
}

func (mt *MemoryTracker) releaseMemory(size int64) {
	mt.mu.Lock()
	defer mt.mu.Unlock()
	
	mt.reservedMemory -= size
	mt.availableMemory += size
	
	if mt.reservedMemory < 0 {
		mt.reservedMemory = 0
	}
	if mt.availableMemory > mt.totalMemory {
		mt.availableMemory = mt.totalMemory
	}
}

// Public API methods

// GetBatchingStats returns current batching statistics
func (dmab *DirectMLMemoryAwareBatcher) GetBatchingStats() BatchingStats {
	dmab.mu.RLock()
	defer dmab.mu.RUnlock()
	
	return dmab.stats
}

// GetMemoryInfo returns current memory information
func (dmab *DirectMLMemoryAwareBatcher) GetMemoryInfo() *MemoryTracker {
	return dmab.memoryTracker
}

// GetActiveBatches returns currently active batches
func (dmab *DirectMLMemoryAwareBatcher) GetActiveBatches() map[string]*MemoryAwareBatch {
	dmab.mu.RLock()
	defer dmab.mu.RUnlock()
	
	batches := make(map[string]*MemoryAwareBatch)
	for k, v := range dmab.activeBatches {
		batches[k] = v
	}
	return batches
}

// SetBatchingStrategy updates the batching strategy
func (dmab *DirectMLMemoryAwareBatcher) SetBatchingStrategy(strategy BatchingStrategy) {
	dmab.mu.Lock()
	defer dmab.mu.Unlock()
	
	dmab.config.BatchingStrategy = strategy
} 