//go:build windows

package gpu

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// DirectMLIncrementalCheckpoint handles incremental checkpointing for DirectML operations
type DirectMLIncrementalCheckpoint struct {
	mu                sync.RWMutex
	checkpoints       map[string]*Checkpoint
	checkpointDir     string
	config            CheckpointConfig
	enabled           bool
	currentCheckpoint *Checkpoint
	deltaTracker      *DeltaTracker
	compressionEngine *CheckpointCompression
	stats             CheckpointStats
}

// Checkpoint represents a DirectML checkpoint
type Checkpoint struct {
	ID              string                 `json:"id"`
	Name            string                 `json:"name"`
	Type            CheckpointType         `json:"type"`
	ModelID         string                 `json:"modelID"`
	Version         int                    `json:"version"`
	ParentID        string                 `json:"parentID,omitempty"`
	CreatedAt       time.Time              `json:"createdAt"`
	Size            int64                  `json:"size"`
	CompressedSize  int64                  `json:"compressedSize"`
	FilePath        string                 `json:"filePath"`
	DeltaPath       string                 `json:"deltaPath,omitempty"`
	Metadata        map[string]interface{} `json:"metadata"`
	StateHash       string                 `json:"stateHash"`
	Dependencies    []string               `json:"dependencies"`
	IsIncremental   bool                   `json:"isIncremental"`
	DeltaSize       int64                  `json:"deltaSize"`
	CompressionRatio float64               `json:"compressionRatio"`
	ValidationHash  string                 `json:"validationHash"`
}

// CheckpointType represents different types of checkpoints
type CheckpointType string

const (
	CheckpointTypeFull        CheckpointType = "full"
	CheckpointTypeIncremental CheckpointType = "incremental"
	CheckpointTypeDelta       CheckpointType = "delta"
	CheckpointTypeSnapshot    CheckpointType = "snapshot"
)

// DeltaTracker tracks changes between checkpoints
type DeltaTracker struct {
	mu              sync.RWMutex
	trackedObjects  map[string]*TrackedObject
	changeLog       []ChangeEntry
	lastSnapshot    time.Time
	compressionEnabled bool
}

// TrackedObject represents an object being tracked for changes
type TrackedObject struct {
	ID           string                 `json:"id"`
	Type         string                 `json:"type"`
	CurrentHash  string                 `json:"currentHash"`
	PreviousHash string                 `json:"previousHash"`
	Size         int64                  `json:"size"`
	LastModified time.Time              `json:"lastModified"`
	ChangeCount  int                    `json:"changeCount"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// ChangeEntry represents a change in the system
type ChangeEntry struct {
	ID          string                 `json:"id"`
	ObjectID    string                 `json:"objectID"`
	ChangeType  ChangeType             `json:"changeType"`
	Timestamp   time.Time              `json:"timestamp"`
	OldValue    interface{}            `json:"oldValue,omitempty"`
	NewValue    interface{}            `json:"newValue,omitempty"`
	Delta       []byte                 `json:"delta,omitempty"`
	Size        int64                  `json:"size"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// ChangeType represents different types of changes
type ChangeType string

const (
	ChangeTypeCreate ChangeType = "create"
	ChangeTypeUpdate ChangeType = "update"
	ChangeTypeDelete ChangeType = "delete"
	ChangeTypeMove   ChangeType = "move"
)

// CheckpointConfig contains configuration for checkpointing
type CheckpointConfig struct {
	CheckpointDir          string        `json:"checkpointDir"`
	MaxCheckpoints         int           `json:"maxCheckpoints"`
	CheckpointInterval     time.Duration `json:"checkpointInterval"`
	EnableCompression      bool          `json:"enableCompression"`
	CompressionLevel       int           `json:"compressionLevel"`
	EnableIncrementalMode  bool          `json:"enableIncrementalMode"`
	DeltaThreshold         float64       `json:"deltaThreshold"`
	AutoCleanup            bool          `json:"autoCleanup"`
	RetentionDays          int           `json:"retentionDays"`
	ValidateChecksums      bool          `json:"validateChecksums"`
	EnableEncryption       bool          `json:"enableEncryption"`
	MaxDeltaSize           int64         `json:"maxDeltaSize"`
}

// CheckpointStats tracks checkpointing statistics
type CheckpointStats struct {
	TotalCheckpoints       int64         `json:"totalCheckpoints"`
	IncrementalCheckpoints int64         `json:"incrementalCheckpoints"`
	FullCheckpoints        int64         `json:"fullCheckpoints"`
	TotalSize              int64         `json:"totalSize"`
	CompressedSize         int64         `json:"compressedSize"`
	AverageCompressionRatio float64      `json:"averageCompressionRatio"`
	AverageCheckpointTime  time.Duration `json:"averageCheckpointTime"`
	LastCheckpointTime     time.Time     `json:"lastCheckpointTime"`
	SpaceSaved             int64         `json:"spaceSaved"`
	CheckpointErrors       int64         `json:"checkpointErrors"`
}

// CheckpointCompression handles checkpoint compression
type CheckpointCompression struct {
	mu              sync.RWMutex
	compressionType CompressionType
	compressionLevel int
	enabled         bool
}

// CompressionType represents different compression algorithms
type CompressionType string

const (
	CompressionGzip   CompressionType = "gzip"
	CompressionLZ4    CompressionType = "lz4"
	CompressionZstd   CompressionType = "zstd"
	CompressionBrotli CompressionType = "brotli"
)

// NewDirectMLIncrementalCheckpoint creates a new incremental checkpoint manager
func NewDirectMLIncrementalCheckpoint(checkpointDir string) *DirectMLIncrementalCheckpoint {
	checkpoint := &DirectMLIncrementalCheckpoint{
		checkpoints:   make(map[string]*Checkpoint),
		checkpointDir: checkpointDir,
		config: CheckpointConfig{
			CheckpointDir:          checkpointDir,
			MaxCheckpoints:         50,
			CheckpointInterval:     5 * time.Minute,
			EnableCompression:      true,
			CompressionLevel:       6,
			EnableIncrementalMode:  true,
			DeltaThreshold:         0.1, // 10% change threshold
			AutoCleanup:            true,
			RetentionDays:          30,
			ValidateChecksums:      true,
			EnableEncryption:       false,
			MaxDeltaSize:           100 * 1024 * 1024, // 100MB
		},
		enabled:           true,
		deltaTracker:      NewDeltaTracker(),
		compressionEngine: NewCheckpointCompression(),
		stats:             CheckpointStats{},
	}
	
	// Create checkpoint directory if it doesn't exist
	os.MkdirAll(checkpointDir, 0755)
	
	return checkpoint
}

// NewDeltaTracker creates a new delta tracker
func NewDeltaTracker() *DeltaTracker {
	return &DeltaTracker{
		trackedObjects:     make(map[string]*TrackedObject),
		changeLog:          make([]ChangeEntry, 0),
		lastSnapshot:       time.Now(),
		compressionEnabled: true,
	}
}

// NewCheckpointCompression creates a new checkpoint compression engine
func NewCheckpointCompression() *CheckpointCompression {
	return &CheckpointCompression{
		compressionType:  CompressionGzip,
		compressionLevel: 6,
		enabled:          true,
	}
}

// CreateCheckpoint creates a new checkpoint
func (dmic *DirectMLIncrementalCheckpoint) CreateCheckpoint(
	modelID string,
	name string,
	data interface{},
	forceFullCheckpoint bool,
) (*Checkpoint, error) {
	
	if !dmic.enabled {
		return nil, fmt.Errorf("incremental checkpointing is disabled")
	}
	
	dmic.mu.Lock()
	defer dmic.mu.Unlock()
	
	// Determine checkpoint type
	checkpointType := dmic.determineCheckpointType(modelID, forceFullCheckpoint)
	
	// Create checkpoint
	checkpoint := &Checkpoint{
		ID:        fmt.Sprintf("ckpt_%s_%d", modelID, time.Now().Unix()),
		Name:      name,
		Type:      checkpointType,
		ModelID:   modelID,
		Version:   dmic.getNextVersion(modelID),
		CreatedAt: time.Now(),
		Metadata: map[string]interface{}{
			"platform": "windows",
			"api":      "directml",
		},
		IsIncremental: checkpointType == CheckpointTypeIncremental,
	}
	
	// Set parent for incremental checkpoints
	if checkpointType == CheckpointTypeIncremental && dmic.currentCheckpoint != nil {
		checkpoint.ParentID = dmic.currentCheckpoint.ID
	}
	
	// Process checkpoint data
	if err := dmic.processCheckpointData(checkpoint, data); err != nil {
		return nil, fmt.Errorf("failed to process checkpoint data: %w", err)
	}
	
	// Store checkpoint
	dmic.checkpoints[checkpoint.ID] = checkpoint
	dmic.currentCheckpoint = checkpoint
	
	// Update statistics
	dmic.updateCheckpointStats(checkpoint)
	
	// Cleanup old checkpoints if needed
	if dmic.config.AutoCleanup {
		dmic.cleanupOldCheckpoints()
	}
	
	return checkpoint, nil
}

// determineCheckpointType determines whether to create full or incremental checkpoint
func (dmic *DirectMLIncrementalCheckpoint) determineCheckpointType(modelID string, forceFullCheckpoint bool) CheckpointType {
	if forceFullCheckpoint || !dmic.config.EnableIncrementalMode {
		return CheckpointTypeFull
	}
	
	if dmic.currentCheckpoint == nil {
		return CheckpointTypeFull
	}
	
	// Check if changes exceed delta threshold
	changeRatio := dmic.deltaTracker.calculateChangeRatio()
	if changeRatio > dmic.config.DeltaThreshold {
		return CheckpointTypeFull
	}
	
	return CheckpointTypeIncremental
}

// processCheckpointData processes and saves checkpoint data
func (dmic *DirectMLIncrementalCheckpoint) processCheckpointData(checkpoint *Checkpoint, data interface{}) error {
	// Generate state hash
	stateHash, err := dmic.generateStateHash(data)
	if err != nil {
		return fmt.Errorf("failed to generate state hash: %w", err)
	}
	checkpoint.StateHash = stateHash
	
	// Prepare file paths
	checkpoint.FilePath = filepath.Join(dmic.checkpointDir, fmt.Sprintf("%s.ckpt", checkpoint.ID))
	
	if checkpoint.IsIncremental {
		checkpoint.DeltaPath = filepath.Join(dmic.checkpointDir, fmt.Sprintf("%s.delta", checkpoint.ID))
		
		// Create delta
		if err := dmic.createDelta(checkpoint, data); err != nil {
			return fmt.Errorf("failed to create delta: %w", err)
		}
	} else {
		// Save full checkpoint
		if err := dmic.saveFullCheckpoint(checkpoint, data); err != nil {
			return fmt.Errorf("failed to save full checkpoint: %w", err)
		}
	}
	
	// Compress if enabled
	if dmic.config.EnableCompression {
		if err := dmic.compressCheckpoint(checkpoint); err != nil {
			return fmt.Errorf("failed to compress checkpoint: %w", err)
		}
	}
	
	// Generate validation hash
	validationHash, err := dmic.generateValidationHash(checkpoint)
	if err != nil {
		return fmt.Errorf("failed to generate validation hash: %w", err)
	}
	checkpoint.ValidationHash = validationHash
	
	return nil
}

// createDelta creates a delta between current and previous checkpoint
func (dmic *DirectMLIncrementalCheckpoint) createDelta(checkpoint *Checkpoint, data interface{}) error {
	if dmic.currentCheckpoint == nil {
		return fmt.Errorf("no previous checkpoint for delta creation")
	}
	
	// TODO: Implement actual delta creation
	// This would involve:
	// 1. Compare current data with previous checkpoint
	// 2. Generate binary diff
	// 3. Save delta to file
	
	// Simulate delta creation
	deltaData := []byte(fmt.Sprintf("delta_data_for_%s", checkpoint.ID))
	
	// Write delta to file
	if err := os.WriteFile(checkpoint.DeltaPath, deltaData, 0644); err != nil {
		return fmt.Errorf("failed to write delta file: %w", err)
	}
	
	// Get file info
	info, err := os.Stat(checkpoint.DeltaPath)
	if err != nil {
		return fmt.Errorf("failed to get delta file info: %w", err)
	}
	
	checkpoint.DeltaSize = info.Size()
	checkpoint.Size = checkpoint.DeltaSize
	
	return nil
}

// saveFullCheckpoint saves a full checkpoint
func (dmic *DirectMLIncrementalCheckpoint) saveFullCheckpoint(checkpoint *Checkpoint, data interface{}) error {
	// TODO: Implement actual DirectML checkpoint saving
	// This would involve:
	// 1. Serialize DirectML model state
	// 2. Save weights, optimizer state, etc.
	// 3. Write to checkpoint file
	
	// Simulate checkpoint saving
	checkpointData := []byte(fmt.Sprintf("full_checkpoint_data_for_%s", checkpoint.ID))
	
	// Write checkpoint to file
	if err := os.WriteFile(checkpoint.FilePath, checkpointData, 0644); err != nil {
		return fmt.Errorf("failed to write checkpoint file: %w", err)
	}
	
	// Get file info
	info, err := os.Stat(checkpoint.FilePath)
	if err != nil {
		return fmt.Errorf("failed to get checkpoint file info: %w", err)
	}
	
	checkpoint.Size = info.Size()
	
	return nil
}

// compressCheckpoint compresses checkpoint files
func (dmic *DirectMLIncrementalCheckpoint) compressCheckpoint(checkpoint *Checkpoint) error {
	if !dmic.compressionEngine.enabled {
		return nil
	}
	
	// Compress main checkpoint file
	if checkpoint.FilePath != "" {
		compressedPath := checkpoint.FilePath + ".gz"
		if err := dmic.compressionEngine.compressFile(checkpoint.FilePath, compressedPath); err != nil {
			return fmt.Errorf("failed to compress checkpoint file: %w", err)
		}
		
		// Update file path and size
		originalSize := checkpoint.Size
		compressedInfo, err := os.Stat(compressedPath)
		if err != nil {
			return fmt.Errorf("failed to get compressed file info: %w", err)
		}
		
		checkpoint.CompressedSize = compressedInfo.Size()
		checkpoint.CompressionRatio = float64(originalSize) / float64(checkpoint.CompressedSize)
		checkpoint.FilePath = compressedPath
		
		// Remove original file
		os.Remove(checkpoint.FilePath[:len(checkpoint.FilePath)-3])
	}
	
	// Compress delta file if it exists
	if checkpoint.DeltaPath != "" {
		compressedDeltaPath := checkpoint.DeltaPath + ".gz"
		if err := dmic.compressionEngine.compressFile(checkpoint.DeltaPath, compressedDeltaPath); err != nil {
			return fmt.Errorf("failed to compress delta file: %w", err)
		}
		
		checkpoint.DeltaPath = compressedDeltaPath
		
		// Remove original delta file
		os.Remove(checkpoint.DeltaPath[:len(checkpoint.DeltaPath)-3])
	}
	
	return nil
}

// LoadCheckpoint loads a checkpoint by ID
func (dmic *DirectMLIncrementalCheckpoint) LoadCheckpoint(checkpointID string) (interface{}, error) {
	dmic.mu.RLock()
	checkpoint, exists := dmic.checkpoints[checkpointID]
	dmic.mu.RUnlock()
	
	if !exists {
		return nil, fmt.Errorf("checkpoint %s not found", checkpointID)
	}
	
	// Validate checkpoint integrity
	if dmic.config.ValidateChecksums {
		if err := dmic.validateCheckpoint(checkpoint); err != nil {
			return nil, fmt.Errorf("checkpoint validation failed: %w", err)
		}
	}
	
	// Load checkpoint data
	if checkpoint.IsIncremental {
		return dmic.loadIncrementalCheckpoint(checkpoint)
	} else {
		return dmic.loadFullCheckpoint(checkpoint)
	}
}

// loadFullCheckpoint loads a full checkpoint
func (dmic *DirectMLIncrementalCheckpoint) loadFullCheckpoint(checkpoint *Checkpoint) (interface{}, error) {
	// Decompress if needed
	filePath := checkpoint.FilePath
	if dmic.config.EnableCompression {
		decompressedPath := filePath[:len(filePath)-3] // Remove .gz
		if err := dmic.compressionEngine.decompressFile(filePath, decompressedPath); err != nil {
			return nil, fmt.Errorf("failed to decompress checkpoint: %w", err)
		}
		defer os.Remove(decompressedPath) // Cleanup decompressed file
		filePath = decompressedPath
	}
	
	// Read checkpoint data
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read checkpoint file: %w", err)
	}
	
	// TODO: Implement actual DirectML checkpoint loading
	// This would involve:
	// 1. Deserialize DirectML model state
	// 2. Restore weights, optimizer state, etc.
	// 3. Return loaded model
	
	return string(data), nil
}

// loadIncrementalCheckpoint loads an incremental checkpoint
func (dmic *DirectMLIncrementalCheckpoint) loadIncrementalCheckpoint(checkpoint *Checkpoint) (interface{}, error) {
	// Load parent checkpoint first
	if checkpoint.ParentID == "" {
		return nil, fmt.Errorf("incremental checkpoint has no parent")
	}
	
	parentData, err := dmic.LoadCheckpoint(checkpoint.ParentID)
	if err != nil {
		return nil, fmt.Errorf("failed to load parent checkpoint: %w", err)
	}
	
	// Load delta
	deltaPath := checkpoint.DeltaPath
	if dmic.config.EnableCompression {
		decompressedDeltaPath := deltaPath[:len(deltaPath)-3] // Remove .gz
		if err := dmic.compressionEngine.decompressFile(deltaPath, decompressedDeltaPath); err != nil {
			return nil, fmt.Errorf("failed to decompress delta: %w", err)
		}
		defer os.Remove(decompressedDeltaPath) // Cleanup decompressed file
		deltaPath = decompressedDeltaPath
	}
	
	deltaData, err := os.ReadFile(deltaPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read delta file: %w", err)
	}
	
	// Apply delta to parent data
	result, err := dmic.applyDelta(parentData, deltaData)
	if err != nil {
		return nil, fmt.Errorf("failed to apply delta: %w", err)
	}
	
	return result, nil
}

// applyDelta applies a delta to base data
func (dmic *DirectMLIncrementalCheckpoint) applyDelta(baseData interface{}, deltaData []byte) (interface{}, error) {
	// TODO: Implement actual delta application
	// This would involve:
	// 1. Parse delta format
	// 2. Apply changes to base data
	// 3. Return reconstructed data
	
	// Simulate delta application
	base := baseData.(string)
	delta := string(deltaData)
	
	return base + "_with_" + delta, nil
}

// Helper methods

func (dmic *DirectMLIncrementalCheckpoint) generateStateHash(data interface{}) (string, error) {
	// TODO: Implement proper state hashing for DirectML models
	// For now, create a simple hash
	hasher := sha256.New()
	hasher.Write([]byte(fmt.Sprintf("%v", data)))
	return hex.EncodeToString(hasher.Sum(nil)), nil
}

func (dmic *DirectMLIncrementalCheckpoint) generateValidationHash(checkpoint *Checkpoint) (string, error) {
	// Generate hash of checkpoint metadata for validation
	hasher := sha256.New()
	hasher.Write([]byte(fmt.Sprintf("%s_%s_%d", checkpoint.ID, checkpoint.StateHash, checkpoint.Size)))
	return hex.EncodeToString(hasher.Sum(nil)), nil
}

func (dmic *DirectMLIncrementalCheckpoint) validateCheckpoint(checkpoint *Checkpoint) error {
	// Validate file existence
	if checkpoint.FilePath != "" {
		if _, err := os.Stat(checkpoint.FilePath); os.IsNotExist(err) {
			return fmt.Errorf("checkpoint file does not exist: %s", checkpoint.FilePath)
		}
	}
	
	if checkpoint.DeltaPath != "" {
		if _, err := os.Stat(checkpoint.DeltaPath); os.IsNotExist(err) {
			return fmt.Errorf("delta file does not exist: %s", checkpoint.DeltaPath)
		}
	}
	
	// TODO: Implement checksum validation
	
	return nil
}

func (dmic *DirectMLIncrementalCheckpoint) getNextVersion(modelID string) int {
	maxVersion := 0
	for _, checkpoint := range dmic.checkpoints {
		if checkpoint.ModelID == modelID && checkpoint.Version > maxVersion {
			maxVersion = checkpoint.Version
		}
	}
	return maxVersion + 1
}

func (dmic *DirectMLIncrementalCheckpoint) updateCheckpointStats(checkpoint *Checkpoint) {
	dmic.stats.TotalCheckpoints++
	dmic.stats.LastCheckpointTime = time.Now()
	
	if checkpoint.IsIncremental {
		dmic.stats.IncrementalCheckpoints++
	} else {
		dmic.stats.FullCheckpoints++
	}
	
	dmic.stats.TotalSize += checkpoint.Size
	if checkpoint.CompressedSize > 0 {
		dmic.stats.CompressedSize += checkpoint.CompressedSize
		dmic.stats.SpaceSaved += (checkpoint.Size - checkpoint.CompressedSize)
		
		// Update average compression ratio
		if dmic.stats.AverageCompressionRatio == 0 {
			dmic.stats.AverageCompressionRatio = checkpoint.CompressionRatio
		} else {
			dmic.stats.AverageCompressionRatio = (dmic.stats.AverageCompressionRatio + checkpoint.CompressionRatio) / 2
		}
	}
}

func (dmic *DirectMLIncrementalCheckpoint) cleanupOldCheckpoints() {
	if len(dmic.checkpoints) <= dmic.config.MaxCheckpoints {
		return
	}
	
	// Sort checkpoints by creation time
	var checkpointList []*Checkpoint
	for _, checkpoint := range dmic.checkpoints {
		checkpointList = append(checkpointList, checkpoint)
	}
	
	// Sort by creation time (oldest first)
	for i := 0; i < len(checkpointList)-1; i++ {
		for j := i + 1; j < len(checkpointList); j++ {
			if checkpointList[i].CreatedAt.After(checkpointList[j].CreatedAt) {
				checkpointList[i], checkpointList[j] = checkpointList[j], checkpointList[i]
			}
		}
	}
	
	// Remove oldest checkpoints
	toRemove := len(checkpointList) - dmic.config.MaxCheckpoints
	for i := 0; i < toRemove; i++ {
		checkpoint := checkpointList[i]
		
		// Remove files
		if checkpoint.FilePath != "" {
			os.Remove(checkpoint.FilePath)
		}
		if checkpoint.DeltaPath != "" {
			os.Remove(checkpoint.DeltaPath)
		}
		
		// Remove from map
		delete(dmic.checkpoints, checkpoint.ID)
	}
}

// DeltaTracker methods

func (dt *DeltaTracker) trackObject(objectID string, objectType string, data interface{}) {
	dt.mu.Lock()
	defer dt.mu.Unlock()
	
	// Generate hash for current state
	hasher := sha256.New()
	hasher.Write([]byte(fmt.Sprintf("%v", data)))
	currentHash := hex.EncodeToString(hasher.Sum(nil))
	
	// Get or create tracked object
	trackedObj, exists := dt.trackedObjects[objectID]
	if !exists {
		trackedObj = &TrackedObject{
			ID:           objectID,
			Type:         objectType,
			CurrentHash:  currentHash,
			Size:         int64(len(fmt.Sprintf("%v", data))),
			LastModified: time.Now(),
			ChangeCount:  0,
		}
		dt.trackedObjects[objectID] = trackedObj
		
		// Log creation
		dt.logChange(objectID, ChangeTypeCreate, nil, data)
	} else {
		// Check for changes
		if trackedObj.CurrentHash != currentHash {
			trackedObj.PreviousHash = trackedObj.CurrentHash
			trackedObj.CurrentHash = currentHash
			trackedObj.LastModified = time.Now()
			trackedObj.ChangeCount++
			
			// Log update
			dt.logChange(objectID, ChangeTypeUpdate, trackedObj.PreviousHash, currentHash)
		}
	}
}

func (dt *DeltaTracker) logChange(objectID string, changeType ChangeType, oldValue, newValue interface{}) {
	change := ChangeEntry{
		ID:         fmt.Sprintf("change_%d", time.Now().UnixNano()),
		ObjectID:   objectID,
		ChangeType: changeType,
		Timestamp:  time.Now(),
		OldValue:   oldValue,
		NewValue:   newValue,
		Size:       int64(len(fmt.Sprintf("%v", newValue))),
	}
	
	dt.changeLog = append(dt.changeLog, change)
	
	// Limit change log size
	if len(dt.changeLog) > 10000 {
		dt.changeLog = dt.changeLog[1000:]
	}
}

func (dt *DeltaTracker) calculateChangeRatio() float64 {
	dt.mu.RLock()
	defer dt.mu.RUnlock()
	
	if len(dt.trackedObjects) == 0 {
		return 0.0
	}
	
	changedObjects := 0
	for _, obj := range dt.trackedObjects {
		if obj.ChangeCount > 0 {
			changedObjects++
		}
	}
	
	return float64(changedObjects) / float64(len(dt.trackedObjects))
}

// CheckpointCompression methods

func (cc *CheckpointCompression) compressFile(inputPath, outputPath string) error {
	// TODO: Implement actual compression
	// For now, simulate compression by copying file
	input, err := os.ReadFile(inputPath)
	if err != nil {
		return err
	}
	
	// Simulate compression (just copy for now)
	return os.WriteFile(outputPath, input, 0644)
}

func (cc *CheckpointCompression) decompressFile(inputPath, outputPath string) error {
	// TODO: Implement actual decompression
	// For now, simulate decompression by copying file
	input, err := os.ReadFile(inputPath)
	if err != nil {
		return err
	}
	
	// Simulate decompression (just copy for now)
	return os.WriteFile(outputPath, input, 0644)
}

// Public API methods

// GetCheckpoints returns all checkpoints
func (dmic *DirectMLIncrementalCheckpoint) GetCheckpoints() map[string]*Checkpoint {
	dmic.mu.RLock()
	defer dmic.mu.RUnlock()
	
	checkpoints := make(map[string]*Checkpoint)
	for k, v := range dmic.checkpoints {
		checkpoints[k] = v
	}
	return checkpoints
}

// GetCheckpointStats returns checkpoint statistics
func (dmic *DirectMLIncrementalCheckpoint) GetCheckpointStats() CheckpointStats {
	dmic.mu.RLock()
	defer dmic.mu.RUnlock()
	
	return dmic.stats
}

// DeleteCheckpoint deletes a checkpoint
func (dmic *DirectMLIncrementalCheckpoint) DeleteCheckpoint(checkpointID string) error {
	dmic.mu.Lock()
	defer dmic.mu.Unlock()
	
	checkpoint, exists := dmic.checkpoints[checkpointID]
	if !exists {
		return fmt.Errorf("checkpoint %s not found", checkpointID)
	}
	
	// Remove files
	if checkpoint.FilePath != "" {
		os.Remove(checkpoint.FilePath)
	}
	if checkpoint.DeltaPath != "" {
		os.Remove(checkpoint.DeltaPath)
	}
	
	// Remove from map
	delete(dmic.checkpoints, checkpointID)
	
	return nil
} 