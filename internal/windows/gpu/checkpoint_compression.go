//go:build windows

package gpu

import (
	"bytes"
	"compress/gzip"
	"compress/lzw"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// DirectMLCheckpointCompression handles compression and encryption for DirectML checkpoints
type DirectMLCheckpointCompression struct {
	mu                sync.RWMutex
	compressionAlgos  map[CompressionAlgorithm]*CompressionAlgorithmConfig
	encryptionEnabled bool
	encryptionKey     []byte
	compressionStats  CompressionStats
	config            CompressionConfig
	enabled           bool
}

// CompressionAlgorithm represents different compression algorithms
type CompressionAlgorithm string

const (
	AlgorithmGzip     CompressionAlgorithm = "gzip"
	AlgorithmLZW      CompressionAlgorithm = "lzw"
	AlgorithmDeflate  CompressionAlgorithm = "deflate"
	AlgorithmBrotli   CompressionAlgorithm = "brotli"
	AlgorithmLZ4      CompressionAlgorithm = "lz4"
	AlgorithmZstd     CompressionAlgorithm = "zstd"
	AlgorithmSnappy   CompressionAlgorithm = "snappy"
)

// CompressionAlgorithmConfig contains configuration for a compression algorithm
type CompressionAlgorithmConfig struct {
	Algorithm        CompressionAlgorithm `json:"algorithm"`
	Level            int                  `json:"level"`
	WindowSize       int                  `json:"windowSize"`
	BlockSize        int                  `json:"blockSize"`
	Enabled          bool                 `json:"enabled"`
	Priority         int                  `json:"priority"`
	CompressionRatio float64              `json:"compressionRatio"`
	CompressionSpeed float64              `json:"compressionSpeed"`
	MemoryUsage      int64                `json:"memoryUsage"`
	BestForDataType  []string             `json:"bestForDataType"`
}

// CompressionConfig contains general compression configuration
type CompressionConfig struct {
	DefaultAlgorithm    CompressionAlgorithm `json:"defaultAlgorithm"`
	AdaptiveCompression bool                 `json:"adaptiveCompression"`
	ParallelCompression bool                 `json:"parallelCompression"`
	MaxWorkers          int                  `json:"maxWorkers"`
	ChunkSize           int64                `json:"chunkSize"`
	EnableEncryption    bool                 `json:"enableEncryption"`
	EncryptionAlgorithm string               `json:"encryptionAlgorithm"`
	CompressionLevel    int                  `json:"compressionLevel"`
	ValidateIntegrity   bool                 `json:"validateIntegrity"`
	EnableMetrics       bool                 `json:"enableMetrics"`
}

// CompressionStats tracks compression statistics
type CompressionStats struct {
	TotalFilesCompressed   int64         `json:"totalFilesCompressed"`
	TotalBytesOriginal     int64         `json:"totalBytesOriginal"`
	TotalBytesCompressed   int64         `json:"totalBytesCompressed"`
	AverageCompressionRatio float64      `json:"averageCompressionRatio"`
	BestCompressionRatio   float64       `json:"bestCompressionRatio"`
	WorstCompressionRatio  float64       `json:"worstCompressionRatio"`
	AverageCompressionTime time.Duration `json:"averageCompressionTime"`
	TotalCompressionTime   time.Duration `json:"totalCompressionTime"`
	SpaceSaved             int64         `json:"spaceSaved"`
	CompressionErrors      int64         `json:"compressionErrors"`
	AlgorithmUsage         map[CompressionAlgorithm]int64 `json:"algorithmUsage"`
}

// CompressedFile represents a compressed file
type CompressedFile struct {
	OriginalPath     string               `json:"originalPath"`
	CompressedPath   string               `json:"compressedPath"`
	Algorithm        CompressionAlgorithm `json:"algorithm"`
	OriginalSize     int64                `json:"originalSize"`
	CompressedSize   int64                `json:"compressedSize"`
	CompressionRatio float64              `json:"compressionRatio"`
	CompressionTime  time.Duration        `json:"compressionTime"`
	Checksum         string               `json:"checksum"`
	Encrypted        bool                 `json:"encrypted"`
	CreatedAt        time.Time            `json:"createdAt"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// CompressionJob represents a compression job
type CompressionJob struct {
	ID           string               `json:"id"`
	InputPath    string               `json:"inputPath"`
	OutputPath   string               `json:"outputPath"`
	Algorithm    CompressionAlgorithm `json:"algorithm"`
	Level        int                  `json:"level"`
	Encrypt      bool                 `json:"encrypt"`
	Status       JobStatus            `json:"status"`
	Progress     float64              `json:"progress"`
	StartTime    time.Time            `json:"startTime"`
	EndTime      time.Time            `json:"endTime"`
	Error        string               `json:"error,omitempty"`
	Result       *CompressedFile      `json:"result,omitempty"`
}

// JobStatus represents the status of a compression job
type JobStatus string

const (
	JobStatusPending    JobStatus = "pending"
	JobStatusRunning    JobStatus = "running"
	JobStatusCompleted  JobStatus = "completed"
	JobStatusFailed     JobStatus = "failed"
	JobStatusCancelled  JobStatus = "cancelled"
)

// NewDirectMLCheckpointCompression creates a new checkpoint compression manager
func NewDirectMLCheckpointCompression() *DirectMLCheckpointCompression {
	compression := &DirectMLCheckpointCompression{
		compressionAlgos: make(map[CompressionAlgorithm]*CompressionAlgorithmConfig),
		config: CompressionConfig{
			DefaultAlgorithm:    AlgorithmGzip,
			AdaptiveCompression: true,
			ParallelCompression: true,
			MaxWorkers:          4,
			ChunkSize:           64 * 1024 * 1024, // 64MB
			EnableEncryption:    false,
			EncryptionAlgorithm: "AES-256-GCM",
			CompressionLevel:    6,
			ValidateIntegrity:   true,
			EnableMetrics:       true,
		},
		enabled:           true,
		encryptionEnabled: false,
		compressionStats: CompressionStats{
			AlgorithmUsage: make(map[CompressionAlgorithm]int64),
		},
	}
	
	// Initialize compression algorithms
	compression.initializeCompressionAlgorithms()
	
	return compression
}

// initializeCompressionAlgorithms initializes available compression algorithms
func (dmcc *DirectMLCheckpointCompression) initializeCompressionAlgorithms() {
	// Gzip configuration
	dmcc.compressionAlgos[AlgorithmGzip] = &CompressionAlgorithmConfig{
		Algorithm:        AlgorithmGzip,
		Level:            6,
		WindowSize:       32768,
		BlockSize:        65536,
		Enabled:          true,
		Priority:         1,
		CompressionRatio: 0.7,
		CompressionSpeed: 0.8,
		MemoryUsage:      256 * 1024, // 256KB
		BestForDataType:  []string{"text", "json", "xml"},
	}
	
	// LZW configuration
	dmcc.compressionAlgos[AlgorithmLZW] = &CompressionAlgorithmConfig{
		Algorithm:        AlgorithmLZW,
		Level:            0, // LZW doesn't have levels
		WindowSize:       4096,
		BlockSize:        32768,
		Enabled:          true,
		Priority:         3,
		CompressionRatio: 0.6,
		CompressionSpeed: 0.9,
		MemoryUsage:      128 * 1024, // 128KB
		BestForDataType:  []string{"binary", "images"},
	}
	
	// Deflate configuration
	dmcc.compressionAlgos[AlgorithmDeflate] = &CompressionAlgorithmConfig{
		Algorithm:        AlgorithmDeflate,
		Level:            6,
		WindowSize:       32768,
		BlockSize:        65536,
		Enabled:          true,
		Priority:         2,
		CompressionRatio: 0.65,
		CompressionSpeed: 0.85,
		MemoryUsage:      512 * 1024, // 512KB
		BestForDataType:  []string{"general", "mixed"},
	}
	
	// Add other algorithms (simplified configurations)
	dmcc.compressionAlgos[AlgorithmBrotli] = &CompressionAlgorithmConfig{
		Algorithm:        AlgorithmBrotli,
		Level:            6,
		Enabled:          false, // Disabled by default (would require external library)
		Priority:         4,
		CompressionRatio: 0.8,
		CompressionSpeed: 0.6,
		BestForDataType:  []string{"web", "text"},
	}
	
	dmcc.compressionAlgos[AlgorithmLZ4] = &CompressionAlgorithmConfig{
		Algorithm:        AlgorithmLZ4,
		Level:            1,
		Enabled:          false, // Disabled by default (would require external library)
		Priority:         5,
		CompressionRatio: 0.5,
		CompressionSpeed: 0.95,
		BestForDataType:  []string{"realtime", "streaming"},
	}
}

// CompressFile compresses a single file
func (dmcc *DirectMLCheckpointCompression) CompressFile(
	inputPath string,
	outputPath string,
	algorithm CompressionAlgorithm,
	level int,
	encrypt bool,
) (*CompressedFile, error) {
	
	if !dmcc.enabled {
		return nil, fmt.Errorf("compression is disabled")
	}
	
	// Validate algorithm
	algoConfig, exists := dmcc.compressionAlgos[algorithm]
	if !exists || !algoConfig.Enabled {
		return nil, fmt.Errorf("compression algorithm %s not available", algorithm)
	}
	
	// Get file info
	fileInfo, err := os.Stat(inputPath)
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}
	
	startTime := time.Now()
	
	// Create compressed file record
	compressedFile := &CompressedFile{
		OriginalPath:   inputPath,
		CompressedPath: outputPath,
		Algorithm:      algorithm,
		OriginalSize:   fileInfo.Size(),
		Encrypted:      encrypt,
		CreatedAt:      time.Now(),
		Metadata: map[string]interface{}{
			"platform": "windows",
			"api":      "directml",
		},
	}
	
	// Perform compression
	if err := dmcc.performCompression(inputPath, outputPath, algorithm, level, encrypt); err != nil {
		dmcc.compressionStats.CompressionErrors++
		return nil, fmt.Errorf("compression failed: %w", err)
	}
	
	// Get compressed file info
	compressedInfo, err := os.Stat(outputPath)
	if err != nil {
		return nil, fmt.Errorf("failed to get compressed file info: %w", err)
	}
	
	// Update compression metrics
	compressedFile.CompressedSize = compressedInfo.Size()
	compressedFile.CompressionTime = time.Since(startTime)
	compressedFile.CompressionRatio = float64(compressedFile.OriginalSize) / float64(compressedFile.CompressedSize)
	
	// Generate checksum
	checksum, err := dmcc.generateChecksum(outputPath)
	if err != nil {
		return nil, fmt.Errorf("failed to generate checksum: %w", err)
	}
	compressedFile.Checksum = checksum
	
	// Update statistics
	dmcc.updateCompressionStats(compressedFile)
	
	return compressedFile, nil
}

// performCompression performs the actual compression
func (dmcc *DirectMLCheckpointCompression) performCompression(
	inputPath, outputPath string,
	algorithm CompressionAlgorithm,
	level int,
	encrypt bool,
) error {
	
	// Read input file
	inputData, err := os.ReadFile(inputPath)
	if err != nil {
		return fmt.Errorf("failed to read input file: %w", err)
	}
	
	// Compress data
	var compressedData []byte
	switch algorithm {
	case AlgorithmGzip:
		compressedData, err = dmcc.compressGzip(inputData, level)
	case AlgorithmLZW:
		compressedData, err = dmcc.compressLZW(inputData)
	case AlgorithmDeflate:
		compressedData, err = dmcc.compressDeflate(inputData, level)
	default:
		return fmt.Errorf("unsupported compression algorithm: %s", algorithm)
	}
	
	if err != nil {
		return fmt.Errorf("compression failed: %w", err)
	}
	
	// Encrypt if requested
	if encrypt && dmcc.encryptionEnabled {
		compressedData, err = dmcc.encryptData(compressedData)
		if err != nil {
			return fmt.Errorf("encryption failed: %w", err)
		}
	}
	
	// Write compressed data
	if err := os.WriteFile(outputPath, compressedData, 0644); err != nil {
		return fmt.Errorf("failed to write compressed file: %w", err)
	}
	
	return nil
}

// Compression algorithm implementations

func (dmcc *DirectMLCheckpointCompression) compressGzip(data []byte, level int) ([]byte, error) {
	var buf bytes.Buffer
	
	writer, err := gzip.NewWriterLevel(&buf, level)
	if err != nil {
		return nil, err
	}
	
	if _, err := writer.Write(data); err != nil {
		return nil, err
	}
	
	if err := writer.Close(); err != nil {
		return nil, err
	}
	
	return buf.Bytes(), nil
}

func (dmcc *DirectMLCheckpointCompression) compressLZW(data []byte) ([]byte, error) {
	var buf bytes.Buffer
	
	writer := lzw.NewWriter(&buf, lzw.MSB, 8)
	
	if _, err := writer.Write(data); err != nil {
		return nil, err
	}
	
	if err := writer.Close(); err != nil {
		return nil, err
	}
	
	return buf.Bytes(), nil
}

func (dmcc *DirectMLCheckpointCompression) compressDeflate(data []byte, level int) ([]byte, error) {
	// For simplicity, use gzip without header (which is essentially deflate)
	return dmcc.compressGzip(data, level)
}

// DecompressFile decompresses a file
func (dmcc *DirectMLCheckpointCompression) DecompressFile(
	inputPath string,
	outputPath string,
	algorithm CompressionAlgorithm,
	encrypted bool,
) error {
	
	// Read compressed file
	compressedData, err := os.ReadFile(inputPath)
	if err != nil {
		return fmt.Errorf("failed to read compressed file: %w", err)
	}
	
	// Decrypt if needed
	if encrypted && dmcc.encryptionEnabled {
		compressedData, err = dmcc.decryptData(compressedData)
		if err != nil {
			return fmt.Errorf("decryption failed: %w", err)
		}
	}
	
	// Decompress data
	var decompressedData []byte
	switch algorithm {
	case AlgorithmGzip:
		decompressedData, err = dmcc.decompressGzip(compressedData)
	case AlgorithmLZW:
		decompressedData, err = dmcc.decompressLZW(compressedData)
	case AlgorithmDeflate:
		decompressedData, err = dmcc.decompressDeflate(compressedData)
	default:
		return fmt.Errorf("unsupported compression algorithm: %s", algorithm)
	}
	
	if err != nil {
		return fmt.Errorf("decompression failed: %w", err)
	}
	
	// Write decompressed data
	if err := os.WriteFile(outputPath, decompressedData, 0644); err != nil {
		return fmt.Errorf("failed to write decompressed file: %w", err)
	}
	
	return nil
}

// Decompression algorithm implementations

func (dmcc *DirectMLCheckpointCompression) decompressGzip(data []byte) ([]byte, error) {
	reader, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, err
	}
	defer reader.Close()
	
	return io.ReadAll(reader)
}

func (dmcc *DirectMLCheckpointCompression) decompressLZW(data []byte) ([]byte, error) {
	reader := lzw.NewReader(bytes.NewReader(data), lzw.MSB, 8)
	defer reader.Close()
	
	return io.ReadAll(reader)
}

func (dmcc *DirectMLCheckpointCompression) decompressDeflate(data []byte) ([]byte, error) {
	// For simplicity, use gzip decompression
	return dmcc.decompressGzip(data)
}

// Encryption methods

func (dmcc *DirectMLCheckpointCompression) encryptData(data []byte) ([]byte, error) {
	if dmcc.encryptionKey == nil {
		return nil, fmt.Errorf("encryption key not set")
	}
	
	// Create AES cipher
	block, err := aes.NewCipher(dmcc.encryptionKey)
	if err != nil {
		return nil, err
	}
	
	// Create GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}
	
	// Generate nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}
	
	// Encrypt data
	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	
	return ciphertext, nil
}

func (dmcc *DirectMLCheckpointCompression) decryptData(data []byte) ([]byte, error) {
	if dmcc.encryptionKey == nil {
		return nil, fmt.Errorf("encryption key not set")
	}
	
	// Create AES cipher
	block, err := aes.NewCipher(dmcc.encryptionKey)
	if err != nil {
		return nil, err
	}
	
	// Create GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}
	
	// Extract nonce
	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return nil, fmt.Errorf("ciphertext too short")
	}
	
	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	
	// Decrypt data
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}
	
	return plaintext, nil
}

// CompressDirectory compresses an entire directory
func (dmcc *DirectMLCheckpointCompression) CompressDirectory(
	inputDir string,
	outputPath string,
	algorithm CompressionAlgorithm,
	level int,
) error {
	
	// Create archive of directory
	archiveData, err := dmcc.createDirectoryArchive(inputDir)
	if err != nil {
		return fmt.Errorf("failed to create directory archive: %w", err)
	}
	
	// Compress archive
	var compressedData []byte
	switch algorithm {
	case AlgorithmGzip:
		compressedData, err = dmcc.compressGzip(archiveData, level)
	case AlgorithmLZW:
		compressedData, err = dmcc.compressLZW(archiveData)
	case AlgorithmDeflate:
		compressedData, err = dmcc.compressDeflate(archiveData, level)
	default:
		return fmt.Errorf("unsupported compression algorithm: %s", algorithm)
	}
	
	if err != nil {
		return fmt.Errorf("compression failed: %w", err)
	}
	
	// Write compressed archive
	if err := os.WriteFile(outputPath, compressedData, 0644); err != nil {
		return fmt.Errorf("failed to write compressed archive: %w", err)
	}
	
	return nil
}

// createDirectoryArchive creates a simple archive of directory contents
func (dmcc *DirectMLCheckpointCompression) createDirectoryArchive(dirPath string) ([]byte, error) {
	var archive bytes.Buffer
	
	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		if !info.IsDir() {
			// Read file
			data, err := os.ReadFile(path)
			if err != nil {
				return err
			}
			
			// Add file to archive (simplified format)
			relPath, _ := filepath.Rel(dirPath, path)
			archive.WriteString(fmt.Sprintf("FILE:%s:%d:", relPath, len(data)))
			archive.Write(data)
			archive.WriteString("ENDFILE:")
		}
		
		return nil
	})
	
	return archive.Bytes(), err
}

// SelectOptimalAlgorithm selects the best compression algorithm for given data
func (dmcc *DirectMLCheckpointCompression) SelectOptimalAlgorithm(
	filePath string,
	dataType string,
) (CompressionAlgorithm, error) {
	
	if !dmcc.config.AdaptiveCompression {
		return dmcc.config.DefaultAlgorithm, nil
	}
	
	// Analyze file to determine best algorithm
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return dmcc.config.DefaultAlgorithm, err
	}
	
	// For small files, use fast compression
	if fileInfo.Size() < 1024*1024 { // 1MB
		return AlgorithmLZW, nil
	}
	
	// Select based on data type
	for algorithm, config := range dmcc.compressionAlgos {
		if !config.Enabled {
			continue
		}
		
		for _, bestType := range config.BestForDataType {
			if bestType == dataType {
				return algorithm, nil
			}
		}
	}
	
	return dmcc.config.DefaultAlgorithm, nil
}

// Helper methods

func (dmcc *DirectMLCheckpointCompression) generateChecksum(filePath string) (string, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}
	
	hash := sha256.Sum256(data)
	return fmt.Sprintf("%x", hash), nil
}

func (dmcc *DirectMLCheckpointCompression) updateCompressionStats(compressedFile *CompressedFile) {
	dmcc.mu.Lock()
	defer dmcc.mu.Unlock()
	
	dmcc.compressionStats.TotalFilesCompressed++
	dmcc.compressionStats.TotalBytesOriginal += compressedFile.OriginalSize
	dmcc.compressionStats.TotalBytesCompressed += compressedFile.CompressedSize
	dmcc.compressionStats.TotalCompressionTime += compressedFile.CompressionTime
	dmcc.compressionStats.SpaceSaved += (compressedFile.OriginalSize - compressedFile.CompressedSize)
	
	// Update algorithm usage
	dmcc.compressionStats.AlgorithmUsage[compressedFile.Algorithm]++
	
	// Update average compression ratio
	if dmcc.compressionStats.AverageCompressionRatio == 0 {
		dmcc.compressionStats.AverageCompressionRatio = compressedFile.CompressionRatio
	} else {
		dmcc.compressionStats.AverageCompressionRatio = 
			(dmcc.compressionStats.AverageCompressionRatio + compressedFile.CompressionRatio) / 2
	}
	
	// Update best/worst ratios
	if compressedFile.CompressionRatio > dmcc.compressionStats.BestCompressionRatio {
		dmcc.compressionStats.BestCompressionRatio = compressedFile.CompressionRatio
	}
	
	if dmcc.compressionStats.WorstCompressionRatio == 0 || 
	   compressedFile.CompressionRatio < dmcc.compressionStats.WorstCompressionRatio {
		dmcc.compressionStats.WorstCompressionRatio = compressedFile.CompressionRatio
	}
	
	// Update average compression time
	if dmcc.compressionStats.AverageCompressionTime == 0 {
		dmcc.compressionStats.AverageCompressionTime = compressedFile.CompressionTime
	} else {
		dmcc.compressionStats.AverageCompressionTime = 
			(dmcc.compressionStats.AverageCompressionTime + compressedFile.CompressionTime) / 2
	}
}

// SetEncryptionKey sets the encryption key
func (dmcc *DirectMLCheckpointCompression) SetEncryptionKey(key []byte) error {
	if len(key) != 32 { // AES-256 requires 32-byte key
		return fmt.Errorf("encryption key must be 32 bytes for AES-256")
	}
	
	dmcc.mu.Lock()
	defer dmcc.mu.Unlock()
	
	dmcc.encryptionKey = key
	dmcc.encryptionEnabled = true
	
	return nil
}

// GetCompressionStats returns compression statistics
func (dmcc *DirectMLCheckpointCompression) GetCompressionStats() CompressionStats {
	dmcc.mu.RLock()
	defer dmcc.mu.RUnlock()
	
	return dmcc.compressionStats
}

// GetAvailableAlgorithms returns available compression algorithms
func (dmcc *DirectMLCheckpointCompression) GetAvailableAlgorithms() map[CompressionAlgorithm]*CompressionAlgorithmConfig {
	dmcc.mu.RLock()
	defer dmcc.mu.RUnlock()
	
	algorithms := make(map[CompressionAlgorithm]*CompressionAlgorithmConfig)
	for k, v := range dmcc.compressionAlgos {
		if v.Enabled {
			algorithms[k] = v
		}
	}
	return algorithms
}

// SetCompressionConfig updates compression configuration
func (dmcc *DirectMLCheckpointCompression) SetCompressionConfig(config CompressionConfig) {
	dmcc.mu.Lock()
	defer dmcc.mu.Unlock()
	
	dmcc.config = config
}

// EnableAlgorithm enables a compression algorithm
func (dmcc *DirectMLCheckpointCompression) EnableAlgorithm(algorithm CompressionAlgorithm) error {
	dmcc.mu.Lock()
	defer dmcc.mu.Unlock()
	
	config, exists := dmcc.compressionAlgos[algorithm]
	if !exists {
		return fmt.Errorf("algorithm %s not found", algorithm)
	}
	
	config.Enabled = true
	return nil
}

// DisableAlgorithm disables a compression algorithm
func (dmcc *DirectMLCheckpointCompression) DisableAlgorithm(algorithm CompressionAlgorithm) error {
	dmcc.mu.Lock()
	defer dmcc.mu.Unlock()
	
	config, exists := dmcc.compressionAlgos[algorithm]
	if !exists {
		return fmt.Errorf("algorithm %s not found", algorithm)
	}
	
	config.Enabled = false
	return nil
} 