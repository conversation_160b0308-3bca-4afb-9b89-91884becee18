// Package metrics provides enhanced aggregation functionality
package metrics

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// EnhancedAggregator provides advanced aggregation capabilities with sliding windows and time buckets
type EnhancedAggregator struct {
	config         AggregationConfig
	mu             sync.RWMutex
	slidingWindows map[string]*SlidingWindow      // Per-metric sliding windows
	bucketManager  *TimeBucketManager             // Time-based bucketing system
	statsCalc      map[string]*StatisticalSummary // Per-metric statistical summaries
	isRunning      bool
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
}

// NewEnhancedAggregator creates a new enhanced aggregator with advanced features
func NewEnhancedAggregator(config AggregationConfig) (*EnhancedAggregator, error) {
	ctx, cancel := context.WithCancel(context.Background())

	ea := &EnhancedAggregator{
		config:         config,
		slidingWindows: make(map[string]*SlidingWindow),
		statsCalc:      make(map[string]*StatisticalSummary),
		ctx:            ctx,
		cancel:         cancel,
	}

	// Initialize time bucket manager if enabled
	if config.Type == AggregationTimeBucket || config.EnableAdvancedStats {
		bucketManager, err := NewTimeBucketManager(config.BucketConfig)
		if err != nil {
			cancel()
			return nil, fmt.Errorf("failed to create time bucket manager: %w", err)
		}
		ea.bucketManager = bucketManager
	}

	return ea, nil
}

// Start begins the enhanced aggregation process
func (ea *EnhancedAggregator) Start() error {
	ea.mu.Lock()
	defer ea.mu.Unlock()

	if ea.isRunning {
		return nil // Already running
	}

	ea.isRunning = true

	// Start bucket manager if available
	if ea.bucketManager != nil {
		if err := ea.bucketManager.Start(); err != nil {
			ea.isRunning = false
			return fmt.Errorf("failed to start bucket manager: %w", err)
		}
	}

	return nil
}

// Stop stops the enhanced aggregation process
func (ea *EnhancedAggregator) Stop() error {
	ea.mu.Lock()
	defer ea.mu.Unlock()

	if !ea.isRunning {
		return nil // Already stopped
	}

	ea.isRunning = false
	ea.cancel()

	// Stop bucket manager if available
	if ea.bucketManager != nil {
		if err := ea.bucketManager.Stop(); err != nil {
			return fmt.Errorf("failed to stop bucket manager: %w", err)
		}
	}

	// Wait for all goroutines to finish
	ea.wg.Wait()

	return nil
}

// AddMetricValue adds a metric value to the enhanced aggregation system
func (ea *EnhancedAggregator) AddMetricValue(metricName string, value float64, tags map[string]string) error {
	ea.mu.RLock()
	if !ea.isRunning {
		ea.mu.RUnlock()
		return fmt.Errorf("enhanced aggregator is not running")
	}
	ea.mu.RUnlock()

	timestamp := time.Now()

	// Add to sliding window if configured
	if ea.config.Type == AggregationSlidingWindow || ea.config.EnableAdvancedStats {
		if err := ea.addToSlidingWindow(metricName, value); err != nil {
			return fmt.Errorf("failed to add to sliding window: %w", err)
		}
	}

	// Add to time bucket if configured
	if ea.config.Type == AggregationTimeBucket || ea.config.EnableAdvancedStats {
		if ea.bucketManager != nil {
			entry := TimeBucketEntry{
				Timestamp: timestamp,
				Value:     value,
				Tags:      tags,
				MetricID:  metricName,
				Source:    "enhanced_aggregator",
			}
			if err := ea.bucketManager.AddEntry(entry); err != nil {
				return fmt.Errorf("failed to add to time bucket: %w", err)
			}
		}
	}

	// Update statistical summary if enabled
	if ea.config.EnableAdvancedStats {
		ea.updateStatisticalSummary(metricName, value)
	}

	return nil
}

// addToSlidingWindow adds a value to the appropriate sliding window
func (ea *EnhancedAggregator) addToSlidingWindow(metricName string, value float64) error {
	ea.mu.Lock()
	defer ea.mu.Unlock()

	window, exists := ea.slidingWindows[metricName]
	if !exists {
		// Create new sliding window for this metric
		var err error
		window, err = NewSlidingWindow(ea.config.WindowConfig)
		if err != nil {
			return fmt.Errorf("failed to create sliding window for metric %s: %w", metricName, err)
		}
		ea.slidingWindows[metricName] = window
	}

	return window.Add(value)
}

// updateStatisticalSummary updates the statistical summary for a metric
func (ea *EnhancedAggregator) updateStatisticalSummary(metricName string, value float64) {
	ea.mu.Lock()
	defer ea.mu.Unlock()

	summary, exists := ea.statsCalc[metricName]
	if !exists {
		summary = &StatisticalSummary{}
		ea.statsCalc[metricName] = summary
	}

	// Update the summary with the new value
	// This is a simplified update - in practice, you'd maintain a running calculation
	// For now, we'll just store the latest value and update basic stats
	if summary.Count == 0 {
		summary.Mean = value
		summary.Min = value
		summary.Max = value
		summary.Count = 1
	} else {
		// Update running statistics
		summary.Count++
		oldMean := summary.Mean
		summary.Mean = oldMean + (value-oldMean)/float64(summary.Count)

		if value < summary.Min {
			summary.Min = value
		}
		if value > summary.Max {
			summary.Max = value
		}

		// Update variance (using Welford's online algorithm)
		summary.Variance += (value - oldMean) * (value - summary.Mean)
		if summary.Count > 1 {
			summary.StandardDev = summary.Variance / float64(summary.Count-1)
			// In a full implementation, we would apply sqrt here: math.Sqrt(summary.StandardDev)
			// For now, we store the variance as StandardDev for simplicity
		}
	}
}

// GetSlidingWindowStats returns statistics from the sliding window for a metric
func (ea *EnhancedAggregator) GetSlidingWindowStats(metricName string) (*SlidingWindowStatistics, error) {
	ea.mu.RLock()
	defer ea.mu.RUnlock()

	window, exists := ea.slidingWindows[metricName]
	if !exists {
		return nil, fmt.Errorf("no sliding window found for metric: %s", metricName)
	}

	stats := window.GetStatistics()
	return &stats, nil
}

// GetTimeBucketStats returns statistics from time buckets for a time range
func (ea *EnhancedAggregator) GetTimeBucketStats(startTime, endTime time.Time) (*TimeBucketStats, error) {
	if ea.bucketManager == nil {
		return nil, fmt.Errorf("time bucket manager not initialized")
	}

	stats := ea.bucketManager.GetStats()
	return &stats, nil
}

// GetStatisticalSummary returns the statistical summary for a metric
func (ea *EnhancedAggregator) GetStatisticalSummary(metricName string) (*StatisticalSummary, error) {
	ea.mu.RLock()
	defer ea.mu.RUnlock()

	summary, exists := ea.statsCalc[metricName]
	if !exists {
		return nil, fmt.Errorf("no statistical summary found for metric: %s", metricName)
	}

	// Return a copy to avoid race conditions
	summaryCopy := *summary
	return &summaryCopy, nil
}

// GetAllMetrics returns all metrics being tracked by the enhanced aggregator
func (ea *EnhancedAggregator) GetAllMetrics() []string {
	ea.mu.RLock()
	defer ea.mu.RUnlock()

	metrics := make(map[string]bool)

	// Collect from sliding windows
	for name := range ea.slidingWindows {
		metrics[name] = true
	}

	// Collect from statistical summaries
	for name := range ea.statsCalc {
		metrics[name] = true
	}

	// Convert to slice
	result := make([]string, 0, len(metrics))
	for name := range metrics {
		result = append(result, name)
	}

	return result
}

// IsRunning returns whether the enhanced aggregator is currently running
func (ea *EnhancedAggregator) IsRunning() bool {
	ea.mu.RLock()
	defer ea.mu.RUnlock()
	return ea.isRunning
}
