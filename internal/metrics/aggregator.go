// Package metrics provides metrics collection and reporting functionality
package metrics

import (
	"context"
	"sync"
	"sync/atomic"
	"time"
)

// AggregationType defines the type of aggregation to perform
type AggregationType int

const (
	AggregationSum AggregationType = iota
	AggregationAverage
	AggregationMin
	AggregationMax
	AggregationCount
	AggregationPercentile
	AggregationSlidingWindow
	AggregationTimeBucket
)

// AggregationConfig defines configuration for automatic aggregation
type AggregationConfig struct {
	Interval    time.Duration   // How often to aggregate
	Type        AggregationType // Type of aggregation
	Percentile  float64         // For percentile aggregation (0.0-1.0)
	BufferSize  int             // Size of the aggregation buffer
	EnableAsync bool            // Whether to run aggregation asynchronously

	// Sliding window configuration
	WindowConfig SlidingWindowConfig

	// Time bucket configuration
	BucketConfig TimeBucketConfig

	// Enable advanced statistical aggregation
	EnableAdvancedStats bool
}

// DefaultAggregationConfig provides sensible defaults for load testing scenarios
var DefaultAggregationConfig = AggregationConfig{
	Interval:    time.Second * 10, // Aggregate every 10 seconds
	Type:        AggregationAverage,
	Percentile:  0.95, // 95th percentile for percentile aggregation
	BufferSize:  1000,
	EnableAsync: true,
	WindowConfig: SlidingWindowConfig{
		Type:        WindowTypeTime,
		Duration:    5 * time.Minute,
		Capacity:    1000,
		EnableStats: true,
		EnableAsync: true,
	},
	BucketConfig: TimeBucketConfig{
		Granularity:        GranularityMinute,
		RetentionBuckets:   1440, // 24 hours of minute buckets
		MaxClockSkew:       30 * time.Second,
		EnableDownsampling: false,
		EnableUpsampling:   false,
		CleanupInterval:    10 * time.Minute,
		EnableAsync:        true,
		EnableCompression:  false,
		MaxMemoryUsage:     100 * 1024 * 1024, // 100MB
		FlushOnShutdown:    true,
	},
	EnableAdvancedStats: true,
}

// AggregatedValue represents a single aggregated metric value
type AggregatedValue struct {
	Timestamp  time.Time         `json:"timestamp"`
	Value      float64           `json:"value"`
	Type       AggregationType   `json:"type"`
	Count      uint64            `json:"count"`      // Number of samples aggregated
	Min        float64           `json:"min"`        // Minimum value in aggregation window
	Max        float64           `json:"max"`        // Maximum value in aggregation window
	Percentile float64           `json:"percentile"` // Percentile value (if applicable)
	Tags       map[string]string `json:"tags"`       // Associated tags

	// Advanced statistical data (when EnableAdvancedStats is true)
	StatsSummary *StatisticalSummary `json:"stats_summary,omitempty"`
}

// MetricAggregator handles automatic aggregation of metrics
type MetricAggregator struct {
	config     AggregationConfig
	mu         sync.RWMutex
	counters   map[string]*Counter
	gauges     map[string]*Gauge
	histograms map[string]*Histogram
	aggregated map[string][]AggregatedValue // Stores aggregated values by metric name
	isRunning  int32                        // Atomic flag for running state
	stopCh     chan struct{}
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup

	// Advanced aggregation components
	slidingWindows map[string]*SlidingWindow      // Per-metric sliding windows
	bucketManager  *TimeBucketManager             // Time-based bucketing system
	statsCalc      map[string]*StatisticalSummary // Per-metric statistical summaries
}

// NewMetricAggregator creates a new metric aggregator with default configuration
func NewMetricAggregator() *MetricAggregator {
	return NewMetricAggregatorWithConfig(DefaultAggregationConfig)
}

// NewMetricAggregatorWithConfig creates a new metric aggregator with custom configuration
func NewMetricAggregatorWithConfig(config AggregationConfig) *MetricAggregator {
	ctx, cancel := context.WithCancel(context.Background())

	ma := &MetricAggregator{
		config:         config,
		counters:       make(map[string]*Counter),
		gauges:         make(map[string]*Gauge),
		histograms:     make(map[string]*Histogram),
		aggregated:     make(map[string][]AggregatedValue),
		stopCh:         make(chan struct{}),
		ctx:            ctx,
		cancel:         cancel,
		slidingWindows: make(map[string]*SlidingWindow),
		statsCalc:      make(map[string]*StatisticalSummary),
	}

	// Initialize time bucket manager if time bucketing is enabled
	if config.Type == AggregationTimeBucket {
		bucketManager, err := NewTimeBucketManager(config.BucketConfig)
		if err == nil {
			ma.bucketManager = bucketManager
		}
	}

	return ma
}

// RegisterCounter registers a counter for automatic aggregation
func (ma *MetricAggregator) RegisterCounter(name string, counter *Counter) {
	ma.mu.Lock()
	defer ma.mu.Unlock()
	ma.counters[name] = counter
}

// RegisterGauge registers a gauge for automatic aggregation
func (ma *MetricAggregator) RegisterGauge(name string, gauge *Gauge) {
	ma.mu.Lock()
	defer ma.mu.Unlock()
	ma.gauges[name] = gauge
}

// RegisterHistogram registers a histogram for automatic aggregation
func (ma *MetricAggregator) RegisterHistogram(name string, histogram *Histogram) {
	ma.mu.Lock()
	defer ma.mu.Unlock()
	ma.histograms[name] = histogram
}

// Start begins the automatic aggregation process
func (ma *MetricAggregator) Start() error {
	if !atomic.CompareAndSwapInt32(&ma.isRunning, 0, 1) {
		return nil // Already running
	}

	if ma.config.EnableAsync {
		ma.wg.Add(1)
		go ma.aggregationLoop()
	}

	return nil
}

// Stop stops the automatic aggregation process
func (ma *MetricAggregator) Stop() error {
	if !atomic.CompareAndSwapInt32(&ma.isRunning, 1, 0) {
		return nil // Already stopped
	}

	ma.cancel()
	close(ma.stopCh)

	if ma.config.EnableAsync {
		ma.wg.Wait()
	}

	return nil
}

// IsRunning returns whether the aggregator is currently running
func (ma *MetricAggregator) IsRunning() bool {
	return atomic.LoadInt32(&ma.isRunning) == 1
}

// aggregationLoop runs the main aggregation loop in a separate goroutine
func (ma *MetricAggregator) aggregationLoop() {
	defer ma.wg.Done()

	ticker := time.NewTicker(ma.config.Interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ma.PerformAggregation()
		case <-ma.ctx.Done():
			return
		case <-ma.stopCh:
			return
		}
	}
}

// PerformAggregation performs a single aggregation cycle
func (ma *MetricAggregator) PerformAggregation() {
	timestamp := time.Now()

	ma.mu.Lock()
	defer ma.mu.Unlock()

	// Aggregate counters
	for name, counter := range ma.counters {
		value := float64(counter.Value())
		aggregatedValue := AggregatedValue{
			Timestamp: timestamp,
			Value:     value,
			Type:      ma.config.Type,
			Count:     1,
			Min:       value,
			Max:       value,
			Tags:      make(map[string]string),
		}

		ma.appendAggregatedValue(name, aggregatedValue)
	}

	// Aggregate gauges
	for name, gauge := range ma.gauges {
		value := gauge.Value()
		aggregatedValue := AggregatedValue{
			Timestamp: timestamp,
			Value:     value,
			Type:      ma.config.Type,
			Count:     1,
			Min:       value,
			Max:       value,
			Tags:      make(map[string]string),
		}

		ma.appendAggregatedValue(name, aggregatedValue)
	}

	// Aggregate histograms
	for name, histogram := range ma.histograms {
		count := histogram.Count()
		sum := histogram.Sum()

		var value float64
		switch ma.config.Type {
		case AggregationSum:
			value = sum
		case AggregationAverage:
			if count > 0 {
				value = sum / float64(count)
			}
		case AggregationCount:
			value = float64(count)
		case AggregationPercentile:
			value = histogram.Quantile(ma.config.Percentile)
		default:
			value = sum
		}

		aggregatedValue := AggregatedValue{
			Timestamp:  timestamp,
			Value:      value,
			Type:       ma.config.Type,
			Count:      count,
			Min:        histogram.Quantile(0.0),
			Max:        histogram.Quantile(1.0),
			Percentile: histogram.Quantile(ma.config.Percentile),
			Tags:       make(map[string]string),
		}

		ma.appendAggregatedValue(name, aggregatedValue)
	}
}

// appendAggregatedValue appends an aggregated value to the buffer, maintaining size limits
func (ma *MetricAggregator) appendAggregatedValue(name string, value AggregatedValue) {
	if ma.aggregated[name] == nil {
		ma.aggregated[name] = make([]AggregatedValue, 0, ma.config.BufferSize)
	}

	// Add new value
	ma.aggregated[name] = append(ma.aggregated[name], value)

	// Maintain buffer size limit
	if len(ma.aggregated[name]) > ma.config.BufferSize {
		// Remove oldest values to maintain buffer size
		copy(ma.aggregated[name], ma.aggregated[name][1:])
		ma.aggregated[name] = ma.aggregated[name][:ma.config.BufferSize]
	}
}

// GetAggregatedValues returns aggregated values for a specific metric
func (ma *MetricAggregator) GetAggregatedValues(name string) []AggregatedValue {
	ma.mu.RLock()
	defer ma.mu.RUnlock()

	values, exists := ma.aggregated[name]
	if !exists {
		return nil
	}

	// Return a copy to avoid race conditions
	result := make([]AggregatedValue, len(values))
	copy(result, values)
	return result
}

// GetAllAggregatedValues returns all aggregated values
func (ma *MetricAggregator) GetAllAggregatedValues() map[string][]AggregatedValue {
	ma.mu.RLock()
	defer ma.mu.RUnlock()

	result := make(map[string][]AggregatedValue)
	for name, values := range ma.aggregated {
		result[name] = make([]AggregatedValue, len(values))
		copy(result[name], values)
	}
	return result
}

// GetLatestAggregatedValue returns the most recent aggregated value for a metric
func (ma *MetricAggregator) GetLatestAggregatedValue(name string) *AggregatedValue {
	ma.mu.RLock()
	defer ma.mu.RUnlock()

	values, exists := ma.aggregated[name]
	if !exists || len(values) == 0 {
		return nil
	}

	// Return a copy of the latest value
	latest := values[len(values)-1]
	return &latest
}

// ClearAggregatedValues clears aggregated values for a specific metric
func (ma *MetricAggregator) ClearAggregatedValues(name string) {
	ma.mu.Lock()
	defer ma.mu.Unlock()
	delete(ma.aggregated, name)
}

// ClearAllAggregatedValues clears all aggregated values
func (ma *MetricAggregator) ClearAllAggregatedValues() {
	ma.mu.Lock()
	defer ma.mu.Unlock()
	ma.aggregated = make(map[string][]AggregatedValue)
}

// GetRegisteredMetrics returns the names of all registered metrics
func (ma *MetricAggregator) GetRegisteredMetrics() []string {
	ma.mu.RLock()
	defer ma.mu.RUnlock()

	var names []string
	for name := range ma.counters {
		names = append(names, name)
	}
	for name := range ma.gauges {
		names = append(names, name)
	}
	for name := range ma.histograms {
		names = append(names, name)
	}

	return names
}

// SetAggregationConfig updates the aggregation configuration
// Note: This will take effect on the next aggregation cycle
func (ma *MetricAggregator) SetAggregationConfig(config AggregationConfig) {
	ma.mu.Lock()
	defer ma.mu.Unlock()
	ma.config = config
}

// GetAggregationConfig returns the current aggregation configuration
func (ma *MetricAggregator) GetAggregationConfig() AggregationConfig {
	ma.mu.RLock()
	defer ma.mu.RUnlock()
	return ma.config
}
