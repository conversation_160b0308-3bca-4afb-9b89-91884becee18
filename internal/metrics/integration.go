// Package metrics provides integration interfaces for the metrics collection system
package metrics

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// MetricsIntegrator provides a unified interface for all metrics collection and aggregation
type MetricsIntegrator struct {
	collectionManager  *CollectionManager
	basicAggregator    *MetricAggregator
	enhancedAggregator *EnhancedAggregator
	mu                 sync.RWMutex
	isRunning          bool
	ctx                context.Context
	cancel             context.CancelFunc
}

// IntegratorConfig defines configuration for the metrics integrator
type IntegratorConfig struct {
	CollectionConfig  CollectionConfig
	AggregationConfig AggregationConfig
	EnableEnhanced    bool
	EnableCollection  bool
	EnableBasicAgg    bool
}

// DefaultIntegratorConfig provides sensible defaults for load testing scenarios
var DefaultIntegratorConfig = IntegratorConfig{
	CollectionConfig:  DefaultCollectionConfig,
	AggregationConfig: DefaultAggregationConfig,
	EnableEnhanced:    true,
	EnableCollection:  true,
	EnableBasicAgg:    true,
}

// NewMetricsIntegrator creates a new metrics integrator with all components
func NewMetricsIntegrator(config IntegratorConfig) (*MetricsIntegrator, error) {
	ctx, cancel := context.WithCancel(context.Background())

	integrator := &MetricsIntegrator{
		ctx:    ctx,
		cancel: cancel,
	}

	// Initialize collection manager if enabled
	if config.EnableCollection {
		collectionManager := NewCollectionManagerWithConfig(config.CollectionConfig)
		integrator.collectionManager = collectionManager
	}

	// Initialize basic aggregator if enabled
	if config.EnableBasicAgg {
		basicAggregator := NewMetricAggregatorWithConfig(config.AggregationConfig)
		integrator.basicAggregator = basicAggregator

		// Connect basic aggregator to collection manager
		if integrator.collectionManager != nil {
			integrator.collectionManager.SetAggregator(basicAggregator)
		}
	}

	// Initialize enhanced aggregator if enabled
	if config.EnableEnhanced {
		enhancedAggregator, err := NewEnhancedAggregator(config.AggregationConfig)
		if err != nil {
			cancel()
			return nil, fmt.Errorf("failed to create enhanced aggregator: %w", err)
		}
		integrator.enhancedAggregator = enhancedAggregator

		// Connect enhanced aggregator to collection manager
		if integrator.collectionManager != nil {
			integrator.collectionManager.SetEnhancedAggregator(enhancedAggregator)
		}
	}

	return integrator, nil
}

// Start starts all components of the metrics integrator
func (mi *MetricsIntegrator) Start() error {
	mi.mu.Lock()
	defer mi.mu.Unlock()

	if mi.isRunning {
		return nil // Already running
	}

	// Start collection manager
	if mi.collectionManager != nil {
		if err := mi.collectionManager.Start(); err != nil {
			return fmt.Errorf("failed to start collection manager: %w", err)
		}
	}

	// Start basic aggregator
	if mi.basicAggregator != nil {
		if err := mi.basicAggregator.Start(); err != nil {
			return fmt.Errorf("failed to start basic aggregator: %w", err)
		}
	}

	// Start enhanced aggregator
	if mi.enhancedAggregator != nil {
		if err := mi.enhancedAggregator.Start(); err != nil {
			return fmt.Errorf("failed to start enhanced aggregator: %w", err)
		}
	}

	mi.isRunning = true
	return nil
}

// Stop stops all components of the metrics integrator
func (mi *MetricsIntegrator) Stop() error {
	mi.mu.Lock()
	defer mi.mu.Unlock()

	if !mi.isRunning {
		return nil // Already stopped
	}

	var errors []error

	// Stop enhanced aggregator
	if mi.enhancedAggregator != nil {
		if err := mi.enhancedAggregator.Stop(); err != nil {
			errors = append(errors, fmt.Errorf("failed to stop enhanced aggregator: %w", err))
		}
	}

	// Stop basic aggregator
	if mi.basicAggregator != nil {
		if err := mi.basicAggregator.Stop(); err != nil {
			errors = append(errors, fmt.Errorf("failed to stop basic aggregator: %w", err))
		}
	}

	// Stop collection manager
	if mi.collectionManager != nil {
		if err := mi.collectionManager.Stop(); err != nil {
			errors = append(errors, fmt.Errorf("failed to stop collection manager: %w", err))
		}
	}

	mi.cancel()
	mi.isRunning = false

	if len(errors) > 0 {
		return fmt.Errorf("errors during shutdown: %v", errors)
	}

	return nil
}

// RegisterCounter registers a counter for collection and aggregation
func (mi *MetricsIntegrator) RegisterCounter(name string, counter *Counter, tags map[string]string) {
	if mi.collectionManager != nil {
		mi.collectionManager.RegisterCounter(name, counter, tags)
	}
	if mi.basicAggregator != nil {
		mi.basicAggregator.RegisterCounter(name, counter)
	}
}

// RegisterGauge registers a gauge for collection and aggregation
func (mi *MetricsIntegrator) RegisterGauge(name string, gauge *Gauge, tags map[string]string) {
	if mi.collectionManager != nil {
		mi.collectionManager.RegisterGauge(name, gauge, tags)
	}
	if mi.basicAggregator != nil {
		mi.basicAggregator.RegisterGauge(name, gauge)
	}
}

// RegisterHistogram registers a histogram for collection and aggregation
func (mi *MetricsIntegrator) RegisterHistogram(name string, histogram *Histogram, tags map[string]string) {
	if mi.collectionManager != nil {
		mi.collectionManager.RegisterHistogram(name, histogram, tags)
	}
	if mi.basicAggregator != nil {
		mi.basicAggregator.RegisterHistogram(name, histogram)
	}
}

// AddMetricValue adds a metric value directly to the enhanced aggregation system
func (mi *MetricsIntegrator) AddMetricValue(metricName string, value float64, tags map[string]string) error {
	if mi.enhancedAggregator == nil {
		return fmt.Errorf("enhanced aggregator not available")
	}
	return mi.enhancedAggregator.AddMetricValue(metricName, value, tags)
}

// GetSlidingWindowStats returns sliding window statistics for a metric
func (mi *MetricsIntegrator) GetSlidingWindowStats(metricName string) (*SlidingWindowStatistics, error) {
	if mi.enhancedAggregator == nil {
		return nil, fmt.Errorf("enhanced aggregator not available")
	}
	return mi.enhancedAggregator.GetSlidingWindowStats(metricName)
}

// GetTimeBucketStats returns time bucket statistics
func (mi *MetricsIntegrator) GetTimeBucketStats(startTime, endTime time.Time) (*TimeBucketStats, error) {
	if mi.enhancedAggregator == nil {
		return nil, fmt.Errorf("enhanced aggregator not available")
	}
	return mi.enhancedAggregator.GetTimeBucketStats(startTime, endTime)
}

// GetStatisticalSummary returns statistical summary for a metric
func (mi *MetricsIntegrator) GetStatisticalSummary(metricName string) (*StatisticalSummary, error) {
	if mi.enhancedAggregator == nil {
		return nil, fmt.Errorf("enhanced aggregator not available")
	}
	return mi.enhancedAggregator.GetStatisticalSummary(metricName)
}

// GetBasicAggregatedValues returns basic aggregated values for a metric
func (mi *MetricsIntegrator) GetBasicAggregatedValues(metricName string) []AggregatedValue {
	if mi.basicAggregator == nil {
		return nil
	}
	return mi.basicAggregator.GetAggregatedValues(metricName)
}

// GetCollectionStats returns collection statistics
func (mi *MetricsIntegrator) GetCollectionStats() *CollectionStats {
	if mi.collectionManager == nil {
		return nil
	}
	stats := mi.collectionManager.GetStats()
	return &stats
}

// IsRunning returns whether the integrator is currently running
func (mi *MetricsIntegrator) IsRunning() bool {
	mi.mu.RLock()
	defer mi.mu.RUnlock()
	return mi.isRunning
}

// GetAllMetrics returns all metrics being tracked
func (mi *MetricsIntegrator) GetAllMetrics() []string {
	if mi.enhancedAggregator != nil {
		return mi.enhancedAggregator.GetAllMetrics()
	}
	if mi.basicAggregator != nil {
		return mi.basicAggregator.GetRegisteredMetrics()
	}
	return nil
}
