package formats

import (
	"fmt"
	"io"
	"sort"
	"strconv"
	"strings"

	"neuralmetergo/internal/metrics/export"
)

// InfluxDBExporter implements the Exporter interface for InfluxDB line protocol format
type InfluxDBExporter struct {
	defaultDatabase   string // Default database name
	includeTimestamp  bool   // Whether to include timestamps (nanosecond precision)
	escapeFieldValues bool   // Whether to escape field values
	measurementPrefix string // Optional prefix for measurement names
}

// NewInfluxDBExporter creates a new InfluxDB line protocol exporter with default settings
func NewInfluxDBExporter() *InfluxDBExporter {
	return &InfluxDBExporter{
		defaultDatabase:   "",
		includeTimestamp:  true,
		escapeFieldValues: true,
		measurementPrefix: "",
	}
}

// NewInfluxDBExporterWithConfig creates a new InfluxDB exporter with custom configuration
func NewInfluxDBExporterWithConfig(database, measurementPrefix string, includeTimestamp bool) *InfluxDBExporter {
	return &InfluxDBExporter{
		defaultDatabase:   database,
		includeTimestamp:  includeTimestamp,
		escapeFieldValues: true,
		measurementPrefix: measurementPrefix,
	}
}

// Export writes metrics data in InfluxDB line protocol format to the provided writer
func (ie *InfluxDBExporter) Export(data *export.ExportData, writer io.Writer) error {
	if data == nil {
		return fmt.Errorf("export data cannot be nil")
	}

	// Export each metric series
	for _, series := range data.MetricSeries {
		for _, point := range series.DataPoints {
			line, err := ie.buildLineProtocolLine(series, point)
			if err != nil {
				return fmt.Errorf("failed to build line protocol for metric %s: %w", series.Name, err)
			}

			// Write the line
			fmt.Fprintln(writer, line)
		}
	}

	return nil
}

// GetContentType returns the MIME content type for InfluxDB line protocol format
func (ie *InfluxDBExporter) GetContentType() string {
	return "text/plain; charset=utf-8"
}

// GetFileExtension returns the recommended file extension for InfluxDB line protocol format
func (ie *InfluxDBExporter) GetFileExtension() string {
	return ".influx"
}

// SupportsStreaming indicates if InfluxDB line protocol format supports streaming export
func (ie *InfluxDBExporter) SupportsStreaming() bool {
	return true // Line protocol is ideal for streaming
}

// Validate checks if the export data is compatible with InfluxDB line protocol format
func (ie *InfluxDBExporter) Validate(data *export.ExportData) error {
	if data == nil {
		return fmt.Errorf("export data cannot be nil")
	}

	if len(data.MetricSeries) == 0 {
		return fmt.Errorf("no metric series to export")
	}

	// Validate that measurement names and tag keys are valid for InfluxDB
	for i, series := range data.MetricSeries {
		// Validate measurement name
		if !ie.isValidMeasurementName(series.Name) {
			return fmt.Errorf("metric series %d has invalid measurement name '%s' for InfluxDB", i, series.Name)
		}

		// Validate tag keys
		for key := range series.Labels {
			if !ie.isValidTagKey(key) {
				return fmt.Errorf("metric series %s has invalid tag key '%s' for InfluxDB", series.Name, key)
			}
		}

		// Validate data point tags and values
		for j, point := range series.DataPoints {
			if point.Timestamp.IsZero() {
				return fmt.Errorf("metric series %s, data point %d has zero timestamp", series.Name, j)
			}

			for key := range point.Tags {
				if !ie.isValidTagKey(key) {
					return fmt.Errorf("metric series %s, data point %d has invalid tag key '%s' for InfluxDB", series.Name, j, key)
				}
			}

			// Check if the value is valid for InfluxDB
			if !ie.isValidFieldValue(point.Value) {
				return fmt.Errorf("metric series %s, data point %d has invalid value for InfluxDB", series.Name, j)
			}
		}
	}

	return nil
}

// buildLineProtocolLine builds a single InfluxDB line protocol line
func (ie *InfluxDBExporter) buildLineProtocolLine(series export.MetricSeries, point export.MetricDataPoint) (string, error) {
	// Build measurement name
	measurement := ie.sanitizeMeasurementName(series.Name)
	if ie.measurementPrefix != "" {
		measurement = ie.measurementPrefix + measurement
	}

	// Build tag set (combining series labels and point tags)
	tagSet := ie.buildTagSet(series.Labels, point.Tags)

	// Build field set
	fieldSet := ie.buildFieldSet(point.Value, series.Type, point.Unit)

	// Build timestamp
	timestamp := ""
	if ie.includeTimestamp {
		// InfluxDB expects nanosecond precision
		timestamp = " " + strconv.FormatInt(point.Timestamp.UnixNano(), 10)
	}

	// Combine all parts: measurement[,tag_set] field_set [timestamp]
	line := measurement
	if tagSet != "" {
		line += "," + tagSet
	}
	line += " " + fieldSet + timestamp

	return line, nil
}

// buildTagSet builds the tag set portion of the line protocol
func (ie *InfluxDBExporter) buildTagSet(seriesLabels, pointTags map[string]string) string {
	// Combine all tags
	allTags := make(map[string]string)

	// Add series labels
	for key, value := range seriesLabels {
		if ie.isValidTagValue(value) {
			allTags[key] = value
		}
	}

	// Add point tags (override series labels if there's a conflict)
	for key, value := range pointTags {
		if ie.isValidTagValue(value) {
			allTags[key] = value
		}
	}

	if len(allTags) == 0 {
		return ""
	}

	// Sort tags by key for consistent output
	keys := make([]string, 0, len(allTags))
	for key := range allTags {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	// Build tag pairs
	pairs := make([]string, 0, len(keys))
	for _, key := range keys {
		value := ie.escapeTagValue(allTags[key])
		pairs = append(pairs, fmt.Sprintf("%s=%s", ie.escapeTagKey(key), value))
	}

	return strings.Join(pairs, ",")
}

// buildFieldSet builds the field set portion of the line protocol
func (ie *InfluxDBExporter) buildFieldSet(value float64, metricType, unit string) string {
	// Primary field is always the value
	fieldValue := ie.formatFieldValue(value)
	fields := []string{fmt.Sprintf("value=%s", fieldValue)}

	// Add additional fields if available
	if metricType != "" {
		fields = append(fields, fmt.Sprintf("type=%s", ie.escapeStringField(metricType)))
	}

	if unit != "" {
		fields = append(fields, fmt.Sprintf("unit=%s", ie.escapeStringField(unit)))
	}

	return strings.Join(fields, ",")
}

// sanitizeMeasurementName ensures measurement name is valid for InfluxDB
func (ie *InfluxDBExporter) sanitizeMeasurementName(name string) string {
	// Replace invalid characters with underscores
	result := ""
	for _, r := range name {
		if ie.isValidMeasurementChar(r) {
			result += string(r)
		} else {
			result += "_"
		}
	}

	// Ensure it doesn't start with underscore
	if len(result) > 0 && result[0] == '_' {
		result = "m" + result
	}

	return result
}

// escapeTagKey escapes a tag key for InfluxDB line protocol
func (ie *InfluxDBExporter) escapeTagKey(key string) string {
	// Escape commas, spaces, and equals signs
	result := strings.ReplaceAll(key, ",", `\,`)
	result = strings.ReplaceAll(result, " ", `\ `)
	result = strings.ReplaceAll(result, "=", `\=`)
	return result
}

// escapeTagValue escapes a tag value for InfluxDB line protocol
func (ie *InfluxDBExporter) escapeTagValue(value string) string {
	// Escape commas, spaces, and equals signs
	result := strings.ReplaceAll(value, ",", `\,`)
	result = strings.ReplaceAll(result, " ", `\ `)
	result = strings.ReplaceAll(result, "=", `\=`)
	return result
}

// escapeStringField escapes a string field value for InfluxDB line protocol
func (ie *InfluxDBExporter) escapeStringField(value string) string {
	// String fields are quoted and internal quotes are escaped
	result := strings.ReplaceAll(value, `"`, `\"`)
	return `"` + result + `"`
}

// formatFieldValue formats a float64 field value for InfluxDB line protocol
func (ie *InfluxDBExporter) formatFieldValue(value float64) string {
	// Handle special values
	if ie.isNaN(value) {
		return `"NaN"`
	}
	if ie.isPositiveInf(value) {
		return `"Inf"`
	}
	if ie.isNegativeInf(value) {
		return `"-Inf"`
	}

	// Use high precision formatting without scientific notation for most values
	formatted := strconv.FormatFloat(value, 'f', -1, 64)

	// If the result contains 'e' (scientific notation), switch to 'g' format
	if strings.Contains(formatted, "e") || strings.Contains(formatted, "E") {
		formatted = strconv.FormatFloat(value, 'g', -1, 64)
	}

	return formatted
}

// isValidMeasurementName checks if a measurement name is valid for InfluxDB
func (ie *InfluxDBExporter) isValidMeasurementName(name string) bool {
	if name == "" {
		return false
	}

	// Check for valid characters
	for _, r := range name {
		if !ie.isValidMeasurementChar(r) {
			return false
		}
	}

	return true
}

// isValidMeasurementChar checks if a character is valid in InfluxDB measurement names
func (ie *InfluxDBExporter) isValidMeasurementChar(r rune) bool {
	return (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') || r == '_' || r == '-' || r == '.'
}

// isValidTagKey checks if a tag key is valid for InfluxDB
func (ie *InfluxDBExporter) isValidTagKey(key string) bool {
	if key == "" || key == "_field" || key == "_measurement" || key == "time" {
		return false // Reserved names
	}

	return true // InfluxDB is quite permissive with tag keys after escaping
}

// isValidTagValue checks if a tag value is valid for InfluxDB
func (ie *InfluxDBExporter) isValidTagValue(value string) bool {
	// Empty values are not allowed for tags
	return value != ""
}

// isValidFieldValue checks if a field value is valid for InfluxDB
func (ie *InfluxDBExporter) isValidFieldValue(value float64) bool {
	// InfluxDB supports all float64 values including NaN and Inf
	return true
}

// isNaN checks if a float64 is NaN
func (ie *InfluxDBExporter) isNaN(value float64) bool {
	return value != value
}

// isPositiveInf checks if a float64 is positive infinity
func (ie *InfluxDBExporter) isPositiveInf(value float64) bool {
	return value > 1.79769e+308
}

// isNegativeInf checks if a float64 is negative infinity
func (ie *InfluxDBExporter) isNegativeInf(value float64) bool {
	return value < -1.79769e+308
}

// SetDefaultDatabase sets the default database name
func (ie *InfluxDBExporter) SetDefaultDatabase(database string) {
	ie.defaultDatabase = database
}

// SetIncludeTimestamp configures whether to include timestamps
func (ie *InfluxDBExporter) SetIncludeTimestamp(include bool) {
	ie.includeTimestamp = include
}

// SetMeasurementPrefix sets a prefix for measurement names
func (ie *InfluxDBExporter) SetMeasurementPrefix(prefix string) {
	ie.measurementPrefix = prefix
}

// SetEscapeFieldValues configures whether to escape field values
func (ie *InfluxDBExporter) SetEscapeFieldValues(escape bool) {
	ie.escapeFieldValues = escape
}
