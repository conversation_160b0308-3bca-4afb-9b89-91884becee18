package formats

import (
	"fmt"
	"io"

	"gopkg.in/yaml.v3"

	"neuralmetergo/internal/metrics/export"
)

// YAMLExporter implements the Exporter interface for YAML format
type YAMLExporter struct {
	pretty bool // Whether to format YAML with indentation
}

// NewYAMLExporter creates a new YAML exporter
func NewYAMLExporter(pretty bool) *YAMLExporter {
	return &YAMLExporter{
		pretty: pretty,
	}
}

// Export writes metrics data in YAML format to the provided writer
func (ye *YAMLExporter) Export(data *export.ExportData, writer io.Writer) error {
	if data == nil {
		return fmt.Errorf("export data cannot be nil")
	}

	// Validate data before exporting
	if err := ye.Validate(data); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// Configure YAML encoder
	encoder := yaml.NewEncoder(writer)
	defer encoder.Close()

	if ye.pretty {
		encoder.SetIndent(2) // Standard YAML indentation
	}

	// Encode the data
	if err := encoder.Encode(data); err != nil {
		return fmt.Errorf("failed to encode YAML: %w", err)
	}

	return nil
}

// GetContentType returns the MIME content type for YAML format
func (ye *YAMLExporter) GetContentType() string {
	return "application/yaml"
}

// GetFileExtension returns the recommended file extension for YAML format
func (ye *YAMLExporter) GetFileExtension() string {
	return ".yaml"
}

// SupportsStreaming indicates if YAML format supports streaming export
func (ye *YAMLExporter) SupportsStreaming() bool {
	return false // YAML doesn't have a standard streaming format like NDJSON
}

// Validate checks if the export data is compatible with YAML format
func (ye *YAMLExporter) Validate(data *export.ExportData) error {
	if data == nil {
		return fmt.Errorf("export data cannot be nil")
	}

	// Validate that all data points have valid timestamps and values
	for i, series := range data.MetricSeries {
		if series.Name == "" {
			return fmt.Errorf("metric series %d has empty name", i)
		}

		for j, point := range series.DataPoints {
			if point.Timestamp.IsZero() {
				return fmt.Errorf("metric series %s, data point %d has zero timestamp", series.Name, j)
			}
		}
	}

	return nil
}

// SetPrettyPrint configures whether to use pretty-printed YAML
func (ye *YAMLExporter) SetPrettyPrint(pretty bool) {
	ye.pretty = pretty
}
