package formats

import (
	"fmt"
	"io"
	"sort"
	"strconv"
	"strings"

	"neuralmetergo/internal/metrics/export"
)

// PrometheusExporter implements the Exporter interface for Prometheus text format
type PrometheusExporter struct {
	includeHelp      bool // Whether to include HELP comments
	includeType      bool // Whether to include TYPE comments
	includeTimestamp bool // Whether to include timestamps
}

// NewPrometheusExporter creates a new Prometheus exporter with default settings
func NewPrometheusExporter() *PrometheusExporter {
	return &PrometheusExporter{
		includeHelp:      true,
		includeType:      true,
		includeTimestamp: false, // Prometheus typically doesn't include timestamps for scraping
	}
}

// NewPrometheusExporterWithTimestamps creates a new Prometheus exporter that includes timestamps
func NewPrometheusExporterWithTimestamps() *PrometheusExporter {
	return &PrometheusExporter{
		includeHelp:      true,
		includeType:      true,
		includeTimestamp: true,
	}
}

// Export writes metrics data in Prometheus text format to the provided writer
func (pe *PrometheusExporter) Export(data *export.ExportData, writer io.Writer) error {
	if data == nil {
		return fmt.Errorf("export data cannot be nil")
	}

	// Group metrics by name to handle multiple data points
	metricGroups := pe.groupMetricsByName(data)

	// Export each metric group
	for _, group := range metricGroups {
		if err := pe.exportMetricGroup(group, writer); err != nil {
			return fmt.Errorf("failed to export metric group %s: %w", group.Name, err)
		}

		// Add blank line between metrics for readability
		fmt.Fprintln(writer)
	}

	return nil
}

// GetContentType returns the MIME content type for Prometheus format
func (pe *PrometheusExporter) GetContentType() string {
	return "text/plain; version=0.0.4; charset=utf-8"
}

// GetFileExtension returns the recommended file extension for Prometheus format
func (pe *PrometheusExporter) GetFileExtension() string {
	return ".prom"
}

// SupportsStreaming indicates if Prometheus format supports streaming export
func (pe *PrometheusExporter) SupportsStreaming() bool {
	return true // Prometheus format can be streamed metric by metric
}

// Validate checks if the export data is compatible with Prometheus format
func (pe *PrometheusExporter) Validate(data *export.ExportData) error {
	if data == nil {
		return fmt.Errorf("export data cannot be nil")
	}

	if len(data.MetricSeries) == 0 {
		return fmt.Errorf("no metric series to export")
	}

	// Validate metric names and labels conform to Prometheus standards
	for i, series := range data.MetricSeries {
		// Validate metric name
		if !pe.isValidMetricName(series.Name) {
			return fmt.Errorf("metric series %d has invalid name '%s' for Prometheus format", i, series.Name)
		}

		// Validate label names
		for key := range series.Labels {
			if !pe.isValidLabelName(key) {
				return fmt.Errorf("metric series %s has invalid label name '%s' for Prometheus format", series.Name, key)
			}
		}

		// Validate data point tags
		for j, point := range series.DataPoints {
			for key := range point.Tags {
				if !pe.isValidLabelName(key) {
					return fmt.Errorf("metric series %s, data point %d has invalid tag name '%s' for Prometheus format", series.Name, j, key)
				}
			}

			// Check if the value is valid for Prometheus
			if !pe.isValidValue(point.Value) {
				return fmt.Errorf("metric series %s, data point %d has invalid value for Prometheus format", series.Name, j)
			}
		}
	}

	return nil
}

// metricGroup represents a grouped set of metrics with the same name
type metricGroup struct {
	Name       string
	Help       string
	Type       string
	Unit       string
	DataPoints []export.MetricDataPoint
	Labels     map[string]string
}

// groupMetricsByName groups metrics by their name for Prometheus output
func (pe *PrometheusExporter) groupMetricsByName(data *export.ExportData) []*metricGroup {
	groups := make(map[string]*metricGroup)

	for _, series := range data.MetricSeries {
		metricName := pe.sanitizeMetricName(series.Name)

		if group, exists := groups[metricName]; exists {
			// Add data points to existing group
			group.DataPoints = append(group.DataPoints, series.DataPoints...)
		} else {
			// Create new group
			groups[metricName] = &metricGroup{
				Name:       metricName,
				Help:       series.Help,
				Type:       pe.normalizeMetricType(series.Type),
				Unit:       series.Unit,
				DataPoints: make([]export.MetricDataPoint, len(series.DataPoints)),
				Labels:     series.Labels,
			}
			copy(groups[metricName].DataPoints, series.DataPoints)
		}
	}

	// Convert map to sorted slice for consistent output
	result := make([]*metricGroup, 0, len(groups))
	for _, group := range groups {
		result = append(result, group)
	}

	// Sort groups by name
	sort.Slice(result, func(i, j int) bool {
		return result[i].Name < result[j].Name
	})

	return result
}

// exportMetricGroup exports a single metric group in Prometheus format
func (pe *PrometheusExporter) exportMetricGroup(group *metricGroup, writer io.Writer) error {
	// Write HELP comment
	if pe.includeHelp && group.Help != "" {
		fmt.Fprintf(writer, "# HELP %s %s\n", group.Name, pe.escapeHelpText(group.Help))
	}

	// Write TYPE comment
	if pe.includeType && group.Type != "" {
		fmt.Fprintf(writer, "# TYPE %s %s\n", group.Name, group.Type)
	}

	// Write data points
	for _, point := range group.DataPoints {
		if err := pe.exportDataPoint(group.Name, point, group.Labels, writer); err != nil {
			return fmt.Errorf("failed to export data point: %w", err)
		}
	}

	return nil
}

// exportDataPoint exports a single data point in Prometheus format
func (pe *PrometheusExporter) exportDataPoint(metricName string, point export.MetricDataPoint, seriesLabels map[string]string, writer io.Writer) error {
	// Combine series labels and point tags
	allLabels := make(map[string]string)

	// Add series labels
	for key, value := range seriesLabels {
		allLabels[key] = value
	}

	// Add point tags (override series labels if there's a conflict)
	for key, value := range point.Tags {
		allLabels[key] = value
	}

	// Build label string
	labelStr := pe.buildLabelString(allLabels)

	// Format the metric line
	line := metricName
	if labelStr != "" {
		line += "{" + labelStr + "}"
	}
	line += " " + pe.formatValue(point.Value)

	// Add timestamp if enabled
	if pe.includeTimestamp {
		// Convert to Unix timestamp in milliseconds
		timestamp := point.Timestamp.UnixNano() / 1000000
		line += " " + strconv.FormatInt(timestamp, 10)
	}

	// Write the line
	fmt.Fprintln(writer, line)

	return nil
}

// buildLabelString creates a Prometheus-formatted label string
func (pe *PrometheusExporter) buildLabelString(labels map[string]string) string {
	if len(labels) == 0 {
		return ""
	}

	// Sort labels by key for consistent output
	keys := make([]string, 0, len(labels))
	for key := range labels {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	// Build label pairs
	pairs := make([]string, 0, len(keys))
	for _, key := range keys {
		value := pe.escapeLabelValue(labels[key])
		pairs = append(pairs, fmt.Sprintf(`%s="%s"`, key, value))
	}

	return strings.Join(pairs, ",")
}

// sanitizeMetricName ensures metric name conforms to Prometheus standards
func (pe *PrometheusExporter) sanitizeMetricName(name string) string {
	// Replace invalid characters with underscores
	result := ""
	for i, r := range name {
		if i == 0 {
			// First character must be a letter or underscore
			if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || r == '_' {
				result += string(r)
			} else {
				result += "_"
			}
		} else {
			// Subsequent characters can be letters, digits, or underscores
			if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') || r == '_' {
				result += string(r)
			} else {
				result += "_"
			}
		}
	}

	return result
}

// normalizeMetricType normalizes metric type to Prometheus standards
func (pe *PrometheusExporter) normalizeMetricType(metricType string) string {
	switch strings.ToLower(metricType) {
	case "counter":
		return "counter"
	case "gauge":
		return "gauge"
	case "histogram":
		return "histogram"
	case "summary":
		return "summary"
	default:
		return "gauge" // Default to gauge for unknown types
	}
}

// formatValue formats a float64 value for Prometheus output
func (pe *PrometheusExporter) formatValue(value float64) string {
	// Handle special values
	if pe.isNaN(value) {
		return "NaN"
	}
	if pe.isPositiveInf(value) {
		return "+Inf"
	}
	if pe.isNegativeInf(value) {
		return "-Inf"
	}

	// Use high precision formatting
	return strconv.FormatFloat(value, 'g', -1, 64)
}

// escapeLabelValue escapes a label value for Prometheus format
func (pe *PrometheusExporter) escapeLabelValue(value string) string {
	// Escape backslashes, double quotes, and newlines
	result := strings.ReplaceAll(value, `\`, `\\`)
	result = strings.ReplaceAll(result, `"`, `\"`)
	result = strings.ReplaceAll(result, "\n", `\n`)
	return result
}

// escapeHelpText escapes help text for Prometheus format
func (pe *PrometheusExporter) escapeHelpText(help string) string {
	// Escape backslashes and newlines in help text
	result := strings.ReplaceAll(help, `\`, `\\`)
	result = strings.ReplaceAll(result, "\n", `\n`)
	return result
}

// isValidMetricName checks if a metric name is valid for Prometheus
func (pe *PrometheusExporter) isValidMetricName(name string) bool {
	if name == "" {
		return false
	}

	// First character must be a letter or underscore
	firstChar := rune(name[0])
	if !((firstChar >= 'a' && firstChar <= 'z') || (firstChar >= 'A' && firstChar <= 'Z') || firstChar == '_') {
		return false
	}

	// Subsequent characters must be letters, digits, or underscores
	for _, r := range name[1:] {
		if !((r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') || r == '_') {
			return false
		}
	}

	return true
}

// isValidLabelName checks if a label name is valid for Prometheus
func (pe *PrometheusExporter) isValidLabelName(name string) bool {
	if name == "" || strings.HasPrefix(name, "__") {
		return false // Empty names and __ prefix are reserved
	}

	return pe.isValidMetricName(name) // Same rules as metric names
}

// isValidValue checks if a float64 value is valid for Prometheus
func (pe *PrometheusExporter) isValidValue(value float64) bool {
	// Prometheus supports NaN and Inf, so all float64 values are valid
	return true
}

// isNaN checks if a float64 is NaN
func (pe *PrometheusExporter) isNaN(value float64) bool {
	return value != value
}

// isPositiveInf checks if a float64 is positive infinity
func (pe *PrometheusExporter) isPositiveInf(value float64) bool {
	return value > 1.79769e+308
}

// isNegativeInf checks if a float64 is negative infinity
func (pe *PrometheusExporter) isNegativeInf(value float64) bool {
	return value < -1.79769e+308
}

// SetIncludeHelp configures whether to include HELP comments
func (pe *PrometheusExporter) SetIncludeHelp(include bool) {
	pe.includeHelp = include
}

// SetIncludeType configures whether to include TYPE comments
func (pe *PrometheusExporter) SetIncludeType(include bool) {
	pe.includeType = include
}

// SetIncludeTimestamp configures whether to include timestamps
func (pe *PrometheusExporter) SetIncludeTimestamp(include bool) {
	pe.includeTimestamp = include
}
