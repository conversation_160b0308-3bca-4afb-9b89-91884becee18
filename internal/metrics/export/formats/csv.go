package formats

import (
	"encoding/csv"
	"fmt"
	"io"
	"strconv"
	"strings"

	"neuralmetergo/internal/metrics/export"
)

// CSVExporter implements the Exporter interface for CSV format
type CSVExporter struct {
	includeHeaders  bool   // Whether to include column headers
	delimiter       rune   // CSV delimiter (default: comma)
	flattenTags     bool   // Whether to flatten tags into separate columns
	timestampFormat string // Format for timestamp output
}

// NewCSVExporter creates a new CSV exporter with default settings
func NewCSVExporter() *CSVExporter {
	return &CSVExporter{
		includeHeaders:  true,
		delimiter:       ',',
		flattenTags:     true,
		timestampFormat: "2006-01-02T15:04:05.000Z07:00", // RFC3339 with milliseconds
	}
}

// NewCSVExporterCustom creates a new CSV exporter with custom settings
func NewCSVExporterCustom(includeHeaders bool, delimiter rune, flattenTags bool) *CSVExporter {
	return &CSVExporter{
		includeHeaders:  includeHeaders,
		delimiter:       delimiter,
		flattenTags:     flattenTags,
		timestampFormat: "2006-01-02T15:04:05.000Z07:00",
	}
}

// Export writes metrics data in CSV format to the provided writer
func (ce *CSVExporter) Export(data *export.ExportData, writer io.Writer) error {
	if data == nil {
		return fmt.Errorf("export data cannot be nil")
	}

	csvWriter := csv.NewWriter(writer)
	csvWriter.Comma = ce.delimiter
	defer csvWriter.Flush()

	// Collect all unique tag keys across all data points for consistent columns
	tagKeys := ce.collectTagKeys(data)

	// Write headers if enabled
	if ce.includeHeaders {
		headers := ce.buildHeaders(tagKeys)
		if err := csvWriter.Write(headers); err != nil {
			return fmt.Errorf("failed to write CSV headers: %w", err)
		}
	}

	// Write data rows
	for _, series := range data.MetricSeries {
		for _, point := range series.DataPoints {
			row := ce.buildDataRow(series, point, tagKeys)
			if err := csvWriter.Write(row); err != nil {
				return fmt.Errorf("failed to write CSV row: %w", err)
			}
		}
	}

	return nil
}

// GetContentType returns the MIME content type for CSV format
func (ce *CSVExporter) GetContentType() string {
	return "text/csv"
}

// GetFileExtension returns the recommended file extension for CSV format
func (ce *CSVExporter) GetFileExtension() string {
	return ".csv"
}

// SupportsStreaming indicates if CSV format supports streaming export
func (ce *CSVExporter) SupportsStreaming() bool {
	return true // CSV supports streaming row by row
}

// Validate checks if the export data is compatible with CSV format
func (ce *CSVExporter) Validate(data *export.ExportData) error {
	if data == nil {
		return fmt.Errorf("export data cannot be nil")
	}

	if len(data.MetricSeries) == 0 {
		return fmt.Errorf("no metric series to export")
	}

	// Validate that all data points have valid values for CSV
	for i, series := range data.MetricSeries {
		if series.Name == "" {
			return fmt.Errorf("metric series %d has empty name", i)
		}

		for j, point := range series.DataPoints {
			if point.Timestamp.IsZero() {
				return fmt.Errorf("metric series %s, data point %d has zero timestamp", series.Name, j)
			}

			// Check if the value is a valid number
			if !ce.isValidNumber(point.Value) {
				return fmt.Errorf("metric series %s, data point %d has invalid value", series.Name, j)
			}
		}
	}

	return nil
}

// collectTagKeys collects all unique tag keys from the export data
func (ce *CSVExporter) collectTagKeys(data *export.ExportData) []string {
	if !ce.flattenTags {
		return nil
	}

	tagKeySet := make(map[string]bool)

	for _, series := range data.MetricSeries {
		// Add series-level labels as tag keys
		for key := range series.Labels {
			tagKeySet[key] = true
		}

		// Add data point tag keys
		for _, point := range series.DataPoints {
			for key := range point.Tags {
				tagKeySet[key] = true
			}
		}
	}

	// Convert set to sorted slice for consistent column ordering
	tagKeys := make([]string, 0, len(tagKeySet))
	for key := range tagKeySet {
		tagKeys = append(tagKeys, key)
	}

	// Sort tag keys for consistent output
	for i := 0; i < len(tagKeys)-1; i++ {
		for j := i + 1; j < len(tagKeys); j++ {
			if tagKeys[i] > tagKeys[j] {
				tagKeys[i], tagKeys[j] = tagKeys[j], tagKeys[i]
			}
		}
	}

	return tagKeys
}

// buildHeaders creates the CSV header row
func (ce *CSVExporter) buildHeaders(tagKeys []string) []string {
	headers := []string{
		"timestamp",
		"metric_name",
		"value",
		"type",
		"unit",
		"help",
	}

	if ce.flattenTags {
		// Add tag columns
		for _, key := range tagKeys {
			headers = append(headers, "tag_"+key)
		}
	} else {
		headers = append(headers, "tags")
	}

	return headers
}

// buildDataRow creates a CSV data row for a metric data point
func (ce *CSVExporter) buildDataRow(series export.MetricSeries, point export.MetricDataPoint, tagKeys []string) []string {
	row := []string{
		point.Timestamp.Format(ce.timestampFormat),
		series.Name,
		ce.formatValue(point.Value),
		series.Type,
		point.Unit,
		series.Help,
	}

	if ce.flattenTags {
		// Add tag values in the same order as tag keys
		for _, key := range tagKeys {
			value := ""

			// Check series labels first
			if v, exists := series.Labels[key]; exists {
				value = v
			}

			// Check point tags (override series labels if present)
			if v, exists := point.Tags[key]; exists {
				value = v
			}

			row = append(row, value)
		}
	} else {
		// Combine all tags into a single formatted string
		var tagPairs []string

		// Add series labels
		for key, value := range series.Labels {
			tagPairs = append(tagPairs, fmt.Sprintf("%s=%s", key, value))
		}

		// Add point tags
		for key, value := range point.Tags {
			tagPairs = append(tagPairs, fmt.Sprintf("%s=%s", key, value))
		}

		row = append(row, strings.Join(tagPairs, ";"))
	}

	return row
}

// formatValue formats a float64 value for CSV output
func (ce *CSVExporter) formatValue(value float64) string {
	// Use high precision formatting to avoid scientific notation for most values
	return strconv.FormatFloat(value, 'f', -1, 64)
}

// isValidNumber checks if a float64 value is valid for CSV export
func (ce *CSVExporter) isValidNumber(value float64) bool {
	// Check for NaN and Inf values
	return !ce.isNaN(value) && !ce.isInf(value)
}

// isNaN checks if a float64 is NaN
func (ce *CSVExporter) isNaN(value float64) bool {
	return value != value
}

// isInf checks if a float64 is infinite
func (ce *CSVExporter) isInf(value float64) bool {
	return value > 1.79769e+308 || value < -1.79769e+308
}

// SetDelimiter sets the CSV delimiter
func (ce *CSVExporter) SetDelimiter(delimiter rune) {
	ce.delimiter = delimiter
}

// SetIncludeHeaders configures whether to include column headers
func (ce *CSVExporter) SetIncludeHeaders(include bool) {
	ce.includeHeaders = include
}

// SetFlattenTags configures whether to flatten tags into separate columns
func (ce *CSVExporter) SetFlattenTags(flatten bool) {
	ce.flattenTags = flatten
}

// SetTimestampFormat sets the format for timestamp output
func (ce *CSVExporter) SetTimestampFormat(format string) {
	ce.timestampFormat = format
}
