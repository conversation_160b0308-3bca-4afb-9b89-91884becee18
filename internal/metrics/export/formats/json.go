package formats

import (
	"encoding/json"
	"fmt"
	"io"

	"neuralmetergo/internal/metrics/export"
)

// JSONExporter implements the Exporter interface for JSON format
type JSONExporter struct {
	pretty            bool // Whether to format JSO<PERSON> with indentation
	includeNullValues bool // Whether to include null/empty values
}

// NewJSONExporter creates a new JSON exporter
func NewJSONExporter(pretty bool) *JSONExporter {
	return &JSONExporter{
		pretty:            pretty,
		includeNullValues: true,
	}
}

// NewJSONExporterCompact creates a new compact JSON exporter
func NewJSONExporterCompact() *JSONExporter {
	return &JSONExporter{
		pretty:            false,
		includeNullValues: false,
	}
}

// Export writes metrics data in JSON format to the provided writer
func (je *JSONExporter) Export(data *export.ExportData, writer io.Writer) error {
	if data == nil {
		return fmt.Errorf("export data cannot be nil")
	}

	encoder := json.NewEncoder(writer)

	if je.pretty {
		encoder.SetIndent("", "  ")
	}

	if !je.includeNullValues {
		encoder.SetEscapeHTML(false)
	}

	if err := encoder.Encode(data); err != nil {
		return fmt.Errorf("failed to encode JSON: %w", err)
	}

	return nil
}

// GetContentType returns the MIME content type for JSON format
func (je *JSONExporter) GetContentType() string {
	return "application/json"
}

// GetFileExtension returns the recommended file extension for JSON format
func (je *JSONExporter) GetFileExtension() string {
	return ".json"
}

// SupportsStreaming indicates if JSON format supports streaming export
func (je *JSONExporter) SupportsStreaming() bool {
	return true // JSON supports streaming with line-delimited JSON
}

// Validate checks if the export data is compatible with JSON format
func (je *JSONExporter) Validate(data *export.ExportData) error {
	if data == nil {
		return fmt.Errorf("export data cannot be nil")
	}

	// JSON format can handle any data structure, so minimal validation needed
	if len(data.MetricSeries) == 0 {
		return fmt.Errorf("no metric series to export")
	}

	// Validate that all data points have valid timestamps and values
	for i, series := range data.MetricSeries {
		if series.Name == "" {
			return fmt.Errorf("metric series %d has empty name", i)
		}

		for j, point := range series.DataPoints {
			if point.Timestamp.IsZero() {
				return fmt.Errorf("metric series %s, data point %d has zero timestamp", series.Name, j)
			}
		}
	}

	return nil
}

// ExportStream writes metrics data as line-delimited JSON (NDJSON)
func (je *JSONExporter) ExportStream(series []export.MetricSeries, writer io.Writer) error {
	encoder := json.NewEncoder(writer)

	if !je.includeNullValues {
		encoder.SetEscapeHTML(false)
	}

	for _, s := range series {
		for _, point := range s.DataPoints {
			// Create a flattened structure for streaming
			streamEntry := map[string]interface{}{
				"timestamp":   point.Timestamp,
				"metric_name": s.Name,
				"value":       point.Value,
				"tags":        point.Tags,
			}

			if s.Type != "" {
				streamEntry["type"] = s.Type
			}
			if point.Unit != "" {
				streamEntry["unit"] = point.Unit
			}
			if s.Help != "" {
				streamEntry["help"] = s.Help
			}

			if err := encoder.Encode(streamEntry); err != nil {
				return fmt.Errorf("failed to encode streaming JSON entry: %w", err)
			}
		}
	}

	return nil
}

// SetPrettyPrint configures whether to use pretty-printed JSON
func (je *JSONExporter) SetPrettyPrint(pretty bool) {
	je.pretty = pretty
}

// SetIncludeNullValues configures whether to include null/empty values
func (je *JSONExporter) SetIncludeNullValues(include bool) {
	je.includeNullValues = include
}
