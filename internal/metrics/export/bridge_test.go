package export

import (
	"fmt"
	"io"
	"testing"
	"time"

	"neuralmetergo/internal/metrics"
)

// mockExporter implements the Exporter interface for testing
type mockExporter struct{}

func (me *mockExporter) Export(data *ExportData, writer io.Writer) error {
	if data == nil {
		return fmt.Errorf("export data cannot be nil")
	}
	_, err := writer.Write([]byte(`{"test": "data"}`))
	return err
}

func (me *mockExporter) GetContentType() string {
	return "application/json"
}

func (me *mockExporter) GetFileExtension() string {
	return ".json"
}

func (me *mockExporter) SupportsStreaming() bool {
	return true
}

func (me *mockExporter) Validate(data *ExportData) error {
	if data == nil {
		return fmt.Errorf("export data cannot be nil")
	}
	return nil
}

// setupExportManagerWithFormats creates an ExportManager with mock exporters registered
func setupExportManagerWithFormats() *ExportManager {
	manager := NewExportManager()

	// Register mock exporters for testing
	mockExp := &mockExporter{}
	manager.RegisterExporter(FormatJSON, mockExp)
	manager.RegisterExporter(FormatCSV, mockExp)
	manager.RegisterExporter(FormatPrometheus, mockExp)
	manager.RegisterExporter(FormatInfluxDB, mockExp)

	return manager
}

// TestMetricsBridge_Creation tests the creation of MetricsBridge
func TestMetricsBridge_Creation(t *testing.T) {
	t.Parallel()

	integrator := &metrics.MetricsIntegrator{}
	exportManager := NewExportManager()

	config := BridgeConfig{
		CollectionInterval:   time.Second * 10,
		MaxConcurrentExports: 3,
		ExportTimeout:        time.Minute * 5,
		IncludeMetadata:      true,
		EnableBuffering:      true,
		BufferSize:           1000,
		DefaultFormat:        FormatJSON,
		DefaultDestination:   "./test_exports",
		AutoExportInterval:   time.Minute * 5,
		ExportConfigs:        make(map[string]ExportConfig),
		RemoteConfigs:        make(map[string]RemoteEndpointConfig),
	}

	bridge := NewMetricsBridge(integrator, exportManager, config)
	if bridge == nil {
		t.Fatal("Expected non-nil MetricsBridge")
	}

	if bridge.integrator != integrator {
		t.Error("Expected integrator to be set correctly")
	}

	if bridge.exportManager != exportManager {
		t.Error("Expected exportManager to be set correctly")
	}

	if bridge.isRunning {
		t.Error("Expected bridge to not be running initially")
	}

	if bridge.exportJobs == nil {
		t.Error("Expected exportJobs map to be initialized")
	}
}

// TestMetricsBridge_StartStop tests starting and stopping the bridge
func TestMetricsBridge_StartStop(t *testing.T) {
	t.Parallel()

	integrator := &metrics.MetricsIntegrator{}
	exportManager := NewExportManager()
	config := DefaultBridgeConfig()

	bridge := NewMetricsBridge(integrator, exportManager, config)

	// Test start
	err := bridge.Start()
	if err != nil {
		t.Fatalf("Failed to start bridge: %v", err)
	}

	if !bridge.isRunning {
		t.Error("Expected bridge to be running after start")
	}

	// Test stop
	err = bridge.Stop()
	if err != nil {
		t.Errorf("Failed to stop bridge: %v", err)
	}

	if bridge.isRunning {
		t.Error("Expected bridge to not be running after stop")
	}
}

// TestMetricsBridge_DataCollection tests the data collection functionality
func TestMetricsBridge_DataCollection(t *testing.T) {
	t.Parallel()

	integrator := &metrics.MetricsIntegrator{}
	exportManager := NewExportManager()
	config := DefaultBridgeConfig()

	bridge := NewMetricsBridge(integrator, exportManager, config)

	// Test data collection (will be empty but should not fail)
	data, err := bridge.CollectMetricsData()
	if err != nil {
		t.Errorf("Data collection failed: %v", err)
	}

	if data == nil {
		t.Error("Expected non-nil data from collection")
	}

	// Verify data structure
	if data.Timestamp.IsZero() {
		t.Error("Expected non-zero timestamp")
	}

	if data.Source == "" {
		t.Error("Expected non-empty source")
	}

	if data.Version == "" {
		t.Error("Expected non-empty version")
	}
}

// TestMetricsBridge_ExportAll tests the collect and export all functionality
func TestMetricsBridge_ExportAll(t *testing.T) {
	t.Parallel()

	// Create temporary file for testing
	tempFile := t.TempDir() + "/test_export.json"

	integrator := &metrics.MetricsIntegrator{}
	exportManager := setupExportManagerWithFormats()
	config := DefaultBridgeConfig()

	bridge := NewMetricsBridge(integrator, exportManager, config)

	err := bridge.Start()
	if err != nil {
		t.Fatalf("Failed to start bridge: %v", err)
	}
	defer bridge.Stop()

	// Test collect and export all
	err = bridge.CollectAndExportAll(FormatJSON, tempFile)
	if err != nil {
		t.Errorf("CollectAndExportAll failed: %v", err)
	}
}

// TestMetricsBridge_ScheduledExport tests scheduled export functionality
func TestMetricsBridge_ScheduledExport(t *testing.T) {
	t.Parallel()

	integrator := &metrics.MetricsIntegrator{}
	exportManager := setupExportManagerWithFormats()
	config := DefaultBridgeConfig()

	bridge := NewMetricsBridge(integrator, exportManager, config)

	err := bridge.Start()
	if err != nil {
		t.Fatalf("Failed to start bridge: %v", err)
	}
	defer bridge.Stop()

	// Create a schedule for testing
	schedule := Schedule{
		Type:     ScheduleTypeInterval,
		Interval: time.Hour, // Long interval for testing
		RunOnce:  false,
	}

	exportConfig := ExportConfig{
		Format:          FormatJSON,
		Destination:     t.TempDir() + "/scheduled_export.json",
		IncludeMetadata: true,
		Compression:     false,
	}

	// Schedule the export
	jobName := "test-scheduled-export"
	err = bridge.ScheduleExport(jobName, schedule, exportConfig)
	if err != nil {
		t.Errorf("Failed to schedule export: %v", err)
	}

	// Verify job was created
	jobs := bridge.GetExportJobs()
	if _, exists := jobs[jobName]; !exists {
		t.Error("Expected scheduled job to exist")
	}

	// Test removing the scheduled export
	err = bridge.RemoveExportJob(jobName)
	if err != nil {
		t.Errorf("Failed to remove scheduled export: %v", err)
	}

	// Verify job was removed
	jobs = bridge.GetExportJobs()
	if _, exists := jobs[jobName]; exists {
		t.Error("Expected scheduled job to be removed")
	}
}

// TestMetricsBridge_ErrorHandling tests error handling scenarios
func TestMetricsBridge_ErrorHandling(t *testing.T) {
	t.Parallel()

	integrator := &metrics.MetricsIntegrator{}
	exportManager := setupExportManagerWithFormats()
	config := DefaultBridgeConfig()

	bridge := NewMetricsBridge(integrator, exportManager, config)

	// Test operations on stopped bridge
	err := bridge.CollectAndExportAll(FormatJSON, "test.json")
	if err == nil {
		t.Error("Expected error when exporting from stopped bridge")
	}

	schedule := Schedule{
		Type:     ScheduleTypeInterval,
		Interval: time.Second * 30,
		RunOnce:  false,
	}

	exportConfig := ExportConfig{
		Format:          FormatJSON,
		Destination:     "test.json",
		IncludeMetadata: true,
		Compression:     false,
	}

	err = bridge.ScheduleExport("test-job", schedule, exportConfig)
	if err == nil {
		t.Error("Expected error when scheduling export on stopped bridge")
	}

	// Test invalid export destination
	err = bridge.Start()
	if err != nil {
		t.Fatalf("Failed to start bridge: %v", err)
	}
	defer bridge.Stop()

	err = bridge.CollectAndExportAll(FormatJSON, "/invalid/path/that/does/not/exist/export.json")
	if err == nil {
		t.Error("Expected error with invalid export destination")
	}
}

// Benchmark test for performance validation
func BenchmarkMetricsBridge_DataCollection(b *testing.B) {
	integrator := &metrics.MetricsIntegrator{}
	exportManager := NewExportManager()
	config := DefaultBridgeConfig()

	bridge := NewMetricsBridge(integrator, exportManager, config)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := bridge.CollectMetricsData()
		if err != nil {
			b.Fatalf("Data collection failed: %v", err)
		}
	}
}
