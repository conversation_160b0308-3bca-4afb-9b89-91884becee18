package export

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"os"
	"sync"
	"time"
)

// ExportScheduler manages the execution of scheduled export jobs
type ExportScheduler struct {
	jobManager    *JobManager
	exportManager *ExportManager
	isRunning     bool
	mu            sync.RWMutex
	stopChan      chan struct{}
	workers       int
	jobQueue      chan *ExportJob
	logger        *log.Logger

	// Configuration
	checkInterval time.Duration
	maxRetries    int

	// Statistics
	stats SchedulerStats
}

// SchedulerStats provides statistics about the scheduler
type SchedulerStats struct {
	StartTime      time.Time     `json:"start_time"`
	TotalJobsRun   int64         `json:"total_jobs_run"`
	SuccessfulJobs int64         `json:"successful_jobs"`
	FailedJobs     int64         `json:"failed_jobs"`
	AverageRunTime time.Duration `json:"average_run_time"`
	LastJobRunTime time.Time     `json:"last_job_run_time"`
	ActiveJobs     int           `json:"active_jobs"`
	QueuedJobs     int           `json:"queued_jobs"`
}

// SchedulerConfig configures the scheduler behavior
type SchedulerConfig struct {
	Workers       int           `json:"workers"`        // Number of concurrent workers
	CheckInterval time.Duration `json:"check_interval"` // How often to check for jobs to run
	MaxRetries    int           `json:"max_retries"`    // Maximum retry attempts
	Logger        *log.Logger   `json:"-"`              // Custom logger
}

// DefaultSchedulerConfig returns default scheduler configuration
func DefaultSchedulerConfig() *SchedulerConfig {
	return &SchedulerConfig{
		Workers:       5,
		CheckInterval: 10 * time.Second,
		MaxRetries:    3,
		Logger:        log.New(os.Stdout, "[ExportScheduler] ", log.LstdFlags),
	}
}

// NewExportScheduler creates a new export scheduler
func NewExportScheduler(jobManager *JobManager, exportManager *ExportManager, config *SchedulerConfig) *ExportScheduler {
	if config == nil {
		config = DefaultSchedulerConfig()
	}

	logger := config.Logger
	if logger == nil {
		logger = log.New(io.Discard, "", 0) // Discard logs if no logger provided
	}

	return &ExportScheduler{
		jobManager:    jobManager,
		exportManager: exportManager,
		isRunning:     false,
		stopChan:      make(chan struct{}),
		workers:       config.Workers,
		jobQueue:      make(chan *ExportJob, config.Workers*2), // Buffer for jobs
		checkInterval: config.CheckInterval,
		maxRetries:    config.MaxRetries,
		logger:        logger,
		stats: SchedulerStats{
			StartTime: time.Now(),
		},
	}
}

// Start begins the scheduler operation
func (s *ExportScheduler) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		return fmt.Errorf("scheduler is already running")
	}

	s.isRunning = true
	s.stats.StartTime = time.Now()
	s.logger.Println("Starting export scheduler with", s.workers, "workers")

	// Start worker goroutines
	for i := 0; i < s.workers; i++ {
		go s.worker(ctx, i)
	}

	// Start the job checker
	go s.jobChecker(ctx)

	return nil
}

// Stop gracefully stops the scheduler
func (s *ExportScheduler) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return fmt.Errorf("scheduler is not running")
	}

	s.logger.Println("Stopping export scheduler")
	s.isRunning = false
	close(s.stopChan)

	return nil
}

// IsRunning returns whether the scheduler is currently running
func (s *ExportScheduler) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.isRunning
}

// GetStats returns current scheduler statistics
func (s *ExportScheduler) GetStats() SchedulerStats {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Update active and queued jobs count
	activeJobs := len(s.jobManager.ListJobs(JobStatusActive))
	queuedJobs := len(s.jobQueue)

	stats := s.stats
	stats.ActiveJobs = activeJobs
	stats.QueuedJobs = queuedJobs

	return stats
}

// ExecuteJobNow immediately executes a job regardless of its schedule
func (s *ExportScheduler) ExecuteJobNow(jobID string) error {
	job, err := s.jobManager.GetJob(jobID)
	if err != nil {
		return fmt.Errorf("failed to get job: %w", err)
	}

	// Execute the job directly
	return s.executeJob(job)
}

// jobChecker periodically checks for jobs that need to be executed
func (s *ExportScheduler) jobChecker(ctx context.Context) {
	ticker := time.NewTicker(s.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-s.stopChan:
			return
		case <-ticker.C:
			s.checkAndQueueJobs()
		}
	}
}

// checkAndQueueJobs checks for jobs that are ready to run and queues them
func (s *ExportScheduler) checkAndQueueJobs() {
	activeJobs := s.jobManager.ListJobs(JobStatusActive)
	now := time.Now()

	for _, job := range activeJobs {
		if job.NextRunAt != nil && job.NextRunAt.Before(now) {
			select {
			case s.jobQueue <- job:
				s.logger.Printf("Queued job %s (%s) for execution", job.ID, job.Name)
			default:
				s.logger.Printf("Job queue full, skipping job %s", job.ID)
			}
		}
	}
}

// worker processes jobs from the job queue
func (s *ExportScheduler) worker(ctx context.Context, workerID int) {
	s.logger.Printf("Worker %d started", workerID)

	for {
		select {
		case <-ctx.Done():
			s.logger.Printf("Worker %d stopping (context cancelled)", workerID)
			return
		case <-s.stopChan:
			s.logger.Printf("Worker %d stopping", workerID)
			return
		case job := <-s.jobQueue:
			if job != nil {
				s.logger.Printf("Worker %d executing job %s", workerID, job.ID)
				s.executeJobWithRetry(job)
			}
		}
	}
}

// executeJobWithRetry executes a job with retry logic
func (s *ExportScheduler) executeJobWithRetry(job *ExportJob) {
	var lastErr error

	for attempt := 0; attempt <= s.maxRetries; attempt++ {
		err := s.executeJob(job)
		if err == nil {
			// Success
			s.updateJobAfterSuccess(job)
			s.updateStats(true, time.Since(time.Now()))
			return
		}

		lastErr = err
		s.logger.Printf("Job %s failed (attempt %d/%d): %v", job.ID, attempt+1, s.maxRetries+1, err)

		if attempt < s.maxRetries {
			// Wait before retry with exponential backoff
			waitTime := time.Duration(attempt+1) * time.Second
			time.Sleep(waitTime)
		}
	}

	// All retries failed
	s.updateJobAfterFailure(job, lastErr)
	s.updateStats(false, time.Since(time.Now()))
	s.logger.Printf("Job %s failed after %d attempts: %v", job.ID, s.maxRetries+1, lastErr)
}

// executeJob performs the actual job execution
func (s *ExportScheduler) executeJob(job *ExportJob) error {
	startTime := time.Now()
	executionID := fmt.Sprintf("%s-%d", job.ID, startTime.Unix())

	// Create job execution record
	execution := &JobExecution{
		JobID:       job.ID,
		ExecutionID: executionID,
		StartTime:   startTime,
		Status:      JobStatusActive,
	}

	// Create export configuration
	config := ExportConfig{
		Format:         job.Format,
		FilterCriteria: job.FilterCriteria,
		RetryConfig:    job.RetryConfig,
	}

	// Get metrics data for export (this would integrate with your metrics system)
	exportData, err := s.collectMetricsData(job)
	if err != nil {
		execution.Status = JobStatusFailed
		execution.Error = err.Error()
		execution.EndTime = &[]time.Time{time.Now()}[0]
		execution.Duration = time.Since(startTime)
		s.jobManager.recordExecution(execution)
		return fmt.Errorf("failed to collect metrics data: %w", err)
	}

	// Create output destination
	output, closeFunc, err := s.createOutput(job.Destination)
	if err != nil {
		execution.Status = JobStatusFailed
		execution.Error = err.Error()
		execution.EndTime = &[]time.Time{time.Now()}[0]
		execution.Duration = time.Since(startTime)
		s.jobManager.recordExecution(execution)
		return fmt.Errorf("failed to create output: %w", err)
	}
	defer closeFunc()

	// Perform the export
	err = s.exportManager.Export(exportData, config, output)
	if err != nil {
		execution.Status = JobStatusFailed
		execution.Error = err.Error()
		execution.EndTime = &[]time.Time{time.Now()}[0]
		execution.Duration = time.Since(startTime)
		s.jobManager.recordExecution(execution)
		return fmt.Errorf("export failed: %w", err)
	}

	// Success - update execution record
	endTime := time.Now()
	execution.Status = JobStatusCompleted
	execution.EndTime = &endTime
	execution.Duration = endTime.Sub(startTime)

	// Calculate bytes exported (approximate based on output)
	if buf, ok := output.(*bytes.Buffer); ok {
		execution.BytesExported = int64(buf.Len())
	}

	// Calculate records exported (approximate based on metrics data)
	totalRecords := int64(0)
	for _, series := range exportData.MetricSeries {
		totalRecords += int64(len(series.DataPoints))
	}
	execution.RecordsExported = totalRecords

	s.jobManager.recordExecution(execution)

	s.logger.Printf("Job %s completed successfully in %v", job.ID, execution.Duration)
	return nil
}

// collectMetricsData collects metrics data for export
// This is a placeholder that would integrate with your actual metrics system
func (s *ExportScheduler) collectMetricsData(job *ExportJob) (*ExportData, error) {
	// This would integrate with your MetricsIntegrator and other metrics components
	// For now, return a basic structure
	now := time.Now()

	return &ExportData{
		Timestamp:   now,
		Source:      "neuralmetergo-scheduler",
		Version:     "1.0.0",
		Description: fmt.Sprintf("Scheduled export from job %s", job.Name),
		MetricSeries: []MetricSeries{
			{
				Name: "scheduler_test_metric",
				Help: "Test metric from scheduler",
				Type: "counter",
				Unit: "count",
				DataPoints: []MetricDataPoint{
					{
						Name:      "scheduler_test_metric",
						Value:     1.0,
						Timestamp: now,
						Tags: map[string]string{
							"job_id":   job.ID,
							"job_name": job.Name,
						},
						Unit: "count",
					},
				},
			},
		},
		Metadata: map[string]interface{}{
			"job_id":      job.ID,
			"job_name":    job.Name,
			"export_time": now.Format(time.RFC3339),
		},
	}, nil
}

// createOutput creates the appropriate output writer based on destination
func (s *ExportScheduler) createOutput(dest ExportDestination) (io.Writer, func(), error) {
	switch dest.Type {
	case "file":
		file, err := os.Create(dest.Location)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to create file %s: %w", dest.Location, err)
		}
		return file, func() { file.Close() }, nil

	case "stdout":
		return os.Stdout, func() {}, nil

	case "buffer":
		buffer := &bytes.Buffer{}
		return buffer, func() {}, nil

	default:
		return nil, nil, fmt.Errorf("unsupported destination type: %s", dest.Type)
	}
}

// updateJobAfterSuccess updates a job after successful execution
func (s *ExportScheduler) updateJobAfterSuccess(job *ExportJob) {
	job.mu.Lock()
	defer job.mu.Unlock()

	now := time.Now()
	job.LastRunAt = &now
	job.RunCount++
	job.SuccessCount++

	// Calculate next run time
	s.jobManager.calculateNextRunTime(job)
}

// updateJobAfterFailure updates a job after failed execution
func (s *ExportScheduler) updateJobAfterFailure(job *ExportJob, err error) {
	job.mu.Lock()
	defer job.mu.Unlock()

	now := time.Now()
	job.LastRunAt = &now
	job.RunCount++
	job.FailureCount++
	job.lastError = err

	// For failed jobs, still calculate next run time to retry later
	s.jobManager.calculateNextRunTime(job)
}

// updateStats updates scheduler statistics
func (s *ExportScheduler) updateStats(success bool, duration time.Duration) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.stats.TotalJobsRun++
	s.stats.LastJobRunTime = time.Now()

	if success {
		s.stats.SuccessfulJobs++
	} else {
		s.stats.FailedJobs++
	}

	// Update average run time
	if s.stats.TotalJobsRun == 1 {
		s.stats.AverageRunTime = duration
	} else {
		// Exponential moving average
		s.stats.AverageRunTime = time.Duration((int64(s.stats.AverageRunTime)*9 + int64(duration)) / 10)
	}
}
