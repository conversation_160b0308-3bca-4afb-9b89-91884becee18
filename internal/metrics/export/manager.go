// Package export provides metrics export functionality in multiple formats
package export

import (
	"context"
	"fmt"
	"io"
	"sync"
	"time"
)

// ExportFormat represents the output format for metrics export
type ExportFormat string

const (
	FormatJSON       ExportFormat = "json"
	FormatCSV        ExportFormat = "csv"
	FormatPrometheus ExportFormat = "prometheus"
	FormatInfluxDB   ExportFormat = "influxdb"
	FormatYAML       ExportFormat = "yaml"
)

// MetricDataPoint represents a single metric data point for export
type MetricDataPoint struct {
	Name      string            `json:"name"`
	Value     float64           `json:"value"`
	Timestamp time.Time         `json:"timestamp"`
	Tags      map[string]string `json:"tags"`
	Unit      string            `json:"unit,omitempty"`
	Help      string            `json:"help,omitempty"`
}

// MetricSeries represents a collection of related metric data points
type MetricSeries struct {
	Name        string                 `json:"name"`
	Help        string                 `json:"help,omitempty"`
	Type        string                 `json:"type"` // counter, gauge, histogram, summary
	Unit        string                 `json:"unit,omitempty"`
	DataPoints  []MetricDataPoint      `json:"data_points"`
	Labels      map[string]string      `json:"labels,omitempty"`      // Series-level labels
	Aggregation map[string]interface{} `json:"aggregation,omitempty"` // Summary statistics
}

// ExportData represents the complete dataset for export
type ExportData struct {
	Timestamp    time.Time              `json:"timestamp"`
	Source       string                 `json:"source"`
	Version      string                 `json:"version,omitempty"`
	Description  string                 `json:"description,omitempty"`
	MetricSeries []MetricSeries         `json:"metric_series"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// FilterCriteria defines criteria for filtering metrics during export
type FilterCriteria struct {
	MetricNames    []string          `json:"metric_names,omitempty"`    // Specific metric names to include
	ExcludeNames   []string          `json:"exclude_names,omitempty"`   // Metric names to exclude
	TagFilters     map[string]string `json:"tag_filters,omitempty"`     // Tag key-value pairs to match
	TimeRange      *TimeRange        `json:"time_range,omitempty"`      // Time range filter
	ValueThreshold *ValueThreshold   `json:"value_threshold,omitempty"` // Value-based filtering
}

// TimeRange specifies a time range for filtering
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// ValueThreshold specifies value-based filtering criteria
type ValueThreshold struct {
	Min *float64 `json:"min,omitempty"`
	Max *float64 `json:"max,omitempty"`
}

// ExportConfig defines configuration for metric export operations
type ExportConfig struct {
	Format          ExportFormat    `json:"format"`
	Destination     string          `json:"destination"` // File path or remote endpoint
	FilterCriteria  *FilterCriteria `json:"filter_criteria,omitempty"`
	IncludeMetadata bool            `json:"include_metadata"`
	Compression     bool            `json:"compression"`
	BatchSize       int             `json:"batch_size"`    // For streaming exports
	MaxFileSize     int64           `json:"max_file_size"` // For file rotation
	RetryConfig     *RetryConfig    `json:"retry_config,omitempty"`
}

// RetryConfig defines retry behavior for export operations
type RetryConfig struct {
	MaxAttempts     int           `json:"max_attempts"`
	InitialDelay    time.Duration `json:"initial_delay"`
	MaxDelay        time.Duration `json:"max_delay"`
	BackoffMultiple float64       `json:"backoff_multiple"`
}

// ExportStats tracks statistics about export operations
type ExportStats struct {
	TotalExports       int64         `json:"total_exports"`
	SuccessfulExports  int64         `json:"successful_exports"`
	FailedExports      int64         `json:"failed_exports"`
	LastExportTime     time.Time     `json:"last_export_time"`
	LastExportSize     int64         `json:"last_export_size"`
	TotalBytesExported int64         `json:"total_bytes_exported"`
	AverageExportTime  time.Duration `json:"average_export_time"`
	RetryAttempts      int64         `json:"retry_attempts"`
}

// Exporter defines the interface for format-specific exporters
type Exporter interface {
	// Export writes metrics data in the specific format to the provided writer
	Export(data *ExportData, writer io.Writer) error

	// GetContentType returns the MIME content type for this format
	GetContentType() string

	// GetFileExtension returns the recommended file extension for this format
	GetFileExtension() string

	// SupportsStreaming indicates if this format supports streaming export
	SupportsStreaming() bool

	// Validate checks if the export data is compatible with this format
	Validate(data *ExportData) error
}

// ExportManager manages metric export operations with multiple format support
type ExportManager struct {
	exporters map[ExportFormat]Exporter
	stats     ExportStats
	mu        sync.RWMutex
	ctx       context.Context
	cancel    context.CancelFunc
}

// NewExportManager creates a new export manager
func NewExportManager() *ExportManager {
	ctx, cancel := context.WithCancel(context.Background())

	return &ExportManager{
		exporters: make(map[ExportFormat]Exporter),
		stats:     ExportStats{},
		ctx:       ctx,
		cancel:    cancel,
	}
}

// RegisterExporter registers a format-specific exporter
func (em *ExportManager) RegisterExporter(format ExportFormat, exporter Exporter) {
	em.mu.Lock()
	defer em.mu.Unlock()

	em.exporters[format] = exporter
}

// GetRegisteredFormats returns all registered export formats
func (em *ExportManager) GetRegisteredFormats() []ExportFormat {
	em.mu.RLock()
	defer em.mu.RUnlock()

	formats := make([]ExportFormat, 0, len(em.exporters))
	for format := range em.exporters {
		formats = append(formats, format)
	}

	return formats
}

// Export performs metric export with the specified configuration
func (em *ExportManager) Export(data *ExportData, config ExportConfig, writer io.Writer) error {
	startTime := time.Now()

	em.mu.RLock()
	exporter, exists := em.exporters[config.Format]
	em.mu.RUnlock()

	if !exists {
		return fmt.Errorf("exporter for format %s not found", config.Format)
	}

	// Apply filters if specified
	filteredData := data
	if config.FilterCriteria != nil {
		var err error
		filteredData, err = em.applyFilters(data, config.FilterCriteria)
		if err != nil {
			em.updateFailureStats()
			return fmt.Errorf("failed to apply filters: %w", err)
		}
	}

	// Validate data compatibility
	if err := exporter.Validate(filteredData); err != nil {
		em.updateFailureStats()
		return fmt.Errorf("data validation failed: %w", err)
	}

	// Perform export
	if err := exporter.Export(filteredData, writer); err != nil {
		em.updateFailureStats()
		return fmt.Errorf("export failed: %w", err)
	}

	// Update success statistics
	em.updateSuccessStats(startTime)

	return nil
}

// ExportWithRetry performs export with retry logic
func (em *ExportManager) ExportWithRetry(data *ExportData, config ExportConfig, writer io.Writer) error {
	retryConfig := config.RetryConfig
	if retryConfig == nil {
		// Use default retry configuration
		retryConfig = &RetryConfig{
			MaxAttempts:     3,
			InitialDelay:    time.Second,
			MaxDelay:        time.Minute,
			BackoffMultiple: 2.0,
		}
	}

	var lastErr error
	delay := retryConfig.InitialDelay

	for attempt := 1; attempt <= retryConfig.MaxAttempts; attempt++ {
		err := em.Export(data, config, writer)
		if err == nil {
			return nil // Success
		}

		lastErr = err
		em.updateRetryStats()

		if attempt < retryConfig.MaxAttempts {
			// Wait before retry
			select {
			case <-em.ctx.Done():
				return fmt.Errorf("export cancelled: %w", em.ctx.Err())
			case <-time.After(delay):
				// Continue to next attempt
			}

			// Increase delay for next attempt
			delay = time.Duration(float64(delay) * retryConfig.BackoffMultiple)
			if delay > retryConfig.MaxDelay {
				delay = retryConfig.MaxDelay
			}
		}
	}

	return fmt.Errorf("export failed after %d attempts: %w", retryConfig.MaxAttempts, lastErr)
}

// GetStats returns current export statistics
func (em *ExportManager) GetStats() ExportStats {
	em.mu.RLock()
	defer em.mu.RUnlock()

	return em.stats
}

// Stop stops the export manager and cancels any ongoing operations
func (em *ExportManager) Stop() {
	em.cancel()
}

// applyFilters applies filtering criteria to export data
func (em *ExportManager) applyFilters(data *ExportData, criteria *FilterCriteria) (*ExportData, error) {
	filtered := &ExportData{
		Timestamp:    data.Timestamp,
		Source:       data.Source,
		Version:      data.Version,
		Description:  data.Description,
		Metadata:     data.Metadata,
		MetricSeries: make([]MetricSeries, 0),
	}

	for _, series := range data.MetricSeries {
		// Apply name filtering
		if !em.matchesNameFilter(series.Name, criteria) {
			continue
		}

		// Filter data points within the series
		filteredSeries := series
		filteredSeries.DataPoints = make([]MetricDataPoint, 0)

		for _, point := range series.DataPoints {
			if em.matchesPointFilter(point, criteria) {
				filteredSeries.DataPoints = append(filteredSeries.DataPoints, point)
			}
		}

		// Only include series that have data points after filtering
		if len(filteredSeries.DataPoints) > 0 {
			filtered.MetricSeries = append(filtered.MetricSeries, filteredSeries)
		}
	}

	return filtered, nil
}

// matchesNameFilter checks if a metric name matches the filter criteria
func (em *ExportManager) matchesNameFilter(name string, criteria *FilterCriteria) bool {
	// Check include list
	if len(criteria.MetricNames) > 0 {
		found := false
		for _, includeName := range criteria.MetricNames {
			if includeName == name {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Check exclude list
	for _, excludeName := range criteria.ExcludeNames {
		if excludeName == name {
			return false
		}
	}

	return true
}

// matchesPointFilter checks if a data point matches the filter criteria
func (em *ExportManager) matchesPointFilter(point MetricDataPoint, criteria *FilterCriteria) bool {
	// Check time range
	if criteria.TimeRange != nil {
		if point.Timestamp.Before(criteria.TimeRange.Start) || point.Timestamp.After(criteria.TimeRange.End) {
			return false
		}
	}

	// Check value threshold
	if criteria.ValueThreshold != nil {
		if criteria.ValueThreshold.Min != nil && point.Value < *criteria.ValueThreshold.Min {
			return false
		}
		if criteria.ValueThreshold.Max != nil && point.Value > *criteria.ValueThreshold.Max {
			return false
		}
	}

	// Check tag filters
	for key, value := range criteria.TagFilters {
		if pointValue, exists := point.Tags[key]; !exists || pointValue != value {
			return false
		}
	}

	return true
}

// updateSuccessStats updates statistics for successful exports
func (em *ExportManager) updateSuccessStats(startTime time.Time) {
	em.mu.Lock()
	defer em.mu.Unlock()

	duration := time.Since(startTime)

	em.stats.TotalExports++
	em.stats.SuccessfulExports++
	em.stats.LastExportTime = time.Now()

	// Update average export time
	if em.stats.SuccessfulExports == 1 {
		em.stats.AverageExportTime = duration
	} else {
		// Calculate running average
		totalTime := em.stats.AverageExportTime * time.Duration(em.stats.SuccessfulExports-1)
		em.stats.AverageExportTime = (totalTime + duration) / time.Duration(em.stats.SuccessfulExports)
	}
}

// updateFailureStats updates statistics for failed exports
func (em *ExportManager) updateFailureStats() {
	em.mu.Lock()
	defer em.mu.Unlock()

	em.stats.TotalExports++
	em.stats.FailedExports++
}

// updateRetryStats updates statistics for retry attempts
func (em *ExportManager) updateRetryStats() {
	em.mu.Lock()
	defer em.mu.Unlock()

	em.stats.RetryAttempts++
}
