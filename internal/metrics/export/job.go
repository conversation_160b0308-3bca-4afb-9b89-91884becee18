package export

import (
	"fmt"
	"strings"
	"sync"
	"time"
)

// JobStatus represents the current status of an export job
type JobStatus string

const (
	JobStatusActive    JobStatus = "active"
	JobStatusPaused    JobStatus = "paused"
	JobStatusCompleted JobStatus = "completed"
	JobStatusFailed    JobStatus = "failed"
	JobStatusCancelled JobStatus = "cancelled"
)

// ScheduleType represents different types of scheduling
type ScheduleType string

const (
	ScheduleTypeInterval ScheduleType = "interval"
	ScheduleTypeCron     ScheduleType = "cron"
	ScheduleTypeOneTime  ScheduleType = "one_time"
)

// ExportDestination represents where export output should be sent
type ExportDestination struct {
	Type     string            `json:"type"`              // file, http, stdout
	Location string            `json:"location"`          // file path, HTTP URL, etc.
	Headers  map[string]string `json:"headers,omitempty"` // For HTTP destinations
	Auth     *AuthConfig       `json:"auth,omitempty"`    // Authentication config
}

// AuthConfig represents authentication configuration for remote destinations
type AuthConfig struct {
	Type     string            `json:"type"` // basic, bearer, api_key
	Username string            `json:"username,omitempty"`
	Password string            `json:"password,omitempty"`
	Token    string            `json:"token,omitempty"`
	Headers  map[string]string `json:"headers,omitempty"`
}

// Schedule represents the scheduling configuration for an export job
type Schedule struct {
	Type      ScheduleType  `json:"type"`
	Interval  time.Duration `json:"interval,omitempty"`   // For interval scheduling
	CronExpr  string        `json:"cron_expr,omitempty"`  // For cron scheduling
	StartTime *time.Time    `json:"start_time,omitempty"` // When to start the schedule
	EndTime   *time.Time    `json:"end_time,omitempty"`   // When to end the schedule
	RunOnce   bool          `json:"run_once"`             // For one-time jobs
}

// ExportJob represents a scheduled export job configuration
type ExportJob struct {
	ID              string            `json:"id"`
	Name            string            `json:"name"`
	Description     string            `json:"description,omitempty"`
	Status          JobStatus         `json:"status"`
	Format          ExportFormat      `json:"format"`
	Destination     ExportDestination `json:"destination"`
	Schedule        Schedule          `json:"schedule"`
	FilterCriteria  *FilterCriteria   `json:"filter_criteria,omitempty"`
	IncludeMetadata bool              `json:"include_metadata"`
	Compression     bool              `json:"compression"`
	RetryConfig     *RetryConfig      `json:"retry_config,omitempty"`

	// Runtime fields
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
	LastRunAt    *time.Time `json:"last_run_at,omitempty"`
	NextRunAt    *time.Time `json:"next_run_at,omitempty"`
	RunCount     int64      `json:"run_count"`
	SuccessCount int64      `json:"success_count"`
	FailureCount int64      `json:"failure_count"`

	// Internal fields
	mu        *sync.RWMutex `json:"-"`
	ticker    *time.Ticker  `json:"-"`
	stopChan  chan struct{} `json:"-"`
	lastError error         `json:"-"`
}

// JobExecution represents a single execution of an export job
type JobExecution struct {
	JobID           string        `json:"job_id"`
	ExecutionID     string        `json:"execution_id"`
	StartTime       time.Time     `json:"start_time"`
	EndTime         *time.Time    `json:"end_time,omitempty"`
	Status          JobStatus     `json:"status"`
	Duration        time.Duration `json:"duration"`
	BytesExported   int64         `json:"bytes_exported"`
	RecordsExported int64         `json:"records_exported"`
	Error           string        `json:"error,omitempty"`
	RetryAttempt    int           `json:"retry_attempt"`
}

// JobStats represents statistics for an export job
type JobStats struct {
	JobID                string        `json:"job_id"`
	JobName              string        `json:"job_name"`
	Status               JobStatus     `json:"status"`
	CreatedAt            time.Time     `json:"created_at"`
	LastRunAt            *time.Time    `json:"last_run_at,omitempty"`
	NextRunAt            *time.Time    `json:"next_run_at,omitempty"`
	RunCount             int64         `json:"run_count"`
	SuccessCount         int64         `json:"success_count"`
	FailureCount         int64         `json:"failure_count"`
	TotalExecutions      int           `json:"total_executions"`
	AverageExecutionTime time.Duration `json:"average_execution_time"`
	TotalBytesExported   int64         `json:"total_bytes_exported"`
	TotalRecordsExported int64         `json:"total_records_exported"`
}

// JobManager manages export jobs
type JobManager struct {
	jobs                map[string]*ExportJob
	executions          map[string][]*JobExecution // Job ID -> executions
	mu                  sync.RWMutex
	exportManager       *ExportManager
	maxExecutionHistory int
}

// NewJobManager creates a new job manager
func NewJobManager(exportManager *ExportManager) *JobManager {
	return &JobManager{
		jobs:                make(map[string]*ExportJob),
		executions:          make(map[string][]*JobExecution),
		exportManager:       exportManager,
		maxExecutionHistory: 100, // Keep last 100 executions per job
	}
}

// CreateJob creates a new export job
func (jm *JobManager) CreateJob(job *ExportJob) error {
	if job.ID == "" {
		return fmt.Errorf("job ID cannot be empty")
	}

	if job.Name == "" {
		return fmt.Errorf("job name cannot be empty")
	}

	// Validate the job configuration
	if err := jm.validateJob(job); err != nil {
		return fmt.Errorf("invalid job configuration: %w", err)
	}

	jm.mu.Lock()
	defer jm.mu.Unlock()

	// Check if job already exists
	if _, exists := jm.jobs[job.ID]; exists {
		return fmt.Errorf("job with ID %s already exists", job.ID)
	}

	// Set creation time and status
	now := time.Now()
	job.CreatedAt = now
	job.UpdatedAt = now
	if job.Status == "" {
		job.Status = JobStatusActive
	}

	// Initialize internal fields
	job.stopChan = make(chan struct{})
	job.mu = &sync.RWMutex{}

	// Calculate next run time
	if err := jm.calculateNextRunTime(job); err != nil {
		return fmt.Errorf("failed to calculate next run time: %w", err)
	}

	// Store the job
	jm.jobs[job.ID] = job
	jm.executions[job.ID] = make([]*JobExecution, 0)

	return nil
}

// GetJob retrieves a job by ID
func (jm *JobManager) GetJob(jobID string) (*ExportJob, error) {
	jm.mu.RLock()
	defer jm.mu.RUnlock()

	job, exists := jm.jobs[jobID]
	if !exists {
		return nil, fmt.Errorf("job with ID %s not found", jobID)
	}

	// Return a copy to prevent external modification
	jobCopy := *job
	return &jobCopy, nil
}

// ListJobs returns all jobs, optionally filtered by status
func (jm *JobManager) ListJobs(status JobStatus) []*ExportJob {
	jm.mu.RLock()
	defer jm.mu.RUnlock()

	var jobs []*ExportJob
	for _, job := range jm.jobs {
		if status == "" || job.Status == status {
			// Return a copy to prevent external modification
			jobCopy := *job
			jobs = append(jobs, &jobCopy)
		}
	}

	return jobs
}

// DeleteJob removes a job
func (jm *JobManager) DeleteJob(jobID string) error {
	jm.mu.Lock()
	defer jm.mu.Unlock()

	job, exists := jm.jobs[jobID]
	if !exists {
		return fmt.Errorf("job with ID %s not found", jobID)
	}

	// Stop the job if it's running
	job.mu.Lock()
	if job.ticker != nil {
		job.ticker.Stop()
	}
	close(job.stopChan)
	job.mu.Unlock()

	// Remove from storage
	delete(jm.jobs, jobID)
	delete(jm.executions, jobID)

	return nil
}

// validateJob validates a job configuration
func (jm *JobManager) validateJob(job *ExportJob) error {
	if job.Name == "" {
		return fmt.Errorf("job name is required")
	}

	if job.Format == "" {
		return fmt.Errorf("export format is required")
	}

	// Validate format is supported
	supportedFormats := jm.exportManager.GetRegisteredFormats()
	formatSupported := false
	for _, format := range supportedFormats {
		if format == job.Format {
			formatSupported = true
			break
		}
	}
	if !formatSupported {
		return fmt.Errorf("export format %s is not supported", job.Format)
	}

	// Validate destination
	if job.Destination.Type == "" {
		return fmt.Errorf("destination type is required")
	}
	if job.Destination.Location == "" {
		return fmt.Errorf("destination location is required")
	}

	// Validate schedule
	if err := jm.validateSchedule(&job.Schedule); err != nil {
		return fmt.Errorf("invalid schedule: %w", err)
	}

	return nil
}

// validateSchedule validates a schedule configuration
func (jm *JobManager) validateSchedule(schedule *Schedule) error {
	switch schedule.Type {
	case ScheduleTypeInterval:
		if schedule.Interval <= 0 {
			return fmt.Errorf("interval must be positive")
		}
	case ScheduleTypeCron:
		if schedule.CronExpr == "" {
			return fmt.Errorf("cron expression is required for cron schedule")
		}
		// Basic cron validation (could be enhanced with a proper cron parser)
		if !jm.isValidCronExpression(schedule.CronExpr) {
			return fmt.Errorf("invalid cron expression: %s", schedule.CronExpr)
		}
	case ScheduleTypeOneTime:
		if schedule.StartTime == nil {
			return fmt.Errorf("start time is required for one-time schedule")
		}
		if schedule.StartTime.Before(time.Now()) {
			return fmt.Errorf("start time must be in the future for one-time schedule")
		}
	default:
		return fmt.Errorf("unsupported schedule type: %s", schedule.Type)
	}

	return nil
}

// isValidCronExpression performs basic validation of cron expressions
func (jm *JobManager) isValidCronExpression(expr string) bool {
	// Basic validation: should have 5 or 6 space-separated fields
	fields := strings.Fields(expr)
	return len(fields) == 5 || len(fields) == 6
}

// calculateNextRunTime calculates the next execution time for a job
func (jm *JobManager) calculateNextRunTime(job *ExportJob) error {
	now := time.Now()

	switch job.Schedule.Type {
	case ScheduleTypeInterval:
		if job.LastRunAt == nil {
			// First run - start immediately or at specified start time
			if job.Schedule.StartTime != nil && job.Schedule.StartTime.After(now) {
				job.NextRunAt = job.Schedule.StartTime
			} else {
				job.NextRunAt = &now
			}
		} else {
			// Calculate next run based on last run + interval
			nextRun := job.LastRunAt.Add(job.Schedule.Interval)
			job.NextRunAt = &nextRun
		}

	case ScheduleTypeCron:
		// For cron scheduling, we would need a proper cron parser
		// For now, implement a simple fallback
		if job.Schedule.StartTime != nil && job.Schedule.StartTime.After(now) {
			job.NextRunAt = job.Schedule.StartTime
		} else {
			// Fallback: run every hour
			nextRun := now.Add(time.Hour)
			job.NextRunAt = &nextRun
		}

	case ScheduleTypeOneTime:
		if job.LastRunAt == nil {
			job.NextRunAt = job.Schedule.StartTime
		} else {
			// One-time job already executed
			job.NextRunAt = nil
			job.Status = JobStatusCompleted
		}
	}

	// Check if we've passed the end time
	if job.Schedule.EndTime != nil && job.NextRunAt != nil && job.NextRunAt.After(*job.Schedule.EndTime) {
		job.NextRunAt = nil
		job.Status = JobStatusCompleted
	}

	return nil
}

// recordExecution records the result of a job execution
func (jm *JobManager) recordExecution(execution *JobExecution) {
	jm.mu.Lock()
	defer jm.mu.Unlock()

	executions := jm.executions[execution.JobID]
	executions = append(executions, execution)

	// Trim history if it exceeds the limit
	if len(executions) > jm.maxExecutionHistory {
		executions = executions[1:]
	}

	jm.executions[execution.JobID] = executions
}

// GetJobStats returns statistics for a job
func (jm *JobManager) GetJobStats(jobID string) (*JobStats, error) {
	jm.mu.RLock()
	defer jm.mu.RUnlock()

	job, exists := jm.jobs[jobID]
	if !exists {
		return nil, fmt.Errorf("job with ID %s not found", jobID)
	}

	executions := jm.executions[jobID]

	stats := &JobStats{
		JobID:           jobID,
		JobName:         job.Name,
		Status:          job.Status,
		CreatedAt:       job.CreatedAt,
		LastRunAt:       job.LastRunAt,
		NextRunAt:       job.NextRunAt,
		RunCount:        job.RunCount,
		SuccessCount:    job.SuccessCount,
		FailureCount:    job.FailureCount,
		TotalExecutions: len(executions),
	}

	// Calculate additional statistics from executions
	if len(executions) > 0 {
		var totalDuration time.Duration
		var totalBytes int64
		var totalRecords int64

		for _, exec := range executions {
			totalDuration += exec.Duration
			totalBytes += exec.BytesExported
			totalRecords += exec.RecordsExported
		}

		stats.AverageExecutionTime = totalDuration / time.Duration(len(executions))
		stats.TotalBytesExported = totalBytes
		stats.TotalRecordsExported = totalRecords
	}

	return stats, nil
}
