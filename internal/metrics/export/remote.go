// Package export provides remote endpoint export functionality with retry logic
package export

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/url"
	"strings"
	"sync"
	"time"

	"neuralmetergo/internal/client"
)

// RemoteEndpointType defines the type of remote endpoint
type RemoteEndpointType string

const (
	EndpointTypeHTTP       RemoteEndpointType = "http"
	EndpointTypeHTTPS      RemoteEndpointType = "https"
	EndpointTypeWebhook    RemoteEndpointType = "webhook"
	EndpointTypePrometheus RemoteEndpointType = "prometheus"
	EndpointTypeInfluxDB   RemoteEndpointType = "influxdb"
	EndpointTypeCustom     RemoteEndpointType = "custom"
)

// RemoteEndpointConfig defines configuration for remote endpoint exports
type RemoteEndpointConfig struct {
	// Endpoint details
	URL    string             `json:"url"`
	Type   RemoteEndpointType `json:"type"`
	Method string             `json:"method"` // HTTP method (GET, POST, PUT, PATCH)

	// Authentication
	AuthConfig *client.AuthConfig `json:"auth_config,omitempty"`

	// Headers
	Headers map[string]string `json:"headers,omitempty"`

	// Retry configuration
	RetryConfig *client.RetryConfig `json:"retry_config,omitempty"`

	// Timeout configuration
	RequestTimeout time.Duration `json:"request_timeout"`

	// Compression
	EnableCompression bool `json:"enable_compression"`

	// Batch configuration
	BatchSize    int           `json:"batch_size"`     // Number of metrics per request
	BatchTimeout time.Duration `json:"batch_timeout"`  // Max time to wait for batch to fill
	MaxBatchSize int           `json:"max_batch_size"` // Maximum batch size limit

	// Connection pooling
	MaxConcurrentRequests int `json:"max_concurrent_requests"`

	// Health check configuration
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	HealthCheckTimeout  time.Duration `json:"health_check_timeout"`
	HealthCheckPath     string        `json:"health_check_path,omitempty"`
}

// RemoteEndpointStats tracks statistics for remote endpoint exports
type RemoteEndpointStats struct {
	// Request statistics
	TotalRequests      int64 `json:"total_requests"`
	SuccessfulRequests int64 `json:"successful_requests"`
	FailedRequests     int64 `json:"failed_requests"`
	RetryAttempts      int64 `json:"retry_attempts"`

	// Performance metrics
	TotalBytesUploaded  int64         `json:"total_bytes_uploaded"`
	AverageResponseTime time.Duration `json:"average_response_time"`
	MinResponseTime     time.Duration `json:"min_response_time"`
	MaxResponseTime     time.Duration `json:"max_response_time"`

	// Batch statistics
	TotalBatches     int64 `json:"total_batches"`
	AverageBatchSize int   `json:"average_batch_size"`

	// Error tracking
	ConnectionErrors int64 `json:"connection_errors"`
	TimeoutErrors    int64 `json:"timeout_errors"`
	AuthErrors       int64 `json:"auth_errors"`
	ServerErrors     int64 `json:"server_errors"`

	// Health status
	LastHealthCheck   time.Time `json:"last_health_check"`
	HealthCheckStatus string    `json:"health_check_status"`
	EndpointAvailable bool      `json:"endpoint_available"`

	// Timestamps
	FirstRequestTime time.Time `json:"first_request_time"`
	LastRequestTime  time.Time `json:"last_request_time"`
	LastSuccessTime  time.Time `json:"last_success_time"`
	LastFailureTime  time.Time `json:"last_failure_time"`

	// Thread safety
	mu *sync.RWMutex `json:"-"`
}

// RemoteExporter handles exports to remote HTTP endpoints with retry logic
type RemoteExporter struct {
	config     RemoteEndpointConfig
	httpClient *client.HTTPClient
	stats      RemoteEndpointStats
	exporter   Exporter // Format-specific exporter

	// Batching support
	batchChan   chan *ExportData
	batchBuffer []*ExportData
	batchMu     sync.Mutex

	// Control channels
	ctx    context.Context
	cancel context.CancelFunc
	stopCh chan struct{}
	doneCh chan struct{}

	// Health monitoring
	healthTicker *time.Ticker

	mu sync.RWMutex
}

// NewRemoteExporter creates a new remote endpoint exporter
func NewRemoteExporter(config RemoteEndpointConfig, formatExporter Exporter) (*RemoteExporter, error) {
	if err := validateRemoteConfig(config); err != nil {
		return nil, fmt.Errorf("invalid remote endpoint config: %w", err)
	}

	// Create HTTP client with appropriate configuration
	clientConfig := &client.Config{
		MaxIdleConns:          10,
		MaxIdleConnsPerHost:   5,
		MaxConnsPerHost:       config.MaxConcurrentRequests,
		IdleConnTimeout:       30 * time.Second,
		DialTimeout:           10 * time.Second,
		ResponseHeaderTimeout: config.RequestTimeout,
		TLSHandshakeTimeout:   10 * time.Second,
		DisableKeepAlives:     false,
		DisableCompression:    !config.EnableCompression,
	}

	httpClient := client.NewHTTPClientWithRetry(clientConfig, config.RetryConfig)

	// Set authentication if provided
	if config.AuthConfig != nil {
		if err := httpClient.SetAuth(config.AuthConfig); err != nil {
			return nil, fmt.Errorf("failed to set authentication: %w", err)
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	exporter := &RemoteExporter{
		config:     config,
		httpClient: httpClient,
		exporter:   formatExporter,
		stats:      RemoteEndpointStats{mu: &sync.RWMutex{}},
		batchChan:  make(chan *ExportData, config.MaxBatchSize),
		stopCh:     make(chan struct{}),
		doneCh:     make(chan struct{}),
		ctx:        ctx,
		cancel:     cancel,
	}

	// Start batch processor if batching is enabled
	if config.BatchSize > 1 {
		go exporter.batchProcessor()
	}

	// Start health check monitoring if configured
	if config.HealthCheckInterval > 0 {
		exporter.healthTicker = time.NewTicker(config.HealthCheckInterval)
		go exporter.healthCheckMonitor()
	}

	return exporter, nil
}

// Export sends metrics data to the remote endpoint
func (re *RemoteExporter) Export(data *ExportData, writer io.Writer) error {
	// For remote exports, we ignore the writer parameter and send to the endpoint
	return re.ExportToEndpoint(data)
}

// ExportToEndpoint sends metrics data directly to the configured remote endpoint
func (re *RemoteExporter) ExportToEndpoint(data *ExportData) error {
	if data == nil {
		return fmt.Errorf("export data cannot be nil")
	}

	// Use batching if configured
	if re.config.BatchSize > 1 {
		return re.addToBatch(data)
	}

	// Send immediately
	return re.sendSingleExport(data)
}

// addToBatch adds data to the batch queue
func (re *RemoteExporter) addToBatch(data *ExportData) error {
	select {
	case re.batchChan <- data:
		return nil
	case <-re.ctx.Done():
		return fmt.Errorf("remote exporter stopped")
	default:
		return fmt.Errorf("batch queue full, cannot add export data")
	}
}

// sendSingleExport sends a single export to the remote endpoint
func (re *RemoteExporter) sendSingleExport(data *ExportData) error {
	startTime := time.Now()

	// Convert data to bytes using the format exporter
	var buffer bytes.Buffer
	if err := re.exporter.Export(data, &buffer); err != nil {
		re.updateErrorStats("format_error", startTime)
		return fmt.Errorf("failed to format export data: %w", err)
	}

	// Prepare HTTP request
	headers := make(map[string]string)

	// Set content type from exporter
	headers["Content-Type"] = re.exporter.GetContentType()

	// Add custom headers
	for key, value := range re.config.Headers {
		headers[key] = value
	}

	// Create HTTP request
	req := &client.Request{
		Method:  re.config.Method,
		URL:     re.config.URL,
		Headers: headers,
		Body:    buffer.Bytes(),
		Timeout: re.config.RequestTimeout,
	}

	// Send request with retry logic (handled by HTTP client)
	resp, err := re.httpClient.Execute(re.ctx, req)
	if err != nil {
		re.updateErrorStats("request_error", startTime)
		return fmt.Errorf("failed to send export request: %w", err)
	}

	// Check response status
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		re.updateErrorStats("http_error", startTime)
		return fmt.Errorf("remote endpoint returned error status: %d %s", resp.StatusCode, resp.Status)
	}

	// Update success statistics
	re.updateSuccessStats(startTime, int64(len(buffer.Bytes())))

	return nil
}

// batchProcessor handles batched exports
func (re *RemoteExporter) batchProcessor() {
	defer close(re.doneCh)

	batchTicker := time.NewTicker(re.config.BatchTimeout)
	defer batchTicker.Stop()

	for {
		select {
		case data := <-re.batchChan:
			re.addDataToBatch(data)

			// Check if batch is full
			if len(re.batchBuffer) >= re.config.BatchSize {
				re.processBatch()
			}

		case <-batchTicker.C:
			// Process batch on timeout if it has data
			if len(re.batchBuffer) > 0 {
				re.processBatch()
			}

		case <-re.stopCh:
			// Process remaining batch before stopping
			if len(re.batchBuffer) > 0 {
				re.processBatch()
			}
			return
		}
	}
}

// addDataToBatch adds data to the batch buffer
func (re *RemoteExporter) addDataToBatch(data *ExportData) {
	re.batchMu.Lock()
	defer re.batchMu.Unlock()

	re.batchBuffer = append(re.batchBuffer, data)
}

// processBatch processes and sends the current batch
func (re *RemoteExporter) processBatch() {
	re.batchMu.Lock()
	batch := make([]*ExportData, len(re.batchBuffer))
	copy(batch, re.batchBuffer)
	re.batchBuffer = re.batchBuffer[:0] // Clear buffer
	re.batchMu.Unlock()

	if len(batch) == 0 {
		return
	}

	// Combine batch data into a single export
	combinedData := re.combineBatchData(batch)

	// Send the combined batch
	if err := re.sendSingleExport(combinedData); err != nil {
		// Log error but don't fail - individual exports might still work
		fmt.Printf("Failed to send batch export: %v\n", err)
	}

	// Update batch statistics
	re.updateBatchStats(len(batch))
}

// combineBatchData combines multiple ExportData into a single one
func (re *RemoteExporter) combineBatchData(batch []*ExportData) *ExportData {
	if len(batch) == 0 {
		return nil
	}

	if len(batch) == 1 {
		return batch[0]
	}

	// Combine all metric series
	var allSeries []MetricSeries
	var latestTimestamp time.Time
	metadata := make(map[string]interface{})

	for _, data := range batch {
		allSeries = append(allSeries, data.MetricSeries...)

		if data.Timestamp.After(latestTimestamp) {
			latestTimestamp = data.Timestamp
		}

		// Merge metadata
		for key, value := range data.Metadata {
			metadata[key] = value
		}
	}

	return &ExportData{
		Timestamp:    latestTimestamp,
		Source:       batch[0].Source,
		Version:      batch[0].Version,
		Description:  fmt.Sprintf("Batched export of %d datasets", len(batch)),
		MetricSeries: allSeries,
		Metadata:     metadata,
	}
}

// healthCheckMonitor performs periodic health checks on the remote endpoint
func (re *RemoteExporter) healthCheckMonitor() {
	for {
		select {
		case <-re.healthTicker.C:
			re.performHealthCheck()
		case <-re.stopCh:
			return
		}
	}
}

// performHealthCheck checks the health of the remote endpoint
func (re *RemoteExporter) performHealthCheck() {
	startTime := time.Now()

	// Determine health check URL
	healthURL := re.config.URL
	if re.config.HealthCheckPath != "" {
		baseURL, err := url.Parse(re.config.URL)
		if err == nil {
			healthURL = fmt.Sprintf("%s://%s%s", baseURL.Scheme, baseURL.Host, re.config.HealthCheckPath)
		}
	}

	// Create health check request
	req := &client.Request{
		Method:  "HEAD", // Use HEAD for health checks
		URL:     healthURL,
		Headers: map[string]string{},
		Timeout: re.config.HealthCheckTimeout,
	}

	// Perform health check
	resp, err := re.httpClient.Execute(re.ctx, req)

	re.stats.mu.Lock()
	re.stats.LastHealthCheck = startTime

	if err != nil {
		re.stats.HealthCheckStatus = fmt.Sprintf("Failed: %v", err)
		re.stats.EndpointAvailable = false
	} else if resp.StatusCode >= 200 && resp.StatusCode < 400 {
		re.stats.HealthCheckStatus = "Healthy"
		re.stats.EndpointAvailable = true
	} else {
		re.stats.HealthCheckStatus = fmt.Sprintf("Unhealthy: HTTP %d", resp.StatusCode)
		re.stats.EndpointAvailable = false
	}
	re.stats.mu.Unlock()
}

// updateSuccessStats updates statistics for successful exports
func (re *RemoteExporter) updateSuccessStats(startTime time.Time, bytesUploaded int64) {
	duration := time.Since(startTime)

	re.stats.mu.Lock()
	defer re.stats.mu.Unlock()

	re.stats.TotalRequests++
	re.stats.SuccessfulRequests++
	re.stats.TotalBytesUploaded += bytesUploaded
	re.stats.LastSuccessTime = startTime
	re.stats.LastRequestTime = startTime

	if re.stats.FirstRequestTime.IsZero() {
		re.stats.FirstRequestTime = startTime
	}

	// Update response time statistics
	if re.stats.MinResponseTime == 0 || duration < re.stats.MinResponseTime {
		re.stats.MinResponseTime = duration
	}
	if duration > re.stats.MaxResponseTime {
		re.stats.MaxResponseTime = duration
	}

	// Update average response time
	totalTime := time.Duration(re.stats.SuccessfulRequests-1)*re.stats.AverageResponseTime + duration
	re.stats.AverageResponseTime = totalTime / time.Duration(re.stats.SuccessfulRequests)
}

// updateErrorStats updates statistics for failed exports
func (re *RemoteExporter) updateErrorStats(errorType string, startTime time.Time) {
	re.stats.mu.Lock()
	defer re.stats.mu.Unlock()

	re.stats.TotalRequests++
	re.stats.FailedRequests++
	re.stats.LastFailureTime = startTime
	re.stats.LastRequestTime = startTime

	if re.stats.FirstRequestTime.IsZero() {
		re.stats.FirstRequestTime = startTime
	}

	// Categorize errors
	switch {
	case strings.Contains(errorType, "connection") || strings.Contains(errorType, "network"):
		re.stats.ConnectionErrors++
	case strings.Contains(errorType, "timeout"):
		re.stats.TimeoutErrors++
	case strings.Contains(errorType, "auth"):
		re.stats.AuthErrors++
	case strings.Contains(errorType, "http") || strings.Contains(errorType, "server"):
		re.stats.ServerErrors++
	}
}

// updateBatchStats updates batch-related statistics
func (re *RemoteExporter) updateBatchStats(batchSize int) {
	re.stats.mu.Lock()
	defer re.stats.mu.Unlock()

	re.stats.TotalBatches++

	// Update average batch size
	totalItems := int64(re.stats.AverageBatchSize)*(re.stats.TotalBatches-1) + int64(batchSize)
	re.stats.AverageBatchSize = int(totalItems / re.stats.TotalBatches)
}

// GetStats returns current statistics
func (re *RemoteExporter) GetStats() RemoteEndpointStats {
	re.stats.mu.RLock()
	defer re.stats.mu.RUnlock()

	// Return a copy to avoid race conditions
	return re.stats
}

// GetContentType returns the content type from the underlying format exporter
func (re *RemoteExporter) GetContentType() string {
	return re.exporter.GetContentType()
}

// GetFileExtension returns the file extension from the underlying format exporter
func (re *RemoteExporter) GetFileExtension() string {
	return re.exporter.GetFileExtension()
}

// SupportsStreaming indicates if remote export supports streaming
func (re *RemoteExporter) SupportsStreaming() bool {
	return re.config.BatchSize > 1 // Streaming supported if batching is enabled
}

// Validate validates export data using the underlying format exporter
func (re *RemoteExporter) Validate(data *ExportData) error {
	return re.exporter.Validate(data)
}

// IsHealthy returns whether the remote endpoint is currently healthy
func (re *RemoteExporter) IsHealthy() bool {
	re.stats.mu.RLock()
	defer re.stats.mu.RUnlock()
	return re.stats.EndpointAvailable
}

// Stop gracefully stops the remote exporter
func (re *RemoteExporter) Stop() {
	re.cancel()
	close(re.stopCh)

	// Stop health check monitoring
	if re.healthTicker != nil {
		re.healthTicker.Stop()
	}

	// Wait for batch processor to finish
	if re.config.BatchSize > 1 {
		<-re.doneCh
	}

	// Close HTTP client
	re.httpClient.Close()
}

// validateRemoteConfig validates the remote endpoint configuration
func validateRemoteConfig(config RemoteEndpointConfig) error {
	if config.URL == "" {
		return fmt.Errorf("URL is required")
	}

	parsedURL, err := url.Parse(config.URL)
	if err != nil {
		return fmt.Errorf("invalid URL: %w", err)
	}

	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		return fmt.Errorf("URL scheme must be http or https")
	}

	if config.Method == "" {
		config.Method = "POST" // Default to POST
	}

	validMethods := []string{"GET", "POST", "PUT", "PATCH"}
	methodValid := false
	for _, method := range validMethods {
		if strings.ToUpper(config.Method) == method {
			methodValid = true
			break
		}
	}
	if !methodValid {
		return fmt.Errorf("invalid HTTP method: %s", config.Method)
	}

	if config.RequestTimeout == 0 {
		config.RequestTimeout = 30 * time.Second // Default timeout
	}

	if config.BatchSize < 1 {
		config.BatchSize = 1 // Default to no batching
	}

	if config.MaxBatchSize < config.BatchSize {
		config.MaxBatchSize = config.BatchSize * 2 // Default to double batch size
	}

	if config.BatchTimeout == 0 && config.BatchSize > 1 {
		config.BatchTimeout = 5 * time.Second // Default batch timeout
	}

	if config.MaxConcurrentRequests == 0 {
		config.MaxConcurrentRequests = 10 // Default concurrent requests
	}

	return nil
}

// DefaultRemoteConfig returns a default configuration for remote endpoints
func DefaultRemoteConfig(url string) RemoteEndpointConfig {
	return RemoteEndpointConfig{
		URL:                   url,
		Type:                  EndpointTypeHTTP,
		Method:                "POST",
		Headers:               make(map[string]string),
		RequestTimeout:        30 * time.Second,
		EnableCompression:     true,
		BatchSize:             1,
		BatchTimeout:          5 * time.Second,
		MaxBatchSize:          100,
		MaxConcurrentRequests: 10,
		HealthCheckInterval:   60 * time.Second,
		HealthCheckTimeout:    10 * time.Second,
	}
}
