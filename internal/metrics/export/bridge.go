// Package export provides a bridge between metrics infrastructure and export system
package export

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	"neuralmetergo/internal/metrics"
)

// MetricsBridge connects the metrics infrastructure with the export system
type MetricsBridge struct {
	integrator    *metrics.MetricsIntegrator
	exportManager *ExportManager
	config        BridgeConfig

	// Export jobs and scheduling
	exportJobs map[string]*ExportJob
	scheduler  *ExportScheduler

	// State management
	isRunning bool
	ctx       context.Context
	cancel    context.CancelFunc
	mu        sync.RWMutex
}

// BridgeConfig defines configuration for the metrics bridge
type BridgeConfig struct {
	// Data collection settings
	CollectionInterval time.Duration `json:"collection_interval"`
	IncludeMetadata    bool          `json:"include_metadata"`
	EnableBuffering    bool          `json:"enable_buffering"`
	BufferSize         int           `json:"buffer_size"`

	// Export settings
	DefaultFormat      ExportFormat                    `json:"default_format"`
	DefaultDestination string                          `json:"default_destination"`
	AutoExportInterval time.Duration                   `json:"auto_export_interval"`
	ExportConfigs      map[string]ExportConfig         `json:"export_configs"`
	RemoteConfigs      map[string]RemoteEndpointConfig `json:"remote_configs"`

	// Performance settings
	MaxConcurrentExports int           `json:"max_concurrent_exports"`
	ExportTimeout        time.Duration `json:"export_timeout"`
}

// DefaultBridgeConfig provides sensible defaults for the metrics bridge
func DefaultBridgeConfig() BridgeConfig {
	return BridgeConfig{
		CollectionInterval:   time.Second * 30,
		IncludeMetadata:      true,
		EnableBuffering:      true,
		BufferSize:           1000,
		DefaultFormat:        FormatJSON,
		DefaultDestination:   "./exports",
		AutoExportInterval:   time.Minute * 5,
		ExportConfigs:        make(map[string]ExportConfig),
		RemoteConfigs:        make(map[string]RemoteEndpointConfig),
		MaxConcurrentExports: 5,
		ExportTimeout:        time.Minute * 2,
	}
}

// NewMetricsBridge creates a new metrics bridge
func NewMetricsBridge(integrator *metrics.MetricsIntegrator, exportManager *ExportManager, config BridgeConfig) *MetricsBridge {
	ctx, cancel := context.WithCancel(context.Background())

	bridge := &MetricsBridge{
		integrator:    integrator,
		exportManager: exportManager,
		config:        config,
		exportJobs:    make(map[string]*ExportJob),
		ctx:           ctx,
		cancel:        cancel,
	}

	// Initialize job manager first
	jobManager := NewJobManager(exportManager)

	// Initialize scheduler
	schedulerConfig := &SchedulerConfig{
		Workers:       config.MaxConcurrentExports,
		CheckInterval: time.Second * 10,
		MaxRetries:    3,
		Logger:        nil, // Will use default logger
	}
	bridge.scheduler = NewExportScheduler(jobManager, exportManager, schedulerConfig)

	return bridge
}

// Start starts the metrics bridge
func (mb *MetricsBridge) Start() error {
	mb.mu.Lock()
	defer mb.mu.Unlock()

	if mb.isRunning {
		return nil
	}

	// Start the scheduler
	if err := mb.scheduler.Start(mb.ctx); err != nil {
		return fmt.Errorf("failed to start export scheduler: %w", err)
	}

	mb.isRunning = true
	return nil
}

// Stop stops the metrics bridge
func (mb *MetricsBridge) Stop() error {
	mb.mu.Lock()
	defer mb.mu.Unlock()

	if !mb.isRunning {
		return nil
	}

	// Stop the scheduler
	if mb.scheduler != nil {
		mb.scheduler.Stop()
	}

	mb.cancel()
	mb.isRunning = false
	return nil
}

// CollectAndExportAll collects all current metrics and exports them
func (mb *MetricsBridge) CollectAndExportAll(format ExportFormat, destination string) error {
	if !mb.isRunning {
		return fmt.Errorf("metrics bridge is not running")
	}

	// Collect current metrics data
	exportData, err := mb.CollectMetricsData()
	if err != nil {
		return fmt.Errorf("failed to collect metrics data: %w", err)
	}

	// Create export configuration
	config := ExportConfig{
		Format:          format,
		Destination:     destination,
		IncludeMetadata: mb.config.IncludeMetadata,
		BatchSize:       mb.config.BufferSize,
	}

	// Export using the export manager
	return mb.ExportData(exportData, config)
}

// CollectMetricsData collects all current metrics and converts them to export format
func (mb *MetricsBridge) CollectMetricsData() (*ExportData, error) {
	if mb.integrator == nil {
		return nil, fmt.Errorf("metrics integrator not available")
	}

	timestamp := time.Now()
	var metricSeries []MetricSeries

	// Get all available metrics
	allMetrics := mb.integrator.GetAllMetrics()

	for _, metricName := range allMetrics {
		series, err := mb.collectMetricSeries(metricName)
		if err != nil {
			// Log the error but continue with other metrics
			fmt.Printf("Failed to collect metric %s: %v\n", metricName, err)
			continue
		}
		if series != nil {
			metricSeries = append(metricSeries, *series)
		}
	}

	// Get collection statistics if available
	metadata := make(map[string]interface{})
	if collectionStats := mb.integrator.GetCollectionStats(); collectionStats != nil {
		metadata["collection_stats"] = collectionStats
	}

	exportData := &ExportData{
		Timestamp:    timestamp,
		Source:       "neuralmetergo",
		Version:      "1.0.0",
		Description:  fmt.Sprintf("Metrics export from %d series", len(metricSeries)),
		MetricSeries: metricSeries,
		Metadata:     metadata,
	}

	return exportData, nil
}

// collectMetricSeries collects data for a specific metric and converts it to MetricSeries
func (mb *MetricsBridge) collectMetricSeries(metricName string) (*MetricSeries, error) {
	series := MetricSeries{
		Name: metricName,
		Help: fmt.Sprintf("Metrics for %s", metricName),
		Type: "gauge", // Default type, can be enhanced later
		Unit: "",
	}

	var dataPoints []MetricDataPoint

	// Try to get sliding window statistics
	if slidingStats, err := mb.integrator.GetSlidingWindowStats(metricName); err == nil && slidingStats != nil {
		dataPoints = append(dataPoints, mb.convertSlidingWindowToDataPoints(metricName, slidingStats)...)
	}

	// Try to get statistical summary
	if statSummary, err := mb.integrator.GetStatisticalSummary(metricName); err == nil && statSummary != nil {
		dataPoints = append(dataPoints, mb.convertStatSummaryToDataPoints(metricName, statSummary)...)
	}

	// Try to get basic aggregated values
	if basicValues := mb.integrator.GetBasicAggregatedValues(metricName); len(basicValues) > 0 {
		dataPoints = append(dataPoints, mb.convertBasicValuesToDataPoints(metricName, basicValues)...)
	}

	// If no data points were collected, skip this metric
	if len(dataPoints) == 0 {
		return nil, nil
	}

	series.DataPoints = dataPoints
	return &series, nil
}

// convertSlidingWindowToDataPoints converts sliding window statistics to data points
func (mb *MetricsBridge) convertSlidingWindowToDataPoints(metricName string, stats *metrics.SlidingWindowStatistics) []MetricDataPoint {
	timestamp := time.Now()
	var points []MetricDataPoint

	// Add various statistical measures as separate data points
	if stats.Count > 0 {
		points = append(points, MetricDataPoint{
			Name:      metricName + "_count",
			Value:     float64(stats.Count),
			Timestamp: timestamp,
			Tags:      map[string]string{"stat": "count", "source": "sliding_window"},
		})
	}

	if stats.Sum != 0 {
		points = append(points, MetricDataPoint{
			Name:      metricName + "_sum",
			Value:     stats.Sum,
			Timestamp: timestamp,
			Tags:      map[string]string{"stat": "sum", "source": "sliding_window"},
		})
	}

	if stats.Mean != 0 {
		points = append(points, MetricDataPoint{
			Name:      metricName + "_mean",
			Value:     stats.Mean,
			Timestamp: timestamp,
			Tags:      map[string]string{"stat": "mean", "source": "sliding_window"},
		})
	}

	if stats.Min != 0 {
		points = append(points, MetricDataPoint{
			Name:      metricName + "_min",
			Value:     stats.Min,
			Timestamp: timestamp,
			Tags:      map[string]string{"stat": "min", "source": "sliding_window"},
		})
	}

	if stats.Max != 0 {
		points = append(points, MetricDataPoint{
			Name:      metricName + "_max",
			Value:     stats.Max,
			Timestamp: timestamp,
			Tags:      map[string]string{"stat": "max", "source": "sliding_window"},
		})
	}

	return points
}

// convertStatSummaryToDataPoints converts statistical summary to data points
func (mb *MetricsBridge) convertStatSummaryToDataPoints(metricName string, summary *metrics.StatisticalSummary) []MetricDataPoint {
	timestamp := time.Now()
	var points []MetricDataPoint

	// Convert various statistical measures
	statMetrics := map[string]float64{
		"count":              float64(summary.Count),
		"sum":                summary.Sum,
		"mean":               summary.Mean,
		"median":             summary.Median,
		"min":                summary.Min,
		"max":                summary.Max,
		"range":              summary.Range,
		"variance":           summary.Variance,
		"standard_deviation": summary.StandardDev,
		"skewness":           summary.Skewness,
		"kurtosis":           summary.Kurtosis,
		"q1":                 summary.Q1,
		"q3":                 summary.Q3,
		"iqr":                summary.IQR,
	}

	for statName, value := range statMetrics {
		if value != 0 || statName == "count" { // Always include count even if zero
			points = append(points, MetricDataPoint{
				Name:      fmt.Sprintf("%s_%s", metricName, statName),
				Value:     value,
				Timestamp: timestamp,
				Tags:      map[string]string{"stat": statName, "source": "statistical_summary"},
			})
		}
	}

	// Add outlier information
	if summary.HasOutliers {
		points = append(points, MetricDataPoint{
			Name:      metricName + "_outlier_count",
			Value:     float64(summary.OutlierCount),
			Timestamp: timestamp,
			Tags:      map[string]string{"stat": "outlier_count", "source": "statistical_summary"},
		})
	}

	return points
}

// convertBasicValuesToDataPoints converts basic aggregated values to data points
func (mb *MetricsBridge) convertBasicValuesToDataPoints(metricName string, values []metrics.AggregatedValue) []MetricDataPoint {
	var points []MetricDataPoint

	for _, value := range values {
		point := MetricDataPoint{
			Name:      metricName,
			Value:     value.Value,
			Timestamp: value.Timestamp,
			Tags:      map[string]string{"source": "basic_aggregator"},
		}

		// Add any additional tags from the aggregated value
		if value.Tags != nil {
			for k, v := range value.Tags {
				point.Tags[k] = v
			}
		}

		points = append(points, point)
	}

	return points
}

// ExportData exports data using the configured export manager
func (mb *MetricsBridge) ExportData(data *ExportData, config ExportConfig) error {
	if mb.exportManager == nil {
		return fmt.Errorf("export manager not available")
	}

	// For file exports, handle the destination
	if config.Destination != "" && config.Destination != "./exports" {
		fmt.Printf("Exporting to destination: %s\n", config.Destination)

		// Create or open the destination file
		file, err := os.Create(config.Destination)
		if err != nil {
			return fmt.Errorf("failed to create destination file: %w", err)
		}
		defer file.Close()

		// Use export manager to perform the export with the file writer
		return mb.exportManager.Export(data, config, file)
	}

	// For other destinations, use a buffer as a fallback
	var buffer bytes.Buffer
	return mb.exportManager.Export(data, config, &buffer)
}

// ScheduleExport schedules a recurring export job
func (mb *MetricsBridge) ScheduleExport(jobName string, schedule Schedule, config ExportConfig) error {
	if !mb.isRunning {
		return fmt.Errorf("metrics bridge is not running")
	}

	// Create export job
	job := &ExportJob{
		ID:              jobName,
		Name:            jobName,
		Description:     fmt.Sprintf("Scheduled export job for %s format", config.Format),
		Schedule:        schedule,
		Format:          config.Format,
		Status:          JobStatusActive,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
		IncludeMetadata: config.IncludeMetadata,
		Destination: ExportDestination{
			Type:     "file",
			Location: config.Destination,
		},
	}

	// Add the job to our tracking
	mb.mu.Lock()
	mb.exportJobs[jobName] = job
	mb.mu.Unlock()

	// Create the job in the job manager
	// Note: The scheduler doesn't have ScheduleJob method, we use the job manager
	if mb.scheduler != nil && mb.scheduler.jobManager != nil {
		return mb.scheduler.jobManager.CreateJob(job)
	}
	return fmt.Errorf("job manager not available")
}

// GetExportJobs returns all scheduled export jobs
func (mb *MetricsBridge) GetExportJobs() map[string]*ExportJob {
	mb.mu.RLock()
	defer mb.mu.RUnlock()

	// Return a copy to prevent race conditions
	jobs := make(map[string]*ExportJob)
	for k, v := range mb.exportJobs {
		jobs[k] = v
	}
	return jobs
}

// RemoveExportJob removes a scheduled export job
func (mb *MetricsBridge) RemoveExportJob(jobName string) error {
	mb.mu.Lock()
	defer mb.mu.Unlock()

	delete(mb.exportJobs, jobName)
	// Remove from job manager
	if mb.scheduler != nil && mb.scheduler.jobManager != nil {
		return mb.scheduler.jobManager.DeleteJob(jobName)
	}
	return fmt.Errorf("job manager not available")
}

// GetExportStats returns statistics about exports
func (mb *MetricsBridge) GetExportStats() ExportStats {
	if mb.exportManager != nil {
		return mb.exportManager.GetStats()
	}
	return ExportStats{}
}

// IsRunning returns whether the bridge is currently running
func (mb *MetricsBridge) IsRunning() bool {
	mb.mu.RLock()
	defer mb.mu.RUnlock()
	return mb.isRunning
}
