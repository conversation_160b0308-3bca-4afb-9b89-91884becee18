// Package metrics provides metrics collection and reporting functionality
package metrics

import (
	"errors"
	"math"
	"sort"
	"sync"
	"sync/atomic"
	"time"
)

// Counter represents a thread-safe counter using atomic operations
type Counter struct {
	value int64
}

// NewCounter creates a new Counter initialized to zero
func NewCounter() *Counter {
	return &Counter{value: 0}
}

// Add atomically adds the given delta to the counter value
func (c *Counter) Add(delta int64) {
	atomic.AddInt64(&c.value, delta)
}

// Inc atomically increments the counter by 1
func (c *Counter) Inc() {
	c.Add(1)
}

// Value atomically returns the current counter value
func (c *Counter) Value() int64 {
	return atomic.LoadInt64(&c.value)
}

// Reset atomically resets the counter to zero
func (c *Counter) Reset() {
	atomic.StoreInt64(&c.value, 0)
}

// Gauge represents a thread-safe gauge using atomic operations for float64 values
type Gauge struct {
	value uint64 // Stores float64 as uint64 for atomic operations
}

// Common errors for Gauge operations
var (
	ErrNaNValue = errors.New("gauge: NaN values are not allowed")
	ErrInfValue = errors.New("gauge: infinite values are not allowed")
)

// NewGauge creates a new Gauge initialized to zero
func NewGauge() *Gauge {
	return &Gauge{value: 0} // 0.0 as uint64 is also 0
}

// Set atomically sets the gauge to the given value
// Returns an error if the value is NaN or infinite
func (g *Gauge) Set(val float64) error {
	if math.IsNaN(val) {
		return ErrNaNValue
	}
	if math.IsInf(val, 0) {
		return ErrInfValue
	}
	atomic.StoreUint64(&g.value, math.Float64bits(val))
	return nil
}

// Add atomically adds the given delta to the gauge value
// Returns an error if the delta is NaN/infinite or if the result would be NaN/infinite
func (g *Gauge) Add(delta float64) error {
	if math.IsNaN(delta) {
		return ErrNaNValue
	}
	if math.IsInf(delta, 0) {
		return ErrInfValue
	}

	for {
		oldBits := atomic.LoadUint64(&g.value)
		oldVal := math.Float64frombits(oldBits)
		newVal := oldVal + delta

		// Check if result is valid
		if math.IsNaN(newVal) {
			return ErrNaNValue
		}
		if math.IsInf(newVal, 0) {
			return ErrInfValue
		}

		newBits := math.Float64bits(newVal)
		if atomic.CompareAndSwapUint64(&g.value, oldBits, newBits) {
			return nil
		}
		// CAS failed, retry
	}
}

// Sub atomically subtracts the given delta from the gauge value
// Returns an error if the delta is NaN/infinite or if the result would be NaN/infinite
func (g *Gauge) Sub(delta float64) error {
	return g.Add(-delta)
}

// Value atomically returns the current gauge value
func (g *Gauge) Value() float64 {
	return math.Float64frombits(atomic.LoadUint64(&g.value))
}

// Reset atomically resets the gauge to zero
func (g *Gauge) Reset() {
	atomic.StoreUint64(&g.value, 0) // 0.0 as uint64 is 0
}

// Histogram represents a thread-safe histogram using atomic operations for high-throughput scenarios
type Histogram struct {
	counts     [numFixedBuckets]uint64  // Fixed buckets for common ranges
	boundaries [numFixedBuckets]float64 // Bucket boundaries (sorted)
	outliers   sync.Map                 // Dynamic buckets for outliers
	totalCount uint64                   // Total observations
	sum        uint64                   // Sum as uint64 (atomic float64)
	_          [56]byte                 // Padding to avoid false sharing
}

// Number of fixed buckets optimized for load testing scenarios
const numFixedBuckets = 15

// Default bucket boundaries optimized for HTTP response times (in seconds)
var defaultBuckets = []float64{
	0.0001,      // 100μs
	0.0005,      // 500μs
	0.001,       // 1ms
	0.005,       // 5ms
	0.01,        // 10ms
	0.025,       // 25ms
	0.05,        // 50ms
	0.1,         // 100ms
	0.25,        // 250ms
	0.5,         // 500ms
	1.0,         // 1s
	2.5,         // 2.5s
	5.0,         // 5s
	10.0,        // 10s
	math.Inf(1), // +Inf
}

// NewHistogram creates a new Histogram with default buckets optimized for load testing
func NewHistogram() *Histogram {
	return NewHistogramWithBuckets(defaultBuckets)
}

// NewHistogramWithBuckets creates a new Histogram with custom bucket boundaries
func NewHistogramWithBuckets(buckets []float64) *Histogram {
	if len(buckets) != numFixedBuckets {
		// For simplicity, we'll use default buckets if wrong size is provided
		buckets = defaultBuckets
	}

	h := &Histogram{}

	// Copy and ensure buckets are sorted
	copy(h.boundaries[:], buckets)
	sort.Float64s(h.boundaries[:])

	return h
}

// Observe records a new observation in the histogram
// This is the hot path and is optimized for performance
func (h *Histogram) Observe(value float64) {
	if math.IsNaN(value) || math.IsInf(value, 0) {
		return // Silently ignore invalid values
	}

	// Update total count
	atomic.AddUint64(&h.totalCount, 1)

	// Update sum using atomic float64 operations (same as Gauge)
	for {
		oldBits := atomic.LoadUint64(&h.sum)
		oldVal := math.Float64frombits(oldBits)
		newVal := oldVal + value

		// Check if result is valid
		if math.IsNaN(newVal) || math.IsInf(newVal, 0) {
			break // Don't update sum if result would be invalid
		}

		newBits := math.Float64bits(newVal)
		if atomic.CompareAndSwapUint64(&h.sum, oldBits, newBits) {
			break
		}
	}

	// Find the appropriate bucket using binary search
	bucketIndex := h.findBucketIndex(value)

	if bucketIndex < numFixedBuckets {
		// Fast path: increment fixed bucket
		atomic.AddUint64(&h.counts[bucketIndex], 1)
	} else {
		// Slow path: handle outlier (value > largest fixed bucket)
		key := math.Float64bits(value)
		if count, loaded := h.outliers.LoadOrStore(key, uint64(1)); loaded {
			// Increment existing outlier count
			for {
				oldCount := count.(uint64)
				if h.outliers.CompareAndSwap(key, oldCount, oldCount+1) {
					break
				}
				if newCount, exists := h.outliers.Load(key); exists {
					count = newCount
				} else {
					break
				}
			}
		}
	}
}

// findBucketIndex finds the appropriate bucket index for a value using binary search
func (h *Histogram) findBucketIndex(value float64) int {
	// Binary search for the bucket
	left, right := 0, numFixedBuckets-1

	for left <= right {
		mid := (left + right) / 2
		if value <= h.boundaries[mid] {
			if mid == 0 || value > h.boundaries[mid-1] {
				return mid
			}
			right = mid - 1
		} else {
			left = mid + 1
		}
	}

	// Value is larger than all fixed buckets
	return numFixedBuckets
}

// Count returns the total number of observations
func (h *Histogram) Count() uint64 {
	return atomic.LoadUint64(&h.totalCount)
}

// Sum returns the sum of all observed values
func (h *Histogram) Sum() float64 {
	return math.Float64frombits(atomic.LoadUint64(&h.sum))
}

// Bucket returns the cumulative count of observations in buckets with upper bound less than or equal to upperBound
func (h *Histogram) Bucket(upperBound float64) uint64 {
	var count uint64

	// Sum up all fixed buckets up to the upper bound
	for i := 0; i < numFixedBuckets; i++ {
		if h.boundaries[i] <= upperBound {
			count += atomic.LoadUint64(&h.counts[i])
		} else {
			break
		}
	}

	// Add outliers that fall within the upper bound
	h.outliers.Range(func(key, value interface{}) bool {
		outlierValue := math.Float64frombits(key.(uint64))
		if outlierValue <= upperBound {
			count += value.(uint64)
		}
		return true
	})

	return count
}

// Quantile calculates the quantile (0.0 to 1.0) using linear interpolation
// For example: Quantile(0.5) returns the median, Quantile(0.95) returns the 95th percentile
func (h *Histogram) Quantile(q float64) float64 {
	if q < 0.0 || q > 1.0 {
		return math.NaN()
	}

	totalCount := h.Count()
	if totalCount == 0 {
		return 0.0
	}

	targetCount := q * float64(totalCount)
	var cumulativeCount uint64

	// Check fixed buckets
	for i := 0; i < numFixedBuckets; i++ {
		bucketCount := atomic.LoadUint64(&h.counts[i])
		cumulativeCount += bucketCount

		if float64(cumulativeCount) >= targetCount {
			// Found the bucket containing the quantile
			if i == 0 {
				// First bucket: interpolate from 0 to boundary
				if bucketCount == 0 {
					return 0.0
				}
				ratio := (targetCount - float64(cumulativeCount-bucketCount)) / float64(bucketCount)
				return ratio * h.boundaries[i]
			} else {
				// Interpolate between previous boundary and current boundary
				if bucketCount == 0 {
					return h.boundaries[i-1]
				}
				ratio := (targetCount - float64(cumulativeCount-bucketCount)) / float64(bucketCount)
				return h.boundaries[i-1] + ratio*(h.boundaries[i]-h.boundaries[i-1])
			}
		}
	}

	// If we reach here, the quantile is in the outliers
	// For simplicity, return the largest fixed bucket boundary
	// A more sophisticated implementation could sort outliers and interpolate
	return h.boundaries[numFixedBuckets-1]
}

// Reset resets all histogram data to zero
func (h *Histogram) Reset() {
	// Reset fixed buckets
	for i := 0; i < numFixedBuckets; i++ {
		atomic.StoreUint64(&h.counts[i], 0)
	}

	// Reset totals
	atomic.StoreUint64(&h.totalCount, 0)
	atomic.StoreUint64(&h.sum, 0)

	// Clear outliers
	h.outliers.Range(func(key, value interface{}) bool {
		h.outliers.Delete(key)
		return true
	})
}

// Metrics represents the core metrics data structure
type Metrics struct {
	mu              *sync.RWMutex
	TotalRequests   int64         `json:"total_requests"`
	SuccessfulReqs  int64         `json:"successful_requests"`
	FailedRequests  int64         `json:"failed_requests"`
	TotalDuration   time.Duration `json:"total_duration_ms"`
	MinResponseTime time.Duration `json:"min_response_time_ms"`
	MaxResponseTime time.Duration `json:"max_response_time_ms"`
	ResponseTimes   []time.Duration
	StartTime       time.Time `json:"start_time"`
	EndTime         time.Time `json:"end_time"`
}

// RequestMetric represents metrics for a single request
type RequestMetric struct {
	Timestamp    time.Time     `json:"timestamp"`
	Duration     time.Duration `json:"duration_ms"`
	StatusCode   int           `json:"status_code"`
	Success      bool          `json:"success"`
	ErrorMessage string        `json:"error_message,omitempty"`
	URL          string        `json:"url"`
	Method       string        `json:"method"`
}

// Collector manages metrics collection
type Collector struct {
	metrics *Metrics
	mu      sync.RWMutex
}

// NewCollector creates a new metrics collector
func NewCollector() *Collector {
	return &Collector{
		metrics: &Metrics{
			StartTime:     time.Now(),
			ResponseTimes: make([]time.Duration, 0),
			mu:            &sync.RWMutex{},
		},
	}
}

// RecordRequest records metrics for a completed request
func (c *Collector) RecordRequest(metric RequestMetric) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// Implementation will be completed in Task 37-41
	// This is a placeholder structure
}

// GetMetrics returns a copy of current metrics
func (c *Collector) GetMetrics() Metrics {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// Return copy to avoid race conditions
	return *c.metrics
}

// TODO: Implement comprehensive metrics functionality:
// - Task 37: Metrics core data structures
// - Task 38: Metrics collection mechanisms
// - Task 39: Metrics aggregation logic
// - Task 40: Metrics export functionality
// - Task 41: Real-time monitoring
