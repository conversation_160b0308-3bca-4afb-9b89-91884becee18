// Package parser provides YAML test plan parsing functionality
package parser

import (
	"bytes"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"gopkg.in/yaml.v3"
)

// ParseError represents a YAML parsing error with location information
type ParseError struct {
	Message  string
	Line     int
	Column   int
	Filename string
	Context  string
}

// Error implements the error interface
func (e *ParseError) Error() string {
	if e.Filename != "" {
		return fmt.Sprintf("%s:%d:%d: %s", e.Filename, e.Line, e.Column, e.Message)
	}
	return fmt.Sprintf("line %d, column %d: %s", e.Line, e.Column, e.Message)
}

// ParserContext holds parsing context for includes and error reporting
type ParserContext struct {
	BasePath     string
	IncludePaths []string
	Depth        int
	MaxDepth     int
}

// NewParserContext creates a new parsing context
func NewParserContext(basePath string) *ParserContext {
	return &ParserContext{
		BasePath:     basePath,
		IncludePaths: make([]string, 0),
		Depth:        0,
		MaxDepth:     10, // Prevent infinite recursion
	}
}

// TestPlan represents the top-level test plan configuration
type TestPlan struct {
	Version     string     `yaml:"version" json:"version" validate:"required"`
	Name        string     `yaml:"name" json:"name" validate:"required"`
	Description string     `yaml:"description,omitempty" json:"description,omitempty"`
	Duration    Duration   `yaml:"duration" json:"duration" validate:"required"`
	Concurrency int        `yaml:"concurrency" json:"concurrency" validate:"required,min=1"`
	RampUp      Duration   `yaml:"ramp_up,omitempty" json:"ramp_up,omitempty"`
	Scenarios   []Scenario `yaml:"scenarios" json:"scenarios" validate:"required,dive"`
	Global      Global     `yaml:"global,omitempty" json:"global,omitempty"`
	Output      Output     `yaml:"output,omitempty" json:"output,omitempty"`
	Variables   []Variable `yaml:"variables,omitempty" json:"variables,omitempty" validate:"dive"`
	Includes    []string   `yaml:"includes,omitempty" json:"includes,omitempty"`
}

// Duration represents a time duration with YAML parsing support
type Duration struct {
	time.Duration
}

// UnmarshalYAML implements custom YAML unmarshaling for Duration with enhanced error handling
func (d *Duration) UnmarshalYAML(value *yaml.Node) error {
	var s string
	if err := value.Decode(&s); err != nil {
		return &ParseError{
			Message: fmt.Sprintf("invalid duration format: expected string, got %v", value.Value),
			Line:    value.Line,
			Column:  value.Column,
		}
	}

	duration, err := time.ParseDuration(s)
	if err != nil {
		return &ParseError{
			Message: fmt.Sprintf("invalid duration format '%s': %v", s, err),
			Line:    value.Line,
			Column:  value.Column,
		}
	}

	d.Duration = duration
	return nil
}

// MarshalYAML implements custom YAML marshaling for Duration
func (d Duration) MarshalYAML() (interface{}, error) {
	return d.Duration.String(), nil
}

// Scenario represents a test scenario with multiple requests
type Scenario struct {
	Name        string     `yaml:"name" json:"name" validate:"required"`
	Description string     `yaml:"description,omitempty" json:"description,omitempty"`
	Weight      int        `yaml:"weight,omitempty" json:"weight,omitempty" validate:"omitempty,min=1,max=100"`
	Requests    []Request  `yaml:"requests" json:"requests" validate:"required,dive"`
	Variables   []Variable `yaml:"variables,omitempty" json:"variables,omitempty" validate:"dive"`
}

// Request represents an HTTP request configuration
type Request struct {
	Name       string            `yaml:"name,omitempty" json:"name,omitempty"`
	Method     string            `yaml:"method" json:"method" validate:"required,oneof=GET POST PUT DELETE PATCH HEAD OPTIONS"`
	URL        string            `yaml:"url" json:"url" validate:"required"`
	Headers    map[string]string `yaml:"headers,omitempty" json:"headers,omitempty"`
	Body       interface{}       `yaml:"body,omitempty" json:"body,omitempty"`
	Timeout    Duration          `yaml:"timeout,omitempty" json:"timeout,omitempty"`
	Assertions []Assertion       `yaml:"assertions,omitempty" json:"assertions,omitempty" validate:"dive"`
	Variables  map[string]string `yaml:"variables,omitempty" json:"variables,omitempty"`
	Extract    []Extract         `yaml:"extract,omitempty" json:"extract,omitempty" validate:"dive"`
}

// Assertion represents a validation rule for responses
type Assertion struct {
	Type        string      `yaml:"type" json:"type" validate:"required,oneof=status_code response_time contains json_path header_exists"`
	Field       string      `yaml:"field,omitempty" json:"field,omitempty"`
	Operator    string      `yaml:"operator,omitempty" json:"operator,omitempty" validate:"omitempty,oneof=eq ne lt le gt ge contains not_contains"`
	Value       interface{} `yaml:"value,omitempty" json:"value,omitempty"`
	Description string      `yaml:"description,omitempty" json:"description,omitempty"`
}

// Extract represents data extraction from responses
type Extract struct {
	Name    string `yaml:"name" json:"name" validate:"required"`
	Type    string `yaml:"type" json:"type" validate:"required,oneof=json_path xpath regex header"`
	Path    string `yaml:"path" json:"path" validate:"required"`
	Default string `yaml:"default,omitempty" json:"default,omitempty"`
	As      string `yaml:"as,omitempty" json:"as,omitempty"`
}

// Global represents global configuration settings
type Global struct {
	BaseURL   string            `yaml:"base_url,omitempty" json:"base_url,omitempty" validate:"omitempty,url"`
	Headers   map[string]string `yaml:"headers,omitempty" json:"headers,omitempty"`
	Timeout   Duration          `yaml:"timeout,omitempty" json:"timeout,omitempty"`
	Variables map[string]string `yaml:"variables,omitempty" json:"variables,omitempty"`
	RateLimit RateLimit         `yaml:"rate_limit,omitempty" json:"rate_limit,omitempty"`
}

// RateLimit represents rate limiting configuration
type RateLimit struct {
	RequestsPerSecond int      `yaml:"requests_per_second,omitempty" json:"requests_per_second,omitempty" validate:"omitempty,min=1"`
	BurstSize         int      `yaml:"burst_size,omitempty" json:"burst_size,omitempty" validate:"omitempty,min=1"`
	Delay             Duration `yaml:"delay,omitempty" json:"delay,omitempty"`
}

// Output represents output configuration
type Output struct {
	Format   []string `yaml:"format,omitempty" json:"format,omitempty" validate:"dive,oneof=json html csv"`
	File     string   `yaml:"file,omitempty" json:"file,omitempty"`
	Metrics  []string `yaml:"metrics,omitempty" json:"metrics,omitempty" validate:"dive,oneof=response_time throughput error_rate success_rate p50 p95 p99"`
	Detailed bool     `yaml:"detailed,omitempty" json:"detailed,omitempty"`
}

// Variable represents a variable definition
type Variable struct {
	Name        string      `yaml:"name" json:"name" validate:"required"`
	Type        string      `yaml:"type" json:"type" validate:"required,oneof=static random faker csv"`
	Value       interface{} `yaml:"value,omitempty" json:"value,omitempty"`
	Method      string      `yaml:"method,omitempty" json:"method,omitempty"`
	File        string      `yaml:"file,omitempty" json:"file,omitempty"`
	Description string      `yaml:"description,omitempty" json:"description,omitempty"`
}

// Parser handles YAML test plan parsing
type Parser struct {
	validator *validator.Validate
	context   *ParserContext
}

// NewParser creates a new YAML parser with validation
func NewParser() *Parser {
	return &Parser{
		validator: validator.New(),
		context:   nil, // Will be set during parsing
	}
}

// NewParserWithContext creates a new YAML parser with parsing context
func NewParserWithContext(basePath string) *Parser {
	return &Parser{
		validator: validator.New(),
		context:   NewParserContext(basePath),
	}
}

// ParseFile parses a YAML test plan from file with includes support
func (p *Parser) ParseFile(filename string) (*TestPlan, error) {
	// Set up context if not already set
	if p.context == nil {
		absPath, err := filepath.Abs(filename)
		if err != nil {
			return nil, fmt.Errorf("failed to get absolute path for %s: %w", filename, err)
		}
		p.context = NewParserContext(filepath.Dir(absPath))
	}

	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filename, err)
	}

	return p.parseWithContext(data, filename)
}

// ParseBytes parses a YAML test plan from byte slice
func (p *Parser) ParseBytes(data []byte) (*TestPlan, error) {
	return p.parseWithContext(data, "")
}

// parseWithContext parses YAML data with full context support
func (p *Parser) parseWithContext(data []byte, filename string) (*TestPlan, error) {
	// Check recursion depth
	if p.context != nil && p.context.Depth > p.context.MaxDepth {
		return nil, fmt.Errorf("maximum include depth exceeded (%d)", p.context.MaxDepth)
	}

	var plan TestPlan

	// Parse YAML with strict mode and enhanced error handling
	decoder := yaml.NewDecoder(bytes.NewReader(data))
	decoder.KnownFields(true) // Strict mode - fail on unknown fields

	if err := decoder.Decode(&plan); err != nil {
		return nil, p.wrapYAMLError(err, filename)
	}

	// Process includes before validation
	if err := p.processIncludes(&plan); err != nil {
		return nil, fmt.Errorf("failed to process includes: %w", err)
	}

	// Process variable references
	if err := p.processVariableReferences(&plan); err != nil {
		return nil, fmt.Errorf("failed to process variable references: %w", err)
	}

	// Validate first before setting defaults
	if err := p.validator.Struct(&plan); err != nil {
		return nil, p.wrapValidationError(err, filename)
	}

	// Set defaults after validation passes
	if err := p.setDefaults(&plan); err != nil {
		return nil, fmt.Errorf("failed to set defaults: %w", err)
	}

	// Custom validation after defaults are set
	if err := p.validateCustomRules(&plan); err != nil {
		return nil, fmt.Errorf("validation failed: custom validation failed: %w", err)
	}

	return &plan, nil
}

// processIncludes processes include directives in the test plan
func (p *Parser) processIncludes(plan *TestPlan) error {
	if len(plan.Includes) == 0 {
		return nil
	}

	if p.context == nil {
		return fmt.Errorf("includes not supported without parser context")
	}

	// Increment depth for recursion tracking
	p.context.Depth++
	defer func() { p.context.Depth-- }()

	for _, includePath := range plan.Includes {
		// Resolve include path relative to base path
		fullPath := filepath.Join(p.context.BasePath, includePath)

		// Check for circular includes
		for _, existing := range p.context.IncludePaths {
			if existing == fullPath {
				return fmt.Errorf("circular include detected: %s", fullPath)
			}
		}

		// Add to include path tracking
		p.context.IncludePaths = append(p.context.IncludePaths, fullPath)

		// Parse included file
		includedPlan, err := p.ParseFile(fullPath)
		if err != nil {
			return fmt.Errorf("failed to parse included file %s: %w", includePath, err)
		}

		// Merge included plan into current plan
		if err := p.mergePlans(plan, includedPlan); err != nil {
			return fmt.Errorf("failed to merge included plan %s: %w", includePath, err)
		}

		// Remove from include path tracking
		p.context.IncludePaths = p.context.IncludePaths[:len(p.context.IncludePaths)-1]
	}

	// Clear includes after processing to avoid reprocessing
	plan.Includes = nil
	return nil
}

// processVariableReferences processes variable references in the test plan
func (p *Parser) processVariableReferences(plan *TestPlan) error {
	// This is a placeholder for variable reference processing
	// In a full implementation, this would traverse the entire plan structure
	// and replace {{.variable_name}} patterns with actual values

	// For now, we'll just validate that referenced variables exist
	return p.validateVariableReferences(plan)
}

// mergePlans merges an included plan into the main plan
func (p *Parser) mergePlans(main, included *TestPlan) error {
	// Merge scenarios
	main.Scenarios = append(main.Scenarios, included.Scenarios...)

	// Merge variables
	main.Variables = append(main.Variables, included.Variables...)

	// Merge global settings (main plan takes precedence)
	if main.Global.BaseURL == "" && included.Global.BaseURL != "" {
		main.Global.BaseURL = included.Global.BaseURL
	}

	// Merge global headers
	if main.Global.Headers == nil {
		main.Global.Headers = make(map[string]string)
	}
	for k, v := range included.Global.Headers {
		if _, exists := main.Global.Headers[k]; !exists {
			main.Global.Headers[k] = v
		}
	}

	// Merge global variables
	if main.Global.Variables == nil {
		main.Global.Variables = make(map[string]string)
	}
	for k, v := range included.Global.Variables {
		if _, exists := main.Global.Variables[k]; !exists {
			main.Global.Variables[k] = v
		}
	}

	return nil
}

// wrapYAMLError wraps a YAML parsing error with enhanced context
func (p *Parser) wrapYAMLError(err error, filename string) error {
	if yamlErr, ok := err.(*yaml.TypeError); ok {
		// Extract line/column information from yaml.TypeError
		lines := strings.Split(yamlErr.Error(), "\n")
		if len(lines) > 0 {
			firstError := lines[0]
			if strings.Contains(firstError, "line ") {
				return &ParseError{
					Message:  yamlErr.Error(),
					Filename: filename,
					Context:  firstError,
				}
			}
		}
	}

	return fmt.Errorf("failed to parse YAML: %w", err)
}

// wrapValidationError wraps a validation error with enhanced context
func (p *Parser) wrapValidationError(err error, filename string) error {
	return fmt.Errorf("validation failed: struct validation failed: %w", err)
}

// Validate validates a parsed test plan
func (p *Parser) Validate(plan *TestPlan) error {
	if err := p.validator.Struct(plan); err != nil {
		return fmt.Errorf("struct validation failed: %w", err)
	}

	// Custom validation logic
	if err := p.validateCustomRules(plan); err != nil {
		return fmt.Errorf("custom validation failed: %w", err)
	}

	return nil
}

// setDefaults sets default values for the test plan
func (p *Parser) setDefaults(plan *TestPlan) error {
	// Set default version if not specified
	if plan.Version == "" {
		plan.Version = "1.0"
	}

	// Set default output format if not specified
	if len(plan.Output.Format) == 0 {
		plan.Output.Format = []string{"json"}
	}

	// Set default metrics if not specified
	if len(plan.Output.Metrics) == 0 {
		plan.Output.Metrics = []string{"response_time", "throughput", "error_rate"}
	}

	// Set default scenario weights
	totalWeight := 0
	for i := range plan.Scenarios {
		if plan.Scenarios[i].Weight == 0 {
			plan.Scenarios[i].Weight = 1
		}
		totalWeight += plan.Scenarios[i].Weight
	}

	// Normalize weights to 100
	if totalWeight > 0 && totalWeight != 100 {
		for i := range plan.Scenarios {
			plan.Scenarios[i].Weight = (plan.Scenarios[i].Weight * 100) / totalWeight
		}
	}

	// Set default timeout if not specified
	defaultTimeout := Duration{Duration: 30 * time.Second}
	if plan.Global.Timeout.Duration == 0 {
		plan.Global.Timeout = defaultTimeout
	}

	// Set default request timeouts
	for i := range plan.Scenarios {
		for j := range plan.Scenarios[i].Requests {
			if plan.Scenarios[i].Requests[j].Timeout.Duration == 0 {
				plan.Scenarios[i].Requests[j].Timeout = defaultTimeout
			}
		}
	}

	return nil
}

// validateCustomRules performs custom validation beyond struct tags
func (p *Parser) validateCustomRules(plan *TestPlan) error {
	// Validate scenario weights sum to reasonable total
	totalWeight := 0
	for _, scenario := range plan.Scenarios {
		totalWeight += scenario.Weight
	}

	if totalWeight == 0 {
		return fmt.Errorf("total scenario weight cannot be zero")
	}

	// Validate variable references
	if err := p.validateVariableReferences(plan); err != nil {
		return err
	}

	// Validate URL formats
	if err := p.validateURLs(plan); err != nil {
		return err
	}

	return nil
}

// validateVariableReferences ensures all variable references are defined
func (p *Parser) validateVariableReferences(plan *TestPlan) error {
	// Collect all defined variables
	definedVars := make(map[string]bool)

	// Global variables
	for name := range plan.Global.Variables {
		definedVars[name] = true
	}

	// Plan-level variables
	for _, variable := range plan.Variables {
		definedVars[variable.Name] = true
	}

	// Scenario-level variables
	for _, scenario := range plan.Scenarios {
		for _, variable := range scenario.Variables {
			definedVars[variable.Name] = true
		}

		// Request-level variables
		for _, request := range scenario.Requests {
			for name := range request.Variables {
				definedVars[name] = true
			}
		}
	}

	// TODO: Implement variable reference validation in URLs, headers, and body
	// This would require parsing template expressions like {{variable_name}}

	return nil
}

// validateURLs validates URL formats in requests
func (p *Parser) validateURLs(plan *TestPlan) error {
	for _, scenario := range plan.Scenarios {
		for _, request := range scenario.Requests {
			// Skip template URLs for now
			if len(request.URL) > 0 && request.URL[0] != '{' {
				// Allow relative URLs if base_url is set, or if URL is absolute
				if request.URL != "" && plan.Global.BaseURL == "" && !isAbsoluteURL(request.URL) {
					// Only fail if the URL is not a simple path (starts with /)
					if len(request.URL) > 0 && request.URL[0] != '/' {
						return fmt.Errorf("request URL '%s' in scenario '%s' is not absolute and no base_url is set", request.URL, scenario.Name)
					}
				}
			}
		}
	}

	return nil
}

// isAbsoluteURL checks if a URL is absolute
func isAbsoluteURL(url string) bool {
	return len(url) >= 7 && (url[:7] == "http://" || (len(url) >= 8 && url[:8] == "https://"))
}
