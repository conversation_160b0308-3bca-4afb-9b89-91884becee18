package core

import (
	"fmt"
)

// GPUError represents a GPU-related error
type GPUError struct {
	Code     GPUErrorCode
	Message  string
	Platform GPUPlatform
	DeviceID int
	Cause    error
}

func (e *GPUError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("GPU Error [%s:%d] %s: %s (caused by: %v)",
			e.Platform.String(), e.DeviceID, e.Code.String(), e.Message, e.Cause)
	}
	return fmt.Sprintf("GPU Error [%s:%d] %s: %s",
		e.Platform.String(), e.DeviceID, e.Code.String(), e.Message)
}

func (e *GPUError) Unwrap() error {
	return e.Cause
}

// GPUErrorCode represents specific GPU error types
type GPUErrorCode int

const (
	// Device Errors
	ErrorDeviceNotFound GPUErrorCode = iota + 1000
	ErrorDeviceNotAvailable
	ErrorDeviceInitializationFailed
	ErrorDeviceContextLost
	ErrorDeviceReset

	// Memory Errors
	ErrorOutOfMemory
	ErrorInvalidMemoryAccess
	ErrorMemoryAllocationFailed
	ErrorMemoryFragmentation
	ErrorMemoryCorruption

	// Model Errors
	ErrorModelLoadFailed
	ErrorModelNotFound
	ErrorModelIncompatible
	ErrorModelCorrupted
	ErrorInferenceFailed

	// Stream Errors
	ErrorStreamCreationFailed
	ErrorStreamSynchronizationFailed
	ErrorStreamDestroyed
	ErrorStreamTimeout

	// Configuration Errors
	ErrorInvalidConfiguration
	ErrorConfigurationNotSupported
	ErrorConfigurationFailed

	// Platform Errors
	ErrorPlatformNotSupported
	ErrorPlatformInitializationFailed
	ErrorDriverNotFound
	ErrorDriverVersionMismatch
	ErrorLibraryNotFound

	// General Errors
	ErrorOperationTimeout
	ErrorOperationCancelled
	ErrorInvalidParameter
	ErrorNotImplemented
	ErrorUnknown
)

func (c GPUErrorCode) String() string {
	switch c {
	// Device Errors
	case ErrorDeviceNotFound:
		return "DEVICE_NOT_FOUND"
	case ErrorDeviceNotAvailable:
		return "DEVICE_NOT_AVAILABLE"
	case ErrorDeviceInitializationFailed:
		return "DEVICE_INITIALIZATION_FAILED"
	case ErrorDeviceContextLost:
		return "DEVICE_CONTEXT_LOST"
	case ErrorDeviceReset:
		return "DEVICE_RESET"

	// Memory Errors
	case ErrorOutOfMemory:
		return "OUT_OF_MEMORY"
	case ErrorInvalidMemoryAccess:
		return "INVALID_MEMORY_ACCESS"
	case ErrorMemoryAllocationFailed:
		return "MEMORY_ALLOCATION_FAILED"
	case ErrorMemoryFragmentation:
		return "MEMORY_FRAGMENTATION"
	case ErrorMemoryCorruption:
		return "MEMORY_CORRUPTION"

	// Model Errors
	case ErrorModelLoadFailed:
		return "MODEL_LOAD_FAILED"
	case ErrorModelNotFound:
		return "MODEL_NOT_FOUND"
	case ErrorModelIncompatible:
		return "MODEL_INCOMPATIBLE"
	case ErrorModelCorrupted:
		return "MODEL_CORRUPTED"
	case ErrorInferenceFailed:
		return "INFERENCE_FAILED"

	// Stream Errors
	case ErrorStreamCreationFailed:
		return "STREAM_CREATION_FAILED"
	case ErrorStreamSynchronizationFailed:
		return "STREAM_SYNCHRONIZATION_FAILED"
	case ErrorStreamDestroyed:
		return "STREAM_DESTROYED"
	case ErrorStreamTimeout:
		return "STREAM_TIMEOUT"

	// Configuration Errors
	case ErrorInvalidConfiguration:
		return "INVALID_CONFIGURATION"
	case ErrorConfigurationNotSupported:
		return "CONFIGURATION_NOT_SUPPORTED"
	case ErrorConfigurationFailed:
		return "CONFIGURATION_FAILED"

	// Platform Errors
	case ErrorPlatformNotSupported:
		return "PLATFORM_NOT_SUPPORTED"
	case ErrorPlatformInitializationFailed:
		return "PLATFORM_INITIALIZATION_FAILED"
	case ErrorDriverNotFound:
		return "DRIVER_NOT_FOUND"
	case ErrorDriverVersionMismatch:
		return "DRIVER_VERSION_MISMATCH"
	case ErrorLibraryNotFound:
		return "LIBRARY_NOT_FOUND"

	// General Errors
	case ErrorOperationTimeout:
		return "OPERATION_TIMEOUT"
	case ErrorOperationCancelled:
		return "OPERATION_CANCELLED"
	case ErrorInvalidParameter:
		return "INVALID_PARAMETER"
	case ErrorNotImplemented:
		return "NOT_IMPLEMENTED"
	case ErrorUnknown:
		return "UNKNOWN"
	default:
		return "UNDEFINED"
	}
}

// NewGPUError creates a new GPU error
func NewGPUError(code GPUErrorCode, platform GPUPlatform, deviceID int, message string, cause error) *GPUError {
	return &GPUError{
		Code:     code,
		Message:  message,
		Platform: platform,
		DeviceID: deviceID,
		Cause:    cause,
	}
}

// IsRecoverable determines if an error can be recovered from
func (e *GPUError) IsRecoverable() bool {
	switch e.Code {
	case ErrorDeviceContextLost, ErrorDeviceReset, ErrorMemoryFragmentation, ErrorStreamTimeout:
		return true
	case ErrorDeviceNotFound, ErrorDriverNotFound, ErrorPlatformNotSupported, ErrorModelCorrupted:
		return false
	default:
		return false
	}
}

// RequiresDeviceReset determines if the error requires a device reset
func (e *GPUError) RequiresDeviceReset() bool {
	switch e.Code {
	case ErrorDeviceContextLost, ErrorMemoryCorruption, ErrorDeviceReset:
		return true
	default:
		return false
	}
}
