package core

import (
	"context"
)

// GPUProvider defines the main interface for GPU operations across all platforms
type GPUProvider interface {
	// Device Management
	GetDevices(ctx context.Context) ([]GPUDevice, error)
	SelectDevice(ctx context.Context, deviceID int) error
	GetSelectedDevice(ctx context.Context) (GPUDevice, error)

	// Memory Management
	AllocateMemory(ctx context.Context, size int64) (GPUMemory, error)
	FreeMemory(ctx context.Context, memory GPUMemory) error
	GetMemoryInfo(ctx context.Context) (MemoryInfo, error)

	// Model Operations
	LoadModel(ctx context.Context, modelPath string) (GPUModel, error)
	UnloadModel(ctx context.Context, model GPUModel) error
	RunInference(ctx context.Context, model GPUModel, input []byte) ([]byte, error)

	// Stream Management
	CreateStream(ctx context.Context) (GPUStream, error)
	DestroyStream(ctx context.Context, stream GPUStream) error
	SynchronizeStream(ctx context.Context, stream GPUStream) error

	// Configuration
	Configure(ctx context.Context, config GPUConfig) error
	GetConfiguration(ctx context.Context) (GPUConfig, error)

	// Error Handling & Recovery
	GetLastError(ctx context.Context) error
	RecoverFromError(ctx context.Context) error

	// Cleanup
	Shutdown(ctx context.Context) error
}

// GPUDevice represents a GPU device
type GPUDevice interface {
	GetID() int
	GetName() string
	GetMemorySize() int64
	GetComputeCapability() string
	GetPlatform() GPUPlatform
	IsAvailable() bool
}

// GPUMemory represents allocated GPU memory
type GPUMemory interface {
	GetSize() int64
	GetAddress() uintptr
	IsValid() bool
}

// GPUModel represents a loaded model on GPU
type GPUModel interface {
	GetID() string
	GetPath() string
	GetMemoryUsage() int64
	IsLoaded() bool
}

// GPUStream represents a GPU computation stream
type GPUStream interface {
	GetID() string
	IsActive() bool
	GetPriority() int
}

// GPUConfig holds configuration settings
type GPUConfig struct {
	DeviceID          int
	MemoryPoolSize    int64
	StreamCount       int
	OptimizationLevel int
	EnableProfiling   bool
	Platform          GPUPlatform
}

// MemoryInfo provides memory statistics
type MemoryInfo struct {
	TotalMemory      int64
	AvailableMemory  int64
	UsedMemory       int64
	FragmentedMemory int64
}

// GPUPlatform represents the GPU platform type
type GPUPlatform int

const (
	PlatformUnknown GPUPlatform = iota
	PlatformCUDA
	PlatformMetal
	PlatformDirectML
	PlatformROCm
	PlatformOpenCL
)

func (p GPUPlatform) String() string {
	switch p {
	case PlatformCUDA:
		return "CUDA"
	case PlatformMetal:
		return "Metal"
	case PlatformDirectML:
		return "DirectML"
	case PlatformROCm:
		return "ROCm"
	case PlatformOpenCL:
		return "OpenCL"
	default:
		return "Unknown"
	}
}

// GPUProviderFactory creates platform-specific GPU providers
type GPUProviderFactory interface {
	CreateProvider(ctx context.Context, platform GPUPlatform) (GPUProvider, error)
	GetSupportedPlatforms() []GPUPlatform
	DetectAvailablePlatforms(ctx context.Context) ([]GPUPlatform, error)
}
