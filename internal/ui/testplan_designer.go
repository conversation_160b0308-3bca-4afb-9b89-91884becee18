//go:build gui
// +build gui

package ui

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"neuralmetergo/internal/parser"
	"neuralmetergo/internal/validation"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
	"gopkg.in/yaml.v3"
)

// Command interface for implementing command pattern
type Command interface {
	Execute() error
	Undo() error
	GetDescription() string
	GetTimestamp() time.Time
}

// ChangeType represents the type of change for undo/redo
type ChangeType string

const (
	ChangeTypeFieldUpdate ChangeType = "field_update"
	ChangeTypeNodeAdd     ChangeType = "node_add"
	ChangeTypeNodeRemove  ChangeType = "node_remove"
	ChangeTypeNodeMove    ChangeType = "node_move"
)

// Change represents a change that can be undone/redone with enhanced command pattern support
type Change struct {
	Type        ChangeType
	NodeID      string
	Field       string
	OldValue    interface{}
	NewValue    interface{}
	Timestamp   time.Time
	Description string
}

// Implement Command interface for Change
func (c *Change) Execute() error {
	// Implementation handled by TestPlanDesigner.applyChange
	return nil
}

func (c *Change) Undo() error {
	// Implementation handled by TestPlanDesigner.applyChange
	return nil
}

func (c *Change) GetDescription() string {
	if c.Description != "" {
		return c.Description
	}
	return fmt.Sprintf("%s: %s", c.Type, c.Field)
}

func (c *Change) GetTimestamp() time.Time {
	return c.Timestamp
}

// FieldUpdateCommand implements Command for field updates
type FieldUpdateCommand struct {
	designer  *TestPlanDesigner
	nodeID    string
	field     string
	oldValue  interface{}
	newValue  interface{}
	timestamp time.Time
}

func NewFieldUpdateCommand(designer *TestPlanDesigner, nodeID, field string, oldValue, newValue interface{}) *FieldUpdateCommand {
	return &FieldUpdateCommand{
		designer:  designer,
		nodeID:    nodeID,
		field:     field,
		oldValue:  oldValue,
		newValue:  newValue,
		timestamp: time.Now(),
	}
}

func (cmd *FieldUpdateCommand) Execute() error {
	node := cmd.designer.findNodeByID(cmd.nodeID)
	if node == nil {
		return fmt.Errorf("node not found: %s", cmd.nodeID)
	}

	node.SetFieldValue(cmd.field, cmd.newValue)
	cmd.designer.treeWidget.RefreshNode(node)
	cmd.designer.validateCurrentPlan()
	return nil
}

func (cmd *FieldUpdateCommand) Undo() error {
	node := cmd.designer.findNodeByID(cmd.nodeID)
	if node == nil {
		return fmt.Errorf("node not found: %s", cmd.nodeID)
	}

	node.SetFieldValue(cmd.field, cmd.oldValue)
	cmd.designer.treeWidget.RefreshNode(node)
	cmd.designer.validateCurrentPlan()
	return nil
}

func (cmd *FieldUpdateCommand) GetDescription() string {
	return fmt.Sprintf("Update %s", cmd.field)
}

func (cmd *FieldUpdateCommand) GetTimestamp() time.Time {
	return cmd.timestamp
}

// Enhanced ChangeTracker with command pattern and memory management
type ChangeTracker struct {
	commands       []Command
	index          int
	maxLevels      int
	mu             sync.RWMutex
	lastPrune      time.Time
	pruneThreshold time.Duration
}

// NewChangeTracker creates a new enhanced change tracker
func NewChangeTracker(maxLevels int) *ChangeTracker {
	return &ChangeTracker{
		commands:       make([]Command, 0),
		index:          -1,
		maxLevels:      maxLevels,
		lastPrune:      time.Now(),
		pruneThreshold: 30 * time.Minute, // Prune commands older than 30 minutes
	}
}

// ExecuteCommand executes a command and adds it to the history
func (ct *ChangeTracker) ExecuteCommand(cmd Command) error {
	ct.mu.Lock()
	defer ct.mu.Unlock()

	if err := cmd.Execute(); err != nil {
		return err
	}

	// Remove any commands after current index (they become invalid after new command)
	ct.commands = ct.commands[:ct.index+1]

	// Add new command
	ct.commands = append(ct.commands, cmd)
	ct.index++

	// Trim if exceeding max levels
	if len(ct.commands) > ct.maxLevels {
		ct.commands = ct.commands[1:]
		ct.index--
	}

	// Auto-prune old commands periodically
	ct.autoPrune()

	return nil
}

// RecordChange records a new change (legacy support)
func (ct *ChangeTracker) RecordChange(change *Change) {
	ct.mu.Lock()
	defer ct.mu.Unlock()

	change.Timestamp = time.Now()

	// Remove any changes after current index
	ct.commands = ct.commands[:ct.index+1]

	// Add new change as command
	ct.commands = append(ct.commands, change)
	ct.index++

	// Trim if exceeding max levels
	if len(ct.commands) > ct.maxLevels {
		ct.commands = ct.commands[1:]
		ct.index--
	}

	// Auto-prune old commands periodically
	ct.autoPrune()
}

// CanUndo returns true if undo is possible
func (ct *ChangeTracker) CanUndo() bool {
	ct.mu.RLock()
	defer ct.mu.RUnlock()
	return ct.index >= 0
}

// CanRedo returns true if redo is possible
func (ct *ChangeTracker) CanRedo() bool {
	ct.mu.RLock()
	defer ct.mu.RUnlock()
	return ct.index < len(ct.commands)-1
}

// Undo returns the change to undo
func (ct *ChangeTracker) Undo() *Change {
	ct.mu.Lock()
	defer ct.mu.Unlock()

	if ct.index < 0 {
		return nil
	}

	cmd := ct.commands[ct.index]
	ct.index--

	// Convert Command back to Change for legacy compatibility
	if change, ok := cmd.(*Change); ok {
		return change
	}

	// Create Change from Command for legacy compatibility
	return &Change{
		Type:        ChangeTypeFieldUpdate,
		Description: cmd.GetDescription(),
		Timestamp:   cmd.GetTimestamp(),
	}
}

// UndoCommand undos the last command using the command pattern
func (ct *ChangeTracker) UndoCommand() error {
	ct.mu.Lock()
	defer ct.mu.Unlock()

	if ct.index < 0 {
		return fmt.Errorf("nothing to undo")
	}

	cmd := ct.commands[ct.index]
	if err := cmd.Undo(); err != nil {
		return err
	}

	ct.index--
	return nil
}

// Redo returns the change to redo
func (ct *ChangeTracker) Redo() *Change {
	ct.mu.Lock()
	defer ct.mu.Unlock()

	if ct.index >= len(ct.commands)-1 {
		return nil
	}

	ct.index++
	cmd := ct.commands[ct.index]

	// Convert Command back to Change for legacy compatibility
	if change, ok := cmd.(*Change); ok {
		return change
	}

	// Create Change from Command for legacy compatibility
	return &Change{
		Type:        ChangeTypeFieldUpdate,
		Description: cmd.GetDescription(),
		Timestamp:   cmd.GetTimestamp(),
	}
}

// RedoCommand redos the next command using the command pattern
func (ct *ChangeTracker) RedoCommand() error {
	ct.mu.Lock()
	defer ct.mu.Unlock()

	if ct.index >= len(ct.commands)-1 {
		return fmt.Errorf("nothing to redo")
	}

	ct.index++
	cmd := ct.commands[ct.index]
	return cmd.Execute()
}

// Clear clears all changes
func (ct *ChangeTracker) Clear() {
	ct.mu.Lock()
	defer ct.mu.Unlock()
	ct.commands = ct.commands[:0]
	ct.index = -1
}

// GetUndoDescription returns description of the command that would be undone
func (ct *ChangeTracker) GetUndoDescription() string {
	ct.mu.RLock()
	defer ct.mu.RUnlock()

	if ct.index < 0 {
		return ""
	}

	return ct.commands[ct.index].GetDescription()
}

// GetRedoDescription returns description of the command that would be redone
func (ct *ChangeTracker) GetRedoDescription() string {
	ct.mu.RLock()
	defer ct.mu.RUnlock()

	if ct.index >= len(ct.commands)-1 {
		return ""
	}

	return ct.commands[ct.index+1].GetDescription()
}

// autoPrune removes old commands to manage memory
func (ct *ChangeTracker) autoPrune() {
	now := time.Now()
	if now.Sub(ct.lastPrune) < ct.pruneThreshold {
		return
	}

	ct.lastPrune = now
	threshold := now.Add(-ct.pruneThreshold)

	// Find first command to keep
	keepFrom := 0
	for i, cmd := range ct.commands {
		if cmd.GetTimestamp().After(threshold) {
			keepFrom = i
			break
		}
	}

	if keepFrom > 0 {
		ct.commands = ct.commands[keepFrom:]
		ct.index -= keepFrom
		if ct.index < -1 {
			ct.index = -1
		}
	}
}

// TreeFormAdapter adapts the comprehensive FormBuilder for tree node editing
type TreeFormAdapter struct {
	formBuilder      *FormBuilder
	validationEngine *validation.ValidationEngine
	logger           *log.Logger
	container        *fyne.Container
	currentNode      *TreeNode
	onChange         func(field string, value interface{})
	onValidation     func(valid bool, errors []string)
	mu               sync.RWMutex
}

// NewTreeFormAdapter creates a new tree form adapter
func NewTreeFormAdapter(validationEngine *validation.ValidationEngine, logger *log.Logger) *TreeFormAdapter {
	if logger == nil {
		logger = log.Default()
	}

	adapter := &TreeFormAdapter{
		validationEngine: validationEngine,
		logger:           logger,
		container:        container.NewVBox(),
		onChange:         func(string, interface{}) {},
		onValidation:     func(bool, []string) {},
	}

	// Create FormBuilder with callbacks
	adapter.formBuilder = NewFormBuilder(validationEngine, logger)
	adapter.formBuilder.SetFieldChangeCallback(adapter.handleFieldChange)
	adapter.formBuilder.SetValidationChangeCallback(adapter.handleValidationChange)

	return adapter
}

// LoadNode loads a node into the form adapter
func (tfa *TreeFormAdapter) LoadNode(node *TreeNode) {
	tfa.mu.Lock()
	defer tfa.mu.Unlock()

	tfa.currentNode = node
	tfa.buildForm()
}

// buildForm builds the form based on the current node type
func (tfa *TreeFormAdapter) buildForm() {
	tfa.container.RemoveAll()

	if tfa.currentNode == nil {
		tfa.container.Add(widget.NewLabel("No node selected"))
		return
	}

	// Add basic info
	tfa.container.Add(widget.NewCard("Node Information", "",
		container.NewVBox(
			widget.NewLabel(fmt.Sprintf("Type: %s", tfa.currentNode.Type)),
			widget.NewLabel(fmt.Sprintf("ID: %s", tfa.currentNode.GetID())),
		),
	))

	// Create appropriate data structure and build form
	var data interface{}
	switch tfa.currentNode.Type {
	case NodeTypeTestPlan:
		data = &parser.TestPlan{}
	case NodeTypeScenario:
		data = &parser.Scenario{}
	case NodeTypeRequest:
		data = &parser.Request{}
	case NodeTypeAssertion:
		data = &parser.Assertion{}
	case NodeTypeExtract:
		data = &parser.Extract{}
	case NodeTypeVariable:
		data = &parser.Variable{}
	case NodeTypeGlobal:
		data = &parser.Global{}
	case NodeTypeOutput:
		data = &parser.Output{}
	default:
		tfa.container.Add(widget.NewLabel("Unknown node type"))
		return
	}

	// Set up field change callback
	tfa.formBuilder.SetFieldChangeCallback(func(field string, value interface{}) {
		if tfa.onChange != nil {
			tfa.onChange(field, value)
		}
	})

	// Build the form using the comprehensive FormBuilder
	formContainer := tfa.formBuilder.BuildForm(data)
	tfa.container.Add(formContainer)
	tfa.container.Refresh()
}

// GetContainer returns the form container
func (tfa *TreeFormAdapter) GetContainer() *fyne.Container {
	return tfa.container
}

// SetOnChange sets the change callback
func (tfa *TreeFormAdapter) SetOnChange(callback func(field string, value interface{})) {
	tfa.onChange = callback
}

// SetOnValidation sets the validation change callback
func (tfa *TreeFormAdapter) SetOnValidation(callback func(valid bool, errors []string)) {
	tfa.onValidation = callback
}

// handleFieldChange processes field changes and updates the underlying data
func (tfa *TreeFormAdapter) handleFieldChange(field string, value interface{}) {
	tfa.mu.RLock()
	currentNode := tfa.currentNode
	tfa.mu.RUnlock()

	if currentNode == nil {
		return
	}

	// Update the node data if it exists
	if currentNode.Data != nil {
		tfa.updateNodeData(currentNode, field, value)
	}

	// Trigger validation
	tfa.validateNode(currentNode)

	// Notify parent component of change
	if tfa.onChange != nil {
		tfa.onChange(field, value)
	}
}

// handleValidationChange processes validation changes
func (tfa *TreeFormAdapter) handleValidationChange(valid bool, errors []string) {
	// Update current node validation state
	tfa.mu.RLock()
	currentNode := tfa.currentNode
	tfa.mu.RUnlock()

	if currentNode != nil {
		currentNode.SetValidationState(!valid, false)
	}

	// Forward to parent callback
	if tfa.onValidation != nil {
		tfa.onValidation(valid, errors)
	}
}

// updateNodeData updates the node's data structure (simplified version)
func (tfa *TreeFormAdapter) updateNodeData(node *TreeNode, field string, value interface{}) {
	if node.Data == nil {
		return
	}

	// For now, just log the update - full reflection-based update can be added later
	tfa.logger.Printf("Updating field %s with value %v for node type %s", field, value, node.Type)

	// TODO: Implement reflection-based field updates when needed
}

// validateNode validates the current node using the validation engine
func (tfa *TreeFormAdapter) validateNode(node *TreeNode) {
	if node == nil || tfa.validationEngine == nil {
		return
	}

	// Create a minimal test plan for validation context
	testPlan := &parser.TestPlan{
		Version: "1.0",
		Name:    "temp",
	}

	// Validate using the validation engine
	result := tfa.validationEngine.ValidateTestPlan(testPlan)

	// Update node validation state based on result
	hasErrors := result.Summary.ErrorCount > 0
	hasWarnings := result.Summary.WarningCount > 0
	node.SetValidationState(hasErrors, hasWarnings)
}

// Template management structures
type TemplateMetadata struct {
	Name        string
	Description string
	Category    string
	Tags        []string
	CreatedAt   time.Time
	UpdatedAt   time.Time
	Version     string
	Author      string
}

type TemplateStorage interface {
	Save(name string, template *parser.TestPlan, metadata *TemplateMetadata) error
	Load(name string) (*parser.TestPlan, *TemplateMetadata, error)
	Delete(name string) error
	List() ([]string, error)
	ListByCategory(category string) ([]string, error)
	GetMetadata(name string) (*TemplateMetadata, error)
}

type FileTemplateStorage struct {
	baseDir string
	logger  *log.Logger
	mu      sync.RWMutex
}

func NewFileTemplateStorage(baseDir string, logger *log.Logger) *FileTemplateStorage {
	return &FileTemplateStorage{
		baseDir: baseDir,
		logger:  logger,
	}
}

func (fts *FileTemplateStorage) Save(name string, template *parser.TestPlan, metadata *TemplateMetadata) error {
	fts.mu.Lock()
	defer fts.mu.Unlock()

	// Create templates directory if it doesn't exist
	templateDir := filepath.Join(fts.baseDir, "templates")
	if err := os.MkdirAll(templateDir, 0755); err != nil {
		return fmt.Errorf("failed to create templates directory: %w", err)
	}

	// Save template file
	templatePath := filepath.Join(templateDir, name+".yaml")
	templateData, err := yaml.Marshal(template)
	if err != nil {
		return fmt.Errorf("failed to marshal template: %w", err)
	}

	if err := os.WriteFile(templatePath, templateData, 0644); err != nil {
		return fmt.Errorf("failed to save template: %w", err)
	}

	// Save metadata file
	metadataPath := filepath.Join(templateDir, name+".meta.yaml")
	metadataData, err := yaml.Marshal(metadata)
	if err != nil {
		return fmt.Errorf("failed to marshal metadata: %w", err)
	}

	if err := os.WriteFile(metadataPath, metadataData, 0644); err != nil {
		return fmt.Errorf("failed to save metadata: %w", err)
	}

	return nil
}

func (fts *FileTemplateStorage) Load(name string) (*parser.TestPlan, *TemplateMetadata, error) {
	fts.mu.RLock()
	defer fts.mu.RUnlock()

	templateDir := filepath.Join(fts.baseDir, "templates")

	// Load template
	templatePath := filepath.Join(templateDir, name+".yaml")
	templateData, err := os.ReadFile(templatePath)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to read template: %w", err)
	}

	var template parser.TestPlan
	if err := yaml.Unmarshal(templateData, &template); err != nil {
		return nil, nil, fmt.Errorf("failed to unmarshal template: %w", err)
	}

	// Load metadata
	metadataPath := filepath.Join(templateDir, name+".meta.yaml")
	metadataData, err := os.ReadFile(metadataPath)
	if err != nil {
		// Create default metadata if file doesn't exist
		metadata := &TemplateMetadata{
			Name:        name,
			Description: "Template",
			Category:    "general",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
			Version:     "1.0",
		}
		return &template, metadata, nil
	}

	var metadata TemplateMetadata
	if err := yaml.Unmarshal(metadataData, &metadata); err != nil {
		return nil, nil, fmt.Errorf("failed to unmarshal metadata: %w", err)
	}

	return &template, &metadata, nil
}

func (fts *FileTemplateStorage) Delete(name string) error {
	fts.mu.Lock()
	defer fts.mu.Unlock()

	templateDir := filepath.Join(fts.baseDir, "templates")
	templatePath := filepath.Join(templateDir, name+".yaml")
	metadataPath := filepath.Join(templateDir, name+".meta.yaml")

	// Remove template file
	if err := os.Remove(templatePath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete template: %w", err)
	}

	// Remove metadata file
	if err := os.Remove(metadataPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete metadata: %w", err)
	}

	return nil
}

func (fts *FileTemplateStorage) List() ([]string, error) {
	fts.mu.RLock()
	defer fts.mu.RUnlock()

	templateDir := filepath.Join(fts.baseDir, "templates")
	entries, err := os.ReadDir(templateDir)
	if err != nil {
		if os.IsNotExist(err) {
			return []string{}, nil
		}
		return nil, err
	}

	var templates []string
	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".yaml") && !strings.HasSuffix(entry.Name(), ".meta.yaml") {
			name := strings.TrimSuffix(entry.Name(), ".yaml")
			templates = append(templates, name)
		}
	}

	return templates, nil
}

func (fts *FileTemplateStorage) ListByCategory(category string) ([]string, error) {
	templates, err := fts.List()
	if err != nil {
		return nil, err
	}

	var filtered []string
	for _, name := range templates {
		metadata, err := fts.GetMetadata(name)
		if err != nil {
			continue
		}
		if metadata.Category == category {
			filtered = append(filtered, name)
		}
	}

	return filtered, nil
}

func (fts *FileTemplateStorage) GetMetadata(name string) (*TemplateMetadata, error) {
	fts.mu.RLock()
	defer fts.mu.RUnlock()

	templateDir := filepath.Join(fts.baseDir, "templates")
	metadataPath := filepath.Join(templateDir, name+".meta.yaml")

	metadataData, err := os.ReadFile(metadataPath)
	if err != nil {
		return nil, err
	}

	var metadata TemplateMetadata
	if err := yaml.Unmarshal(metadataData, &metadata); err != nil {
		return nil, err
	}

	return &metadata, nil
}

// Enhanced TemplateManager with comprehensive functionality
type TemplateManager struct {
	logger           *log.Logger
	templates        map[string]*parser.TestPlan
	metadata         map[string]*TemplateMetadata
	categories       map[string][]string
	storage          TemplateStorage
	validationEngine *validation.ValidationEngine
	mu               sync.RWMutex
}

// NewTemplateManager creates a new enhanced template manager
func NewTemplateManager(logger *log.Logger) *TemplateManager {
	if logger == nil {
		logger = log.Default()
	}

	// Create storage with default directory
	storage := NewFileTemplateStorage(".taskmaster", logger)

	tm := &TemplateManager{
		logger:           logger,
		templates:        make(map[string]*parser.TestPlan),
		metadata:         make(map[string]*TemplateMetadata),
		categories:       make(map[string][]string),
		storage:          storage,
		validationEngine: validation.NewValidationEngine(),
	}

	tm.loadDefaultTemplates()
	tm.loadStoredTemplates()
	return tm
}

// loadDefaultTemplates loads default templates
func (tm *TemplateManager) loadDefaultTemplates() {
	// Basic template
	basicPlan := &parser.TestPlan{
		Name:        "New Test Plan",
		Description: "A basic test plan template",
		Global: parser.Global{
			BaseURL: "https://api.example.com",
		},
		Scenarios: []parser.Scenario{
			{
				Name:        "Basic Scenario",
				Description: "A basic scenario template",
				Requests: []parser.Request{
					{
						Name:   "Basic Request",
						Method: "GET",
						URL:    "/health",
						Assertions: []parser.Assertion{
							{
								Type:     "status",
								Operator: "equals",
								Value:    "200",
							},
						},
					},
				},
			},
		},
	}

	tm.templates["basic"] = basicPlan
}

// loadStoredTemplates loads templates from storage
func (tm *TemplateManager) loadStoredTemplates() {
	templates, err := tm.storage.List()
	if err != nil {
		tm.logger.Printf("Failed to load stored templates: %v", err)
		return
	}

	for _, name := range templates {
		template, metadata, err := tm.storage.Load(name)
		if err != nil {
			tm.logger.Printf("Failed to load template '%s': %v", name, err)
			continue
		}

		tm.templates[name] = template
		tm.metadata[name] = metadata

		// Update categories
		if tm.categories[metadata.Category] == nil {
			tm.categories[metadata.Category] = []string{}
		}
		tm.categories[metadata.Category] = append(tm.categories[metadata.Category], name)
	}
}

// SaveTemplate saves a template with metadata
func (tm *TemplateManager) SaveTemplate(name string, template *parser.TestPlan, metadata *TemplateMetadata) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	// Validate template before saving
	if err := tm.validateTemplate(template); err != nil {
		return fmt.Errorf("template validation failed: %w", err)
	}

	// Set timestamps
	now := time.Now()
	if metadata.CreatedAt.IsZero() {
		metadata.CreatedAt = now
	}
	metadata.UpdatedAt = now

	// Save to storage
	if err := tm.storage.Save(name, template, metadata); err != nil {
		return err
	}

	// Update in-memory cache
	tm.templates[name] = template
	tm.metadata[name] = metadata

	// Update categories
	if tm.categories[metadata.Category] == nil {
		tm.categories[metadata.Category] = []string{}
	}

	// Remove from old category if exists
	for category, templates := range tm.categories {
		for i, tName := range templates {
			if tName == name && category != metadata.Category {
				tm.categories[category] = append(templates[:i], templates[i+1:]...)
				break
			}
		}
	}

	// Add to new category
	found := false
	for _, tName := range tm.categories[metadata.Category] {
		if tName == name {
			found = true
			break
		}
	}
	if !found {
		tm.categories[metadata.Category] = append(tm.categories[metadata.Category], name)
	}

	return nil
}

// DeleteTemplate deletes a template
func (tm *TemplateManager) DeleteTemplate(name string) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	// Check if template exists
	metadata, exists := tm.metadata[name]
	if !exists {
		return fmt.Errorf("template '%s' not found", name)
	}

	// Delete from storage
	if err := tm.storage.Delete(name); err != nil {
		return err
	}

	// Remove from in-memory cache
	delete(tm.templates, name)
	delete(tm.metadata, name)

	// Remove from categories
	if templates, exists := tm.categories[metadata.Category]; exists {
		for i, tName := range templates {
			if tName == name {
				tm.categories[metadata.Category] = append(templates[:i], templates[i+1:]...)
				break
			}
		}
	}

	return nil
}

// GetTemplate gets a template by name
func (tm *TemplateManager) GetTemplate(name string) (*parser.TestPlan, *TemplateMetadata, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	template, exists := tm.templates[name]
	if !exists {
		return nil, nil, fmt.Errorf("template '%s' not found", name)
	}

	metadata := tm.metadata[name]
	return template, metadata, nil
}

// GetTemplateMetadata gets metadata for a template
func (tm *TemplateManager) GetTemplateMetadata(name string) (*TemplateMetadata, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	metadata, exists := tm.metadata[name]
	if !exists {
		return nil, fmt.Errorf("template '%s' not found", name)
	}

	return metadata, nil
}

// GetCategories returns all categories
func (tm *TemplateManager) GetCategories() []string {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	categories := make([]string, 0, len(tm.categories))
	for category := range tm.categories {
		categories = append(categories, category)
	}
	return categories
}

// GetTemplatesByCategory returns templates in a category
func (tm *TemplateManager) GetTemplatesByCategory(category string) []string {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if templates, exists := tm.categories[category]; exists {
		result := make([]string, len(templates))
		copy(result, templates)
		return result
	}
	return []string{}
}

// CreateFromTemplateWithParams creates a test plan from template with parameter substitution
func (tm *TemplateManager) CreateFromTemplateWithParams(templateName string, params map[string]string) (*parser.TestPlan, error) {
	tm.mu.RLock()
	template, exists := tm.templates[templateName]
	tm.mu.RUnlock()

	if !exists {
		template = tm.templates["basic"] // fallback to basic
	}

	// Deep copy the template
	newPlan := tm.deepCopyTemplate(template)

	// Apply parameter substitution
	if params != nil {
		tm.applyParameterSubstitution(newPlan, params)
	}

	return newPlan, nil
}

// validateTemplate validates a template
func (tm *TemplateManager) validateTemplate(template *parser.TestPlan) error {
	if template == nil {
		return fmt.Errorf("template cannot be nil")
	}

	if template.Name == "" {
		return fmt.Errorf("template name cannot be empty")
	}

	// Use validation engine to validate structure
	result := tm.validationEngine.ValidateTestPlan(template)
	if !result.Valid {
		return fmt.Errorf("template validation failed: %d issues found", len(result.Issues))
	}

	return nil
}

// deepCopyTemplate creates a deep copy of a template
func (tm *TemplateManager) deepCopyTemplate(template *parser.TestPlan) *parser.TestPlan {
	newPlan := &parser.TestPlan{
		Name:        template.Name,
		Description: template.Description,
		Global:      template.Global,
		Scenarios:   make([]parser.Scenario, len(template.Scenarios)),
		Variables:   make([]parser.Variable, len(template.Variables)),
		Output:      template.Output,
	}

	// Deep copy scenarios
	for i, scenario := range template.Scenarios {
		newScenario := parser.Scenario{
			Name:        scenario.Name,
			Description: scenario.Description,
			Requests:    make([]parser.Request, len(scenario.Requests)),
		}

		// Deep copy requests
		for j, request := range scenario.Requests {
			newRequest := parser.Request{
				Name:       request.Name,
				Method:     request.Method,
				URL:        request.URL,
				Headers:    make(map[string]string),
				Body:       request.Body,
				Assertions: make([]parser.Assertion, len(request.Assertions)),
				Extract:    make([]parser.Extract, len(request.Extract)),
			}

			// Copy headers
			for k, v := range request.Headers {
				newRequest.Headers[k] = v
			}

			// Copy assertions
			copy(newRequest.Assertions, request.Assertions)

			// Copy extracts
			copy(newRequest.Extract, request.Extract)

			newScenario.Requests[j] = newRequest
		}

		newPlan.Scenarios[i] = newScenario
	}

	// Deep copy variables
	copy(newPlan.Variables, template.Variables)

	return newPlan
}

// applyParameterSubstitution applies parameter substitution to a template
func (tm *TemplateManager) applyParameterSubstitution(template *parser.TestPlan, params map[string]string) {
	// Substitute in template name and description
	template.Name = tm.substituteString(template.Name, params)
	template.Description = tm.substituteString(template.Description, params)

	// Substitute in global settings
	template.Global.BaseURL = tm.substituteString(template.Global.BaseURL, params)

	// Substitute in scenarios
	for i := range template.Scenarios {
		scenario := &template.Scenarios[i]
		scenario.Name = tm.substituteString(scenario.Name, params)
		scenario.Description = tm.substituteString(scenario.Description, params)

		// Substitute in requests
		for j := range scenario.Requests {
			request := &scenario.Requests[j]
			request.Name = tm.substituteString(request.Name, params)
			request.URL = tm.substituteString(request.URL, params)

			// Handle Body which is interface{} - only substitute if it's a string
			if bodyStr, ok := request.Body.(string); ok {
				request.Body = tm.substituteString(bodyStr, params)
			}

			// Substitute in headers
			for k, v := range request.Headers {
				request.Headers[k] = tm.substituteString(v, params)
			}

			// Substitute in assertions
			for k := range request.Assertions {
				assertion := &request.Assertions[k]
				// Handle Value which is interface{} - only substitute if it's a string
				if valueStr, ok := assertion.Value.(string); ok {
					assertion.Value = tm.substituteString(valueStr, params)
				}
			}
		}
	}

	// Substitute in variables
	for i := range template.Variables {
		variable := &template.Variables[i]
		// Handle Value which is interface{} - only substitute if it's a string
		if valueStr, ok := variable.Value.(string); ok {
			variable.Value = tm.substituteString(valueStr, params)
		}
	}
}

// substituteString performs parameter substitution in a string
func (tm *TemplateManager) substituteString(text string, params map[string]string) string {
	result := text
	for key, value := range params {
		placeholder := fmt.Sprintf("{{%s}}", key)
		result = strings.ReplaceAll(result, placeholder, value)
	}
	return result
}

// CreateFromTemplate creates a test plan from a template (legacy method for compatibility)
func (tm *TemplateManager) CreateFromTemplate(templateName string) *parser.TestPlan {
	plan, err := tm.CreateFromTemplateWithParams(templateName, nil)
	if err != nil {
		tm.logger.Printf("Failed to create from template '%s': %v", templateName, err)
		// Return basic template as fallback
		return tm.templates["basic"]
	}
	return plan
}

// GetTemplateNames returns available template names (legacy method for compatibility)
func (tm *TemplateManager) GetTemplateNames() []string {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	names := make([]string, 0, len(tm.templates))
	for name := range tm.templates {
		names = append(names, name)
	}
	return names
}

// Enhanced TestPlanDesigner with better state management
type TestPlanDesigner struct {
	app              *NeuralMeterApp
	logger           *log.Logger
	container        *fyne.Container
	treeWidget       *TestPlanTreeWidget
	formBuilder      *TreeFormAdapter
	validationEngine *validation.ValidationEngine
	currentTestPlan  *parser.TestPlan
	selectedNode     *TreeNode
	isDirty          bool
	changeTracker    *ChangeTracker
	templateManager  *TemplateManager

	// UI Components
	leftPanel  *fyne.Container
	rightPanel *fyne.Container
	splitter   *container.Split
	toolbar    *fyne.Container
	statusBar  *fyne.Container

	// Enhanced UI state management
	undoButton  *widget.Button
	redoButton  *widget.Button
	saveButton  *widget.Button
	statusLabel *widget.Label

	// Auto-save functionality
	autoSaveTimer    *time.Timer
	autoSaveInterval time.Duration

	// Event handlers
	onTestPlanChanged  func(*parser.TestPlan)
	onSelectionChanged func(*TreeNode)
	onValidationUpdate func(*validation.ValidationResult)

	// Thread safety
	mu sync.RWMutex
}

// TestPlanDesignerConfig holds configuration for the designer
type TestPlanDesignerConfig struct {
	EnableTemplates       bool
	EnableSyntaxHighlight bool
	EnableAutoComplete    bool
	EnableChangeTracking  bool
	MaxUndoLevels         int
	AutoSaveInterval      int // seconds, 0 to disable
	DefaultTemplate       string
}

// DefaultTestPlanDesignerConfig returns default configuration
func DefaultTestPlanDesignerConfig() *TestPlanDesignerConfig {
	return &TestPlanDesignerConfig{
		EnableTemplates:       true,
		EnableSyntaxHighlight: true,
		EnableAutoComplete:    true,
		EnableChangeTracking:  true,
		MaxUndoLevels:         50,
		AutoSaveInterval:      30,
		DefaultTemplate:       "basic",
	}
}

// NewTestPlanDesigner creates a new test plan designer
func NewTestPlanDesigner(app *NeuralMeterApp, logger *log.Logger) *TestPlanDesigner {
	if logger == nil {
		logger = log.Default()
	}

	tpd := &TestPlanDesigner{
		app:                app,
		logger:             logger,
		isDirty:            false,
		autoSaveInterval:   30 * time.Second, // Default auto-save every 30 seconds
		onTestPlanChanged:  func(*parser.TestPlan) {},
		onSelectionChanged: func(*TreeNode) {},
		onValidationUpdate: func(*validation.ValidationResult) {},
	}

	tpd.initializeComponents()
	tpd.setupLayout()
	tpd.setupEventHandlers()

	return tpd
}

// initializeComponents initializes all the components
func (tpd *TestPlanDesigner) initializeComponents() {
	// Initialize validation engine
	tpd.validationEngine = validation.NewValidationEngine()

	// Initialize change tracker with default max levels
	config := DefaultTestPlanDesignerConfig()
	tpd.changeTracker = NewChangeTracker(config.MaxUndoLevels)

	// Initialize template manager
	tpd.templateManager = NewTemplateManager(tpd.logger)

	// Initialize tree widget
	tpd.treeWidget = NewTestPlanTreeWidget(tpd.logger)

	// Initialize form builder
	tpd.formBuilder = NewTreeFormAdapter(tpd.validationEngine, tpd.logger)
}

// setupLayout creates the main layout structure
func (tpd *TestPlanDesigner) setupLayout() {
	// Create toolbar
	tpd.createToolbar()

	// Create left panel (tree navigation)
	tpd.leftPanel = container.NewBorder(
		widget.NewLabel("Test Plan Structure"),
		nil, nil, nil,
		tpd.treeWidget.GetContainer(),
	)

	// Create right panel (form editing)
	tpd.rightPanel = container.NewBorder(
		widget.NewLabel("Properties"),
		nil, nil, nil,
		tpd.formBuilder.GetContainer(),
	)

	// Create splitter
	tpd.splitter = container.NewHSplit(tpd.leftPanel, tpd.rightPanel)
	tpd.splitter.SetOffset(0.3) // 30% for tree, 70% for form

	// Create status bar
	tpd.createStatusBar()

	// Create main container
	tpd.container = container.NewBorder(
		tpd.toolbar,   // top
		tpd.statusBar, // bottom
		nil, nil,      // left, right
		tpd.splitter, // center
	)

	tpd.logger.Printf("TestPlanDesigner layout created")
}

// createToolbar creates the toolbar with enhanced button management
func (tpd *TestPlanDesigner) createToolbar() {
	// Create action buttons with references
	newButton := widget.NewButtonWithIcon("New", theme.DocumentCreateIcon(), tpd.createNewTestPlan)
	openButton := widget.NewButtonWithIcon("Open", theme.FolderOpenIcon(), tpd.openTestPlan)

	// Assign save button reference
	tpd.saveButton = widget.NewButtonWithIcon("Save", theme.DocumentSaveIcon(), tpd.saveTestPlan)
	tpd.saveButton.Disable() // Initially disabled

	// Create separator
	separator1 := widget.NewSeparator()

	// Assign undo/redo button references with keyboard shortcuts
	tpd.undoButton = widget.NewButtonWithIcon("Undo", theme.NavigateBackIcon(), tpd.undo)
	tpd.undoButton.Disable() // Initially disabled

	tpd.redoButton = widget.NewButtonWithIcon("Redo", theme.NavigateNextIcon(), tpd.redo)
	tpd.redoButton.Disable() // Initially disabled

	// Create separator
	separator2 := widget.NewSeparator()

	// Validation and template buttons
	validateButton := widget.NewButtonWithIcon("Validate", theme.ConfirmIcon(), tpd.validateTestPlan)
	templateButton := widget.NewButtonWithIcon("Templates", theme.MenuIcon(), tpd.showTemplateManager)

	// Create toolbar container
	tpd.toolbar = container.NewHBox(
		newButton,
		openButton,
		tpd.saveButton,
		separator1,
		tpd.undoButton,
		tpd.redoButton,
		separator2,
		validateButton,
		templateButton,
	)
}

// createStatusBar creates the status bar with enhanced state tracking
func (tpd *TestPlanDesigner) createStatusBar() {
	// Create status label reference
	tpd.statusLabel = widget.NewLabel("Ready")

	// Create validation status
	validationLabel := widget.NewLabel("No validation")

	// Create dirty state indicator
	dirtyLabel := widget.NewLabel("Saved")

	// Create status bar container
	tpd.statusBar = container.NewBorder(
		nil, nil,
		tpd.statusLabel,
		container.NewHBox(
			widget.NewSeparator(),
			dirtyLabel,
			widget.NewSeparator(),
			validationLabel,
		),
		nil,
	)
}

// setupEventHandlers sets up event handlers including keyboard shortcuts
func (tpd *TestPlanDesigner) setupEventHandlers() {
	// Set up tree selection handler
	tpd.treeWidget.SetOnSelectionChanged(tpd.handleSelectionChanged)

	// Set up form change handler
	tpd.formBuilder.SetOnChange(tpd.handleFieldChanged)
	tpd.formBuilder.SetOnValidation(func(valid bool, errors []string) {
		// Handle validation changes
		if valid {
			tpd.updateStatus("Validation passed")
		} else {
			tpd.updateStatus(fmt.Sprintf("Validation failed: %d errors", len(errors)))
		}
	})

	// Set up keyboard shortcuts
	tpd.setupKeyboardShortcuts()
}

// setupKeyboardShortcuts sets up keyboard shortcuts for common actions
func (tpd *TestPlanDesigner) setupKeyboardShortcuts() {
	// Note: In a real implementation, keyboard shortcuts would be set up
	// through the main window's event handling system
	// This is a placeholder for the keyboard shortcut setup

	// Common shortcuts:
	// Ctrl+Z / Cmd+Z: Undo
	// Ctrl+Y / Cmd+Y: Redo
	// Ctrl+S / Cmd+S: Save
	// Ctrl+N / Cmd+N: New
	// Ctrl+O / Cmd+O: Open

	tpd.logger.Printf("Keyboard shortcuts initialized (implementation pending)")
}

// GetContainer returns the main container for embedding in layouts
func (tpd *TestPlanDesigner) GetContainer() *fyne.Container {
	return tpd.container
}

// LoadTestPlan loads a test plan into the designer
func (tpd *TestPlanDesigner) LoadTestPlan(plan *parser.TestPlan) error {
	tpd.mu.Lock()
	defer tpd.mu.Unlock()

	if plan == nil {
		return fmt.Errorf("test plan cannot be nil")
	}

	// Validate the plan first
	result := tpd.validationEngine.Validate(plan)
	if !result.Valid && len(result.Issues) > 0 {
		// Show validation errors but allow loading
		tpd.showValidationDialog(result)
	}

	// Load into tree widget
	if err := tpd.treeWidget.LoadTestPlan(plan); err != nil {
		return fmt.Errorf("failed to load test plan into tree: %w", err)
	}

	// Store current plan
	tpd.currentTestPlan = plan
	tpd.setDirty(false)

	// Clear change tracker
	tpd.changeTracker.Clear()

	// Update status
	tpd.updateStatus(fmt.Sprintf("Loaded test plan: %s", plan.Name))

	// Trigger event
	if tpd.onTestPlanChanged != nil {
		tpd.onTestPlanChanged(plan)
	}

	tpd.logger.Printf("Test plan loaded: %s", plan.Name)
	return nil
}

// GetTestPlan returns the current test plan
func (tpd *TestPlanDesigner) GetTestPlan() *parser.TestPlan {
	tpd.mu.RLock()
	defer tpd.mu.RUnlock()
	return tpd.currentTestPlan
}

// handleSelectionChanged handles tree selection changes
func (tpd *TestPlanDesigner) handleSelectionChanged(node *TreeNode) {
	tpd.mu.Lock()
	tpd.selectedNode = node
	tpd.mu.Unlock()

	if tpd.onSelectionChanged != nil {
		tpd.onSelectionChanged(node)
	}
}

// handleFieldChanged handles form field changes
func (tpd *TestPlanDesigner) handleFieldChanged(field string, value interface{}) {
	tpd.mu.Lock()
	defer tpd.mu.Unlock()

	if tpd.selectedNode == nil {
		return
	}

	// Get old value for undo
	oldValue := tpd.selectedNode.GetFieldValue(field)

	// Create and execute command using new command pattern
	cmd := NewFieldUpdateCommand(tpd, tpd.selectedNode.GetID(), field, oldValue, value)

	if err := tpd.changeTracker.ExecuteCommand(cmd); err != nil {
		tpd.logger.Printf("Failed to execute field update command: %v", err)
		tpd.showErrorDialog("Update Failed", err)
		return
	}

	// Mark as dirty and update UI state
	tpd.setDirty(true)
	tpd.updateToolbarState()
	tpd.scheduleAutoSave()
}

// validateCurrentPlan validates the current test plan
func (tpd *TestPlanDesigner) validateCurrentPlan() {
	if tpd.currentTestPlan == nil {
		return
	}

	result := tpd.validationEngine.Validate(tpd.currentTestPlan)
	if tpd.onValidationUpdate != nil {
		tpd.onValidationUpdate(result)
	}
}

// Action handlers
func (tpd *TestPlanDesigner) createNewTestPlan() {
	if tpd.isDirty {
		tpd.showUnsavedChangesDialog(func() {
			tpd.doCreateNewTestPlan()
		})
		return
	}
	tpd.doCreateNewTestPlan()
}

func (tpd *TestPlanDesigner) doCreateNewTestPlan() {
	// Create a basic test plan template
	plan := tpd.templateManager.CreateFromTemplate("basic")
	if err := tpd.LoadTestPlan(plan); err != nil {
		tpd.showErrorDialog("Failed to create new test plan", err)
	}
}

func (tpd *TestPlanDesigner) openTestPlan() {
	dialog.ShowFileOpen(func(reader fyne.URIReadCloser, err error) {
		if err != nil {
			tpd.showErrorDialog("Failed to open file", err)
			return
		}
		if reader == nil {
			return
		}
		defer reader.Close()

		// Parse the file
		p := parser.NewParser()
		plan, err := p.ParseFile(reader.URI().Path())
		if err != nil {
			tpd.showErrorDialog("Failed to open test plan", err)
			return
		}

		if err := tpd.LoadTestPlan(plan); err != nil {
			tpd.showErrorDialog("Failed to load test plan", err)
		}
	}, tpd.app.GetWindow())
}

func (tpd *TestPlanDesigner) saveTestPlan() {
	if tpd.currentTestPlan == nil {
		tpd.showErrorDialog("No test plan to save", fmt.Errorf("no test plan loaded"))
		return
	}

	dialog.ShowFileSave(func(writer fyne.URIWriteCloser, err error) {
		if err != nil {
			tpd.showErrorDialog("Failed to save file", err)
			return
		}
		if writer == nil {
			return
		}
		defer writer.Close()

		// Convert to YAML and save
		// This would need implementation in the parser package
		tpd.updateStatus("Test plan saved")
		tpd.setDirty(false)
	}, tpd.app.GetWindow())
}

func (tpd *TestPlanDesigner) undo() {
	if err := tpd.changeTracker.UndoCommand(); err != nil {
		// Fallback to legacy undo if command pattern fails
		if change := tpd.changeTracker.Undo(); change != nil {
			tpd.applyChange(change, true)
		} else {
			tpd.logger.Printf("Undo failed: %v", err)
		}
	}

	tpd.updateToolbarState()
	tpd.updateStatus(fmt.Sprintf("Undone: %s", tpd.changeTracker.GetRedoDescription()))
}

func (tpd *TestPlanDesigner) redo() {
	if err := tpd.changeTracker.RedoCommand(); err != nil {
		// Fallback to legacy redo if command pattern fails
		if change := tpd.changeTracker.Redo(); change != nil {
			tpd.applyChange(change, false)
		} else {
			tpd.logger.Printf("Redo failed: %v", err)
		}
	}

	tpd.updateToolbarState()
	tpd.updateStatus(fmt.Sprintf("Redone: %s", tpd.changeTracker.GetUndoDescription()))
}

func (tpd *TestPlanDesigner) validateTestPlan() {
	if tpd.currentTestPlan == nil {
		tpd.showErrorDialog("No test plan to validate", fmt.Errorf("no test plan loaded"))
		return
	}

	result := tpd.validationEngine.Validate(tpd.currentTestPlan)
	tpd.showValidationDialog(result)
}

func (tpd *TestPlanDesigner) showTemplateManager() {
	// Create template manager dialog
	dialog := NewTemplateManagerDialog(tpd.templateManager, tpd.app.GetWindow(), tpd.logger)

	// Set callback for when a template is selected for use
	dialog.SetOnTemplateSelected(func(templateName string, params map[string]string) {
		plan, err := tpd.templateManager.CreateFromTemplateWithParams(templateName, params)
		if err != nil {
			tpd.showErrorDialog("Template Error", err)
			return
		}

		// Load the new test plan
		if err := tpd.LoadTestPlan(plan); err != nil {
			tpd.showErrorDialog("Load Error", err)
			return
		}

		tpd.updateStatus(fmt.Sprintf("Created test plan from template: %s", templateName))
	})

	// Show the dialog
	dialog.Show()
}

// Helper methods
func (tpd *TestPlanDesigner) setDirty(dirty bool) {
	tpd.isDirty = dirty
	tpd.updateToolbarState()

	if dirty {
		tpd.scheduleAutoSave()
	} else {
		// Cancel auto-save timer when clean
		if tpd.autoSaveTimer != nil {
			tpd.autoSaveTimer.Stop()
			tpd.autoSaveTimer = nil
		}
	}
}

func (tpd *TestPlanDesigner) updateStatus(message string) {
	timestamp := time.Now().Format("15:04:05")
	fullMessage := fmt.Sprintf("[%s] %s", timestamp, message)

	if tpd.statusLabel != nil {
		tpd.statusLabel.SetText(fullMessage)
	}

	tpd.logger.Printf("Status: %s", message)
}

func (tpd *TestPlanDesigner) updateValidationStatus(result *validation.ValidationResult) {
	// Update validation status in the status bar
	if result.Valid {
		tpd.updateStatus("Validation: Passed")
	} else {
		tpd.updateStatus(fmt.Sprintf("Validation: %d issues", len(result.Issues)))
	}
}

func (tpd *TestPlanDesigner) applyChange(change *Change, reverse bool) {
	node := tpd.findNodeByID(change.NodeID)
	if node == nil {
		tpd.logger.Printf("Node not found for change: %s", change.NodeID)
		return
	}

	var targetValue interface{}
	if reverse {
		targetValue = change.OldValue
	} else {
		targetValue = change.NewValue
	}

	switch change.Type {
	case ChangeTypeFieldUpdate:
		node.SetFieldValue(change.Field, targetValue)
		tpd.treeWidget.RefreshNode(node)

		// Update form if this node is currently selected
		if tpd.selectedNode != nil && tpd.selectedNode.GetID() == change.NodeID {
			tpd.formBuilder.LoadNode(node)
		}

	case ChangeTypeNodeAdd:
		// TODO: Implement node addition logic
		tpd.logger.Printf("Node add/remove not yet implemented")

	case ChangeTypeNodeRemove:
		// TODO: Implement node removal logic
		tpd.logger.Printf("Node add/remove not yet implemented")

	case ChangeTypeNodeMove:
		// TODO: Implement node move logic
		tpd.logger.Printf("Node move not yet implemented")
	}

	// Trigger validation after applying change
	tpd.validateCurrentPlan()
}

func (tpd *TestPlanDesigner) showErrorDialog(title string, err error) {
	dialog.ShowError(err, tpd.app.GetWindow())
}

func (tpd *TestPlanDesigner) showValidationDialog(result *validation.ValidationResult) {
	// Create a dialog showing validation results
	// This would show errors, warnings, and suggestions
}

func (tpd *TestPlanDesigner) showUnsavedChangesDialog(onDiscard func()) {
	dialog.ShowConfirm(
		"Unsaved Changes",
		"You have unsaved changes. Do you want to discard them?",
		func(confirmed bool) {
			if confirmed && onDiscard != nil {
				onDiscard()
			}
		},
		tpd.app.GetWindow(),
	)
}

// SetOnTestPlanChanged sets the callback for test plan changes
func (tpd *TestPlanDesigner) SetOnTestPlanChanged(callback func(*parser.TestPlan)) {
	tpd.onTestPlanChanged = callback
}

// SetOnSelectionChanged sets the callback for selection changes
func (tpd *TestPlanDesigner) SetOnSelectionChanged(callback func(*TreeNode)) {
	tpd.onSelectionChanged = callback
}

// SetOnValidationUpdate sets the callback for validation updates
func (tpd *TestPlanDesigner) SetOnValidationUpdate(callback func(*validation.ValidationResult)) {
	tpd.onValidationUpdate = callback
}

// IsModified returns true if the test plan has unsaved changes
func (tpd *TestPlanDesigner) IsModified() bool {
	tpd.mu.RLock()
	defer tpd.mu.RUnlock()
	return tpd.isDirty
}

// CanUndo returns true if undo is available
func (tpd *TestPlanDesigner) CanUndo() bool {
	return tpd.changeTracker.CanUndo()
}

// CanRedo returns true if redo is available
func (tpd *TestPlanDesigner) CanRedo() bool {
	return tpd.changeTracker.CanRedo()
}

// findNodeByID finds a node by its ID in the tree
func (tpd *TestPlanDesigner) findNodeByID(nodeID string) *TreeNode {
	if tpd.treeWidget == nil {
		return nil
	}

	// Use the private getNodeByID method through the tree widget's nodeMap
	tpd.treeWidget.mu.RLock()
	defer tpd.treeWidget.mu.RUnlock()

	if node, exists := tpd.treeWidget.nodeMap[nodeID]; exists {
		return node
	}
	return nil
}

// updateToolbarState updates the state of toolbar buttons based on current state
func (tpd *TestPlanDesigner) updateToolbarState() {
	if tpd.undoButton != nil {
		if tpd.changeTracker.CanUndo() {
			tpd.undoButton.Enable()
		} else {
			tpd.undoButton.Disable()
		}
		undoDesc := tpd.changeTracker.GetUndoDescription()
		if undoDesc != "" {
			tpd.undoButton.SetText(fmt.Sprintf("Undo: %s", undoDesc))
		} else {
			tpd.undoButton.SetText("Undo")
		}
	}

	if tpd.redoButton != nil {
		if tpd.changeTracker.CanRedo() {
			tpd.redoButton.Enable()
		} else {
			tpd.redoButton.Disable()
		}
		redoDesc := tpd.changeTracker.GetRedoDescription()
		if redoDesc != "" {
			tpd.redoButton.SetText(fmt.Sprintf("Redo: %s", redoDesc))
		} else {
			tpd.redoButton.SetText("Redo")
		}
	}

	if tpd.saveButton != nil {
		if tpd.isDirty {
			tpd.saveButton.Enable()
		} else {
			tpd.saveButton.Disable()
		}
	}

	// Update status bar with dirty state
	if tpd.statusLabel != nil {
		if tpd.isDirty {
			tpd.statusLabel.SetText("Modified")
		} else {
			tpd.statusLabel.SetText("Saved")
		}
	}
}

// scheduleAutoSave schedules an auto-save operation
func (tpd *TestPlanDesigner) scheduleAutoSave() {
	if tpd.autoSaveInterval <= 0 {
		return // Auto-save disabled
	}

	// Cancel existing timer
	if tpd.autoSaveTimer != nil {
		tpd.autoSaveTimer.Stop()
	}

	// Schedule new auto-save
	tpd.autoSaveTimer = time.AfterFunc(tpd.autoSaveInterval, func() {
		if tpd.isDirty {
			tpd.autoSave()
		}
	})
}

// autoSave performs an automatic save operation
func (tpd *TestPlanDesigner) autoSave() {
	tpd.mu.Lock()
	defer tpd.mu.Unlock()

	if !tpd.isDirty || tpd.currentTestPlan == nil {
		return
	}

	// TODO: Implement actual auto-save to temporary file
	tpd.logger.Printf("Auto-save triggered (implementation pending)")
	tpd.updateStatus("Auto-saved")
}

// TemplateManagerDialog provides a comprehensive template management interface
type TemplateManagerDialog struct {
	templateManager *TemplateManager
	window          fyne.Window
	logger          *log.Logger
	dialog          *dialog.CustomDialog

	// UI Components
	categoryList  *widget.List
	templateList  *widget.List
	detailsCard   *widget.Card
	previewCard   *widget.Card
	parameterForm *fyne.Container

	// State
	selectedCategory   string
	selectedTemplate   string
	parameters         map[string]*widget.Entry
	onTemplateSelected func(string, map[string]string)

	// Data
	categories []string
	templates  []string
	metadata   *TemplateMetadata
}

// NewTemplateManagerDialog creates a new template manager dialog
func NewTemplateManagerDialog(tm *TemplateManager, parent fyne.Window, logger *log.Logger) *TemplateManagerDialog {
	if logger == nil {
		logger = log.Default()
	}

	tmd := &TemplateManagerDialog{
		templateManager:    tm,
		window:             parent,
		logger:             logger,
		parameters:         make(map[string]*widget.Entry),
		onTemplateSelected: func(string, map[string]string) {},
	}

	tmd.setupUI()
	return tmd
}

// setupUI creates the template manager dialog UI
func (tmd *TemplateManagerDialog) setupUI() {
	// Load categories and templates
	tmd.categories = tmd.templateManager.GetCategories()
	if len(tmd.categories) == 0 {
		tmd.categories = []string{"general"}
	}

	// Create category list
	tmd.categoryList = widget.NewList(
		func() int { return len(tmd.categories) },
		func() fyne.CanvasObject {
			return widget.NewLabel("Category")
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			label := obj.(*widget.Label)
			if id < len(tmd.categories) {
				label.SetText(tmd.categories[id])
			}
		},
	)

	tmd.categoryList.OnSelected = func(id widget.ListItemID) {
		if id < len(tmd.categories) {
			tmd.selectedCategory = tmd.categories[id]
			tmd.loadTemplatesForCategory(tmd.selectedCategory)
		}
	}

	// Create template list
	tmd.templateList = widget.NewList(
		func() int { return len(tmd.templates) },
		func() fyne.CanvasObject {
			return widget.NewLabel("Template")
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			label := obj.(*widget.Label)
			if id < len(tmd.templates) {
				label.SetText(tmd.templates[id])
			}
		},
	)

	tmd.templateList.OnSelected = func(id widget.ListItemID) {
		if id < len(tmd.templates) {
			tmd.selectedTemplate = tmd.templates[id]
			tmd.loadTemplateDetails(tmd.selectedTemplate)
		}
	}

	// Create details card
	tmd.detailsCard = widget.NewCard("Template Details", "", widget.NewLabel("Select a template to view details"))

	// Create preview card
	tmd.previewCard = widget.NewCard("Preview", "", widget.NewLabel("Select a template to preview"))

	// Create parameter form container
	tmd.parameterForm = container.NewVBox()

	// Create buttons
	useButton := widget.NewButton("Use Template", func() {
		if tmd.selectedTemplate != "" {
			params := tmd.getParameterValues()
			tmd.onTemplateSelected(tmd.selectedTemplate, params)
			tmd.dialog.Hide()
		}
	})

	saveAsButton := widget.NewButton("Save As New", func() {
		tmd.showSaveAsDialog()
	})

	deleteButton := widget.NewButton("Delete", func() {
		if tmd.selectedTemplate != "" {
			tmd.showDeleteConfirmation()
		}
	})

	refreshButton := widget.NewButton("Refresh", func() {
		tmd.refreshTemplates()
	})

	closeButton := widget.NewButton("Close", func() {
		tmd.dialog.Hide()
	})

	// Layout the dialog
	leftPanel := container.NewBorder(
		widget.NewCard("Categories", "", tmd.categoryList),
		nil, nil, nil,
		widget.NewCard("Templates", "", tmd.templateList),
	)

	rightPanel := container.NewVBox(
		tmd.detailsCard,
		widget.NewCard("Parameters", "", container.NewScroll(tmd.parameterForm)),
		tmd.previewCard,
	)

	buttonBar := container.NewHBox(
		useButton,
		widget.NewSeparator(),
		saveAsButton,
		deleteButton,
		widget.NewSeparator(),
		refreshButton,
		layout.NewSpacer(),
		closeButton,
	)

	content := container.NewBorder(
		nil,
		buttonBar,
		leftPanel,
		nil,
		rightPanel,
	)

	// Create the dialog
	tmd.dialog = dialog.NewCustom("Template Manager", "", content, tmd.window)
	tmd.dialog.Resize(fyne.NewSize(800, 600))

	// Load initial data
	if len(tmd.categories) > 0 {
		tmd.categoryList.Select(0)
	}
}

// loadTemplatesForCategory loads templates for the selected category
func (tmd *TemplateManagerDialog) loadTemplatesForCategory(category string) {
	tmd.templates = tmd.templateManager.GetTemplatesByCategory(category)
	tmd.templateList.Refresh()

	// Clear selection
	tmd.selectedTemplate = ""
	tmd.detailsCard.SetContent(widget.NewLabel("Select a template to view details"))
	tmd.previewCard.SetContent(widget.NewLabel("Select a template to preview"))
	tmd.parameterForm.RemoveAll()
}

// loadTemplateDetails loads details for the selected template
func (tmd *TemplateManagerDialog) loadTemplateDetails(templateName string) {
	metadata, err := tmd.templateManager.GetTemplateMetadata(templateName)
	if err != nil {
		tmd.logger.Printf("Failed to load template metadata: %v", err)
		return
	}

	tmd.metadata = metadata

	// Create details content
	details := container.NewVBox(
		widget.NewRichTextFromMarkdown(fmt.Sprintf("**Name:** %s", metadata.Name)),
		widget.NewRichTextFromMarkdown(fmt.Sprintf("**Description:** %s", metadata.Description)),
		widget.NewRichTextFromMarkdown(fmt.Sprintf("**Category:** %s", metadata.Category)),
		widget.NewRichTextFromMarkdown(fmt.Sprintf("**Version:** %s", metadata.Version)),
		widget.NewRichTextFromMarkdown(fmt.Sprintf("**Author:** %s", metadata.Author)),
		widget.NewRichTextFromMarkdown(fmt.Sprintf("**Created:** %s", metadata.CreatedAt.Format("2006-01-02 15:04"))),
		widget.NewRichTextFromMarkdown(fmt.Sprintf("**Updated:** %s", metadata.UpdatedAt.Format("2006-01-02 15:04"))),
	)

	if len(metadata.Tags) > 0 {
		tags := strings.Join(metadata.Tags, ", ")
		details.Add(widget.NewRichTextFromMarkdown(fmt.Sprintf("**Tags:** %s", tags)))
	}

	tmd.detailsCard.SetContent(container.NewScroll(details))

	// Load parameter form
	tmd.loadParameterForm(templateName)

	// Load preview
	tmd.loadTemplatePreview(templateName)
}

// loadParameterForm creates parameter input form
func (tmd *TemplateManagerDialog) loadParameterForm(templateName string) {
	tmd.parameterForm.RemoveAll()
	tmd.parameters = make(map[string]*widget.Entry)

	// Get template to analyze for parameters
	template, _, err := tmd.templateManager.GetTemplate(templateName)
	if err != nil {
		return
	}

	// Extract parameters from template (look for {{param}} patterns)
	params := tmd.extractParameters(template)

	if len(params) == 0 {
		tmd.parameterForm.Add(widget.NewLabel("No parameters found in this template"))
		return
	}

	tmd.parameterForm.Add(widget.NewLabel("Template Parameters:"))

	for _, param := range params {
		entry := widget.NewEntry()
		entry.SetPlaceHolder(fmt.Sprintf("Enter value for %s", param))

		tmd.parameters[param] = entry

		paramContainer := container.NewBorder(
			nil, nil,
			widget.NewLabel(param+":"), nil,
			entry,
		)
		tmd.parameterForm.Add(paramContainer)
	}

	tmd.parameterForm.Refresh()
}

// extractParameters extracts parameter names from template
func (tmd *TemplateManagerDialog) extractParameters(template *parser.TestPlan) []string {
	paramSet := make(map[string]bool)

	// Helper function to extract from string
	extractFromString := func(text string) {
		// Find all {{param}} patterns
		start := 0
		for {
			startIdx := strings.Index(text[start:], "{{")
			if startIdx == -1 {
				break
			}
			startIdx += start

			endIdx := strings.Index(text[startIdx:], "}}")
			if endIdx == -1 {
				break
			}
			endIdx += startIdx

			param := text[startIdx+2 : endIdx]
			param = strings.TrimSpace(param)
			if param != "" {
				paramSet[param] = true
			}

			start = endIdx + 2
		}
	}

	// Extract from various template fields
	extractFromString(template.Name)
	extractFromString(template.Description)
	extractFromString(template.Global.BaseURL)

	for _, scenario := range template.Scenarios {
		extractFromString(scenario.Name)
		extractFromString(scenario.Description)

		for _, request := range scenario.Requests {
			extractFromString(request.Name)
			extractFromString(request.URL)

			if bodyStr, ok := request.Body.(string); ok {
				extractFromString(bodyStr)
			}

			for _, v := range request.Headers {
				extractFromString(v)
			}
		}
	}

	// Convert to sorted slice
	params := make([]string, 0, len(paramSet))
	for param := range paramSet {
		params = append(params, param)
	}

	return params
}

// loadTemplatePreview creates a preview of the template
func (tmd *TemplateManagerDialog) loadTemplatePreview(templateName string) {
	template, _, err := tmd.templateManager.GetTemplate(templateName)
	if err != nil {
		return
	}

	// Create a simple preview
	preview := container.NewVBox(
		widget.NewRichTextFromMarkdown(fmt.Sprintf("**Test Plan:** %s", template.Name)),
		widget.NewRichTextFromMarkdown(fmt.Sprintf("**Scenarios:** %d", len(template.Scenarios))),
	)

	if len(template.Scenarios) > 0 {
		preview.Add(widget.NewLabel("Scenarios:"))
		for i, scenario := range template.Scenarios {
			if i >= 3 { // Limit preview to first 3 scenarios
				preview.Add(widget.NewLabel("..."))
				break
			}
			preview.Add(widget.NewLabel(fmt.Sprintf("  • %s (%d requests)", scenario.Name, len(scenario.Requests))))
		}
	}

	tmd.previewCard.SetContent(container.NewScroll(preview))
}

// getParameterValues gets the current parameter values from the form
func (tmd *TemplateManagerDialog) getParameterValues() map[string]string {
	params := make(map[string]string)
	for name, entry := range tmd.parameters {
		if entry.Text != "" {
			params[name] = entry.Text
		}
	}
	return params
}

// showSaveAsDialog shows the save as new template dialog
func (tmd *TemplateManagerDialog) showSaveAsDialog() {
	if tmd.selectedTemplate == "" {
		return
	}

	// Get current template
	template, _, err := tmd.templateManager.GetTemplate(tmd.selectedTemplate)
	if err != nil {
		tmd.logger.Printf("Failed to get template: %v", err)
		return
	}

	// Create form for new template metadata
	nameEntry := widget.NewEntry()
	nameEntry.SetText(template.Name + " Copy")

	descEntry := widget.NewEntry()
	descEntry.SetText("Copy of " + template.Description)

	categoryEntry := widget.NewEntry()
	categoryEntry.SetText(tmd.selectedCategory)

	authorEntry := widget.NewEntry()
	authorEntry.SetText("User")

	form := container.NewVBox(
		widget.NewLabel("Save Template As:"),
		container.NewBorder(nil, nil, widget.NewLabel("Name:"), nil, nameEntry),
		container.NewBorder(nil, nil, widget.NewLabel("Description:"), nil, descEntry),
		container.NewBorder(nil, nil, widget.NewLabel("Category:"), nil, categoryEntry),
		container.NewBorder(nil, nil, widget.NewLabel("Author:"), nil, authorEntry),
	)

	saveDialog := dialog.NewCustomConfirm("Save Template", "Save", "Cancel", form, func(confirmed bool) {
		if confirmed {
			metadata := &TemplateMetadata{
				Name:        nameEntry.Text,
				Description: descEntry.Text,
				Category:    categoryEntry.Text,
				Author:      authorEntry.Text,
				Version:     "1.0",
			}

			if err := tmd.templateManager.SaveTemplate(nameEntry.Text, template, metadata); err != nil {
				dialog.ShowError(err, tmd.window)
			} else {
				tmd.refreshTemplates()
			}
		}
	}, tmd.window)

	saveDialog.Show()
}

// showDeleteConfirmation shows delete confirmation dialog
func (tmd *TemplateManagerDialog) showDeleteConfirmation() {
	if tmd.selectedTemplate == "" {
		return
	}

	dialog.ShowConfirm("Delete Template",
		fmt.Sprintf("Are you sure you want to delete the template '%s'?", tmd.selectedTemplate),
		func(confirmed bool) {
			if confirmed {
				if err := tmd.templateManager.DeleteTemplate(tmd.selectedTemplate); err != nil {
					dialog.ShowError(err, tmd.window)
				} else {
					tmd.refreshTemplates()
				}
			}
		}, tmd.window)
}

// refreshTemplates refreshes the template lists
func (tmd *TemplateManagerDialog) refreshTemplates() {
	tmd.categories = tmd.templateManager.GetCategories()
	tmd.categoryList.Refresh()

	if tmd.selectedCategory != "" {
		tmd.loadTemplatesForCategory(tmd.selectedCategory)
	}
}

// SetOnTemplateSelected sets the callback for template selection
func (tmd *TemplateManagerDialog) SetOnTemplateSelected(callback func(string, map[string]string)) {
	tmd.onTemplateSelected = callback
}

// Show displays the template manager dialog
func (tmd *TemplateManagerDialog) Show() {
	tmd.dialog.Show()
}
