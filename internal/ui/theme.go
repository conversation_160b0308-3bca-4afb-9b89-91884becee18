//go:build gui
// +build gui

package ui

import (
	"fmt"
	"image/color"
	"log"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

// ThemeManager manages themes for the NeuralMeter application
type ThemeManager struct {
	app          fyne.App
	currentTheme string
	customThemes map[string]fyne.Theme
	logger       *log.Logger
}

// NewThemeManager creates a new theme manager
func NewThemeManager(app fyne.App, logger *log.Logger) *ThemeManager {
	if logger == nil {
		logger = log.Default()
	}

	tm := &ThemeManager{
		app:          app,
		currentTheme: "dark",
		customThemes: make(map[string]fyne.Theme),
		logger:       logger,
	}

	// Register custom themes
	tm.registerCustomThemes()

	return tm
}

// registerCustomThemes registers all available custom themes
func (tm *ThemeManager) registerCustomThemes() {
	// Register Material Design Dark theme
	tm.customThemes["material-dark"] = &MaterialDarkTheme{}

	// Register Material Design Light theme
	tm.customThemes["material-light"] = &MaterialLightTheme{}

	// Register NeuralMeter custom theme
	tm.customThemes["neural-dark"] = &NeuralMeterTheme{}
}

// SetTheme sets the current theme by name
func (tm *ThemeManager) SetTheme(themeName string) error {
	switch themeName {
	case "light":
		tm.app.Settings().SetTheme(theme.LightTheme())
		tm.currentTheme = "light"
	case "dark":
		tm.app.Settings().SetTheme(theme.DarkTheme())
		tm.currentTheme = "dark"
	case "default":
		tm.app.Settings().SetTheme(theme.DefaultTheme())
		tm.currentTheme = "default"
	default:
		// Check custom themes
		if customTheme, exists := tm.customThemes[themeName]; exists {
			tm.app.Settings().SetTheme(customTheme)
			tm.currentTheme = themeName
		} else {
			return fmt.Errorf("theme '%s' not found", themeName)
		}
	}

	tm.logger.Printf("Theme changed to: %s", themeName)
	return nil
}

// GetCurrentTheme returns the name of the current theme
func (tm *ThemeManager) GetCurrentTheme() string {
	return tm.currentTheme
}

// GetAvailableThemes returns a list of all available theme names
func (tm *ThemeManager) GetAvailableThemes() []string {
	themes := []string{"light", "dark", "default"}

	// Add custom themes
	for name := range tm.customThemes {
		themes = append(themes, name)
	}

	return themes
}

// CreateThemeSwitcher creates a widget for switching themes
func (tm *ThemeManager) CreateThemeSwitcher() *widget.Select {
	themes := tm.GetAvailableThemes()

	switcher := widget.NewSelect(themes, func(selected string) {
		if err := tm.SetTheme(selected); err != nil {
			tm.logger.Printf("Error setting theme %s: %v", selected, err)
		}
	})

	switcher.SetSelected(tm.currentTheme)
	return switcher
}

// MaterialDarkTheme implements a Material Design dark theme
type MaterialDarkTheme struct{}

var _ fyne.Theme = (*MaterialDarkTheme)(nil)

func (m *MaterialDarkTheme) Color(name fyne.ThemeColorName, variant fyne.ThemeVariant) color.Color {
	switch name {
	case theme.ColorNameBackground:
		return color.NRGBA{R: 0x12, G: 0x12, B: 0x12, A: 0xff} // Material Dark Surface
	case theme.ColorNameButton:
		return color.NRGBA{R: 0x2D, G: 0x2D, B: 0x2D, A: 0xff} // Material Dark Surface Variant
	case theme.ColorNameDisabledButton:
		return color.NRGBA{R: 0x1E, G: 0x1E, B: 0x1E, A: 0xff}
	case theme.ColorNameForeground:
		return color.NRGBA{R: 0xE3, G: 0xE3, B: 0xE3, A: 0xff} // Material Dark On Surface
	case theme.ColorNameHover:
		return color.NRGBA{R: 0x3F, G: 0x51, B: 0xB5, A: 0x3D} // Material Primary with Alpha
	case theme.ColorNameInputBackground:
		return color.NRGBA{R: 0x2D, G: 0x2D, B: 0x2D, A: 0xff}
	case theme.ColorNamePrimary:
		return color.NRGBA{R: 0x6C, G: 0x7B, B: 0x7F, A: 0xff} // Material Primary Blue
	case theme.ColorNameScrollBar:
		return color.NRGBA{R: 0x48, G: 0x48, B: 0x48, A: 0xff}
	case theme.ColorNameSeparator:
		return color.NRGBA{R: 0x48, G: 0x48, B: 0x48, A: 0xff}
	case theme.ColorNameShadow:
		return color.NRGBA{R: 0x00, G: 0x00, B: 0x00, A: 0x66}
	default:
		return theme.DefaultTheme().Color(name, variant)
	}
}

func (m *MaterialDarkTheme) Icon(name fyne.ThemeIconName) fyne.Resource {
	return theme.DefaultTheme().Icon(name)
}

func (m *MaterialDarkTheme) Font(style fyne.TextStyle) fyne.Resource {
	return theme.DefaultTheme().Font(style)
}

func (m *MaterialDarkTheme) Size(name fyne.ThemeSizeName) float32 {
	switch name {
	case theme.SizeNameText:
		return 14
	case theme.SizeNameInputBorder:
		return 1
	case theme.SizeNamePadding:
		return 8
	case theme.SizeNameScrollBar:
		return 12
	case theme.SizeNameScrollBarSmall:
		return 8
	default:
		return theme.DefaultTheme().Size(name)
	}
}

// MaterialLightTheme implements a Material Design light theme
type MaterialLightTheme struct{}

var _ fyne.Theme = (*MaterialLightTheme)(nil)

func (m *MaterialLightTheme) Color(name fyne.ThemeColorName, variant fyne.ThemeVariant) color.Color {
	switch name {
	case theme.ColorNameBackground:
		return color.NRGBA{R: 0xFF, G: 0xFF, B: 0xFF, A: 0xff} // Material Light Surface
	case theme.ColorNameButton:
		return color.NRGBA{R: 0xF5, G: 0xF5, B: 0xF5, A: 0xff} // Material Light Surface Variant
	case theme.ColorNameDisabledButton:
		return color.NRGBA{R: 0xE0, G: 0xE0, B: 0xE0, A: 0xff}
	case theme.ColorNameForeground:
		return color.NRGBA{R: 0x1C, G: 0x1C, B: 0x1C, A: 0xff} // Material Light On Surface
	case theme.ColorNameHover:
		return color.NRGBA{R: 0x3F, G: 0x51, B: 0xB5, A: 0x1F} // Material Primary with Alpha
	case theme.ColorNameInputBackground:
		return color.NRGBA{R: 0xF8, G: 0xF9, B: 0xFA, A: 0xff}
	case theme.ColorNamePrimary:
		return color.NRGBA{R: 0x3F, G: 0x51, B: 0xB5, A: 0xff} // Material Primary Blue
	case theme.ColorNameScrollBar:
		return color.NRGBA{R: 0xC0, G: 0xC0, B: 0xC0, A: 0xff}
	case theme.ColorNameSeparator:
		return color.NRGBA{R: 0xE0, G: 0xE0, B: 0xE0, A: 0xff}
	case theme.ColorNameShadow:
		return color.NRGBA{R: 0x00, G: 0x00, B: 0x00, A: 0x33}
	default:
		return theme.DefaultTheme().Color(name, variant)
	}
}

func (m *MaterialLightTheme) Icon(name fyne.ThemeIconName) fyne.Resource {
	return theme.DefaultTheme().Icon(name)
}

func (m *MaterialLightTheme) Font(style fyne.TextStyle) fyne.Resource {
	return theme.DefaultTheme().Font(style)
}

func (m *MaterialLightTheme) Size(name fyne.ThemeSizeName) float32 {
	switch name {
	case theme.SizeNameText:
		return 14
	case theme.SizeNameInputBorder:
		return 1
	case theme.SizeNamePadding:
		return 8
	case theme.SizeNameScrollBar:
		return 12
	case theme.SizeNameScrollBarSmall:
		return 8
	default:
		return theme.DefaultTheme().Size(name)
	}
}

// NeuralMeterTheme implements a custom theme for NeuralMeter
type NeuralMeterTheme struct{}

var _ fyne.Theme = (*NeuralMeterTheme)(nil)

func (n *NeuralMeterTheme) Color(name fyne.ThemeColorName, variant fyne.ThemeVariant) color.Color {
	switch name {
	case theme.ColorNameBackground:
		return color.NRGBA{R: 0x0D, G: 0x1B, B: 0x2A, A: 0xff} // Deep Neural Blue
	case theme.ColorNameButton:
		return color.NRGBA{R: 0x1E, G: 0x3A, B: 0x5F, A: 0xff} // Neural Button Blue
	case theme.ColorNameDisabledButton:
		return color.NRGBA{R: 0x15, G: 0x25, B: 0x3A, A: 0xff}
	case theme.ColorNameForeground:
		return color.NRGBA{R: 0xE8, G: 0xF4, B: 0xFD, A: 0xff} // Neural Light Blue
	case theme.ColorNameHover:
		return color.NRGBA{R: 0x00, G: 0xE6, B: 0x76, A: 0x4D} // Neural Green with Alpha
	case theme.ColorNameInputBackground:
		return color.NRGBA{R: 0x1A, G: 0x2E, B: 0x42, A: 0xff}
	case theme.ColorNamePrimary:
		return color.NRGBA{R: 0x00, G: 0xE6, B: 0x76, A: 0xff} // Neural Accent Green
	case theme.ColorNameScrollBar:
		return color.NRGBA{R: 0x3A, G: 0x5F, B: 0x8C, A: 0xff}
	case theme.ColorNameSeparator:
		return color.NRGBA{R: 0x2E, G: 0x4A, B: 0x6F, A: 0xff}
	case theme.ColorNameShadow:
		return color.NRGBA{R: 0x00, G: 0x00, B: 0x00, A: 0x80}
	case theme.ColorNameSuccess:
		return color.NRGBA{R: 0x00, G: 0xE6, B: 0x76, A: 0xff} // Neural Green
	case theme.ColorNameWarning:
		return color.NRGBA{R: 0xFF, G: 0xB8, B: 0x00, A: 0xff} // Neural Orange
	case theme.ColorNameError:
		return color.NRGBA{R: 0xFF, G: 0x33, B: 0x66, A: 0xff} // Neural Red
	default:
		return theme.DefaultTheme().Color(name, variant)
	}
}

func (n *NeuralMeterTheme) Icon(name fyne.ThemeIconName) fyne.Resource {
	return theme.DefaultTheme().Icon(name)
}

func (n *NeuralMeterTheme) Font(style fyne.TextStyle) fyne.Resource {
	return theme.DefaultTheme().Font(style)
}

func (n *NeuralMeterTheme) Size(name fyne.ThemeSizeName) float32 {
	switch name {
	case theme.SizeNameText:
		return 14
	case theme.SizeNameCaptionText:
		return 12
	case theme.SizeNameInlineIcon:
		return 16
	case theme.SizeNameInputBorder:
		return 2
	case theme.SizeNamePadding:
		return 10
	case theme.SizeNameScrollBar:
		return 14
	case theme.SizeNameScrollBarSmall:
		return 10
	// Note: SizeNameSeparator is not available in all Fyne versions
	// Remove this case or use a different size name
	default:
		return theme.DefaultTheme().Size(name)
	}
}
