//go:build gui
// +build gui

package ui

import (
	"fmt"
	"log"
	"sync"

	"neuralmetergo/internal/parser"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

// TreeNodeType represents the type of a tree node
type TreeNodeType string

const (
	NodeTypeTestPlan  TreeNodeType = "testplan"
	NodeTypeScenario  TreeNodeType = "scenario"
	NodeTypeRequest   TreeNodeType = "request"
	NodeTypeAssertion TreeNodeType = "assertion"
	NodeTypeExtract   TreeNodeType = "extract"
	NodeTypeVariable  TreeNodeType = "variable"
	NodeTypeGlobal    TreeNodeType = "global"
	NodeTypeOutput    TreeNodeType = "output"
)

// TreeNode represents a node in the test plan tree
type TreeNode struct {
	ID          string
	Type        TreeNodeType
	DisplayName string
	Icon        fyne.Resource
	Data        interface{}
	Parent      *TreeNode
	Children    []*TreeNode
	IsExpanded  bool
	IsSelected  bool
	HasErrors   bool
	HasWarnings bool
	mu          sync.RWMutex
}

// NewTreeNode creates a new tree node
func NewTreeNode(id string, nodeType TreeNodeType, displayName string, data interface{}) *TreeNode {
	return &TreeNode{
		ID:          id,
		Type:        nodeType,
		DisplayName: displayName,
		Data:        data,
		Children:    make([]*TreeNode, 0),
		IsExpanded:  false,
		IsSelected:  false,
		HasErrors:   false,
		HasWarnings: false,
	}
}

// GetID returns the node ID
func (tn *TreeNode) GetID() string {
	tn.mu.RLock()
	defer tn.mu.RUnlock()
	return tn.ID
}

// GetDisplayName returns the display name
func (tn *TreeNode) GetDisplayName() string {
	tn.mu.RLock()
	defer tn.mu.RUnlock()
	return tn.DisplayName
}

// SetDisplayName sets the display name
func (tn *TreeNode) SetDisplayName(name string) {
	tn.mu.Lock()
	defer tn.mu.Unlock()
	tn.DisplayName = name
}

// GetData returns the node data
func (tn *TreeNode) GetData() interface{} {
	tn.mu.RLock()
	defer tn.mu.RUnlock()
	return tn.Data
}

// SetData sets the node data
func (tn *TreeNode) SetData(data interface{}) {
	tn.mu.Lock()
	defer tn.mu.Unlock()
	tn.Data = data
}

// AddChild adds a child node
func (tn *TreeNode) AddChild(child *TreeNode) {
	tn.mu.Lock()
	defer tn.mu.Unlock()
	child.Parent = tn
	tn.Children = append(tn.Children, child)
}

// RemoveChild removes a child node
func (tn *TreeNode) RemoveChild(child *TreeNode) {
	tn.mu.Lock()
	defer tn.mu.Unlock()
	for i, c := range tn.Children {
		if c == child {
			tn.Children = append(tn.Children[:i], tn.Children[i+1:]...)
			child.Parent = nil
			break
		}
	}
}

// GetChildren returns a copy of the children slice
func (tn *TreeNode) GetChildren() []*TreeNode {
	tn.mu.RLock()
	defer tn.mu.RUnlock()
	children := make([]*TreeNode, len(tn.Children))
	copy(children, tn.Children)
	return children
}

// GetFieldValue gets a field value from the node's data
func (tn *TreeNode) GetFieldValue(field string) interface{} {
	tn.mu.RLock()
	defer tn.mu.RUnlock()

	// This would need reflection or type-specific handling
	// For now, return nil as placeholder
	return nil
}

// SetFieldValue sets a field value in the node's data
func (tn *TreeNode) SetFieldValue(field string, value interface{}) {
	tn.mu.Lock()
	defer tn.mu.Unlock()

	// This would need reflection or type-specific handling
	// For now, this is a placeholder
}

// SetValidationState sets the validation state of the node
func (tn *TreeNode) SetValidationState(hasErrors, hasWarnings bool) {
	tn.mu.Lock()
	defer tn.mu.Unlock()
	tn.HasErrors = hasErrors
	tn.HasWarnings = hasWarnings
}

// TestPlanTreeWidget provides tree navigation for test plans
type TestPlanTreeWidget struct {
	logger             *log.Logger
	container          *fyne.Container
	tree               *widget.Tree
	rootNode           *TreeNode
	nodeMap            map[string]*TreeNode
	selectedNode       *TreeNode
	onSelectionChanged func(*TreeNode)
	onNodeChanged      func(*TreeNode)
	mu                 sync.RWMutex
}

// NewTestPlanTreeWidget creates a new test plan tree widget
func NewTestPlanTreeWidget(logger *log.Logger) *TestPlanTreeWidget {
	if logger == nil {
		logger = log.Default()
	}

	widget := &TestPlanTreeWidget{
		logger:  logger,
		nodeMap: make(map[string]*TreeNode),
	}

	widget.initializeTree()
	widget.setupContainer()

	return widget
}

// initializeTree initializes the Fyne tree widget
func (tptw *TestPlanTreeWidget) initializeTree() {
	tptw.tree = widget.NewTree(
		// ChildUIDs function
		func(uid string) []string {
			node := tptw.getNodeByID(uid)
			if node == nil {
				return []string{}
			}

			children := node.GetChildren()
			childUIDs := make([]string, len(children))
			for i, child := range children {
				childUIDs[i] = child.GetID()
			}
			return childUIDs
		},
		// IsBranch function
		func(uid string) bool {
			node := tptw.getNodeByID(uid)
			if node == nil {
				return false
			}
			return len(node.GetChildren()) > 0 || tptw.canHaveChildren(node.Type)
		},
		// CreateNode function
		func(branch bool) fyne.CanvasObject {
			icon := widget.NewIcon(theme.DocumentIcon())
			label := widget.NewLabel("Node")

			return container.NewHBox(icon, label)
		},
		// UpdateNode function
		func(uid string, branch bool, obj fyne.CanvasObject) {
			node := tptw.getNodeByID(uid)
			if node == nil {
				return
			}

			hbox := obj.(*fyne.Container)
			icon := hbox.Objects[0].(*widget.Icon)
			label := hbox.Objects[1].(*widget.Label)

			// Set icon based on node type
			icon.SetResource(tptw.getIconForNodeType(node.Type))

			// Set label text with validation indicators
			labelText := node.GetDisplayName()
			if node.HasErrors {
				labelText += " ❌"
			} else if node.HasWarnings {
				labelText += " ⚠️"
			}
			label.SetText(labelText)

			// Set selection state
			if node.IsSelected {
				label.Importance = widget.HighImportance
			} else {
				label.Importance = widget.MediumImportance
			}
		},
	)

	// Set up tree event handlers
	tptw.tree.OnSelected = func(uid string) {
		tptw.handleNodeSelection(uid)
	}

	tptw.tree.OnUnselected = func(uid string) {
		tptw.handleNodeUnselection(uid)
	}
}

// setupContainer creates the container with the tree and any additional controls
func (tptw *TestPlanTreeWidget) setupContainer() {
	// Add toolbar for tree operations
	toolbar := tptw.createTreeToolbar()

	// Create main container
	tptw.container = container.NewBorder(
		toolbar,  // top
		nil,      // bottom
		nil, nil, // left, right
		tptw.tree, // center
	)
}

// createTreeToolbar creates toolbar for tree operations
func (tptw *TestPlanTreeWidget) createTreeToolbar() *fyne.Container {
	expandAllBtn := widget.NewButtonWithIcon("", theme.ViewFullScreenIcon(), func() {
		tptw.expandAll()
	})
	expandAllBtn.SetText("")

	collapseAllBtn := widget.NewButtonWithIcon("", theme.ViewRestoreIcon(), func() {
		tptw.collapseAll()
	})
	collapseAllBtn.SetText("")

	addBtn := widget.NewButtonWithIcon("", theme.ContentAddIcon(), func() {
		tptw.addNodeToSelected()
	})
	addBtn.SetText("")

	deleteBtn := widget.NewButtonWithIcon("", theme.ContentRemoveIcon(), func() {
		tptw.deleteSelectedNode()
	})
	deleteBtn.SetText("")

	return container.NewHBox(
		expandAllBtn,
		collapseAllBtn,
		widget.NewSeparator(),
		addBtn,
		deleteBtn,
	)
}

// LoadTestPlan loads a test plan into the tree
func (tptw *TestPlanTreeWidget) LoadTestPlan(plan *parser.TestPlan) error {
	tptw.mu.Lock()
	defer tptw.mu.Unlock()

	if plan == nil {
		return fmt.Errorf("test plan cannot be nil")
	}

	// Clear existing tree
	tptw.nodeMap = make(map[string]*TreeNode)
	tptw.selectedNode = nil

	// Create root node
	tptw.rootNode = NewTreeNode("root", NodeTypeTestPlan, plan.Name, plan)
	tptw.nodeMap["root"] = tptw.rootNode

	// Build tree structure
	if err := tptw.buildTreeFromTestPlan(plan); err != nil {
		return fmt.Errorf("failed to build tree: %w", err)
	}

	// Refresh the tree widget
	tptw.tree.Refresh()

	tptw.logger.Printf("Test plan loaded into tree: %s", plan.Name)
	return nil
}

// buildTreeFromTestPlan builds the tree structure from a test plan
func (tptw *TestPlanTreeWidget) buildTreeFromTestPlan(plan *parser.TestPlan) error {
	// Add global settings node
	if tptw.hasGlobalSettings(plan) {
		globalNode := NewTreeNode("global", NodeTypeGlobal, "Global Settings", &plan.Global)
		tptw.rootNode.AddChild(globalNode)
		tptw.nodeMap["global"] = globalNode
	}

	// Add variables node
	if len(plan.Variables) > 0 {
		variablesNode := NewTreeNode("variables", NodeTypeVariable, "Variables", plan.Variables)
		tptw.rootNode.AddChild(variablesNode)
		tptw.nodeMap["variables"] = variablesNode

		// Add individual variables
		for i, variable := range plan.Variables {
			varID := fmt.Sprintf("variable_%d", i)
			varNode := NewTreeNode(varID, NodeTypeVariable, variable.Name, &variable)
			variablesNode.AddChild(varNode)
			tptw.nodeMap[varID] = varNode
		}
	}

	// Add scenarios
	for i, scenario := range plan.Scenarios {
		scenarioID := fmt.Sprintf("scenario_%d", i)
		scenarioNode := NewTreeNode(scenarioID, NodeTypeScenario, scenario.Name, &scenario)
		tptw.rootNode.AddChild(scenarioNode)
		tptw.nodeMap[scenarioID] = scenarioNode

		// Add requests to scenario
		for j, request := range scenario.Requests {
			requestID := fmt.Sprintf("request_%d_%d", i, j)
			requestName := request.Name
			if requestName == "" {
				requestName = fmt.Sprintf("%s %s", request.Method, request.URL)
			}
			requestNode := NewTreeNode(requestID, NodeTypeRequest, requestName, &request)
			scenarioNode.AddChild(requestNode)
			tptw.nodeMap[requestID] = requestNode

			// Add assertions to request
			for k, assertion := range request.Assertions {
				assertionID := fmt.Sprintf("assertion_%d_%d_%d", i, j, k)
				assertionName := fmt.Sprintf("%s %s", assertion.Type, assertion.Operator)
				assertionNode := NewTreeNode(assertionID, NodeTypeAssertion, assertionName, &assertion)
				requestNode.AddChild(assertionNode)
				tptw.nodeMap[assertionID] = assertionNode
			}

			// Add extracts to request
			for k, extract := range request.Extract {
				extractID := fmt.Sprintf("extract_%d_%d_%d", i, j, k)
				extractName := fmt.Sprintf("Extract: %s", extract.Name)
				extractNode := NewTreeNode(extractID, NodeTypeExtract, extractName, &extract)
				requestNode.AddChild(extractNode)
				tptw.nodeMap[extractID] = extractNode
			}
		}
	}

	// Add output settings
	outputNode := NewTreeNode("output", NodeTypeOutput, "Output Settings", &plan.Output)
	tptw.rootNode.AddChild(outputNode)
	tptw.nodeMap["output"] = outputNode

	return nil
}

// hasGlobalSettings checks if the test plan has global settings
func (tptw *TestPlanTreeWidget) hasGlobalSettings(plan *parser.TestPlan) bool {
	return plan.Global.BaseURL != "" ||
		len(plan.Global.Headers) > 0 ||
		len(plan.Global.Variables) > 0 ||
		plan.Global.Timeout.Duration > 0
}

// getNodeByID retrieves a node by its ID
func (tptw *TestPlanTreeWidget) getNodeByID(id string) *TreeNode {
	tptw.mu.RLock()
	defer tptw.mu.RUnlock()
	return tptw.nodeMap[id]
}

// handleNodeSelection handles node selection
func (tptw *TestPlanTreeWidget) handleNodeSelection(uid string) {
	tptw.mu.Lock()
	defer tptw.mu.Unlock()

	// Clear previous selection
	if tptw.selectedNode != nil {
		tptw.selectedNode.IsSelected = false
	}

	// Set new selection
	node := tptw.nodeMap[uid]
	if node != nil {
		node.IsSelected = true
		tptw.selectedNode = node

		// Trigger callback
		if tptw.onSelectionChanged != nil {
			go tptw.onSelectionChanged(node)
		}
	}
}

// handleNodeUnselection handles node unselection
func (tptw *TestPlanTreeWidget) handleNodeUnselection(uid string) {
	tptw.mu.Lock()
	defer tptw.mu.Unlock()

	node := tptw.nodeMap[uid]
	if node != nil {
		node.IsSelected = false
		if tptw.selectedNode == node {
			tptw.selectedNode = nil
		}
	}
}

// getIconForNodeType returns the appropriate icon for a node type
func (tptw *TestPlanTreeWidget) getIconForNodeType(nodeType TreeNodeType) fyne.Resource {
	switch nodeType {
	case NodeTypeTestPlan:
		return theme.DocumentIcon()
	case NodeTypeScenario:
		return theme.FolderIcon()
	case NodeTypeRequest:
		return theme.MailSendIcon()
	case NodeTypeAssertion:
		return theme.ConfirmIcon()
	case NodeTypeExtract:
		return theme.DownloadIcon()
	case NodeTypeVariable:
		return theme.SettingsIcon()
	case NodeTypeGlobal:
		return theme.SettingsIcon()
	case NodeTypeOutput:
		return theme.InfoIcon()
	default:
		return theme.DocumentIcon()
	}
}

// canHaveChildren returns true if the node type can have children
func (tptw *TestPlanTreeWidget) canHaveChildren(nodeType TreeNodeType) bool {
	switch nodeType {
	case NodeTypeTestPlan, NodeTypeScenario, NodeTypeRequest:
		return true
	default:
		return false
	}
}

// RefreshNode refreshes a specific node in the tree
func (tptw *TestPlanTreeWidget) RefreshNode(node *TreeNode) {
	if node == nil {
		return
	}
	// Refresh the specific node in the tree widget
	tptw.tree.Refresh()
}

// GetContainer returns the container for embedding
func (tptw *TestPlanTreeWidget) GetContainer() *fyne.Container {
	return tptw.container
}

// SetOnSelectionChanged sets the selection change callback
func (tptw *TestPlanTreeWidget) SetOnSelectionChanged(callback func(*TreeNode)) {
	tptw.onSelectionChanged = callback
}

// SetOnNodeChanged sets the node change callback
func (tptw *TestPlanTreeWidget) SetOnNodeChanged(callback func(*TreeNode)) {
	tptw.onNodeChanged = callback
}

// GetSelectedNode returns the currently selected node
func (tptw *TestPlanTreeWidget) GetSelectedNode() *TreeNode {
	tptw.mu.RLock()
	defer tptw.mu.RUnlock()
	return tptw.selectedNode
}

// expandAll expands all nodes in the tree
func (tptw *TestPlanTreeWidget) expandAll() {
	tptw.mu.RLock()
	defer tptw.mu.RUnlock()

	for _, node := range tptw.nodeMap {
		if len(node.GetChildren()) > 0 {
			tptw.tree.OpenBranch(node.GetID())
			node.IsExpanded = true
		}
	}
}

// collapseAll collapses all nodes in the tree
func (tptw *TestPlanTreeWidget) collapseAll() {
	tptw.mu.RLock()
	defer tptw.mu.RUnlock()

	for _, node := range tptw.nodeMap {
		if len(node.GetChildren()) > 0 {
			tptw.tree.CloseBranch(node.GetID())
			node.IsExpanded = false
		}
	}
}

// addNodeToSelected adds a new node to the selected node
func (tptw *TestPlanTreeWidget) addNodeToSelected() {
	tptw.mu.RLock()
	selectedNode := tptw.selectedNode
	tptw.mu.RUnlock()

	if selectedNode == nil {
		return
	}

	// Determine what type of node can be added
	var newNodeType TreeNodeType
	var newNodeName string

	switch selectedNode.Type {
	case NodeTypeTestPlan:
		newNodeType = NodeTypeScenario
		newNodeName = "New Scenario"
	case NodeTypeScenario:
		newNodeType = NodeTypeRequest
		newNodeName = "New Request"
	case NodeTypeRequest:
		newNodeType = NodeTypeAssertion
		newNodeName = "New Assertion"
	default:
		return // Cannot add children to this node type
	}

	// Create new node
	newID := fmt.Sprintf("%s_new_%d", selectedNode.GetID(), len(selectedNode.GetChildren()))
	newNode := NewTreeNode(newID, newNodeType, newNodeName, nil)

	tptw.mu.Lock()
	selectedNode.AddChild(newNode)
	tptw.nodeMap[newID] = newNode
	tptw.mu.Unlock()

	// Refresh tree
	tptw.tree.Refresh()

	// Trigger change callback
	if tptw.onNodeChanged != nil {
		go tptw.onNodeChanged(newNode)
	}
}

// deleteSelectedNode deletes the selected node
func (tptw *TestPlanTreeWidget) deleteSelectedNode() {
	tptw.mu.Lock()
	defer tptw.mu.Unlock()

	if tptw.selectedNode == nil || tptw.selectedNode == tptw.rootNode {
		return
	}

	// Remove from parent
	if tptw.selectedNode.Parent != nil {
		tptw.selectedNode.Parent.RemoveChild(tptw.selectedNode)
	}

	// Remove from node map
	delete(tptw.nodeMap, tptw.selectedNode.GetID())

	// Clear selection
	tptw.selectedNode = nil

	// Refresh tree
	tptw.tree.Refresh()
}

// UpdateValidationState updates the validation state of nodes
func (tptw *TestPlanTreeWidget) UpdateValidationState(nodeID string, hasErrors, hasWarnings bool) {
	node := tptw.getNodeByID(nodeID)
	if node != nil {
		node.SetValidationState(hasErrors, hasWarnings)
		tptw.RefreshNode(node)
	}
}

// GetRootNodeID returns the root node ID for the tree widget
func (tptw *TestPlanTreeWidget) GetRootNodeID() string {
	return "root"
}
