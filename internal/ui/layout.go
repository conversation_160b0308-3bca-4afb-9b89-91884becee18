//go:build gui
// +build gui

package ui

import (
	"fmt"
	"log"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

// LayoutManager manages the main window layout and navigation
type LayoutManager struct {
	app                   *NeuralMeterApp
	logger                *log.Logger
	mainContainer         *fyne.Container
	navigationTabs        *container.AppTabs
	dockablePanels        map[string]*DockablePanel
	sidebarContainer      *fyne.Container
	contentContainer      *fyne.Container
	statusBar             *StatusBar
	breadcrumbs           *BreadcrumbNavigation
	currentLayout         LayoutType
	responsiveBreakpoints map[BreakpointType]float32
}

// LayoutType represents different layout configurations
type LayoutType int

const (
	LayoutTypeDesktop LayoutType = iota
	LayoutTypeLaptop
	LayoutTypeTablet
	LayoutTypeCompact
)

// BreakpointType represents responsive design breakpoints
type BreakpointType int

const (
	BreakpointDesktop BreakpointType = iota
	BreakpointLaptop
	BreakpointTablet
	BreakpointCompact
)

// PanelPosition represents where a panel can be docked
type PanelPosition int

const (
	PanelPositionLeft PanelPosition = iota
	PanelPositionRight
	PanelPositionBottom
	PanelPositionTop
	PanelPositionFloating
)

// DockablePanel represents a panel that can be docked, resized, and hidden
type DockablePanel struct {
	ID          string
	Title       string
	Content     fyne.CanvasObject
	Position    PanelPosition
	Size        fyne.Size
	IsVisible   bool
	IsCollapsed bool
	MinSize     fyne.Size
	MaxSize     fyne.Size
	Container   *fyne.Container
}

// StatusBar represents the application status bar
type StatusBar struct {
	Container   *fyne.Container
	StatusLabel *widget.Label
	ProgressBar *widget.ProgressBar
	InfoLabels  map[string]*widget.Label
}

// BreadcrumbNavigation represents breadcrumb navigation
type BreadcrumbNavigation struct {
	Container   *fyne.Container
	Breadcrumbs []BreadcrumbItem
}

// BreadcrumbItem represents a single breadcrumb
type BreadcrumbItem struct {
	Label    string
	Callback func()
}

// NewLayoutManager creates a new layout manager
func NewLayoutManager(app *NeuralMeterApp, logger *log.Logger) *LayoutManager {
	if logger == nil {
		logger = log.Default()
	}

	lm := &LayoutManager{
		app:            app,
		logger:         logger,
		dockablePanels: make(map[string]*DockablePanel),
		currentLayout:  LayoutTypeDesktop,
		responsiveBreakpoints: map[BreakpointType]float32{
			BreakpointDesktop: 1200,
			BreakpointLaptop:  1024,
			BreakpointTablet:  768,
			BreakpointCompact: 480,
		},
	}

	lm.initializeLayout()
	return lm
}

// initializeLayout sets up the initial layout structure
func (lm *LayoutManager) initializeLayout() {
	// Create main navigation tabs
	lm.navigationTabs = container.NewAppTabs()
	lm.navigationTabs.SetTabLocation(container.TabLocationLeading)

	// Create status bar
	lm.statusBar = lm.createStatusBar()

	// Create breadcrumb navigation
	lm.breadcrumbs = lm.createBreadcrumbNavigation()

	// Create sidebar container
	lm.sidebarContainer = container.NewVBox()

	// Create content container
	lm.contentContainer = container.NewBorder(
		lm.breadcrumbs.Container, // top
		nil,                      // bottom
		nil,                      // left
		nil,                      // right
		lm.navigationTabs,        // center
	)

	// Create main container with responsive layout
	lm.mainContainer = container.NewBorder(
		nil,                    // top
		lm.statusBar.Container, // bottom
		lm.sidebarContainer,    // left
		nil,                    // right
		lm.contentContainer,    // center
	)

	// Initialize default panels
	lm.initializeDefaultPanels()

	// Set up responsive behavior
	lm.setupResponsiveBehavior()
}

// createStatusBar creates the application status bar
func (lm *LayoutManager) createStatusBar() *StatusBar {
	statusLabel := widget.NewLabel("Ready")
	progressBar := widget.NewProgressBar()
	progressBar.Hide() // Hidden by default

	// Create info labels for various status information
	infoLabels := map[string]*widget.Label{
		"memory":     widget.NewLabel("Memory: 0 MB"),
		"tasks":      widget.NewLabel("Tasks: 0"),
		"connection": widget.NewLabel("Disconnected"),
	}

	// Create status bar container
	statusContainer := container.NewHBox(
		statusLabel,
		layout.NewSpacer(),
		infoLabels["memory"],
		widget.NewSeparator(),
		infoLabels["tasks"],
		widget.NewSeparator(),
		infoLabels["connection"],
		progressBar,
	)

	return &StatusBar{
		Container:   statusContainer,
		StatusLabel: statusLabel,
		ProgressBar: progressBar,
		InfoLabels:  infoLabels,
	}
}

// createBreadcrumbNavigation creates the breadcrumb navigation
func (lm *LayoutManager) createBreadcrumbNavigation() *BreadcrumbNavigation {
	breadcrumbContainer := container.NewHBox()

	return &BreadcrumbNavigation{
		Container:   breadcrumbContainer,
		Breadcrumbs: make([]BreadcrumbItem, 0),
	}
}

// initializeDefaultPanels creates the default dockable panels
func (lm *LayoutManager) initializeDefaultPanels() {
	// Create Project Explorer panel
	projectExplorer := lm.createProjectExplorerPanel()
	lm.AddPanel("project-explorer", projectExplorer)

	// Create Properties panel
	propertiesPanel := lm.createPropertiesPanel()
	lm.AddPanel("properties", propertiesPanel)

	// Create Console panel
	consolePanel := lm.createConsolePanel()
	lm.AddPanel("console", consolePanel)
}

// createProjectExplorerPanel creates the project explorer panel
func (lm *LayoutManager) createProjectExplorerPanel() *DockablePanel {
	// Create tree widget for project structure
	tree := widget.NewTree(
		func(uid string) []string {
			// Mock data - in real implementation, this would show actual project structure
			if uid == "" {
				return []string{"Test Plans", "Resources", "Results"}
			}
			switch uid {
			case "Test Plans":
				return []string{"HTTP Tests", "API Tests", "Load Tests"}
			case "Resources":
				return []string{"CSV Files", "JSON Files", "Templates"}
			case "Results":
				return []string{"Recent Results", "Archived Results"}
			}
			return nil
		},
		func(uid string) bool {
			return uid == "" || uid == "Test Plans" || uid == "Resources" || uid == "Results"
		},
		func(branch bool) fyne.CanvasObject {
			if branch {
				return widget.NewLabel("Folder")
			}
			return widget.NewLabel("File")
		},
		func(uid string, branch bool, obj fyne.CanvasObject) {
			label := obj.(*widget.Label)
			label.SetText(uid)
		},
	)

	tree.OpenBranch("")
	tree.OpenBranch("Test Plans")

	content := container.NewBorder(
		widget.NewLabel("Project Explorer"),
		nil, nil, nil,
		container.NewScroll(tree),
	)

	return &DockablePanel{
		ID:          "project-explorer",
		Title:       "Project Explorer",
		Content:     content,
		Position:    PanelPositionLeft,
		Size:        fyne.NewSize(250, 400),
		IsVisible:   true,
		IsCollapsed: false,
		MinSize:     fyne.NewSize(200, 300),
		MaxSize:     fyne.NewSize(400, 800),
	}
}

// createPropertiesPanel creates the properties panel
func (lm *LayoutManager) createPropertiesPanel() *DockablePanel {
	// Create form for properties
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("Property name")

	valueEntry := widget.NewEntry()
	valueEntry.SetPlaceHolder("Property value")

	typeSelect := widget.NewSelect([]string{"String", "Number", "Boolean", "Array"}, nil)
	typeSelect.SetSelected("String")

	form := &widget.Form{
		Items: []*widget.FormItem{
			{Text: "Name", Widget: nameEntry},
			{Text: "Value", Widget: valueEntry},
			{Text: "Type", Widget: typeSelect},
		},
	}

	content := container.NewBorder(
		widget.NewLabel("Properties"),
		nil, nil, nil,
		container.NewScroll(form),
	)

	return &DockablePanel{
		ID:          "properties",
		Title:       "Properties",
		Content:     content,
		Position:    PanelPositionRight,
		Size:        fyne.NewSize(300, 400),
		IsVisible:   true,
		IsCollapsed: false,
		MinSize:     fyne.NewSize(250, 300),
		MaxSize:     fyne.NewSize(500, 800),
	}
}

// createConsolePanel creates the console panel
func (lm *LayoutManager) createConsolePanel() *DockablePanel {
	// Create console output
	consoleText := widget.NewRichText()
	consoleText.Segments = []widget.RichTextSegment{
		&widget.TextSegment{
			Text:  "NeuralMeter Console\n",
			Style: widget.RichTextStyle{SizeName: theme.SizeNameHeadingText},
		},
		&widget.TextSegment{
			Text:  "Application started successfully.\n",
			Style: widget.RichTextStyle{ColorName: theme.ColorNameSuccess},
		},
		&widget.TextSegment{
			Text:  "Ready for load testing operations.\n",
			Style: widget.RichTextStyle{},
		},
	}

	scroll := container.NewScroll(consoleText)
	scroll.SetMinSize(fyne.NewSize(400, 150))

	content := container.NewBorder(
		widget.NewLabel("Console"),
		nil, nil, nil,
		scroll,
	)

	return &DockablePanel{
		ID:          "console",
		Title:       "Console",
		Content:     content,
		Position:    PanelPositionBottom,
		Size:        fyne.NewSize(800, 200),
		IsVisible:   true,
		IsCollapsed: false,
		MinSize:     fyne.NewSize(400, 150),
		MaxSize:     fyne.NewSize(1200, 400),
	}
}

// setupResponsiveBehavior sets up responsive layout behavior
func (lm *LayoutManager) setupResponsiveBehavior() {
	// Note: Fyne v2 doesn't have SetOnResized method for Window
	// We'll implement resize handling through canvas resize events
	if lm.app.Window != nil {
		canvas := lm.app.Window.Canvas()
		canvas.SetOnTypedKey(func(event *fyne.KeyEvent) {
			// Handle keyboard shortcuts for layout
			switch event.Name {
			case fyne.KeyF12:
				// Toggle panel visibility
				lm.toggleAllPanels()
			}
		})

		// Set up periodic size checking as workaround
		lm.setupPeriodicSizeCheck()
	}
}

// setupPeriodicSizeCheck sets up periodic window size checking
func (lm *LayoutManager) setupPeriodicSizeCheck() {
	// This is a workaround since Fyne v2 doesn't have direct resize callbacks
	// In a real implementation, you might use a timer or other mechanism
	lm.logger.Printf("Responsive behavior setup completed")
}

// toggleAllPanels toggles visibility of all panels
func (lm *LayoutManager) toggleAllPanels() {
	allHidden := true
	for _, panel := range lm.dockablePanels {
		if panel.IsVisible {
			allHidden = false
			break
		}
	}

	// Toggle all panels
	for _, panel := range lm.dockablePanels {
		panel.IsVisible = allHidden
	}

	lm.updatePanelVisibility()
}

// handleWindowResize handles window resize events
func (lm *LayoutManager) handleWindowResize(size fyne.Size) {
	// Determine appropriate layout based on window size
	newLayout := lm.determineLayout(size.Width)

	if newLayout != lm.currentLayout {
		lm.ApplyLayout(newLayout)
		lm.currentLayout = newLayout
		lm.logger.Printf("Layout changed to: %v", newLayout)
	}
}

// determineLayout determines the appropriate layout based on window width
func (lm *LayoutManager) determineLayout(width float32) LayoutType {
	switch {
	case width >= lm.responsiveBreakpoints[BreakpointDesktop]:
		return LayoutTypeDesktop
	case width >= lm.responsiveBreakpoints[BreakpointLaptop]:
		return LayoutTypeLaptop
	case width >= lm.responsiveBreakpoints[BreakpointTablet]:
		return LayoutTypeTablet
	default:
		return LayoutTypeCompact
	}
}

// ApplyLayout applies the specified layout configuration
func (lm *LayoutManager) ApplyLayout(layoutType LayoutType) {
	switch layoutType {
	case LayoutTypeDesktop:
		lm.applyDesktopLayout()
	case LayoutTypeLaptop:
		lm.applyLaptopLayout()
	case LayoutTypeTablet:
		lm.applyTabletLayout()
	case LayoutTypeCompact:
		lm.applyCompactLayout()
	}
}

// applyDesktopLayout applies desktop layout (full features)
func (lm *LayoutManager) applyDesktopLayout() {
	// Show all panels
	for _, panel := range lm.dockablePanels {
		panel.IsVisible = true
	}
	lm.updatePanelVisibility()
}

// applyLaptopLayout applies laptop layout (slightly reduced)
func (lm *LayoutManager) applyLaptopLayout() {
	// Show most panels, possibly reduce sizes
	for _, panel := range lm.dockablePanels {
		panel.IsVisible = true
		if panel.Position == PanelPositionLeft || panel.Position == PanelPositionRight {
			// Reduce sidebar panel widths
			panel.Size.Width = panel.Size.Width * 0.8
		}
	}
	lm.updatePanelVisibility()
}

// applyTabletLayout applies tablet layout (reduced panels)
func (lm *LayoutManager) applyTabletLayout() {
	// Hide some panels, collapse others
	for _, panel := range lm.dockablePanels {
		if panel.Position == PanelPositionRight {
			panel.IsVisible = false
		} else {
			panel.IsVisible = true
			panel.IsCollapsed = panel.Position == PanelPositionLeft
		}
	}
	lm.updatePanelVisibility()
}

// applyCompactLayout applies compact layout (minimal panels)
func (lm *LayoutManager) applyCompactLayout() {
	// Hide most panels, keep only essential ones
	for _, panel := range lm.dockablePanels {
		if panel.ID == "console" {
			panel.IsVisible = true
			panel.IsCollapsed = true
		} else {
			panel.IsVisible = false
		}
	}
	lm.updatePanelVisibility()
}

// updatePanelVisibility updates the visibility of panels based on their state
func (lm *LayoutManager) updatePanelVisibility() {
	// Clear sidebar
	lm.sidebarContainer.Objects = nil

	// Add visible panels to appropriate containers
	for _, panel := range lm.dockablePanels {
		if panel.IsVisible {
			switch panel.Position {
			case PanelPositionLeft:
				if panel.IsCollapsed {
					// Show only title bar when collapsed
					titleBar := lm.createPanelTitleBar(panel)
					lm.sidebarContainer.Add(titleBar)
				} else {
					lm.sidebarContainer.Add(panel.Content)
				}
			case PanelPositionRight:
				// For now, add to sidebar (in future, create right sidebar)
				if !panel.IsCollapsed {
					lm.sidebarContainer.Add(panel.Content)
				}
			case PanelPositionBottom:
				// Add to bottom of content area
				if !panel.IsCollapsed {
					lm.contentContainer = container.NewBorder(
						lm.breadcrumbs.Container,
						panel.Content,
						nil,
						nil,
						lm.navigationTabs,
					)
				}
			}
		}
	}

	// Refresh containers
	lm.sidebarContainer.Refresh()
	lm.contentContainer.Refresh()
	lm.mainContainer.Refresh()
}

// createPanelTitleBar creates a collapsible title bar for a panel
func (lm *LayoutManager) createPanelTitleBar(panel *DockablePanel) fyne.CanvasObject {
	titleButton := widget.NewButton(panel.Title, func() {
		panel.IsCollapsed = !panel.IsCollapsed
		lm.updatePanelVisibility()
	})

	return container.NewHBox(titleButton)
}

// AddPanel adds a new dockable panel
func (lm *LayoutManager) AddPanel(id string, panel *DockablePanel) {
	lm.dockablePanels[id] = panel
	lm.updatePanelVisibility()
}

// RemovePanel removes a dockable panel
func (lm *LayoutManager) RemovePanel(id string) {
	delete(lm.dockablePanels, id)
	lm.updatePanelVisibility()
}

// GetPanel returns a panel by ID
func (lm *LayoutManager) GetPanel(id string) *DockablePanel {
	return lm.dockablePanels[id]
}

// AddTab adds a new tab to the navigation
func (lm *LayoutManager) AddTab(text string, content fyne.CanvasObject) {
	tab := container.NewTabItem(text, content)
	lm.navigationTabs.Append(tab)
}

// RemoveTab removes a tab by index
func (lm *LayoutManager) RemoveTab(index int) {
	if index >= 0 && index < len(lm.navigationTabs.Items) {
		lm.navigationTabs.RemoveIndex(index)
	}
}

// SetActiveTab sets the active tab by index
func (lm *LayoutManager) SetActiveTab(index int) {
	if index >= 0 && index < len(lm.navigationTabs.Items) {
		lm.navigationTabs.SelectTabIndex(index)
	}
}

// UpdateBreadcrumbs updates the breadcrumb navigation
func (lm *LayoutManager) UpdateBreadcrumbs(items []BreadcrumbItem) {
	lm.breadcrumbs.Breadcrumbs = items
	lm.refreshBreadcrumbs()
}

// refreshBreadcrumbs refreshes the breadcrumb display
func (lm *LayoutManager) refreshBreadcrumbs() {
	lm.breadcrumbs.Container.Objects = nil

	for i, item := range lm.breadcrumbs.Breadcrumbs {
		if i > 0 {
			// Add separator
			separator := widget.NewLabel(" > ")
			lm.breadcrumbs.Container.Add(separator)
		}

		// Add breadcrumb button
		button := widget.NewButton(item.Label, item.Callback)
		lm.breadcrumbs.Container.Add(button)
	}

	lm.breadcrumbs.Container.Refresh()
}

// UpdateStatus updates the status bar
func (lm *LayoutManager) UpdateStatus(status string) {
	lm.statusBar.StatusLabel.SetText(status)
}

// UpdateProgress updates the progress bar
func (lm *LayoutManager) UpdateProgress(value float64, visible bool) {
	lm.statusBar.ProgressBar.SetValue(value)
	if visible {
		lm.statusBar.ProgressBar.Show()
	} else {
		lm.statusBar.ProgressBar.Hide()
	}
}

// UpdateInfoLabel updates an info label in the status bar
func (lm *LayoutManager) UpdateInfoLabel(key, value string) {
	if label, exists := lm.statusBar.InfoLabels[key]; exists {
		label.SetText(value)
	}
}

// GetMainContainer returns the main container for the window
func (lm *LayoutManager) GetMainContainer() *fyne.Container {
	return lm.mainContainer
}

// GetNavigationTabs returns the navigation tabs container
func (lm *LayoutManager) GetNavigationTabs() *container.AppTabs {
	return lm.navigationTabs
}

// ToggleFullscreen toggles fullscreen mode with layout adjustments
func (lm *LayoutManager) ToggleFullscreen() {
	if lm.app.Window != nil {
		isFullscreen := lm.app.Window.FullScreen()
		lm.app.Window.SetFullScreen(!isFullscreen)

		// Adjust layout for fullscreen
		if !isFullscreen {
			// Entering fullscreen - hide some UI elements
			lm.breadcrumbs.Container.Hide()
			lm.statusBar.Container.Hide()
		} else {
			// Exiting fullscreen - restore UI elements
			lm.breadcrumbs.Container.Show()
			lm.statusBar.Container.Show()
		}
	}
}

// SaveLayoutState saves the current layout state to preferences
func (lm *LayoutManager) SaveLayoutState() error {
	if lm.app.App == nil {
		return fmt.Errorf("app not initialized")
	}

	prefs := lm.app.App.Preferences()

	// Save panel states
	for id, panel := range lm.dockablePanels {
		prefs.SetBool(fmt.Sprintf("panel_%s_visible", id), panel.IsVisible)
		prefs.SetBool(fmt.Sprintf("panel_%s_collapsed", id), panel.IsCollapsed)
		prefs.SetFloat(fmt.Sprintf("panel_%s_width", id), float64(panel.Size.Width))
		prefs.SetFloat(fmt.Sprintf("panel_%s_height", id), float64(panel.Size.Height))
	}

	// Save current layout type
	prefs.SetInt("current_layout", int(lm.currentLayout))

	lm.logger.Printf("Layout state saved")
	return nil
}

// LoadLayoutState loads the layout state from preferences
func (lm *LayoutManager) LoadLayoutState() error {
	if lm.app.App == nil {
		return fmt.Errorf("app not initialized")
	}

	prefs := lm.app.App.Preferences()

	// Load panel states
	for id, panel := range lm.dockablePanels {
		if prefs.Bool(fmt.Sprintf("panel_%s_visible", id)) {
			panel.IsVisible = true
		}
		if prefs.Bool(fmt.Sprintf("panel_%s_collapsed", id)) {
			panel.IsCollapsed = true
		}

		width := prefs.Float(fmt.Sprintf("panel_%s_width", id))
		height := prefs.Float(fmt.Sprintf("panel_%s_height", id))
		if width > 0 && height > 0 {
			panel.Size = fyne.NewSize(float32(width), float32(height))
		}
	}

	// Load current layout type
	layoutType := LayoutType(prefs.Int("current_layout"))
	lm.currentLayout = layoutType
	lm.ApplyLayout(layoutType)

	lm.logger.Printf("Layout state loaded")
	return nil
}
