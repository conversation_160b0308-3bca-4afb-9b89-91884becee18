package ui

import (
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"strconv"
	"strings"
	"time"

	"neuralmetergo/internal/parser"
	"neuralmetergo/internal/validation"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/widget"
	"gopkg.in/yaml.v3"
)

// FormFieldType represents different types of form fields
type FormFieldType string

const (
	FieldTypeText     FormFieldType = "text"
	FieldTypeTextArea FormFieldType = "textarea"
	FieldTypeNumber   FormFieldType = "number"
	FieldTypeSelect   FormFieldType = "select"
	FieldTypeCheckbox FormFieldType = "checkbox"
	FieldTypeDuration FormFieldType = "duration"
)

// Language types for syntax highlighting
type Language string

const (
	LanguageYAML       Language = "yaml"
	LanguageJSON       Language = "json"
	LanguageJavaScript Language = "javascript"
	LanguageXPath      Language = "xpath"
	LanguageRegex      Language = "regex"
	LanguageText       Language = "text"
)

// CompletionProvider interface for auto-completion
type CompletionProvider interface {
	GetCompletions(prefix string, context string) []string
	GetHelp(keyword string) string
}

// SyntaxHighlightEntry extends widget.Entry with advanced features
type SyntaxHighlightEntry struct {
	widget.Entry
	language           Language
	completionProvider CompletionProvider
	helpProvider       CompletionProvider
	completionList     *widget.PopUp
	helpTooltip        *widget.PopUp
	shortcuts          map[string]func()
	onCompletion       func(string)
}

// NewSyntaxHighlightEntry creates a new syntax highlighting entry
func NewSyntaxHighlightEntry(language Language) *SyntaxHighlightEntry {
	entry := &SyntaxHighlightEntry{
		language:  language,
		shortcuts: make(map[string]func()),
	}
	entry.ExtendBaseWidget(entry)
	entry.setupProviders()
	entry.setupShortcuts()
	return entry
}

// setupProviders initializes completion and help providers based on language
func (she *SyntaxHighlightEntry) setupProviders() {
	switch she.language {
	case LanguageYAML:
		she.completionProvider = NewYAMLCompletionProvider()
		she.helpProvider = NewYAMLHelpProvider()
	case LanguageJSON:
		she.completionProvider = NewJSONCompletionProvider()
		she.helpProvider = NewJSONHelpProvider()
	case LanguageJavaScript:
		she.completionProvider = NewJavaScriptCompletionProvider()
		she.helpProvider = NewJavaScriptHelpProvider()
	case LanguageXPath:
		she.completionProvider = NewXPathCompletionProvider()
		she.helpProvider = NewXPathHelpProvider()
	case LanguageRegex:
		she.completionProvider = NewRegexCompletionProvider()
		she.helpProvider = NewRegexHelpProvider()
	default:
		she.completionProvider = NewDefaultCompletionProvider()
		she.helpProvider = NewDefaultHelpProvider()
	}
}

// setupShortcuts configures keyboard shortcuts
func (she *SyntaxHighlightEntry) setupShortcuts() {
	she.shortcuts["Ctrl+Space"] = she.showCompletions
	she.shortcuts["Ctrl+H"] = she.showHelp
	she.shortcuts["Escape"] = she.hidePopups
	she.shortcuts["Tab"] = she.acceptCompletion
}

// showCompletions displays auto-completion suggestions
func (she *SyntaxHighlightEntry) showCompletions() {
	if she.completionProvider == nil {
		return
	}

	text := she.Text
	cursor := she.CursorColumn
	prefix := she.getCurrentWordPrefix(text, cursor)
	context := she.getContext(text, cursor)

	completions := she.completionProvider.GetCompletions(prefix, context)
	if len(completions) == 0 {
		return
	}

	she.displayCompletions(completions)
}

// showHelp displays context-sensitive help
func (she *SyntaxHighlightEntry) showHelp() {
	if she.helpProvider == nil {
		return
	}

	text := she.Text
	cursor := she.CursorColumn
	word := she.getCurrentWord(text, cursor)

	help := she.helpProvider.GetHelp(word)
	if help != "" {
		she.displayHelp(help)
	}
}

// getCurrentWordPrefix extracts the current word prefix for completion
func (she *SyntaxHighlightEntry) getCurrentWordPrefix(text string, cursor int) string {
	if cursor > len(text) {
		cursor = len(text)
	}

	start := cursor
	for start > 0 && isWordChar(rune(text[start-1])) {
		start--
	}

	return text[start:cursor]
}

// getCurrentWord extracts the current word at cursor position
func (she *SyntaxHighlightEntry) getCurrentWord(text string, cursor int) string {
	if cursor > len(text) {
		cursor = len(text)
	}

	start := cursor
	for start > 0 && isWordChar(rune(text[start-1])) {
		start--
	}

	end := cursor
	for end < len(text) && isWordChar(rune(text[end])) {
		end++
	}

	return text[start:end]
}

// getContext extracts context around cursor for better completion
func (she *SyntaxHighlightEntry) getContext(text string, cursor int) string {
	lines := strings.Split(text, "\n")
	if len(lines) == 0 {
		return ""
	}

	// Find current line
	pos := 0
	for _, line := range lines {
		if pos+len(line) >= cursor {
			// Return current line as context
			return line
		}
		pos += len(line) + 1 // +1 for newline
	}

	return ""
}

// isWordChar checks if a character is part of a word
func isWordChar(r rune) bool {
	return (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') || r == '_' || r == '-'
}

// displayCompletions shows completion popup
func (she *SyntaxHighlightEntry) displayCompletions(completions []string) {
	if she.completionList != nil {
		she.completionList.Hide()
	}

	list := widget.NewList(
		func() int { return len(completions) },
		func() fyne.CanvasObject { return widget.NewLabel("") },
		func(i widget.ListItemID, obj fyne.CanvasObject) {
			obj.(*widget.Label).SetText(completions[i])
		},
	)

	list.OnSelected = func(id widget.ListItemID) {
		if id < len(completions) {
			she.acceptCompletionText(completions[id])
		}
		she.hidePopups()
	}

	she.completionList = widget.NewPopUp(container.NewBorder(nil, nil, nil, nil, list), fyne.CurrentApp().Driver().CanvasForObject(she))
	she.completionList.Resize(fyne.NewSize(200, 150))
	she.completionList.Show()
}

// displayHelp shows help tooltip
func (she *SyntaxHighlightEntry) displayHelp(help string) {
	if she.helpTooltip != nil {
		she.helpTooltip.Hide()
	}

	helpText := widget.NewRichTextFromMarkdown(help)
	helpText.Wrapping = fyne.TextWrapWord

	scroll := container.NewScroll(helpText)
	scroll.SetMinSize(fyne.NewSize(300, 200))

	she.helpTooltip = widget.NewPopUp(scroll, fyne.CurrentApp().Driver().CanvasForObject(she))
	she.helpTooltip.Show()
}

// acceptCompletionText inserts completion text
func (she *SyntaxHighlightEntry) acceptCompletionText(completion string) {
	text := she.Text
	cursor := she.CursorColumn
	prefix := she.getCurrentWordPrefix(text, cursor)

	// Replace prefix with completion
	newText := text[:cursor-len(prefix)] + completion + text[cursor:]
	she.SetText(newText)

	// Move cursor to end of completion
	she.CursorColumn = cursor - len(prefix) + len(completion)

	if she.onCompletion != nil {
		she.onCompletion(completion)
	}
}

// acceptCompletion accepts the first completion suggestion
func (she *SyntaxHighlightEntry) acceptCompletion() {
	if she.completionList != nil && she.completionList.Visible() {
		// Simulate selection of first item
		if she.completionProvider != nil {
			text := she.Text
			cursor := she.CursorColumn
			prefix := she.getCurrentWordPrefix(text, cursor)
			context := she.getContext(text, cursor)
			completions := she.completionProvider.GetCompletions(prefix, context)
			if len(completions) > 0 {
				she.acceptCompletionText(completions[0])
			}
		}
		she.hidePopups()
	}
}

// hidePopups hides all popups
func (she *SyntaxHighlightEntry) hidePopups() {
	if she.completionList != nil {
		she.completionList.Hide()
	}
	if she.helpTooltip != nil {
		she.helpTooltip.Hide()
	}
}

// SetCompletionCallback sets callback for completion events
func (she *SyntaxHighlightEntry) SetCompletionCallback(callback func(string)) {
	she.onCompletion = callback
}

// FormField represents a single form field configuration
type FormField struct {
	Name        string
	Label       string
	Type        FormFieldType
	Required    bool
	Options     []string
	Placeholder string
	Help        string
	Validation  func(interface{}) error
	Value       interface{}
	Binding     binding.String
	Widget      fyne.Widget
}

// Enhanced FormBuilder with advanced editing features
type FormBuilder struct {
	validator             *validation.ValidationEngine
	logger                *log.Logger
	fields                []*FormField
	container             *fyne.Container
	data                  interface{}
	onFieldChange         func(field string, value interface{})
	onValidationChange    func(valid bool, errors []string)
	enableAdvancedEditing bool // New flag to enable advanced features
}

// NewFormBuilder creates a new form builder
func NewFormBuilder(validator *validation.ValidationEngine, logger *log.Logger) *FormBuilder {
	return &FormBuilder{
		validator:             validator,
		logger:                logger,
		fields:                make([]*FormField, 0),
		onFieldChange:         func(string, interface{}) {},
		onValidationChange:    func(bool, []string) {},
		enableAdvancedEditing: true, // Enable by default
	}
}

// SetFieldChangeCallback sets the callback for field changes
func (fb *FormBuilder) SetFieldChangeCallback(callback func(field string, value interface{})) {
	fb.onFieldChange = callback
}

// SetValidationChangeCallback sets the callback for validation changes
func (fb *FormBuilder) SetValidationChangeCallback(callback func(valid bool, errors []string)) {
	fb.onValidationChange = callback
}

// SetAdvancedEditingEnabled enables or disables advanced editing features
func (fb *FormBuilder) SetAdvancedEditingEnabled(enabled bool) {
	fb.enableAdvancedEditing = enabled
}

// BuildForm creates a form for the given data structure
func (fb *FormBuilder) BuildForm(data interface{}) *fyne.Container {
	fb.data = data
	fb.fields = make([]*FormField, 0)

	switch v := data.(type) {
	case *parser.TestPlan:
		return fb.buildTestPlanForm(v)
	case *parser.Scenario:
		return fb.buildScenarioForm(v)
	case *parser.Request:
		return fb.buildRequestForm(v)
	case *parser.Assertion:
		return fb.buildAssertionForm(v)
	case *parser.Extract:
		return fb.buildExtractForm(v)
	case *parser.Variable:
		return fb.buildVariableForm(v)
	case *parser.Global:
		return fb.buildGlobalForm(v)
	case *parser.Output:
		return fb.buildOutputForm(v)
	default:
		fb.logger.Printf("Unknown form type: %T", data)
		return container.NewVBox(widget.NewLabel("Unknown form type"))
	}
}

// buildTestPlanForm creates a form for TestPlan
func (fb *FormBuilder) buildTestPlanForm(plan *parser.TestPlan) *fyne.Container {
	form := container.NewVBox()

	// Basic Information Section
	basicSection := fb.createSection("Basic Information")
	basicSection.Add(fb.createTextField("name", "Name", plan.Name, true, "Test plan name"))
	basicSection.Add(fb.createTextField("version", "Version", plan.Version, true, "Version (e.g., 1.0.0)"))
	basicSection.Add(fb.createTextAreaField("description", "Description", plan.Description, false, "Test plan description"))
	form.Add(basicSection)

	// Execution Configuration Section
	execSection := fb.createSection("Execution Configuration")
	execSection.Add(fb.createDurationField("duration", "Duration", plan.Duration, true, "Total test duration (e.g., 5m, 1h)"))
	execSection.Add(fb.createNumberField("concurrency", "Concurrency", plan.Concurrency, true, "Number of concurrent users"))
	execSection.Add(fb.createDurationField("ramp_up", "Ramp Up", plan.RampUp, false, "Ramp up time (e.g., 30s, 2m)"))
	form.Add(execSection)

	fb.container = form
	return form
}

// buildScenarioForm creates a form for Scenario
func (fb *FormBuilder) buildScenarioForm(scenario *parser.Scenario) *fyne.Container {
	form := container.NewVBox()

	// Basic Information
	basicSection := fb.createSection("Scenario Information")
	basicSection.Add(fb.createTextField("name", "Name", scenario.Name, true, "Scenario name"))
	basicSection.Add(fb.createTextAreaField("description", "Description", scenario.Description, false, "Scenario description"))
	basicSection.Add(fb.createNumberField("weight", "Weight", scenario.Weight, false, "Scenario weight (1-100)"))
	form.Add(basicSection)

	fb.container = form
	return form
}

// buildRequestForm creates a form for Request
func (fb *FormBuilder) buildRequestForm(request *parser.Request) *fyne.Container {
	form := container.NewVBox()

	// Basic Information
	basicSection := fb.createSection("Request Information")
	basicSection.Add(fb.createTextField("name", "Name", request.Name, false, "Request name"))
	basicSection.Add(fb.createSelectField("method", "HTTP Method", request.Method, true,
		[]string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"}))
	basicSection.Add(fb.createTextField("url", "URL", request.URL, true, "Request URL"))
	basicSection.Add(fb.createDurationField("timeout", "Timeout", request.Timeout, false, "Request timeout"))
	form.Add(basicSection)

	fb.container = form
	return form
}

// buildAssertionForm creates a form for Assertion
func (fb *FormBuilder) buildAssertionForm(assertion *parser.Assertion) *fyne.Container {
	form := container.NewVBox()

	basicSection := fb.createSection("Assertion")
	basicSection.Add(fb.createSelectField("type", "Type", assertion.Type, true,
		[]string{"status", "header", "body", "json", "xpath"}))
	basicSection.Add(fb.createSelectField("operator", "Operator", assertion.Operator, true,
		[]string{"equals", "contains", "exists", "not_exists", "greater_than", "less_than"}))
	basicSection.Add(fb.createTextField("value", "Value", fmt.Sprintf("%v", assertion.Value), false, "Expected value"))
	form.Add(basicSection)

	fb.container = form
	return form
}

// buildExtractForm creates a form for Extract
func (fb *FormBuilder) buildExtractForm(extract *parser.Extract) *fyne.Container {
	form := container.NewVBox()

	basicSection := fb.createSection("Extract")
	basicSection.Add(fb.createTextField("name", "Name", extract.Name, true, "Variable name"))
	basicSection.Add(fb.createSelectField("type", "Type", extract.Type, true,
		[]string{"json", "xpath", "regex", "header"}))
	basicSection.Add(fb.createTextField("path", "Path/Expression", extract.Path, true, "JSON path, XPath, or regex"))
	form.Add(basicSection)

	fb.container = form
	return form
}

// buildVariableForm creates a form for Variable
func (fb *FormBuilder) buildVariableForm(variable *parser.Variable) *fyne.Container {
	form := container.NewVBox()

	basicSection := fb.createSection("Variable")
	basicSection.Add(fb.createTextField("name", "Name", variable.Name, true, "Variable name"))
	basicSection.Add(fb.createSelectField("type", "Type", variable.Type, true,
		[]string{"static", "random", "faker", "csv", "counter"}))
	basicSection.Add(fb.createTextField("value", "Value", fmt.Sprintf("%v", variable.Value), false, "Variable value"))
	form.Add(basicSection)

	fb.container = form
	return form
}

// buildGlobalForm creates a form for Global
func (fb *FormBuilder) buildGlobalForm(global *parser.Global) *fyne.Container {
	form := container.NewVBox()

	basicSection := fb.createSection("Global Configuration")
	basicSection.Add(fb.createTextField("base_url", "Base URL", global.BaseURL, false, "Base URL for requests"))
	basicSection.Add(fb.createDurationField("timeout", "Default Timeout", global.Timeout, false, "Default request timeout"))
	// Remove retry_count field as it doesn't exist in Global struct
	form.Add(basicSection)

	fb.container = form
	return form
}

// buildOutputForm creates a form for Output
func (fb *FormBuilder) buildOutputForm(output *parser.Output) *fyne.Container {
	form := container.NewVBox()

	basicSection := fb.createSection("Output Configuration")
	formatValue := ""
	if len(output.Format) > 0 {
		formatValue = output.Format[0]
	}
	basicSection.Add(fb.createSelectField("format", "Format", formatValue, true,
		[]string{"json", "xml", "csv", "yaml"}))
	basicSection.Add(fb.createTextField("file", "Output File", output.File, false, "Output file path"))
	form.Add(basicSection)

	fb.container = form
	return form
}

// Helper methods for creating form elements

func (fb *FormBuilder) createSection(title string) *fyne.Container {
	label := widget.NewLabel(title)
	label.TextStyle = fyne.TextStyle{Bold: true}
	return container.NewVBox(label)
}

func (fb *FormBuilder) createTextField(name, label, value string, required bool, help string) *fyne.Container {
	labelWidget := widget.NewLabel(label)
	if required {
		labelWidget.Text += " *"
	}

	entry := widget.NewEntry()
	entry.SetText(value)
	entry.OnChanged = func(text string) {
		fb.onFieldChange(name, text)
		fb.validateField(name, text, required)
	}

	field := &FormField{
		Name:     name,
		Label:    label,
		Type:     FieldTypeText,
		Required: required,
		Help:     help,
		Value:    value,
		Widget:   entry,
	}
	fb.fields = append(fb.fields, field)

	containerWidget := container.NewVBox(labelWidget, entry)
	if help != "" {
		helpLabel := widget.NewLabel(help)
		helpLabel.TextStyle = fyne.TextStyle{Italic: true}
		containerWidget.Add(helpLabel)
	}

	return containerWidget
}

// Enhanced createTextAreaField with syntax highlighting and advanced features
func (fb *FormBuilder) createTextAreaField(name, label, value string, required bool, help string) *fyne.Container {
	labelWidget := widget.NewLabel(label)
	if required {
		labelWidget.Text += " *"
	}

	var entry fyne.Widget

	if fb.enableAdvancedEditing {
		// Detect language based on field name and content
		language := fb.detectLanguage(name, value)

		// Create advanced syntax highlighting entry
		syntaxEntry := NewSyntaxHighlightEntry(language)
		syntaxEntry.SetText(value)
		syntaxEntry.MultiLine = true
		syntaxEntry.Wrapping = fyne.TextWrapWord

		// Set up callbacks
		syntaxEntry.OnChanged = func(text string) {
			fb.onFieldChange(name, text)
			fb.validateField(name, text, required)
		}

		syntaxEntry.SetCompletionCallback(func(completion string) {
			fb.logger.Printf("Auto-completion used: %s", completion)
		})

		entry = syntaxEntry
	} else {
		// Fallback to standard multi-line entry
		standardEntry := widget.NewMultiLineEntry()
		standardEntry.SetText(value)
		standardEntry.OnChanged = func(text string) {
			fb.onFieldChange(name, text)
			fb.validateField(name, text, required)
		}
		entry = standardEntry
	}

	field := &FormField{
		Name:     name,
		Label:    label,
		Type:     FieldTypeTextArea,
		Required: required,
		Help:     help,
		Value:    value,
		Widget:   entry,
	}
	fb.fields = append(fb.fields, field)

	containerWidget := container.NewVBox(labelWidget, entry)

	// Enhanced help display
	if help != "" {
		helpLabel := widget.NewLabel(help)
		helpLabel.TextStyle = fyne.TextStyle{Italic: true}

		if fb.enableAdvancedEditing {
			// Add keyboard shortcuts info
			shortcutsHelp := widget.NewLabel("💡 Shortcuts: Ctrl+Space (autocomplete), Ctrl+H (help), Tab (accept)")
			shortcutsHelp.TextStyle = fyne.TextStyle{Italic: true}
			containerWidget.Add(helpLabel)
			containerWidget.Add(shortcutsHelp)
		} else {
			containerWidget.Add(helpLabel)
		}
	}

	return containerWidget
}

// detectLanguage determines the appropriate language for syntax highlighting
func (fb *FormBuilder) detectLanguage(fieldName, content string) Language {
	fieldName = strings.ToLower(fieldName)
	content = strings.TrimSpace(content)

	// Field name-based detection
	switch {
	case strings.Contains(fieldName, "script") || strings.Contains(fieldName, "javascript") || strings.Contains(fieldName, "js"):
		return LanguageJavaScript
	case strings.Contains(fieldName, "xpath") || strings.Contains(fieldName, "path"):
		return LanguageXPath
	case strings.Contains(fieldName, "regex") || strings.Contains(fieldName, "pattern"):
		return LanguageRegex
	case strings.Contains(fieldName, "json"):
		return LanguageJSON
	case strings.Contains(fieldName, "yaml") || strings.Contains(fieldName, "yml"):
		return LanguageYAML
	}

	// Content-based detection
	if content != "" {
		// Try to detect JSON
		if (strings.HasPrefix(content, "{") && strings.HasSuffix(content, "}")) ||
			(strings.HasPrefix(content, "[") && strings.HasSuffix(content, "]")) {
			var js json.RawMessage
			if json.Unmarshal([]byte(content), &js) == nil {
				return LanguageJSON
			}
		}

		// Try to detect YAML
		if strings.Contains(content, ":") && !strings.Contains(content, "{") {
			var yamlData interface{}
			if yaml.Unmarshal([]byte(content), &yamlData) == nil {
				return LanguageYAML
			}
		}

		// Try to detect XPath
		if strings.Contains(content, "//") || strings.Contains(content, "/@") ||
			strings.Contains(content, "text()") || strings.Contains(content, "contains(") {
			return LanguageXPath
		}

		// Try to detect regex patterns
		if _, err := regexp.Compile(content); err == nil &&
			(strings.Contains(content, "\\") || strings.Contains(content, "[") ||
				strings.Contains(content, "*") || strings.Contains(content, "+")) {
			return LanguageRegex
		}

		// Try to detect JavaScript
		if strings.Contains(content, "function") || strings.Contains(content, "var ") ||
			strings.Contains(content, "let ") || strings.Contains(content, "const ") ||
			strings.Contains(content, "JSON.") || strings.Contains(content, "response.") {
			return LanguageJavaScript
		}
	}

	// Default to text
	return LanguageText
}

func (fb *FormBuilder) createNumberField(name, label string, value int, required bool, help string) *fyne.Container {
	labelWidget := widget.NewLabel(label)
	if required {
		labelWidget.Text += " *"
	}

	entry := widget.NewEntry()
	entry.SetText(strconv.Itoa(value))
	entry.OnChanged = func(text string) {
		if num, err := strconv.Atoi(text); err == nil {
			fb.onFieldChange(name, num)
			fb.validateField(name, num, required)
		}
	}

	field := &FormField{
		Name:     name,
		Label:    label,
		Type:     FieldTypeNumber,
		Required: required,
		Help:     help,
		Value:    value,
		Widget:   entry,
	}
	fb.fields = append(fb.fields, field)

	containerWidget := container.NewVBox(labelWidget, entry)
	if help != "" {
		helpLabel := widget.NewLabel(help)
		helpLabel.TextStyle = fyne.TextStyle{Italic: true}
		containerWidget.Add(helpLabel)
	}

	return containerWidget
}

func (fb *FormBuilder) createSelectField(name, label, value string, required bool, options []string) *fyne.Container {
	labelWidget := widget.NewLabel(label)
	if required {
		labelWidget.Text += " *"
	}

	selectWidget := widget.NewSelect(options, func(selected string) {
		fb.onFieldChange(name, selected)
		fb.validateField(name, selected, required)
	})
	selectWidget.SetSelected(value)

	field := &FormField{
		Name:     name,
		Label:    label,
		Type:     FieldTypeSelect,
		Required: required,
		Options:  options,
		Value:    value,
		Widget:   selectWidget,
	}
	fb.fields = append(fb.fields, field)

	return container.NewVBox(labelWidget, selectWidget)
}

func (fb *FormBuilder) createCheckboxField(name, label string, value bool, help string) *fyne.Container {
	checkbox := widget.NewCheck(label, func(checked bool) {
		fb.onFieldChange(name, checked)
	})
	checkbox.SetChecked(value)

	field := &FormField{
		Name:   name,
		Label:  label,
		Type:   FieldTypeCheckbox,
		Help:   help,
		Value:  value,
		Widget: checkbox,
	}
	fb.fields = append(fb.fields, field)

	return container.NewVBox(checkbox)
}

func (fb *FormBuilder) createDurationField(name, label string, value parser.Duration, required bool, help string) *fyne.Container {
	labelWidget := widget.NewLabel(label)
	if required {
		labelWidget.Text += " *"
	}

	entry := widget.NewEntry()
	entry.SetText(value.String())
	entry.OnChanged = func(text string) {
		if duration, err := time.ParseDuration(text); err == nil {
			parsedDuration := parser.Duration{Duration: duration}
			fb.onFieldChange(name, parsedDuration)
			fb.validateField(name, parsedDuration, required)
		}
	}

	field := &FormField{
		Name:     name,
		Label:    label,
		Type:     FieldTypeDuration,
		Required: required,
		Help:     help,
		Value:    value,
		Widget:   entry,
	}
	fb.fields = append(fb.fields, field)

	containerWidget := container.NewVBox(labelWidget, entry)
	if help != "" {
		helpLabel := widget.NewLabel(help)
		helpLabel.TextStyle = fyne.TextStyle{Italic: true}
		containerWidget.Add(helpLabel)
	}

	return containerWidget
}

func (fb *FormBuilder) validateField(name string, value interface{}, required bool) {
	var errors []string

	if required && fb.isEmpty(value) {
		errors = append(errors, fmt.Sprintf("%s is required", name))
	}

	// Find field and run custom validation if present
	for _, field := range fb.fields {
		if field.Name == name && field.Validation != nil {
			if err := field.Validation(value); err != nil {
				errors = append(errors, err.Error())
			}
			break
		}
	}

	// Notify validation callback
	fb.onValidationChange(len(errors) == 0, errors)
}

func (fb *FormBuilder) isEmpty(value interface{}) bool {
	switch v := value.(type) {
	case string:
		return strings.TrimSpace(v) == ""
	case int:
		return v == 0
	case parser.Duration:
		return v.Duration == 0
	default:
		return value == nil
	}
}

// GetFormData returns the current form data
func (fb *FormBuilder) GetFormData() interface{} {
	return fb.data
}

// ValidateForm validates all form fields
func (fb *FormBuilder) ValidateForm() (bool, []string) {
	var allErrors []string
	for _, field := range fb.fields {
		if field.Required && fb.isEmpty(field.Value) {
			allErrors = append(allErrors, fmt.Sprintf("%s is required", field.Label))
		}
		if field.Validation != nil {
			if err := field.Validation(field.Value); err != nil {
				allErrors = append(allErrors, err.Error())
			}
		}
	}
	return len(allErrors) == 0, allErrors
}

// RefreshForm refreshes the form with new data
func (fb *FormBuilder) RefreshForm(data interface{}) {
	fb.data = data
	// Rebuild form - this is a simplified implementation
	// In a full implementation, you'd update field values without rebuilding
}

// Default completion provider implementations
type DefaultCompletionProvider struct{}

func NewDefaultCompletionProvider() *DefaultCompletionProvider {
	return &DefaultCompletionProvider{}
}

func (dcp *DefaultCompletionProvider) GetCompletions(prefix string, context string) []string {
	return []string{}
}

func (dcp *DefaultCompletionProvider) GetHelp(keyword string) string {
	return ""
}

// Default help provider
type DefaultHelpProvider struct{}

func NewDefaultHelpProvider() *DefaultHelpProvider {
	return &DefaultHelpProvider{}
}

func (dhp *DefaultHelpProvider) GetCompletions(prefix string, context string) []string {
	return []string{}
}

func (dhp *DefaultHelpProvider) GetHelp(keyword string) string {
	return ""
}

// YAML completion provider
type YAMLCompletionProvider struct {
	keywords []string
}

func NewYAMLCompletionProvider() *YAMLCompletionProvider {
	return &YAMLCompletionProvider{
		keywords: []string{
			"name", "description", "version", "scenarios", "requests", "method", "url", "headers",
			"body", "timeout", "assertions", "extracts", "variables", "global", "output",
			"duration", "concurrency", "ramp_up", "type", "operator", "value", "path",
			"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS",
			"status", "header", "json", "xpath", "regex", "equals", "contains", "exists",
		},
	}
}

func (ycp *YAMLCompletionProvider) GetCompletions(prefix string, context string) []string {
	var matches []string
	for _, keyword := range ycp.keywords {
		if strings.HasPrefix(strings.ToLower(keyword), strings.ToLower(prefix)) {
			matches = append(matches, keyword)
		}
	}
	return matches
}

func (ycp *YAMLCompletionProvider) GetHelp(keyword string) string {
	helpMap := map[string]string{
		"name":        "**Name**: Unique identifier for the test plan or scenario",
		"description": "**Description**: Human-readable description of the test plan or scenario",
		"method":      "**Method**: HTTP method (GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS)",
		"url":         "**URL**: Target URL for the HTTP request",
		"timeout":     "**Timeout**: Maximum time to wait for response (e.g., '30s', '5m')",
		"assertions":  "**Assertions**: Validation rules to check response correctness",
		"extracts":    "**Extracts**: Data extraction rules for capturing values from responses",
	}

	if help, exists := helpMap[strings.ToLower(keyword)]; exists {
		return help
	}
	return ""
}

// YAML help provider
type YAMLHelpProvider struct{}

func NewYAMLHelpProvider() *YAMLHelpProvider {
	return &YAMLHelpProvider{}
}

func (yhp *YAMLHelpProvider) GetCompletions(prefix string, context string) []string {
	return []string{}
}

func (yhp *YAMLHelpProvider) GetHelp(keyword string) string {
	return NewYAMLCompletionProvider().GetHelp(keyword)
}

// JSON completion provider
type JSONCompletionProvider struct {
	keywords []string
}

func NewJSONCompletionProvider() *JSONCompletionProvider {
	return &JSONCompletionProvider{
		keywords: []string{
			"name", "description", "version", "scenarios", "requests", "method", "url", "headers",
			"body", "timeout", "assertions", "extracts", "variables", "global", "output",
			"duration", "concurrency", "ramp_up", "type", "operator", "value", "path",
		},
	}
}

func (jcp *JSONCompletionProvider) GetCompletions(prefix string, context string) []string {
	var matches []string
	for _, keyword := range jcp.keywords {
		if strings.HasPrefix(strings.ToLower(keyword), strings.ToLower(prefix)) {
			matches = append(matches, "\""+keyword+"\"")
		}
	}
	return matches
}

func (jcp *JSONCompletionProvider) GetHelp(keyword string) string {
	return NewYAMLCompletionProvider().GetHelp(keyword) // Reuse YAML help
}

// JSON help provider
type JSONHelpProvider struct{}

func NewJSONHelpProvider() *JSONHelpProvider {
	return &JSONHelpProvider{}
}

func (jhp *JSONHelpProvider) GetCompletions(prefix string, context string) []string {
	return []string{}
}

func (jhp *JSONHelpProvider) GetHelp(keyword string) string {
	return NewJSONCompletionProvider().GetHelp(keyword)
}

// JavaScript completion provider
type JavaScriptCompletionProvider struct {
	keywords []string
}

func NewJavaScriptCompletionProvider() *JavaScriptCompletionProvider {
	return &JavaScriptCompletionProvider{
		keywords: []string{
			"function", "var", "let", "const", "if", "else", "for", "while", "return",
			"JSON", "parse", "stringify", "response", "request", "body", "headers",
			"Math", "String", "Number", "Array", "Object", "console", "log",
		},
	}
}

func (jscp *JavaScriptCompletionProvider) GetCompletions(prefix string, context string) []string {
	var matches []string
	for _, keyword := range jscp.keywords {
		if strings.HasPrefix(strings.ToLower(keyword), strings.ToLower(prefix)) {
			matches = append(matches, keyword)
		}
	}
	return matches
}

func (jscp *JavaScriptCompletionProvider) GetHelp(keyword string) string {
	helpMap := map[string]string{
		"function": "**function**: Declares a JavaScript function",
		"JSON":     "**JSON**: Built-in object for parsing and stringifying JSON data",
		"response": "**response**: HTTP response object containing status, headers, and body",
		"request":  "**request**: HTTP request object containing method, url, headers, and body",
		"console":  "**console**: Provides access to debugging console",
	}

	if help, exists := helpMap[strings.ToLower(keyword)]; exists {
		return help
	}
	return ""
}

// JavaScript help provider
type JavaScriptHelpProvider struct{}

func NewJavaScriptHelpProvider() *JavaScriptHelpProvider {
	return &JavaScriptHelpProvider{}
}

func (jshp *JavaScriptHelpProvider) GetCompletions(prefix string, context string) []string {
	return []string{}
}

func (jshp *JavaScriptHelpProvider) GetHelp(keyword string) string {
	return NewJavaScriptCompletionProvider().GetHelp(keyword)
}

// XPath completion provider
type XPathCompletionProvider struct {
	keywords []string
}

func NewXPathCompletionProvider() *XPathCompletionProvider {
	return &XPathCompletionProvider{
		keywords: []string{
			"//", "/", "@", "text()", "node()", "contains()", "starts-with()", "ends-with()",
			"position()", "last()", "count()", "sum()", "string()", "number()",
			"div", "span", "p", "a", "img", "input", "button", "table", "tr", "td",
		},
	}
}

func (xcp *XPathCompletionProvider) GetCompletions(prefix string, context string) []string {
	var matches []string
	for _, keyword := range xcp.keywords {
		if strings.HasPrefix(strings.ToLower(keyword), strings.ToLower(prefix)) {
			matches = append(matches, keyword)
		}
	}
	return matches
}

func (xcp *XPathCompletionProvider) GetHelp(keyword string) string {
	helpMap := map[string]string{
		"//":            "**//**: Selects nodes anywhere in the document",
		"/":             "**/**: Selects from the document root",
		"@":             "**@**: Selects attributes",
		"text()":        "**text()**: Selects the text content of a node",
		"contains()":    "**contains()**: Tests if a string contains a substring",
		"starts-with()": "**starts-with()**: Tests if a string starts with a substring",
	}

	if help, exists := helpMap[strings.ToLower(keyword)]; exists {
		return help
	}
	return ""
}

// XPath help provider
type XPathHelpProvider struct{}

func NewXPathHelpProvider() *XPathHelpProvider {
	return &XPathHelpProvider{}
}

func (xhp *XPathHelpProvider) GetCompletions(prefix string, context string) []string {
	return []string{}
}

func (xhp *XPathHelpProvider) GetHelp(keyword string) string {
	return NewXPathCompletionProvider().GetHelp(keyword)
}

// Regex completion provider
type RegexCompletionProvider struct {
	keywords []string
}

func NewRegexCompletionProvider() *RegexCompletionProvider {
	return &RegexCompletionProvider{
		keywords: []string{
			"\\d", "\\w", "\\s", "\\D", "\\W", "\\S", "\\b", "\\B",
			"*", "+", "?", "{}", "[]", "()", "|", "^", "$", ".",
			"\\n", "\\r", "\\t", "\\f", "\\v", "\\0",
		},
	}
}

func (rcp *RegexCompletionProvider) GetCompletions(prefix string, context string) []string {
	var matches []string
	for _, keyword := range rcp.keywords {
		if strings.HasPrefix(keyword, prefix) {
			matches = append(matches, keyword)
		}
	}
	return matches
}

func (rcp *RegexCompletionProvider) GetHelp(keyword string) string {
	helpMap := map[string]string{
		"\\d": "**\\d**: Matches any digit (0-9)",
		"\\w": "**\\w**: Matches any word character (a-z, A-Z, 0-9, _)",
		"\\s": "**\\s**: Matches any whitespace character",
		"*":   "*****: Matches 0 or more of the preceding character",
		"+":   "**+**: Matches 1 or more of the preceding character",
		"?":   "**?**: Matches 0 or 1 of the preceding character",
		"^":   "**^**: Matches the beginning of a line",
		"$":   "**$**: Matches the end of a line",
	}

	if help, exists := helpMap[keyword]; exists {
		return help
	}
	return ""
}

// Regex help provider
type RegexHelpProvider struct{}

func NewRegexHelpProvider() *RegexHelpProvider {
	return &RegexHelpProvider{}
}

func (rhp *RegexHelpProvider) GetCompletions(prefix string, context string) []string {
	return []string{}
}

func (rhp *RegexHelpProvider) GetHelp(keyword string) string {
	return NewRegexCompletionProvider().GetHelp(keyword)
}
