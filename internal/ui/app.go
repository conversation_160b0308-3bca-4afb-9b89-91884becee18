//go:build gui
// +build gui

package ui

import (
	"fmt"
	"log"
	"runtime"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"
)

// AppConfig holds configuration for the NeuralMeter application
type AppConfig struct {
	AppID           string
	AppName         string
	AppVersion      string
	DefaultTheme    string
	WindowWidth     float32
	WindowHeight    float32
	WindowCentered  bool
	WindowResizable bool
}

// DefaultAppConfig returns the default configuration for NeuralMeter
func DefaultAppConfig() *AppConfig {
	return &AppConfig{
		AppID:           "com.neuralmeter.loadtester",
		AppName:         "NeuralMeter",
		AppVersion:      "1.0.0",
		DefaultTheme:    "dark",
		WindowWidth:     1200,
		WindowHeight:    800,
		WindowCentered:  true,
		WindowResizable: true,
	}
}

// NeuralMeterApp represents the main Fyne application
type NeuralMeterApp struct {
	App    fyne.App
	Window fyne.Window
	Config *AppConfig
	Logger *log.Logger
	Themes *ThemeManager
	Layout *LayoutManager

	// State management
	IsStarted bool
	IsStopped bool
}

// NewNeuralMeterApp creates a new instance of the NeuralMeter application
func NewNeuralMeterApp(config *AppConfig, logger *log.Logger) (*NeuralMeterApp, error) {
	if config == nil {
		config = DefaultAppConfig()
	}

	if logger == nil {
		logger = log.Default()
	}

	// Create Fyne app with unique ID
	fyneApp := app.NewWithID(config.AppID)

	// Create theme manager
	themeManager := NewThemeManager(fyneApp, logger)

	// Apply default theme
	if err := themeManager.SetTheme(config.DefaultTheme); err != nil {
		logger.Printf("Warning: Failed to set default theme %s: %v", config.DefaultTheme, err)
		// Fallback to system default
		fyneApp.Settings().SetTheme(theme.DefaultTheme())
	}

	app := &NeuralMeterApp{
		App:       fyneApp,
		Config:    config,
		Logger:    logger,
		Themes:    themeManager,
		IsStarted: false,
		IsStopped: false,
	}

	// Set up application metadata
	if err := app.setupApplicationMetadata(); err != nil {
		return nil, fmt.Errorf("failed to setup application metadata: %w", err)
	}

	// Set up lifecycle hooks
	app.setupLifecycleHooks()

	// Create main window
	if err := app.createMainWindow(); err != nil {
		return nil, fmt.Errorf("failed to create main window: %w", err)
	}

	return app, nil
}

// setupApplicationMetadata configures application-level metadata
func (nma *NeuralMeterApp) setupApplicationMetadata() error {
	// Set application icon (using Fyne's built-in icon for now)
	nma.App.SetIcon(theme.ComputerIcon())

	// Set application name and version in metadata
	metadata := nma.App.Metadata()
	metadata.Name = nma.Config.AppName
	metadata.Version = nma.Config.AppVersion

	return nil
}

// setupLifecycleHooks configures application startup and shutdown hooks
func (nma *NeuralMeterApp) setupLifecycleHooks() {
	// Note: Fyne v2 doesn't have SetOnStarted/SetOnStopped methods
	// We'll call these methods manually during Run() and Quit()
	nma.Logger.Printf("Application lifecycle hooks configured")
}

// createMainWindow creates and configures the main application window
func (nma *NeuralMeterApp) createMainWindow() error {
	// Create main window
	nma.Window = nma.App.NewWindow(nma.Config.AppName)

	// Configure window properties
	nma.Window.SetIcon(theme.ComputerIcon())
	nma.Window.Resize(fyne.NewSize(nma.Config.WindowWidth, nma.Config.WindowHeight))

	if nma.Config.WindowCentered {
		nma.Window.CenterOnScreen()
	}

	nma.Window.SetFixedSize(!nma.Config.WindowResizable)
	nma.Window.SetPadded(true)
	nma.Window.SetMaster() // Set as main window

	// Set up window event handlers
	nma.setupWindowEventHandlers()

	// Set up keyboard shortcuts
	nma.setupKeyboardShortcuts()

	// Create layout manager
	nma.Layout = NewLayoutManager(nma, nma.Logger)

	// Set up initial content with layout manager
	nma.setupInitialLayout()

	return nil
}

// setupWindowEventHandlers configures window event handlers
func (nma *NeuralMeterApp) setupWindowEventHandlers() {
	// Handle window close with confirmation
	nma.Window.SetCloseIntercept(func() {
		nma.showExitConfirmation()
	})
}

// setupKeyboardShortcuts configures global keyboard shortcuts
func (nma *NeuralMeterApp) setupKeyboardShortcuts() {
	nma.Window.Canvas().SetOnTypedKey(func(event *fyne.KeyEvent) {
		switch event.Name {
		case fyne.KeyF11:
			// Toggle fullscreen
			nma.Window.SetFullScreen(!nma.Window.FullScreen())
		case fyne.KeyEscape:
			// Exit fullscreen if in fullscreen mode
			if nma.Window.FullScreen() {
				nma.Window.SetFullScreen(false)
			}
		}
	})
}

// setupInitialLayout sets up the initial layout with the layout manager
func (nma *NeuralMeterApp) setupInitialLayout() {
	// Set the main container as the window content
	nma.Window.SetContent(nma.Layout.GetMainContainer())

	// Create initial tabs
	nma.setupInitialTabs()

	// Update status
	nma.Layout.UpdateStatus("NeuralMeter Ready")
	nma.Layout.UpdateInfoLabel("connection", "Ready")
	nma.Layout.UpdateInfoLabel("tasks", "0 tasks")
	nma.Layout.UpdateInfoLabel("memory", "Memory: 0 MB")

	// Set initial breadcrumbs
	breadcrumbs := []BreadcrumbItem{
		{Label: "Home", Callback: func() { nma.showHomeTab() }},
		{Label: "Welcome", Callback: func() {}},
	}
	nma.Layout.UpdateBreadcrumbs(breadcrumbs)
}

// setupInitialTabs creates the initial application tabs
func (nma *NeuralMeterApp) setupInitialTabs() {
	// Welcome tab
	welcomeContent := nma.createWelcomeContent()
	nma.Layout.AddTab("Welcome", welcomeContent)

	// Test Plans tab
	testPlansContent := nma.createTestPlansContent()
	nma.Layout.AddTab("Test Plans", testPlansContent)

	// Results tab
	resultsContent := nma.createResultsContent()
	nma.Layout.AddTab("Results", resultsContent)

	// Settings tab
	settingsContent := nma.createSettingsContent()
	nma.Layout.AddTab("Settings", settingsContent)
}

// createWelcomeContent creates the welcome tab content
func (nma *NeuralMeterApp) createWelcomeContent() fyne.CanvasObject {
	// Create welcome content with theme switcher
	welcomeLabel := widget.NewRichTextFromMarkdown(`
# Welcome to NeuralMeter

## Next-Generation Load Testing Platform

NeuralMeter combines revolutionary GPU acceleration with cutting-edge authentication methods and a modern user interface.

### Features:
- **GPU-Accelerated Processing**: Harness the power of modern GPUs for unprecedented performance
- **Advanced Authentication**: Support for WebAuthn, DPoP, mTLS, and AI agent authentication
- **Modern UI**: Cross-platform interface built with Fyne v2
- **Intelligent Batching**: Memory-aware batch processing with variable input sizes
- **Real-time Analytics**: Comprehensive monitoring and reporting capabilities

*Ready to revolutionize your load testing?*
`)

	// Create theme switcher
	themeSwitcher := nma.Themes.CreateThemeSwitcher()

	// Create action buttons
	newTestButton := widget.NewButton("Create New Test", func() {
		nma.Layout.SetActiveTab(1) // Switch to Test Plans tab
	})
	newTestButton.Importance = widget.HighImportance

	openResultsButton := widget.NewButton("View Results", func() {
		nma.Layout.SetActiveTab(2) // Switch to Results tab
	})

	// Create main content container
	content := container.NewVBox(
		welcomeLabel,
		widget.NewSeparator(),
		widget.NewForm(
			widget.NewFormItem("Theme", themeSwitcher),
		),
		widget.NewSeparator(),
		container.NewHBox(
			newTestButton,
			openResultsButton,
		),
	)

	return container.NewScroll(content)
}

// createTestPlansContent creates the test plans tab content
func (nma *NeuralMeterApp) createTestPlansContent() fyne.CanvasObject {
	label := widget.NewLabel("Test Plans")
	label.TextStyle = fyne.TextStyle{Bold: true}

	placeholder := widget.NewLabel("Test plan designer will be implemented here.")

	return container.NewVBox(
		label,
		widget.NewSeparator(),
		placeholder,
	)
}

// createResultsContent creates the results tab content
func (nma *NeuralMeterApp) createResultsContent() fyne.CanvasObject {
	label := widget.NewLabel("Test Results")
	label.TextStyle = fyne.TextStyle{Bold: true}

	placeholder := widget.NewLabel("Results analysis interface will be implemented here.")

	return container.NewVBox(
		label,
		widget.NewSeparator(),
		placeholder,
	)
}

// createSettingsContent creates the settings tab content
func (nma *NeuralMeterApp) createSettingsContent() fyne.CanvasObject {
	label := widget.NewLabel("Settings")
	label.TextStyle = fyne.TextStyle{Bold: true}

	// Theme settings
	themeSwitcher := nma.Themes.CreateThemeSwitcher()

	// Layout settings
	layoutButton := widget.NewButton("Reset Layout", func() {
		nma.Layout.ApplyLayout(LayoutTypeDesktop)
		nma.Layout.UpdateStatus("Layout reset to default")
	})

	form := &widget.Form{
		Items: []*widget.FormItem{
			{Text: "Theme", Widget: themeSwitcher},
			{Text: "Layout", Widget: layoutButton},
		},
	}

	return container.NewVBox(
		label,
		widget.NewSeparator(),
		form,
	)
}

// showHomeTab switches to the welcome tab
func (nma *NeuralMeterApp) showHomeTab() {
	nma.Layout.SetActiveTab(0)
}

// showExitConfirmation displays a confirmation dialog before closing the application
func (nma *NeuralMeterApp) showExitConfirmation() {
	dialog.ShowConfirm(
		"Exit NeuralMeter",
		"Are you sure you want to exit NeuralMeter?",
		func(confirmed bool) {
			if confirmed {
				nma.Logger.Printf("User confirmed application exit")
				nma.Window.Close()
			}
		},
		nma.Window,
	)
}

// onApplicationStarted is called when the application starts
func (nma *NeuralMeterApp) onApplicationStarted() {
	// Perform platform-specific setup
	nma.platformSpecificSetup()

	// Load user preferences
	nma.loadUserPreferences()

	// Initialize subsystems (placeholder for future implementation)
	// - GPU subsystem initialization
	// - Authentication system setup
	// - Metrics system startup
}

// onApplicationStopped is called when the application stops
func (nma *NeuralMeterApp) onApplicationStopped() {
	// Save user preferences
	nma.saveUserPreferences()

	// Cleanup resources
	// - GPU resource cleanup
	// - Close network connections
	// - Save state to disk
}

// platformSpecificSetup performs platform-specific initialization
func (nma *NeuralMeterApp) platformSpecificSetup() {
	switch runtime.GOOS {
	case "darwin":
		nma.setupMacOSFeatures()
	case "windows":
		nma.setupWindowsFeatures()
	case "linux":
		nma.setupLinuxFeatures()
	}
}

// setupMacOSFeatures configures macOS-specific features
func (nma *NeuralMeterApp) setupMacOSFeatures() {
	nma.Logger.Printf("Setting up macOS-specific features")
	// Future: System tray menu, native notifications, etc.
}

// setupWindowsFeatures configures Windows-specific features
func (nma *NeuralMeterApp) setupWindowsFeatures() {
	nma.Logger.Printf("Setting up Windows-specific features")
	// Future: Windows integration features
}

// setupLinuxFeatures configures Linux-specific features
func (nma *NeuralMeterApp) setupLinuxFeatures() {
	nma.Logger.Printf("Setting up Linux-specific features")
	// Future: Linux desktop integration
}

// loadUserPreferences loads user preferences from storage
func (nma *NeuralMeterApp) loadUserPreferences() {
	// Load theme preference
	if savedTheme := nma.App.Preferences().String("theme"); savedTheme != "" {
		if err := nma.Themes.SetTheme(savedTheme); err != nil {
			nma.Logger.Printf("Warning: Failed to load saved theme %s: %v", savedTheme, err)
		}
	}

	// Load window preferences
	if width := nma.App.Preferences().Float("window.width"); width > 0 {
		if height := nma.App.Preferences().Float("window.height"); height > 0 {
			nma.Window.Resize(fyne.NewSize(float32(width), float32(height)))
		}
	}

	// Load layout state
	if nma.Layout != nil {
		if err := nma.Layout.LoadLayoutState(); err != nil {
			nma.Logger.Printf("Warning: Failed to load layout state: %v", err)
		}
	}
}

// saveUserPreferences saves current user preferences to storage
func (nma *NeuralMeterApp) saveUserPreferences() {
	// Save current theme
	nma.App.Preferences().SetString("theme", nma.Themes.GetCurrentTheme())

	// Save window size
	size := nma.Window.Canvas().Size()
	nma.App.Preferences().SetFloat("window.width", float64(size.Width))
	nma.App.Preferences().SetFloat("window.height", float64(size.Height))

	// Save layout state
	if nma.Layout != nil {
		if err := nma.Layout.SaveLayoutState(); err != nil {
			nma.Logger.Printf("Warning: Failed to save layout state: %v", err)
		}
	}
}

// Run starts and runs the application
func (nma *NeuralMeterApp) Run() {
	nma.Logger.Printf("Starting NeuralMeter application...")
	nma.IsStarted = true
	nma.onApplicationStarted()
	nma.Window.ShowAndRun()
}

// Quit gracefully shuts down the application
func (nma *NeuralMeterApp) Quit() {
	nma.IsStopped = true
	nma.onApplicationStopped()
	if nma.Window != nil {
		nma.Window.Close()
	}
}

// GetApp returns the underlying Fyne app
func (nma *NeuralMeterApp) GetApp() fyne.App {
	return nma.App
}

// GetWindow returns the main window
func (nma *NeuralMeterApp) GetWindow() fyne.Window {
	return nma.Window
}

// IsRunning returns true if the application is currently running
func (nma *NeuralMeterApp) IsRunning() bool {
	return nma.IsStarted && !nma.IsStopped
}
