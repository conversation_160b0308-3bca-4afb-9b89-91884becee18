package xmlxpath

import (
	"bytes"
	"encoding/xml"
	"errors"
	"fmt"
	"strings"
)

// XPathEngine provides XPath parsing and evaluation for XML documents
type XPathEngine struct{}

// NewXPathEngine creates a new XPathEngine instance
func NewXPathEngine() *XPathEngine {
	return &XPathEngine{}
}

// Evaluate parses and evaluates an XPath expression against the given XML document
// Returns a slice of extracted string values (text or attribute values)
func (xe *XPathEngine) Evaluate(expr string, xmlData []byte) ([]string, error) {
	// 1. Parse XML into DOM (flat stack for simple traversal)
	type node struct {
		Name     string
		NS       string // Namespace URI
		Attr     []xml.Attr
		Children []*node
		Text     string
	}

	var root *node
	var stack []*node
	var nsMap = make(map[string]string) // prefix -> URI

	dec := xml.NewDecoder(bytes.NewReader(xmlData))
	for {
		tok, err := dec.Token()
		if err != nil {
			if err.Error() == "EOF" {
				break
			}
			return nil, fmt.<PERSON>rrorf("malformed XML: %w", err)
		}
		switch t := tok.(type) {
		case xml.StartElement:
			// Build namespace map from xmlns attributes
			for _, attr := range t.Attr {
				if attr.Name.Space == "xmlns" {
					nsMap[attr.Name.Local] = attr.Value
				} else if attr.Name.Local == "xmlns" && attr.Name.Space == "" {
					nsMap["_"] = attr.Value // default namespace
				}
			}
			// Determine element namespace
			nsURI := t.Name.Space // encoding/xml sets this to the resolved URI
			n := &node{Name: t.Name.Local, NS: nsURI, Attr: t.Attr}
			if len(stack) > 0 {
				parent := stack[len(stack)-1]
				parent.Children = append(parent.Children, n)
			}
			stack = append(stack, n)
			if root == nil {
				root = n
			}
		case xml.EndElement:
			if len(stack) > 0 {
				stack = stack[:len(stack)-1]
			}
		case xml.CharData:
			if len(stack) > 0 {
				stack[len(stack)-1].Text += string(t)
			}
		}
	}

	if root == nil {
		return nil, errors.New("empty or invalid XML")
	}

	// 2. Basic XPath syntax validation (very minimal for now)
	expr = strings.TrimSpace(expr)
	if expr == "" || (!strings.HasPrefix(expr, "/") && !strings.HasPrefix(expr, ".")) {
		return nil, errors.New("invalid XPath: must start with '/' or '.'")
	}
	if strings.Count(expr, "[") != strings.Count(expr, "]") {
		return nil, errors.New("invalid XPath: unmatched brackets")
	}
	if strings.Contains(expr, "@") && !strings.Contains(expr, "/@") {
		return nil, errors.New("invalid XPath: attribute selector must use '/@attr'")
	}

	// 3. Minimal evaluation: support only absolute paths like /root/item and /root/item/@attr
	var attrName string
	if strings.Contains(expr, "/@") {
		parts := strings.Split(expr, "/@")
		if len(parts) != 2 || parts[1] == "" {
			return nil, errors.New("invalid XPath: malformed attribute selector")
		}
		expr = parts[0]
		attrName = parts[1]
	}
	if strings.Contains(expr, "[") || strings.Contains(expr, "]") {
		return nil, errors.New("predicates not supported yet")
	}

	// In traversal, update path segment parsing to support prefix:localname and resolve prefix to URI
	segments := strings.Split(expr, "/")
	var path []struct{ NS, Name string }
	for _, seg := range segments {
		if seg != "" {
			if strings.Contains(seg, ":") {
				parts := strings.SplitN(seg, ":", 2)
				prefix := parts[0]
				name := parts[1]
				uri := nsMap[prefix]
				path = append(path, struct{ NS, Name string }{NS: uri, Name: name})
			} else {
				// For default namespace, use nsMap["_"] if present
				uri := nsMap["_"]
				path = append(path, struct{ NS, Name string }{NS: uri, Name: seg})
			}
		}
	}
	if len(path) == 0 {
		return nil, errors.New("invalid XPath: no path segments")
	}

	// Traverse the tree
	var results []*node
	var traverse func(n *node, depth int)
	traverse = func(n *node, depth int) {
		if depth >= len(path) {
			return
		}
		seg := path[depth]
		match := n.Name == seg.Name && n.NS == seg.NS
		if match {
			if depth == len(path)-1 {
				results = append(results, n)
			} else {
				for _, c := range n.Children {
					traverse(c, depth+1)
				}
			}
		} else {
			for _, c := range n.Children {
				traverse(c, depth)
			}
		}
	}
	traverse(root, 0)

	var out []string
	if attrName != "" {
		// Attribute extraction
		for _, n := range results {
			for _, attr := range n.Attr {
				if attr.Name.Local == attrName {
					out = append(out, attr.Value)
				}
			}
		}
		return out, nil
	}

	// Collect text content
	for _, n := range results {
		text := strings.TrimSpace(n.Text)
		if text != "" {
			out = append(out, text)
		}
	}
	return out, nil
}
