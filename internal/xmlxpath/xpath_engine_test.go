package xmlxpath

import (
	"testing"
)

func TestXPathEngine_Evaluate_Valid(t *testing.T) {
	xml := []byte(`<root><item>one</item><item>two</item></root>`)
	xe := NewXPathEngine()
	res, err := xe.Evaluate("/root/item", xml)
	if err != nil {
		t.<PERSON><PERSON>("Expected no error, got: %v", err)
	}
	if len(res) != 2 || res[0] != "one" || res[1] != "two" {
		t.<PERSON><PERSON><PERSON>("Expected [one two], got: %v", res)
	}
}

func TestXPathEngine_Evaluate_InvalidXPath(t *testing.T) {
	xml := []byte(`<root><item>one</item></root>`)
	xe := NewXPathEngine()
	_, err := xe.Evaluate("/root/[@", xml)
	if err == nil {
		t.<PERSON>("Expected error for invalid XPath expression")
	}
}

func TestXPathEngine_Evaluate_MalformedXML(t *testing.T) {
	xml := []byte(`<root><item>one</item>`)
	xe := NewXPathEngine()
	_, err := xe.Evaluate("/root/item", xml)
	if err == nil {
		t.Error("Expected error for malformed XML")
	}
}

func TestXPathEngine_Evaluate_EmptyResult(t *testing.T) {
	xml := []byte(`<root><item>one</item></root>`)
	xe := NewXPathEngine()
	res, err := xe.Evaluate("/root/doesnotexist", xml)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if len(res) != 0 {
		t.Errorf("Expected empty result, got: %v", res)
	}
}

func TestXPathEngine_Evaluate_Attribute_NotImplemented(t *testing.T) {
	xml := []byte(`<root><item id='x'>one</item></root>`)
	xe := NewXPathEngine()
	res, err := xe.Evaluate("/root/item/@id", xml)
	if err != nil {
		t.Errorf("Expected no error for attribute extraction, got: %v", err)
	}
	if len(res) != 1 || res[0] != "x" {
		t.Errorf("Expected [x], got: %v", res)
	}

	xml2 := []byte(`<root><item id='x'>one</item><item id='y'>two</item></root>`)
	res2, err := xe.Evaluate("/root/item/@id", xml2)
	if err != nil {
		t.Errorf("Expected no error for attribute extraction, got: %v", err)
	}
	if len(res2) != 2 || res2[0] != "x" || res2[1] != "y" {
		t.Errorf("Expected [x y], got: %v", res2)
	}

	// Missing attribute
	xml3 := []byte(`<root><item>one</item></root>`)
	res3, err := xe.Evaluate("/root/item/@id", xml3)
	if err != nil {
		t.Errorf("Expected no error for missing attribute, got: %v", err)
	}
	if len(res3) != 0 {
		t.Errorf("Expected empty result for missing attribute, got: %v", res3)
	}

	// No matching elements
	xml4 := []byte(`<root><foo id='z'>bar</foo></root>`)
	res4, err := xe.Evaluate("/root/item/@id", xml4)
	if err != nil {
		t.Errorf("Expected no error for no matching elements, got: %v", err)
	}
	if len(res4) != 0 {
		t.Errorf("Expected empty result for no matching elements, got: %v", res4)
	}
}

func TestXPathEngine_Evaluate_NamespaceSupport(t *testing.T) {
	xml := []byte(`<root xmlns:h="http://example.com/hello"><h:item id="a">one</h:item><h:item id="b">two</h:item></root>`)
	xe := NewXPathEngine()
	res, err := xe.Evaluate("/root/h:item", xml)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if len(res) != 2 || res[0] != "one" || res[1] != "two" {
		t.Errorf("Expected [one two], got: %v", res)
	}

	// Attribute extraction with namespace
	res2, err := xe.Evaluate("/root/h:item/@id", xml)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if len(res2) != 2 || res2[0] != "a" || res2[1] != "b" {
		t.Errorf("Expected [a b], got: %v", res2)
	}

	// Default namespace
	xml2 := []byte(`<root xmlns="http://example.com/hello"><item>foo</item></root>`)
	res3, err := xe.Evaluate("/root/item", xml2)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if len(res3) != 1 || res3[0] != "foo" {
		t.Errorf("Expected [foo], got: %v", res3)
	}
}
