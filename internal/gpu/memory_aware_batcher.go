package gpu

import (
	"context"
	"fmt"
	"log"
	"math"
	"sort"
	"sync"
	"time"
)

// Note: MemoryPressureLevel is defined in memory_pool.go
// Using existing constants: PressureLow, PressureMedium, PressureHigh, PressureCritical

// BatchingStrategy defines different batching approaches based on memory conditions
type BatchingStrategy int

const (
	BatchingStrategyOptimal BatchingStrategy = iota
	BatchingStrategyConservative
	BatchingStrategyMinimal
	BatchingStrategyIndividual
)

// String returns the string representation of batching strategy
func (strategy BatchingStrategy) String() string {
	switch strategy {
	case BatchingStrategyOptimal:
		return "optimal"
	case BatchingStrategyConservative:
		return "conservative"
	case BatchingStrategyMinimal:
		return "minimal"
	case BatchingStrategyIndividual:
		return "individual"
	default:
		return "unknown"
	}
}

// MemoryRequirement represents memory requirements for a request or batch
type MemoryRequirement struct {
	BaseMemory     uint64                 `json:"base_memory"`     // Base memory for model
	InputMemory    uint64                 `json:"input_memory"`    // Memory for input data
	OutputMemory   uint64                 `json:"output_memory"`   // Memory for output data
	WorkingMemory  uint64                 `json:"working_memory"`  // Temporary working memory
	TotalEstimated uint64                 `json:"total_estimated"` // Total estimated memory
	Confidence     float64                `json:"confidence"`      // Confidence in estimation (0-1)
	Timestamp      time.Time              `json:"timestamp"`
	Metadata       map[string]interface{} `json:"metadata"`
}

// MemoryPrediction contains predicted memory usage for a batch
type MemoryPrediction struct {
	BatchSize         int                    `json:"batch_size"`
	PredictedUsage    uint64                 `json:"predicted_usage"`    // Predicted memory usage in bytes
	PeakUsage         uint64                 `json:"peak_usage"`         // Predicted peak memory usage
	SafetyMargin      uint64                 `json:"safety_margin"`      // Additional safety margin
	TotalRequired     uint64                 `json:"total_required"`     // Total required including margin
	ProbabilityOfOOM  float64                `json:"probability_of_oom"` // Probability of OOM (0-1)
	RecommendedAction string                 `json:"recommended_action"`
	Confidence        float64                `json:"confidence"`
	Timestamp         time.Time              `json:"timestamp"`
	Metadata          map[string]interface{} `json:"metadata"`
}

// BatchMemoryStats tracks memory usage statistics for batches
type BatchMemoryStats struct {
	TotalBatches       int64     `json:"total_batches"`
	SuccessfulBatches  int64     `json:"successful_batches"`
	FailedBatches      int64     `json:"failed_batches"`
	OOMPrevented       int64     `json:"oom_prevented"`       // Number of potential OOMs prevented
	FallbacksTriggered int64     `json:"fallbacks_triggered"` // Number of fallback strategies used
	AvgMemoryUsage     uint64    `json:"avg_memory_usage"`
	PeakMemoryUsage    uint64    `json:"peak_memory_usage"`
	MemoryEfficiency   float64   `json:"memory_efficiency"` // Actual vs predicted usage ratio
	PredictionAccuracy float64   `json:"prediction_accuracy"`
	LastUpdated        time.Time `json:"last_updated"`
}

// MemoryAwareBatcherConfig contains configuration for memory-aware batching
type MemoryAwareBatcherConfig struct {
	DeviceID                int                             `json:"device_id"`
	MemoryThresholds        map[MemoryPressureLevel]float64 `json:"memory_thresholds"` // Memory usage percentages
	SafetyMargins           map[MemoryPressureLevel]float64 `json:"safety_margins"`    // Safety margin percentages
	MaxBatchSizes           map[MemoryPressureLevel]int     `json:"max_batch_sizes"`   // Max batch sizes per pressure level
	MemoryCheckInterval     time.Duration                   `json:"memory_check_interval"`
	PredictionWindowSize    int                             `json:"prediction_window_size"`
	EnablePredictiveScaling bool                            `json:"enable_predictive_scaling"`
	EnableMemoryDefrag      bool                            `json:"enable_memory_defrag"`
	EnableGarbageCollection bool                            `json:"enable_garbage_collection"`
	FallbackTimeout         time.Duration                   `json:"fallback_timeout"`
	MonitoringEnabled       bool                            `json:"monitoring_enabled"`
	MetricsHistorySize      int                             `json:"metrics_history_size"`
	OOMPreventionEnabled    bool                            `json:"oom_prevention_enabled"`
	AdaptiveSafetyMargin    bool                            `json:"adaptive_safety_margin"`
}

// DefaultMemoryAwareBatcherConfig returns sensible default configuration
func DefaultMemoryAwareBatcherConfig(deviceID int) *MemoryAwareBatcherConfig {
	return &MemoryAwareBatcherConfig{
		DeviceID: deviceID,
		MemoryThresholds: map[MemoryPressureLevel]float64{
			PressureLow:      0.60, // 0-60%
			PressureMedium:   0.80, // 60-80%
			PressureHigh:     0.95, // 80-95%
			PressureCritical: 1.00, // 95%+
		},
		SafetyMargins: map[MemoryPressureLevel]float64{
			PressureLow:      0.10, // 10% safety margin
			PressureMedium:   0.15, // 15% safety margin
			PressureHigh:     0.25, // 25% safety margin
			PressureCritical: 0.40, // 40% safety margin
		},
		MaxBatchSizes: map[MemoryPressureLevel]int{
			PressureLow:      128, // Full batches
			PressureMedium:   32,  // Medium batches
			PressureHigh:     8,   // Small batches
			PressureCritical: 1,   // Individual processing
		},
		MemoryCheckInterval:     500 * time.Millisecond,
		PredictionWindowSize:    50,
		EnablePredictiveScaling: true,
		EnableMemoryDefrag:      true,
		EnableGarbageCollection: true,
		FallbackTimeout:         30 * time.Second,
		MonitoringEnabled:       true,
		MetricsHistorySize:      1000,
		OOMPreventionEnabled:    true,
		AdaptiveSafetyMargin:    true,
	}
}

// MemoryPredictor predicts memory requirements for requests and batches
type MemoryPredictor struct {
	mu                   sync.RWMutex
	deviceID             int
	modelMemoryBaselines map[string]uint64  // modelID -> base memory requirement
	inputSizeFactors     map[string]float64 // modelID -> memory per input unit
	batchScalingFactors  map[string]float64 // modelID -> batch scaling factor
	predictionHistory    []*MemoryPrediction
	actualUsageHistory   []uint64
	maxHistorySize       int
	logger               *log.Logger
}

// NewMemoryPredictor creates a new memory predictor
func NewMemoryPredictor(deviceID int, maxHistorySize int, logger *log.Logger) *MemoryPredictor {
	if logger == nil {
		logger = log.Default()
	}

	return &MemoryPredictor{
		deviceID:             deviceID,
		modelMemoryBaselines: make(map[string]uint64),
		inputSizeFactors:     make(map[string]float64),
		batchScalingFactors:  make(map[string]float64),
		predictionHistory:    make([]*MemoryPrediction, 0, maxHistorySize),
		actualUsageHistory:   make([]uint64, 0, maxHistorySize),
		maxHistorySize:       maxHistorySize,
		logger:               logger,
	}
}

// RegisterModel registers memory characteristics for a model
func (mp *MemoryPredictor) RegisterModel(modelID string, baseMemory uint64, inputSizeFactor, batchScalingFactor float64) {
	mp.mu.Lock()
	defer mp.mu.Unlock()

	mp.modelMemoryBaselines[modelID] = baseMemory
	mp.inputSizeFactors[modelID] = inputSizeFactor
	mp.batchScalingFactors[modelID] = batchScalingFactor

	mp.logger.Printf("Registered model %s: base=%d bytes, input_factor=%.2f, batch_factor=%.2f",
		modelID, baseMemory, inputSizeFactor, batchScalingFactor)
}

// PredictBatchMemory predicts memory usage for a batch of requests
func (mp *MemoryPredictor) PredictBatchMemory(requests []*InferenceRequest) (*MemoryPrediction, error) {
	mp.mu.RLock()
	defer mp.mu.RUnlock()

	if len(requests) == 0 {
		return nil, fmt.Errorf("cannot predict memory for empty batch")
	}

	batchSize := len(requests)
	prediction := &MemoryPrediction{
		BatchSize: batchSize,
		Timestamp: time.Now(),
		Metadata:  make(map[string]interface{}),
	}

	// Group requests by model
	modelGroups := make(map[string][]*InferenceRequest)
	for _, req := range requests {
		modelGroups[req.ModelID] = append(modelGroups[req.ModelID], req)
	}

	var totalPredictedMemory uint64
	var totalConfidence float64
	modelCount := 0

	// Predict memory for each model group
	for modelID, modelRequests := range modelGroups {
		modelMemory, confidence := mp.predictModelMemory(modelID, modelRequests)
		totalPredictedMemory += modelMemory
		totalConfidence += confidence
		modelCount++

		prediction.Metadata[fmt.Sprintf("model_%s_memory", modelID)] = modelMemory
		prediction.Metadata[fmt.Sprintf("model_%s_requests", modelID)] = len(modelRequests)
	}

	// Calculate averages and add safety margins
	if modelCount > 0 {
		prediction.Confidence = totalConfidence / float64(modelCount)
	}

	prediction.PredictedUsage = totalPredictedMemory
	prediction.PeakUsage = uint64(float64(totalPredictedMemory) * 1.2)     // 20% peak overhead
	prediction.SafetyMargin = uint64(float64(totalPredictedMemory) * 0.15) // 15% safety margin
	prediction.TotalRequired = prediction.PeakUsage + prediction.SafetyMargin

	// Calculate OOM probability based on historical accuracy
	prediction.ProbabilityOfOOM = mp.calculateOOMProbability(prediction.TotalRequired)

	// Recommend action based on prediction
	prediction.RecommendedAction = mp.recommendAction(prediction)

	// Store prediction
	mp.addPredictionToHistory(prediction)

	return prediction, nil
}

// predictModelMemory predicts memory usage for a specific model and requests
func (mp *MemoryPredictor) predictModelMemory(modelID string, requests []*InferenceRequest) (uint64, float64) {
	baseMemory, hasBaseline := mp.modelMemoryBaselines[modelID]
	inputFactor, hasInputFactor := mp.inputSizeFactors[modelID]
	batchFactor, hasBatchFactor := mp.batchScalingFactors[modelID]

	// Use defaults if model is not registered
	if !hasBaseline {
		baseMemory = 512 * 1024 * 1024 // 512MB default
	}
	if !hasInputFactor {
		inputFactor = 1.0
	}
	if !hasBatchFactor {
		batchFactor = 0.8 // Sub-linear scaling by default
	}

	// Calculate total input size
	var totalInputSize uint64
	for _, req := range requests {
		inputSize := uint64(len(req.Data))
		totalInputSize += inputSize
	}

	// Calculate memory requirement
	inputMemory := uint64(float64(totalInputSize) * inputFactor)
	batchCount := len(requests)
	batchOverhead := uint64(float64(baseMemory) * math.Pow(float64(batchCount), batchFactor))

	totalMemory := baseMemory + inputMemory + batchOverhead

	// Calculate confidence based on available data
	confidence := 0.5 // Base confidence
	if hasBaseline {
		confidence += 0.2
	}
	if hasInputFactor {
		confidence += 0.2
	}
	if hasBatchFactor {
		confidence += 0.1
	}

	return totalMemory, math.Min(confidence, 1.0)
}

// calculateOOMProbability calculates the probability of OOM based on historical data
func (mp *MemoryPredictor) calculateOOMProbability(requiredMemory uint64) float64 {
	if len(mp.actualUsageHistory) < 10 {
		return 0.1 // Low probability if insufficient history
	}

	// Calculate statistics from history
	var total uint64
	var max uint64
	for _, usage := range mp.actualUsageHistory {
		total += usage
		if usage > max {
			max = usage
		}
	}

	avgUsage := total / uint64(len(mp.actualUsageHistory))

	// Simple heuristic based on required vs historical usage
	if requiredMemory > max {
		return 0.8 // High probability if exceeding historical maximum
	} else if requiredMemory > avgUsage*2 {
		return 0.5 // Medium probability if significantly above average
	} else if requiredMemory > avgUsage {
		return 0.2 // Low probability if above average but within range
	}

	return 0.05 // Very low probability
}

// recommendAction recommends an action based on memory prediction
func (mp *MemoryPredictor) recommendAction(prediction *MemoryPrediction) string {
	if prediction.ProbabilityOfOOM > 0.7 {
		return "split_batch"
	} else if prediction.ProbabilityOfOOM > 0.4 {
		return "reduce_batch_size"
	} else if prediction.ProbabilityOfOOM > 0.2 {
		return "monitor_closely"
	}
	return "proceed"
}

// RecordActualUsage records actual memory usage for learning
func (mp *MemoryPredictor) RecordActualUsage(batchSize int, actualUsage uint64) {
	mp.mu.Lock()
	defer mp.mu.Unlock()

	mp.actualUsageHistory = append(mp.actualUsageHistory, actualUsage)
	if len(mp.actualUsageHistory) > mp.maxHistorySize {
		mp.actualUsageHistory = mp.actualUsageHistory[1:]
	}

	// Update prediction accuracy if we have a corresponding prediction
	if len(mp.predictionHistory) > 0 {
		// Find the most recent prediction for this batch size
		for i := len(mp.predictionHistory) - 1; i >= 0; i-- {
			pred := mp.predictionHistory[i]
			if pred.BatchSize == batchSize {
				// Calculate accuracy and potentially adjust models
				accuracy := 1.0 - math.Abs(float64(actualUsage)-float64(pred.PredictedUsage))/float64(pred.PredictedUsage)
				mp.logger.Printf("Prediction accuracy for batch size %d: %.2f (predicted: %d, actual: %d)",
					batchSize, accuracy, pred.PredictedUsage, actualUsage)
				break
			}
		}
	}
}

// addPredictionToHistory adds a prediction to the history
func (mp *MemoryPredictor) addPredictionToHistory(prediction *MemoryPrediction) {
	mp.predictionHistory = append(mp.predictionHistory, prediction)
	if len(mp.predictionHistory) > mp.maxHistorySize {
		mp.predictionHistory = mp.predictionHistory[1:]
	}
}

// GetPredictionAccuracy returns the current prediction accuracy
func (mp *MemoryPredictor) GetPredictionAccuracy() float64 {
	mp.mu.RLock()
	defer mp.mu.RUnlock()

	if len(mp.predictionHistory) == 0 || len(mp.actualUsageHistory) == 0 {
		return 0.0
	}

	// Calculate accuracy for overlapping predictions and actual usage
	var totalAccuracy float64
	var count int

	minLen := len(mp.predictionHistory)
	if len(mp.actualUsageHistory) < minLen {
		minLen = len(mp.actualUsageHistory)
	}

	for i := 0; i < minLen; i++ {
		pred := mp.predictionHistory[len(mp.predictionHistory)-1-i]
		actual := mp.actualUsageHistory[len(mp.actualUsageHistory)-1-i]

		if pred.PredictedUsage > 0 {
			accuracy := 1.0 - math.Abs(float64(actual)-float64(pred.PredictedUsage))/float64(pred.PredictedUsage)
			totalAccuracy += math.Max(0, accuracy) // Ensure non-negative
			count++
		}
	}

	if count == 0 {
		return 0.0
	}

	return totalAccuracy / float64(count)
}

// MemoryPressureManager manages different memory pressure levels and strategies
type MemoryPressureManager struct {
	mu              sync.RWMutex
	currentLevel    MemoryPressureLevel
	config          *MemoryAwareBatcherConfig
	memoryPool      CUDAMemoryPool
	resourceManager *ResourceManager
	levelHistory    []MemoryPressureLevel
	maxHistorySize  int
	logger          *log.Logger
}

// NewMemoryPressureManager creates a new memory pressure manager
func NewMemoryPressureManager(config *MemoryAwareBatcherConfig, memoryPool CUDAMemoryPool, resourceManager *ResourceManager, logger *log.Logger) *MemoryPressureManager {
	if logger == nil {
		logger = log.Default()
	}

	return &MemoryPressureManager{
		currentLevel:    PressureLow,
		config:          config,
		memoryPool:      memoryPool,
		resourceManager: resourceManager,
		levelHistory:    make([]MemoryPressureLevel, 0, 100),
		maxHistorySize:  100,
		logger:          logger,
	}
}

// UpdateMemoryPressure updates the current memory pressure level
func (mpm *MemoryPressureManager) UpdateMemoryPressure(ctx context.Context) error {
	mpm.mu.Lock()
	defer mpm.mu.Unlock()

	// Get current memory statistics
	stats := mpm.memoryPool.GetStats()
	totalMemory := stats.TotalAllocated + stats.FreeMemory

	var utilizationRatio float64
	if totalMemory > 0 {
		utilizationRatio = float64(stats.CurrentAllocated) / float64(totalMemory)
	}

	// Determine new pressure level
	newLevel := mpm.calculatePressureLevel(utilizationRatio)

	// Update level if changed
	if newLevel != mpm.currentLevel {
		oldLevel := mpm.currentLevel
		mpm.currentLevel = newLevel

		// Add to history
		mpm.levelHistory = append(mpm.levelHistory, newLevel)
		if len(mpm.levelHistory) > mpm.maxHistorySize {
			mpm.levelHistory = mpm.levelHistory[1:]
		}

		mpm.logger.Printf("Memory pressure level changed from %s to %s (utilization: %.2f%%)",
			oldLevel.String(), newLevel.String(), utilizationRatio*100)

		// Trigger appropriate actions based on new level
		if err := mpm.handlePressureLevelChange(ctx, oldLevel, newLevel); err != nil {
			mpm.logger.Printf("Error handling pressure level change: %v", err)
		}
	}

	return nil
}

// calculatePressureLevel determines pressure level based on utilization
func (mpm *MemoryPressureManager) calculatePressureLevel(utilization float64) MemoryPressureLevel {
	thresholds := mpm.config.MemoryThresholds

	if utilization >= thresholds[PressureCritical] {
		return PressureCritical
	} else if utilization >= thresholds[PressureHigh] {
		return PressureHigh
	} else if utilization >= thresholds[PressureMedium] {
		return PressureMedium
	}

	return PressureLow
}

// handlePressureLevelChange handles actions when pressure level changes
func (mpm *MemoryPressureManager) handlePressureLevelChange(ctx context.Context, oldLevel, newLevel MemoryPressureLevel) error {
	// Trigger garbage collection if pressure increases significantly
	if newLevel > oldLevel && mpm.config.EnableGarbageCollection {
		if newLevel >= PressureHigh {
			mpm.logger.Printf("Triggering garbage collection due to high memory pressure")
			// Note: Actual GC would be implemented based on specific memory pool
		}
	}

	// Trigger memory defragmentation if enabled
	if newLevel >= PressureHigh && mpm.config.EnableMemoryDefrag {
		mpm.logger.Printf("Considering memory defragmentation due to high pressure")
		// Note: Actual defragmentation would be implemented based on specific memory pool
	}

	return nil
}

// GetCurrentLevel returns the current memory pressure level
func (mpm *MemoryPressureManager) GetCurrentLevel() MemoryPressureLevel {
	mpm.mu.RLock()
	defer mpm.mu.RUnlock()
	return mpm.currentLevel
}

// GetRecommendedBatchSize returns recommended batch size for current pressure level
func (mpm *MemoryPressureManager) GetRecommendedBatchSize() int {
	mpm.mu.RLock()
	defer mpm.mu.RUnlock()
	return mpm.config.MaxBatchSizes[mpm.currentLevel]
}

// GetRecommendedStrategy returns recommended batching strategy for current pressure level
func (mpm *MemoryPressureManager) GetRecommendedStrategy() BatchingStrategy {
	mpm.mu.RLock()
	defer mpm.mu.RUnlock()

	switch mpm.currentLevel {
	case PressureLow:
		return BatchingStrategyOptimal
	case PressureMedium:
		return BatchingStrategyConservative
	case PressureHigh:
		return BatchingStrategyMinimal
	case PressureCritical:
		return BatchingStrategyIndividual
	default:
		return BatchingStrategyConservative
	}
}

// GetSafetyMargin returns the safety margin for current pressure level
func (mpm *MemoryPressureManager) GetSafetyMargin() float64 {
	mpm.mu.RLock()
	defer mpm.mu.RUnlock()
	return mpm.config.SafetyMargins[mpm.currentLevel]
}

// MemoryAwareBatcher is the main coordinator for memory-aware batching
type MemoryAwareBatcher struct {
	mu              sync.RWMutex
	config          *MemoryAwareBatcherConfig
	memoryPredictor *MemoryPredictor
	pressureManager *MemoryPressureManager
	batchOptimizer  *BatchSizeOptimizer
	inferenceQueue  *InferenceQueue
	memoryPool      CUDAMemoryPool
	resourceManager *ResourceManager

	// Monitoring and statistics
	stats            *BatchMemoryStats
	isRunning        bool
	monitoringCtx    context.Context
	monitoringCancel context.CancelFunc
	monitoringWg     sync.WaitGroup

	// Mixed batch processing
	mixedBatchProcessor *MixedBatchProcessor
	mixedBatchConfig    *MixedBatchConfig

	logger *log.Logger
}

// NewMemoryAwareBatcher creates a new memory-aware batcher
func NewMemoryAwareBatcher(
	config *MemoryAwareBatcherConfig,
	batchOptimizer *BatchSizeOptimizer,
	inferenceQueue *InferenceQueue,
	memoryPool CUDAMemoryPool,
	resourceManager *ResourceManager,
	logger *log.Logger,
) (*MemoryAwareBatcher, error) {
	if config == nil {
		config = DefaultMemoryAwareBatcherConfig(0)
	}
	if logger == nil {
		logger = log.Default()
	}

	// Create memory predictor
	memoryPredictor := NewMemoryPredictor(config.DeviceID, config.PredictionWindowSize, logger)

	// Create pressure manager
	pressureManager := NewMemoryPressureManager(config, memoryPool, resourceManager, logger)

	// Initialize mixed batch processing
	mixedBatchConfig := DefaultMixedBatchConfig()
	mixedBatchProcessor := NewMixedBatchProcessor(mixedBatchConfig, memoryPredictor, logger)

	batcher := &MemoryAwareBatcher{
		config:              config,
		memoryPredictor:     memoryPredictor,
		pressureManager:     pressureManager,
		batchOptimizer:      batchOptimizer,
		inferenceQueue:      inferenceQueue,
		memoryPool:          memoryPool,
		resourceManager:     resourceManager,
		mixedBatchProcessor: mixedBatchProcessor,
		mixedBatchConfig:    mixedBatchConfig,
		stats:               &BatchMemoryStats{LastUpdated: time.Now()},
		logger:              logger,
	}

	return batcher, nil
}

// Start begins memory-aware batching operations
func (mab *MemoryAwareBatcher) Start(ctx context.Context) error {
	mab.mu.Lock()
	defer mab.mu.Unlock()

	if mab.isRunning {
		return fmt.Errorf("memory-aware batcher is already running")
	}

	mab.monitoringCtx, mab.monitoringCancel = context.WithCancel(ctx)
	mab.isRunning = true

	// Start memory monitoring
	if mab.config.MonitoringEnabled {
		mab.monitoringWg.Add(1)
		go mab.memoryMonitoringLoop()
	}

	mab.logger.Printf("Memory-aware batcher started for device %d", mab.config.DeviceID)
	return nil
}

// Stop stops memory-aware batching operations
func (mab *MemoryAwareBatcher) Stop() error {
	mab.mu.Lock()
	defer mab.mu.Unlock()

	if !mab.isRunning {
		return nil
	}

	mab.monitoringCancel()
	mab.isRunning = false

	// Wait for monitoring loop to finish
	mab.monitoringWg.Wait()

	mab.logger.Printf("Memory-aware batcher stopped")
	return nil
}

// CreateMemoryAwareBatch creates a batch considering memory constraints
func (mab *MemoryAwareBatcher) CreateMemoryAwareBatch(ctx context.Context, availableRequests []*InferenceRequest) ([]*InferenceRequest, error) {
	mab.mu.RLock()
	defer mab.mu.RUnlock()

	if len(availableRequests) == 0 {
		return nil, fmt.Errorf("no requests available for batching")
	}

	// Update memory pressure
	if err := mab.pressureManager.UpdateMemoryPressure(ctx); err != nil {
		mab.logger.Printf("Error updating memory pressure: %v", err)
	}

	// Get current pressure level and strategy
	currentLevel := mab.pressureManager.GetCurrentLevel()
	strategy := mab.pressureManager.GetRecommendedStrategy()
	maxBatchSize := mab.pressureManager.GetRecommendedBatchSize()

	mab.logger.Printf("Creating batch with strategy %s (pressure: %s, max_size: %d)",
		strategy.String(), currentLevel.String(), maxBatchSize)

	// Apply strategy-specific batching
	batch, err := mab.applyBatchingStrategy(ctx, availableRequests, strategy, maxBatchSize)
	if err != nil {
		mab.stats.FailedBatches++
		return nil, fmt.Errorf("failed to apply batching strategy: %w", err)
	}

	// Update statistics
	mab.stats.TotalBatches++
	if batch != nil && len(batch) > 0 {
		mab.stats.SuccessfulBatches++
	}

	return batch, nil
}

// applyBatchingStrategy applies the specified batching strategy
func (mab *MemoryAwareBatcher) applyBatchingStrategy(ctx context.Context, requests []*InferenceRequest, strategy BatchingStrategy, maxBatchSize int) ([]*InferenceRequest, error) {
	switch strategy {
	case BatchingStrategyOptimal:
		return mab.createOptimalBatch(ctx, requests, maxBatchSize)
	case BatchingStrategyConservative:
		return mab.createConservativeBatch(ctx, requests, maxBatchSize)
	case BatchingStrategyMinimal:
		return mab.createMinimalBatch(ctx, requests, maxBatchSize)
	case BatchingStrategyIndividual:
		return mab.createIndividualBatch(ctx, requests)
	default:
		return mab.createConservativeBatch(ctx, requests, maxBatchSize)
	}
}

// createOptimalBatch creates an optimal batch using full batch optimizer capabilities
func (mab *MemoryAwareBatcher) createOptimalBatch(ctx context.Context, requests []*InferenceRequest, maxBatchSize int) ([]*InferenceRequest, error) {
	// Get recommendation from batch optimizer
	recommendation, err := mab.batchOptimizer.GetRecommendation(ctx)
	if err != nil {
		mab.logger.Printf("Error getting batch size recommendation: %v", err)
		recommendation = &BatchSizeRecommendation{RecommendedSize: maxBatchSize / 2}
	}

	// Use the smaller of optimizer recommendation and memory-based limit
	targetSize := int(math.Min(float64(recommendation.RecommendedSize), float64(maxBatchSize)))
	if targetSize > len(requests) {
		targetSize = len(requests)
	}

	// Select requests for batch
	batch := mab.selectRequestsForBatch(requests, targetSize)

	// Verify memory requirements
	if mab.config.OOMPreventionEnabled {
		if safe, err := mab.verifyBatchMemorySafety(ctx, batch); err != nil || !safe {
			if err != nil {
				mab.logger.Printf("Error verifying batch memory safety: %v", err)
			}
			// Reduce batch size and try again
			return mab.createConservativeBatch(ctx, requests, maxBatchSize/2)
		}
	}

	return batch, nil
}

// createConservativeBatch creates a conservative batch with extra safety margins
func (mab *MemoryAwareBatcher) createConservativeBatch(ctx context.Context, requests []*InferenceRequest, maxBatchSize int) ([]*InferenceRequest, error) {
	// Use conservative batch size (typically 50-75% of max)
	targetSize := int(float64(maxBatchSize) * 0.6)
	if targetSize > len(requests) {
		targetSize = len(requests)
	}
	if targetSize < 1 {
		targetSize = 1
	}

	batch := mab.selectRequestsForBatch(requests, targetSize)

	// Verify memory safety with higher safety margin
	if mab.config.OOMPreventionEnabled {
		if safe, err := mab.verifyBatchMemorySafety(ctx, batch); err != nil || !safe {
			// Further reduce batch size
			return mab.createMinimalBatch(ctx, requests, targetSize/2)
		}
	}

	return batch, nil
}

// createMinimalBatch creates a minimal batch with very small size
func (mab *MemoryAwareBatcher) createMinimalBatch(ctx context.Context, requests []*InferenceRequest, maxBatchSize int) ([]*InferenceRequest, error) {
	// Use minimal batch size (1-4 requests typically)
	targetSize := int(math.Min(4, float64(maxBatchSize)))
	if targetSize > len(requests) {
		targetSize = len(requests)
	}
	if targetSize < 1 {
		targetSize = 1
	}

	batch := mab.selectRequestsForBatch(requests, targetSize)

	// Always verify memory safety for minimal batches
	if mab.config.OOMPreventionEnabled {
		if safe, err := mab.verifyBatchMemorySafety(ctx, batch); err != nil || !safe {
			// Fall back to individual processing
			return mab.createIndividualBatch(ctx, requests)
		}
	}

	return batch, nil
}

// createIndividualBatch creates a batch with single request for individual processing
func (mab *MemoryAwareBatcher) createIndividualBatch(ctx context.Context, requests []*InferenceRequest) ([]*InferenceRequest, error) {
	if len(requests) == 0 {
		return nil, fmt.Errorf("no requests available for individual processing")
	}

	// Select the highest priority request
	batch := []*InferenceRequest{mab.selectHighestPriorityRequest(requests)}

	// Even individual requests should be verified in critical conditions
	if mab.config.OOMPreventionEnabled {
		if safe, err := mab.verifyBatchMemorySafety(ctx, batch); err != nil || !safe {
			mab.stats.OOMPrevented++
			return nil, fmt.Errorf("even individual request would exceed memory limits")
		}
	}

	return batch, nil
}

// selectRequestsForBatch selects requests for a batch based on priority and compatibility
func (mab *MemoryAwareBatcher) selectRequestsForBatch(requests []*InferenceRequest, targetSize int) []*InferenceRequest {
	if targetSize <= 0 || len(requests) == 0 {
		return nil
	}

	// Sort requests by priority (highest first)
	sortedRequests := make([]*InferenceRequest, len(requests))
	copy(sortedRequests, requests)
	sort.Slice(sortedRequests, func(i, j int) bool {
		return sortedRequests[i].Priority > sortedRequests[j].Priority
	})

	// Select up to targetSize requests
	batchSize := targetSize
	if batchSize > len(sortedRequests) {
		batchSize = len(sortedRequests)
	}

	return sortedRequests[:batchSize]
}

// selectHighestPriorityRequest selects the highest priority request
func (mab *MemoryAwareBatcher) selectHighestPriorityRequest(requests []*InferenceRequest) *InferenceRequest {
	if len(requests) == 0 {
		return nil
	}

	highest := requests[0]
	for _, req := range requests[1:] {
		if req.Priority > highest.Priority {
			highest = req
		}
	}

	return highest
}

// verifyBatchMemorySafety verifies that a batch can be processed safely without OOM
func (mab *MemoryAwareBatcher) verifyBatchMemorySafety(ctx context.Context, batch []*InferenceRequest) (bool, error) {
	if len(batch) == 0 {
		return true, nil
	}

	// Get memory prediction for the batch
	prediction, err := mab.memoryPredictor.PredictBatchMemory(batch)
	if err != nil {
		return false, fmt.Errorf("failed to predict batch memory: %w", err)
	}

	// Get current memory statistics
	stats := mab.memoryPool.GetStats()
	availableMemory := stats.FreeMemory

	// Apply safety margin
	safetyMargin := mab.pressureManager.GetSafetyMargin()
	requiredMemory := uint64(float64(prediction.TotalRequired) * (1.0 + safetyMargin))

	// Check if we have enough memory
	if requiredMemory > uint64(availableMemory) {
		mab.logger.Printf("Batch would exceed available memory: required=%d, available=%d (with %.2f%% safety margin)",
			requiredMemory, availableMemory, safetyMargin*100)
		return false, nil
	}

	// Check OOM probability
	if prediction.ProbabilityOfOOM > 0.3 {
		mab.logger.Printf("High OOM probability for batch (%.2f), rejecting", prediction.ProbabilityOfOOM)
		return false, nil
	}

	return true, nil
}

// RegisterModel registers a model with the memory predictor
func (mab *MemoryAwareBatcher) RegisterModel(modelID string, baseMemory uint64, inputSizeFactor, batchScalingFactor float64) {
	mab.memoryPredictor.RegisterModel(modelID, baseMemory, inputSizeFactor, batchScalingFactor)
}

// RecordBatchMemoryUsage records actual memory usage for a completed batch
func (mab *MemoryAwareBatcher) RecordBatchMemoryUsage(batchSize int, actualUsage uint64) {
	mab.memoryPredictor.RecordActualUsage(batchSize, actualUsage)

	// Update statistics
	mab.mu.Lock()
	defer mab.mu.Unlock()

	// Update average memory usage
	if mab.stats.TotalBatches > 0 {
		mab.stats.AvgMemoryUsage = (mab.stats.AvgMemoryUsage*uint64(mab.stats.TotalBatches-1) + actualUsage) / uint64(mab.stats.TotalBatches)
	} else {
		mab.stats.AvgMemoryUsage = actualUsage
	}

	// Update peak memory usage
	if actualUsage > mab.stats.PeakMemoryUsage {
		mab.stats.PeakMemoryUsage = actualUsage
	}

	// Update prediction accuracy
	mab.stats.PredictionAccuracy = mab.memoryPredictor.GetPredictionAccuracy()
	mab.stats.LastUpdated = time.Now()
}

// GetStatistics returns current memory batching statistics
func (mab *MemoryAwareBatcher) GetStatistics() *BatchMemoryStats {
	mab.mu.RLock()
	defer mab.mu.RUnlock()

	// Return a copy
	stats := *mab.stats
	return &stats
}

// GetCurrentMemoryPressure returns the current memory pressure level
func (mab *MemoryAwareBatcher) GetCurrentMemoryPressure() MemoryPressureLevel {
	return mab.pressureManager.GetCurrentLevel()
}

// memoryMonitoringLoop runs the background memory monitoring
func (mab *MemoryAwareBatcher) memoryMonitoringLoop() {
	defer mab.monitoringWg.Done()

	ticker := time.NewTicker(mab.config.MemoryCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-mab.monitoringCtx.Done():
			return
		case <-ticker.C:
			// Update memory pressure
			if err := mab.pressureManager.UpdateMemoryPressure(mab.monitoringCtx); err != nil {
				mab.logger.Printf("Error updating memory pressure during monitoring: %v", err)
			}

			// Update statistics
			mab.updateMonitoringStatistics()
		}
	}
}

// updateMonitoringStatistics updates statistics during monitoring
func (mab *MemoryAwareBatcher) updateMonitoringStatistics() {
	mab.mu.Lock()
	defer mab.mu.Unlock()

	// Update memory efficiency based on current pool stats
	stats := mab.memoryPool.GetStats()
	if stats.TotalAllocated > 0 {
		mab.stats.MemoryEfficiency = float64(stats.CurrentAllocated) / float64(stats.TotalAllocated)
	}

	mab.stats.LastUpdated = time.Now()
}

// CreateVariableInputBatch creates a batch that can handle variable input sizes and mixed types
func (mab *MemoryAwareBatcher) CreateVariableInputBatch(ctx context.Context, requests []*InferenceRequest) (*BatchCreationResult, error) {
	if len(requests) == 0 {
		return nil, fmt.Errorf("cannot create batch from empty requests")
	}

	mab.logger.Printf("Creating variable input batch for %d requests", len(requests))

	// Get current memory pressure
	currentPressure := mab.pressureManager.GetCurrentLevel()
	safetyMargin := mab.config.SafetyMargins[currentPressure]

	// Check if this is a homogeneous or mixed batch
	batchGroups, err := mab.mixedBatchProcessor.ProcessMixedBatch(ctx, requests)
	if err != nil {
		return nil, fmt.Errorf("failed to process mixed batch: %w", err)
	}

	// Process each batch group
	var createdBatches []*BatchCreationResult
	totalMemoryRequired := uint64(0)

	for _, group := range batchGroups {
		groupResult, err := mab.createBatchFromGroup(ctx, group, safetyMargin)
		if err != nil {
			mab.logger.Printf("Failed to create batch from group %s: %v", group.GroupKey.String(), err)
			continue
		}

		createdBatches = append(createdBatches, groupResult)
		totalMemoryRequired += groupResult.MemoryRequirement
	}

	if len(createdBatches) == 0 {
		return nil, fmt.Errorf("no valid batches could be created from any group")
	}

	// If we have multiple groups, we might need to process them sequentially
	// or create a composite result
	if len(createdBatches) == 1 {
		return createdBatches[0], nil
	}

	// Create a composite result for multiple groups
	compositeResult := &BatchCreationResult{
		BatchID:           fmt.Sprintf("composite_%d_groups", len(createdBatches)),
		Requests:          requests,
		MemoryRequirement: totalMemoryRequired,
		BatchSize:         len(requests),
		CreatedAt:         time.Now(),
		Metadata: map[string]interface{}{
			"batch_type":      "variable_input_composite",
			"group_count":     len(batchGroups),
			"batch_count":     len(createdBatches),
			"memory_pressure": currentPressure.String(),
		},
	}

	mab.logger.Printf("Created composite variable input batch with %d groups, total memory: %d bytes",
		len(createdBatches), totalMemoryRequired)

	return compositeResult, nil
}

// createBatchFromGroup creates a batch from a processed batch group
func (mab *MemoryAwareBatcher) createBatchFromGroup(ctx context.Context, group *BatchGroup, safetyMargin float64) (*BatchCreationResult, error) {
	// Predict memory requirements for the normalized group
	memoryPrediction, err := mab.memoryPredictor.PredictBatchMemory(group.Requests)
	if err != nil {
		return nil, fmt.Errorf("failed to predict memory for group: %w", err)
	}

	// Adjust memory prediction based on padding overhead
	adjustedMemory := uint64(float64(memoryPrediction.PredictedUsage) * (1.0 + group.PaddingOverhead))

	// Check if we can safely create this batch
	canCreate, err := mab.verifyBatchMemorySafety(ctx, group.Requests)
	if err != nil {
		return nil, fmt.Errorf("failed to validate batch creation: %w", err)
	}

	if !canCreate {
		return nil, fmt.Errorf("insufficient memory for batch group %s", group.GroupKey.String())
	}

	// Create batch result
	batchID := fmt.Sprintf("mixed_batch_%s_%d", group.GroupKey.String(), time.Now().UnixNano())

	result := &BatchCreationResult{
		BatchID:           batchID,
		Requests:          group.Requests,
		MemoryRequirement: adjustedMemory,
		BatchSize:         len(group.Requests),
		CreatedAt:         time.Now(),
		Metadata: map[string]interface{}{
			"batch_type":          "variable_input",
			"group_key":           group.GroupKey.String(),
			"target_shape":        group.TargetShape,
			"compatibility_level": group.CompatibilityLevel.String(),
			"padding_overhead":    group.PaddingOverhead,
			"estimated_memory":    group.EstimatedMemory,
			"adjusted_memory":     adjustedMemory,
		},
	}

	mab.logger.Printf("Created batch from group %s: %d requests, target shape: %v, padding overhead: %.2f%%",
		group.GroupKey.String(), len(group.Requests), group.TargetShape, group.PaddingOverhead*100)

	return result, nil
}

// GetMixedBatchStatistics returns statistics about mixed batch processing
func (mab *MemoryAwareBatcher) GetMixedBatchStatistics() *MixedBatchStats {
	if mab.mixedBatchProcessor != nil {
		return mab.mixedBatchProcessor.GetStatistics()
	}
	return &MixedBatchStats{
		LastUpdated: time.Now(),
	}
}

// GetBatchNormalizationStatistics returns statistics about batch normalization
func (mab *MemoryAwareBatcher) GetBatchNormalizationStatistics() map[string]interface{} {
	if mab.mixedBatchProcessor != nil && mab.mixedBatchProcessor.normalizer != nil {
		return mab.mixedBatchProcessor.normalizer.GetNormalizationStatistics()
	}
	return map[string]interface{}{
		"total_normalizations": 0,
	}
}
