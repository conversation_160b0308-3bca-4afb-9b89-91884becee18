package gpu

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// IntelligentScheduler provides intelligent task scheduling across multiple GPUs
// focusing on utilization and memory optimization without thermal control
type IntelligentScheduler struct {
	workloadCoordinator *WorkloadIntelligenceCoordinator
	multiDeviceMgr      *MultiDeviceManager
	logger              *log.Logger

	// State management
	mu              sync.RWMutex
	isRunning       bool
	ctx             context.Context
	cancel          context.CancelFunc
	taskQueue       []*IntelligentTask
	activeTask      map[string]*IntelligentTask
	schedulingStats IntelligentStats
}

// IntelligentTask represents a task in the intelligent scheduler queue
type IntelligentTask struct {
	ID              string              `json:"id"`
	WorkloadRequest *WorkloadRequest    `json:"workload_request"`
	Priority        float64             `json:"priority"`
	SubmittedAt     time.Time           `json:"submitted_at"`
	ScheduledAt     *time.Time          `json:"scheduled_at,omitempty"`
	Status          IntelligentStatus   `json:"status"`
	Assignment      *WorkloadAssignment `json:"assignment,omitempty"`
	ErrorMessage    string              `json:"error_message,omitempty"`
}

// IntelligentStatus represents the status of a scheduled task
type IntelligentStatus string

const (
	IntelligentStatusQueued    IntelligentStatus = "queued"
	IntelligentStatusScheduled IntelligentStatus = "scheduled"
	IntelligentStatusRunning   IntelligentStatus = "running"
	IntelligentStatusCompleted IntelligentStatus = "completed"
	IntelligentStatusFailed    IntelligentStatus = "failed"
)

// IntelligentStats tracks scheduler performance metrics
type IntelligentStats struct {
	TotalTasksSubmitted int64     `json:"total_tasks_submitted"`
	TotalTasksCompleted int64     `json:"total_tasks_completed"`
	CurrentQueueSize    int       `json:"current_queue_size"`
	ActiveTasks         int       `json:"active_tasks"`
	LastSchedulingTime  time.Time `json:"last_scheduling_time"`
}

// NewIntelligentScheduler creates a new intelligent scheduler
func NewIntelligentScheduler(
	workloadCoordinator *WorkloadIntelligenceCoordinator,
	multiDeviceMgr *MultiDeviceManager,
	logger *log.Logger,
) (*IntelligentScheduler, error) {
	if logger == nil {
		logger = log.Default()
	}

	scheduler := &IntelligentScheduler{
		workloadCoordinator: workloadCoordinator,
		multiDeviceMgr:      multiDeviceMgr,
		logger:              logger,
		taskQueue:           make([]*IntelligentTask, 0),
		activeTask:          make(map[string]*IntelligentTask),
	}

	return scheduler, nil
}

// Start begins the scheduling process
func (is *IntelligentScheduler) Start(ctx context.Context) error {
	is.mu.Lock()
	defer is.mu.Unlock()

	if is.isRunning {
		return fmt.Errorf("scheduler is already running")
	}

	is.ctx, is.cancel = context.WithCancel(ctx)
	is.isRunning = true

	// Start scheduling loop
	go is.schedulingLoop()

	is.logger.Printf("Intelligent scheduler started")
	return nil
}

// Stop terminates the scheduling process
func (is *IntelligentScheduler) Stop() error {
	is.mu.Lock()
	defer is.mu.Unlock()

	if !is.isRunning {
		return fmt.Errorf("scheduler is not running")
	}

	is.cancel()
	is.isRunning = false

	is.logger.Printf("Intelligent scheduler stopped")
	return nil
}

// SubmitTask submits a new task to the scheduler
func (is *IntelligentScheduler) SubmitTask(request *WorkloadRequest) (*IntelligentTask, error) {
	if !is.isRunning {
		return nil, fmt.Errorf("scheduler is not running")
	}

	is.mu.Lock()
	defer is.mu.Unlock()

	// Create scheduled task
	task := &IntelligentTask{
		ID:              fmt.Sprintf("task_%d_%s", time.Now().UnixNano(), request.TaskID),
		WorkloadRequest: request,
		Priority:        float64(request.Priority),
		SubmittedAt:     time.Now(),
		Status:          IntelligentStatusQueued,
	}

	// Add to queue
	is.taskQueue = append(is.taskQueue, task)
	is.schedulingStats.TotalTasksSubmitted++
	is.schedulingStats.CurrentQueueSize = len(is.taskQueue)

	is.logger.Printf("Task %s submitted to scheduler queue", task.ID)
	return task, nil
}

// GetStats returns current scheduling statistics
func (is *IntelligentScheduler) GetStats() IntelligentStats {
	is.mu.RLock()
	defer is.mu.RUnlock()

	stats := is.schedulingStats
	stats.CurrentQueueSize = len(is.taskQueue)
	stats.ActiveTasks = len(is.activeTask)

	return stats
}

// schedulingLoop main scheduling loop
func (is *IntelligentScheduler) schedulingLoop() {
	ticker := time.NewTicker(time.Second * 5)
	defer ticker.Stop()

	for {
		select {
		case <-is.ctx.Done():
			return
		case <-ticker.C:
			if err := is.processQueue(); err != nil {
				is.logger.Printf("Error processing queue: %v", err)
			}
		}
	}
}

// processQueue processes queued tasks and assigns them to devices
func (is *IntelligentScheduler) processQueue() error {
	is.mu.Lock()
	defer is.mu.Unlock()

	if len(is.taskQueue) == 0 {
		return nil
	}

	// Process first task in queue
	task := is.taskQueue[0]

	// Try to assign task to a device
	assignment, err := is.workloadCoordinator.AssignWorkload(is.ctx, task.WorkloadRequest)
	if err != nil {
		is.logger.Printf("Failed to assign task %s: %v", task.ID, err)
		return nil
	}

	// Task successfully assigned
	task.Assignment = assignment
	task.Status = IntelligentStatusScheduled
	now := time.Now()
	task.ScheduledAt = &now

	// Move to active tasks
	is.activeTask[task.ID] = task

	// Remove from queue
	is.taskQueue = is.taskQueue[1:]

	is.schedulingStats.CurrentQueueSize = len(is.taskQueue)
	is.schedulingStats.ActiveTasks = len(is.activeTask)
	is.schedulingStats.LastSchedulingTime = time.Now()

	is.logger.Printf("Task %s assigned to device %s", task.ID, assignment.AssignedDevice.Device.ID)

	return nil
}
