package gpu

import (
	"fmt"
	"math"
	"testing"
)

// Helper function to fill tensors with test data using proper indexing
func fillTensorWithTestData(tensor *Tensor, valueFunc func(i int) float32) error {
	shape := tensor.Shape()
	if len(shape) == 4 {
		// 4D tensor (batch, channels, height, width)
		count := 0
		for b := int64(0); b < shape[0]; b++ {
			for c := int64(0); c < shape[1]; c++ {
				for h := int64(0); h < shape[2]; h++ {
					for w := int64(0); w < shape[3]; w++ {
						val := valueFunc(count)
						err := tensor.SetFloat32(val, b, c, h, w)
						if err != nil {
							return err
						}
						count++
					}
				}
			}
		}
	} else if len(shape) == 2 {
		// 2D tensor (matrix)
		count := 0
		for i := int64(0); i < shape[0]; i++ {
			for j := int64(0); j < shape[1]; j++ {
				val := valueFunc(count)
				err := tensor.SetFloat32(val, i, j)
				if err != nil {
					return err
				}
				count++
			}
		}
	} else if len(shape) == 1 {
		// 1D tensor (vector)
		for i := int64(0); i < shape[0]; i++ {
			val := valueFunc(int(i))
			err := tensor.SetFloat32(val, i)
			if err != nil {
				return err
			}
		}
	} else {
		return fmt.Errorf("unsupported tensor shape for test data: %v", shape)
	}
	return nil
}

func TestNewAccuracyPreservationEngine(t *testing.T) {
	// Create quantization engine
	qConfig := QuantizationConfig{
		Method:                 QuantizationSymmetric,
		TargetType:             TensorInt8,
		UseCalibration:         true,
		PreserveAccuracy:       true,
		PerChannelQuantization: false,
	}
	qEngine := NewQuantizationEngine(qConfig)

	// Create mixed precision engine
	mpConfig := DefaultMixedPrecisionConfig()
	mpEngine := NewMixedPrecisionEngine(mpConfig, nil)

	// Create accuracy preservation engine
	apConfig := DefaultAccuracyPreservationConfig()
	apEngine := NewAccuracyPreservationEngine(apConfig, qEngine, mpEngine)

	if apEngine == nil {
		t.Fatal("Expected non-nil accuracy preservation engine")
	}

	if apEngine.quantizationEngine != qEngine {
		t.Error("Quantization engine not properly set")
	}

	if apEngine.mixedPrecisionEngine != mpEngine {
		t.Error("Mixed precision engine not properly set")
	}

	if len(apEngine.biasCorrections) != 0 {
		t.Error("Expected empty bias corrections initially")
	}

	if len(apEngine.outlierChannelInfo) != 0 {
		t.Error("Expected empty outlier channel info initially")
	}

	if len(apEngine.degradationAnalyses) != 0 {
		t.Error("Expected empty degradation analyses initially")
	}
}

func TestDefaultAccuracyPreservationConfig(t *testing.T) {
	config := DefaultAccuracyPreservationConfig()

	// Check enabled methods
	if len(config.EnabledMethods) != 2 {
		t.Errorf("Expected 2 enabled methods, got %d", len(config.EnabledMethods))
	}

	expectedMethods := []AccuracyPreservationMethod{
		AccuracyDegradationAnalysis,
		AccuracyBiasCorrection,
	}

	for i, expected := range expectedMethods {
		if config.EnabledMethods[i] != expected {
			t.Errorf("Expected method %s at index %d, got %s", expected, i, config.EnabledMethods[i])
		}
	}

	// Check bias correction config
	if !config.BiasCorrection.EnableAdaptiveCorrection {
		t.Error("Expected adaptive correction to be enabled")
	}

	if !config.BiasCorrection.EnablePerChannelCorrection {
		t.Error("Expected per-channel correction to be enabled")
	}

	if config.BiasCorrection.CorrectionThreshold != 0.01 {
		t.Errorf("Expected correction threshold 0.01, got %f", config.BiasCorrection.CorrectionThreshold)
	}

	// Check degradation analysis config
	if !config.DegradationAnalysis.EnableLayerWiseAnalysis {
		t.Error("Expected layer-wise analysis to be enabled")
	}

	if config.DegradationAnalysis.AccuracyThreshold != 0.05 {
		t.Errorf("Expected accuracy threshold 0.05, got %f", config.DegradationAnalysis.AccuracyThreshold)
	}

	// Check overall accuracy threshold
	if config.AccuracyThreshold != 0.95 {
		t.Errorf("Expected accuracy threshold 0.95, got %f", config.AccuracyThreshold)
	}
}

func TestAccuracyPreservationMethodString(t *testing.T) {
	testCases := []struct {
		method   AccuracyPreservationMethod
		expected string
	}{
		{AccuracyBiasCorrection, "bias_correction"},
		{AccuracyOutlierChannelSplit, "outlier_channel_split"},
		{AccuracyFineTuning, "fine_tuning"},
		{AccuracyDegradationAnalysis, "degradation_analysis"},
		{AccuracyAll, "all"},
		{AccuracyPreservationMethod(999), "unknown"},
	}

	for _, tc := range testCases {
		result := tc.method.String()
		if result != tc.expected {
			t.Errorf("Expected %s for method %d, got %s", tc.expected, tc.method, result)
		}
	}
}

func TestApplyAccuracyPreservation_BiasCorrection(t *testing.T) {
	// Setup
	qConfig := QuantizationConfig{
		Method:                 QuantizationSymmetric,
		TargetType:             TensorInt8,
		UseCalibration:         true,
		PreserveAccuracy:       true,
		PerChannelQuantization: false,
	}
	qEngine := NewQuantizationEngine(qConfig)

	mpConfig := DefaultMixedPrecisionConfig()
	mpEngine := NewMixedPrecisionEngine(mpConfig, nil)

	apConfig := AccuracyPreservationConfig{
		EnabledMethods: []AccuracyPreservationMethod{AccuracyBiasCorrection},
		BiasCorrection: BiasConfig{
			EnableAdaptiveCorrection:   true,
			EnablePerChannelCorrection: true,
			CorrectionThreshold:        0.01,
			AdaptiveFactor:             0.1,
			MaxCorrectionMagnitude:     1.0,
			ConvergenceTolerance:       1e-6,
			MaxIterations:              100,
		},
		AccuracyThreshold:    0.95,
		MaxIterations:        100,
		ConvergenceThreshold: 0.001,
		EnableProgressiveOpt: true,
		ParallelProcessing:   true,
	}

	apEngine := NewAccuracyPreservationEngine(apConfig, qEngine, mpEngine)

	// Create test layer with bias
	layer := NewLayer("test_layer_1", "test_conv", LayerTypeConvolution)
	layer.InputShape = []int64{1, 32, 64, 64}
	layer.OutputShape = []int64{1, 64, 32, 32}
	layer.WeightShape = []int64{64, 32, 3, 3}
	layer.BiasShape = []int64{64}
	layer.AssignedPrecision = PrecisionINT8

	// Create test weights
	weights, err := NewTensor(TensorShape(layer.WeightShape), TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create weights tensor: %v", err)
	}
	defer weights.Free()

	// Fill weights with test data using proper tensor indexing
	err = fillTensorWithTestData(weights, func(i int) float32 {
		return float32(0.01 * math.Sin(float64(i)))
	})
	if err != nil {
		t.Fatalf("Failed to fill weights: %v", err)
	}

	// Create test calibration data
	testData := make([]*CalibrationSample, 5)
	for i := range testData {
		data, err := NewTensor(TensorShape(layer.InputShape), TensorFloat32, DeviceCPU, 0)
		if err != nil {
			t.Fatalf("Failed to create calibration data: %v", err)
		}
		defer data.Free()

		// Fill with random data
		err = fillTensorWithTestData(data, func(j int) float32 {
			return float32(0.5 + 0.1*math.Sin(float64(i*100+j)))
		})
		if err != nil {
			t.Fatalf("Failed to fill calibration data: %v", err)
		}

		testData[i] = &CalibrationSample{
			Data:   data,
			Weight: 1.0,
		}
	}

	// Apply accuracy preservation
	err = apEngine.ApplyAccuracyPreservation(layer, weights, testData)
	if err != nil {
		t.Fatalf("Failed to apply accuracy preservation: %v", err)
	}

	// Verify bias correction was applied
	if len(apEngine.biasCorrections) != 1 {
		t.Errorf("Expected 1 bias correction, got %d", len(apEngine.biasCorrections))
	}

	biasCorrection, exists := apEngine.biasCorrections["test_layer_1"]
	if !exists {
		t.Error("Expected bias correction for test_layer_1")
	} else {
		if biasCorrection.LayerID != "test_layer_1" {
			t.Errorf("Expected layer ID test_layer_1, got %s", biasCorrection.LayerID)
		}

		if len(biasCorrection.QuantizationError) == 0 {
			t.Error("Expected non-empty quantization error")
		}

		if biasCorrection.AccuracyImprovement < 0 {
			t.Error("Expected non-negative accuracy improvement")
		}
	}
}

func TestApplyAccuracyPreservation_DegradationAnalysis(t *testing.T) {
	// Setup
	qConfig := QuantizationConfig{
		Method:                 QuantizationSymmetric,
		TargetType:             TensorInt8,
		UseCalibration:         true,
		PreserveAccuracy:       true,
		PerChannelQuantization: false,
	}
	qEngine := NewQuantizationEngine(qConfig)

	mpConfig := DefaultMixedPrecisionConfig()
	mpEngine := NewMixedPrecisionEngine(mpConfig, nil)

	apConfig := AccuracyPreservationConfig{
		EnabledMethods: []AccuracyPreservationMethod{AccuracyDegradationAnalysis},
		DegradationAnalysis: DegradationConfig{
			EnableLayerWiseAnalysis:     true,
			EnableAutomaticOptimization: true,
			AccuracyThreshold:           0.05,
			SampleSize:                  1000,
			ConfidenceLevel:             0.95,
			StatisticalTestType:         "t-test",
			MaxOptimizationRounds:       10,
		},
		AccuracyThreshold:    0.95,
		MaxIterations:        100,
		ConvergenceThreshold: 0.001,
		EnableProgressiveOpt: true,
		ParallelProcessing:   true,
	}

	apEngine := NewAccuracyPreservationEngine(apConfig, qEngine, mpEngine)

	// Create test layer
	layer := NewLayer("test_layer_2", "test_linear", LayerTypeLinear)
	layer.InputShape = []int64{1, 256}
	layer.OutputShape = []int64{1, 128}
	layer.WeightShape = []int64{256, 128}
	layer.AssignedPrecision = PrecisionINT8

	// Create test weights
	weights, err := NewTensor(TensorShape(layer.WeightShape), TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create weights tensor: %v", err)
	}
	defer weights.Free()

	// Fill weights with test data using proper tensor indexing
	err = fillTensorWithTestData(weights, func(i int) float32 {
		return float32(0.02 * math.Cos(float64(i)))
	})
	if err != nil {
		t.Fatalf("Failed to fill weights: %v", err)
	}

	// Create test calibration data
	testData := make([]*CalibrationSample, 3)
	for i := range testData {
		data, err := NewTensor(TensorShape(layer.InputShape), TensorFloat32, DeviceCPU, 0)
		if err != nil {
			t.Fatalf("Failed to create calibration data: %v", err)
		}
		defer data.Free()

		// Fill with random data
		err = fillTensorWithTestData(data, func(j int) float32 {
			return float32(0.3 + 0.2*math.Cos(float64(i*50+j)))
		})
		if err != nil {
			t.Fatalf("Failed to fill calibration data: %v", err)
		}

		testData[i] = &CalibrationSample{
			Data:   data,
			Weight: 1.0,
		}
	}

	// Apply accuracy preservation
	err = apEngine.ApplyAccuracyPreservation(layer, weights, testData)
	if err != nil {
		t.Fatalf("Failed to apply accuracy preservation: %v", err)
	}

	// Verify degradation analysis was performed
	if len(apEngine.degradationAnalyses) != 1 {
		t.Errorf("Expected 1 degradation analysis, got %d", len(apEngine.degradationAnalyses))
	}

	analysis, exists := apEngine.degradationAnalyses["test_layer_2"]
	if !exists {
		t.Error("Expected degradation analysis for test_layer_2")
	} else {
		if analysis.LayerID != "test_layer_2" {
			t.Errorf("Expected layer ID test_layer_2, got %s", analysis.LayerID)
		}

		if analysis.BaselineAccuracy <= 0 {
			t.Error("Expected positive baseline accuracy")
		}

		if analysis.QuantizedAccuracy <= 0 {
			t.Error("Expected positive quantized accuracy")
		}

		if analysis.AccuracyDegradation < 0 {
			t.Error("Expected non-negative accuracy degradation")
		}

		if len(analysis.OptimizationSuggestions) == 0 {
			t.Error("Expected optimization suggestions")
		}

		// Check that recommended precision is valid
		validPrecisions := []PrecisionMode{PrecisionFP32, PrecisionFP16, PrecisionINT8, PrecisionINT4}
		found := false
		for _, validPrecision := range validPrecisions {
			if analysis.RecommendedPrecision == validPrecision {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Invalid recommended precision: %s", analysis.RecommendedPrecision)
		}
	}

	// Verify accuracy history was updated
	if len(apEngine.accuracyHistory) == 0 {
		t.Error("Expected accuracy history to be updated")
	}
}

func TestApplyAccuracyPreservation_AllMethods(t *testing.T) {
	// Setup
	qConfig := QuantizationConfig{
		Method:                 QuantizationSymmetric,
		TargetType:             TensorInt8,
		UseCalibration:         true,
		PreserveAccuracy:       true,
		PerChannelQuantization: false,
	}
	qEngine := NewQuantizationEngine(qConfig)

	mpConfig := DefaultMixedPrecisionConfig()
	mpEngine := NewMixedPrecisionEngine(mpConfig, nil)

	apConfig := AccuracyPreservationConfig{
		EnabledMethods: []AccuracyPreservationMethod{AccuracyAll},
		BiasCorrection: BiasConfig{
			EnableAdaptiveCorrection:   true,
			EnablePerChannelCorrection: true,
			CorrectionThreshold:        0.01,
			AdaptiveFactor:             0.1,
			MaxCorrectionMagnitude:     1.0,
			ConvergenceTolerance:       1e-6,
			MaxIterations:              100,
		},
		OutlierChannelSplitting: OutlierConfig{
			EnableAdaptiveThreshold: true,
			EnableGradientGuided:    true,
			OutlierThreshold:        2.0,
			GradientThreshold:       0.1,
			MaxSplitRatio:           0.1,
			MinChannelsToSplit:      4,
			AdaptiveThresholdFactor: 1.5,
		},
		FineTuning: FineTuningConfig{
			LearningRate:              0.001,
			Epochs:                    10, // Reduced for test speed
			BatchSize:                 32,
			LayerWiseAdaptive:         true,
			LayerSpecificLR:           make(map[string]float32),
			GradientClipping:          true,
			GradientClipValue:         1.0,
			QuantizationAwareTraining: true,
			RegularizationWeight:      0.0001,
			ConvergenceThreshold:      0.001,
			EarlyStoppingPatience:     5,
			ValidationSplit:           0.2,
			SensitivityBasedSchedule:  true,
		},
		DegradationAnalysis: DegradationConfig{
			EnableLayerWiseAnalysis:     true,
			EnableAutomaticOptimization: true,
			AccuracyThreshold:           0.05,
			SampleSize:                  1000,
			ConfidenceLevel:             0.95,
			StatisticalTestType:         "t-test",
			MaxOptimizationRounds:       10,
		},
		AccuracyThreshold:    0.95,
		MaxIterations:        100,
		ConvergenceThreshold: 0.001,
		EnableProgressiveOpt: true,
		ParallelProcessing:   true,
	}

	apEngine := NewAccuracyPreservationEngine(apConfig, qEngine, mpEngine)

	// Create test layer with bias
	layer := NewLayer("test_layer_all", "test_conv_all", LayerTypeConvolution)
	layer.InputShape = []int64{1, 16, 32, 32}
	layer.OutputShape = []int64{1, 32, 16, 16}
	layer.WeightShape = []int64{32, 16, 3, 3}
	layer.BiasShape = []int64{32}
	layer.AssignedPrecision = PrecisionINT8

	// Create test weights
	weights, err := NewTensor(TensorShape(layer.WeightShape), TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create weights tensor: %v", err)
	}
	defer weights.Free()

	// Fill weights with test data using proper tensor indexing
	err = fillTensorWithTestData(weights, func(i int) float32 {
		return float32(0.01 * math.Sin(float64(i)))
	})
	if err != nil {
		t.Fatalf("Failed to fill weights: %v", err)
	}

	// Create test calibration data
	testData := make([]*CalibrationSample, 2)
	for i := range testData {
		data, err := NewTensor(TensorShape(layer.InputShape), TensorFloat32, DeviceCPU, 0)
		if err != nil {
			t.Fatalf("Failed to create calibration data: %v", err)
		}
		defer data.Free()

		// Fill with random data
		err = fillTensorWithTestData(data, func(j int) float32 {
			return float32(0.5 + 0.1*math.Sin(float64(i*100+j)))
		})
		if err != nil {
			t.Fatalf("Failed to fill calibration data: %v", err)
		}

		testData[i] = &CalibrationSample{
			Data:   data,
			Weight: 1.0,
		}
	}

	// Apply accuracy preservation with all methods
	err = apEngine.ApplyAccuracyPreservation(layer, weights, testData)
	if err != nil {
		t.Fatalf("Failed to apply accuracy preservation: %v", err)
	}

	// Verify all methods were applied
	if len(apEngine.degradationAnalyses) != 1 {
		t.Errorf("Expected 1 degradation analysis, got %d", len(apEngine.degradationAnalyses))
	}

	if len(apEngine.biasCorrections) != 1 {
		t.Errorf("Expected 1 bias correction, got %d", len(apEngine.biasCorrections))
	}

	if len(apEngine.outlierChannelInfo) != 1 {
		t.Errorf("Expected 1 outlier channel info, got %d", len(apEngine.outlierChannelInfo))
	}
}

func TestGetAccuracyPreservationSummary(t *testing.T) {
	// Setup basic engine
	qConfig := QuantizationConfig{
		Method:                 QuantizationSymmetric,
		TargetType:             TensorInt8,
		UseCalibration:         true,
		PreserveAccuracy:       true,
		PerChannelQuantization: false,
	}
	qEngine := NewQuantizationEngine(qConfig)

	mpConfig := DefaultMixedPrecisionConfig()
	mpEngine := NewMixedPrecisionEngine(mpConfig, nil)

	apConfig := DefaultAccuracyPreservationConfig()
	apEngine := NewAccuracyPreservationEngine(apConfig, qEngine, mpEngine)

	// Add some mock data
	apEngine.biasCorrections["layer1"] = &BiasCorrection{
		LayerID:             "layer1",
		AccuracyImprovement: 0.05,
	}
	apEngine.biasCorrections["layer2"] = &BiasCorrection{
		LayerID:             "layer2",
		AccuracyImprovement: 0.03,
	}

	apEngine.degradationAnalyses["layer1"] = &DegradationAnalysis{
		LayerID:             "layer1",
		RelativeDegradation: 0.02,
		CriticalityScore:    0.8,
	}
	apEngine.degradationAnalyses["layer2"] = &DegradationAnalysis{
		LayerID:             "layer2",
		RelativeDegradation: 0.01,
		CriticalityScore:    0.4,
	}

	apEngine.outlierChannelInfo["layer1"] = &OutlierChannelInfo{
		LayerID: "layer1",
	}

	apEngine.accuracyHistory = []float32{0.92, 0.94, 0.93}

	// Get summary
	summary := apEngine.GetAccuracyPreservationSummary()

	// Verify summary contents
	if summary["total_layers_processed"] != 2 {
		t.Errorf("Expected 2 layers processed, got %v", summary["total_layers_processed"])
	}

	if summary["bias_corrections_applied"] != 2 {
		t.Errorf("Expected 2 bias corrections, got %v", summary["bias_corrections_applied"])
	}

	if summary["outlier_channels_detected"] != 1 {
		t.Errorf("Expected 1 outlier channel detection, got %v", summary["outlier_channels_detected"])
	}

	avgImprovement, exists := summary["average_accuracy_improvement"]
	if !exists {
		t.Error("Expected average accuracy improvement in summary")
	} else {
		expected := float32(0.04) // (0.05 + 0.03) / 2
		if math.Abs(float64(avgImprovement.(float32)-expected)) > 1e-6 {
			t.Errorf("Expected average improvement %f, got %f", expected, avgImprovement)
		}
	}

	avgDegradation, exists := summary["average_degradation"]
	if !exists {
		t.Error("Expected average degradation in summary")
	} else {
		expected := float32(0.015) // (0.02 + 0.01) / 2
		if math.Abs(float64(avgDegradation.(float32)-expected)) > 1e-6 {
			t.Errorf("Expected average degradation %f, got %f", expected, avgDegradation)
		}
	}

	if summary["high_criticality_layers"] != 1 {
		t.Errorf("Expected 1 high criticality layer, got %v", summary["high_criticality_layers"])
	}

	historySlice, exists := summary["accuracy_history"]
	if !exists {
		t.Error("Expected accuracy history in summary")
	} else {
		history := historySlice.([]float32)
		if len(history) != 3 {
			t.Errorf("Expected 3 history entries, got %d", len(history))
		}
	}
}

func TestAccuracyPreservationEngine_ErrorHandling(t *testing.T) {
	// Setup
	qConfig := QuantizationConfig{
		Method:                 QuantizationSymmetric,
		TargetType:             TensorInt8,
		UseCalibration:         true,
		PreserveAccuracy:       true,
		PerChannelQuantization: false,
	}
	qEngine := NewQuantizationEngine(qConfig)

	mpConfig := DefaultMixedPrecisionConfig()
	mpEngine := NewMixedPrecisionEngine(mpConfig, nil)

	apConfig := DefaultAccuracyPreservationConfig()
	apEngine := NewAccuracyPreservationEngine(apConfig, qEngine, mpEngine)

	// Test with nil layer
	err := apEngine.ApplyAccuracyPreservation(nil, nil, nil)
	if err == nil {
		t.Error("Expected error for nil layer")
	}

	// Test with valid layer but nil weights
	layer := NewLayer("test_error", "test", LayerTypeLinear)
	err = apEngine.ApplyAccuracyPreservation(layer, nil, nil)
	if err == nil {
		t.Error("Expected error for nil weights")
	}
}

func BenchmarkAccuracyPreservation_BiasCorrection(b *testing.B) {
	// Setup
	qConfig := QuantizationConfig{
		Method:                 QuantizationSymmetric,
		TargetType:             TensorInt8,
		UseCalibration:         true,
		PreserveAccuracy:       true,
		PerChannelQuantization: false,
	}
	qEngine := NewQuantizationEngine(qConfig)

	mpConfig := DefaultMixedPrecisionConfig()
	mpEngine := NewMixedPrecisionEngine(mpConfig, nil)

	apConfig := AccuracyPreservationConfig{
		EnabledMethods: []AccuracyPreservationMethod{AccuracyBiasCorrection},
		BiasCorrection: BiasConfig{
			EnableAdaptiveCorrection:   true,
			EnablePerChannelCorrection: true,
			CorrectionThreshold:        0.01,
			AdaptiveFactor:             0.1,
			MaxCorrectionMagnitude:     1.0,
			ConvergenceTolerance:       1e-6,
			MaxIterations:              100,
		},
		AccuracyThreshold:    0.95,
		MaxIterations:        100,
		ConvergenceThreshold: 0.001,
		EnableProgressiveOpt: true,
		ParallelProcessing:   true,
	}

	apEngine := NewAccuracyPreservationEngine(apConfig, qEngine, mpEngine)

	// Create test layer
	layer := NewLayer("bench_layer", "bench_conv", LayerTypeConvolution)
	layer.InputShape = []int64{1, 32, 64, 64}
	layer.OutputShape = []int64{1, 64, 32, 32}
	layer.WeightShape = []int64{64, 32, 3, 3}
	layer.BiasShape = []int64{64}
	layer.AssignedPrecision = PrecisionINT8

	// Create test weights
	weights, err := NewTensor(TensorShape(layer.WeightShape), TensorFloat32, DeviceCPU, 0)
	if err != nil {
		b.Fatalf("Failed to create weights tensor: %v", err)
	}
	defer weights.Free()

	// Create test calibration data
	testData := make([]*CalibrationSample, 5)
	for i := range testData {
		data, err := NewTensor(TensorShape(layer.InputShape), TensorFloat32, DeviceCPU, 0)
		if err != nil {
			b.Fatalf("Failed to create calibration data: %v", err)
		}
		defer data.Free()

		testData[i] = &CalibrationSample{
			Data:   data,
			Weight: 1.0,
		}
	}

	// Benchmark
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Clear previous results
		apEngine.biasCorrections = make(map[string]*BiasCorrection)

		err := apEngine.ApplyAccuracyPreservation(layer, weights, testData)
		if err != nil {
			b.Fatalf("Failed to apply accuracy preservation: %v", err)
		}
	}
}
