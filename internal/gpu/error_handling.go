package gpu

import (
	"context"
	"fmt"
	"log"
	"runtime"
	"sync"
	"time"
)

// GPUErrorType represents different categories of GPU errors
type GPUErrorType int

const (
	GPUErrorTypeMemory GPUErrorType = iota
	GPUErrorTypeExecution
	GPUErrorTypeDriver
	GPUErrorTypeDevice
	GPUErrorTypeContext
	GPUErrorTypeTimeout
	GPUErrorTypeOOM
	GPUErrorTypeUnknown
)

// String returns a human-readable string representation of the GPU error type
func (t GPUErrorType) String() string {
	switch t {
	case GPUErrorTypeMemory:
		return "memory"
	case GPUErrorTypeExecution:
		return "execution"
	case GPUErrorTypeDriver:
		return "driver"
	case GPUErrorTypeDevice:
		return "device"
	case GPUErrorTypeContext:
		return "context"
	case GPUErrorTypeTimeout:
		return "timeout"
	case GPUErrorTypeOOM:
		return "out_of_memory"
	default:
		return "unknown"
	}
}

// GPUErrorSeverity represents the severity level of GPU errors
type GPUErrorSeverity int

const (
	GPUErrorSeverityInfo GPUErrorSeverity = iota
	GPUErrorSeverityWarning
	GPUErrorSeverityError
	GPUErrorSeverityCritical
	GPUErrorSeverityFatal
)

// String returns a human-readable string representation of the severity
func (s GPUErrorSeverity) String() string {
	switch s {
	case GPUErrorSeverityInfo:
		return "info"
	case GPUErrorSeverityWarning:
		return "warning"
	case GPUErrorSeverityError:
		return "error"
	case GPUErrorSeverityCritical:
		return "critical"
	case GPUErrorSeverityFatal:
		return "fatal"
	default:
		return "unknown"
	}
}

// RecoveryStrategy represents different recovery strategies
type RecoveryStrategy int

const (
	RecoveryStrategyNone RecoveryStrategy = iota
	RecoveryStrategyRetry
	RecoveryStrategyReinitialize
	RecoveryStrategyFallbackCPU
	RecoveryStrategyDeviceReset
	RecoveryStrategyMemoryCleanup
	RecoveryStrategyContextReset
)

// String returns a human-readable string representation of the recovery strategy
func (r RecoveryStrategy) String() string {
	switch r {
	case RecoveryStrategyNone:
		return "none"
	case RecoveryStrategyRetry:
		return "retry"
	case RecoveryStrategyReinitialize:
		return "reinitialize"
	case RecoveryStrategyFallbackCPU:
		return "fallback_cpu"
	case RecoveryStrategyDeviceReset:
		return "device_reset"
	case RecoveryStrategyMemoryCleanup:
		return "memory_cleanup"
	case RecoveryStrategyContextReset:
		return "context_reset"
	default:
		return "unknown"
	}
}

// GPUErrorInterface defines the common interface for all GPU errors
type GPUErrorInterface interface {
	error
	GetType() GPUErrorType
	GetSeverity() GPUErrorSeverity
	GetDeviceID() int
	GetTimestamp() time.Time
	GetContext() map[string]interface{}
	IsRecoverable() bool
	GetRecoveryStrategy() RecoveryStrategy
	WithContext(key string, value interface{}) GPUErrorInterface
	GetStackTrace() string
}

// BaseGPUError represents a base GPU error with comprehensive details
type BaseGPUError struct {
	Type             GPUErrorType           `json:"type"`
	Severity         GPUErrorSeverity       `json:"severity"`
	Message          string                 `json:"message"`
	DeviceID         int                    `json:"device_id"`
	Timestamp        time.Time              `json:"timestamp"`
	Context          map[string]interface{} `json:"context"`
	Underlying       error                  `json:"-"`
	Recoverable      bool                   `json:"recoverable"`
	RecoveryStrategy RecoveryStrategy       `json:"recovery_strategy"`
	StackTrace       string                 `json:"stack_trace"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// Error implements the error interface
func (e *BaseGPUError) Error() string {
	return fmt.Sprintf("GPU %s error [%s] on device %d: %s (recoverable: %v)",
		e.Type.String(), e.Severity.String(), e.DeviceID, e.Message, e.Recoverable)
}

// GetType returns the error type
func (e *BaseGPUError) GetType() GPUErrorType {
	return e.Type
}

// GetSeverity returns the error severity
func (e *BaseGPUError) GetSeverity() GPUErrorSeverity {
	return e.Severity
}

// GetDeviceID returns the device ID
func (e *BaseGPUError) GetDeviceID() int {
	return e.DeviceID
}

// GetTimestamp returns the error timestamp
func (e *BaseGPUError) GetTimestamp() time.Time {
	return e.Timestamp
}

// GetContext returns the error context
func (e *BaseGPUError) GetContext() map[string]interface{} {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	return e.Context
}

// IsRecoverable returns whether the error is recoverable
func (e *BaseGPUError) IsRecoverable() bool {
	return e.Recoverable
}

// GetRecoveryStrategy returns the recovery strategy
func (e *BaseGPUError) GetRecoveryStrategy() RecoveryStrategy {
	return e.RecoveryStrategy
}

// WithContext adds context to the error
func (e *BaseGPUError) WithContext(key string, value interface{}) GPUErrorInterface {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

// GetStackTrace returns the stack trace
func (e *BaseGPUError) GetStackTrace() string {
	return e.StackTrace
}

// Unwrap returns the underlying error
func (e *BaseGPUError) Unwrap() error {
	return e.Underlying
}

// captureStackTrace captures the current stack trace
func captureStackTrace() string {
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	return string(buf[:n])
}

// Specific error types

// MemoryError represents GPU memory-related errors
type MemoryError struct {
	*BaseGPUError
	AllocationType    string `json:"allocation_type"`
	RequestedSize     int64  `json:"requested_size"`
	AvailableMemory   int64  `json:"available_memory"`
	TotalMemory       int64  `json:"total_memory"`
	FragmentationInfo string `json:"fragmentation_info"`
}

// NewMemoryError creates a new memory error
func NewMemoryError(message string, deviceID int, allocationType string, requestedSize, availableMemory, totalMemory int64, underlying error) *MemoryError {
	severity := GPUErrorSeverityError
	recoverable := true
	strategy := RecoveryStrategyMemoryCleanup

	// In GPU-focused product, don't fallback to CPU for memory errors
	// Instead, use memory cleanup or fail the operation
	if requestedSize > totalMemory {
		severity = GPUErrorSeverityCritical
		recoverable = false             // Cannot recover if request exceeds total GPU memory
		strategy = RecoveryStrategyNone // Changed from RecoveryStrategyFallbackCPU
	} else if availableMemory < requestedSize {
		strategy = RecoveryStrategyMemoryCleanup
	}

	return &MemoryError{
		BaseGPUError: &BaseGPUError{
			Type:             GPUErrorTypeMemory,
			Severity:         severity,
			Message:          message,
			DeviceID:         deviceID,
			Timestamp:        time.Now(),
			Context:          make(map[string]interface{}),
			Underlying:       underlying,
			Recoverable:      recoverable,
			RecoveryStrategy: strategy,
			StackTrace:       captureStackTrace(),
			Metadata:         make(map[string]interface{}),
		},
		AllocationType:  allocationType,
		RequestedSize:   requestedSize,
		AvailableMemory: availableMemory,
		TotalMemory:     totalMemory,
	}
}

// ExecutionError represents GPU execution-related errors
type ExecutionError struct {
	*BaseGPUError
	KernelName   string                 `json:"kernel_name"`
	GridSize     [3]int                 `json:"grid_size"`
	BlockSize    [3]int                 `json:"block_size"`
	SharedMemory int64                  `json:"shared_memory"`
	Parameters   map[string]interface{} `json:"parameters"`
}

// NewExecutionError creates a new execution error
func NewExecutionError(message string, deviceID int, kernelName string, underlying error) *ExecutionError {
	return &ExecutionError{
		BaseGPUError: &BaseGPUError{
			Type:             GPUErrorTypeExecution,
			Severity:         GPUErrorSeverityError,
			Message:          message,
			DeviceID:         deviceID,
			Timestamp:        time.Now(),
			Context:          make(map[string]interface{}),
			Underlying:       underlying,
			Recoverable:      true,
			RecoveryStrategy: RecoveryStrategyRetry,
			StackTrace:       captureStackTrace(),
			Metadata:         make(map[string]interface{}),
		},
		KernelName: kernelName,
		Parameters: make(map[string]interface{}),
	}
}

// DriverError represents GPU driver-related errors
type DriverError struct {
	*BaseGPUError
	DriverVersion string `json:"driver_version"`
	CUDAVersion   string `json:"cuda_version"`
	ErrorCode     int    `json:"error_code"`
}

// NewDriverError creates a new driver error
func NewDriverError(message string, deviceID int, driverVersion, cudaVersion string, errorCode int, underlying error) *DriverError {
	return &DriverError{
		BaseGPUError: &BaseGPUError{
			Type:             GPUErrorTypeDriver,
			Severity:         GPUErrorSeverityCritical,
			Message:          message,
			DeviceID:         deviceID,
			Timestamp:        time.Now(),
			Context:          make(map[string]interface{}),
			Underlying:       underlying,
			Recoverable:      false,                // Driver errors are not recoverable
			RecoveryStrategy: RecoveryStrategyNone, // Changed from RecoveryStrategyFallbackCPU
			StackTrace:       captureStackTrace(),
			Metadata:         make(map[string]interface{}),
		},
		DriverVersion: driverVersion,
		CUDAVersion:   cudaVersion,
		ErrorCode:     errorCode,
	}
}

// DeviceLossError represents GPU device disconnection errors
type DeviceLossError struct {
	*BaseGPUError
	LastKnownState map[string]interface{} `json:"last_known_state"`
	DetectionTime  time.Time              `json:"detection_time"`
}

// NewDeviceLossError creates a new device loss error
func NewDeviceLossError(message string, deviceID int, lastKnownState map[string]interface{}, underlying error) *DeviceLossError {
	return &DeviceLossError{
		BaseGPUError: &BaseGPUError{
			Type:             GPUErrorTypeDevice,
			Severity:         GPUErrorSeverityFatal,
			Message:          message,
			DeviceID:         deviceID,
			Timestamp:        time.Now(),
			Context:          make(map[string]interface{}),
			Underlying:       underlying,
			Recoverable:      true,
			RecoveryStrategy: RecoveryStrategyReinitialize,
			StackTrace:       captureStackTrace(),
			Metadata:         make(map[string]interface{}),
		},
		LastKnownState: lastKnownState,
		DetectionTime:  time.Now(),
	}
}

// OutOfMemoryError represents GPU out-of-memory errors
type OutOfMemoryError struct {
	*BaseGPUError
	RequestedBytes   int64                  `json:"requested_bytes"`
	AvailableBytes   int64                  `json:"available_bytes"`
	TotalBytes       int64                  `json:"total_bytes"`
	AllocationMap    map[string]int64       `json:"allocation_map"`
	MemoryPressure   float64                `json:"memory_pressure"`
	SuggestedActions []string               `json:"suggested_actions"`
	FragmentationMap map[string]interface{} `json:"fragmentation_map"`
}

// NewOutOfMemoryError creates a new out-of-memory error
func NewOutOfMemoryError(message string, deviceID int, requestedBytes, availableBytes, totalBytes int64, underlying error) *OutOfMemoryError {
	memoryPressure := float64(totalBytes-availableBytes) / float64(totalBytes) * 100.0

	// GPU-focused suggestions - no CPU fallback
	suggestedActions := []string{
		"Reduce batch size",
		"Enable memory pooling",
		"Use gradient checkpointing",
		"Clear unused GPU memory", // Changed from "Switch to CPU fallback"
		"Use mixed precision training",
	}

	if memoryPressure > 95.0 {
		suggestedActions = append(suggestedActions, "Immediate memory cleanup required")
	}

	return &OutOfMemoryError{
		BaseGPUError: &BaseGPUError{
			Type:             GPUErrorTypeOOM,
			Severity:         GPUErrorSeverityCritical,
			Message:          message,
			DeviceID:         deviceID,
			Timestamp:        time.Now(),
			Context:          make(map[string]interface{}),
			Underlying:       underlying,
			Recoverable:      true,
			RecoveryStrategy: RecoveryStrategyMemoryCleanup,
			StackTrace:       captureStackTrace(),
			Metadata:         make(map[string]interface{}),
		},
		RequestedBytes:   requestedBytes,
		AvailableBytes:   availableBytes,
		TotalBytes:       totalBytes,
		AllocationMap:    make(map[string]int64),
		MemoryPressure:   memoryPressure,
		SuggestedActions: suggestedActions,
		FragmentationMap: make(map[string]interface{}),
	}
}

// GPUErrorStatistics tracks GPU error statistics
type GPUErrorStatistics struct {
	TotalErrors          int64                      `json:"total_errors"`
	ErrorsByType         map[GPUErrorType]int64     `json:"errors_by_type"`
	ErrorsBySeverity     map[GPUErrorSeverity]int64 `json:"errors_by_severity"`
	ErrorsByDevice       map[int]int64              `json:"errors_by_device"`
	RecoverableErrors    int64                      `json:"recoverable_errors"`
	NonRecoverableErrors int64                      `json:"non_recoverable_errors"`
	SuccessfulRecoveries int64                      `json:"successful_recoveries"`
	FailedRecoveries     int64                      `json:"failed_recoveries"`
	AverageErrorDuration time.Duration              `json:"average_error_duration"`
	LastError            GPUErrorInterface          `json:"-"`
	ErrorFrequency       map[string]int64           `json:"error_frequency"`
	mutex                sync.RWMutex               `json:"-"`
}

// NewGPUErrorStatistics creates a new GPU error statistics tracker
func NewGPUErrorStatistics() *GPUErrorStatistics {
	return &GPUErrorStatistics{
		ErrorsByType:     make(map[GPUErrorType]int64),
		ErrorsBySeverity: make(map[GPUErrorSeverity]int64),
		ErrorsByDevice:   make(map[int]int64),
		ErrorFrequency:   make(map[string]int64),
	}
}

// RecordError records a GPU error in statistics
func (s *GPUErrorStatistics) RecordError(err GPUErrorInterface) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.TotalErrors++
	s.ErrorsByType[err.GetType()]++
	s.ErrorsBySeverity[err.GetSeverity()]++
	s.ErrorsByDevice[err.GetDeviceID()]++
	s.ErrorFrequency[err.Error()]++
	s.LastError = err

	if err.IsRecoverable() {
		s.RecoverableErrors++
	} else {
		s.NonRecoverableErrors++
	}
}

// RecordRecovery records a recovery attempt result
func (s *GPUErrorStatistics) RecordRecovery(successful bool) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if successful {
		s.SuccessfulRecoveries++
	} else {
		s.FailedRecoveries++
	}
}

// GetRecoveryRate returns the recovery success rate
func (s *GPUErrorStatistics) GetRecoveryRate() float64 {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	total := s.SuccessfulRecoveries + s.FailedRecoveries
	if total == 0 {
		return 0.0
	}
	return float64(s.SuccessfulRecoveries) / float64(total) * 100.0
}

// RecoverySession represents an active recovery session
type RecoverySession struct {
	DeviceID   int                `json:"device_id"`
	StartTime  time.Time          `json:"start_time"`
	EndTime    time.Time          `json:"end_time"`
	Strategy   RecoveryStrategy   `json:"strategy"`
	Attempts   int                `json:"attempts"`
	Successful bool               `json:"successful"`
	Error      error              `json:"-"`
	Context    context.Context    `json:"-"`
	CancelFunc context.CancelFunc `json:"-"`
	mutex      sync.RWMutex       `json:"-"`
}

// IsActive returns whether the recovery session is active
func (rs *RecoverySession) IsActive() bool {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()
	return rs.EndTime.IsZero()
}

// MarkSuccess marks the recovery as successful
func (rs *RecoverySession) MarkSuccess() {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()
	rs.Successful = true
	rs.EndTime = time.Now()
}

// MarkFailure marks the recovery as failed
func (rs *RecoverySession) MarkFailure(err error) {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()
	rs.Successful = false
	rs.Error = err
	rs.EndTime = time.Now()
}

// Cancel cancels the recovery session
func (rs *RecoverySession) Cancel() {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()
	if rs.CancelFunc != nil {
		rs.CancelFunc()
	}
	rs.EndTime = time.Now()
}

// RecoveryManager manages GPU recovery operations
type RecoveryManager struct {
	logger         *log.Logger
	config         ErrorHandlerConfig
	activeSessions map[int]*RecoverySession
	mutex          sync.RWMutex
}

// NewRecoveryManager creates a new recovery manager
func NewRecoveryManager(logger *log.Logger, config ErrorHandlerConfig) *RecoveryManager {
	return &RecoveryManager{
		logger:         logger,
		config:         config,
		activeSessions: make(map[int]*RecoverySession),
	}
}

// StartRecovery starts a new recovery session
func (rm *RecoveryManager) StartRecovery(ctx context.Context, err GPUErrorInterface) *RecoverySession {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	deviceID := err.GetDeviceID()
	recoveryCtx, cancel := context.WithTimeout(ctx, rm.config.RecoveryTimeout)

	session := &RecoverySession{
		DeviceID:   deviceID,
		StartTime:  time.Now(),
		Strategy:   err.GetRecoveryStrategy(),
		Attempts:   0,
		Context:    recoveryCtx,
		CancelFunc: cancel,
	}

	rm.activeSessions[deviceID] = session
	return session
}

// MemoryTracker tracks GPU memory allocations and provides cleanup
type MemoryTracker struct {
	logger      *log.Logger
	allocations map[int]map[string]int64 // deviceID -> allocation name -> size
	mutex       sync.RWMutex
}

// NewMemoryTracker creates a new memory tracker
func NewMemoryTracker(logger *log.Logger) *MemoryTracker {
	return &MemoryTracker{
		logger:      logger,
		allocations: make(map[int]map[string]int64),
	}
}

// TrackAllocation tracks a memory allocation
func (mt *MemoryTracker) TrackAllocation(deviceID int, name string, size int64) {
	mt.mutex.Lock()
	defer mt.mutex.Unlock()

	if mt.allocations[deviceID] == nil {
		mt.allocations[deviceID] = make(map[string]int64)
	}
	mt.allocations[deviceID][name] = size
}

// CleanupDevice performs memory cleanup for a device
func (mt *MemoryTracker) CleanupDevice(deviceID int) error {
	mt.mutex.Lock()
	defer mt.mutex.Unlock()

	if allocations, exists := mt.allocations[deviceID]; exists {
		for name := range allocations {
			mt.logger.Printf("Cleaning up allocation %s on device %d", name, deviceID)
		}
		delete(mt.allocations, deviceID)
	}

	return nil
}

// DeviceMonitor monitors GPU device health
type DeviceMonitor struct {
	logger   *log.Logger
	interval time.Duration
	stopChan chan struct{}
	running  bool
	mutex    sync.RWMutex
}

// NewDeviceMonitor creates a new device monitor
func NewDeviceMonitor(logger *log.Logger, interval time.Duration) *DeviceMonitor {
	return &DeviceMonitor{
		logger:   logger,
		interval: interval,
		stopChan: make(chan struct{}),
	}
}

// Start starts the device monitor
func (dm *DeviceMonitor) Start() error {
	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	if dm.running {
		return fmt.Errorf("device monitor already running")
	}

	dm.running = true
	go dm.monitorLoop()
	return nil
}

// Stop stops the device monitor
func (dm *DeviceMonitor) Stop() error {
	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	if !dm.running {
		return nil
	}

	close(dm.stopChan)
	dm.running = false
	return nil
}

// monitorLoop runs the monitoring loop
func (dm *DeviceMonitor) monitorLoop() {
	ticker := time.NewTicker(dm.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			dm.checkDevices()
		case <-dm.stopChan:
			return
		}
	}
}

// checkDevices checks the health of all devices
func (dm *DeviceMonitor) checkDevices() {
	// In a real implementation, this would check actual device health
	dm.logger.Printf("Checking device health...")
}

// ErrorHandlerConfig represents configuration for the error handler
type ErrorHandlerConfig struct {
	EnableRecovery         bool          `json:"enable_recovery"`
	MaxRecoveryAttempts    int           `json:"max_recovery_attempts"`
	RecoveryTimeout        time.Duration `json:"recovery_timeout"`
	MemoryCleanupThreshold float64       `json:"memory_cleanup_threshold"`
	DeviceCheckInterval    time.Duration `json:"device_check_interval"`
	LogLevel               string        `json:"log_level"`
	MetricsEnabled         bool          `json:"metrics_enabled"`
	TestMode               bool          `json:"test_mode"`
	StrictGPUMode          bool          `json:"strict_gpu_mode"`
}

// DefaultErrorHandlerConfig returns default error handler configuration
func DefaultErrorHandlerConfig() ErrorHandlerConfig {
	return ErrorHandlerConfig{
		EnableRecovery:         true,
		MaxRecoveryAttempts:    3,
		RecoveryTimeout:        30 * time.Second,
		MemoryCleanupThreshold: 0.8,
		DeviceCheckInterval:    5 * time.Second,
		LogLevel:               "info",
		MetricsEnabled:         true,
		TestMode:               false, // Default to production mode
		StrictGPUMode:          true,  // Default to strict GPU-focused behavior
	}
}

// TestErrorHandlerConfig returns configuration optimized for testing
// - Strict GPU mode enabled (no CPU fallback)
// - Shorter timeouts for faster test execution
func TestErrorHandlerConfig() ErrorHandlerConfig {
	return ErrorHandlerConfig{
		EnableRecovery:         true,
		MaxRecoveryAttempts:    2,                // Fewer retries for faster test execution
		RecoveryTimeout:        10 * time.Second, // Shorter timeout for tests
		MemoryCleanupThreshold: 0.8,
		DeviceCheckInterval:    2 * time.Second, // More frequent checks for tests
		LogLevel:               "debug",         // More verbose logging for test debugging
		MetricsEnabled:         true,
		TestMode:               true, // Explicitly test mode
		StrictGPUMode:          true, // Strict GPU mode for tests
	}
}

// ProductionErrorHandlerConfig returns configuration optimized for production
// - GPU-focused with strict mode enabled
// - More conservative retry settings
func ProductionErrorHandlerConfig() ErrorHandlerConfig {
	return ErrorHandlerConfig{
		EnableRecovery:         true,
		MaxRecoveryAttempts:    5,                // More retries for production resilience
		RecoveryTimeout:        60 * time.Second, // Longer timeout for production
		MemoryCleanupThreshold: 0.85,             // More conservative threshold
		DeviceCheckInterval:    10 * time.Second, // Less frequent checks
		LogLevel:               "warn",           // Less verbose for production
		MetricsEnabled:         true,
		TestMode:               false, // Production mode
		StrictGPUMode:          true,  // GPU-focused behavior in production
	}
}

// GPUErrorHandler handles GPU errors with recovery mechanisms
type GPUErrorHandler struct {
	logger           *log.Logger
	errorStats       *GPUErrorStatistics
	recoveryManager  *RecoveryManager
	memoryTracker    *MemoryTracker
	deviceMonitor    *DeviceMonitor
	config           ErrorHandlerConfig
	mutex            sync.RWMutex
	activeRecoveries map[int]*RecoverySession
}

// NewGPUErrorHandler creates a new GPU error handler
func NewGPUErrorHandler(logger *log.Logger, config ErrorHandlerConfig) (*GPUErrorHandler, error) {
	if logger == nil {
		logger = log.Default()
	}

	errorStats := NewGPUErrorStatistics()
	recoveryManager := NewRecoveryManager(logger, config)
	memoryTracker := NewMemoryTracker(logger)
	deviceMonitor := NewDeviceMonitor(logger, config.DeviceCheckInterval)

	handler := &GPUErrorHandler{
		logger:           logger,
		errorStats:       errorStats,
		recoveryManager:  recoveryManager,
		memoryTracker:    memoryTracker,
		deviceMonitor:    deviceMonitor,
		config:           config,
		activeRecoveries: make(map[int]*RecoverySession),
	}

	// Start device monitoring if enabled
	if config.EnableRecovery {
		if err := handler.deviceMonitor.Start(); err != nil {
			return nil, fmt.Errorf("failed to start device monitor: %w", err)
		}
	}

	return handler, nil
}

// HandleError handles a GPU error with appropriate recovery strategy
func (h *GPUErrorHandler) HandleError(ctx context.Context, err GPUErrorInterface) error {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	// Record error statistics
	h.errorStats.RecordError(err)

	// Log the error
	h.logError(err)

	// Check if recovery is enabled and the error is recoverable
	if !h.config.EnableRecovery || !err.IsRecoverable() {
		return err
	}

	// Check if there's already an active recovery for this device
	deviceID := err.GetDeviceID()
	if session, exists := h.activeRecoveries[deviceID]; exists {
		if session.IsActive() {
			return fmt.Errorf("recovery already in progress for device %d", deviceID)
		}
		delete(h.activeRecoveries, deviceID)
	}

	// Start recovery
	session := h.recoveryManager.StartRecovery(ctx, err)
	h.activeRecoveries[deviceID] = session

	// Execute recovery strategy
	recoveryErr := h.executeRecoveryStrategy(ctx, err, session)

	// Record recovery result
	h.errorStats.RecordRecovery(recoveryErr == nil)

	// Clean up recovery session
	delete(h.activeRecoveries, deviceID)

	return recoveryErr
}

// executeRecoveryStrategy executes the appropriate recovery strategy
func (h *GPUErrorHandler) executeRecoveryStrategy(ctx context.Context, err GPUErrorInterface, session *RecoverySession) error {
	strategy := err.GetRecoveryStrategy()
	deviceID := err.GetDeviceID()

	h.logger.Printf("Executing recovery strategy %s for device %d", strategy.String(), deviceID)

	switch strategy {
	case RecoveryStrategyRetry:
		return h.retryOperation(ctx, err, session)
	case RecoveryStrategyReinitialize:
		return h.reinitializeDevice(ctx, deviceID, session)
	case RecoveryStrategyFallbackCPU:
		return h.fallbackToCPU(ctx, err, session)
	case RecoveryStrategyDeviceReset:
		return h.resetDevice(ctx, deviceID, session)
	case RecoveryStrategyMemoryCleanup:
		return h.cleanupMemory(ctx, deviceID, session)
	case RecoveryStrategyContextReset:
		return h.resetContext(ctx, deviceID, session)
	default:
		return fmt.Errorf("unknown recovery strategy: %s", strategy.String())
	}
}

// retryOperation retries the failed operation
func (h *GPUErrorHandler) retryOperation(ctx context.Context, err GPUErrorInterface, session *RecoverySession) error {
	maxAttempts := h.config.MaxRecoveryAttempts

	for attempt := 1; attempt <= maxAttempts; attempt++ {
		select {
		case <-ctx.Done():
			session.MarkFailure(ctx.Err())
			return ctx.Err()
		default:
		}

		session.Attempts = attempt
		h.logger.Printf("Retry attempt %d/%d for device %d", attempt, maxAttempts, err.GetDeviceID())

		// Wait before retry with exponential backoff
		backoff := time.Duration(attempt) * time.Second
		time.Sleep(backoff)

		// Here would be the actual retry logic - for now, we simulate success
		// In a real implementation, this would re-execute the failed operation
		if attempt == maxAttempts {
			session.MarkFailure(fmt.Errorf("retry failed after %d attempts", maxAttempts))
			return fmt.Errorf("retry failed after %d attempts", maxAttempts)
		}

		// Simulate successful retry on second attempt
		if attempt >= 2 {
			session.MarkSuccess()
			h.logger.Printf("Retry successful on attempt %d for device %d", attempt, err.GetDeviceID())
			return nil
		}
	}

	return fmt.Errorf("retry exhausted")
}

// reinitializeDevice reinitializes the GPU device
func (h *GPUErrorHandler) reinitializeDevice(ctx context.Context, deviceID int, session *RecoverySession) error {
	h.logger.Printf("Reinitializing device %d", deviceID)

	// Cleanup existing resources
	if err := h.cleanupDeviceResources(deviceID); err != nil {
		h.logger.Printf("Warning: failed to cleanup device resources: %v", err)
	}

	// Reinitialize device
	// In a real implementation, this would call actual CUDA/OpenCL initialization
	select {
	case <-ctx.Done():
		session.MarkFailure(ctx.Err())
		return ctx.Err()
	case <-time.After(2 * time.Second): // Simulate reinitialization time
	}

	session.MarkSuccess()
	h.logger.Printf("Device %d reinitialized successfully", deviceID)
	return nil
}

// fallbackToCPU attempts to fallback to CPU processing
func (h *GPUErrorHandler) fallbackToCPU(ctx context.Context, err GPUErrorInterface, session *RecoverySession) error {
	deviceID := err.GetDeviceID()

	// GPU-focused product: CPU fallback is blocked during GPU failures
	if h.config.StrictGPUMode {
		session.MarkFailure(fmt.Errorf("CPU fallback blocked for device %d - strict GPU mode prevents fallback during GPU failures", deviceID))
		h.logger.Printf("ERROR: CPU fallback blocked for device %d - strict GPU mode prevents fallback during GPU failures", deviceID)
		return fmt.Errorf("CPU fallback blocked in strict GPU mode - GPU failures should not fallback to CPU")
	}

	// Even if not in strict mode, this product is GPU-focused and should not fallback to CPU
	session.MarkFailure(fmt.Errorf("CPU fallback not supported - this is a GPU-focused product"))
	h.logger.Printf("ERROR: CPU fallback requested but not supported for device %d", deviceID)
	return fmt.Errorf("CPU fallback not supported - this is a GPU-focused product")
}

// resetDevice resets the GPU device
func (h *GPUErrorHandler) resetDevice(ctx context.Context, deviceID int, session *RecoverySession) error {
	h.logger.Printf("Resetting device %d", deviceID)

	// In a real implementation, this would perform device reset
	select {
	case <-ctx.Done():
		session.MarkFailure(ctx.Err())
		return ctx.Err()
	case <-time.After(3 * time.Second): // Simulate reset time
	}

	session.MarkSuccess()
	h.logger.Printf("Device %d reset successfully", deviceID)
	return nil
}

// cleanupMemory performs memory cleanup
func (h *GPUErrorHandler) cleanupMemory(ctx context.Context, deviceID int, session *RecoverySession) error {
	h.logger.Printf("Cleaning up memory for device %d", deviceID)

	if h.memoryTracker != nil {
		if err := h.memoryTracker.CleanupDevice(deviceID); err != nil {
			session.MarkFailure(err)
			return fmt.Errorf("memory cleanup failed: %w", err)
		}
	}

	session.MarkSuccess()
	h.logger.Printf("Memory cleanup completed for device %d", deviceID)
	return nil
}

// resetContext resets the GPU context
func (h *GPUErrorHandler) resetContext(ctx context.Context, deviceID int, session *RecoverySession) error {
	h.logger.Printf("Resetting context for device %d", deviceID)

	// In a real implementation, this would reset the GPU context
	select {
	case <-ctx.Done():
		session.MarkFailure(ctx.Err())
		return ctx.Err()
	case <-time.After(1 * time.Second): // Simulate context reset time
	}

	session.MarkSuccess()
	h.logger.Printf("Context reset completed for device %d", deviceID)
	return nil
}

// cleanupDeviceResources cleans up device resources
func (h *GPUErrorHandler) cleanupDeviceResources(deviceID int) error {
	// In a real implementation, this would cleanup actual GPU resources
	h.logger.Printf("Cleaning up resources for device %d", deviceID)
	return nil
}

// logError logs the GPU error with appropriate level
func (h *GPUErrorHandler) logError(err GPUErrorInterface) {
	severity := err.GetSeverity()
	message := fmt.Sprintf("GPU Error [%s]: %s", severity.String(), err.Error())

	switch severity {
	case GPUErrorSeverityInfo:
		h.logger.Printf("INFO: %s", message)
	case GPUErrorSeverityWarning:
		h.logger.Printf("WARNING: %s", message)
	case GPUErrorSeverityError:
		h.logger.Printf("ERROR: %s", message)
	case GPUErrorSeverityCritical:
		h.logger.Printf("CRITICAL: %s", message)
	case GPUErrorSeverityFatal:
		h.logger.Printf("FATAL: %s", message)
	}

	// Log additional context if available
	if context := err.GetContext(); len(context) > 0 {
		h.logger.Printf("Error context: %+v", context)
	}
}

// GetErrorStatistics returns error statistics
func (h *GPUErrorHandler) GetErrorStatistics() *GPUErrorStatistics {
	return h.errorStats
}

// GetActiveRecoveries returns the number of active recoveries
func (h *GPUErrorHandler) GetActiveRecoveries() int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return len(h.activeRecoveries)
}

// IsRecoveryActive checks if recovery is active for a device
func (h *GPUErrorHandler) IsRecoveryActive(deviceID int) bool {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	session, exists := h.activeRecoveries[deviceID]
	return exists && session.IsActive()
}

// Cleanup cleans up the error handler
func (h *GPUErrorHandler) Cleanup() error {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	// Stop device monitoring
	if h.deviceMonitor != nil {
		if err := h.deviceMonitor.Stop(); err != nil {
			h.logger.Printf("Warning: failed to stop device monitor: %v", err)
		}
	}

	// Cancel any active recoveries
	for deviceID, session := range h.activeRecoveries {
		session.Cancel()
		h.logger.Printf("Cancelled active recovery for device %d", deviceID)
	}

	h.activeRecoveries = make(map[int]*RecoverySession)
	return nil
}
