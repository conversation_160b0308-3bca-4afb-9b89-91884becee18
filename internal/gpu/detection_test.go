package gpu

import (
	"log"
	"os"
	"testing"
	"time"
)

func TestDefaultGPUConfig(t *testing.T) {
	config := DefaultGPUConfig()

	if !config.Enabled {
		t.Error("Default config should have GPU enabled")
	}

	if !config.PreferCUDA {
		t.Error("Default config should prefer CUDA")
	}

	if config.MinMemoryGB != 2.0 {
		t.<PERSON><PERSON>("Expected MinMemoryGB to be 2.0, got %f", config.MinMemoryGB)
	}

	if err := config.Validate(); err != nil {
		t.<PERSON>rrorf("Default config should be valid: %v", err)
	}
}

func TestGPUConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      GPUConfig
		expectError bool
	}{
		{
			name:        "Valid config",
			config:      DefaultGPUConfig(),
			expectError: false,
		},
		{
			name: "Negative memory",
			config: GPUConfig{
				MinMemoryGB:          -1.0,
				MaxMemoryUtilization: 90.0,
				MonitoringInterval:   time.Second * 5,
			},
			expectError: true,
		},
		{
			name: "Invalid memory utilization",
			config: GPUConfig{
				MinMemoryGB:          2.0,
				MaxMemoryUtilization: 150.0,
				MonitoringInterval:   time.Second * 5,
			},
			expectError: true,
		},
		{
			name: "Invalid monitoring interval",
			config: GPUConfig{
				MinMemoryGB:          2.0,
				MaxMemoryUtilization: 90.0,
				MonitoringInterval:   time.Millisecond * 500,
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError && err == nil {
				t.Error("Expected validation error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no validation error but got: %v", err)
			}
		})
	}
}

func TestGPUInfoCompatibility(t *testing.T) {
	gpu := &GPUInfo{
		TotalMemory:       8 * 1024 * 1024 * 1024, // 8GB
		Available:         true,
		Type:              GPUTypeCUDA,
		ComputeCapability: ComputeCapability{Major: 7, Minor: 5},
	}

	// Should be compatible with default requirements
	minMemory := int64(2 * 1024 * 1024 * 1024) // 2GB
	minCC := ComputeCapability{Major: 3, Minor: 5}

	if !gpu.IsCompatible(minMemory, minCC) {
		t.Error("GPU should be compatible with requirements")
	}

	// Should not be compatible with higher memory requirement
	highMemory := int64(16 * 1024 * 1024 * 1024) // 16GB
	if gpu.IsCompatible(highMemory, minCC) {
		t.Error("GPU should not be compatible with high memory requirement")
	}

	// Should not be compatible if not available
	gpu.Available = false
	if gpu.IsCompatible(minMemory, minCC) {
		t.Error("Unavailable GPU should not be compatible")
	}
}

func TestGPUInfoMemoryUtilization(t *testing.T) {
	gpu := &GPUInfo{
		TotalMemory: 8 * 1024 * 1024 * 1024, // 8GB
		FreeMemory:  2 * 1024 * 1024 * 1024, // 2GB free
	}

	utilization := gpu.MemoryUtilization()
	expected := 75.0 // (8-2)/8 * 100 = 75%

	if utilization != expected {
		t.Errorf("Expected memory utilization %.1f%%, got %.1f%%", expected, utilization)
	}

	// Test zero memory case
	gpu.TotalMemory = 0
	utilization = gpu.MemoryUtilization()
	if utilization != 0 {
		t.Errorf("Expected 0%% utilization for zero memory, got %.1f%%", utilization)
	}
}

func TestComputeCapabilityString(t *testing.T) {
	cc := ComputeCapability{Major: 7, Minor: 5}
	expected := "7.5"

	if cc.String() != expected {
		t.Errorf("Expected compute capability string %s, got %s", expected, cc.String())
	}
}

func TestCPUDetector(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	detector := NewCPUDetector(logger)

	// Should always be supported
	if !detector.IsSupported() {
		t.Error("CPU detector should always be supported")
	}

	// Should initialize successfully
	if err := detector.Initialize(); err != nil {
		t.Errorf("CPU detector initialization failed: %v", err)
	}

	// Should detect at least one CPU "GPU"
	gpus, err := detector.Detect()
	if err != nil {
		t.Errorf("CPU detection failed: %v", err)
	}

	if len(gpus) != 1 {
		t.Errorf("Expected 1 CPU device, got %d", len(gpus))
	}

	if len(gpus) > 0 {
		gpu := gpus[0]
		if gpu.Type != GPUTypeCPU {
			t.Errorf("Expected GPU type %s, got %s", GPUTypeCPU, gpu.Type)
		}

		if !gpu.Available {
			t.Error("CPU should be available")
		}

		if gpu.TotalMemory <= 0 {
			t.Error("CPU should have positive total memory estimate")
		}
	}

	// Should get info for device 0
	info, err := detector.GetInfo(0)
	if err != nil {
		t.Errorf("Failed to get CPU info: %v", err)
	}

	if info == nil {
		t.Error("CPU info should not be nil")
	}

	// Should get metrics for device 0
	metrics, err := detector.GetMetrics(0)
	if err != nil {
		t.Errorf("Failed to get CPU metrics: %v", err)
	}

	if metrics == nil {
		t.Error("CPU metrics should not be nil")
	}

	// Should fail for invalid device ID
	_, err = detector.GetInfo(1)
	if err == nil {
		t.Error("Should fail for invalid device ID")
	}

	// Should cleanup successfully
	if err := detector.Cleanup(); err != nil {
		t.Errorf("CPU detector cleanup failed: %v", err)
	}
}

func TestGPUManager(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := DefaultGPUConfig()

	manager := NewManager(config, logger)
	if manager == nil {
		t.Fatal("Failed to create GPU manager")
	}

	// Should return configuration
	retrievedConfig := manager.GetConfiguration()
	if retrievedConfig.MinMemoryGB != config.MinMemoryGB {
		t.Error("Retrieved config should match original")
	}

	// Should get available GPUs (at least CPU fallback)
	gpus, err := manager.GetAvailableGPUs()
	if err != nil {
		t.Errorf("Failed to get available GPUs: %v", err)
	}

	if len(gpus) == 0 {
		t.Error("Should have at least CPU fallback GPU")
	}

	// Should get GPU types
	types := manager.GetGPUTypes()
	if len(types) == 0 {
		t.Error("Should have at least one GPU type")
	}

	// CPU type should be available
	foundCPU := false
	for _, gpuType := range types {
		if gpuType == GPUTypeCPU {
			foundCPU = true
			break
		}
	}
	if !foundCPU {
		t.Error("CPU type should be available")
	}

	// Should be able to update configuration
	newConfig := config
	newConfig.MinMemoryGB = 4.0
	if err := manager.UpdateConfiguration(newConfig); err != nil {
		t.Errorf("Failed to update configuration: %v", err)
	}

	// Should cleanup successfully
	if err := manager.Cleanup(); err != nil {
		t.Errorf("Manager cleanup failed: %v", err)
	}
}

func TestGPUManagerSelection(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	config := DefaultGPUConfig()

	// Modify config to not prefer CUDA since we only have CPU fallback in tests
	config.PreferCUDA = false

	manager := NewManager(config, logger)

	// Should select best GPU (CPU fallback)
	gpu, err := manager.SelectBestGPU(config)
	if err != nil {
		t.Errorf("Failed to select best GPU: %v", err)
	}

	if gpu == nil {
		t.Error("Selected GPU should not be nil")
	}

	if gpu != nil && gpu.Type != GPUTypeCPU {
		t.Errorf("Expected CPU type, got %s", gpu.Type)
	}

	// Should fail when GPU is disabled
	disabledConfig := config
	disabledConfig.Enabled = false
	_, err = manager.SelectBestGPU(disabledConfig)
	if err == nil {
		t.Error("Should fail when GPU is disabled")
	}

	// Should fail with impossible requirements
	impossibleConfig := config
	impossibleConfig.MinMemoryGB = 1000.0 // 1TB
	_, err = manager.SelectBestGPU(impossibleConfig)
	if err == nil {
		t.Error("Should fail with impossible memory requirements")
	}
}

func TestGPUError(t *testing.T) {
	err := NewGPUError(ErrorTypeDetection, 123, "test error", 0)

	expectedMsg := "GPU error on device 0: test error (code: 123)"
	if err.Error() != expectedMsg {
		t.Errorf("Expected error message %s, got %s", expectedMsg, err.Error())
	}

	// Test error without device ID
	err2 := NewGPUError(ErrorTypeInitialization, 456, "init error", -1)
	expectedMsg2 := "GPU error: init error (code: 456)"
	if err2.Error() != expectedMsg2 {
		t.Errorf("Expected error message %s, got %s", expectedMsg2, err2.Error())
	}
}

// Benchmark GPU detection performance
func BenchmarkGPUDetection(b *testing.B) {
	logger := log.New(os.Stdout, "[BENCH] ", log.LstdFlags)
	config := DefaultGPUConfig()

	for i := 0; i < b.N; i++ {
		manager := NewManager(config, logger)
		_, err := manager.GetAvailableGPUs()
		if err != nil {
			b.Errorf("GPU detection failed: %v", err)
		}
		manager.Cleanup()
	}
}

// Benchmark GPU manager creation
func BenchmarkGPUManagerCreation(b *testing.B) {
	logger := log.New(os.Stdout, "[BENCH] ", log.LstdFlags)
	config := DefaultGPUConfig()

	for i := 0; i < b.N; i++ {
		manager := NewManager(config, logger)
		manager.Cleanup()
	}
}
