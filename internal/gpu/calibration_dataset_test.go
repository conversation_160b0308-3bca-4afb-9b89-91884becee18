package gpu

import (
	"testing"
)

func TestCalibrationDataset_NewCalibrationDataset(t *testing.T) {
	config := DefaultCalibrationConfig()
	dataset := NewCalibrationDataset(config)

	if dataset == nil {
		t.Fatal("NewCalibrationDataset returned nil")
	}

	if dataset.GetSampleCount() != 0 {
		t.<PERSON><PERSON><PERSON>("Expected 0 samples initially, got %d", dataset.GetSampleCount())
	}

	if dataset.Config.MaxSampleSize != config.MaxSampleSize {
		t.Errorf("Expected MaxSampleSize %d, got %d", config.MaxSampleSize, dataset.Config.MaxSampleSize)
	}
}

func TestCalibrationDataset_AddCalibrationSample(t *testing.T) {
	config := DefaultCalibrationConfig()
	dataset := NewCalibrationDataset(config)

	// Create test tensor
	shape := TensorShape{2, 3}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Add sample
	err = dataset.AddCalibrationSample(tensor, 1.0)
	if err != nil {
		t.Fatalf("Failed to add calibration sample: %v", err)
	}

	if dataset.GetSampleCount() != 1 {
		t.Errorf("Expected 1 sample, got %d", dataset.GetSampleCount())
	}

	// Test nil tensor
	err = dataset.AddCalibrationSample(nil, 1.0)
	if err == nil {
		t.Error("Expected error for nil tensor")
	}
}

func TestQuantizationEngine_CalibrationIntegration(t *testing.T) {
	config := QuantizationConfig{
		Method:            QuantizationSymmetric,
		TargetType:        TensorInt8,
		UseCalibration:    true,
		CalibrationConfig: DefaultCalibrationConfig(),
	}

	engine := NewQuantizationEngine(config)

	// Add calibration data
	for i := 0; i < 5; i++ {
		shape := TensorShape{3, 3}
		tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
		if err != nil {
			t.Fatalf("Failed to create calibration tensor %d: %v", i, err)
		}
		defer tensor.Free()

		err = tensor.Fill(float32(i + 1))
		if err != nil {
			t.Fatalf("Failed to fill calibration tensor %d: %v", i, err)
		}

		err = engine.AddCalibrationData(tensor)
		if err != nil {
			t.Fatalf("Failed to add calibration data %d: %v", i, err)
		}
	}

	// Test tensor for quantization
	shape := TensorShape{2, 2}
	testTensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create test tensor: %v", err)
	}
	defer testTensor.Free()

	err = testTensor.Fill(float32(3.0))
	if err != nil {
		t.Fatalf("Failed to fill test tensor: %v", err)
	}

	// Test calibration methods
	params, err := engine.PerformCalibration(testTensor)
	if err != nil {
		t.Fatalf("Failed to perform calibration: %v", err)
	}

	if params.Scale <= 0 {
		t.Error("Scale should be positive")
	}

	// Test dataset access
	dataset := engine.GetCalibrationDataset()
	if dataset == nil {
		t.Error("Calibration dataset should not be nil")
	}

	if dataset.GetSampleCount() != 5 {
		t.Errorf("Expected 5 calibration samples, got %d", dataset.GetSampleCount())
	}
}

func TestDefaultCalibrationConfig(t *testing.T) {
	config := DefaultCalibrationConfig()

	if config.MaxSampleSize <= 0 {
		t.Error("MaxSampleSize should be positive")
	}

	if config.MinSampleSize <= 0 {
		t.Error("MinSampleSize should be positive")
	}

	if config.MinSampleSize > config.MaxSampleSize {
		t.Error("MinSampleSize should not exceed MaxSampleSize")
	}

	if config.OutlierThreshold <= 0 {
		t.Error("OutlierThreshold should be positive")
	}

	if config.DiversityWeight < 0 || config.DiversityWeight > 1 {
		t.Error("DiversityWeight should be between 0 and 1")
	}
}
