// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: cluster.proto

package clusterpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TaskType int32

const (
	TaskType_TASK_TYPE_UNSPECIFIED        TaskType = 0
	TaskType_TASK_TYPE_INFERENCE          TaskType = 1
	TaskType_TASK_TYPE_TRAINING           TaskType = 2
	TaskType_TASK_TYPE_DATA_PROCESSING    TaskType = 3
	TaskType_TASK_TYPE_MODEL_OPTIMIZATION TaskType = 4
)

// Enum value maps for TaskType.
var (
	TaskType_name = map[int32]string{
		0: "TASK_TYPE_UNSPECIFIED",
		1: "TASK_TYPE_INFERENCE",
		2: "TASK_TYPE_TRAINING",
		3: "TASK_TYPE_DATA_PROCESSING",
		4: "TASK_TYPE_MODEL_OPTIMIZATION",
	}
	TaskType_value = map[string]int32{
		"TASK_TYPE_UNSPECIFIED":        0,
		"TASK_TYPE_INFERENCE":          1,
		"TASK_TYPE_TRAINING":           2,
		"TASK_TYPE_DATA_PROCESSING":    3,
		"TASK_TYPE_MODEL_OPTIMIZATION": 4,
	}
)

func (x TaskType) Enum() *TaskType {
	p := new(TaskType)
	*p = x
	return p
}

func (x TaskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskType) Descriptor() protoreflect.EnumDescriptor {
	return file_cluster_proto_enumTypes[0].Descriptor()
}

func (TaskType) Type() protoreflect.EnumType {
	return &file_cluster_proto_enumTypes[0]
}

func (x TaskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskType.Descriptor instead.
func (TaskType) EnumDescriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{0}
}

type TaskPriority int32

const (
	TaskPriority_PRIORITY_UNSPECIFIED TaskPriority = 0
	TaskPriority_PRIORITY_LOW         TaskPriority = 1
	TaskPriority_PRIORITY_MEDIUM      TaskPriority = 2
	TaskPriority_PRIORITY_HIGH        TaskPriority = 3
	TaskPriority_PRIORITY_CRITICAL    TaskPriority = 4
)

// Enum value maps for TaskPriority.
var (
	TaskPriority_name = map[int32]string{
		0: "PRIORITY_UNSPECIFIED",
		1: "PRIORITY_LOW",
		2: "PRIORITY_MEDIUM",
		3: "PRIORITY_HIGH",
		4: "PRIORITY_CRITICAL",
	}
	TaskPriority_value = map[string]int32{
		"PRIORITY_UNSPECIFIED": 0,
		"PRIORITY_LOW":         1,
		"PRIORITY_MEDIUM":      2,
		"PRIORITY_HIGH":        3,
		"PRIORITY_CRITICAL":    4,
	}
)

func (x TaskPriority) Enum() *TaskPriority {
	p := new(TaskPriority)
	*p = x
	return p
}

func (x TaskPriority) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskPriority) Descriptor() protoreflect.EnumDescriptor {
	return file_cluster_proto_enumTypes[1].Descriptor()
}

func (TaskPriority) Type() protoreflect.EnumType {
	return &file_cluster_proto_enumTypes[1]
}

func (x TaskPriority) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskPriority.Descriptor instead.
func (TaskPriority) EnumDescriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{1}
}

type TaskStatus int32

const (
	TaskStatus_STATUS_UNSPECIFIED TaskStatus = 0
	TaskStatus_STATUS_PENDING     TaskStatus = 1
	TaskStatus_STATUS_ASSIGNED    TaskStatus = 2
	TaskStatus_STATUS_RUNNING     TaskStatus = 3
	TaskStatus_STATUS_COMPLETED   TaskStatus = 4
	TaskStatus_STATUS_FAILED      TaskStatus = 5
	TaskStatus_STATUS_CANCELLED   TaskStatus = 6
	TaskStatus_STATUS_MIGRATING   TaskStatus = 7
)

// Enum value maps for TaskStatus.
var (
	TaskStatus_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "STATUS_PENDING",
		2: "STATUS_ASSIGNED",
		3: "STATUS_RUNNING",
		4: "STATUS_COMPLETED",
		5: "STATUS_FAILED",
		6: "STATUS_CANCELLED",
		7: "STATUS_MIGRATING",
	}
	TaskStatus_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"STATUS_PENDING":     1,
		"STATUS_ASSIGNED":    2,
		"STATUS_RUNNING":     3,
		"STATUS_COMPLETED":   4,
		"STATUS_FAILED":      5,
		"STATUS_CANCELLED":   6,
		"STATUS_MIGRATING":   7,
	}
)

func (x TaskStatus) Enum() *TaskStatus {
	p := new(TaskStatus)
	*p = x
	return p
}

func (x TaskStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_cluster_proto_enumTypes[2].Descriptor()
}

func (TaskStatus) Type() protoreflect.EnumType {
	return &file_cluster_proto_enumTypes[2]
}

func (x TaskStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskStatus.Descriptor instead.
func (TaskStatus) EnumDescriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{2}
}

type GPUStatus int32

const (
	GPUStatus_GPU_STATUS_UNSPECIFIED GPUStatus = 0
	GPUStatus_GPU_STATUS_AVAILABLE   GPUStatus = 1
	GPUStatus_GPU_STATUS_BUSY        GPUStatus = 2
	GPUStatus_GPU_STATUS_ERROR       GPUStatus = 3
	GPUStatus_GPU_STATUS_MAINTENANCE GPUStatus = 4
)

// Enum value maps for GPUStatus.
var (
	GPUStatus_name = map[int32]string{
		0: "GPU_STATUS_UNSPECIFIED",
		1: "GPU_STATUS_AVAILABLE",
		2: "GPU_STATUS_BUSY",
		3: "GPU_STATUS_ERROR",
		4: "GPU_STATUS_MAINTENANCE",
	}
	GPUStatus_value = map[string]int32{
		"GPU_STATUS_UNSPECIFIED": 0,
		"GPU_STATUS_AVAILABLE":   1,
		"GPU_STATUS_BUSY":        2,
		"GPU_STATUS_ERROR":       3,
		"GPU_STATUS_MAINTENANCE": 4,
	}
)

func (x GPUStatus) Enum() *GPUStatus {
	p := new(GPUStatus)
	*p = x
	return p
}

func (x GPUStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GPUStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_cluster_proto_enumTypes[3].Descriptor()
}

func (GPUStatus) Type() protoreflect.EnumType {
	return &file_cluster_proto_enumTypes[3]
}

func (x GPUStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GPUStatus.Descriptor instead.
func (GPUStatus) EnumDescriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{3}
}

type NodeHealth int32

const (
	NodeHealth_HEALTH_UNSPECIFIED NodeHealth = 0
	NodeHealth_HEALTH_HEALTHY     NodeHealth = 1
	NodeHealth_HEALTH_DEGRADED    NodeHealth = 2
	NodeHealth_HEALTH_UNHEALTHY   NodeHealth = 3
	NodeHealth_HEALTH_OFFLINE     NodeHealth = 4
)

// Enum value maps for NodeHealth.
var (
	NodeHealth_name = map[int32]string{
		0: "HEALTH_UNSPECIFIED",
		1: "HEALTH_HEALTHY",
		2: "HEALTH_DEGRADED",
		3: "HEALTH_UNHEALTHY",
		4: "HEALTH_OFFLINE",
	}
	NodeHealth_value = map[string]int32{
		"HEALTH_UNSPECIFIED": 0,
		"HEALTH_HEALTHY":     1,
		"HEALTH_DEGRADED":    2,
		"HEALTH_UNHEALTHY":   3,
		"HEALTH_OFFLINE":     4,
	}
)

func (x NodeHealth) Enum() *NodeHealth {
	p := new(NodeHealth)
	*p = x
	return p
}

func (x NodeHealth) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NodeHealth) Descriptor() protoreflect.EnumDescriptor {
	return file_cluster_proto_enumTypes[4].Descriptor()
}

func (NodeHealth) Type() protoreflect.EnumType {
	return &file_cluster_proto_enumTypes[4]
}

func (x NodeHealth) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NodeHealth.Descriptor instead.
func (NodeHealth) EnumDescriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{4}
}

type AggregationType int32

const (
	AggregationType_AGGREGATION_TYPE_UNSPECIFIED AggregationType = 0
	AggregationType_AGGREGATION_TYPE_SUM         AggregationType = 1
	AggregationType_AGGREGATION_TYPE_AVERAGE     AggregationType = 2
	AggregationType_AGGREGATION_TYPE_CONCAT      AggregationType = 3
	AggregationType_AGGREGATION_TYPE_MERGE       AggregationType = 4
	AggregationType_AGGREGATION_TYPE_CUSTOM      AggregationType = 5
)

// Enum value maps for AggregationType.
var (
	AggregationType_name = map[int32]string{
		0: "AGGREGATION_TYPE_UNSPECIFIED",
		1: "AGGREGATION_TYPE_SUM",
		2: "AGGREGATION_TYPE_AVERAGE",
		3: "AGGREGATION_TYPE_CONCAT",
		4: "AGGREGATION_TYPE_MERGE",
		5: "AGGREGATION_TYPE_CUSTOM",
	}
	AggregationType_value = map[string]int32{
		"AGGREGATION_TYPE_UNSPECIFIED": 0,
		"AGGREGATION_TYPE_SUM":         1,
		"AGGREGATION_TYPE_AVERAGE":     2,
		"AGGREGATION_TYPE_CONCAT":      3,
		"AGGREGATION_TYPE_MERGE":       4,
		"AGGREGATION_TYPE_CUSTOM":      5,
	}
)

func (x AggregationType) Enum() *AggregationType {
	p := new(AggregationType)
	*p = x
	return p
}

func (x AggregationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AggregationType) Descriptor() protoreflect.EnumDescriptor {
	return file_cluster_proto_enumTypes[5].Descriptor()
}

func (AggregationType) Type() protoreflect.EnumType {
	return &file_cluster_proto_enumTypes[5]
}

func (x AggregationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AggregationType.Descriptor instead.
func (AggregationType) EnumDescriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{5}
}

type RebalanceReason int32

const (
	RebalanceReason_REBALANCE_REASON_UNSPECIFIED              RebalanceReason = 0
	RebalanceReason_REBALANCE_REASON_LOAD_IMBALANCE           RebalanceReason = 1
	RebalanceReason_REBALANCE_REASON_NODE_FAILURE             RebalanceReason = 2
	RebalanceReason_REBALANCE_REASON_RESOURCE_SHORTAGE        RebalanceReason = 3
	RebalanceReason_REBALANCE_REASON_PERFORMANCE_OPTIMIZATION RebalanceReason = 4
)

// Enum value maps for RebalanceReason.
var (
	RebalanceReason_name = map[int32]string{
		0: "REBALANCE_REASON_UNSPECIFIED",
		1: "REBALANCE_REASON_LOAD_IMBALANCE",
		2: "REBALANCE_REASON_NODE_FAILURE",
		3: "REBALANCE_REASON_RESOURCE_SHORTAGE",
		4: "REBALANCE_REASON_PERFORMANCE_OPTIMIZATION",
	}
	RebalanceReason_value = map[string]int32{
		"REBALANCE_REASON_UNSPECIFIED":              0,
		"REBALANCE_REASON_LOAD_IMBALANCE":           1,
		"REBALANCE_REASON_NODE_FAILURE":             2,
		"REBALANCE_REASON_RESOURCE_SHORTAGE":        3,
		"REBALANCE_REASON_PERFORMANCE_OPTIMIZATION": 4,
	}
)

func (x RebalanceReason) Enum() *RebalanceReason {
	p := new(RebalanceReason)
	*p = x
	return p
}

func (x RebalanceReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RebalanceReason) Descriptor() protoreflect.EnumDescriptor {
	return file_cluster_proto_enumTypes[6].Descriptor()
}

func (RebalanceReason) Type() protoreflect.EnumType {
	return &file_cluster_proto_enumTypes[6]
}

func (x RebalanceReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RebalanceReason.Descriptor instead.
func (RebalanceReason) EnumDescriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{6}
}

type MigrationReason int32

const (
	MigrationReason_MIGRATION_REASON_UNSPECIFIED           MigrationReason = 0
	MigrationReason_MIGRATION_REASON_LOAD_BALANCING        MigrationReason = 1
	MigrationReason_MIGRATION_REASON_NODE_FAILURE          MigrationReason = 2
	MigrationReason_MIGRATION_REASON_RESOURCE_OPTIMIZATION MigrationReason = 3
	MigrationReason_MIGRATION_REASON_MAINTENANCE           MigrationReason = 4
)

// Enum value maps for MigrationReason.
var (
	MigrationReason_name = map[int32]string{
		0: "MIGRATION_REASON_UNSPECIFIED",
		1: "MIGRATION_REASON_LOAD_BALANCING",
		2: "MIGRATION_REASON_NODE_FAILURE",
		3: "MIGRATION_REASON_RESOURCE_OPTIMIZATION",
		4: "MIGRATION_REASON_MAINTENANCE",
	}
	MigrationReason_value = map[string]int32{
		"MIGRATION_REASON_UNSPECIFIED":           0,
		"MIGRATION_REASON_LOAD_BALANCING":        1,
		"MIGRATION_REASON_NODE_FAILURE":          2,
		"MIGRATION_REASON_RESOURCE_OPTIMIZATION": 3,
		"MIGRATION_REASON_MAINTENANCE":           4,
	}
)

func (x MigrationReason) Enum() *MigrationReason {
	p := new(MigrationReason)
	*p = x
	return p
}

func (x MigrationReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MigrationReason) Descriptor() protoreflect.EnumDescriptor {
	return file_cluster_proto_enumTypes[7].Descriptor()
}

func (MigrationReason) Type() protoreflect.EnumType {
	return &file_cluster_proto_enumTypes[7]
}

func (x MigrationReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MigrationReason.Descriptor instead.
func (MigrationReason) EnumDescriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{7}
}

// Task Assignment
type TaskAssignmentRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TaskId         string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	TaskSpec       *TaskSpec              `protobuf:"bytes,2,opt,name=task_spec,json=taskSpec,proto3" json:"task_spec,omitempty"`
	PreferredNodes []string               `protobuf:"bytes,3,rep,name=preferred_nodes,json=preferredNodes,proto3" json:"preferred_nodes,omitempty"`
	ExcludedNodes  []string               `protobuf:"bytes,4,rep,name=excluded_nodes,json=excludedNodes,proto3" json:"excluded_nodes,omitempty"`
	Priority       TaskPriority           `protobuf:"varint,5,opt,name=priority,proto3,enum=gpu.cluster.TaskPriority" json:"priority,omitempty"`
	Deadline       *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=deadline,proto3" json:"deadline,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *TaskAssignmentRequest) Reset() {
	*x = TaskAssignmentRequest{}
	mi := &file_cluster_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskAssignmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskAssignmentRequest) ProtoMessage() {}

func (x *TaskAssignmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskAssignmentRequest.ProtoReflect.Descriptor instead.
func (*TaskAssignmentRequest) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{0}
}

func (x *TaskAssignmentRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskAssignmentRequest) GetTaskSpec() *TaskSpec {
	if x != nil {
		return x.TaskSpec
	}
	return nil
}

func (x *TaskAssignmentRequest) GetPreferredNodes() []string {
	if x != nil {
		return x.PreferredNodes
	}
	return nil
}

func (x *TaskAssignmentRequest) GetExcludedNodes() []string {
	if x != nil {
		return x.ExcludedNodes
	}
	return nil
}

func (x *TaskAssignmentRequest) GetPriority() TaskPriority {
	if x != nil {
		return x.Priority
	}
	return TaskPriority_PRIORITY_UNSPECIFIED
}

func (x *TaskAssignmentRequest) GetDeadline() *timestamppb.Timestamp {
	if x != nil {
		return x.Deadline
	}
	return nil
}

type TaskAssignmentResponse struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Success            bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	AssignedNodeId     string                 `protobuf:"bytes,2,opt,name=assigned_node_id,json=assignedNodeId,proto3" json:"assigned_node_id,omitempty"`
	Message            string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	EstimatedStartTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=estimated_start_time,json=estimatedStartTime,proto3" json:"estimated_start_time,omitempty"`
	EstimatedDuration  *durationpb.Duration   `protobuf:"bytes,5,opt,name=estimated_duration,json=estimatedDuration,proto3" json:"estimated_duration,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *TaskAssignmentResponse) Reset() {
	*x = TaskAssignmentResponse{}
	mi := &file_cluster_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskAssignmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskAssignmentResponse) ProtoMessage() {}

func (x *TaskAssignmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskAssignmentResponse.ProtoReflect.Descriptor instead.
func (*TaskAssignmentResponse) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{1}
}

func (x *TaskAssignmentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *TaskAssignmentResponse) GetAssignedNodeId() string {
	if x != nil {
		return x.AssignedNodeId
	}
	return ""
}

func (x *TaskAssignmentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TaskAssignmentResponse) GetEstimatedStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EstimatedStartTime
	}
	return nil
}

func (x *TaskAssignmentResponse) GetEstimatedDuration() *durationpb.Duration {
	if x != nil {
		return x.EstimatedDuration
	}
	return nil
}

type TaskSpec struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Id                   string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description          string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Type                 TaskType               `protobuf:"varint,4,opt,name=type,proto3,enum=gpu.cluster.TaskType" json:"type,omitempty"`
	ResourceRequirements *ResourceRequirements  `protobuf:"bytes,5,opt,name=resource_requirements,json=resourceRequirements,proto3" json:"resource_requirements,omitempty"`
	Parameters           map[string]string      `protobuf:"bytes,6,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	InputDataUrls        []string               `protobuf:"bytes,7,rep,name=input_data_urls,json=inputDataUrls,proto3" json:"input_data_urls,omitempty"`
	OutputLocation       string                 `protobuf:"bytes,8,opt,name=output_location,json=outputLocation,proto3" json:"output_location,omitempty"`
	Dependencies         []string               `protobuf:"bytes,9,rep,name=dependencies,proto3" json:"dependencies,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *TaskSpec) Reset() {
	*x = TaskSpec{}
	mi := &file_cluster_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskSpec) ProtoMessage() {}

func (x *TaskSpec) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskSpec.ProtoReflect.Descriptor instead.
func (*TaskSpec) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{2}
}

func (x *TaskSpec) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TaskSpec) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TaskSpec) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TaskSpec) GetType() TaskType {
	if x != nil {
		return x.Type
	}
	return TaskType_TASK_TYPE_UNSPECIFIED
}

func (x *TaskSpec) GetResourceRequirements() *ResourceRequirements {
	if x != nil {
		return x.ResourceRequirements
	}
	return nil
}

func (x *TaskSpec) GetParameters() map[string]string {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *TaskSpec) GetInputDataUrls() []string {
	if x != nil {
		return x.InputDataUrls
	}
	return nil
}

func (x *TaskSpec) GetOutputLocation() string {
	if x != nil {
		return x.OutputLocation
	}
	return ""
}

func (x *TaskSpec) GetDependencies() []string {
	if x != nil {
		return x.Dependencies
	}
	return nil
}

type ResourceRequirements struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	GpuMemoryMb             int64                  `protobuf:"varint,1,opt,name=gpu_memory_mb,json=gpuMemoryMb,proto3" json:"gpu_memory_mb,omitempty"`
	SystemMemoryMb          int64                  `protobuf:"varint,2,opt,name=system_memory_mb,json=systemMemoryMb,proto3" json:"system_memory_mb,omitempty"`
	GpuComputeUnits         int32                  `protobuf:"varint,3,opt,name=gpu_compute_units,json=gpuComputeUnits,proto3" json:"gpu_compute_units,omitempty"`
	MinGpuUtilization       float32                `protobuf:"fixed32,4,opt,name=min_gpu_utilization,json=minGpuUtilization,proto3" json:"min_gpu_utilization,omitempty"`
	RequiredGpuCapabilities []string               `protobuf:"bytes,5,rep,name=required_gpu_capabilities,json=requiredGpuCapabilities,proto3" json:"required_gpu_capabilities,omitempty"`
	NetworkRequirements     *NetworkRequirements   `protobuf:"bytes,6,opt,name=network_requirements,json=networkRequirements,proto3" json:"network_requirements,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *ResourceRequirements) Reset() {
	*x = ResourceRequirements{}
	mi := &file_cluster_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceRequirements) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceRequirements) ProtoMessage() {}

func (x *ResourceRequirements) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceRequirements.ProtoReflect.Descriptor instead.
func (*ResourceRequirements) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{3}
}

func (x *ResourceRequirements) GetGpuMemoryMb() int64 {
	if x != nil {
		return x.GpuMemoryMb
	}
	return 0
}

func (x *ResourceRequirements) GetSystemMemoryMb() int64 {
	if x != nil {
		return x.SystemMemoryMb
	}
	return 0
}

func (x *ResourceRequirements) GetGpuComputeUnits() int32 {
	if x != nil {
		return x.GpuComputeUnits
	}
	return 0
}

func (x *ResourceRequirements) GetMinGpuUtilization() float32 {
	if x != nil {
		return x.MinGpuUtilization
	}
	return 0
}

func (x *ResourceRequirements) GetRequiredGpuCapabilities() []string {
	if x != nil {
		return x.RequiredGpuCapabilities
	}
	return nil
}

func (x *ResourceRequirements) GetNetworkRequirements() *NetworkRequirements {
	if x != nil {
		return x.NetworkRequirements
	}
	return nil
}

type NetworkRequirements struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	MinBandwidthMbps     float32                `protobuf:"fixed32,1,opt,name=min_bandwidth_mbps,json=minBandwidthMbps,proto3" json:"min_bandwidth_mbps,omitempty"`
	MaxLatencyMs         float32                `protobuf:"fixed32,2,opt,name=max_latency_ms,json=maxLatencyMs,proto3" json:"max_latency_ms,omitempty"`
	MaxPacketLossPercent float32                `protobuf:"fixed32,3,opt,name=max_packet_loss_percent,json=maxPacketLossPercent,proto3" json:"max_packet_loss_percent,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *NetworkRequirements) Reset() {
	*x = NetworkRequirements{}
	mi := &file_cluster_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetworkRequirements) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkRequirements) ProtoMessage() {}

func (x *NetworkRequirements) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkRequirements.ProtoReflect.Descriptor instead.
func (*NetworkRequirements) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{4}
}

func (x *NetworkRequirements) GetMinBandwidthMbps() float32 {
	if x != nil {
		return x.MinBandwidthMbps
	}
	return 0
}

func (x *NetworkRequirements) GetMaxLatencyMs() float32 {
	if x != nil {
		return x.MaxLatencyMs
	}
	return 0
}

func (x *NetworkRequirements) GetMaxPacketLossPercent() float32 {
	if x != nil {
		return x.MaxPacketLossPercent
	}
	return 0
}

// Task Status Updates
type TaskStatusUpdate struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TaskId          string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	NodeId          string                 `protobuf:"bytes,2,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	Status          TaskStatus             `protobuf:"varint,3,opt,name=status,proto3,enum=gpu.cluster.TaskStatus" json:"status,omitempty"`
	ProgressPercent float32                `protobuf:"fixed32,4,opt,name=progress_percent,json=progressPercent,proto3" json:"progress_percent,omitempty"`
	Message         string                 `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	Timestamp       *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Metrics         map[string]string      `protobuf:"bytes,7,rep,name=metrics,proto3" json:"metrics,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TaskStatusUpdate) Reset() {
	*x = TaskStatusUpdate{}
	mi := &file_cluster_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskStatusUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskStatusUpdate) ProtoMessage() {}

func (x *TaskStatusUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskStatusUpdate.ProtoReflect.Descriptor instead.
func (*TaskStatusUpdate) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{5}
}

func (x *TaskStatusUpdate) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskStatusUpdate) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *TaskStatusUpdate) GetStatus() TaskStatus {
	if x != nil {
		return x.Status
	}
	return TaskStatus_STATUS_UNSPECIFIED
}

func (x *TaskStatusUpdate) GetProgressPercent() float32 {
	if x != nil {
		return x.ProgressPercent
	}
	return 0
}

func (x *TaskStatusUpdate) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TaskStatusUpdate) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *TaskStatusUpdate) GetMetrics() map[string]string {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type TaskStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskStatusResponse) Reset() {
	*x = TaskStatusResponse{}
	mi := &file_cluster_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskStatusResponse) ProtoMessage() {}

func (x *TaskStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskStatusResponse.ProtoReflect.Descriptor instead.
func (*TaskStatusResponse) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{6}
}

func (x *TaskStatusResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *TaskStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// Task Completion
type TaskCompletionRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TaskId         string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	NodeId         string                 `protobuf:"bytes,2,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	Result         *TaskResult            `protobuf:"bytes,3,opt,name=result,proto3" json:"result,omitempty"`
	CompletionTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=completion_time,json=completionTime,proto3" json:"completion_time,omitempty"`
	Metrics        *TaskMetrics           `protobuf:"bytes,5,opt,name=metrics,proto3" json:"metrics,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *TaskCompletionRequest) Reset() {
	*x = TaskCompletionRequest{}
	mi := &file_cluster_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskCompletionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskCompletionRequest) ProtoMessage() {}

func (x *TaskCompletionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskCompletionRequest.ProtoReflect.Descriptor instead.
func (*TaskCompletionRequest) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{7}
}

func (x *TaskCompletionRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskCompletionRequest) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *TaskCompletionRequest) GetResult() *TaskResult {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *TaskCompletionRequest) GetCompletionTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletionTime
	}
	return nil
}

func (x *TaskCompletionRequest) GetMetrics() *TaskMetrics {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type TaskCompletionResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Success        bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message        string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ResultAccepted bool                   `protobuf:"varint,3,opt,name=result_accepted,json=resultAccepted,proto3" json:"result_accepted,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *TaskCompletionResponse) Reset() {
	*x = TaskCompletionResponse{}
	mi := &file_cluster_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskCompletionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskCompletionResponse) ProtoMessage() {}

func (x *TaskCompletionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskCompletionResponse.ProtoReflect.Descriptor instead.
func (*TaskCompletionResponse) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{8}
}

func (x *TaskCompletionResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *TaskCompletionResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TaskCompletionResponse) GetResultAccepted() bool {
	if x != nil {
		return x.ResultAccepted
	}
	return false
}

type TaskResult struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Success        bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	ErrorMessage   string                 `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	OutputDataUrls []string               `protobuf:"bytes,3,rep,name=output_data_urls,json=outputDataUrls,proto3" json:"output_data_urls,omitempty"`
	ResultMetadata map[string]string      `protobuf:"bytes,4,rep,name=result_metadata,json=resultMetadata,proto3" json:"result_metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	ResultData     []byte                 `protobuf:"bytes,5,opt,name=result_data,json=resultData,proto3" json:"result_data,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *TaskResult) Reset() {
	*x = TaskResult{}
	mi := &file_cluster_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskResult) ProtoMessage() {}

func (x *TaskResult) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskResult.ProtoReflect.Descriptor instead.
func (*TaskResult) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{9}
}

func (x *TaskResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *TaskResult) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *TaskResult) GetOutputDataUrls() []string {
	if x != nil {
		return x.OutputDataUrls
	}
	return nil
}

func (x *TaskResult) GetResultMetadata() map[string]string {
	if x != nil {
		return x.ResultMetadata
	}
	return nil
}

func (x *TaskResult) GetResultData() []byte {
	if x != nil {
		return x.ResultData
	}
	return nil
}

type TaskMetrics struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	ExecutionTime         *durationpb.Duration   `protobuf:"bytes,1,opt,name=execution_time,json=executionTime,proto3" json:"execution_time,omitempty"`
	GpuMemoryUsedMb       int64                  `protobuf:"varint,2,opt,name=gpu_memory_used_mb,json=gpuMemoryUsedMb,proto3" json:"gpu_memory_used_mb,omitempty"`
	SystemMemoryUsedMb    int64                  `protobuf:"varint,3,opt,name=system_memory_used_mb,json=systemMemoryUsedMb,proto3" json:"system_memory_used_mb,omitempty"`
	GpuUtilizationPercent float32                `protobuf:"fixed32,4,opt,name=gpu_utilization_percent,json=gpuUtilizationPercent,proto3" json:"gpu_utilization_percent,omitempty"`
	CpuUtilizationPercent float32                `protobuf:"fixed32,5,opt,name=cpu_utilization_percent,json=cpuUtilizationPercent,proto3" json:"cpu_utilization_percent,omitempty"`
	DataTransferredBytes  int64                  `protobuf:"varint,6,opt,name=data_transferred_bytes,json=dataTransferredBytes,proto3" json:"data_transferred_bytes,omitempty"`
	PowerConsumptionWatts float32                `protobuf:"fixed32,7,opt,name=power_consumption_watts,json=powerConsumptionWatts,proto3" json:"power_consumption_watts,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *TaskMetrics) Reset() {
	*x = TaskMetrics{}
	mi := &file_cluster_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskMetrics) ProtoMessage() {}

func (x *TaskMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskMetrics.ProtoReflect.Descriptor instead.
func (*TaskMetrics) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{10}
}

func (x *TaskMetrics) GetExecutionTime() *durationpb.Duration {
	if x != nil {
		return x.ExecutionTime
	}
	return nil
}

func (x *TaskMetrics) GetGpuMemoryUsedMb() int64 {
	if x != nil {
		return x.GpuMemoryUsedMb
	}
	return 0
}

func (x *TaskMetrics) GetSystemMemoryUsedMb() int64 {
	if x != nil {
		return x.SystemMemoryUsedMb
	}
	return 0
}

func (x *TaskMetrics) GetGpuUtilizationPercent() float32 {
	if x != nil {
		return x.GpuUtilizationPercent
	}
	return 0
}

func (x *TaskMetrics) GetCpuUtilizationPercent() float32 {
	if x != nil {
		return x.CpuUtilizationPercent
	}
	return 0
}

func (x *TaskMetrics) GetDataTransferredBytes() int64 {
	if x != nil {
		return x.DataTransferredBytes
	}
	return 0
}

func (x *TaskMetrics) GetPowerConsumptionWatts() float32 {
	if x != nil {
		return x.PowerConsumptionWatts
	}
	return 0
}

// Task Cancellation
type TaskCancellationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Reason        string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	Force         bool                   `protobuf:"varint,3,opt,name=force,proto3" json:"force,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskCancellationRequest) Reset() {
	*x = TaskCancellationRequest{}
	mi := &file_cluster_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskCancellationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskCancellationRequest) ProtoMessage() {}

func (x *TaskCancellationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskCancellationRequest.ProtoReflect.Descriptor instead.
func (*TaskCancellationRequest) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{11}
}

func (x *TaskCancellationRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskCancellationRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *TaskCancellationRequest) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

type TaskCancellationResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Success        bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message        string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	TaskWasRunning bool                   `protobuf:"varint,3,opt,name=task_was_running,json=taskWasRunning,proto3" json:"task_was_running,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *TaskCancellationResponse) Reset() {
	*x = TaskCancellationResponse{}
	mi := &file_cluster_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskCancellationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskCancellationResponse) ProtoMessage() {}

func (x *TaskCancellationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskCancellationResponse.ProtoReflect.Descriptor instead.
func (*TaskCancellationResponse) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{12}
}

func (x *TaskCancellationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *TaskCancellationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TaskCancellationResponse) GetTaskWasRunning() bool {
	if x != nil {
		return x.TaskWasRunning
	}
	return false
}

// Node Registration
type NodeRegistrationRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	NodeInfo            *NodeInfo              `protobuf:"bytes,1,opt,name=node_info,json=nodeInfo,proto3" json:"node_info,omitempty"`
	GpuDevices          []*GPUDevice           `protobuf:"bytes,2,rep,name=gpu_devices,json=gpuDevices,proto3" json:"gpu_devices,omitempty"`
	AuthenticationToken string                 `protobuf:"bytes,3,opt,name=authentication_token,json=authenticationToken,proto3" json:"authentication_token,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *NodeRegistrationRequest) Reset() {
	*x = NodeRegistrationRequest{}
	mi := &file_cluster_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeRegistrationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeRegistrationRequest) ProtoMessage() {}

func (x *NodeRegistrationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeRegistrationRequest.ProtoReflect.Descriptor instead.
func (*NodeRegistrationRequest) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{13}
}

func (x *NodeRegistrationRequest) GetNodeInfo() *NodeInfo {
	if x != nil {
		return x.NodeInfo
	}
	return nil
}

func (x *NodeRegistrationRequest) GetGpuDevices() []*GPUDevice {
	if x != nil {
		return x.GpuDevices
	}
	return nil
}

func (x *NodeRegistrationRequest) GetAuthenticationToken() string {
	if x != nil {
		return x.AuthenticationToken
	}
	return ""
}

type NodeRegistrationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	NodeId        string                 `protobuf:"bytes,2,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	ClusterConfig *ClusterConfig         `protobuf:"bytes,4,opt,name=cluster_config,json=clusterConfig,proto3" json:"cluster_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeRegistrationResponse) Reset() {
	*x = NodeRegistrationResponse{}
	mi := &file_cluster_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeRegistrationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeRegistrationResponse) ProtoMessage() {}

func (x *NodeRegistrationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeRegistrationResponse.ProtoReflect.Descriptor instead.
func (*NodeRegistrationResponse) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{14}
}

func (x *NodeRegistrationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *NodeRegistrationResponse) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *NodeRegistrationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *NodeRegistrationResponse) GetClusterConfig() *ClusterConfig {
	if x != nil {
		return x.ClusterConfig
	}
	return nil
}

type NodeInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeId        string                 `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	Hostname      string                 `protobuf:"bytes,2,opt,name=hostname,proto3" json:"hostname,omitempty"`
	IpAddress     string                 `protobuf:"bytes,3,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	Port          int32                  `protobuf:"varint,4,opt,name=port,proto3" json:"port,omitempty"`
	SystemInfo    *SystemInfo            `protobuf:"bytes,5,opt,name=system_info,json=systemInfo,proto3" json:"system_info,omitempty"`
	NetworkInfo   *NetworkInfo           `protobuf:"bytes,6,opt,name=network_info,json=networkInfo,proto3" json:"network_info,omitempty"`
	Capabilities  map[string]string      `protobuf:"bytes,7,rep,name=capabilities,proto3" json:"capabilities,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeInfo) Reset() {
	*x = NodeInfo{}
	mi := &file_cluster_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeInfo) ProtoMessage() {}

func (x *NodeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeInfo.ProtoReflect.Descriptor instead.
func (*NodeInfo) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{15}
}

func (x *NodeInfo) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *NodeInfo) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *NodeInfo) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *NodeInfo) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *NodeInfo) GetSystemInfo() *SystemInfo {
	if x != nil {
		return x.SystemInfo
	}
	return nil
}

func (x *NodeInfo) GetNetworkInfo() *NetworkInfo {
	if x != nil {
		return x.NetworkInfo
	}
	return nil
}

func (x *NodeInfo) GetCapabilities() map[string]string {
	if x != nil {
		return x.Capabilities
	}
	return nil
}

type SystemInfo struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Os                string                 `protobuf:"bytes,1,opt,name=os,proto3" json:"os,omitempty"`
	Architecture      string                 `protobuf:"bytes,2,opt,name=architecture,proto3" json:"architecture,omitempty"`
	CpuCores          int32                  `protobuf:"varint,3,opt,name=cpu_cores,json=cpuCores,proto3" json:"cpu_cores,omitempty"`
	TotalMemoryMb     int64                  `protobuf:"varint,4,opt,name=total_memory_mb,json=totalMemoryMb,proto3" json:"total_memory_mb,omitempty"`
	AvailableMemoryMb int64                  `protobuf:"varint,5,opt,name=available_memory_mb,json=availableMemoryMb,proto3" json:"available_memory_mb,omitempty"`
	LoadAverage       float32                `protobuf:"fixed32,6,opt,name=load_average,json=loadAverage,proto3" json:"load_average,omitempty"`
	Uptime            *durationpb.Duration   `protobuf:"bytes,7,opt,name=uptime,proto3" json:"uptime,omitempty"`
	DriverVersion     string                 `protobuf:"bytes,8,opt,name=driver_version,json=driverVersion,proto3" json:"driver_version,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SystemInfo) Reset() {
	*x = SystemInfo{}
	mi := &file_cluster_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SystemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemInfo) ProtoMessage() {}

func (x *SystemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemInfo.ProtoReflect.Descriptor instead.
func (*SystemInfo) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{16}
}

func (x *SystemInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *SystemInfo) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *SystemInfo) GetCpuCores() int32 {
	if x != nil {
		return x.CpuCores
	}
	return 0
}

func (x *SystemInfo) GetTotalMemoryMb() int64 {
	if x != nil {
		return x.TotalMemoryMb
	}
	return 0
}

func (x *SystemInfo) GetAvailableMemoryMb() int64 {
	if x != nil {
		return x.AvailableMemoryMb
	}
	return 0
}

func (x *SystemInfo) GetLoadAverage() float32 {
	if x != nil {
		return x.LoadAverage
	}
	return 0
}

func (x *SystemInfo) GetUptime() *durationpb.Duration {
	if x != nil {
		return x.Uptime
	}
	return nil
}

func (x *SystemInfo) GetDriverVersion() string {
	if x != nil {
		return x.DriverVersion
	}
	return ""
}

type NetworkInfo struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	BandwidthMbps     float32                `protobuf:"fixed32,1,opt,name=bandwidth_mbps,json=bandwidthMbps,proto3" json:"bandwidth_mbps,omitempty"`
	LatencyMs         float32                `protobuf:"fixed32,2,opt,name=latency_ms,json=latencyMs,proto3" json:"latency_ms,omitempty"`
	PacketLossPercent float32                `protobuf:"fixed32,3,opt,name=packet_loss_percent,json=packetLossPercent,proto3" json:"packet_loss_percent,omitempty"`
	Interfaces        []string               `protobuf:"bytes,4,rep,name=interfaces,proto3" json:"interfaces,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *NetworkInfo) Reset() {
	*x = NetworkInfo{}
	mi := &file_cluster_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetworkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkInfo) ProtoMessage() {}

func (x *NetworkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkInfo.ProtoReflect.Descriptor instead.
func (*NetworkInfo) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{17}
}

func (x *NetworkInfo) GetBandwidthMbps() float32 {
	if x != nil {
		return x.BandwidthMbps
	}
	return 0
}

func (x *NetworkInfo) GetLatencyMs() float32 {
	if x != nil {
		return x.LatencyMs
	}
	return 0
}

func (x *NetworkInfo) GetPacketLossPercent() float32 {
	if x != nil {
		return x.PacketLossPercent
	}
	return 0
}

func (x *NetworkInfo) GetInterfaces() []string {
	if x != nil {
		return x.Interfaces
	}
	return nil
}

type GPUDevice struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	DeviceId           string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Name               string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Vendor             string                 `protobuf:"bytes,3,opt,name=vendor,proto3" json:"vendor,omitempty"`
	Model              string                 `protobuf:"bytes,4,opt,name=model,proto3" json:"model,omitempty"`
	MemoryMb           int64                  `protobuf:"varint,5,opt,name=memory_mb,json=memoryMb,proto3" json:"memory_mb,omitempty"`
	AvailableMemoryMb  int64                  `protobuf:"varint,6,opt,name=available_memory_mb,json=availableMemoryMb,proto3" json:"available_memory_mb,omitempty"`
	ComputeUnits       int32                  `protobuf:"varint,7,opt,name=compute_units,json=computeUnits,proto3" json:"compute_units,omitempty"`
	UtilizationPercent float32                `protobuf:"fixed32,8,opt,name=utilization_percent,json=utilizationPercent,proto3" json:"utilization_percent,omitempty"`
	TemperatureCelsius float32                `protobuf:"fixed32,9,opt,name=temperature_celsius,json=temperatureCelsius,proto3" json:"temperature_celsius,omitempty"`
	PowerUsageWatts    float32                `protobuf:"fixed32,10,opt,name=power_usage_watts,json=powerUsageWatts,proto3" json:"power_usage_watts,omitempty"`
	Capabilities       []string               `protobuf:"bytes,11,rep,name=capabilities,proto3" json:"capabilities,omitempty"`
	Status             GPUStatus              `protobuf:"varint,12,opt,name=status,proto3,enum=gpu.cluster.GPUStatus" json:"status,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GPUDevice) Reset() {
	*x = GPUDevice{}
	mi := &file_cluster_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GPUDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GPUDevice) ProtoMessage() {}

func (x *GPUDevice) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GPUDevice.ProtoReflect.Descriptor instead.
func (*GPUDevice) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{18}
}

func (x *GPUDevice) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *GPUDevice) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GPUDevice) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *GPUDevice) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *GPUDevice) GetMemoryMb() int64 {
	if x != nil {
		return x.MemoryMb
	}
	return 0
}

func (x *GPUDevice) GetAvailableMemoryMb() int64 {
	if x != nil {
		return x.AvailableMemoryMb
	}
	return 0
}

func (x *GPUDevice) GetComputeUnits() int32 {
	if x != nil {
		return x.ComputeUnits
	}
	return 0
}

func (x *GPUDevice) GetUtilizationPercent() float32 {
	if x != nil {
		return x.UtilizationPercent
	}
	return 0
}

func (x *GPUDevice) GetTemperatureCelsius() float32 {
	if x != nil {
		return x.TemperatureCelsius
	}
	return 0
}

func (x *GPUDevice) GetPowerUsageWatts() float32 {
	if x != nil {
		return x.PowerUsageWatts
	}
	return 0
}

func (x *GPUDevice) GetCapabilities() []string {
	if x != nil {
		return x.Capabilities
	}
	return nil
}

func (x *GPUDevice) GetStatus() GPUStatus {
	if x != nil {
		return x.Status
	}
	return GPUStatus_GPU_STATUS_UNSPECIFIED
}

// Node Status Updates
type NodeStatusUpdate struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	NodeId          string                 `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	SystemInfo      *SystemInfo            `protobuf:"bytes,2,opt,name=system_info,json=systemInfo,proto3" json:"system_info,omitempty"`
	GpuDevices      []*GPUDevice           `protobuf:"bytes,3,rep,name=gpu_devices,json=gpuDevices,proto3" json:"gpu_devices,omitempty"`
	WorkloadMetrics *WorkloadMetrics       `protobuf:"bytes,4,opt,name=workload_metrics,json=workloadMetrics,proto3" json:"workload_metrics,omitempty"`
	Timestamp       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *NodeStatusUpdate) Reset() {
	*x = NodeStatusUpdate{}
	mi := &file_cluster_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeStatusUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeStatusUpdate) ProtoMessage() {}

func (x *NodeStatusUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeStatusUpdate.ProtoReflect.Descriptor instead.
func (*NodeStatusUpdate) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{19}
}

func (x *NodeStatusUpdate) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *NodeStatusUpdate) GetSystemInfo() *SystemInfo {
	if x != nil {
		return x.SystemInfo
	}
	return nil
}

func (x *NodeStatusUpdate) GetGpuDevices() []*GPUDevice {
	if x != nil {
		return x.GpuDevices
	}
	return nil
}

func (x *NodeStatusUpdate) GetWorkloadMetrics() *WorkloadMetrics {
	if x != nil {
		return x.WorkloadMetrics
	}
	return nil
}

func (x *NodeStatusUpdate) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

type NodeStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeStatusResponse) Reset() {
	*x = NodeStatusResponse{}
	mi := &file_cluster_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeStatusResponse) ProtoMessage() {}

func (x *NodeStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeStatusResponse.ProtoReflect.Descriptor instead.
func (*NodeStatusResponse) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{20}
}

func (x *NodeStatusResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *NodeStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type WorkloadMetrics struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	ActiveTasks           int32                  `protobuf:"varint,1,opt,name=active_tasks,json=activeTasks,proto3" json:"active_tasks,omitempty"`
	QueuedTasks           int32                  `protobuf:"varint,2,opt,name=queued_tasks,json=queuedTasks,proto3" json:"queued_tasks,omitempty"`
	CompletedTasks        int32                  `protobuf:"varint,3,opt,name=completed_tasks,json=completedTasks,proto3" json:"completed_tasks,omitempty"`
	FailedTasks           int32                  `protobuf:"varint,4,opt,name=failed_tasks,json=failedTasks,proto3" json:"failed_tasks,omitempty"`
	ThroughputTasksPerSec float32                `protobuf:"fixed32,5,opt,name=throughput_tasks_per_sec,json=throughputTasksPerSec,proto3" json:"throughput_tasks_per_sec,omitempty"`
	AverageTaskTime       *durationpb.Duration   `protobuf:"bytes,6,opt,name=average_task_time,json=averageTaskTime,proto3" json:"average_task_time,omitempty"`
	SuccessRatePercent    float32                `protobuf:"fixed32,7,opt,name=success_rate_percent,json=successRatePercent,proto3" json:"success_rate_percent,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *WorkloadMetrics) Reset() {
	*x = WorkloadMetrics{}
	mi := &file_cluster_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkloadMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkloadMetrics) ProtoMessage() {}

func (x *WorkloadMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkloadMetrics.ProtoReflect.Descriptor instead.
func (*WorkloadMetrics) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{21}
}

func (x *WorkloadMetrics) GetActiveTasks() int32 {
	if x != nil {
		return x.ActiveTasks
	}
	return 0
}

func (x *WorkloadMetrics) GetQueuedTasks() int32 {
	if x != nil {
		return x.QueuedTasks
	}
	return 0
}

func (x *WorkloadMetrics) GetCompletedTasks() int32 {
	if x != nil {
		return x.CompletedTasks
	}
	return 0
}

func (x *WorkloadMetrics) GetFailedTasks() int32 {
	if x != nil {
		return x.FailedTasks
	}
	return 0
}

func (x *WorkloadMetrics) GetThroughputTasksPerSec() float32 {
	if x != nil {
		return x.ThroughputTasksPerSec
	}
	return 0
}

func (x *WorkloadMetrics) GetAverageTaskTime() *durationpb.Duration {
	if x != nil {
		return x.AverageTaskTime
	}
	return nil
}

func (x *WorkloadMetrics) GetSuccessRatePercent() float32 {
	if x != nil {
		return x.SuccessRatePercent
	}
	return 0
}

// Cluster Status
type ClusterStatusRequest struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	IncludeDetailedMetrics bool                   `protobuf:"varint,1,opt,name=include_detailed_metrics,json=includeDetailedMetrics,proto3" json:"include_detailed_metrics,omitempty"`
	NodeFilters            []string               `protobuf:"bytes,2,rep,name=node_filters,json=nodeFilters,proto3" json:"node_filters,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ClusterStatusRequest) Reset() {
	*x = ClusterStatusRequest{}
	mi := &file_cluster_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClusterStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterStatusRequest) ProtoMessage() {}

func (x *ClusterStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterStatusRequest.ProtoReflect.Descriptor instead.
func (*ClusterStatusRequest) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{22}
}

func (x *ClusterStatusRequest) GetIncludeDetailedMetrics() bool {
	if x != nil {
		return x.IncludeDetailedMetrics
	}
	return false
}

func (x *ClusterStatusRequest) GetNodeFilters() []string {
	if x != nil {
		return x.NodeFilters
	}
	return nil
}

type ClusterStatusResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ClusterInfo    *ClusterInfo           `protobuf:"bytes,1,opt,name=cluster_info,json=clusterInfo,proto3" json:"cluster_info,omitempty"`
	NodeStatuses   []*NodeStatus          `protobuf:"bytes,2,rep,name=node_statuses,json=nodeStatuses,proto3" json:"node_statuses,omitempty"`
	ClusterMetrics *ClusterMetrics        `protobuf:"bytes,3,opt,name=cluster_metrics,json=clusterMetrics,proto3" json:"cluster_metrics,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ClusterStatusResponse) Reset() {
	*x = ClusterStatusResponse{}
	mi := &file_cluster_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClusterStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterStatusResponse) ProtoMessage() {}

func (x *ClusterStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterStatusResponse.ProtoReflect.Descriptor instead.
func (*ClusterStatusResponse) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{23}
}

func (x *ClusterStatusResponse) GetClusterInfo() *ClusterInfo {
	if x != nil {
		return x.ClusterInfo
	}
	return nil
}

func (x *ClusterStatusResponse) GetNodeStatuses() []*NodeStatus {
	if x != nil {
		return x.NodeStatuses
	}
	return nil
}

func (x *ClusterStatusResponse) GetClusterMetrics() *ClusterMetrics {
	if x != nil {
		return x.ClusterMetrics
	}
	return nil
}

type ClusterInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClusterId     string                 `protobuf:"bytes,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	TotalNodes    int32                  `protobuf:"varint,3,opt,name=total_nodes,json=totalNodes,proto3" json:"total_nodes,omitempty"`
	ActiveNodes   int32                  `protobuf:"varint,4,opt,name=active_nodes,json=activeNodes,proto3" json:"active_nodes,omitempty"`
	TotalGpus     int32                  `protobuf:"varint,5,opt,name=total_gpus,json=totalGpus,proto3" json:"total_gpus,omitempty"`
	AvailableGpus int32                  `protobuf:"varint,6,opt,name=available_gpus,json=availableGpus,proto3" json:"available_gpus,omitempty"`
	LastUpdated   *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=last_updated,json=lastUpdated,proto3" json:"last_updated,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClusterInfo) Reset() {
	*x = ClusterInfo{}
	mi := &file_cluster_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClusterInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterInfo) ProtoMessage() {}

func (x *ClusterInfo) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterInfo.ProtoReflect.Descriptor instead.
func (*ClusterInfo) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{24}
}

func (x *ClusterInfo) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *ClusterInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ClusterInfo) GetTotalNodes() int32 {
	if x != nil {
		return x.TotalNodes
	}
	return 0
}

func (x *ClusterInfo) GetActiveNodes() int32 {
	if x != nil {
		return x.ActiveNodes
	}
	return 0
}

func (x *ClusterInfo) GetTotalGpus() int32 {
	if x != nil {
		return x.TotalGpus
	}
	return 0
}

func (x *ClusterInfo) GetAvailableGpus() int32 {
	if x != nil {
		return x.AvailableGpus
	}
	return 0
}

func (x *ClusterInfo) GetLastUpdated() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdated
	}
	return nil
}

type NodeStatus struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	NodeId          string                 `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	Health          NodeHealth             `protobuf:"varint,2,opt,name=health,proto3,enum=gpu.cluster.NodeHealth" json:"health,omitempty"`
	SystemInfo      *SystemInfo            `protobuf:"bytes,3,opt,name=system_info,json=systemInfo,proto3" json:"system_info,omitempty"`
	GpuDevices      []*GPUDevice           `protobuf:"bytes,4,rep,name=gpu_devices,json=gpuDevices,proto3" json:"gpu_devices,omitempty"`
	WorkloadMetrics *WorkloadMetrics       `protobuf:"bytes,5,opt,name=workload_metrics,json=workloadMetrics,proto3" json:"workload_metrics,omitempty"`
	LastHeartbeat   *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=last_heartbeat,json=lastHeartbeat,proto3" json:"last_heartbeat,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *NodeStatus) Reset() {
	*x = NodeStatus{}
	mi := &file_cluster_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeStatus) ProtoMessage() {}

func (x *NodeStatus) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeStatus.ProtoReflect.Descriptor instead.
func (*NodeStatus) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{25}
}

func (x *NodeStatus) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *NodeStatus) GetHealth() NodeHealth {
	if x != nil {
		return x.Health
	}
	return NodeHealth_HEALTH_UNSPECIFIED
}

func (x *NodeStatus) GetSystemInfo() *SystemInfo {
	if x != nil {
		return x.SystemInfo
	}
	return nil
}

func (x *NodeStatus) GetGpuDevices() []*GPUDevice {
	if x != nil {
		return x.GpuDevices
	}
	return nil
}

func (x *NodeStatus) GetWorkloadMetrics() *WorkloadMetrics {
	if x != nil {
		return x.WorkloadMetrics
	}
	return nil
}

func (x *NodeStatus) GetLastHeartbeat() *timestamppb.Timestamp {
	if x != nil {
		return x.LastHeartbeat
	}
	return nil
}

type ClusterMetrics struct {
	state                        protoimpl.MessageState `protogen:"open.v1"`
	TotalTasksRunning            int32                  `protobuf:"varint,1,opt,name=total_tasks_running,json=totalTasksRunning,proto3" json:"total_tasks_running,omitempty"`
	TotalTasksQueued             int32                  `protobuf:"varint,2,opt,name=total_tasks_queued,json=totalTasksQueued,proto3" json:"total_tasks_queued,omitempty"`
	ClusterUtilizationPercent    float32                `protobuf:"fixed32,3,opt,name=cluster_utilization_percent,json=clusterUtilizationPercent,proto3" json:"cluster_utilization_percent,omitempty"`
	AverageTaskCompletionTimeSec float32                `protobuf:"fixed32,4,opt,name=average_task_completion_time_sec,json=averageTaskCompletionTimeSec,proto3" json:"average_task_completion_time_sec,omitempty"`
	ClusterThroughputTasksPerSec float32                `protobuf:"fixed32,5,opt,name=cluster_throughput_tasks_per_sec,json=clusterThroughputTasksPerSec,proto3" json:"cluster_throughput_tasks_per_sec,omitempty"`
	TotalDataTransferredBytes    int64                  `protobuf:"varint,6,opt,name=total_data_transferred_bytes,json=totalDataTransferredBytes,proto3" json:"total_data_transferred_bytes,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *ClusterMetrics) Reset() {
	*x = ClusterMetrics{}
	mi := &file_cluster_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClusterMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterMetrics) ProtoMessage() {}

func (x *ClusterMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterMetrics.ProtoReflect.Descriptor instead.
func (*ClusterMetrics) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{26}
}

func (x *ClusterMetrics) GetTotalTasksRunning() int32 {
	if x != nil {
		return x.TotalTasksRunning
	}
	return 0
}

func (x *ClusterMetrics) GetTotalTasksQueued() int32 {
	if x != nil {
		return x.TotalTasksQueued
	}
	return 0
}

func (x *ClusterMetrics) GetClusterUtilizationPercent() float32 {
	if x != nil {
		return x.ClusterUtilizationPercent
	}
	return 0
}

func (x *ClusterMetrics) GetAverageTaskCompletionTimeSec() float32 {
	if x != nil {
		return x.AverageTaskCompletionTimeSec
	}
	return 0
}

func (x *ClusterMetrics) GetClusterThroughputTasksPerSec() float32 {
	if x != nil {
		return x.ClusterThroughputTasksPerSec
	}
	return 0
}

func (x *ClusterMetrics) GetTotalDataTransferredBytes() int64 {
	if x != nil {
		return x.TotalDataTransferredBytes
	}
	return 0
}

// Heartbeat
type HeartBeatRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeId        string                 `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Health        NodeHealth             `protobuf:"varint,3,opt,name=health,proto3,enum=gpu.cluster.NodeHealth" json:"health,omitempty"`
	ActiveTaskIds []string               `protobuf:"bytes,4,rep,name=active_task_ids,json=activeTaskIds,proto3" json:"active_task_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeartBeatRequest) Reset() {
	*x = HeartBeatRequest{}
	mi := &file_cluster_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeartBeatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartBeatRequest) ProtoMessage() {}

func (x *HeartBeatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartBeatRequest.ProtoReflect.Descriptor instead.
func (*HeartBeatRequest) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{27}
}

func (x *HeartBeatRequest) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *HeartBeatRequest) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *HeartBeatRequest) GetHealth() NodeHealth {
	if x != nil {
		return x.Health
	}
	return NodeHealth_HEALTH_UNSPECIFIED
}

func (x *HeartBeatRequest) GetActiveTaskIds() []string {
	if x != nil {
		return x.ActiveTaskIds
	}
	return nil
}

type HeartBeatResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Success         bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	ServerTimestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=server_timestamp,json=serverTimestamp,proto3" json:"server_timestamp,omitempty"`
	Commands        []string               `protobuf:"bytes,3,rep,name=commands,proto3" json:"commands,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *HeartBeatResponse) Reset() {
	*x = HeartBeatResponse{}
	mi := &file_cluster_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeartBeatResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartBeatResponse) ProtoMessage() {}

func (x *HeartBeatResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartBeatResponse.ProtoReflect.Descriptor instead.
func (*HeartBeatResponse) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{28}
}

func (x *HeartBeatResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *HeartBeatResponse) GetServerTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ServerTimestamp
	}
	return nil
}

func (x *HeartBeatResponse) GetCommands() []string {
	if x != nil {
		return x.Commands
	}
	return nil
}

// Data Transfer
type DataChunk struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TransferId    string                 `protobuf:"bytes,1,opt,name=transfer_id,json=transferId,proto3" json:"transfer_id,omitempty"`
	ChunkIndex    int64                  `protobuf:"varint,2,opt,name=chunk_index,json=chunkIndex,proto3" json:"chunk_index,omitempty"`
	TotalChunks   int64                  `protobuf:"varint,3,opt,name=total_chunks,json=totalChunks,proto3" json:"total_chunks,omitempty"`
	Data          []byte                 `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	Checksum      string                 `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
	IsFinal       bool                   `protobuf:"varint,6,opt,name=is_final,json=isFinal,proto3" json:"is_final,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataChunk) Reset() {
	*x = DataChunk{}
	mi := &file_cluster_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataChunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataChunk) ProtoMessage() {}

func (x *DataChunk) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataChunk.ProtoReflect.Descriptor instead.
func (*DataChunk) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{29}
}

func (x *DataChunk) GetTransferId() string {
	if x != nil {
		return x.TransferId
	}
	return ""
}

func (x *DataChunk) GetChunkIndex() int64 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

func (x *DataChunk) GetTotalChunks() int64 {
	if x != nil {
		return x.TotalChunks
	}
	return 0
}

func (x *DataChunk) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *DataChunk) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *DataChunk) GetIsFinal() bool {
	if x != nil {
		return x.IsFinal
	}
	return false
}

type DataRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DataId           string                 `protobuf:"bytes,1,opt,name=data_id,json=dataId,proto3" json:"data_id,omitempty"`
	RequestingNodeId string                 `protobuf:"bytes,2,opt,name=requesting_node_id,json=requestingNodeId,proto3" json:"requesting_node_id,omitempty"`
	Offset           int64                  `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Length           int64                  `protobuf:"varint,4,opt,name=length,proto3" json:"length,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *DataRequest) Reset() {
	*x = DataRequest{}
	mi := &file_cluster_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataRequest) ProtoMessage() {}

func (x *DataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataRequest.ProtoReflect.Descriptor instead.
func (*DataRequest) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{30}
}

func (x *DataRequest) GetDataId() string {
	if x != nil {
		return x.DataId
	}
	return ""
}

func (x *DataRequest) GetRequestingNodeId() string {
	if x != nil {
		return x.RequestingNodeId
	}
	return ""
}

func (x *DataRequest) GetOffset() int64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *DataRequest) GetLength() int64 {
	if x != nil {
		return x.Length
	}
	return 0
}

type DataTransferResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	TransferId    string                 `protobuf:"bytes,2,opt,name=transfer_id,json=transferId,proto3" json:"transfer_id,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	BytesReceived int64                  `protobuf:"varint,4,opt,name=bytes_received,json=bytesReceived,proto3" json:"bytes_received,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataTransferResponse) Reset() {
	*x = DataTransferResponse{}
	mi := &file_cluster_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataTransferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataTransferResponse) ProtoMessage() {}

func (x *DataTransferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataTransferResponse.ProtoReflect.Descriptor instead.
func (*DataTransferResponse) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{31}
}

func (x *DataTransferResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DataTransferResponse) GetTransferId() string {
	if x != nil {
		return x.TransferId
	}
	return ""
}

func (x *DataTransferResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DataTransferResponse) GetBytesReceived() int64 {
	if x != nil {
		return x.BytesReceived
	}
	return 0
}

// Result Aggregation
type ResultSubmissionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	NodeId        string                 `protobuf:"bytes,2,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	Result        *TaskResult            `protobuf:"bytes,3,opt,name=result,proto3" json:"result,omitempty"`
	IsPartial     bool                   `protobuf:"varint,4,opt,name=is_partial,json=isPartial,proto3" json:"is_partial,omitempty"`
	PartIndex     int32                  `protobuf:"varint,5,opt,name=part_index,json=partIndex,proto3" json:"part_index,omitempty"`
	TotalParts    int32                  `protobuf:"varint,6,opt,name=total_parts,json=totalParts,proto3" json:"total_parts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResultSubmissionRequest) Reset() {
	*x = ResultSubmissionRequest{}
	mi := &file_cluster_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResultSubmissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultSubmissionRequest) ProtoMessage() {}

func (x *ResultSubmissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultSubmissionRequest.ProtoReflect.Descriptor instead.
func (*ResultSubmissionRequest) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{32}
}

func (x *ResultSubmissionRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ResultSubmissionRequest) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *ResultSubmissionRequest) GetResult() *TaskResult {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *ResultSubmissionRequest) GetIsPartial() bool {
	if x != nil {
		return x.IsPartial
	}
	return false
}

func (x *ResultSubmissionRequest) GetPartIndex() int32 {
	if x != nil {
		return x.PartIndex
	}
	return 0
}

func (x *ResultSubmissionRequest) GetTotalParts() int32 {
	if x != nil {
		return x.TotalParts
	}
	return 0
}

type ResultSubmissionResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Success             bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message             string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	AggregationComplete bool                   `protobuf:"varint,3,opt,name=aggregation_complete,json=aggregationComplete,proto3" json:"aggregation_complete,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *ResultSubmissionResponse) Reset() {
	*x = ResultSubmissionResponse{}
	mi := &file_cluster_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResultSubmissionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultSubmissionResponse) ProtoMessage() {}

func (x *ResultSubmissionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultSubmissionResponse.ProtoReflect.Descriptor instead.
func (*ResultSubmissionResponse) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{33}
}

func (x *ResultSubmissionResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ResultSubmissionResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ResultSubmissionResponse) GetAggregationComplete() bool {
	if x != nil {
		return x.AggregationComplete
	}
	return false
}

type ResultAggregationRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	AggregationId   string                 `protobuf:"bytes,1,opt,name=aggregation_id,json=aggregationId,proto3" json:"aggregation_id,omitempty"`
	TaskIds         []string               `protobuf:"bytes,2,rep,name=task_ids,json=taskIds,proto3" json:"task_ids,omitempty"`
	AggregationType AggregationType        `protobuf:"varint,3,opt,name=aggregation_type,json=aggregationType,proto3,enum=gpu.cluster.AggregationType" json:"aggregation_type,omitempty"`
	Parameters      map[string]string      `protobuf:"bytes,4,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ResultAggregationRequest) Reset() {
	*x = ResultAggregationRequest{}
	mi := &file_cluster_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResultAggregationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultAggregationRequest) ProtoMessage() {}

func (x *ResultAggregationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultAggregationRequest.ProtoReflect.Descriptor instead.
func (*ResultAggregationRequest) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{34}
}

func (x *ResultAggregationRequest) GetAggregationId() string {
	if x != nil {
		return x.AggregationId
	}
	return ""
}

func (x *ResultAggregationRequest) GetTaskIds() []string {
	if x != nil {
		return x.TaskIds
	}
	return nil
}

func (x *ResultAggregationRequest) GetAggregationType() AggregationType {
	if x != nil {
		return x.AggregationType
	}
	return AggregationType_AGGREGATION_TYPE_UNSPECIFIED
}

func (x *ResultAggregationRequest) GetParameters() map[string]string {
	if x != nil {
		return x.Parameters
	}
	return nil
}

type ResultAggregationResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Success             bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message             string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	AggregationId       string                 `protobuf:"bytes,3,opt,name=aggregation_id,json=aggregationId,proto3" json:"aggregation_id,omitempty"`
	PartialResultsCount int32                  `protobuf:"varint,4,opt,name=partial_results_count,json=partialResultsCount,proto3" json:"partial_results_count,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *ResultAggregationResponse) Reset() {
	*x = ResultAggregationResponse{}
	mi := &file_cluster_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResultAggregationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultAggregationResponse) ProtoMessage() {}

func (x *ResultAggregationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultAggregationResponse.ProtoReflect.Descriptor instead.
func (*ResultAggregationResponse) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{35}
}

func (x *ResultAggregationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ResultAggregationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ResultAggregationResponse) GetAggregationId() string {
	if x != nil {
		return x.AggregationId
	}
	return ""
}

func (x *ResultAggregationResponse) GetPartialResultsCount() int32 {
	if x != nil {
		return x.PartialResultsCount
	}
	return 0
}

type AggregatedResult struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	AggregationId   string                 `protobuf:"bytes,1,opt,name=aggregation_id,json=aggregationId,proto3" json:"aggregation_id,omitempty"`
	Type            AggregationType        `protobuf:"varint,2,opt,name=type,proto3,enum=gpu.cluster.AggregationType" json:"type,omitempty"`
	AggregatedData  []byte                 `protobuf:"bytes,3,opt,name=aggregated_data,json=aggregatedData,proto3" json:"aggregated_data,omitempty"`
	Metadata        map[string]string      `protobuf:"bytes,4,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	IsFinal         bool                   `protobuf:"varint,5,opt,name=is_final,json=isFinal,proto3" json:"is_final,omitempty"`
	ProgressPercent float32                `protobuf:"fixed32,6,opt,name=progress_percent,json=progressPercent,proto3" json:"progress_percent,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AggregatedResult) Reset() {
	*x = AggregatedResult{}
	mi := &file_cluster_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AggregatedResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AggregatedResult) ProtoMessage() {}

func (x *AggregatedResult) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AggregatedResult.ProtoReflect.Descriptor instead.
func (*AggregatedResult) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{36}
}

func (x *AggregatedResult) GetAggregationId() string {
	if x != nil {
		return x.AggregationId
	}
	return ""
}

func (x *AggregatedResult) GetType() AggregationType {
	if x != nil {
		return x.Type
	}
	return AggregationType_AGGREGATION_TYPE_UNSPECIFIED
}

func (x *AggregatedResult) GetAggregatedData() []byte {
	if x != nil {
		return x.AggregatedData
	}
	return nil
}

func (x *AggregatedResult) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *AggregatedResult) GetIsFinal() bool {
	if x != nil {
		return x.IsFinal
	}
	return false
}

func (x *AggregatedResult) GetProgressPercent() float32 {
	if x != nil {
		return x.ProgressPercent
	}
	return 0
}

// Load Balancing
type RebalanceRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	InitiatingNodeId string                 `protobuf:"bytes,1,opt,name=initiating_node_id,json=initiatingNodeId,proto3" json:"initiating_node_id,omitempty"`
	Reason           RebalanceReason        `protobuf:"varint,2,opt,name=reason,proto3,enum=gpu.cluster.RebalanceReason" json:"reason,omitempty"`
	AffectedTaskIds  []string               `protobuf:"bytes,3,rep,name=affected_task_ids,json=affectedTaskIds,proto3" json:"affected_task_ids,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *RebalanceRequest) Reset() {
	*x = RebalanceRequest{}
	mi := &file_cluster_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RebalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RebalanceRequest) ProtoMessage() {}

func (x *RebalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RebalanceRequest.ProtoReflect.Descriptor instead.
func (*RebalanceRequest) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{37}
}

func (x *RebalanceRequest) GetInitiatingNodeId() string {
	if x != nil {
		return x.InitiatingNodeId
	}
	return ""
}

func (x *RebalanceRequest) GetReason() RebalanceReason {
	if x != nil {
		return x.Reason
	}
	return RebalanceReason_REBALANCE_REASON_UNSPECIFIED
}

func (x *RebalanceRequest) GetAffectedTaskIds() []string {
	if x != nil {
		return x.AffectedTaskIds
	}
	return nil
}

type RebalanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Migrations    []*TaskMigration       `protobuf:"bytes,3,rep,name=migrations,proto3" json:"migrations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RebalanceResponse) Reset() {
	*x = RebalanceResponse{}
	mi := &file_cluster_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RebalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RebalanceResponse) ProtoMessage() {}

func (x *RebalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RebalanceResponse.ProtoReflect.Descriptor instead.
func (*RebalanceResponse) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{38}
}

func (x *RebalanceResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RebalanceResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RebalanceResponse) GetMigrations() []*TaskMigration {
	if x != nil {
		return x.Migrations
	}
	return nil
}

type TaskMigrationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	SourceNodeId  string                 `protobuf:"bytes,2,opt,name=source_node_id,json=sourceNodeId,proto3" json:"source_node_id,omitempty"`
	TargetNodeId  string                 `protobuf:"bytes,3,opt,name=target_node_id,json=targetNodeId,proto3" json:"target_node_id,omitempty"`
	Reason        MigrationReason        `protobuf:"varint,4,opt,name=reason,proto3,enum=gpu.cluster.MigrationReason" json:"reason,omitempty"`
	PreserveState bool                   `protobuf:"varint,5,opt,name=preserve_state,json=preserveState,proto3" json:"preserve_state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskMigrationRequest) Reset() {
	*x = TaskMigrationRequest{}
	mi := &file_cluster_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskMigrationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskMigrationRequest) ProtoMessage() {}

func (x *TaskMigrationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskMigrationRequest.ProtoReflect.Descriptor instead.
func (*TaskMigrationRequest) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{39}
}

func (x *TaskMigrationRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskMigrationRequest) GetSourceNodeId() string {
	if x != nil {
		return x.SourceNodeId
	}
	return ""
}

func (x *TaskMigrationRequest) GetTargetNodeId() string {
	if x != nil {
		return x.TargetNodeId
	}
	return ""
}

func (x *TaskMigrationRequest) GetReason() MigrationReason {
	if x != nil {
		return x.Reason
	}
	return MigrationReason_MIGRATION_REASON_UNSPECIFIED
}

func (x *TaskMigrationRequest) GetPreserveState() bool {
	if x != nil {
		return x.PreserveState
	}
	return false
}

type TaskMigrationResponse struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	Success                bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message                string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	MigrationStartTime     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=migration_start_time,json=migrationStartTime,proto3" json:"migration_start_time,omitempty"`
	EstimatedMigrationTime *durationpb.Duration   `protobuf:"bytes,4,opt,name=estimated_migration_time,json=estimatedMigrationTime,proto3" json:"estimated_migration_time,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *TaskMigrationResponse) Reset() {
	*x = TaskMigrationResponse{}
	mi := &file_cluster_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskMigrationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskMigrationResponse) ProtoMessage() {}

func (x *TaskMigrationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskMigrationResponse.ProtoReflect.Descriptor instead.
func (*TaskMigrationResponse) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{40}
}

func (x *TaskMigrationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *TaskMigrationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TaskMigrationResponse) GetMigrationStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.MigrationStartTime
	}
	return nil
}

func (x *TaskMigrationResponse) GetEstimatedMigrationTime() *durationpb.Duration {
	if x != nil {
		return x.EstimatedMigrationTime
	}
	return nil
}

type TaskMigration struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	FromNodeId    string                 `protobuf:"bytes,2,opt,name=from_node_id,json=fromNodeId,proto3" json:"from_node_id,omitempty"`
	ToNodeId      string                 `protobuf:"bytes,3,opt,name=to_node_id,json=toNodeId,proto3" json:"to_node_id,omitempty"`
	Reason        MigrationReason        `protobuf:"varint,4,opt,name=reason,proto3,enum=gpu.cluster.MigrationReason" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskMigration) Reset() {
	*x = TaskMigration{}
	mi := &file_cluster_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskMigration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskMigration) ProtoMessage() {}

func (x *TaskMigration) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskMigration.ProtoReflect.Descriptor instead.
func (*TaskMigration) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{41}
}

func (x *TaskMigration) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskMigration) GetFromNodeId() string {
	if x != nil {
		return x.FromNodeId
	}
	return ""
}

func (x *TaskMigration) GetToNodeId() string {
	if x != nil {
		return x.ToNodeId
	}
	return ""
}

func (x *TaskMigration) GetReason() MigrationReason {
	if x != nil {
		return x.Reason
	}
	return MigrationReason_MIGRATION_REASON_UNSPECIFIED
}

// Configuration
type ClusterConfig struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	ClusterId            string                 `protobuf:"bytes,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	Settings             map[string]string      `protobuf:"bytes,2,rep,name=settings,proto3" json:"settings,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	CoordinatorEndpoints []string               `protobuf:"bytes,3,rep,name=coordinator_endpoints,json=coordinatorEndpoints,proto3" json:"coordinator_endpoints,omitempty"`
	SecurityConfig       *SecurityConfig        `protobuf:"bytes,4,opt,name=security_config,json=securityConfig,proto3" json:"security_config,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ClusterConfig) Reset() {
	*x = ClusterConfig{}
	mi := &file_cluster_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClusterConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterConfig) ProtoMessage() {}

func (x *ClusterConfig) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterConfig.ProtoReflect.Descriptor instead.
func (*ClusterConfig) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{42}
}

func (x *ClusterConfig) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *ClusterConfig) GetSettings() map[string]string {
	if x != nil {
		return x.Settings
	}
	return nil
}

func (x *ClusterConfig) GetCoordinatorEndpoints() []string {
	if x != nil {
		return x.CoordinatorEndpoints
	}
	return nil
}

func (x *ClusterConfig) GetSecurityConfig() *SecurityConfig {
	if x != nil {
		return x.SecurityConfig
	}
	return nil
}

type SecurityConfig struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	TlsEnabled         bool                   `protobuf:"varint,1,opt,name=tls_enabled,json=tlsEnabled,proto3" json:"tls_enabled,omitempty"`
	CaCert             string                 `protobuf:"bytes,2,opt,name=ca_cert,json=caCert,proto3" json:"ca_cert,omitempty"`
	MutualTls          bool                   `protobuf:"varint,3,opt,name=mutual_tls,json=mutualTls,proto3" json:"mutual_tls,omitempty"`
	TokenExpirySeconds int32                  `protobuf:"varint,4,opt,name=token_expiry_seconds,json=tokenExpirySeconds,proto3" json:"token_expiry_seconds,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *SecurityConfig) Reset() {
	*x = SecurityConfig{}
	mi := &file_cluster_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecurityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityConfig) ProtoMessage() {}

func (x *SecurityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_cluster_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityConfig.ProtoReflect.Descriptor instead.
func (*SecurityConfig) Descriptor() ([]byte, []int) {
	return file_cluster_proto_rawDescGZIP(), []int{43}
}

func (x *SecurityConfig) GetTlsEnabled() bool {
	if x != nil {
		return x.TlsEnabled
	}
	return false
}

func (x *SecurityConfig) GetCaCert() string {
	if x != nil {
		return x.CaCert
	}
	return ""
}

func (x *SecurityConfig) GetMutualTls() bool {
	if x != nil {
		return x.MutualTls
	}
	return false
}

func (x *SecurityConfig) GetTokenExpirySeconds() int32 {
	if x != nil {
		return x.TokenExpirySeconds
	}
	return 0
}

var File_cluster_proto protoreflect.FileDescriptor

const file_cluster_proto_rawDesc = "" +
	"\n" +
	"\rcluster.proto\x12\vgpu.cluster\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/duration.proto\"\xa3\x02\n" +
	"\x15TaskAssignmentRequest\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x122\n" +
	"\ttask_spec\x18\x02 \x01(\v2\x15.gpu.cluster.TaskSpecR\btaskSpec\x12'\n" +
	"\x0fpreferred_nodes\x18\x03 \x03(\tR\x0epreferredNodes\x12%\n" +
	"\x0eexcluded_nodes\x18\x04 \x03(\tR\rexcludedNodes\x125\n" +
	"\bpriority\x18\x05 \x01(\x0e2\x19.gpu.cluster.TaskPriorityR\bpriority\x126\n" +
	"\bdeadline\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\bdeadline\"\x8e\x02\n" +
	"\x16TaskAssignmentResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12(\n" +
	"\x10assigned_node_id\x18\x02 \x01(\tR\x0eassignedNodeId\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\x12L\n" +
	"\x14estimated_start_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x12estimatedStartTime\x12H\n" +
	"\x12estimated_duration\x18\x05 \x01(\v2\x19.google.protobuf.DurationR\x11estimatedDuration\"\xce\x03\n" +
	"\bTaskSpec\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12)\n" +
	"\x04type\x18\x04 \x01(\x0e2\x15.gpu.cluster.TaskTypeR\x04type\x12V\n" +
	"\x15resource_requirements\x18\x05 \x01(\v2!.gpu.cluster.ResourceRequirementsR\x14resourceRequirements\x12E\n" +
	"\n" +
	"parameters\x18\x06 \x03(\v2%.gpu.cluster.TaskSpec.ParametersEntryR\n" +
	"parameters\x12&\n" +
	"\x0finput_data_urls\x18\a \x03(\tR\rinputDataUrls\x12'\n" +
	"\x0foutput_location\x18\b \x01(\tR\x0eoutputLocation\x12\"\n" +
	"\fdependencies\x18\t \x03(\tR\fdependencies\x1a=\n" +
	"\x0fParametersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xd1\x02\n" +
	"\x14ResourceRequirements\x12\"\n" +
	"\rgpu_memory_mb\x18\x01 \x01(\x03R\vgpuMemoryMb\x12(\n" +
	"\x10system_memory_mb\x18\x02 \x01(\x03R\x0esystemMemoryMb\x12*\n" +
	"\x11gpu_compute_units\x18\x03 \x01(\x05R\x0fgpuComputeUnits\x12.\n" +
	"\x13min_gpu_utilization\x18\x04 \x01(\x02R\x11minGpuUtilization\x12:\n" +
	"\x19required_gpu_capabilities\x18\x05 \x03(\tR\x17requiredGpuCapabilities\x12S\n" +
	"\x14network_requirements\x18\x06 \x01(\v2 .gpu.cluster.NetworkRequirementsR\x13networkRequirements\"\xa0\x01\n" +
	"\x13NetworkRequirements\x12,\n" +
	"\x12min_bandwidth_mbps\x18\x01 \x01(\x02R\x10minBandwidthMbps\x12$\n" +
	"\x0emax_latency_ms\x18\x02 \x01(\x02R\fmaxLatencyMs\x125\n" +
	"\x17max_packet_loss_percent\x18\x03 \x01(\x02R\x14maxPacketLossPercent\"\xf6\x02\n" +
	"\x10TaskStatusUpdate\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12\x17\n" +
	"\anode_id\x18\x02 \x01(\tR\x06nodeId\x12/\n" +
	"\x06status\x18\x03 \x01(\x0e2\x17.gpu.cluster.TaskStatusR\x06status\x12)\n" +
	"\x10progress_percent\x18\x04 \x01(\x02R\x0fprogressPercent\x12\x18\n" +
	"\amessage\x18\x05 \x01(\tR\amessage\x128\n" +
	"\ttimestamp\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12D\n" +
	"\ametrics\x18\a \x03(\v2*.gpu.cluster.TaskStatusUpdate.MetricsEntryR\ametrics\x1a:\n" +
	"\fMetricsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"H\n" +
	"\x12TaskStatusResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xf3\x01\n" +
	"\x15TaskCompletionRequest\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12\x17\n" +
	"\anode_id\x18\x02 \x01(\tR\x06nodeId\x12/\n" +
	"\x06result\x18\x03 \x01(\v2\x17.gpu.cluster.TaskResultR\x06result\x12C\n" +
	"\x0fcompletion_time\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x0ecompletionTime\x122\n" +
	"\ametrics\x18\x05 \x01(\v2\x18.gpu.cluster.TaskMetricsR\ametrics\"u\n" +
	"\x16TaskCompletionResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12'\n" +
	"\x0fresult_accepted\x18\x03 \x01(\bR\x0eresultAccepted\"\xaf\x02\n" +
	"\n" +
	"TaskResult\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12#\n" +
	"\rerror_message\x18\x02 \x01(\tR\ferrorMessage\x12(\n" +
	"\x10output_data_urls\x18\x03 \x03(\tR\x0eoutputDataUrls\x12T\n" +
	"\x0fresult_metadata\x18\x04 \x03(\v2+.gpu.cluster.TaskResult.ResultMetadataEntryR\x0eresultMetadata\x12\x1f\n" +
	"\vresult_data\x18\x05 \x01(\fR\n" +
	"resultData\x1aA\n" +
	"\x13ResultMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x8d\x03\n" +
	"\vTaskMetrics\x12@\n" +
	"\x0eexecution_time\x18\x01 \x01(\v2\x19.google.protobuf.DurationR\rexecutionTime\x12+\n" +
	"\x12gpu_memory_used_mb\x18\x02 \x01(\x03R\x0fgpuMemoryUsedMb\x121\n" +
	"\x15system_memory_used_mb\x18\x03 \x01(\x03R\x12systemMemoryUsedMb\x126\n" +
	"\x17gpu_utilization_percent\x18\x04 \x01(\x02R\x15gpuUtilizationPercent\x126\n" +
	"\x17cpu_utilization_percent\x18\x05 \x01(\x02R\x15cpuUtilizationPercent\x124\n" +
	"\x16data_transferred_bytes\x18\x06 \x01(\x03R\x14dataTransferredBytes\x126\n" +
	"\x17power_consumption_watts\x18\a \x01(\x02R\x15powerConsumptionWatts\"`\n" +
	"\x17TaskCancellationRequest\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12\x16\n" +
	"\x06reason\x18\x02 \x01(\tR\x06reason\x12\x14\n" +
	"\x05force\x18\x03 \x01(\bR\x05force\"x\n" +
	"\x18TaskCancellationResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12(\n" +
	"\x10task_was_running\x18\x03 \x01(\bR\x0etaskWasRunning\"\xb9\x01\n" +
	"\x17NodeRegistrationRequest\x122\n" +
	"\tnode_info\x18\x01 \x01(\v2\x15.gpu.cluster.NodeInfoR\bnodeInfo\x127\n" +
	"\vgpu_devices\x18\x02 \x03(\v2\x16.gpu.cluster.GPUDeviceR\n" +
	"gpuDevices\x121\n" +
	"\x14authentication_token\x18\x03 \x01(\tR\x13authenticationToken\"\xaa\x01\n" +
	"\x18NodeRegistrationResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x17\n" +
	"\anode_id\x18\x02 \x01(\tR\x06nodeId\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\x12A\n" +
	"\x0ecluster_config\x18\x04 \x01(\v2\x1a.gpu.cluster.ClusterConfigR\rclusterConfig\"\xf7\x02\n" +
	"\bNodeInfo\x12\x17\n" +
	"\anode_id\x18\x01 \x01(\tR\x06nodeId\x12\x1a\n" +
	"\bhostname\x18\x02 \x01(\tR\bhostname\x12\x1d\n" +
	"\n" +
	"ip_address\x18\x03 \x01(\tR\tipAddress\x12\x12\n" +
	"\x04port\x18\x04 \x01(\x05R\x04port\x128\n" +
	"\vsystem_info\x18\x05 \x01(\v2\x17.gpu.cluster.SystemInfoR\n" +
	"systemInfo\x12;\n" +
	"\fnetwork_info\x18\x06 \x01(\v2\x18.gpu.cluster.NetworkInfoR\vnetworkInfo\x12K\n" +
	"\fcapabilities\x18\a \x03(\v2'.gpu.cluster.NodeInfo.CapabilitiesEntryR\fcapabilities\x1a?\n" +
	"\x11CapabilitiesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xb2\x02\n" +
	"\n" +
	"SystemInfo\x12\x0e\n" +
	"\x02os\x18\x01 \x01(\tR\x02os\x12\"\n" +
	"\farchitecture\x18\x02 \x01(\tR\farchitecture\x12\x1b\n" +
	"\tcpu_cores\x18\x03 \x01(\x05R\bcpuCores\x12&\n" +
	"\x0ftotal_memory_mb\x18\x04 \x01(\x03R\rtotalMemoryMb\x12.\n" +
	"\x13available_memory_mb\x18\x05 \x01(\x03R\x11availableMemoryMb\x12!\n" +
	"\fload_average\x18\x06 \x01(\x02R\vloadAverage\x121\n" +
	"\x06uptime\x18\a \x01(\v2\x19.google.protobuf.DurationR\x06uptime\x12%\n" +
	"\x0edriver_version\x18\b \x01(\tR\rdriverVersion\"\xa3\x01\n" +
	"\vNetworkInfo\x12%\n" +
	"\x0ebandwidth_mbps\x18\x01 \x01(\x02R\rbandwidthMbps\x12\x1d\n" +
	"\n" +
	"latency_ms\x18\x02 \x01(\x02R\tlatencyMs\x12.\n" +
	"\x13packet_loss_percent\x18\x03 \x01(\x02R\x11packetLossPercent\x12\x1e\n" +
	"\n" +
	"interfaces\x18\x04 \x03(\tR\n" +
	"interfaces\"\xbe\x03\n" +
	"\tGPUDevice\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06vendor\x18\x03 \x01(\tR\x06vendor\x12\x14\n" +
	"\x05model\x18\x04 \x01(\tR\x05model\x12\x1b\n" +
	"\tmemory_mb\x18\x05 \x01(\x03R\bmemoryMb\x12.\n" +
	"\x13available_memory_mb\x18\x06 \x01(\x03R\x11availableMemoryMb\x12#\n" +
	"\rcompute_units\x18\a \x01(\x05R\fcomputeUnits\x12/\n" +
	"\x13utilization_percent\x18\b \x01(\x02R\x12utilizationPercent\x12/\n" +
	"\x13temperature_celsius\x18\t \x01(\x02R\x12temperatureCelsius\x12*\n" +
	"\x11power_usage_watts\x18\n" +
	" \x01(\x02R\x0fpowerUsageWatts\x12\"\n" +
	"\fcapabilities\x18\v \x03(\tR\fcapabilities\x12.\n" +
	"\x06status\x18\f \x01(\x0e2\x16.gpu.cluster.GPUStatusR\x06status\"\xa1\x02\n" +
	"\x10NodeStatusUpdate\x12\x17\n" +
	"\anode_id\x18\x01 \x01(\tR\x06nodeId\x128\n" +
	"\vsystem_info\x18\x02 \x01(\v2\x17.gpu.cluster.SystemInfoR\n" +
	"systemInfo\x127\n" +
	"\vgpu_devices\x18\x03 \x03(\v2\x16.gpu.cluster.GPUDeviceR\n" +
	"gpuDevices\x12G\n" +
	"\x10workload_metrics\x18\x04 \x01(\v2\x1c.gpu.cluster.WorkloadMetricsR\x0fworkloadMetrics\x128\n" +
	"\ttimestamp\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\"H\n" +
	"\x12NodeStatusResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xd5\x02\n" +
	"\x0fWorkloadMetrics\x12!\n" +
	"\factive_tasks\x18\x01 \x01(\x05R\vactiveTasks\x12!\n" +
	"\fqueued_tasks\x18\x02 \x01(\x05R\vqueuedTasks\x12'\n" +
	"\x0fcompleted_tasks\x18\x03 \x01(\x05R\x0ecompletedTasks\x12!\n" +
	"\ffailed_tasks\x18\x04 \x01(\x05R\vfailedTasks\x127\n" +
	"\x18throughput_tasks_per_sec\x18\x05 \x01(\x02R\x15throughputTasksPerSec\x12E\n" +
	"\x11average_task_time\x18\x06 \x01(\v2\x19.google.protobuf.DurationR\x0faverageTaskTime\x120\n" +
	"\x14success_rate_percent\x18\a \x01(\x02R\x12successRatePercent\"s\n" +
	"\x14ClusterStatusRequest\x128\n" +
	"\x18include_detailed_metrics\x18\x01 \x01(\bR\x16includeDetailedMetrics\x12!\n" +
	"\fnode_filters\x18\x02 \x03(\tR\vnodeFilters\"\xd8\x01\n" +
	"\x15ClusterStatusResponse\x12;\n" +
	"\fcluster_info\x18\x01 \x01(\v2\x18.gpu.cluster.ClusterInfoR\vclusterInfo\x12<\n" +
	"\rnode_statuses\x18\x02 \x03(\v2\x17.gpu.cluster.NodeStatusR\fnodeStatuses\x12D\n" +
	"\x0fcluster_metrics\x18\x03 \x01(\v2\x1b.gpu.cluster.ClusterMetricsR\x0eclusterMetrics\"\x89\x02\n" +
	"\vClusterInfo\x12\x1d\n" +
	"\n" +
	"cluster_id\x18\x01 \x01(\tR\tclusterId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1f\n" +
	"\vtotal_nodes\x18\x03 \x01(\x05R\n" +
	"totalNodes\x12!\n" +
	"\factive_nodes\x18\x04 \x01(\x05R\vactiveNodes\x12\x1d\n" +
	"\n" +
	"total_gpus\x18\x05 \x01(\x05R\ttotalGpus\x12%\n" +
	"\x0eavailable_gpus\x18\x06 \x01(\x05R\ravailableGpus\x12=\n" +
	"\flast_updated\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\vlastUpdated\"\xd5\x02\n" +
	"\n" +
	"NodeStatus\x12\x17\n" +
	"\anode_id\x18\x01 \x01(\tR\x06nodeId\x12/\n" +
	"\x06health\x18\x02 \x01(\x0e2\x17.gpu.cluster.NodeHealthR\x06health\x128\n" +
	"\vsystem_info\x18\x03 \x01(\v2\x17.gpu.cluster.SystemInfoR\n" +
	"systemInfo\x127\n" +
	"\vgpu_devices\x18\x04 \x03(\v2\x16.gpu.cluster.GPUDeviceR\n" +
	"gpuDevices\x12G\n" +
	"\x10workload_metrics\x18\x05 \x01(\v2\x1c.gpu.cluster.WorkloadMetricsR\x0fworkloadMetrics\x12A\n" +
	"\x0elast_heartbeat\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\rlastHeartbeat\"\xff\x02\n" +
	"\x0eClusterMetrics\x12.\n" +
	"\x13total_tasks_running\x18\x01 \x01(\x05R\x11totalTasksRunning\x12,\n" +
	"\x12total_tasks_queued\x18\x02 \x01(\x05R\x10totalTasksQueued\x12>\n" +
	"\x1bcluster_utilization_percent\x18\x03 \x01(\x02R\x19clusterUtilizationPercent\x12F\n" +
	" average_task_completion_time_sec\x18\x04 \x01(\x02R\x1caverageTaskCompletionTimeSec\x12F\n" +
	" cluster_throughput_tasks_per_sec\x18\x05 \x01(\x02R\x1cclusterThroughputTasksPerSec\x12?\n" +
	"\x1ctotal_data_transferred_bytes\x18\x06 \x01(\x03R\x19totalDataTransferredBytes\"\xbe\x01\n" +
	"\x10HeartBeatRequest\x12\x17\n" +
	"\anode_id\x18\x01 \x01(\tR\x06nodeId\x128\n" +
	"\ttimestamp\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12/\n" +
	"\x06health\x18\x03 \x01(\x0e2\x17.gpu.cluster.NodeHealthR\x06health\x12&\n" +
	"\x0factive_task_ids\x18\x04 \x03(\tR\ractiveTaskIds\"\x90\x01\n" +
	"\x11HeartBeatResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12E\n" +
	"\x10server_timestamp\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\x0fserverTimestamp\x12\x1a\n" +
	"\bcommands\x18\x03 \x03(\tR\bcommands\"\xbb\x01\n" +
	"\tDataChunk\x12\x1f\n" +
	"\vtransfer_id\x18\x01 \x01(\tR\n" +
	"transferId\x12\x1f\n" +
	"\vchunk_index\x18\x02 \x01(\x03R\n" +
	"chunkIndex\x12!\n" +
	"\ftotal_chunks\x18\x03 \x01(\x03R\vtotalChunks\x12\x12\n" +
	"\x04data\x18\x04 \x01(\fR\x04data\x12\x1a\n" +
	"\bchecksum\x18\x05 \x01(\tR\bchecksum\x12\x19\n" +
	"\bis_final\x18\x06 \x01(\bR\aisFinal\"\x84\x01\n" +
	"\vDataRequest\x12\x17\n" +
	"\adata_id\x18\x01 \x01(\tR\x06dataId\x12,\n" +
	"\x12requesting_node_id\x18\x02 \x01(\tR\x10requestingNodeId\x12\x16\n" +
	"\x06offset\x18\x03 \x01(\x03R\x06offset\x12\x16\n" +
	"\x06length\x18\x04 \x01(\x03R\x06length\"\x92\x01\n" +
	"\x14DataTransferResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x1f\n" +
	"\vtransfer_id\x18\x02 \x01(\tR\n" +
	"transferId\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\x12%\n" +
	"\x0ebytes_received\x18\x04 \x01(\x03R\rbytesReceived\"\xdb\x01\n" +
	"\x17ResultSubmissionRequest\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12\x17\n" +
	"\anode_id\x18\x02 \x01(\tR\x06nodeId\x12/\n" +
	"\x06result\x18\x03 \x01(\v2\x17.gpu.cluster.TaskResultR\x06result\x12\x1d\n" +
	"\n" +
	"is_partial\x18\x04 \x01(\bR\tisPartial\x12\x1d\n" +
	"\n" +
	"part_index\x18\x05 \x01(\x05R\tpartIndex\x12\x1f\n" +
	"\vtotal_parts\x18\x06 \x01(\x05R\n" +
	"totalParts\"\x81\x01\n" +
	"\x18ResultSubmissionResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x121\n" +
	"\x14aggregation_complete\x18\x03 \x01(\bR\x13aggregationComplete\"\xbb\x02\n" +
	"\x18ResultAggregationRequest\x12%\n" +
	"\x0eaggregation_id\x18\x01 \x01(\tR\raggregationId\x12\x19\n" +
	"\btask_ids\x18\x02 \x03(\tR\ataskIds\x12G\n" +
	"\x10aggregation_type\x18\x03 \x01(\x0e2\x1c.gpu.cluster.AggregationTypeR\x0faggregationType\x12U\n" +
	"\n" +
	"parameters\x18\x04 \x03(\v25.gpu.cluster.ResultAggregationRequest.ParametersEntryR\n" +
	"parameters\x1a=\n" +
	"\x0fParametersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xaa\x01\n" +
	"\x19ResultAggregationResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12%\n" +
	"\x0eaggregation_id\x18\x03 \x01(\tR\raggregationId\x122\n" +
	"\x15partial_results_count\x18\x04 \x01(\x05R\x13partialResultsCount\"\xe0\x02\n" +
	"\x10AggregatedResult\x12%\n" +
	"\x0eaggregation_id\x18\x01 \x01(\tR\raggregationId\x120\n" +
	"\x04type\x18\x02 \x01(\x0e2\x1c.gpu.cluster.AggregationTypeR\x04type\x12'\n" +
	"\x0faggregated_data\x18\x03 \x01(\fR\x0eaggregatedData\x12G\n" +
	"\bmetadata\x18\x04 \x03(\v2+.gpu.cluster.AggregatedResult.MetadataEntryR\bmetadata\x12\x19\n" +
	"\bis_final\x18\x05 \x01(\bR\aisFinal\x12)\n" +
	"\x10progress_percent\x18\x06 \x01(\x02R\x0fprogressPercent\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xa2\x01\n" +
	"\x10RebalanceRequest\x12,\n" +
	"\x12initiating_node_id\x18\x01 \x01(\tR\x10initiatingNodeId\x124\n" +
	"\x06reason\x18\x02 \x01(\x0e2\x1c.gpu.cluster.RebalanceReasonR\x06reason\x12*\n" +
	"\x11affected_task_ids\x18\x03 \x03(\tR\x0faffectedTaskIds\"\x83\x01\n" +
	"\x11RebalanceResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12:\n" +
	"\n" +
	"migrations\x18\x03 \x03(\v2\x1a.gpu.cluster.TaskMigrationR\n" +
	"migrations\"\xd8\x01\n" +
	"\x14TaskMigrationRequest\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12$\n" +
	"\x0esource_node_id\x18\x02 \x01(\tR\fsourceNodeId\x12$\n" +
	"\x0etarget_node_id\x18\x03 \x01(\tR\ftargetNodeId\x124\n" +
	"\x06reason\x18\x04 \x01(\x0e2\x1c.gpu.cluster.MigrationReasonR\x06reason\x12%\n" +
	"\x0epreserve_state\x18\x05 \x01(\bR\rpreserveState\"\xee\x01\n" +
	"\x15TaskMigrationResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12L\n" +
	"\x14migration_start_time\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\x12migrationStartTime\x12S\n" +
	"\x18estimated_migration_time\x18\x04 \x01(\v2\x19.google.protobuf.DurationR\x16estimatedMigrationTime\"\x9e\x01\n" +
	"\rTaskMigration\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12 \n" +
	"\ffrom_node_id\x18\x02 \x01(\tR\n" +
	"fromNodeId\x12\x1c\n" +
	"\n" +
	"to_node_id\x18\x03 \x01(\tR\btoNodeId\x124\n" +
	"\x06reason\x18\x04 \x01(\x0e2\x1c.gpu.cluster.MigrationReasonR\x06reason\"\xac\x02\n" +
	"\rClusterConfig\x12\x1d\n" +
	"\n" +
	"cluster_id\x18\x01 \x01(\tR\tclusterId\x12D\n" +
	"\bsettings\x18\x02 \x03(\v2(.gpu.cluster.ClusterConfig.SettingsEntryR\bsettings\x123\n" +
	"\x15coordinator_endpoints\x18\x03 \x03(\tR\x14coordinatorEndpoints\x12D\n" +
	"\x0fsecurity_config\x18\x04 \x01(\v2\x1b.gpu.cluster.SecurityConfigR\x0esecurityConfig\x1a;\n" +
	"\rSettingsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x9b\x01\n" +
	"\x0eSecurityConfig\x12\x1f\n" +
	"\vtls_enabled\x18\x01 \x01(\bR\n" +
	"tlsEnabled\x12\x17\n" +
	"\aca_cert\x18\x02 \x01(\tR\x06caCert\x12\x1d\n" +
	"\n" +
	"mutual_tls\x18\x03 \x01(\bR\tmutualTls\x120\n" +
	"\x14token_expiry_seconds\x18\x04 \x01(\x05R\x12tokenExpirySeconds*\x97\x01\n" +
	"\bTaskType\x12\x19\n" +
	"\x15TASK_TYPE_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13TASK_TYPE_INFERENCE\x10\x01\x12\x16\n" +
	"\x12TASK_TYPE_TRAINING\x10\x02\x12\x1d\n" +
	"\x19TASK_TYPE_DATA_PROCESSING\x10\x03\x12 \n" +
	"\x1cTASK_TYPE_MODEL_OPTIMIZATION\x10\x04*y\n" +
	"\fTaskPriority\x12\x18\n" +
	"\x14PRIORITY_UNSPECIFIED\x10\x00\x12\x10\n" +
	"\fPRIORITY_LOW\x10\x01\x12\x13\n" +
	"\x0fPRIORITY_MEDIUM\x10\x02\x12\x11\n" +
	"\rPRIORITY_HIGH\x10\x03\x12\x15\n" +
	"\x11PRIORITY_CRITICAL\x10\x04*\xb6\x01\n" +
	"\n" +
	"TaskStatus\x12\x16\n" +
	"\x12STATUS_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0eSTATUS_PENDING\x10\x01\x12\x13\n" +
	"\x0fSTATUS_ASSIGNED\x10\x02\x12\x12\n" +
	"\x0eSTATUS_RUNNING\x10\x03\x12\x14\n" +
	"\x10STATUS_COMPLETED\x10\x04\x12\x11\n" +
	"\rSTATUS_FAILED\x10\x05\x12\x14\n" +
	"\x10STATUS_CANCELLED\x10\x06\x12\x14\n" +
	"\x10STATUS_MIGRATING\x10\a*\x88\x01\n" +
	"\tGPUStatus\x12\x1a\n" +
	"\x16GPU_STATUS_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14GPU_STATUS_AVAILABLE\x10\x01\x12\x13\n" +
	"\x0fGPU_STATUS_BUSY\x10\x02\x12\x14\n" +
	"\x10GPU_STATUS_ERROR\x10\x03\x12\x1a\n" +
	"\x16GPU_STATUS_MAINTENANCE\x10\x04*w\n" +
	"\n" +
	"NodeHealth\x12\x16\n" +
	"\x12HEALTH_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0eHEALTH_HEALTHY\x10\x01\x12\x13\n" +
	"\x0fHEALTH_DEGRADED\x10\x02\x12\x14\n" +
	"\x10HEALTH_UNHEALTHY\x10\x03\x12\x12\n" +
	"\x0eHEALTH_OFFLINE\x10\x04*\xc1\x01\n" +
	"\x0fAggregationType\x12 \n" +
	"\x1cAGGREGATION_TYPE_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14AGGREGATION_TYPE_SUM\x10\x01\x12\x1c\n" +
	"\x18AGGREGATION_TYPE_AVERAGE\x10\x02\x12\x1b\n" +
	"\x17AGGREGATION_TYPE_CONCAT\x10\x03\x12\x1a\n" +
	"\x16AGGREGATION_TYPE_MERGE\x10\x04\x12\x1b\n" +
	"\x17AGGREGATION_TYPE_CUSTOM\x10\x05*\xd2\x01\n" +
	"\x0fRebalanceReason\x12 \n" +
	"\x1cREBALANCE_REASON_UNSPECIFIED\x10\x00\x12#\n" +
	"\x1fREBALANCE_REASON_LOAD_IMBALANCE\x10\x01\x12!\n" +
	"\x1dREBALANCE_REASON_NODE_FAILURE\x10\x02\x12&\n" +
	"\"REBALANCE_REASON_RESOURCE_SHORTAGE\x10\x03\x12-\n" +
	")REBALANCE_REASON_PERFORMANCE_OPTIMIZATION\x10\x04*\xc9\x01\n" +
	"\x0fMigrationReason\x12 \n" +
	"\x1cMIGRATION_REASON_UNSPECIFIED\x10\x00\x12#\n" +
	"\x1fMIGRATION_REASON_LOAD_BALANCING\x10\x01\x12!\n" +
	"\x1dMIGRATION_REASON_NODE_FAILURE\x10\x02\x12*\n" +
	"&MIGRATION_REASON_RESOURCE_OPTIMIZATION\x10\x03\x12 \n" +
	"\x1cMIGRATION_REASON_MAINTENANCE\x10\x042\xc3\t\n" +
	"\x11GPUClusterService\x12U\n" +
	"\n" +
	"AssignTask\x12\".gpu.cluster.TaskAssignmentRequest\x1a#.gpu.cluster.TaskAssignmentResponse\x12R\n" +
	"\x10UpdateTaskStatus\x12\x1d.gpu.cluster.TaskStatusUpdate\x1a\x1f.gpu.cluster.TaskStatusResponse\x12W\n" +
	"\fCompleteTask\x12\".gpu.cluster.TaskCompletionRequest\x1a#.gpu.cluster.TaskCompletionResponse\x12Y\n" +
	"\n" +
	"CancelTask\x12$.gpu.cluster.TaskCancellationRequest\x1a%.gpu.cluster.TaskCancellationResponse\x12[\n" +
	"\fRegisterNode\x12$.gpu.cluster.NodeRegistrationRequest\x1a%.gpu.cluster.NodeRegistrationResponse\x12R\n" +
	"\x10UpdateNodeStatus\x12\x1d.gpu.cluster.NodeStatusUpdate\x1a\x1f.gpu.cluster.NodeStatusResponse\x12Y\n" +
	"\x10GetClusterStatus\x12!.gpu.cluster.ClusterStatusRequest\x1a\".gpu.cluster.ClusterStatusResponse\x12J\n" +
	"\tHeartBeat\x12\x1d.gpu.cluster.HeartBeatRequest\x1a\x1e.gpu.cluster.HeartBeatResponse\x12K\n" +
	"\fTransferData\x12\x16.gpu.cluster.DataChunk\x1a!.gpu.cluster.DataTransferResponse(\x01\x12A\n" +
	"\vRequestData\x12\x18.gpu.cluster.DataRequest\x1a\x16.gpu.cluster.DataChunk0\x01\x12[\n" +
	"\fSubmitResult\x12$.gpu.cluster.ResultSubmissionRequest\x1a%.gpu.cluster.ResultSubmissionResponse\x12a\n" +
	"\x10AggregateResults\x12%.gpu.cluster.ResultAggregationRequest\x1a&.gpu.cluster.ResultAggregationResponse\x12Q\n" +
	"\x10RequestRebalance\x12\x1d.gpu.cluster.RebalanceRequest\x1a\x1e.gpu.cluster.RebalanceResponse\x12T\n" +
	"\vMigrateTask\x12!.gpu.cluster.TaskMigrationRequest\x1a\".gpu.cluster.TaskMigrationResponseB,Z*neuralmetergo/internal/gpu/proto;clusterpbb\x06proto3"

var (
	file_cluster_proto_rawDescOnce sync.Once
	file_cluster_proto_rawDescData []byte
)

func file_cluster_proto_rawDescGZIP() []byte {
	file_cluster_proto_rawDescOnce.Do(func() {
		file_cluster_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cluster_proto_rawDesc), len(file_cluster_proto_rawDesc)))
	})
	return file_cluster_proto_rawDescData
}

var file_cluster_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_cluster_proto_msgTypes = make([]protoimpl.MessageInfo, 51)
var file_cluster_proto_goTypes = []any{
	(TaskType)(0),                     // 0: gpu.cluster.TaskType
	(TaskPriority)(0),                 // 1: gpu.cluster.TaskPriority
	(TaskStatus)(0),                   // 2: gpu.cluster.TaskStatus
	(GPUStatus)(0),                    // 3: gpu.cluster.GPUStatus
	(NodeHealth)(0),                   // 4: gpu.cluster.NodeHealth
	(AggregationType)(0),              // 5: gpu.cluster.AggregationType
	(RebalanceReason)(0),              // 6: gpu.cluster.RebalanceReason
	(MigrationReason)(0),              // 7: gpu.cluster.MigrationReason
	(*TaskAssignmentRequest)(nil),     // 8: gpu.cluster.TaskAssignmentRequest
	(*TaskAssignmentResponse)(nil),    // 9: gpu.cluster.TaskAssignmentResponse
	(*TaskSpec)(nil),                  // 10: gpu.cluster.TaskSpec
	(*ResourceRequirements)(nil),      // 11: gpu.cluster.ResourceRequirements
	(*NetworkRequirements)(nil),       // 12: gpu.cluster.NetworkRequirements
	(*TaskStatusUpdate)(nil),          // 13: gpu.cluster.TaskStatusUpdate
	(*TaskStatusResponse)(nil),        // 14: gpu.cluster.TaskStatusResponse
	(*TaskCompletionRequest)(nil),     // 15: gpu.cluster.TaskCompletionRequest
	(*TaskCompletionResponse)(nil),    // 16: gpu.cluster.TaskCompletionResponse
	(*TaskResult)(nil),                // 17: gpu.cluster.TaskResult
	(*TaskMetrics)(nil),               // 18: gpu.cluster.TaskMetrics
	(*TaskCancellationRequest)(nil),   // 19: gpu.cluster.TaskCancellationRequest
	(*TaskCancellationResponse)(nil),  // 20: gpu.cluster.TaskCancellationResponse
	(*NodeRegistrationRequest)(nil),   // 21: gpu.cluster.NodeRegistrationRequest
	(*NodeRegistrationResponse)(nil),  // 22: gpu.cluster.NodeRegistrationResponse
	(*NodeInfo)(nil),                  // 23: gpu.cluster.NodeInfo
	(*SystemInfo)(nil),                // 24: gpu.cluster.SystemInfo
	(*NetworkInfo)(nil),               // 25: gpu.cluster.NetworkInfo
	(*GPUDevice)(nil),                 // 26: gpu.cluster.GPUDevice
	(*NodeStatusUpdate)(nil),          // 27: gpu.cluster.NodeStatusUpdate
	(*NodeStatusResponse)(nil),        // 28: gpu.cluster.NodeStatusResponse
	(*WorkloadMetrics)(nil),           // 29: gpu.cluster.WorkloadMetrics
	(*ClusterStatusRequest)(nil),      // 30: gpu.cluster.ClusterStatusRequest
	(*ClusterStatusResponse)(nil),     // 31: gpu.cluster.ClusterStatusResponse
	(*ClusterInfo)(nil),               // 32: gpu.cluster.ClusterInfo
	(*NodeStatus)(nil),                // 33: gpu.cluster.NodeStatus
	(*ClusterMetrics)(nil),            // 34: gpu.cluster.ClusterMetrics
	(*HeartBeatRequest)(nil),          // 35: gpu.cluster.HeartBeatRequest
	(*HeartBeatResponse)(nil),         // 36: gpu.cluster.HeartBeatResponse
	(*DataChunk)(nil),                 // 37: gpu.cluster.DataChunk
	(*DataRequest)(nil),               // 38: gpu.cluster.DataRequest
	(*DataTransferResponse)(nil),      // 39: gpu.cluster.DataTransferResponse
	(*ResultSubmissionRequest)(nil),   // 40: gpu.cluster.ResultSubmissionRequest
	(*ResultSubmissionResponse)(nil),  // 41: gpu.cluster.ResultSubmissionResponse
	(*ResultAggregationRequest)(nil),  // 42: gpu.cluster.ResultAggregationRequest
	(*ResultAggregationResponse)(nil), // 43: gpu.cluster.ResultAggregationResponse
	(*AggregatedResult)(nil),          // 44: gpu.cluster.AggregatedResult
	(*RebalanceRequest)(nil),          // 45: gpu.cluster.RebalanceRequest
	(*RebalanceResponse)(nil),         // 46: gpu.cluster.RebalanceResponse
	(*TaskMigrationRequest)(nil),      // 47: gpu.cluster.TaskMigrationRequest
	(*TaskMigrationResponse)(nil),     // 48: gpu.cluster.TaskMigrationResponse
	(*TaskMigration)(nil),             // 49: gpu.cluster.TaskMigration
	(*ClusterConfig)(nil),             // 50: gpu.cluster.ClusterConfig
	(*SecurityConfig)(nil),            // 51: gpu.cluster.SecurityConfig
	nil,                               // 52: gpu.cluster.TaskSpec.ParametersEntry
	nil,                               // 53: gpu.cluster.TaskStatusUpdate.MetricsEntry
	nil,                               // 54: gpu.cluster.TaskResult.ResultMetadataEntry
	nil,                               // 55: gpu.cluster.NodeInfo.CapabilitiesEntry
	nil,                               // 56: gpu.cluster.ResultAggregationRequest.ParametersEntry
	nil,                               // 57: gpu.cluster.AggregatedResult.MetadataEntry
	nil,                               // 58: gpu.cluster.ClusterConfig.SettingsEntry
	(*timestamppb.Timestamp)(nil),     // 59: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),       // 60: google.protobuf.Duration
}
var file_cluster_proto_depIdxs = []int32{
	10, // 0: gpu.cluster.TaskAssignmentRequest.task_spec:type_name -> gpu.cluster.TaskSpec
	1,  // 1: gpu.cluster.TaskAssignmentRequest.priority:type_name -> gpu.cluster.TaskPriority
	59, // 2: gpu.cluster.TaskAssignmentRequest.deadline:type_name -> google.protobuf.Timestamp
	59, // 3: gpu.cluster.TaskAssignmentResponse.estimated_start_time:type_name -> google.protobuf.Timestamp
	60, // 4: gpu.cluster.TaskAssignmentResponse.estimated_duration:type_name -> google.protobuf.Duration
	0,  // 5: gpu.cluster.TaskSpec.type:type_name -> gpu.cluster.TaskType
	11, // 6: gpu.cluster.TaskSpec.resource_requirements:type_name -> gpu.cluster.ResourceRequirements
	52, // 7: gpu.cluster.TaskSpec.parameters:type_name -> gpu.cluster.TaskSpec.ParametersEntry
	12, // 8: gpu.cluster.ResourceRequirements.network_requirements:type_name -> gpu.cluster.NetworkRequirements
	2,  // 9: gpu.cluster.TaskStatusUpdate.status:type_name -> gpu.cluster.TaskStatus
	59, // 10: gpu.cluster.TaskStatusUpdate.timestamp:type_name -> google.protobuf.Timestamp
	53, // 11: gpu.cluster.TaskStatusUpdate.metrics:type_name -> gpu.cluster.TaskStatusUpdate.MetricsEntry
	17, // 12: gpu.cluster.TaskCompletionRequest.result:type_name -> gpu.cluster.TaskResult
	59, // 13: gpu.cluster.TaskCompletionRequest.completion_time:type_name -> google.protobuf.Timestamp
	18, // 14: gpu.cluster.TaskCompletionRequest.metrics:type_name -> gpu.cluster.TaskMetrics
	54, // 15: gpu.cluster.TaskResult.result_metadata:type_name -> gpu.cluster.TaskResult.ResultMetadataEntry
	60, // 16: gpu.cluster.TaskMetrics.execution_time:type_name -> google.protobuf.Duration
	23, // 17: gpu.cluster.NodeRegistrationRequest.node_info:type_name -> gpu.cluster.NodeInfo
	26, // 18: gpu.cluster.NodeRegistrationRequest.gpu_devices:type_name -> gpu.cluster.GPUDevice
	50, // 19: gpu.cluster.NodeRegistrationResponse.cluster_config:type_name -> gpu.cluster.ClusterConfig
	24, // 20: gpu.cluster.NodeInfo.system_info:type_name -> gpu.cluster.SystemInfo
	25, // 21: gpu.cluster.NodeInfo.network_info:type_name -> gpu.cluster.NetworkInfo
	55, // 22: gpu.cluster.NodeInfo.capabilities:type_name -> gpu.cluster.NodeInfo.CapabilitiesEntry
	60, // 23: gpu.cluster.SystemInfo.uptime:type_name -> google.protobuf.Duration
	3,  // 24: gpu.cluster.GPUDevice.status:type_name -> gpu.cluster.GPUStatus
	24, // 25: gpu.cluster.NodeStatusUpdate.system_info:type_name -> gpu.cluster.SystemInfo
	26, // 26: gpu.cluster.NodeStatusUpdate.gpu_devices:type_name -> gpu.cluster.GPUDevice
	29, // 27: gpu.cluster.NodeStatusUpdate.workload_metrics:type_name -> gpu.cluster.WorkloadMetrics
	59, // 28: gpu.cluster.NodeStatusUpdate.timestamp:type_name -> google.protobuf.Timestamp
	60, // 29: gpu.cluster.WorkloadMetrics.average_task_time:type_name -> google.protobuf.Duration
	32, // 30: gpu.cluster.ClusterStatusResponse.cluster_info:type_name -> gpu.cluster.ClusterInfo
	33, // 31: gpu.cluster.ClusterStatusResponse.node_statuses:type_name -> gpu.cluster.NodeStatus
	34, // 32: gpu.cluster.ClusterStatusResponse.cluster_metrics:type_name -> gpu.cluster.ClusterMetrics
	59, // 33: gpu.cluster.ClusterInfo.last_updated:type_name -> google.protobuf.Timestamp
	4,  // 34: gpu.cluster.NodeStatus.health:type_name -> gpu.cluster.NodeHealth
	24, // 35: gpu.cluster.NodeStatus.system_info:type_name -> gpu.cluster.SystemInfo
	26, // 36: gpu.cluster.NodeStatus.gpu_devices:type_name -> gpu.cluster.GPUDevice
	29, // 37: gpu.cluster.NodeStatus.workload_metrics:type_name -> gpu.cluster.WorkloadMetrics
	59, // 38: gpu.cluster.NodeStatus.last_heartbeat:type_name -> google.protobuf.Timestamp
	59, // 39: gpu.cluster.HeartBeatRequest.timestamp:type_name -> google.protobuf.Timestamp
	4,  // 40: gpu.cluster.HeartBeatRequest.health:type_name -> gpu.cluster.NodeHealth
	59, // 41: gpu.cluster.HeartBeatResponse.server_timestamp:type_name -> google.protobuf.Timestamp
	17, // 42: gpu.cluster.ResultSubmissionRequest.result:type_name -> gpu.cluster.TaskResult
	5,  // 43: gpu.cluster.ResultAggregationRequest.aggregation_type:type_name -> gpu.cluster.AggregationType
	56, // 44: gpu.cluster.ResultAggregationRequest.parameters:type_name -> gpu.cluster.ResultAggregationRequest.ParametersEntry
	5,  // 45: gpu.cluster.AggregatedResult.type:type_name -> gpu.cluster.AggregationType
	57, // 46: gpu.cluster.AggregatedResult.metadata:type_name -> gpu.cluster.AggregatedResult.MetadataEntry
	6,  // 47: gpu.cluster.RebalanceRequest.reason:type_name -> gpu.cluster.RebalanceReason
	49, // 48: gpu.cluster.RebalanceResponse.migrations:type_name -> gpu.cluster.TaskMigration
	7,  // 49: gpu.cluster.TaskMigrationRequest.reason:type_name -> gpu.cluster.MigrationReason
	59, // 50: gpu.cluster.TaskMigrationResponse.migration_start_time:type_name -> google.protobuf.Timestamp
	60, // 51: gpu.cluster.TaskMigrationResponse.estimated_migration_time:type_name -> google.protobuf.Duration
	7,  // 52: gpu.cluster.TaskMigration.reason:type_name -> gpu.cluster.MigrationReason
	58, // 53: gpu.cluster.ClusterConfig.settings:type_name -> gpu.cluster.ClusterConfig.SettingsEntry
	51, // 54: gpu.cluster.ClusterConfig.security_config:type_name -> gpu.cluster.SecurityConfig
	8,  // 55: gpu.cluster.GPUClusterService.AssignTask:input_type -> gpu.cluster.TaskAssignmentRequest
	13, // 56: gpu.cluster.GPUClusterService.UpdateTaskStatus:input_type -> gpu.cluster.TaskStatusUpdate
	15, // 57: gpu.cluster.GPUClusterService.CompleteTask:input_type -> gpu.cluster.TaskCompletionRequest
	19, // 58: gpu.cluster.GPUClusterService.CancelTask:input_type -> gpu.cluster.TaskCancellationRequest
	21, // 59: gpu.cluster.GPUClusterService.RegisterNode:input_type -> gpu.cluster.NodeRegistrationRequest
	27, // 60: gpu.cluster.GPUClusterService.UpdateNodeStatus:input_type -> gpu.cluster.NodeStatusUpdate
	30, // 61: gpu.cluster.GPUClusterService.GetClusterStatus:input_type -> gpu.cluster.ClusterStatusRequest
	35, // 62: gpu.cluster.GPUClusterService.HeartBeat:input_type -> gpu.cluster.HeartBeatRequest
	37, // 63: gpu.cluster.GPUClusterService.TransferData:input_type -> gpu.cluster.DataChunk
	38, // 64: gpu.cluster.GPUClusterService.RequestData:input_type -> gpu.cluster.DataRequest
	40, // 65: gpu.cluster.GPUClusterService.SubmitResult:input_type -> gpu.cluster.ResultSubmissionRequest
	42, // 66: gpu.cluster.GPUClusterService.AggregateResults:input_type -> gpu.cluster.ResultAggregationRequest
	45, // 67: gpu.cluster.GPUClusterService.RequestRebalance:input_type -> gpu.cluster.RebalanceRequest
	47, // 68: gpu.cluster.GPUClusterService.MigrateTask:input_type -> gpu.cluster.TaskMigrationRequest
	9,  // 69: gpu.cluster.GPUClusterService.AssignTask:output_type -> gpu.cluster.TaskAssignmentResponse
	14, // 70: gpu.cluster.GPUClusterService.UpdateTaskStatus:output_type -> gpu.cluster.TaskStatusResponse
	16, // 71: gpu.cluster.GPUClusterService.CompleteTask:output_type -> gpu.cluster.TaskCompletionResponse
	20, // 72: gpu.cluster.GPUClusterService.CancelTask:output_type -> gpu.cluster.TaskCancellationResponse
	22, // 73: gpu.cluster.GPUClusterService.RegisterNode:output_type -> gpu.cluster.NodeRegistrationResponse
	28, // 74: gpu.cluster.GPUClusterService.UpdateNodeStatus:output_type -> gpu.cluster.NodeStatusResponse
	31, // 75: gpu.cluster.GPUClusterService.GetClusterStatus:output_type -> gpu.cluster.ClusterStatusResponse
	36, // 76: gpu.cluster.GPUClusterService.HeartBeat:output_type -> gpu.cluster.HeartBeatResponse
	39, // 77: gpu.cluster.GPUClusterService.TransferData:output_type -> gpu.cluster.DataTransferResponse
	37, // 78: gpu.cluster.GPUClusterService.RequestData:output_type -> gpu.cluster.DataChunk
	41, // 79: gpu.cluster.GPUClusterService.SubmitResult:output_type -> gpu.cluster.ResultSubmissionResponse
	43, // 80: gpu.cluster.GPUClusterService.AggregateResults:output_type -> gpu.cluster.ResultAggregationResponse
	46, // 81: gpu.cluster.GPUClusterService.RequestRebalance:output_type -> gpu.cluster.RebalanceResponse
	48, // 82: gpu.cluster.GPUClusterService.MigrateTask:output_type -> gpu.cluster.TaskMigrationResponse
	69, // [69:83] is the sub-list for method output_type
	55, // [55:69] is the sub-list for method input_type
	55, // [55:55] is the sub-list for extension type_name
	55, // [55:55] is the sub-list for extension extendee
	0,  // [0:55] is the sub-list for field type_name
}

func init() { file_cluster_proto_init() }
func file_cluster_proto_init() {
	if File_cluster_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cluster_proto_rawDesc), len(file_cluster_proto_rawDesc)),
			NumEnums:      8,
			NumMessages:   51,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_cluster_proto_goTypes,
		DependencyIndexes: file_cluster_proto_depIdxs,
		EnumInfos:         file_cluster_proto_enumTypes,
		MessageInfos:      file_cluster_proto_msgTypes,
	}.Build()
	File_cluster_proto = out.File
	file_cluster_proto_goTypes = nil
	file_cluster_proto_depIdxs = nil
}
