syntax = "proto3";

package gpu.cluster;

option go_package = "neuralmetergo/internal/gpu/proto;clusterpb";

import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";

// GPU Cluster Service Definition
service GPUClusterService {
  // Task Management
  rpc AssignTask(TaskAssignmentRequest) returns (TaskAssignmentResponse);
  rpc UpdateTaskStatus(TaskStatusUpdate) returns (TaskStatusResponse);
  rpc CompleteTask(TaskCompletionRequest) returns (TaskCompletionResponse);
  rpc CancelTask(TaskCancellationRequest) returns (TaskCancellationResponse);
  
  // Resource Management
  rpc RegisterNode(NodeRegistrationRequest) returns (NodeRegistrationResponse);
  rpc UpdateNodeStatus(NodeStatusUpdate) returns (NodeStatusResponse);
  rpc GetClusterStatus(ClusterStatusRequest) returns (ClusterStatusResponse);
  rpc HeartBeat(HeartBeatRequest) returns (HeartBeatResponse);
  
  // Data Transfer
  rpc TransferData(stream DataChunk) returns (DataTransferResponse);
  rpc RequestData(DataRequest) returns (stream DataChunk);
  
  // Result Aggregation
  rpc SubmitResult(ResultSubmissionRequest) returns (ResultSubmissionResponse);
  rpc AggregateResults(ResultAggregationRequest) returns (ResultAggregationResponse);
  
  // Load Balancing
  rpc RequestRebalance(RebalanceRequest) returns (RebalanceResponse);
  rpc MigrateTask(TaskMigrationRequest) returns (TaskMigrationResponse);
}

// Message Types

// Task Assignment
message TaskAssignmentRequest {
  string task_id = 1;
  TaskSpec task_spec = 2;
  repeated string preferred_nodes = 3;
  repeated string excluded_nodes = 4;
  TaskPriority priority = 5;
  google.protobuf.Timestamp deadline = 6;
}

message TaskAssignmentResponse {
  bool success = 1;
  string assigned_node_id = 2;
  string message = 3;
  google.protobuf.Timestamp estimated_start_time = 4;
  google.protobuf.Duration estimated_duration = 5;
}

message TaskSpec {
  string id = 1;
  string name = 2;
  string description = 3;
  TaskType type = 4;
  ResourceRequirements resource_requirements = 5;
  map<string, string> parameters = 6;
  repeated string input_data_urls = 7;
  string output_location = 8;
  repeated string dependencies = 9;
}

message ResourceRequirements {
  int64 gpu_memory_mb = 1;
  int64 system_memory_mb = 2;
  int32 gpu_compute_units = 3;
  float min_gpu_utilization = 4;
  repeated string required_gpu_capabilities = 5;
  NetworkRequirements network_requirements = 6;
}

message NetworkRequirements {
  float min_bandwidth_mbps = 1;
  float max_latency_ms = 2;
  float max_packet_loss_percent = 3;
}

enum TaskType {
  TASK_TYPE_UNSPECIFIED = 0;
  TASK_TYPE_INFERENCE = 1;
  TASK_TYPE_TRAINING = 2;
  TASK_TYPE_DATA_PROCESSING = 3;
  TASK_TYPE_MODEL_OPTIMIZATION = 4;
}

enum TaskPriority {
  PRIORITY_UNSPECIFIED = 0;
  PRIORITY_LOW = 1;
  PRIORITY_MEDIUM = 2;
  PRIORITY_HIGH = 3;
  PRIORITY_CRITICAL = 4;
}

enum TaskStatus {
  STATUS_UNSPECIFIED = 0;
  STATUS_PENDING = 1;
  STATUS_ASSIGNED = 2;
  STATUS_RUNNING = 3;
  STATUS_COMPLETED = 4;
  STATUS_FAILED = 5;
  STATUS_CANCELLED = 6;
  STATUS_MIGRATING = 7;
}

// Task Status Updates
message TaskStatusUpdate {
  string task_id = 1;
  string node_id = 2;
  TaskStatus status = 3;
  float progress_percent = 4;
  string message = 5;
  google.protobuf.Timestamp timestamp = 6;
  map<string, string> metrics = 7;
}

message TaskStatusResponse {
  bool success = 1;
  string message = 2;
}

// Task Completion
message TaskCompletionRequest {
  string task_id = 1;
  string node_id = 2;
  TaskResult result = 3;
  google.protobuf.Timestamp completion_time = 4;
  TaskMetrics metrics = 5;
}

message TaskCompletionResponse {
  bool success = 1;
  string message = 2;
  bool result_accepted = 3;
}

message TaskResult {
  bool success = 1;
  string error_message = 2;
  repeated string output_data_urls = 3;
  map<string, string> result_metadata = 4;
  bytes result_data = 5;
}

message TaskMetrics {
  google.protobuf.Duration execution_time = 1;
  int64 gpu_memory_used_mb = 2;
  int64 system_memory_used_mb = 3;
  float gpu_utilization_percent = 4;
  float cpu_utilization_percent = 5;
  int64 data_transferred_bytes = 6;
  float power_consumption_watts = 7;
}

// Task Cancellation
message TaskCancellationRequest {
  string task_id = 1;
  string reason = 2;
  bool force = 3;
}

message TaskCancellationResponse {
  bool success = 1;
  string message = 2;
  bool task_was_running = 3;
}

// Node Registration
message NodeRegistrationRequest {
  NodeInfo node_info = 1;
  repeated GPUDevice gpu_devices = 2;
  string authentication_token = 3;
}

message NodeRegistrationResponse {
  bool success = 1;
  string node_id = 2;
  string message = 3;
  ClusterConfig cluster_config = 4;
}

message NodeInfo {
  string node_id = 1;
  string hostname = 2;
  string ip_address = 3;
  int32 port = 4;
  SystemInfo system_info = 5;
  NetworkInfo network_info = 6;
  map<string, string> capabilities = 7;
}

message SystemInfo {
  string os = 1;
  string architecture = 2;
  int32 cpu_cores = 3;
  int64 total_memory_mb = 4;
  int64 available_memory_mb = 5;
  float load_average = 6;
  google.protobuf.Duration uptime = 7;
  string driver_version = 8;
}

message NetworkInfo {
  float bandwidth_mbps = 1;
  float latency_ms = 2;
  float packet_loss_percent = 3;
  repeated string interfaces = 4;
}

message GPUDevice {
  string device_id = 1;
  string name = 2;
  string vendor = 3;
  string model = 4;
  int64 memory_mb = 5;
  int64 available_memory_mb = 6;
  int32 compute_units = 7;
  float utilization_percent = 8;
  float temperature_celsius = 9;
  float power_usage_watts = 10;
  repeated string capabilities = 11;
  GPUStatus status = 12;
}

enum GPUStatus {
  GPU_STATUS_UNSPECIFIED = 0;
  GPU_STATUS_AVAILABLE = 1;
  GPU_STATUS_BUSY = 2;
  GPU_STATUS_ERROR = 3;
  GPU_STATUS_MAINTENANCE = 4;
}

// Node Status Updates
message NodeStatusUpdate {
  string node_id = 1;
  SystemInfo system_info = 2;
  repeated GPUDevice gpu_devices = 3;
  WorkloadMetrics workload_metrics = 4;
  google.protobuf.Timestamp timestamp = 5;
}

message NodeStatusResponse {
  bool success = 1;
  string message = 2;
}

message WorkloadMetrics {
  int32 active_tasks = 1;
  int32 queued_tasks = 2;
  int32 completed_tasks = 3;
  int32 failed_tasks = 4;
  float throughput_tasks_per_sec = 5;
  google.protobuf.Duration average_task_time = 6;
  float success_rate_percent = 7;
}

// Cluster Status
message ClusterStatusRequest {
  bool include_detailed_metrics = 1;
  repeated string node_filters = 2;
}

message ClusterStatusResponse {
  ClusterInfo cluster_info = 1;
  repeated NodeStatus node_statuses = 2;
  ClusterMetrics cluster_metrics = 3;
}

message ClusterInfo {
  string cluster_id = 1;
  string name = 2;
  int32 total_nodes = 3;
  int32 active_nodes = 4;
  int32 total_gpus = 5;
  int32 available_gpus = 6;
  google.protobuf.Timestamp last_updated = 7;
}

message NodeStatus {
  string node_id = 1;
  NodeHealth health = 2;
  SystemInfo system_info = 3;
  repeated GPUDevice gpu_devices = 4;
  WorkloadMetrics workload_metrics = 5;
  google.protobuf.Timestamp last_heartbeat = 6;
}

enum NodeHealth {
  HEALTH_UNSPECIFIED = 0;
  HEALTH_HEALTHY = 1;
  HEALTH_DEGRADED = 2;
  HEALTH_UNHEALTHY = 3;
  HEALTH_OFFLINE = 4;
}

message ClusterMetrics {
  int32 total_tasks_running = 1;
  int32 total_tasks_queued = 2;
  float cluster_utilization_percent = 3;
  float average_task_completion_time_sec = 4;
  float cluster_throughput_tasks_per_sec = 5;
  int64 total_data_transferred_bytes = 6;
}

// Heartbeat
message HeartBeatRequest {
  string node_id = 1;
  google.protobuf.Timestamp timestamp = 2;
  NodeHealth health = 3;
  repeated string active_task_ids = 4;
}

message HeartBeatResponse {
  bool success = 1;
  google.protobuf.Timestamp server_timestamp = 2;
  repeated string commands = 3;
}

// Data Transfer
message DataChunk {
  string transfer_id = 1;
  int64 chunk_index = 2;
  int64 total_chunks = 3;
  bytes data = 4;
  string checksum = 5;
  bool is_final = 6;
}

message DataRequest {
  string data_id = 1;
  string requesting_node_id = 2;
  int64 offset = 3;
  int64 length = 4;
}

message DataTransferResponse {
  bool success = 1;
  string transfer_id = 2;
  string message = 3;
  int64 bytes_received = 4;
}

// Result Aggregation
message ResultSubmissionRequest {
  string task_id = 1;
  string node_id = 2;
  TaskResult result = 3;
  bool is_partial = 4;
  int32 part_index = 5;
  int32 total_parts = 6;
}

message ResultSubmissionResponse {
  bool success = 1;
  string message = 2;
  bool aggregation_complete = 3;
}

message ResultAggregationRequest {
  string aggregation_id = 1;
  repeated string task_ids = 2;
  AggregationType aggregation_type = 3;
  map<string, string> parameters = 4;
}

message ResultAggregationResponse {
  bool success = 1;
  string message = 2;
  string aggregation_id = 3;
  int32 partial_results_count = 4;
}

message AggregatedResult {
  string aggregation_id = 1;
  AggregationType type = 2;
  bytes aggregated_data = 3;
  map<string, string> metadata = 4;
  bool is_final = 5;
  float progress_percent = 6;
}

enum AggregationType {
  AGGREGATION_TYPE_UNSPECIFIED = 0;
  AGGREGATION_TYPE_SUM = 1;
  AGGREGATION_TYPE_AVERAGE = 2;
  AGGREGATION_TYPE_CONCAT = 3;
  AGGREGATION_TYPE_MERGE = 4;
  AGGREGATION_TYPE_CUSTOM = 5;
}

// Load Balancing
message RebalanceRequest {
  string initiating_node_id = 1;
  RebalanceReason reason = 2;
  repeated string affected_task_ids = 3;
}

message RebalanceResponse {
  bool success = 1;
  string message = 2;
  repeated TaskMigration migrations = 3;
}

message TaskMigrationRequest {
  string task_id = 1;
  string source_node_id = 2;
  string target_node_id = 3;
  MigrationReason reason = 4;
  bool preserve_state = 5;
}

message TaskMigrationResponse {
  bool success = 1;
  string message = 2;
  google.protobuf.Timestamp migration_start_time = 3;
  google.protobuf.Duration estimated_migration_time = 4;
}

message TaskMigration {
  string task_id = 1;
  string from_node_id = 2;
  string to_node_id = 3;
  MigrationReason reason = 4;
}

enum RebalanceReason {
  REBALANCE_REASON_UNSPECIFIED = 0;
  REBALANCE_REASON_LOAD_IMBALANCE = 1;
  REBALANCE_REASON_NODE_FAILURE = 2;
  REBALANCE_REASON_RESOURCE_SHORTAGE = 3;
  REBALANCE_REASON_PERFORMANCE_OPTIMIZATION = 4;
}

enum MigrationReason {
  MIGRATION_REASON_UNSPECIFIED = 0;
  MIGRATION_REASON_LOAD_BALANCING = 1;
  MIGRATION_REASON_NODE_FAILURE = 2;
  MIGRATION_REASON_RESOURCE_OPTIMIZATION = 3;
  MIGRATION_REASON_MAINTENANCE = 4;
}

// Configuration
message ClusterConfig {
  string cluster_id = 1;
  map<string, string> settings = 2;
  repeated string coordinator_endpoints = 3;
  SecurityConfig security_config = 4;
}

message SecurityConfig {
  bool tls_enabled = 1;
  string ca_cert = 2;
  bool mutual_tls = 3;
  int32 token_expiry_seconds = 4;
} 