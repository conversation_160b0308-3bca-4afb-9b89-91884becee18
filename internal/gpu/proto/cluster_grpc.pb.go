// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: cluster.proto

package clusterpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	GPUClusterService_AssignTask_FullMethodName       = "/gpu.cluster.GPUClusterService/AssignTask"
	GPUClusterService_UpdateTaskStatus_FullMethodName = "/gpu.cluster.GPUClusterService/UpdateTaskStatus"
	GPUClusterService_CompleteTask_FullMethodName     = "/gpu.cluster.GPUClusterService/CompleteTask"
	GPUClusterService_CancelTask_FullMethodName       = "/gpu.cluster.GPUClusterService/CancelTask"
	GPUClusterService_RegisterNode_FullMethodName     = "/gpu.cluster.GPUClusterService/RegisterNode"
	GPUClusterService_UpdateNodeStatus_FullMethodName = "/gpu.cluster.GPUClusterService/UpdateNodeStatus"
	GPUClusterService_GetClusterStatus_FullMethodName = "/gpu.cluster.GPUClusterService/GetClusterStatus"
	GPUClusterService_HeartBeat_FullMethodName        = "/gpu.cluster.GPUClusterService/HeartBeat"
	GPUClusterService_TransferData_FullMethodName     = "/gpu.cluster.GPUClusterService/TransferData"
	GPUClusterService_RequestData_FullMethodName      = "/gpu.cluster.GPUClusterService/RequestData"
	GPUClusterService_SubmitResult_FullMethodName     = "/gpu.cluster.GPUClusterService/SubmitResult"
	GPUClusterService_AggregateResults_FullMethodName = "/gpu.cluster.GPUClusterService/AggregateResults"
	GPUClusterService_RequestRebalance_FullMethodName = "/gpu.cluster.GPUClusterService/RequestRebalance"
	GPUClusterService_MigrateTask_FullMethodName      = "/gpu.cluster.GPUClusterService/MigrateTask"
)

// GPUClusterServiceClient is the client API for GPUClusterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// GPU Cluster Service Definition
type GPUClusterServiceClient interface {
	// Task Management
	AssignTask(ctx context.Context, in *TaskAssignmentRequest, opts ...grpc.CallOption) (*TaskAssignmentResponse, error)
	UpdateTaskStatus(ctx context.Context, in *TaskStatusUpdate, opts ...grpc.CallOption) (*TaskStatusResponse, error)
	CompleteTask(ctx context.Context, in *TaskCompletionRequest, opts ...grpc.CallOption) (*TaskCompletionResponse, error)
	CancelTask(ctx context.Context, in *TaskCancellationRequest, opts ...grpc.CallOption) (*TaskCancellationResponse, error)
	// Resource Management
	RegisterNode(ctx context.Context, in *NodeRegistrationRequest, opts ...grpc.CallOption) (*NodeRegistrationResponse, error)
	UpdateNodeStatus(ctx context.Context, in *NodeStatusUpdate, opts ...grpc.CallOption) (*NodeStatusResponse, error)
	GetClusterStatus(ctx context.Context, in *ClusterStatusRequest, opts ...grpc.CallOption) (*ClusterStatusResponse, error)
	HeartBeat(ctx context.Context, in *HeartBeatRequest, opts ...grpc.CallOption) (*HeartBeatResponse, error)
	// Data Transfer
	TransferData(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[DataChunk, DataTransferResponse], error)
	RequestData(ctx context.Context, in *DataRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[DataChunk], error)
	// Result Aggregation
	SubmitResult(ctx context.Context, in *ResultSubmissionRequest, opts ...grpc.CallOption) (*ResultSubmissionResponse, error)
	AggregateResults(ctx context.Context, in *ResultAggregationRequest, opts ...grpc.CallOption) (*ResultAggregationResponse, error)
	// Load Balancing
	RequestRebalance(ctx context.Context, in *RebalanceRequest, opts ...grpc.CallOption) (*RebalanceResponse, error)
	MigrateTask(ctx context.Context, in *TaskMigrationRequest, opts ...grpc.CallOption) (*TaskMigrationResponse, error)
}

type gPUClusterServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGPUClusterServiceClient(cc grpc.ClientConnInterface) GPUClusterServiceClient {
	return &gPUClusterServiceClient{cc}
}

func (c *gPUClusterServiceClient) AssignTask(ctx context.Context, in *TaskAssignmentRequest, opts ...grpc.CallOption) (*TaskAssignmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TaskAssignmentResponse)
	err := c.cc.Invoke(ctx, GPUClusterService_AssignTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gPUClusterServiceClient) UpdateTaskStatus(ctx context.Context, in *TaskStatusUpdate, opts ...grpc.CallOption) (*TaskStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TaskStatusResponse)
	err := c.cc.Invoke(ctx, GPUClusterService_UpdateTaskStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gPUClusterServiceClient) CompleteTask(ctx context.Context, in *TaskCompletionRequest, opts ...grpc.CallOption) (*TaskCompletionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TaskCompletionResponse)
	err := c.cc.Invoke(ctx, GPUClusterService_CompleteTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gPUClusterServiceClient) CancelTask(ctx context.Context, in *TaskCancellationRequest, opts ...grpc.CallOption) (*TaskCancellationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TaskCancellationResponse)
	err := c.cc.Invoke(ctx, GPUClusterService_CancelTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gPUClusterServiceClient) RegisterNode(ctx context.Context, in *NodeRegistrationRequest, opts ...grpc.CallOption) (*NodeRegistrationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NodeRegistrationResponse)
	err := c.cc.Invoke(ctx, GPUClusterService_RegisterNode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gPUClusterServiceClient) UpdateNodeStatus(ctx context.Context, in *NodeStatusUpdate, opts ...grpc.CallOption) (*NodeStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NodeStatusResponse)
	err := c.cc.Invoke(ctx, GPUClusterService_UpdateNodeStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gPUClusterServiceClient) GetClusterStatus(ctx context.Context, in *ClusterStatusRequest, opts ...grpc.CallOption) (*ClusterStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ClusterStatusResponse)
	err := c.cc.Invoke(ctx, GPUClusterService_GetClusterStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gPUClusterServiceClient) HeartBeat(ctx context.Context, in *HeartBeatRequest, opts ...grpc.CallOption) (*HeartBeatResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HeartBeatResponse)
	err := c.cc.Invoke(ctx, GPUClusterService_HeartBeat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gPUClusterServiceClient) TransferData(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[DataChunk, DataTransferResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &GPUClusterService_ServiceDesc.Streams[0], GPUClusterService_TransferData_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[DataChunk, DataTransferResponse]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type GPUClusterService_TransferDataClient = grpc.ClientStreamingClient[DataChunk, DataTransferResponse]

func (c *gPUClusterServiceClient) RequestData(ctx context.Context, in *DataRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[DataChunk], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &GPUClusterService_ServiceDesc.Streams[1], GPUClusterService_RequestData_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[DataRequest, DataChunk]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type GPUClusterService_RequestDataClient = grpc.ServerStreamingClient[DataChunk]

func (c *gPUClusterServiceClient) SubmitResult(ctx context.Context, in *ResultSubmissionRequest, opts ...grpc.CallOption) (*ResultSubmissionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResultSubmissionResponse)
	err := c.cc.Invoke(ctx, GPUClusterService_SubmitResult_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gPUClusterServiceClient) AggregateResults(ctx context.Context, in *ResultAggregationRequest, opts ...grpc.CallOption) (*ResultAggregationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResultAggregationResponse)
	err := c.cc.Invoke(ctx, GPUClusterService_AggregateResults_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gPUClusterServiceClient) RequestRebalance(ctx context.Context, in *RebalanceRequest, opts ...grpc.CallOption) (*RebalanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RebalanceResponse)
	err := c.cc.Invoke(ctx, GPUClusterService_RequestRebalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gPUClusterServiceClient) MigrateTask(ctx context.Context, in *TaskMigrationRequest, opts ...grpc.CallOption) (*TaskMigrationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TaskMigrationResponse)
	err := c.cc.Invoke(ctx, GPUClusterService_MigrateTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GPUClusterServiceServer is the server API for GPUClusterService service.
// All implementations must embed UnimplementedGPUClusterServiceServer
// for forward compatibility.
//
// GPU Cluster Service Definition
type GPUClusterServiceServer interface {
	// Task Management
	AssignTask(context.Context, *TaskAssignmentRequest) (*TaskAssignmentResponse, error)
	UpdateTaskStatus(context.Context, *TaskStatusUpdate) (*TaskStatusResponse, error)
	CompleteTask(context.Context, *TaskCompletionRequest) (*TaskCompletionResponse, error)
	CancelTask(context.Context, *TaskCancellationRequest) (*TaskCancellationResponse, error)
	// Resource Management
	RegisterNode(context.Context, *NodeRegistrationRequest) (*NodeRegistrationResponse, error)
	UpdateNodeStatus(context.Context, *NodeStatusUpdate) (*NodeStatusResponse, error)
	GetClusterStatus(context.Context, *ClusterStatusRequest) (*ClusterStatusResponse, error)
	HeartBeat(context.Context, *HeartBeatRequest) (*HeartBeatResponse, error)
	// Data Transfer
	TransferData(grpc.ClientStreamingServer[DataChunk, DataTransferResponse]) error
	RequestData(*DataRequest, grpc.ServerStreamingServer[DataChunk]) error
	// Result Aggregation
	SubmitResult(context.Context, *ResultSubmissionRequest) (*ResultSubmissionResponse, error)
	AggregateResults(context.Context, *ResultAggregationRequest) (*ResultAggregationResponse, error)
	// Load Balancing
	RequestRebalance(context.Context, *RebalanceRequest) (*RebalanceResponse, error)
	MigrateTask(context.Context, *TaskMigrationRequest) (*TaskMigrationResponse, error)
	mustEmbedUnimplementedGPUClusterServiceServer()
}

// UnimplementedGPUClusterServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedGPUClusterServiceServer struct{}

func (UnimplementedGPUClusterServiceServer) AssignTask(context.Context, *TaskAssignmentRequest) (*TaskAssignmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AssignTask not implemented")
}
func (UnimplementedGPUClusterServiceServer) UpdateTaskStatus(context.Context, *TaskStatusUpdate) (*TaskStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTaskStatus not implemented")
}
func (UnimplementedGPUClusterServiceServer) CompleteTask(context.Context, *TaskCompletionRequest) (*TaskCompletionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompleteTask not implemented")
}
func (UnimplementedGPUClusterServiceServer) CancelTask(context.Context, *TaskCancellationRequest) (*TaskCancellationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelTask not implemented")
}
func (UnimplementedGPUClusterServiceServer) RegisterNode(context.Context, *NodeRegistrationRequest) (*NodeRegistrationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterNode not implemented")
}
func (UnimplementedGPUClusterServiceServer) UpdateNodeStatus(context.Context, *NodeStatusUpdate) (*NodeStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodeStatus not implemented")
}
func (UnimplementedGPUClusterServiceServer) GetClusterStatus(context.Context, *ClusterStatusRequest) (*ClusterStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClusterStatus not implemented")
}
func (UnimplementedGPUClusterServiceServer) HeartBeat(context.Context, *HeartBeatRequest) (*HeartBeatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HeartBeat not implemented")
}
func (UnimplementedGPUClusterServiceServer) TransferData(grpc.ClientStreamingServer[DataChunk, DataTransferResponse]) error {
	return status.Errorf(codes.Unimplemented, "method TransferData not implemented")
}
func (UnimplementedGPUClusterServiceServer) RequestData(*DataRequest, grpc.ServerStreamingServer[DataChunk]) error {
	return status.Errorf(codes.Unimplemented, "method RequestData not implemented")
}
func (UnimplementedGPUClusterServiceServer) SubmitResult(context.Context, *ResultSubmissionRequest) (*ResultSubmissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitResult not implemented")
}
func (UnimplementedGPUClusterServiceServer) AggregateResults(context.Context, *ResultAggregationRequest) (*ResultAggregationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AggregateResults not implemented")
}
func (UnimplementedGPUClusterServiceServer) RequestRebalance(context.Context, *RebalanceRequest) (*RebalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RequestRebalance not implemented")
}
func (UnimplementedGPUClusterServiceServer) MigrateTask(context.Context, *TaskMigrationRequest) (*TaskMigrationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MigrateTask not implemented")
}
func (UnimplementedGPUClusterServiceServer) mustEmbedUnimplementedGPUClusterServiceServer() {}
func (UnimplementedGPUClusterServiceServer) testEmbeddedByValue()                           {}

// UnsafeGPUClusterServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GPUClusterServiceServer will
// result in compilation errors.
type UnsafeGPUClusterServiceServer interface {
	mustEmbedUnimplementedGPUClusterServiceServer()
}

func RegisterGPUClusterServiceServer(s grpc.ServiceRegistrar, srv GPUClusterServiceServer) {
	// If the following call pancis, it indicates UnimplementedGPUClusterServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&GPUClusterService_ServiceDesc, srv)
}

func _GPUClusterService_AssignTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskAssignmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GPUClusterServiceServer).AssignTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GPUClusterService_AssignTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GPUClusterServiceServer).AssignTask(ctx, req.(*TaskAssignmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GPUClusterService_UpdateTaskStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskStatusUpdate)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GPUClusterServiceServer).UpdateTaskStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GPUClusterService_UpdateTaskStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GPUClusterServiceServer).UpdateTaskStatus(ctx, req.(*TaskStatusUpdate))
	}
	return interceptor(ctx, in, info, handler)
}

func _GPUClusterService_CompleteTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskCompletionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GPUClusterServiceServer).CompleteTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GPUClusterService_CompleteTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GPUClusterServiceServer).CompleteTask(ctx, req.(*TaskCompletionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GPUClusterService_CancelTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskCancellationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GPUClusterServiceServer).CancelTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GPUClusterService_CancelTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GPUClusterServiceServer).CancelTask(ctx, req.(*TaskCancellationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GPUClusterService_RegisterNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NodeRegistrationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GPUClusterServiceServer).RegisterNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GPUClusterService_RegisterNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GPUClusterServiceServer).RegisterNode(ctx, req.(*NodeRegistrationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GPUClusterService_UpdateNodeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NodeStatusUpdate)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GPUClusterServiceServer).UpdateNodeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GPUClusterService_UpdateNodeStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GPUClusterServiceServer).UpdateNodeStatus(ctx, req.(*NodeStatusUpdate))
	}
	return interceptor(ctx, in, info, handler)
}

func _GPUClusterService_GetClusterStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClusterStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GPUClusterServiceServer).GetClusterStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GPUClusterService_GetClusterStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GPUClusterServiceServer).GetClusterStatus(ctx, req.(*ClusterStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GPUClusterService_HeartBeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HeartBeatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GPUClusterServiceServer).HeartBeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GPUClusterService_HeartBeat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GPUClusterServiceServer).HeartBeat(ctx, req.(*HeartBeatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GPUClusterService_TransferData_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(GPUClusterServiceServer).TransferData(&grpc.GenericServerStream[DataChunk, DataTransferResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type GPUClusterService_TransferDataServer = grpc.ClientStreamingServer[DataChunk, DataTransferResponse]

func _GPUClusterService_RequestData_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(DataRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(GPUClusterServiceServer).RequestData(m, &grpc.GenericServerStream[DataRequest, DataChunk]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type GPUClusterService_RequestDataServer = grpc.ServerStreamingServer[DataChunk]

func _GPUClusterService_SubmitResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResultSubmissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GPUClusterServiceServer).SubmitResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GPUClusterService_SubmitResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GPUClusterServiceServer).SubmitResult(ctx, req.(*ResultSubmissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GPUClusterService_AggregateResults_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResultAggregationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GPUClusterServiceServer).AggregateResults(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GPUClusterService_AggregateResults_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GPUClusterServiceServer).AggregateResults(ctx, req.(*ResultAggregationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GPUClusterService_RequestRebalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RebalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GPUClusterServiceServer).RequestRebalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GPUClusterService_RequestRebalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GPUClusterServiceServer).RequestRebalance(ctx, req.(*RebalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GPUClusterService_MigrateTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskMigrationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GPUClusterServiceServer).MigrateTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GPUClusterService_MigrateTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GPUClusterServiceServer).MigrateTask(ctx, req.(*TaskMigrationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// GPUClusterService_ServiceDesc is the grpc.ServiceDesc for GPUClusterService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GPUClusterService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "gpu.cluster.GPUClusterService",
	HandlerType: (*GPUClusterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AssignTask",
			Handler:    _GPUClusterService_AssignTask_Handler,
		},
		{
			MethodName: "UpdateTaskStatus",
			Handler:    _GPUClusterService_UpdateTaskStatus_Handler,
		},
		{
			MethodName: "CompleteTask",
			Handler:    _GPUClusterService_CompleteTask_Handler,
		},
		{
			MethodName: "CancelTask",
			Handler:    _GPUClusterService_CancelTask_Handler,
		},
		{
			MethodName: "RegisterNode",
			Handler:    _GPUClusterService_RegisterNode_Handler,
		},
		{
			MethodName: "UpdateNodeStatus",
			Handler:    _GPUClusterService_UpdateNodeStatus_Handler,
		},
		{
			MethodName: "GetClusterStatus",
			Handler:    _GPUClusterService_GetClusterStatus_Handler,
		},
		{
			MethodName: "HeartBeat",
			Handler:    _GPUClusterService_HeartBeat_Handler,
		},
		{
			MethodName: "SubmitResult",
			Handler:    _GPUClusterService_SubmitResult_Handler,
		},
		{
			MethodName: "AggregateResults",
			Handler:    _GPUClusterService_AggregateResults_Handler,
		},
		{
			MethodName: "RequestRebalance",
			Handler:    _GPUClusterService_RequestRebalance_Handler,
		},
		{
			MethodName: "MigrateTask",
			Handler:    _GPUClusterService_MigrateTask_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "TransferData",
			Handler:       _GPUClusterService_TransferData_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "RequestData",
			Handler:       _GPUClusterService_RequestData_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "cluster.proto",
}
