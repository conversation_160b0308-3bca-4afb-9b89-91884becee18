package gpu

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"sync"
	"time"
)

// ClusterNode represents a node in the GPU cluster
type ClusterNode struct {
	ID              string               `json:"id"`
	Address         string               `json:"address"`
	Port            int                  `json:"port"`
	Hostname        string               `json:"hostname"`
	LastSeen        time.Time            `json:"last_seen"`
	Status          NodeStatus           `json:"status"`
	GPUDevices      []*ClusterGPUDevice  `json:"gpu_devices"`
	SystemInfo      *NodeSystemInfo      `json:"system_info"`
	Capabilities    []string             `json:"capabilities"`
	WorkloadMetrics *NodeWorkloadMetrics `json:"workload_metrics"`
	NetworkInfo     *NodeNetworkInfo     `json:"network_info"`
}

// NodeStatus represents the status of a cluster node
type NodeStatus string

const (
	NodeStatusActive      NodeStatus = "active"
	NodeStatusInactive    NodeStatus = "inactive"
	NodeStatusUnavailable NodeStatus = "unavailable"
	NodeStatusMaintenance NodeStatus = "maintenance"
	NodeStatusFailed      NodeStatus = "failed"
)

// ClusterGPUDevice represents a GPU device available in the cluster
type ClusterGPUDevice struct {
	DeviceID          string                 `json:"device_id"`
	NodeID            string                 `json:"node_id"`
	Name              string                 `json:"name"`
	Type              GPUType                `json:"type"`
	Vendor            string                 `json:"vendor"`
	Architecture      string                 `json:"architecture"`
	TotalMemory       int64                  `json:"total_memory"`
	AvailableMemory   int64                  `json:"available_memory"`
	ComputeCapability ComputeCapability      `json:"compute_capability,omitempty"`
	CoreCount         int                    `json:"core_count"`
	ClockRate         int                    `json:"clock_rate"`
	MemoryBandwidth   float64                `json:"memory_bandwidth"`
	Status            GPUDeviceStatus        `json:"status"`
	Utilization       *GPUUtilization        `json:"utilization"`
	Temperature       int                    `json:"temperature"`
	PowerUsage        float64                `json:"power_usage"`
	LastUpdate        time.Time              `json:"last_update"`
	Features          []string               `json:"features"`
	Metadata          map[string]interface{} `json:"metadata,omitempty"`
}

// GPUDeviceStatus represents the status of a GPU device
type GPUDeviceStatus string

const (
	GPUStatusAvailable   GPUDeviceStatus = "available"
	GPUStatusBusy        GPUDeviceStatus = "busy"
	GPUStatusMaintenance GPUDeviceStatus = "maintenance"
	GPUStatusError       GPUDeviceStatus = "error"
	GPUStatusOffline     GPUDeviceStatus = "offline"
)

// GPUUtilization represents current GPU utilization metrics
type GPUUtilization struct {
	ComputePercent float64 `json:"compute_percent"`
	MemoryPercent  float64 `json:"memory_percent"`
	EncoderPercent float64 `json:"encoder_percent,omitempty"`
	DecoderPercent float64 `json:"decoder_percent,omitempty"`
}

// NodeSystemInfo represents system information for a cluster node
type NodeSystemInfo struct {
	OS              string            `json:"os"`
	Architecture    string            `json:"architecture"`
	CPUCount        int               `json:"cpu_count"`
	TotalMemory     int64             `json:"total_memory"`
	AvailableMemory int64             `json:"available_memory"`
	LoadAverage     float64           `json:"load_average"`
	Uptime          int64             `json:"uptime"`
	KernelVersion   string            `json:"kernel_version,omitempty"`
	DriverVersions  map[string]string `json:"driver_versions,omitempty"`
}

// NodeWorkloadMetrics represents workload metrics for a cluster node
type NodeWorkloadMetrics struct {
	ActiveTasks           int       `json:"active_tasks"`
	CompletedTasks        int64     `json:"completed_tasks"`
	FailedTasks           int64     `json:"failed_tasks"`
	AverageTaskTime       float64   `json:"average_task_time"`
	ThroughputTasksPerSec float64   `json:"throughput_tasks_per_sec"`
	QueueLength           int       `json:"queue_length"`
	LastTaskTime          time.Time `json:"last_task_time"`
}

// NodeNetworkInfo represents network information for a cluster node
type NodeNetworkInfo struct {
	Bandwidth  float64            `json:"bandwidth_mbps"`
	Latency    float64            `json:"latency_ms"`
	PacketLoss float64            `json:"packet_loss_percent"`
	Interfaces []NetworkInterface `json:"interfaces"`
	PublicIP   string             `json:"public_ip,omitempty"`
	PrivateIP  string             `json:"private_ip,omitempty"`
}

// NetworkInterface represents a network interface
type NetworkInterface struct {
	Name     string `json:"name"`
	IP       string `json:"ip"`
	MAC      string `json:"mac"`
	MTU      int    `json:"mtu"`
	Speed    int64  `json:"speed_mbps"`
	IsActive bool   `json:"is_active"`
}

// GPUResourceDiscovery handles discovery and cataloging of GPU resources across cluster nodes
type GPUResourceDiscovery struct {
	nodes           map[string]*ClusterNode
	nodesMutex      sync.RWMutex
	discoveryConfig *DiscoveryConfig
	logger          *log.Logger

	// Discovery channels
	nodeUpdates     chan *ClusterNode
	nodeRemovals    chan string
	discoveryErrors chan error

	// Discovery state
	isRunning bool
	stopChan  chan struct{}
	wg        sync.WaitGroup

	// Network discovery
	multicastConn *net.UDPConn
	unicastConn   *net.UDPConn

	// Callbacks
	nodeAddedCallback   func(*ClusterNode)
	nodeUpdatedCallback func(*ClusterNode)
	nodeRemovedCallback func(string)
}

// DiscoveryConfig contains configuration for GPU resource discovery
type DiscoveryConfig struct {
	// Network configuration
	MulticastAddress string `json:"multicast_address"`
	MulticastPort    int    `json:"multicast_port"`
	UnicastPort      int    `json:"unicast_port"`

	// Discovery timing
	DiscoveryInterval time.Duration `json:"discovery_interval"`
	HeartbeatInterval time.Duration `json:"heartbeat_interval"`
	NodeTimeout       time.Duration `json:"node_timeout"`

	// Network scanning
	SubnetScanEnabled bool     `json:"subnet_scan_enabled"`
	ScanSubnets       []string `json:"scan_subnets"`
	PortRange         []int    `json:"port_range"`

	// Security
	AuthToken  string `json:"auth_token,omitempty"`
	TLSEnabled bool   `json:"tls_enabled"`
	CertFile   string `json:"cert_file,omitempty"`
	KeyFile    string `json:"key_file,omitempty"`

	// Filtering
	AllowedNodeTypes     []string `json:"allowed_node_types"`
	MinGPUMemory         int64    `json:"min_gpu_memory"`
	RequiredCapabilities []string `json:"required_capabilities"`
}

// DefaultDiscoveryConfig returns default configuration for GPU resource discovery
func DefaultDiscoveryConfig() *DiscoveryConfig {
	return &DiscoveryConfig{
		MulticastAddress:     "***************",
		MulticastPort:        19132,
		UnicastPort:          19133,
		DiscoveryInterval:    time.Second * 30,
		HeartbeatInterval:    time.Second * 10,
		NodeTimeout:          time.Minute * 2,
		SubnetScanEnabled:    true,
		ScanSubnets:          []string{"***********/16", "10.0.0.0/8", "**********/12"},
		PortRange:            []int{19130, 19140},
		TLSEnabled:           false,
		AllowedNodeTypes:     []string{"gpu_worker", "gpu_coordinator"},
		MinGPUMemory:         1 << 30, // 1GB minimum
		RequiredCapabilities: []string{},
	}
}

// NewGPUResourceDiscovery creates a new GPU resource discovery service
func NewGPUResourceDiscovery(config *DiscoveryConfig, logger *log.Logger) (*GPUResourceDiscovery, error) {
	if config == nil {
		config = DefaultDiscoveryConfig()
	}

	if logger == nil {
		logger = log.Default()
	}

	discovery := &GPUResourceDiscovery{
		nodes:           make(map[string]*ClusterNode),
		discoveryConfig: config,
		logger:          logger,
		nodeUpdates:     make(chan *ClusterNode, 100),
		nodeRemovals:    make(chan string, 100),
		discoveryErrors: make(chan error, 100),
		stopChan:        make(chan struct{}),
	}

	return discovery, nil
}

// Start begins the GPU resource discovery process
func (grd *GPUResourceDiscovery) Start(ctx context.Context) error {
	grd.nodesMutex.Lock()
	defer grd.nodesMutex.Unlock()

	if grd.isRunning {
		return fmt.Errorf("GPU resource discovery is already running")
	}

	// Initialize network connections
	if err := grd.initializeNetworking(); err != nil {
		return fmt.Errorf("failed to initialize networking: %w", err)
	}

	grd.isRunning = true

	// Start discovery goroutines
	grd.wg.Add(4)
	go grd.multicastDiscoveryLoop(ctx)
	go grd.unicastScanLoop(ctx)
	go grd.nodeMaintenanceLoop(ctx)
	go grd.eventProcessingLoop(ctx)

	grd.logger.Printf("GPU resource discovery started with config: multicast=%s:%d, unicast=%d",
		grd.discoveryConfig.MulticastAddress, grd.discoveryConfig.MulticastPort, grd.discoveryConfig.UnicastPort)

	return nil
}

// Stop stops the GPU resource discovery process
func (grd *GPUResourceDiscovery) Stop() error {
	grd.nodesMutex.Lock()
	defer grd.nodesMutex.Unlock()

	if !grd.isRunning {
		return nil
	}

	close(grd.stopChan)
	grd.wg.Wait()

	// Close network connections
	if grd.multicastConn != nil {
		grd.multicastConn.Close()
	}
	if grd.unicastConn != nil {
		grd.unicastConn.Close()
	}

	grd.isRunning = false
	grd.logger.Printf("GPU resource discovery stopped")

	return nil
}

// GetNodes returns all discovered cluster nodes
func (grd *GPUResourceDiscovery) GetNodes() []*ClusterNode {
	grd.nodesMutex.RLock()
	defer grd.nodesMutex.RUnlock()

	nodes := make([]*ClusterNode, 0, len(grd.nodes))
	for _, node := range grd.nodes {
		nodes = append(nodes, node)
	}

	return nodes
}

// GetNode returns a specific cluster node by ID
func (grd *GPUResourceDiscovery) GetNode(nodeID string) (*ClusterNode, error) {
	grd.nodesMutex.RLock()
	defer grd.nodesMutex.RUnlock()

	node, exists := grd.nodes[nodeID]
	if !exists {
		return nil, fmt.Errorf("node %s not found", nodeID)
	}

	return node, nil
}

// GetActiveNodes returns all active cluster nodes
func (grd *GPUResourceDiscovery) GetActiveNodes() []*ClusterNode {
	grd.nodesMutex.RLock()
	defer grd.nodesMutex.RUnlock()

	activeNodes := make([]*ClusterNode, 0)
	for _, node := range grd.nodes {
		if node.Status == NodeStatusActive {
			activeNodes = append(activeNodes, node)
		}
	}

	return activeNodes
}

// GetAvailableGPUs returns all available GPU devices across the cluster
func (grd *GPUResourceDiscovery) GetAvailableGPUs() []*ClusterGPUDevice {
	grd.nodesMutex.RLock()
	defer grd.nodesMutex.RUnlock()

	availableGPUs := make([]*ClusterGPUDevice, 0)
	for _, node := range grd.nodes {
		if node.Status == NodeStatusActive {
			for _, gpu := range node.GPUDevices {
				if gpu.Status == GPUStatusAvailable {
					availableGPUs = append(availableGPUs, gpu)
				}
			}
		}
	}

	return availableGPUs
}

// SetCallbacks sets callback functions for node events
func (grd *GPUResourceDiscovery) SetCallbacks(
	onNodeAdded func(*ClusterNode),
	onNodeUpdated func(*ClusterNode),
	onNodeRemoved func(string),
) {
	grd.nodeAddedCallback = onNodeAdded
	grd.nodeUpdatedCallback = onNodeUpdated
	grd.nodeRemovedCallback = onNodeRemoved
}

// initializeNetworking sets up UDP connections for discovery
func (grd *GPUResourceDiscovery) initializeNetworking() error {
	// Set up multicast connection for discovery broadcasts
	multicastAddr, err := net.ResolveUDPAddr("udp4",
		fmt.Sprintf("%s:%d", grd.discoveryConfig.MulticastAddress, grd.discoveryConfig.MulticastPort))
	if err != nil {
		return fmt.Errorf("failed to resolve multicast address: %w", err)
	}

	grd.multicastConn, err = net.ListenMulticastUDP("udp4", nil, multicastAddr)
	if err != nil {
		return fmt.Errorf("failed to listen on multicast address: %w", err)
	}

	// Set up unicast connection for direct communication
	unicastAddr, err := net.ResolveUDPAddr("udp4", fmt.Sprintf(":%d", grd.discoveryConfig.UnicastPort))
	if err != nil {
		return fmt.Errorf("failed to resolve unicast address: %w", err)
	}

	grd.unicastConn, err = net.ListenUDP("udp4", unicastAddr)
	if err != nil {
		return fmt.Errorf("failed to listen on unicast address: %w", err)
	}

	return nil
}

// multicastDiscoveryLoop handles multicast discovery messages
func (grd *GPUResourceDiscovery) multicastDiscoveryLoop(ctx context.Context) {
	defer grd.wg.Done()

	buffer := make([]byte, 4096)

	for {
		select {
		case <-ctx.Done():
			return
		case <-grd.stopChan:
			return
		default:
			// Set read timeout
			grd.multicastConn.SetReadDeadline(time.Now().Add(time.Second))

			n, addr, err := grd.multicastConn.ReadFromUDP(buffer)
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					continue
				}
				grd.discoveryErrors <- fmt.Errorf("multicast read error: %w", err)
				continue
			}

			// Process discovery message
			if err := grd.processDiscoveryMessage(buffer[:n], addr); err != nil {
				grd.logger.Printf("Error processing discovery message from %s: %v", addr, err)
			}
		}
	}
}

// unicastScanLoop performs periodic unicast scanning of subnets
func (grd *GPUResourceDiscovery) unicastScanLoop(ctx context.Context) {
	defer grd.wg.Done()

	if !grd.discoveryConfig.SubnetScanEnabled {
		return
	}

	ticker := time.NewTicker(grd.discoveryConfig.DiscoveryInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-grd.stopChan:
			return
		case <-ticker.C:
			grd.performSubnetScan(ctx)
		}
	}
}

// nodeMaintenanceLoop performs periodic maintenance on discovered nodes
func (grd *GPUResourceDiscovery) nodeMaintenanceLoop(ctx context.Context) {
	defer grd.wg.Done()

	ticker := time.NewTicker(grd.discoveryConfig.HeartbeatInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-grd.stopChan:
			return
		case <-ticker.C:
			grd.performNodeMaintenance()
		}
	}
}

// eventProcessingLoop processes node update and removal events
func (grd *GPUResourceDiscovery) eventProcessingLoop(ctx context.Context) {
	defer grd.wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		case <-grd.stopChan:
			return
		case nodeUpdate := <-grd.nodeUpdates:
			grd.handleNodeUpdate(nodeUpdate)
		case nodeID := <-grd.nodeRemovals:
			grd.handleNodeRemoval(nodeID)
		case err := <-grd.discoveryErrors:
			grd.logger.Printf("Discovery error: %v", err)
		}
	}
}

// processDiscoveryMessage processes an incoming discovery message
func (grd *GPUResourceDiscovery) processDiscoveryMessage(data []byte, addr *net.UDPAddr) error {
	var message DiscoveryMessage
	if err := json.Unmarshal(data, &message); err != nil {
		return fmt.Errorf("failed to unmarshal discovery message: %w", err)
	}

	// Validate authentication if enabled
	if grd.discoveryConfig.AuthToken != "" && message.AuthToken != grd.discoveryConfig.AuthToken {
		return fmt.Errorf("invalid auth token from %s", addr)
	}

	// Process based on message type
	switch message.Type {
	case "node_announcement":
		return grd.handleNodeAnnouncement(&message, addr)
	case "node_heartbeat":
		return grd.handleNodeHeartbeat(&message, addr)
	case "gpu_status_update":
		return grd.handleGPUStatusUpdate(&message, addr)
	default:
		return fmt.Errorf("unknown message type: %s", message.Type)
	}
}

// DiscoveryMessage represents a discovery protocol message
type DiscoveryMessage struct {
	Type      string                 `json:"type"`
	NodeID    string                 `json:"node_id"`
	Timestamp time.Time              `json:"timestamp"`
	AuthToken string                 `json:"auth_token,omitempty"`
	Data      map[string]interface{} `json:"data"`
}

// handleNodeAnnouncement processes a node announcement message
func (grd *GPUResourceDiscovery) handleNodeAnnouncement(message *DiscoveryMessage, addr *net.UDPAddr) error {
	nodeData, ok := message.Data["node"]
	if !ok {
		return fmt.Errorf("missing node data in announcement")
	}

	nodeBytes, err := json.Marshal(nodeData)
	if err != nil {
		return fmt.Errorf("failed to marshal node data: %w", err)
	}

	var node ClusterNode
	if err := json.Unmarshal(nodeBytes, &node); err != nil {
		return fmt.Errorf("failed to unmarshal node data: %w", err)
	}

	// Update node address from UDP source
	node.Address = addr.IP.String()
	node.LastSeen = time.Now()

	// Validate node meets requirements
	if !grd.validateNode(&node) {
		return fmt.Errorf("node %s does not meet requirements", node.ID)
	}

	// Send node update
	select {
	case grd.nodeUpdates <- &node:
	default:
		grd.logger.Printf("Node update channel full, dropping update for node %s", node.ID)
	}

	return nil
}

// handleNodeHeartbeat processes a node heartbeat message
func (grd *GPUResourceDiscovery) handleNodeHeartbeat(message *DiscoveryMessage, addr *net.UDPAddr) error {
	grd.nodesMutex.Lock()
	defer grd.nodesMutex.Unlock()

	node, exists := grd.nodes[message.NodeID]
	if !exists {
		return fmt.Errorf("received heartbeat from unknown node %s", message.NodeID)
	}

	node.LastSeen = time.Now()
	node.Status = NodeStatusActive

	// Update workload metrics if provided
	if workloadData, ok := message.Data["workload_metrics"]; ok {
		workloadBytes, err := json.Marshal(workloadData)
		if err == nil {
			var metrics NodeWorkloadMetrics
			if err := json.Unmarshal(workloadBytes, &metrics); err == nil {
				node.WorkloadMetrics = &metrics
			}
		}
	}

	return nil
}

// handleGPUStatusUpdate processes a GPU status update message
func (grd *GPUResourceDiscovery) handleGPUStatusUpdate(message *DiscoveryMessage, addr *net.UDPAddr) error {
	grd.nodesMutex.Lock()
	defer grd.nodesMutex.Unlock()

	node, exists := grd.nodes[message.NodeID]
	if !exists {
		return fmt.Errorf("received GPU status update from unknown node %s", message.NodeID)
	}

	gpuUpdates, ok := message.Data["gpu_devices"]
	if !ok {
		return fmt.Errorf("missing GPU device data in status update")
	}

	gpuBytes, err := json.Marshal(gpuUpdates)
	if err != nil {
		return fmt.Errorf("failed to marshal GPU data: %w", err)
	}

	var gpuDevices []*ClusterGPUDevice
	if err := json.Unmarshal(gpuBytes, &gpuDevices); err != nil {
		return fmt.Errorf("failed to unmarshal GPU data: %w", err)
	}

	// Update GPU devices
	node.GPUDevices = gpuDevices
	node.LastSeen = time.Now()

	return nil
}

// validateNode checks if a node meets the discovery requirements
func (grd *GPUResourceDiscovery) validateNode(node *ClusterNode) bool {
	// Check minimum GPU memory requirement
	for _, gpu := range node.GPUDevices {
		if gpu.TotalMemory >= grd.discoveryConfig.MinGPUMemory {
			return true
		}
	}

	// Check required capabilities
	for _, required := range grd.discoveryConfig.RequiredCapabilities {
		found := false
		for _, capability := range node.Capabilities {
			if capability == required {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return len(node.GPUDevices) > 0
}

// handleNodeUpdate processes a node update event
func (grd *GPUResourceDiscovery) handleNodeUpdate(node *ClusterNode) {
	grd.nodesMutex.Lock()
	defer grd.nodesMutex.Unlock()

	_, exists := grd.nodes[node.ID]
	isNewNode := !exists

	grd.nodes[node.ID] = node

	if isNewNode {
		grd.logger.Printf("Discovered new cluster node: %s (%s) with %d GPUs",
			node.ID, node.Address, len(node.GPUDevices))

		if grd.nodeAddedCallback != nil {
			go grd.nodeAddedCallback(node)
		}
	} else {
		grd.logger.Printf("Updated cluster node: %s (%s)", node.ID, node.Address)

		if grd.nodeUpdatedCallback != nil {
			go grd.nodeUpdatedCallback(node)
		}
	}
}

// handleNodeRemoval processes a node removal event
func (grd *GPUResourceDiscovery) handleNodeRemoval(nodeID string) {
	grd.nodesMutex.Lock()
	defer grd.nodesMutex.Unlock()

	if _, exists := grd.nodes[nodeID]; exists {
		delete(grd.nodes, nodeID)
		grd.logger.Printf("Removed cluster node: %s", nodeID)

		if grd.nodeRemovedCallback != nil {
			go grd.nodeRemovedCallback(nodeID)
		}
	}
}

// performSubnetScan performs active scanning of configured subnets
func (grd *GPUResourceDiscovery) performSubnetScan(ctx context.Context) {
	for _, subnet := range grd.discoveryConfig.ScanSubnets {
		select {
		case <-ctx.Done():
			return
		case <-grd.stopChan:
			return
		default:
			if err := grd.scanSubnet(subnet); err != nil {
				grd.logger.Printf("Error scanning subnet %s: %v", subnet, err)
			}
		}
	}
}

// scanSubnet scans a specific subnet for GPU nodes
func (grd *GPUResourceDiscovery) scanSubnet(subnet string) error {
	_, ipNet, err := net.ParseCIDR(subnet)
	if err != nil {
		return fmt.Errorf("invalid subnet CIDR: %w", err)
	}

	// Generate IPs to scan (simplified implementation)
	// In a production system, this would be more sophisticated
	for ip := ipNet.IP.Mask(ipNet.Mask); ipNet.Contains(ip); grd.incrementIP(ip) {
		for _, port := range grd.discoveryConfig.PortRange {
			address := fmt.Sprintf("%s:%d", ip.String(), port)

			// Try to connect with timeout
			conn, err := net.DialTimeout("udp", address, time.Second*2)
			if err != nil {
				continue
			}

			// Send discovery probe
			probe := DiscoveryMessage{
				Type:      "discovery_probe",
				NodeID:    "scanner",
				Timestamp: time.Now(),
				AuthToken: grd.discoveryConfig.AuthToken,
				Data:      map[string]interface{}{},
			}

			probeData, _ := json.Marshal(probe)
			conn.Write(probeData)
			conn.Close()
		}
	}

	return nil
}

// incrementIP increments an IP address
func (grd *GPUResourceDiscovery) incrementIP(ip net.IP) {
	for j := len(ip) - 1; j >= 0; j-- {
		ip[j]++
		if ip[j] > 0 {
			break
		}
	}
}

// performNodeMaintenance checks for inactive nodes and removes them
func (grd *GPUResourceDiscovery) performNodeMaintenance() {
	grd.nodesMutex.Lock()
	defer grd.nodesMutex.Unlock()

	now := time.Now()
	timeout := grd.discoveryConfig.NodeTimeout

	for nodeID, node := range grd.nodes {
		if now.Sub(node.LastSeen) > timeout {
			// Mark node as inactive or remove it
			if node.Status != NodeStatusInactive {
				node.Status = NodeStatusInactive
				grd.logger.Printf("Node %s marked as inactive (last seen: %v)", nodeID, node.LastSeen)
			}

			// Remove if inactive for too long
			if now.Sub(node.LastSeen) > timeout*2 {
				select {
				case grd.nodeRemovals <- nodeID:
				default:
					grd.logger.Printf("Node removal channel full, dropping removal for node %s", nodeID)
				}
			}
		}
	}
}
