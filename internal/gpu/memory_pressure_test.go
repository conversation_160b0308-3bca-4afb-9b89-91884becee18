package gpu

import (
	"log"
	"os"
	"sync"
	"testing"
	"time"
)

// TestMemoryPressureComprehensive tests all aspects of memory pressure detection and handling
func TestMemoryPressureComprehensive(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	config := DefaultPoolConfig(0)
	config.InitialSize = 4 * 1024 * 1024 // 4MB
	config.MaxSize = 8 * 1024 * 1024     // 8MB
	config.BlockSize = 1024 * 1024       // 1MB
	config.Strategy = StrategyDynamic
	config.GCInterval = 100 * time.Millisecond
	config.PressureThresholds = PressureThresholds{
		MediumPercent:   40.0, // 40% of max size
		HighPercent:     70.0, // 70% of max size
		CriticalPercent: 90.0, // 90% of max size
	}

	pool, err := NewAdvancedMemoryPool(config, logger)
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	if err := pool.Initialize(); err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Test 1: Initial state should be low pressure
	stats := pool.GetStatistics()
	if stats.PressureLevel != PressureLow {
		t.Errorf("Expected initial pressure level to be low, got %v", stats.PressureLevel)
	}

	// Test 2: Track pressure level changes (use sync to avoid race conditions)
	var pressureEvents []MemoryPressureLevel
	var pressureMutex sync.Mutex
	pool.AddPressureHandler(func(oldLevel, newLevel MemoryPressureLevel, stats MemoryPoolStatistics) {
		pressureMutex.Lock()
		pressureEvents = append(pressureEvents, newLevel)
		pressureMutex.Unlock()
		t.Logf("Pressure changed from %s to %s (utilization: %.1f%% of max)",
			oldLevel, newLevel, float64(stats.UsedSize)/float64(config.MaxSize)*100.0)
	})

	// Test 3: Gradually increase memory usage to trigger pressure levels
	var allocations []CUDAMemoryPtr

	// Allocate to trigger medium pressure (~50% of max size)
	for i := 0; i < 4; i++ {
		ptr, err := pool.Allocate(1024 * 1024) // 1MB each
		if err != nil {
			t.Fatalf("Failed to allocate memory: %v", err)
		}
		allocations = append(allocations, ptr)
	}

	// Allow time for pressure detection
	time.Sleep(50 * time.Millisecond)

	stats = pool.GetStatistics()
	// At 4MB/8MB (50%), should be medium pressure (threshold 40%)
	if stats.PressureLevel == PressureLow {
		t.Errorf("Expected at least medium pressure level after 50%% allocation, got %v", stats.PressureLevel)
	}

	// Allocate more to trigger high pressure (~75% of max size)
	for i := 0; i < 2; i++ {
		ptr, err := pool.Allocate(1024 * 1024) // 1MB each
		if err != nil {
			t.Fatalf("Failed to allocate memory: %v", err)
		}
		allocations = append(allocations, ptr)
	}

	time.Sleep(50 * time.Millisecond)

	stats = pool.GetStatistics()
	// At 6MB/8MB (75%), should be high pressure (threshold 70%)
	if stats.PressureLevel != PressureHigh && stats.PressureLevel != PressureMedium {
		t.Errorf("Expected high or medium pressure level after 75%% allocation, got %v", stats.PressureLevel)
	}

	// Test 4: Free some memory to reduce pressure
	for i := 0; i < 3; i++ {
		if err := pool.Free(allocations[i]); err != nil {
			t.Errorf("Failed to free memory: %v", err)
		}
	}

	time.Sleep(50 * time.Millisecond)

	stats = pool.GetStatistics()
	if stats.PressureLevel == PressureHigh {
		t.Errorf("Expected pressure level to decrease after freeing memory, still got %v", stats.PressureLevel)
	}

	// Test 5: Verify pressure change events were received
	pressureMutex.Lock()
	eventCount := len(pressureEvents)
	pressureMutex.Unlock()

	if eventCount < 2 {
		t.Errorf("Expected at least 2 pressure change events, got %d", eventCount)
	}

	// Clean up remaining allocations
	for i := 3; i < len(allocations); i++ {
		pool.Free(allocations[i])
	}
}

// TestGarbageCollectionHandling tests garbage collection under memory pressure
func TestGarbageCollectionHandling(t *testing.T) {
	logger := log.New(os.Stdout, "TEST_GC: ", log.LstdFlags)

	config := DefaultPoolConfig(0)
	config.Strategy = StrategyDynamic
	config.GCInterval = 50 * time.Millisecond // Frequent GC for testing
	config.MaxSize = 4 * 1024 * 1024          // 4MB max

	pool, err := NewAdvancedMemoryPool(config, logger)
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	if err := pool.Initialize(); err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Track GC events
	var gcEvents []int64
	pool.AddGCHandler(func(freedMemory int64, duration time.Duration, stats MemoryPoolStatistics) {
		gcEvents = append(gcEvents, freedMemory)
		t.Logf("GC freed %d bytes in %v", freedMemory, duration)
	})

	// Allocate and free memory to create garbage
	var allocations []CUDAMemoryPtr
	for i := 0; i < 5; i++ {
		ptr, err := pool.Allocate(512 * 1024) // 512KB each
		if err != nil {
			t.Fatalf("Failed to allocate memory: %v", err)
		}
		allocations = append(allocations, ptr)
	}

	// Free half the allocations to create garbage
	for i := 0; i < 3; i++ {
		if err := pool.Free(allocations[i]); err != nil {
			t.Errorf("Failed to free memory: %v", err)
		}
	}

	// Wait for GC cycles to run
	time.Sleep(200 * time.Millisecond)

	stats := pool.GetStatistics()
	if stats.GCCount == 0 {
		t.Errorf("Expected at least one GC cycle to have run, got %d", stats.GCCount)
	}

	// Clean up remaining allocations
	for i := 3; i < len(allocations); i++ {
		pool.Free(allocations[i])
	}
}

// TestMemoryPressureRecovery tests system's ability to recover from high pressure
func TestMemoryPressureRecovery(t *testing.T) {
	logger := log.New(os.Stdout, "TEST_RECOVERY: ", log.LstdFlags)

	config := DefaultPoolConfig(0)
	config.Strategy = StrategyAdaptive
	config.MaxSize = 2 * 1024 * 1024 // 2MB max for quick pressure buildup
	config.GCInterval = 25 * time.Millisecond
	config.PressureThresholds = PressureThresholds{
		MediumPercent:   50.0,
		HighPercent:     80.0,
		CriticalPercent: 95.0,
	}

	pool, err := NewAdvancedMemoryPool(config, logger)
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	if err := pool.Initialize(); err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Test recovery scenario: push to high pressure, then free memory
	var allocations []CUDAMemoryPtr

	// Push to high pressure
	for i := 0; i < 8; i++ {
		ptr, err := pool.Allocate(256 * 1024) // 256KB each
		if err != nil {
			break // Expected to fail at some point due to size limits
		}
		allocations = append(allocations, ptr)
	}

	stats := pool.GetStatistics()
	initialPressure := stats.PressureLevel
	t.Logf("Reached pressure level: %v with %d allocations", initialPressure, len(allocations))

	// Free most allocations to test recovery
	freeCount := len(allocations) * 3 / 4 // Free 75%
	for i := 0; i < freeCount; i++ {
		if err := pool.Free(allocations[i]); err != nil {
			t.Errorf("Failed to free memory during recovery: %v", err)
		}
	}

	// Allow time for pressure level to update
	time.Sleep(100 * time.Millisecond)

	stats = pool.GetStatistics()
	if stats.PressureLevel >= initialPressure {
		t.Errorf("Expected pressure level to decrease during recovery, was %v, now %v",
			initialPressure, stats.PressureLevel)
	}

	// Clean up remaining allocations
	for i := freeCount; i < len(allocations); i++ {
		pool.Free(allocations[i])
	}
}

// TestDefragmentationTrigger tests defragmentation under fragmented conditions
func TestDefragmentationTrigger(t *testing.T) {
	logger := log.New(os.Stdout, "TEST_DEFRAG: ", log.LstdFlags)

	config := DefaultPoolConfig(0)
	config.Strategy = StrategyDynamic
	config.EnableDefragmentation = true
	config.DefragmentationInterval = 50 * time.Millisecond
	config.FragmentationLimit = 50.0 // Trigger defrag at 50% fragmentation

	pool, err := NewAdvancedMemoryPool(config, logger)
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	if err := pool.Initialize(); err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Create fragmentation by allocating and freeing in a pattern
	var allocations []CUDAMemoryPtr

	// Allocate many small blocks
	for i := 0; i < 10; i++ {
		ptr, err := pool.Allocate(128 * 1024) // 128KB each
		if err != nil {
			t.Fatalf("Failed to allocate memory: %v", err)
		}
		allocations = append(allocations, ptr)
	}

	// Free every other block to create fragmentation
	for i := 0; i < len(allocations); i += 2 {
		if err := pool.Free(allocations[i]); err != nil {
			t.Errorf("Failed to free memory: %v", err)
		}
	}

	// Wait for defragmentation to potentially run
	time.Sleep(200 * time.Millisecond)

	stats := pool.GetStatistics()
	t.Logf("Fragmentation: %.1f%%, Defragmentation count: %d",
		stats.FragmentationPercent, stats.DefragmentationCount)

	// Clean up remaining allocations
	for i := 1; i < len(allocations); i += 2 {
		pool.Free(allocations[i])
	}
}

// TestMemoryPressureHandlerIntegration tests integration with pressure handlers
func TestMemoryPressureHandlerIntegration(t *testing.T) {
	logger := log.New(os.Stdout, "TEST_HANDLERS: ", log.LstdFlags)

	config := DefaultPoolConfig(0)
	config.Strategy = StrategyFixed
	config.MaxSize = 3 * 1024 * 1024 // 3MB max
	config.BlockSize = 512 * 1024    // 512KB blocks (max 6 blocks)
	config.PressureThresholds = PressureThresholds{
		MediumPercent:   40.0,
		HighPercent:     70.0,
		CriticalPercent: 90.0,
	}

	pool, err := NewAdvancedMemoryPool(config, logger)
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	if err := pool.Initialize(); err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Test multiple allocations within pool capacity
	// With 3MB max and 512KB blocks, we can allocate 6 blocks maximum
	var allocations []CUDAMemoryPtr

	// Allocate 3 blocks (50% usage - should trigger medium pressure)
	for i := 0; i < 3; i++ {
		ptr, err := pool.Allocate(config.BlockSize)
		if err != nil {
			t.Fatalf("Failed to allocate block %d: %v", i, err)
		}
		allocations = append(allocations, ptr)
	}

	// Check medium pressure
	stats := pool.GetStatistics()
	usagePercent := float64(stats.UsedSize) / float64(stats.TotalSize) * 100.0
	if usagePercent < 40.0 {
		t.Logf("Warning: Expected medium pressure (>40%%), got %.1f%%", usagePercent)
	}

	// Allocate 2 more blocks (83% usage - should trigger high pressure)
	for i := 0; i < 2; i++ {
		ptr, err := pool.Allocate(config.BlockSize)
		if err != nil {
			t.Fatalf("Failed to allocate block %d: %v", i+3, err)
		}
		allocations = append(allocations, ptr)
	}

	// Check high pressure
	stats = pool.GetStatistics()
	usagePercent = float64(stats.UsedSize) / float64(stats.TotalSize) * 100.0
	if usagePercent < 70.0 {
		t.Logf("Warning: Expected high pressure (>70%%), got %.1f%%", usagePercent)
	}

	// Try to allocate one more block (would be 100% usage)
	ptr, err := pool.Allocate(config.BlockSize)
	if err != nil {
		// This is expected - we've reached the pool limit
		t.Logf("Expected allocation failure at capacity: %v", err)
	} else {
		// If it succeeded, add to allocations for cleanup
		allocations = append(allocations, ptr)

		// Check critical pressure
		stats = pool.GetStatistics()
		usagePercent = float64(stats.UsedSize) / float64(stats.TotalSize) * 100.0
		if usagePercent < 90.0 {
			t.Logf("Warning: Expected critical pressure (>90%%), got %.1f%%", usagePercent)
		}
	}

	// Free all allocations
	for i, ptr := range allocations {
		if err := pool.Free(ptr); err != nil {
			t.Errorf("Failed to free allocation %d: %v", i, err)
		}
	}

	// Verify cleanup
	finalStats := pool.GetStatistics()
	finalUsagePercent := float64(finalStats.UsedSize) / float64(finalStats.TotalSize) * 100.0
	if finalUsagePercent > 5.0 {
		t.Errorf("Expected low usage after cleanup, got %.1f%%", finalUsagePercent)
	}

	t.Logf("Memory pressure test completed successfully")
	t.Logf("Final stats: %.1f%% usage, %d allocations", finalUsagePercent, finalStats.UsedBlockCount)
}
