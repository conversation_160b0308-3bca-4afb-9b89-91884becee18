package gpu

import (
	"log"
	"testing"
)

func TestSimpleMemoryPoolCreation(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.GCInterval = 0 // Disable background processes
	config.EnableDefragmentation = false

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	if pool == nil {
		t.Fatal("Pool is nil")
	}

	if pool.isInitialized {
		t.Error("Pool should not be initialized yet")
	}
}

func TestSimpleMemoryPoolBasicOperations(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.Strategy = StrategyDynamic
	config.InitialSize = 0
	config.GCInterval = 0 // Disable background processes
	config.EnableDefragmentation = false

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	err = pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}

	// Test allocation
	ptr, err := pool.Allocate(1024)
	if err != nil {
		t.Fatalf("Failed to allocate memory: %v", err)
	}

	if ptr == 0 {
		t.Error("Expected non-zero pointer")
	}

	// Test statistics
	stats := pool.GetStatistics()
	if stats.AllocationCount != 1 {
		t.Errorf("Expected 1 allocation, got %d", stats.AllocationCount)
	}

	// Test free
	err = pool.Free(ptr)
	if err != nil {
		t.Fatalf("Failed to free memory: %v", err)
	}

	stats = pool.GetStatistics()
	if stats.DeallocationCount != 1 {
		t.Errorf("Expected 1 deallocation, got %d", stats.DeallocationCount)
	}

	// Immediate shutdown without background processes
	err = pool.Shutdown()
	if err != nil {
		t.Fatalf("Failed to shutdown pool: %v", err)
	}
}

func TestSimpleAllocationStrategies(t *testing.T) {
	strategies := []AllocationStrategy{StrategyFixed, StrategyDynamic, StrategyAdaptive}

	for _, strategy := range strategies {
		t.Run(strategy.String(), func(t *testing.T) {
			config := DefaultPoolConfig(0)
			config.Strategy = strategy
			config.GCInterval = 0
			config.EnableDefragmentation = false

			if strategy == StrategyFixed {
				config.InitialSize = 32 * 1024 * 1024 // 32MB
				config.BlockSize = 8 * 1024 * 1024    // 8MB
			}

			pool, err := NewAdvancedMemoryPool(config, log.Default())
			if err != nil {
				t.Fatalf("Failed to create %s pool: %v", strategy, err)
			}

			err = pool.Initialize()
			if err != nil {
				t.Fatalf("Failed to initialize %s pool: %v", strategy, err)
			}

			// Test basic allocation
			ptr, err := pool.Allocate(1024)
			if err != nil {
				t.Fatalf("Failed to allocate from %s pool: %v", strategy, err)
			}

			if ptr == 0 {
				t.Errorf("Expected non-zero pointer from %s pool", strategy)
			}

			err = pool.Free(ptr)
			if err != nil {
				t.Fatalf("Failed to free from %s pool: %v", strategy, err)
			}

			err = pool.Shutdown()
			if err != nil {
				t.Fatalf("Failed to shutdown %s pool: %v", strategy, err)
			}
		})
	}
}

func TestMemoryPoolStatistics(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.Strategy = StrategyDynamic
	config.GCInterval = 0
	config.EnableDefragmentation = false

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	err = pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	initialStats := pool.GetStatistics()
	if initialStats.AllocationCount != 0 {
		t.Errorf("Expected 0 initial allocations, got %d", initialStats.AllocationCount)
	}

	// Allocate some memory
	sizes := []int64{1024, 2048, 4096}
	ptrs := make([]CUDAMemoryPtr, len(sizes))

	for i, size := range sizes {
		ptr, err := pool.Allocate(size)
		if err != nil {
			t.Fatalf("Failed to allocate %d bytes: %v", size, err)
		}
		ptrs[i] = ptr
	}

	stats := pool.GetStatistics()
	if stats.AllocationCount != int64(len(sizes)) {
		t.Errorf("Expected %d allocations, got %d", len(sizes), stats.AllocationCount)
	}

	if stats.UsedSize == 0 {
		t.Error("Expected non-zero used size")
	}

	// Free memory
	for _, ptr := range ptrs {
		err := pool.Free(ptr)
		if err != nil {
			t.Fatalf("Failed to free pointer %v: %v", ptr, err)
		}
	}

	finalStats := pool.GetStatistics()
	if finalStats.DeallocationCount != int64(len(sizes)) {
		t.Errorf("Expected %d deallocations, got %d", len(sizes), finalStats.DeallocationCount)
	}
}
