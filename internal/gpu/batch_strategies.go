package gpu

import (
	"context"
	"fmt"
	"math"
	"sync"
	"time"
)

// BinarySearchStrategy uses binary search to find optimal batch size
type BinarySearchStrategy struct {
	config      *OptimizerConfig
	mu          sync.RWMutex
	minSize     int
	maxSize     int
	currentSize int
	targetBound int
	searchPhase string // "exploring" or "exploiting"
	bestSize    int
	bestScore   float64
	iterations  int
}

// NewBinarySearchStrategy creates a new binary search optimization strategy
func NewBinarySearchStrategy(config *OptimizerConfig) *BinarySearchStrategy {
	return &BinarySearchStrategy{
		config:      config,
		minSize:     config.MinBatchSize,
		maxSize:     config.MaxBatchSize,
		currentSize: config.DefaultBatchSize,
		targetBound: config.MaxBatchSize,
		searchPhase: "exploring",
		bestSize:    config.DefaultBatchSize,
		bestScore:   0.0,
		iterations:  0,
	}
}

// Name returns the strategy name
func (s *BinarySearchStrategy) Name() string {
	return "binary_search"
}

// OptimizeBatchSize uses binary search to find the optimal batch size
func (s *BinarySearchStrategy) OptimizeBatchSize(ctx context.Context, metrics *GPUPerformanceMetrics, history []*BatchPerformanceRecord) (*BatchSizeRecommendation, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// If we have recent history, use it to adjust our search
	if len(history) > 0 {
		recentRecord := history[len(history)-1]
		score := s.calculatePerformanceScore(recentRecord)

		if score > s.bestScore {
			s.bestScore = score
			s.bestSize = recentRecord.BatchSize
		}

		// Adjust search bounds based on performance
		if recentRecord.Success {
			if recentRecord.BatchSize == s.currentSize {
				if score > s.bestScore*0.95 { // Within 5% of best
					s.minSize = s.currentSize
				} else {
					s.maxSize = s.currentSize
				}
			}
		} else {
			// Failed batch, reduce upper bound
			s.maxSize = s.currentSize - 1
		}
	}

	// Binary search logic
	if s.maxSize <= s.minSize {
		// Search converged, exploit best known size
		s.searchPhase = "exploiting"
		s.currentSize = s.bestSize
	} else {
		// Continue binary search
		s.searchPhase = "exploring"
		s.currentSize = (s.minSize + s.maxSize) / 2
	}

	s.iterations++

	confidence := s.calculateConfidence()
	expectedThroughput := s.estimateThroughput(s.currentSize, metrics)
	expectedLatency := s.estimateLatency(s.currentSize, metrics)

	return &BatchSizeRecommendation{
		RecommendedSize:    s.currentSize,
		Confidence:         confidence,
		ExpectedThroughput: expectedThroughput,
		ExpectedLatency:    expectedLatency,
		MemoryUtilization:  metrics.MemoryUtilization,
		Strategy:           s.Name(),
		Metadata: map[string]interface{}{
			"search_phase": s.searchPhase,
			"min_size":     s.minSize,
			"max_size":     s.maxSize,
			"best_size":    s.bestSize,
			"best_score":   s.bestScore,
			"iterations":   s.iterations,
		},
		Timestamp: time.Now(),
	}, nil
}

// UpdateWithFeedback updates the strategy with performance feedback
func (s *BinarySearchStrategy) UpdateWithFeedback(ctx context.Context, batchSize int, performance *BatchPerformanceRecord) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	score := s.calculatePerformanceScore(performance)
	if score > s.bestScore {
		s.bestScore = score
		s.bestSize = batchSize
	}

	return nil
}

// Reset clears the strategy state
func (s *BinarySearchStrategy) Reset() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.minSize = s.config.MinBatchSize
	s.maxSize = s.config.MaxBatchSize
	s.currentSize = s.config.DefaultBatchSize
	s.searchPhase = "exploring"
	s.bestSize = s.config.DefaultBatchSize
	s.bestScore = 0.0
	s.iterations = 0

	return nil
}

// HeuristicStrategy uses heuristic rules based on system state
type HeuristicStrategy struct {
	config      *OptimizerConfig
	mu          sync.RWMutex
	recentSizes []int
	maxHistory  int
}

// NewHeuristicStrategy creates a new heuristic optimization strategy
func NewHeuristicStrategy(config *OptimizerConfig) *HeuristicStrategy {
	return &HeuristicStrategy{
		config:      config,
		recentSizes: make([]int, 0, 10),
		maxHistory:  10,
	}
}

// Name returns the strategy name
func (s *HeuristicStrategy) Name() string {
	return "heuristic"
}

// OptimizeBatchSize uses heuristic rules to determine optimal batch size
func (s *HeuristicStrategy) OptimizeBatchSize(ctx context.Context, metrics *GPUPerformanceMetrics, history []*BatchPerformanceRecord) (*BatchSizeRecommendation, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	baseSize := s.config.DefaultBatchSize

	// Heuristic rules based on GPU state
	if metrics.MemoryUtilization > 0.8 {
		// High memory usage, reduce batch size
		baseSize = int(float64(baseSize) * 0.7)
	} else if metrics.MemoryUtilization < 0.5 {
		// Low memory usage, can increase batch size
		baseSize = int(float64(baseSize) * 1.3)
	}

	if metrics.ComputeUtilization > 0.9 {
		// High compute usage, maintain current size
		baseSize = int(float64(baseSize) * 1.0)
	} else if metrics.ComputeUtilization < 0.6 {
		// Low compute usage, increase batch size
		baseSize = int(float64(baseSize) * 1.2)
	}

	// Consider queue length
	if metrics.QueuedRequests > 50 {
		// High queue, reduce batch size for better latency
		baseSize = int(float64(baseSize) * 0.8)
	} else if metrics.QueuedRequests < 10 {
		// Low queue, can increase batch size
		baseSize = int(float64(baseSize) * 1.1)
	}

	// Consider recent performance
	if len(history) > 0 {
		recentSuccessRate := s.calculateRecentSuccessRate(history)
		if recentSuccessRate < 0.8 {
			// Recent failures, be conservative
			baseSize = int(float64(baseSize) * 0.9)
		}
	}

	// Ensure within bounds
	if baseSize < s.config.MinBatchSize {
		baseSize = s.config.MinBatchSize
	}
	if baseSize > s.config.MaxBatchSize {
		baseSize = s.config.MaxBatchSize
	}

	// Update history
	s.recentSizes = append(s.recentSizes, baseSize)
	if len(s.recentSizes) > s.maxHistory {
		s.recentSizes = s.recentSizes[1:]
	}

	confidence := s.calculateConfidence(metrics)
	expectedThroughput := s.estimateThroughput(baseSize, metrics)
	expectedLatency := s.estimateLatency(baseSize, metrics)

	return &BatchSizeRecommendation{
		RecommendedSize:    baseSize,
		Confidence:         confidence,
		ExpectedThroughput: expectedThroughput,
		ExpectedLatency:    expectedLatency,
		MemoryUtilization:  metrics.MemoryUtilization,
		Strategy:           s.Name(),
		Metadata: map[string]interface{}{
			"memory_utilization":  metrics.MemoryUtilization,
			"compute_utilization": metrics.ComputeUtilization,
			"queued_requests":     metrics.QueuedRequests,
			"recent_sizes":        s.recentSizes,
		},
		Timestamp: time.Now(),
	}, nil
}

// UpdateWithFeedback updates the strategy with performance feedback
func (s *HeuristicStrategy) UpdateWithFeedback(ctx context.Context, batchSize int, performance *BatchPerformanceRecord) error {
	// Heuristic strategy learns from feedback implicitly through the rules
	return nil
}

// Reset clears the strategy state
func (s *HeuristicStrategy) Reset() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.recentSizes = make([]int, 0, s.maxHistory)
	return nil
}

// BanditStrategy uses multi-armed bandit to explore batch sizes
type BanditStrategy struct {
	config      *OptimizerConfig
	mu          sync.RWMutex
	arms        map[int]*BanditArm
	totalPlays  int
	exploration float64 // UCB exploration parameter
}

// BanditArm represents a batch size option in the multi-armed bandit
type BanditArm struct {
	BatchSize   int
	Plays       int
	TotalReward float64
	AvgReward   float64
	Confidence  float64
}

// NewBanditStrategy creates a new multi-armed bandit optimization strategy
func NewBanditStrategy(config *OptimizerConfig) *BanditStrategy {
	arms := make(map[int]*BanditArm)

	// Initialize arms for different batch sizes
	for size := config.MinBatchSize; size <= config.MaxBatchSize; size *= 2 {
		arms[size] = &BanditArm{
			BatchSize:   size,
			Plays:       0,
			TotalReward: 0.0,
			AvgReward:   0.0,
			Confidence:  0.0,
		}
	}

	return &BanditStrategy{
		config:      config,
		arms:        arms,
		totalPlays:  0,
		exploration: 2.0, // UCB exploration parameter
	}
}

// Name returns the strategy name
func (s *BanditStrategy) Name() string {
	return "bandit"
}

// OptimizeBatchSize uses UCB (Upper Confidence Bound) to select batch size
func (s *BanditStrategy) OptimizeBatchSize(ctx context.Context, metrics *GPUPerformanceMetrics, history []*BatchPerformanceRecord) (*BatchSizeRecommendation, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Update UCB scores for all arms
	s.updateUCBScores()

	// Select arm with highest UCB score
	selectedArm := s.selectBestArm()

	s.totalPlays++
	selectedArm.Plays++

	confidence := s.calculateConfidence(selectedArm)
	expectedThroughput := s.estimateThroughput(selectedArm.BatchSize, metrics)
	expectedLatency := s.estimateLatency(selectedArm.BatchSize, metrics)

	return &BatchSizeRecommendation{
		RecommendedSize:    selectedArm.BatchSize,
		Confidence:         confidence,
		ExpectedThroughput: expectedThroughput,
		ExpectedLatency:    expectedLatency,
		MemoryUtilization:  metrics.MemoryUtilization,
		Strategy:           s.Name(),
		Metadata: map[string]interface{}{
			"selected_arm":   selectedArm.BatchSize,
			"arm_plays":      selectedArm.Plays,
			"arm_avg_reward": selectedArm.AvgReward,
			"total_plays":    s.totalPlays,
			"ucb_scores":     s.getUCBScores(),
		},
		Timestamp: time.Now(),
	}, nil
}

// UpdateWithFeedback updates the selected arm with performance feedback
func (s *BanditStrategy) UpdateWithFeedback(ctx context.Context, batchSize int, performance *BatchPerformanceRecord) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	arm, exists := s.arms[batchSize]
	if !exists {
		return fmt.Errorf("arm for batch size %d not found", batchSize)
	}

	// Calculate reward based on performance
	reward := s.calculateReward(performance)

	// Update arm statistics
	arm.TotalReward += reward
	arm.AvgReward = arm.TotalReward / float64(arm.Plays)

	return nil
}

// Reset clears the strategy state
func (s *BanditStrategy) Reset() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	for _, arm := range s.arms {
		arm.Plays = 0
		arm.TotalReward = 0.0
		arm.AvgReward = 0.0
		arm.Confidence = 0.0
	}
	s.totalPlays = 0

	return nil
}

// Helper methods for strategies

func (s *BinarySearchStrategy) calculatePerformanceScore(record *BatchPerformanceRecord) float64 {
	if !record.Success {
		return 0.0
	}

	// Combine throughput and inverse latency for score
	latencyScore := 1.0 / (1.0 + float64(record.Latency.Milliseconds()))
	throughputScore := record.Throughput / 1000.0 // normalize

	return (latencyScore + throughputScore) / 2.0
}

func (s *BinarySearchStrategy) calculateConfidence() float64 {
	if s.iterations == 0 {
		return 0.1
	}

	// Confidence increases with iterations but caps at 0.9
	confidence := math.Min(0.9, float64(s.iterations)/20.0)

	if s.searchPhase == "exploiting" {
		confidence = math.Max(confidence, 0.8)
	}

	return confidence
}

func (s *BinarySearchStrategy) estimateThroughput(batchSize int, metrics *GPUPerformanceMetrics) float64 {
	// Simple estimation based on compute utilization and batch size
	baseRate := 100.0 // base requests per second
	utilizationFactor := metrics.ComputeUtilization
	batchFactor := math.Log2(float64(batchSize))

	return baseRate * utilizationFactor * batchFactor
}

func (s *BinarySearchStrategy) estimateLatency(batchSize int, metrics *GPUPerformanceMetrics) time.Duration {
	// Simple estimation - larger batches take longer but more efficient
	baseMsPerRequest := 10.0
	batchFactor := math.Sqrt(float64(batchSize))
	utilizationFactor := 1.0 / math.Max(0.1, metrics.ComputeUtilization)

	latencyMs := baseMsPerRequest * batchFactor * utilizationFactor
	return time.Duration(latencyMs) * time.Millisecond
}

func (s *HeuristicStrategy) calculateRecentSuccessRate(history []*BatchPerformanceRecord) float64 {
	if len(history) == 0 {
		return 1.0
	}

	// Look at last 10 records
	start := len(history) - 10
	if start < 0 {
		start = 0
	}

	successful := 0
	total := 0
	for i := start; i < len(history); i++ {
		if history[i].Success {
			successful++
		}
		total++
	}

	return float64(successful) / float64(total)
}

func (s *HeuristicStrategy) calculateConfidence(metrics *GPUPerformanceMetrics) float64 {
	// Confidence based on system stability
	memoryStability := 1.0 - math.Abs(metrics.MemoryUtilization-0.7)   // prefer 70% utilization
	computeStability := 1.0 - math.Abs(metrics.ComputeUtilization-0.8) // prefer 80% utilization

	return (memoryStability + computeStability) / 2.0
}

func (s *HeuristicStrategy) estimateThroughput(batchSize int, metrics *GPUPerformanceMetrics) float64 {
	baseRate := 100.0
	batchFactor := math.Log2(float64(batchSize))
	utilizationFactor := (metrics.ComputeUtilization + metrics.MemoryUtilization) / 2.0

	return baseRate * batchFactor * utilizationFactor
}

func (s *HeuristicStrategy) estimateLatency(batchSize int, metrics *GPUPerformanceMetrics) time.Duration {
	baseMsPerRequest := 10.0
	batchFactor := math.Sqrt(float64(batchSize))
	queueFactor := 1.0 + (float64(metrics.QueuedRequests) / 100.0)

	latencyMs := baseMsPerRequest * batchFactor * queueFactor
	return time.Duration(latencyMs) * time.Millisecond
}

func (s *BanditStrategy) updateUCBScores() {
	for _, arm := range s.arms {
		if arm.Plays == 0 {
			arm.Confidence = math.Inf(1) // Unplayed arms get infinite confidence
		} else {
			exploration := s.exploration * math.Sqrt(math.Log(float64(s.totalPlays))/float64(arm.Plays))
			arm.Confidence = arm.AvgReward + exploration
		}
	}
}

func (s *BanditStrategy) selectBestArm() *BanditArm {
	var bestArm *BanditArm
	bestScore := math.Inf(-1)

	for _, arm := range s.arms {
		if arm.Confidence > bestScore {
			bestScore = arm.Confidence
			bestArm = arm
		}
	}

	return bestArm
}

func (s *BanditStrategy) calculateConfidence(arm *BanditArm) float64 {
	if arm.Plays == 0 {
		return 0.1
	}

	// Confidence based on number of plays and reward
	playsFactor := math.Min(0.8, float64(arm.Plays)/50.0)
	rewardFactor := math.Min(0.2, arm.AvgReward)

	return playsFactor + rewardFactor
}

func (s *BanditStrategy) estimateThroughput(batchSize int, metrics *GPUPerformanceMetrics) float64 {
	baseRate := 100.0
	batchFactor := math.Log2(float64(batchSize))
	utilizationFactor := metrics.ComputeUtilization

	return baseRate * batchFactor * utilizationFactor
}

func (s *BanditStrategy) estimateLatency(batchSize int, metrics *GPUPerformanceMetrics) time.Duration {
	baseMsPerRequest := 10.0
	batchFactor := math.Sqrt(float64(batchSize))

	latencyMs := baseMsPerRequest * batchFactor
	return time.Duration(latencyMs) * time.Millisecond
}

func (s *BanditStrategy) calculateReward(performance *BatchPerformanceRecord) float64 {
	if !performance.Success {
		return -1.0 // Penalty for failures
	}

	// Reward based on throughput and inverse latency
	throughputReward := performance.Throughput / 1000.0
	latencyPenalty := float64(performance.Latency.Milliseconds()) / 1000.0

	return throughputReward - latencyPenalty
}

func (s *BanditStrategy) getUCBScores() map[int]float64 {
	scores := make(map[int]float64)
	for batchSize, arm := range s.arms {
		scores[batchSize] = arm.Confidence
	}
	return scores
}
