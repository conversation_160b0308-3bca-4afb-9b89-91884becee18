package gpu

import (
	"fmt"
	"math"
	"strings"
	"testing"
)

// Test basic kernel fusion engine creation and configuration
func TestNewKernelFusionEngine(t *testing.T) {
	config := DefaultKernelFusionConfig()

	// Create a mock compiler (nil for testing purposes)
	engine := NewKernelFusionEngine(config, nil)

	if engine == nil {
		t.<PERSON>("Expected non-nil kernel fusion engine")
	}

	if !engine.config.Enabled {
		t.E<PERSON>r("Expected fusion to be enabled by default")
	}

	if engine.config.MaxFusionDepth != 5 {
		t.<PERSON><PERSON><PERSON>("Expected max fusion depth 5, got %d", engine.config.MaxFusionDepth)
	}

	if engine.config.MinSpeedupThreshold != 1.2 {
		t.<PERSON><PERSON>("Expected min speedup threshold 1.2, got %f", engine.config.MinSpeedupThreshold)
	}

	// Check that patterns map is initialized
	if engine.patterns == nil {
		t.Error("Expected patterns map to be initialized")
	}

	// Check that cache is initialized
	if engine.optimizedCache == nil {
		t.Error("Expected optimized cache to be initialized")
	}
}

// Test default fusion configuration
func TestDefaultKernelFusionConfig(t *testing.T) {
	config := DefaultKernelFusionConfig()

	expectedBlockSizes := map[string]int{
		"elementwise": 256,
		"reduction":   128,
		"matmul":      16,
	}

	for opType, expectedSize := range expectedBlockSizes {
		if actualSize, exists := config.BlockSizeHints[opType]; !exists || actualSize != expectedSize {
			t.Errorf("Expected block size hint for %s to be %d, got %d", opType, expectedSize, actualSize)
		}
	}

	if config.SharedMemoryLimits["default"] != 48*1024 {
		t.Errorf("Expected default shared memory limit to be 48KB, got %d", config.SharedMemoryLimits["default"])
	}
}

// Test fusion operation creation
func TestFusionOperation(t *testing.T) {
	shape := TensorShape{100, 100}

	op := &FusionOperation{
		ID:          "test_op_1",
		Type:        FusionElementwise,
		Name:        "Add",
		Parameters:  make(map[string]interface{}),
		InputShapes: []TensorShape{shape, shape},
		OutputShape: shape,
		DataType:    TensorFloat32,
		Cost:        1.0,
	}

	if op.Type != FusionElementwise {
		t.Errorf("Expected operation type %s, got %s", FusionElementwise, op.Type)
	}

	if op.Name != "Add" {
		t.Errorf("Expected operation name 'Add', got '%s'", op.Name)
	}

	if op.Cost != 1.0 {
		t.Errorf("Expected operation cost 1.0, got %f", op.Cost)
	}
}

// Test fusion operation validation
func TestValidateFusionOperations(t *testing.T) {
	config := DefaultKernelFusionConfig()
	engine := NewKernelFusionEngine(config, nil)

	shape := TensorShape{100, 100}

	// Test valid operation sequence
	validOps := []*FusionOperation{
		{
			ID:          "op1",
			Type:        FusionElementwise,
			Name:        "Add",
			InputShapes: []TensorShape{shape, shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        1.0,
		},
		{
			ID:          "op2",
			Type:        FusionActivation,
			Name:        "ReLU",
			InputShapes: []TensorShape{shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        0.5,
		},
	}

	err := engine.validateFusionOperations(validOps)
	if err != nil {
		t.Errorf("Expected valid operations to pass validation, got error: %v", err)
	}

	// Test insufficient operations
	singleOp := []*FusionOperation{validOps[0]}
	err = engine.validateFusionOperations(singleOp)
	if err == nil {
		t.Error("Expected error for single operation, got nil")
	}

	// Test incompatible data types
	incompatibleOps := []*FusionOperation{
		{
			ID:          "op1",
			Type:        FusionElementwise,
			Name:        "Add",
			InputShapes: []TensorShape{shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        1.0,
		},
		{
			ID:          "op2",
			Type:        FusionActivation,
			Name:        "ReLU",
			InputShapes: []TensorShape{shape},
			OutputShape: shape,
			DataType:    TensorInt8, // Different data type
			Cost:        0.5,
		},
	}

	err = engine.validateFusionOperations(incompatibleOps)
	if err == nil {
		t.Error("Expected error for incompatible data types, got nil")
	}
}

// Test fusion type compatibility
func TestTypesCanFuse(t *testing.T) {
	config := DefaultKernelFusionConfig()
	engine := NewKernelFusionEngine(config, nil)

	testCases := []struct {
		type1    FusionOpType
		type2    FusionOpType
		expected bool
	}{
		{FusionElementwise, FusionActivation, true},
		{FusionElementwise, FusionElementwise, true},
		{FusionConvolution, FusionActivation, true},
		{FusionMatMul, FusionActivation, true},
		{FusionActivation, FusionConvolution, false}, // Not bidirectional
		{FusionPooling, FusionElementwise, false},
	}

	for _, tc := range testCases {
		result := engine.typesCanFuse(tc.type1, tc.type2)
		if result != tc.expected {
			t.Errorf("Expected fusion compatibility between %s and %s to be %v, got %v",
				tc.type1, tc.type2, tc.expected, result)
		}
	}
}

// Test shape compatibility checking
func TestShapesCompatible(t *testing.T) {
	config := DefaultKernelFusionConfig()
	engine := NewKernelFusionEngine(config, nil)

	shape1 := TensorShape{100, 100}
	shape2 := TensorShape{100, 100}
	shape3 := TensorShape{100, 50}
	shape4 := TensorShape{100, 100, 1}

	if !engine.shapesCompatible(shape1, shape2) {
		t.Error("Expected identical shapes to be compatible")
	}

	if engine.shapesCompatible(shape1, shape3) {
		t.Error("Expected different shapes to be incompatible")
	}

	if engine.shapesCompatible(shape1, shape4) {
		t.Error("Expected shapes with different dimensions to be incompatible")
	}
}

// Test pattern ID and name generation
func TestPatternGeneration(t *testing.T) {
	config := DefaultKernelFusionConfig()
	engine := NewKernelFusionEngine(config, nil)

	ops := []*FusionOperation{
		{Type: FusionElementwise, Name: "Add"},
		{Type: FusionActivation, Name: "ReLU"},
	}

	patternID := engine.generatePatternID(ops)
	expectedID := "elementwise_activation"
	if patternID != expectedID {
		t.Errorf("Expected pattern ID '%s', got '%s'", expectedID, patternID)
	}

	patternName := engine.generatePatternName(ops)
	expectedName := "Fused_Add_ReLU"
	if patternName != expectedName {
		t.Errorf("Expected pattern name '%s', got '%s'", expectedName, patternName)
	}
}

// Test cost calculations
func TestCostCalculations(t *testing.T) {
	config := DefaultKernelFusionConfig()
	engine := NewKernelFusionEngine(config, nil)

	shape := TensorShape{100, 100}
	ops := []*FusionOperation{
		{
			Type:        FusionElementwise,
			Name:        "Add",
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        1.0,
		},
		{
			Type:        FusionActivation,
			Name:        "ReLU",
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        0.5,
		},
	}

	separateCost := engine.calculateSeparateCost(ops)
	expectedSeparate := 1.0 + 0.5 + 0.1 // ops cost + memory transfer
	if separateCost != expectedSeparate {
		t.Errorf("Expected separate cost %f, got %f", expectedSeparate, separateCost)
	}

	fusionCost := engine.calculateFusionCost(ops)
	expectedFusion := (1.0+0.5)*0.9 + 0.05 // reduced ops cost + single transfer
	if math.Abs(fusionCost-expectedFusion) > 1e-9 {
		t.Errorf("Expected fusion cost %f, got %f", expectedFusion, fusionCost)
	}

	memorySaved := engine.calculateMemorySavings(ops)
	expectedMemory := shape.NumElements() * int64(TensorFloat32.Size())
	if memorySaved != expectedMemory {
		t.Errorf("Expected memory savings %d, got %d", expectedMemory, memorySaved)
	}
}

// Test successful fusion operation
func TestFuseOperations(t *testing.T) {
	config := DefaultKernelFusionConfig()
	config.MinSpeedupThreshold = 1.0 // Lower threshold for testing
	engine := NewKernelFusionEngine(config, nil)

	shape := TensorShape{100, 100}
	ops := []*FusionOperation{
		{
			ID:          "op1",
			Type:        FusionElementwise,
			Name:        "Add",
			InputShapes: []TensorShape{shape, shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        1.0,
		},
		{
			ID:          "op2",
			Type:        FusionActivation,
			Name:        "ReLU",
			InputShapes: []TensorShape{shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        0.5,
		},
	}

	pattern, err := engine.FuseOperations(ops)
	if err != nil {
		t.Fatalf("Expected successful fusion, got error: %v", err)
	}

	if pattern == nil {
		t.Fatal("Expected non-nil fusion pattern")
	}

	if pattern.ID != "elementwise_activation" {
		t.Errorf("Expected pattern ID 'elementwise_activation', got '%s'", pattern.ID)
	}

	if len(pattern.Operations) != 2 {
		t.Errorf("Expected 2 operations in pattern, got %d", len(pattern.Operations))
	}

	if pattern.Speedup <= 1.0 {
		t.Errorf("Expected speedup > 1.0, got %f", pattern.Speedup)
	}

	// Check metrics update
	metrics := engine.GetMetrics()
	if metrics.SuccessfulFusions != 1 {
		t.Errorf("Expected 1 successful fusion, got %d", metrics.SuccessfulFusions)
	}
}

// Test fusion with insufficient speedup
func TestFuseOperationsInsufficientSpeedup(t *testing.T) {
	config := DefaultKernelFusionConfig()
	config.MinSpeedupThreshold = 10.0 // Very high threshold
	engine := NewKernelFusionEngine(config, nil)

	shape := TensorShape{100, 100}
	ops := []*FusionOperation{
		{
			ID:          "op1",
			Type:        FusionElementwise,
			Name:        "Add",
			InputShapes: []TensorShape{shape, shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        1.0,
		},
		{
			ID:          "op2",
			Type:        FusionActivation,
			Name:        "ReLU",
			InputShapes: []TensorShape{shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        0.5,
		},
	}

	_, err := engine.FuseOperations(ops)
	if err == nil {
		t.Error("Expected error for insufficient speedup, got nil")
	}

	// Check metrics update
	metrics := engine.GetMetrics()
	if metrics.FailedFusions != 1 {
		t.Errorf("Expected 1 failed fusion, got %d", metrics.FailedFusions)
	}
}

// Test finding fusion opportunities
func TestFindFusionOpportunities(t *testing.T) {
	config := DefaultKernelFusionConfig()
	config.MinSpeedupThreshold = 1.0 // Lower threshold for testing
	engine := NewKernelFusionEngine(config, nil)

	shape := TensorShape{100, 100}
	ops := []*FusionOperation{
		{
			ID:          "op1",
			Type:        FusionElementwise,
			Name:        "Add",
			InputShapes: []TensorShape{shape, shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        1.0,
		},
		{
			ID:          "op2",
			Type:        FusionActivation,
			Name:        "ReLU",
			InputShapes: []TensorShape{shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        0.5,
		},
		{
			ID:          "op3",
			Type:        FusionElementwise,
			Name:        "Scale",
			InputShapes: []TensorShape{shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        0.3,
		},
	}

	opportunities, err := engine.FindFusionOpportunities(ops)
	if err != nil {
		t.Fatalf("Expected successful opportunity finding, got error: %v", err)
	}

	if len(opportunities) == 0 {
		t.Error("Expected at least one fusion opportunity")
	}

	// Should find at least the elementwise+activation pattern
	found := false
	for _, opp := range opportunities {
		if opp.ID == "elementwise_activation" {
			found = true
			break
		}
	}

	if !found {
		t.Error("Expected to find elementwise_activation fusion opportunity")
	}
}

// Test kernel parameter generation
func TestGenerateKernelParameters(t *testing.T) {
	config := DefaultKernelFusionConfig()
	engine := NewKernelFusionEngine(config, nil)

	pattern := &FusionPattern{
		Operations: []*FusionOperation{
			{
				Type: FusionElementwise,
				Name: "Add",
			},
		},
	}

	params := engine.generateKernelParameters(pattern)

	if len(params) < 2 {
		t.Errorf("Expected at least 2 parameters (input, output), got %d", len(params))
	}

	// Check for input parameter
	foundInput := false
	foundOutput := false
	for _, param := range params {
		if param.Name == "input" {
			foundInput = true
			if param.Type != "float*" {
				t.Errorf("Expected input parameter type 'float*', got '%s'", param.Type)
			}
		}
		if param.Name == "output" {
			foundOutput = true
			if param.Type != "float*" {
				t.Errorf("Expected output parameter type 'float*', got '%s'", param.Type)
			}
		}
	}

	if !foundInput {
		t.Error("Expected to find input parameter")
	}
	if !foundOutput {
		t.Error("Expected to find output parameter")
	}
}

// Test CUDA kernel source generation
func TestGenerateCUDAKernelSource(t *testing.T) {
	config := DefaultKernelFusionConfig()
	engine := NewKernelFusionEngine(config, nil)

	target := CompilationTarget{
		Architecture:       "sm_75",
		MaxThreadsPerBlock: 1024,
	}

	pattern := &FusionPattern{
		Name: "Test_Fusion",
		Operations: []*FusionOperation{
			{
				Type:        FusionElementwise,
				Name:        "Add",
				OutputShape: TensorShape{100, 100},
			},
			{
				Type: FusionActivation,
				Name: "ReLU",
			},
		},
	}

	source := engine.generateCUDAKernelSource(pattern, target)

	// Check for essential kernel components
	if !strings.Contains(source, "__global__ void") {
		t.Error("Expected kernel to contain '__global__ void'")
	}

	if !strings.Contains(source, "Test_Fusion_kernel") {
		t.Error("Expected kernel to contain pattern name")
	}

	if !strings.Contains(source, "blockIdx.x") {
		t.Error("Expected kernel to contain thread indexing")
	}

	if !strings.Contains(source, "temp = temp + other[i]") {
		t.Error("Expected kernel to contain Add operation")
	}

	if !strings.Contains(source, "temp = fmaxf(0.0f, temp)") {
		t.Error("Expected kernel to contain ReLU operation")
	}
}

// Test launch parameter calculation
func TestCalculateOptimalGridSize(t *testing.T) {
	config := DefaultKernelFusionConfig()
	engine := NewKernelFusionEngine(config, nil)

	target := CompilationTarget{
		Architecture:       "sm_75",
		MaxThreadsPerBlock: 1024,
	}

	pattern := &FusionPattern{
		Operations: []*FusionOperation{
			{
				OutputShape: TensorShape{1000, 1000}, // 1M elements
			},
		},
	}

	blockSize := 256
	gridSize := engine.calculateOptimalGridSize(pattern, blockSize, target)

	expectedGridSize := int((1000*1000 + int64(blockSize) - 1) / int64(blockSize))
	if gridSize != expectedGridSize {
		t.Errorf("Expected grid size %d, got %d", expectedGridSize, gridSize)
	}
}

// Test shared memory calculation
func TestCalculateSharedMemoryUsage(t *testing.T) {
	config := DefaultKernelFusionConfig()
	engine := NewKernelFusionEngine(config, nil)

	pattern := &FusionPattern{
		Operations: []*FusionOperation{
			{Type: FusionElementwise},
		},
	}

	blockSize := 256
	sharedMem := engine.calculateSharedMemoryUsage(pattern, blockSize)

	expectedBasic := int64(blockSize * 4) // 4 bytes per thread
	if sharedMem != expectedBasic {
		t.Errorf("Expected shared memory %d, got %d", expectedBasic, sharedMem)
	}

	// Test with reduction operation (should use more memory)
	patternWithReduction := &FusionPattern{
		Operations: []*FusionOperation{
			{Type: FusionReduction},
		},
	}

	sharedMemReduction := engine.calculateSharedMemoryUsage(patternWithReduction, blockSize)
	if sharedMemReduction <= sharedMem {
		t.Error("Expected reduction operation to use more shared memory")
	}
}

// Test metrics tracking
func TestMetricsTracking(t *testing.T) {
	config := DefaultKernelFusionConfig()
	config.MinSpeedupThreshold = 1.0
	engine := NewKernelFusionEngine(config, nil)

	// Initial metrics should be zero
	metrics := engine.GetMetrics()
	if metrics.TotalFusions != 0 {
		t.Errorf("Expected initial total fusions to be 0, got %d", metrics.TotalFusions)
	}

	// Perform a successful fusion
	shape := TensorShape{100, 100}
	ops := []*FusionOperation{
		{
			ID:          "op1",
			Type:        FusionElementwise,
			Name:        "Add",
			InputShapes: []TensorShape{shape, shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        1.0,
		},
		{
			ID:          "op2",
			Type:        FusionActivation,
			Name:        "ReLU",
			InputShapes: []TensorShape{shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        0.5,
		},
	}

	_, err := engine.FuseOperations(ops)
	if err != nil {
		t.Fatalf("Fusion failed: %v", err)
	}

	// Check updated metrics
	metrics = engine.GetMetrics()
	if metrics.SuccessfulFusions != 1 {
		t.Errorf("Expected 1 successful fusion, got %d", metrics.SuccessfulFusions)
	}

	if metrics.AverageSpeedup <= 1.0 {
		t.Errorf("Expected average speedup > 1.0, got %f", metrics.AverageSpeedup)
	}

	if metrics.TotalMemorySaved <= 0 {
		t.Errorf("Expected memory savings > 0, got %d", metrics.TotalMemorySaved)
	}
}

// Test cache functionality
func TestCacheFunctionality(t *testing.T) {
	config := DefaultKernelFusionConfig()
	engine := NewKernelFusionEngine(config, nil)

	// Test cache clearing
	engine.ClearCache()

	// Verify cache is empty
	if len(engine.optimizedCache) != 0 {
		t.Errorf("Expected empty cache after clearing, got %d entries", len(engine.optimizedCache))
	}

	// Test pattern retrieval
	patterns := engine.GetFusionPatterns()
	if patterns == nil {
		t.Error("Expected non-nil patterns map")
	}
}

// Test disabled fusion engine
func TestDisabledFusionEngine(t *testing.T) {
	config := DefaultKernelFusionConfig()
	config.Enabled = false
	engine := NewKernelFusionEngine(config, nil)

	ops := []*FusionOperation{
		{Type: FusionElementwise, Name: "Add"},
		{Type: FusionActivation, Name: "ReLU"},
	}

	_, err := engine.FuseOperations(ops)
	if err == nil {
		t.Error("Expected error when fusion is disabled, got nil")
	}
}

// Benchmark kernel fusion performance
func BenchmarkFuseOperations(b *testing.B) {
	config := DefaultKernelFusionConfig()
	config.MinSpeedupThreshold = 1.0
	engine := NewKernelFusionEngine(config, nil)

	shape := TensorShape{1000, 1000}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		ops := []*FusionOperation{
			{
				ID:          fmt.Sprintf("op1_%d", i),
				Type:        FusionElementwise,
				Name:        "Add",
				InputShapes: []TensorShape{shape, shape},
				OutputShape: shape,
				DataType:    TensorFloat32,
				Cost:        1.0,
			},
			{
				ID:          fmt.Sprintf("op2_%d", i),
				Type:        FusionActivation,
				Name:        "ReLU",
				InputShapes: []TensorShape{shape},
				OutputShape: shape,
				DataType:    TensorFloat32,
				Cost:        0.5,
			},
		}

		_, err := engine.FuseOperations(ops)
		if err != nil {
			b.Fatalf("Fusion failed: %v", err)
		}
	}
}

// Benchmark pattern matching
func BenchmarkFindFusionOpportunities(b *testing.B) {
	config := DefaultKernelFusionConfig()
	config.MinSpeedupThreshold = 1.0
	engine := NewKernelFusionEngine(config, nil)

	shape := TensorShape{100, 100}
	ops := []*FusionOperation{
		{
			ID:          "op1",
			Type:        FusionElementwise,
			Name:        "Add",
			InputShapes: []TensorShape{shape, shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        1.0,
		},
		{
			ID:          "op2",
			Type:        FusionActivation,
			Name:        "ReLU",
			InputShapes: []TensorShape{shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        0.5,
		},
		{
			ID:          "op3",
			Type:        FusionElementwise,
			Name:        "Scale",
			InputShapes: []TensorShape{shape},
			OutputShape: shape,
			DataType:    TensorFloat32,
			Cost:        0.3,
		},
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := engine.FindFusionOpportunities(ops)
		if err != nil {
			b.Fatalf("Pattern matching failed: %v", err)
		}
	}
}

// Benchmark CUDA kernel source generation
func BenchmarkGenerateCUDAKernelSource(b *testing.B) {
	config := DefaultKernelFusionConfig()
	engine := NewKernelFusionEngine(config, nil)

	target := CompilationTarget{
		Architecture:       "sm_75",
		MaxThreadsPerBlock: 1024,
	}

	pattern := &FusionPattern{
		Name: "Benchmark_Fusion",
		Operations: []*FusionOperation{
			{
				Type:        FusionElementwise,
				Name:        "Add",
				OutputShape: TensorShape{1000, 1000},
			},
			{
				Type: FusionActivation,
				Name: "ReLU",
			},
		},
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_ = engine.generateCUDAKernelSource(pattern, target)
	}
}
