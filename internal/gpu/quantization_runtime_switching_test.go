package gpu

import (
	"fmt"
	"testing"
	"time"
)

func TestNewRuntimeQuantizationSwitcher(t *testing.T) {
	// Create required engines
	qConfig := QuantizationConfig{
		Method:                 QuantizationSymmetric,
		TargetType:             TensorInt8,
		UseCalibration:         true,
		PreserveAccuracy:       true,
		PerChannelQuantization: false,
	}
	qEngine := NewQuantizationEngine(qConfig)

	mpConfig := DefaultMixedPrecisionConfig()
	mpEngine := NewMixedPrecisionEngine(mpConfig, nil)

	apConfig := DefaultAccuracyPreservationConfig()
	apEngine := NewAccuracyPreservationEngine(apConfig, qEngine, mpEngine)

	// Create runtime switching config
	runtimeConfig := DefaultRuntimeSwitchingConfig()

	// Create switcher
	switcher := NewRuntimeQuantizationSwitcher(
		runtimeConfig,
		qEngine,
		mpEngine,
		apEngine,
		nil, // Mock hardware info
	)

	if switcher == nil {
		t.Fatal("Expected non-nil runtime quantization switcher")
	}

	if switcher.config.Mode != SwitchingAdaptive {
		t.Errorf("Expected adaptive mode, got %s", switcher.config.Mode)
	}

	if len(switcher.memoryPools) == 0 {
		t.Error("Expected memory pools to be initialized")
	}

	if switcher.metricsCollector == nil {
		t.Error("Expected metrics collector to be initialized")
	}

	if switcher.decisionEngine == nil {
		t.Error("Expected decision engine to be initialized")
	}
}

func TestDefaultRuntimeSwitchingConfig(t *testing.T) {
	config := DefaultRuntimeSwitchingConfig()

	if config.Mode != SwitchingAdaptive {
		t.Errorf("Expected adaptive mode, got %s", config.Mode)
	}

	if config.ActiveProfile != "balanced" {
		t.Errorf("Expected balanced profile, got %s", config.ActiveProfile)
	}

	if len(config.SwitchingProfiles) == 0 {
		t.Error("Expected switching profiles to be defined")
	}

	// Check balanced profile
	balancedProfile, exists := config.SwitchingProfiles["balanced"]
	if !exists {
		t.Error("Expected balanced profile to exist")
	} else {
		if balancedProfile.DefaultPrecision != PrecisionFP32 {
			t.Errorf("Expected FP32 default precision, got %s", balancedProfile.DefaultPrecision)
		}

		if len(balancedProfile.PrecisionHierarchy) == 0 {
			t.Error("Expected precision hierarchy to be defined")
		}

		if len(balancedProfile.SwitchingTriggers) == 0 {
			t.Error("Expected switching triggers to be defined")
		}
	}

	// Check memory pools
	if len(config.MemoryPoolSizePerPrecision) != 4 {
		t.Errorf("Expected 4 memory pools, got %d", len(config.MemoryPoolSizePerPrecision))
	}

	for precision, size := range config.MemoryPoolSizePerPrecision {
		if size <= 0 {
			t.Errorf("Expected positive memory pool size for %s, got %d", precision, size)
		}
	}
}

func TestRuntimeSwitchingModeString(t *testing.T) {
	testCases := []struct {
		mode     RuntimeSwitchingMode
		expected string
	}{
		{SwitchingAdaptive, "adaptive"},
		{SwitchingHybrid, "hybrid"},
		{SwitchingManual, "manual"},
		{SwitchingAutomatic, "automatic"},
		{RuntimeSwitchingMode(999), "unknown"},
	}

	for _, tc := range testCases {
		result := tc.mode.String()
		if result != tc.expected {
			t.Errorf("Expected %s for mode %d, got %s", tc.expected, tc.mode, result)
		}
	}
}

func TestRuntimeQuantizationSwitcher_AddLayer(t *testing.T) {
	// Setup
	runtimeConfig := DefaultRuntimeSwitchingConfig()
	switcher := NewRuntimeQuantizationSwitcher(
		runtimeConfig,
		NewQuantizationEngine(QuantizationConfig{}),
		NewMixedPrecisionEngine(DefaultMixedPrecisionConfig(), nil),
		nil,
		nil,
	)

	// Create test layer
	layer := NewLayer("test_layer", "test_conv", LayerTypeConvolution)
	layer.SetPrecision(PrecisionFP32)

	// Test adding layer
	err := switcher.AddLayer(layer)
	if err != nil {
		t.Fatalf("Failed to add layer: %v", err)
	}

	// Verify layer was added
	state, err := switcher.GetLayerPrecisionState("test_layer")
	if err != nil {
		t.Fatalf("Failed to get layer state: %v", err)
	}

	if state.LayerID != "test_layer" {
		t.Errorf("Expected layer ID test_layer, got %s", state.LayerID)
	}

	if state.CurrentPrecision != PrecisionFP32 {
		t.Errorf("Expected FP32 precision, got %s", state.CurrentPrecision)
	}

	if state.TargetPrecision != PrecisionFP32 {
		t.Errorf("Expected FP32 target precision, got %s", state.TargetPrecision)
	}

	// Test adding nil layer
	err = switcher.AddLayer(nil)
	if err == nil {
		t.Error("Expected error when adding nil layer")
	}
}

func TestRuntimeQuantizationSwitcher_RemoveLayer(t *testing.T) {
	// Setup
	runtimeConfig := DefaultRuntimeSwitchingConfig()
	switcher := NewRuntimeQuantizationSwitcher(
		runtimeConfig,
		NewQuantizationEngine(QuantizationConfig{}),
		NewMixedPrecisionEngine(DefaultMixedPrecisionConfig(), nil),
		nil,
		nil,
	)

	// Add layer first
	layer := NewLayer("test_layer", "test_conv", LayerTypeConvolution)
	switcher.AddLayer(layer)

	// Verify layer exists
	_, err := switcher.GetLayerPrecisionState("test_layer")
	if err != nil {
		t.Fatalf("Layer should exist before removal")
	}

	// Remove layer
	err = switcher.RemoveLayer("test_layer")
	if err != nil {
		t.Fatalf("Failed to remove layer: %v", err)
	}

	// Verify layer is removed
	_, err = switcher.GetLayerPrecisionState("test_layer")
	if err == nil {
		t.Error("Expected error when getting removed layer state")
	}
}

func TestRuntimeQuantizationSwitcher_RequestPrecisionSwitch(t *testing.T) {
	// Setup
	runtimeConfig := DefaultRuntimeSwitchingConfig()
	switcher := NewRuntimeQuantizationSwitcher(
		runtimeConfig,
		NewQuantizationEngine(QuantizationConfig{}),
		NewMixedPrecisionEngine(DefaultMixedPrecisionConfig(), nil),
		nil,
		nil,
	)

	// Add layer
	layer := NewLayer("test_layer", "test_conv", LayerTypeConvolution)
	layer.SetPrecision(PrecisionFP32)
	switcher.AddLayer(layer)

	// Test valid precision switch request
	err := switcher.RequestPrecisionSwitch("test_layer", PrecisionINT8, 5, "test_reason")
	if err != nil {
		t.Fatalf("Failed to request precision switch: %v", err)
	}

	// Test invalid layer ID
	err = switcher.RequestPrecisionSwitch("nonexistent_layer", PrecisionINT8, 5, "test_reason")
	if err != nil {
		// This is expected to not error at request time, but will be ignored during processing
	}

	// Test empty layer ID
	err = switcher.RequestPrecisionSwitch("", PrecisionINT8, 5, "test_reason")
	if err == nil {
		t.Error("Expected error for empty layer ID")
	}
}

func TestRuntimeQuantizationSwitcher_GetSwitchingSummary(t *testing.T) {
	// Setup
	runtimeConfig := DefaultRuntimeSwitchingConfig()
	switcher := NewRuntimeQuantizationSwitcher(
		runtimeConfig,
		NewQuantizationEngine(QuantizationConfig{}),
		NewMixedPrecisionEngine(DefaultMixedPrecisionConfig(), nil),
		nil,
		nil,
	)

	// Add some layers
	layer1 := NewLayer("layer1", "conv1", LayerTypeConvolution)
	layer1.SetPrecision(PrecisionFP32)
	switcher.AddLayer(layer1)

	layer2 := NewLayer("layer2", "conv2", LayerTypeConvolution)
	layer2.SetPrecision(PrecisionINT8)
	switcher.AddLayer(layer2)

	// Get summary
	summary := switcher.GetSwitchingSummary()

	// Verify basic fields
	if summary["mode"] != "adaptive" {
		t.Errorf("Expected adaptive mode, got %v", summary["mode"])
	}

	if summary["active_profile"] != "balanced" {
		t.Errorf("Expected balanced profile, got %v", summary["active_profile"])
	}

	if summary["running"] != false {
		t.Errorf("Expected running=false, got %v", summary["running"])
	}

	if summary["total_layers"] != 2 {
		t.Errorf("Expected 2 total layers, got %v", summary["total_layers"])
	}

	// Verify precision distribution
	precisionDist, ok := summary["precision_distribution"].(map[PrecisionMode]int)
	if !ok {
		t.Fatal("Expected precision_distribution to be a map")
	}

	if precisionDist[PrecisionFP32] != 1 {
		t.Errorf("Expected 1 FP32 layer, got %d", precisionDist[PrecisionFP32])
	}

	if precisionDist[PrecisionINT8] != 1 {
		t.Errorf("Expected 1 INT8 layer, got %d", precisionDist[PrecisionINT8])
	}

	// Verify memory pools
	memoryStatus, ok := summary["memory_pools"].(map[PrecisionMode]map[string]interface{})
	if !ok {
		t.Fatal("Expected memory_pools to be a map")
	}

	for precision := range runtimeConfig.MemoryPoolSizePerPrecision {
		if _, exists := memoryStatus[precision]; !exists {
			t.Errorf("Expected memory status for precision %s", precision)
		}
	}
}

func TestRuntimeQuantizationSwitcher_StartStop(t *testing.T) {
	t.Parallel()
	// Setup
	runtimeConfig := DefaultRuntimeSwitchingConfig()
	runtimeConfig.MetricsCollectionInterval = time.Millisecond * 10 // Fast for testing
	runtimeConfig.DecisionInterval = time.Millisecond * 20

	switcher := NewRuntimeQuantizationSwitcher(
		runtimeConfig,
		NewQuantizationEngine(QuantizationConfig{}),
		NewMixedPrecisionEngine(DefaultMixedPrecisionConfig(), nil),
		nil,
		nil,
	)

	// Test starting
	err := switcher.Start()
	if err != nil {
		t.Fatalf("Failed to start switcher: %v", err)
	}
	defer switcher.Stop()

	// Verify running state
	summary := switcher.GetSwitchingSummary()
	if summary["running"] != true {
		t.Error("Expected switcher to be running after start")
	}

	// Test starting again (should error)
	err = switcher.Start()
	if err == nil {
		t.Error("Expected error when starting already running switcher")
	}

	// Let it run briefly to collect some metrics
	time.Sleep(time.Millisecond * 50)

	// Test stopping
	err = switcher.Stop()
	if err != nil {
		t.Fatalf("Failed to stop switcher: %v", err)
	}

	// Verify stopped state
	summary = switcher.GetSwitchingSummary()
	if summary["running"] != false {
		t.Error("Expected switcher to be stopped after stop")
	}

	// Test stopping again (should error)
	err = switcher.Stop()
	if err == nil {
		t.Error("Expected error when stopping already stopped switcher")
	}
}

func TestRuntimeQuantizationSwitcher_MetricsCollection(t *testing.T) {
	t.Parallel()
	// Setup
	runtimeConfig := DefaultRuntimeSwitchingConfig()
	runtimeConfig.MetricsCollectionInterval = time.Millisecond * 10

	switcher := NewRuntimeQuantizationSwitcher(
		runtimeConfig,
		NewQuantizationEngine(QuantizationConfig{}),
		NewMixedPrecisionEngine(DefaultMixedPrecisionConfig(), nil),
		nil,
		nil,
	)

	// Add some layers to affect metrics
	layer1 := NewLayer("layer1", "conv1", LayerTypeConvolution)
	layer1.SetPrecision(PrecisionFP32)
	switcher.AddLayer(layer1)

	layer2 := NewLayer("layer2", "conv2", LayerTypeConvolution)
	layer2.SetPrecision(PrecisionINT8)
	switcher.AddLayer(layer2)

	// Start and let it collect metrics
	err := switcher.Start()
	if err != nil {
		t.Fatalf("Failed to start switcher: %v", err)
	}
	defer switcher.Stop()

	// Wait for metrics collection
	time.Sleep(time.Millisecond * 100)

	// Check if metrics are being collected
	metrics := switcher.GetRuntimeMetrics()
	if metrics == nil {
		t.Error("Expected runtime metrics to be available")
	} else {
		if metrics.ThroughputOpsPerSec <= 0 {
			t.Error("Expected positive throughput")
		}

		if metrics.LatencyMs <= 0 {
			t.Error("Expected positive latency")
		}

		if metrics.AccuracyScore < 0 || metrics.AccuracyScore > 1 {
			t.Errorf("Expected accuracy score in [0,1], got %f", metrics.AccuracyScore)
		}
	}
}

func TestMetricsCollector(t *testing.T) {
	collector := NewMetricsCollector(nil, time.Second)

	if collector == nil {
		t.Fatal("Expected non-nil metrics collector")
	}

	// Test adding metrics
	metrics1 := &RuntimeMetrics{
		Timestamp:           time.Now(),
		ThroughputOpsPerSec: 1000.0,
		LatencyMs:           50.0,
		AccuracyScore:       0.95,
	}

	collector.addMetrics(metrics1)

	// Check that metrics were added
	collector.mutex.RLock()
	historyLen := len(collector.metricsHistory)
	collector.mutex.RUnlock()

	if historyLen != 1 {
		t.Errorf("Expected 1 metrics entry, got %d", historyLen)
	}

	// Add more metrics to test history management
	for i := 0; i < 600; i++ { // Exceed max history size
		metrics := &RuntimeMetrics{
			Timestamp:           time.Now(),
			ThroughputOpsPerSec: float32(1000 + i),
		}
		collector.addMetrics(metrics)
	}

	// Check that history is bounded
	collector.mutex.RLock()
	finalHistoryLen := len(collector.metricsHistory)
	collector.mutex.RUnlock()

	if finalHistoryLen > 500 {
		t.Errorf("Expected history to be bounded to 500, got %d", finalHistoryLen)
	}
}

func TestPrecisionDecisionEngine(t *testing.T) {
	config := DefaultRuntimeSwitchingConfig()
	engine := NewPrecisionDecisionEngine(config)

	if engine == nil {
		t.Fatal("Expected non-nil decision engine")
	}

	if engine.currentProfile == nil {
		t.Error("Expected current profile to be set")
	}

	if len(engine.weightFactors) == 0 {
		t.Error("Expected weight factors to be initialized")
	}

	// Test decision making with mock data
	layerState := &LayerPrecisionState{
		LayerID:          "test_layer",
		CurrentPrecision: PrecisionFP32,
		TargetPrecision:  PrecisionFP32,
		LastSwitchTime:   time.Now().Add(-time.Hour), // Long ago
	}

	// Create metrics that should trigger a switch
	metrics := &RuntimeMetrics{
		Timestamp:     time.Now(),
		MemoryUsageMB: 900.0, // High memory usage
		LatencyMs:     120.0, // High latency
		AccuracyScore: 0.95,
	}

	decision := engine.makeDecision("test_layer", layerState, metrics)
	if decision != nil {
		if decision.LayerID != "test_layer" {
			t.Errorf("Expected layer ID test_layer, got %s", decision.LayerID)
		}

		if decision.FromPrecision != PrecisionFP32 {
			t.Errorf("Expected from precision FP32, got %s", decision.FromPrecision)
		}

		if decision.ConfidenceScore < 0 || decision.ConfidenceScore > 1 {
			t.Errorf("Expected confidence score in [0,1], got %f", decision.ConfidenceScore)
		}

		if len(decision.ExpectedBenefit) == 0 {
			t.Error("Expected expected benefits to be calculated")
		}
	}
}

func TestPrecisionPredictiveModel(t *testing.T) {
	model := NewPrecisionPredictiveModel("exponential")

	if model == nil {
		t.Fatal("Expected non-nil predictive model")
	}

	if model.modelType != "exponential" {
		t.Errorf("Expected exponential model type, got %s", model.modelType)
	}

	if !model.isEnabled {
		t.Error("Expected model to be enabled by default")
	}

	// Test training with insufficient data
	metrics := []*RuntimeMetrics{
		{Timestamp: time.Now(), AccuracyScore: 0.95},
		{Timestamp: time.Now(), AccuracyScore: 0.94},
	}

	model.trainModel(metrics)

	// Test prediction with insufficient training data
	prediction := model.predictOptimalPrecision("test_layer", &RuntimeMetrics{
		MemoryUsageMB: 500.0,
		LatencyMs:     30.0,
		AccuracyScore: 0.95,
	})

	if prediction != nil {
		t.Error("Expected nil prediction with insufficient training data")
	}

	// Add more training data
	for i := 0; i < 10; i++ {
		model.trainModel([]*RuntimeMetrics{
			{Timestamp: time.Now(), AccuracyScore: 0.95 - float32(i)*0.01},
		})
	}

	// Test prediction with sufficient data
	prediction = model.predictOptimalPrecision("test_layer", &RuntimeMetrics{
		MemoryUsageMB: 900.0, // High memory usage
		LatencyMs:     30.0,
		AccuracyScore: 0.95,
	})

	if prediction == nil {
		t.Error("Expected non-nil prediction with sufficient training data")
	} else {
		if prediction.RecommendedPrecision == "" {
			t.Error("Expected recommended precision to be set")
		}

		if prediction.Confidence < 0 || prediction.Confidence > 1 {
			t.Errorf("Expected confidence in [0,1], got %f", prediction.Confidence)
		}

		if prediction.Reasoning == "" {
			t.Error("Expected reasoning to be provided")
		}
	}
}

func BenchmarkRuntimeQuantizationSwitcher_MetricsCollection(b *testing.B) {
	// Setup
	runtimeConfig := DefaultRuntimeSwitchingConfig()
	switcher := NewRuntimeQuantizationSwitcher(
		runtimeConfig,
		NewQuantizationEngine(QuantizationConfig{}),
		NewMixedPrecisionEngine(DefaultMixedPrecisionConfig(), nil),
		nil,
		nil,
	)

	// Add some layers
	for i := 0; i < 10; i++ {
		layer := NewLayer(fmt.Sprintf("layer_%d", i), "conv", LayerTypeConvolution)
		switcher.AddLayer(layer)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		metrics := switcher.collectRuntimeMetrics()
		if metrics == nil {
			b.Error("Expected non-nil metrics")
		}
	}
}

func BenchmarkRuntimeQuantizationSwitcher_PrecisionSwitchRequest(b *testing.B) {
	// Setup
	runtimeConfig := DefaultRuntimeSwitchingConfig()
	switcher := NewRuntimeQuantizationSwitcher(
		runtimeConfig,
		NewQuantizationEngine(QuantizationConfig{}),
		NewMixedPrecisionEngine(DefaultMixedPrecisionConfig(), nil),
		nil,
		nil,
	)

	// Add a layer
	layer := NewLayer("bench_layer", "conv", LayerTypeConvolution)
	switcher.AddLayer(layer)

	precisions := []PrecisionMode{PrecisionFP32, PrecisionFP16, PrecisionINT8, PrecisionINT4}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		targetPrecision := precisions[i%len(precisions)]
		err := switcher.RequestPrecisionSwitch("bench_layer", targetPrecision, 5, "benchmark")
		if err != nil {
			b.Errorf("Failed to request precision switch: %v", err)
		}
	}
}
