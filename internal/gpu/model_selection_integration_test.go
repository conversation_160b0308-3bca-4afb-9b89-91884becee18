package gpu

import (
	"log"
	"os"
	"testing"
	"time"
)

func TestModelSelectionIntegration(t *testing.T) {
	// Create predictor
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    10,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)

	// Create logger
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	// Create integration
	integration := NewModelSelectionIntegration(predictor, logger)

	// Validate integration
	if integration == nil {
		t.Fatal("Expected integration instance, got nil")
	}

	if integration.predictor != predictor {
		t.Error("Expected predictor to be set")
	}

	if integration.optimizer == nil {
		t.Error("Expected optimizer to be initialized")
	}

	if integration.featureEngineer == nil {
		t.Error("Expected feature engineer to be initialized")
	}

	// Test configuration
	status := integration.GetOptimizationStatus()
	if status.OptimizationInterval != time.Hour*24 {
		t.<PERSON>("Expected optimization interval of 24h, got %v", status.OptimizationInterval)
	}

	if !status.IsOptimizationDue {
		t.Error("Expected optimization to be due initially")
	}
}

func TestOptimizationStatus(t *testing.T) {
	// Create predictor and integration
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    10,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	integration := NewModelSelectionIntegration(predictor, logger)

	// Test initial status
	status := integration.GetOptimizationStatus()
	if !status.IsOptimizationDue {
		t.Error("Expected optimization to be due initially")
	}

	// Simulate optimization run
	integration.lastOptimization = time.Now()

	// Test status after optimization
	status = integration.GetOptimizationStatus()
	if status.IsOptimizationDue {
		t.Error("Expected optimization to not be due after recent run")
	}

	expectedNext := integration.lastOptimization.Add(integration.optimizationInterval)
	if !status.NextOptimization.Equal(expectedNext) {
		t.Errorf("Expected next optimization at %v, got %v", expectedNext, status.NextOptimization)
	}
}

func TestModelRecommendations(t *testing.T) {
	// Create integration
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    10,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	integration := NewModelSelectionIntegration(predictor, logger)

	// Create mock optimization result
	candidates := []*ModelCandidate{
		{
			ID:              "model_1",
			ModelType:       "linear_regression",
			MeanScore:       0.85,
			Hyperparameters: map[string]interface{}{"param1": "value1"},
		},
		{
			ID:              "model_2",
			ModelType:       "lstm",
			MeanScore:       0.88,
			Hyperparameters: map[string]interface{}{"param2": "value2"},
		},
		{
			ID:              "model_3",
			ModelType:       "ensemble",
			MeanScore:       0.91,
			IsEnsemble:      true,
			Hyperparameters: map[string]interface{}{"param3": "value3"},
		},
	}

	result := &OptimizationResult{
		BestModel:     candidates[2], // Highest score
		TopModels:     candidates,
		AllCandidates: candidates,
	}

	// Get recommendations
	recommendations := integration.GetModelRecommendations(result)

	// Validate recommendations
	if len(recommendations) == 0 {
		t.Fatal("Expected recommendations, got none")
	}

	// Check primary recommendation
	primary := recommendations[0]
	if primary.Type != "primary" {
		t.Errorf("Expected primary recommendation, got %s", primary.Type)
	}

	if primary.ModelType != "ensemble" {
		t.Errorf("Expected ensemble as primary, got %s", primary.ModelType)
	}

	if primary.Confidence != 0.91 {
		t.Errorf("Expected confidence 0.91, got %f", primary.Confidence)
	}

	if primary.Priority != 1 {
		t.Errorf("Expected priority 1, got %d", primary.Priority)
	}

	// Check for ensemble recommendation
	hasEnsemble := false
	for _, rec := range recommendations {
		if rec.Type == "ensemble" {
			hasEnsemble = true
			break
		}
	}
	if !hasEnsemble {
		t.Error("Expected ensemble recommendation")
	}

	t.Logf("Generated %d recommendations:", len(recommendations))
	for i, rec := range recommendations {
		t.Logf("  %d. %s: %s (%.2f%% confidence)",
			i+1, rec.Type, rec.ModelType, rec.Confidence*100)
	}
}

func TestModelEvaluation(t *testing.T) {
	// Create integration
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    5,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	integration := NewModelSelectionIntegration(predictor, logger)

	// Add some data to predictor
	testData := generateTestWorkloadDataIntegration(10)
	for _, point := range testData {
		predictor.AddDataPoint(point)
	}

	// Test evaluation with empty data
	evaluation := integration.EvaluateCurrentModel([]WorkloadDataPoint{})
	if evaluation.IsValid {
		t.Error("Expected evaluation to be invalid with empty data")
	}

	// Test evaluation with data
	evaluation = integration.EvaluateCurrentModel(testData)
	if !evaluation.IsValid {
		t.Errorf("Expected evaluation to be valid, got error: %s", evaluation.Error)
	}

	if evaluation.DataPoints != len(testData) {
		t.Errorf("Expected %d data points, got %d", len(testData), evaluation.DataPoints)
	}

	if evaluation.Accuracy < 0 || evaluation.Accuracy > 1 {
		t.Errorf("Accuracy out of range [0,1]: %f", evaluation.Accuracy)
	}

	if evaluation.RMSE < 0 {
		t.Errorf("RMSE should be non-negative: %f", evaluation.RMSE)
	}

	if evaluation.MAE < 0 {
		t.Errorf("MAE should be non-negative: %f", evaluation.MAE)
	}

	t.Logf("Model evaluation results:")
	t.Logf("  Accuracy: %.4f", evaluation.Accuracy)
	t.Logf("  RMSE: %.4f", evaluation.RMSE)
	t.Logf("  MAE: %.4f", evaluation.MAE)
	t.Logf("  Data points: %d", evaluation.DataPoints)
}

func TestAutoOptimizationManager(t *testing.T) {
	// Create integration
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    5,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	integration := NewModelSelectionIntegration(predictor, logger)

	// Create auto optimization manager
	manager := NewAutoOptimizationManager(integration, logger)

	// Test initial state
	if manager.IsRunning() {
		t.Error("Expected manager to not be running initially")
	}

	// Test start
	manager.Start()
	if !manager.IsRunning() {
		t.Error("Expected manager to be running after start")
	}

	// Test stop
	manager.Stop()
	if manager.IsRunning() {
		t.Error("Expected manager to not be running after stop")
	}

	// Test multiple starts/stops
	manager.Start()
	manager.Start() // Should be safe to call multiple times
	if !manager.IsRunning() {
		t.Error("Expected manager to be running after multiple starts")
	}

	manager.Stop()
	manager.Stop() // Should be safe to call multiple times
	if manager.IsRunning() {
		t.Error("Expected manager to not be running after multiple stops")
	}
}

func TestConfigurationUpdate(t *testing.T) {
	// Create integration
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    5,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	integration := NewModelSelectionIntegration(predictor, logger)

	// Get initial config
	initialConfig := integration.config
	if initialConfig.CVFolds != 3 {
		t.Errorf("Expected initial CVFolds=3, got %d", initialConfig.CVFolds)
	}

	// Update configuration
	newConfig := DefaultModelSelectionConfig()
	newConfig.CVFolds = 5
	newConfig.MaxIterations = 50
	newConfig.Timeout = time.Minute * 30

	integration.UpdateConfig(newConfig)

	// Verify configuration was updated
	if integration.config.CVFolds != 5 {
		t.Errorf("Expected updated CVFolds=5, got %d", integration.config.CVFolds)
	}

	if integration.config.MaxIterations != 50 {
		t.Errorf("Expected updated MaxIterations=50, got %d", integration.config.MaxIterations)
	}

	if integration.config.Timeout != time.Minute*30 {
		t.Errorf("Expected updated Timeout=30m, got %v", integration.config.Timeout)
	}

	// Verify optimizer was recreated
	if integration.optimizer == nil {
		t.Error("Expected optimizer to be recreated after config update")
	}
}

func TestPeriodicOptimization(t *testing.T) {
	// Create integration
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    5,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	integration := NewModelSelectionIntegration(predictor, logger)

	// Test when optimization is not due
	integration.lastOptimization = time.Now()
	_, err := integration.PerformPeriodicOptimization()
	if err == nil {
		t.Error("Expected error when optimization is not due")
	}

	// Test when optimization is due but no data
	integration.lastOptimization = time.Now().Add(-time.Hour * 25) // Make it due
	_, err = integration.PerformPeriodicOptimization()
	if err == nil {
		t.Error("Expected error when no historical data available")
	}

	// Add some data
	testData := generateTestWorkloadDataIntegration(50)
	for _, point := range testData {
		predictor.AddDataPoint(point)
	}

	// Test successful optimization
	result, err := integration.PerformPeriodicOptimization()
	if err != nil {
		t.Errorf("Expected successful optimization, got error: %v", err)
	}

	if result == nil {
		t.Error("Expected optimization result, got nil")
	}

	// Verify optimization was recorded
	if time.Since(integration.lastOptimization) > time.Second {
		t.Error("Expected lastOptimization to be updated")
	}
}

func TestMetricCalculations(t *testing.T) {
	// Create integration
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    5,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	integration := NewModelSelectionIntegration(predictor, logger)

	// Test data
	predictions := []float64{10, 20, 30, 40, 50}
	actuals := []float64{12, 18, 32, 38, 52}

	// Test accuracy calculation
	accuracy := integration.calculateAccuracy(predictions, actuals)
	if accuracy < 0 || accuracy > 1 {
		t.Errorf("Accuracy out of range [0,1]: %f", accuracy)
	}

	// Test RMSE calculation
	rmse := integration.calculateRMSE(predictions, actuals)
	if rmse < 0 {
		t.Errorf("RMSE should be non-negative: %f", rmse)
	}

	// Test MAE calculation
	mae := integration.calculateMAE(predictions, actuals)
	if mae < 0 {
		t.Errorf("MAE should be non-negative: %f", mae)
	}

	// Test with empty data
	emptyAccuracy := integration.calculateAccuracy([]float64{}, []float64{})
	if emptyAccuracy != 0.0 {
		t.Errorf("Expected accuracy=0 for empty data, got %f", emptyAccuracy)
	}

	emptyRMSE := integration.calculateRMSE([]float64{}, []float64{})
	if emptyRMSE != 0.0 {
		t.Errorf("Expected RMSE=0 for empty data, got %f", emptyRMSE)
	}

	emptyMAE := integration.calculateMAE([]float64{}, []float64{})
	if emptyMAE != 0.0 {
		t.Errorf("Expected MAE=0 for empty data, got %f", emptyMAE)
	}

	t.Logf("Metric calculations:")
	t.Logf("  Accuracy: %.4f", accuracy)
	t.Logf("  RMSE: %.4f", rmse)
	t.Logf("  MAE: %.4f", mae)
}

// Helper function to generate test workload data for integration tests
func generateTestWorkloadDataIntegration(count int) []WorkloadDataPoint {
	var data []WorkloadDataPoint
	baseTime := time.Now().Add(-time.Hour * 24)

	for i := 0; i < count; i++ {
		timeOffset := time.Duration(i) * time.Minute * 30
		timestamp := baseTime.Add(timeOffset)

		// Create realistic patterns
		utilization := 0.5 + 0.3*float64(i%10)/10.0 // 0.5-0.8 range
		queueLength := int(utilization * 20)        // Scale with utilization
		activeTasks := int(utilization * 15)        // Scale with utilization
		nodesActive := int(utilization*8) + 2       // 2-10 nodes

		point := WorkloadDataPoint{
			Timestamp:      timestamp,
			QueueLength:    queueLength,
			ActiveTasks:    activeTasks,
			AvgUtilization: utilization,
			NodesActive:    nodesActive,
		}

		data = append(data, point)
	}

	return data
}

// Benchmark tests
func BenchmarkModelSelectionIntegration(b *testing.B) {
	// Create integration
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    5,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	logger := log.New(os.Stdout, "BENCH: ", log.LstdFlags)
	integration := NewModelSelectionIntegration(predictor, logger)

	// Generate test data
	testData := generateTestWorkloadDataIntegration(30)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := integration.OptimizeModelSelection(testData)
		if err != nil {
			b.Fatalf("Optimization failed: %v", err)
		}
	}
}

func BenchmarkModelEvaluation(b *testing.B) {
	// Create integration
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    5,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	logger := log.New(os.Stdout, "BENCH: ", log.LstdFlags)
	integration := NewModelSelectionIntegration(predictor, logger)

	// Add data to predictor
	testData := generateTestWorkloadDataIntegration(20)
	for _, point := range testData {
		predictor.AddDataPoint(point)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		evaluation := integration.EvaluateCurrentModel(testData)
		if !evaluation.IsValid {
			b.Fatalf("Evaluation failed: %s", evaluation.Error)
		}
	}
}
