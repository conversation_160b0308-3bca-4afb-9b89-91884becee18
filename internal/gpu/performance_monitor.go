package gpu

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// MultiDeviceManagerInterface defines the interface needed by performance monitor
type MultiDeviceManagerInterface interface {
	GetActiveDevices() []*ManagedDevice
	IsInitialized() bool
	GetConfiguration() MultiDeviceConfig
}

// GPUPerformanceMonitor coordinates performance monitoring across multiple GPU devices
type GPUPerformanceMonitor struct {
	multiDeviceMgr MultiDeviceManagerInterface
	config         PerformanceMonitorConfig
	logger         *log.Logger

	// Monitoring state
	ctx     context.Context
	cancel  context.CancelFunc
	running bool
	mu      sync.RWMutex
	wg      sync.WaitGroup

	// Performance data
	currentSnapshot *SystemPerformanceSnapshot
	performanceHist []SystemPerformanceSnapshot
	maxHistorySize  int
	snapshotsMu     sync.RWMutex

	// Metrics tracking
	startTime        time.Time
	totalSnapshots   int64
	lastSnapshotTime time.Time

	// Alerting
	alertThresholds *AlertThresholds
	alertCallback   func(AlertLevel, string, interface{})
}

// SystemPerformanceSnapshot captures complete system state at a point in time
type SystemPerformanceSnapshot struct {
	Timestamp         time.Time                         `json:"timestamp"`
	SystemHealth      SystemHealthMetrics               `json:"system_health"`
	DevicePerformance map[string]*DevicePerformance     `json:"device_performance"`
	LoadBalancing     LoadBalancingMetrics              `json:"load_balancing"`
	SystemEfficiency  SystemEfficiencyMetrics           `json:"system_efficiency"`
	TransferMetrics   MultiDeviceTransferMetrics        `json:"transfer_metrics"`
	SyncMetrics       SynchronizationPerformanceMetrics `json:"sync_metrics"`
}

// DevicePerformance contains detailed performance metrics for a single device
type DevicePerformance struct {
	DeviceID           string                   `json:"device_id"`
	BasicMetrics       *DeviceMetrics           `json:"basic_metrics"`
	UtilizationMetrics UtilizationMetrics       `json:"utilization_metrics"`
	MemoryMetrics      MemoryPerformanceMetrics `json:"memory_metrics"`
	ThermalMetrics     ThermalMetrics           `json:"thermal_metrics"`
	TaskMetrics        TaskPerformanceMetrics   `json:"task_metrics"`
	EfficiencyScore    float64                  `json:"efficiency_score"`
}

// SystemHealthMetrics tracks overall system health and status
type SystemHealthMetrics struct {
	OverallHealth   HealthStatus  `json:"overall_health"`
	ActiveDevices   int           `json:"active_devices"`
	FailedDevices   int           `json:"failed_devices"`
	TotalErrors     int64         `json:"total_errors"`
	AvgTemperature  float64       `json:"avg_temperature"`
	MaxTemperature  float64       `json:"max_temperature"`
	TotalPowerUsage float64       `json:"total_power_usage"`
	SystemUptime    time.Duration `json:"system_uptime"`
	CriticalAlerts  int           `json:"critical_alerts"`
	WarningAlerts   int           `json:"warning_alerts"`
}

// UtilizationMetrics tracks device utilization patterns
type UtilizationMetrics struct {
	GPUUtilization    float64        `json:"gpu_utilization"`
	MemoryUtilization float64        `json:"memory_utilization"`
	UtilizationTrend  TrendDirection `json:"utilization_trend"`
	PeakUtilization   float64        `json:"peak_utilization"`
	AvgUtilization    float64        `json:"avg_utilization"`
	IdleTime          time.Duration  `json:"idle_time"`
	ActiveTime        time.Duration  `json:"active_time"`
}

// MemoryPerformanceMetrics tracks memory usage and efficiency
type MemoryPerformanceMetrics struct {
	TotalMemory      int64   `json:"total_memory"`
	UsedMemory       int64   `json:"used_memory"`
	FreeMemory       int64   `json:"free_memory"`
	MemoryBandwidth  float64 `json:"memory_bandwidth_gbps"`
	AllocationRate   float64 `json:"allocation_rate_mb_per_sec"`
	DeallocationRate float64 `json:"deallocation_rate_mb_per_sec"`
	FragmentationPct float64 `json:"fragmentation_percent"`
	CacheHitRatio    float64 `json:"cache_hit_ratio"`
}

// ThermalMetrics tracks thermal performance and status
type ThermalMetrics struct {
	CurrentTemp   int            `json:"current_temp_celsius"`
	MaxTemp       int            `json:"max_temp_celsius"`
	ThermalStatus ThermalStatus  `json:"thermal_status"`
	FanSpeed      int            `json:"fan_speed_rpm"`
	ThermalTrend  TrendDirection `json:"thermal_trend"`
	TimeAtMaxTemp time.Duration  `json:"time_at_max_temp"`
}

// TaskPerformanceMetrics tracks task execution performance
type TaskPerformanceMetrics struct {
	TasksCompleted  int64         `json:"tasks_completed"`
	TasksPerSecond  float64       `json:"tasks_per_second"`
	AvgTaskTime     time.Duration `json:"avg_task_time"`
	MinTaskTime     time.Duration `json:"min_task_time"`
	MaxTaskTime     time.Duration `json:"max_task_time"`
	TaskSuccessRate float64       `json:"task_success_rate"`
	QueuedTasks     int           `json:"queued_tasks"`
	FailedTasks     int64         `json:"failed_tasks"`
}

// LoadBalancingMetrics analyzes load distribution across devices
type LoadBalancingMetrics struct {
	Strategy               LoadBalancingStrategy `json:"strategy"`
	LoadDistribution       map[string]float64    `json:"load_distribution"`
	LoadImbalance          float64               `json:"load_imbalance"`
	DistributionEfficiency float64               `json:"distribution_efficiency"`
	RebalanceEvents        int64                 `json:"rebalance_events"`
	LastRebalanceTime      time.Time             `json:"last_rebalance_time"`
}

// SystemEfficiencyMetrics calculates overall system efficiency
type SystemEfficiencyMetrics struct {
	OverallEfficiency float64 `json:"overall_efficiency"`
	ComputeEfficiency float64 `json:"compute_efficiency"`
	MemoryEfficiency  float64 `json:"memory_efficiency"`
	ThermalEfficiency float64 `json:"thermal_efficiency"`
	PowerEfficiency   float64 `json:"power_efficiency"`
	ThroughputMBps    float64 `json:"throughput_mbps"`
	UtilizationScore  float64 `json:"utilization_score"`
}

// MultiDeviceTransferMetrics tracks data transfer performance
type MultiDeviceTransferMetrics struct {
	HostToDeviceRate     float64       `json:"host_to_device_rate_mbps"`
	DeviceToHostRate     float64       `json:"device_to_host_rate_mbps"`
	DeviceToDeviceRate   float64       `json:"device_to_device_rate_mbps"`
	TotalTransfers       int64         `json:"total_transfers"`
	FailedTransfers      int64         `json:"failed_transfers"`
	AvgTransferLatency   time.Duration `json:"avg_transfer_latency"`
	BandwidthUtilization float64       `json:"bandwidth_utilization"`
}

// SynchronizationPerformanceMetrics tracks coordination overhead
type SynchronizationPerformanceMetrics struct {
	BarrierOperations   int64         `json:"barrier_operations"`
	AvgBarrierTime      time.Duration `json:"avg_barrier_time"`
	SyncOverheadPercent float64       `json:"sync_overhead_percent"`
	DeadlockEvents      int64         `json:"deadlock_events"`
	TimeoutEvents       int64         `json:"timeout_events"`
	CoordinationLatency time.Duration `json:"coordination_latency"`
}

// Enums and status types
type HealthStatus string

const (
	HealthStatusHealthy  HealthStatus = "healthy"
	HealthStatusDegraded HealthStatus = "degraded"
	HealthStatusCritical HealthStatus = "critical"
	HealthStatusFailed   HealthStatus = "failed"
)

type ThermalStatus string

const (
	ThermalStatusNormal     ThermalStatus = "normal"
	ThermalStatusWarning    ThermalStatus = "warning"
	ThermalStatusCritical   ThermalStatus = "critical"
	ThermalStatusThrottling ThermalStatus = "throttling"
)

type TrendDirection string

const (
	TrendDirectionStable     TrendDirection = "stable"
	TrendDirectionIncreasing TrendDirection = "increasing"
	TrendDirectionDecreasing TrendDirection = "decreasing"
)

type AlertLevel string

const (
	AlertLevelInfo     AlertLevel = "info"
	AlertLevelWarning  AlertLevel = "warning"
	AlertLevelCritical AlertLevel = "critical"
)

// Configuration structures
type PerformanceMonitorConfig struct {
	Enabled            bool          `json:"enabled"`
	MonitoringInterval time.Duration `json:"monitoring_interval"`
	HistorySize        int           `json:"history_size"`
	AlertingEnabled    bool          `json:"alerting_enabled"`
	DetailedMetrics    bool          `json:"detailed_metrics"`
	ThermalMonitoring  bool          `json:"thermal_monitoring"`
	TransferMonitoring bool          `json:"transfer_monitoring"`
	SyncMonitoring     bool          `json:"sync_monitoring"`
	LogLevel           string        `json:"log_level"`
}

type AlertThresholds struct {
	CriticalTemperature    int     `json:"critical_temperature"`
	WarningTemperature     int     `json:"warning_temperature"`
	CriticalMemoryUsage    float64 `json:"critical_memory_usage"`
	WarningMemoryUsage     float64 `json:"warning_memory_usage"`
	CriticalGPUUtilization float64 `json:"critical_gpu_utilization"`
	WarningGPUUtilization  float64 `json:"warning_gpu_utilization"`
	MaxPowerUsage          float64 `json:"max_power_usage"`
	MinEfficiencyScore     float64 `json:"min_efficiency_score"`
}

// DefaultPerformanceMonitorConfig returns default configuration
func DefaultPerformanceMonitorConfig() PerformanceMonitorConfig {
	return PerformanceMonitorConfig{
		Enabled:            true,
		MonitoringInterval: time.Second * 2,
		HistorySize:        100,
		AlertingEnabled:    true,
		DetailedMetrics:    true,
		ThermalMonitoring:  true,
		TransferMonitoring: true,
		SyncMonitoring:     true,
		LogLevel:           "info",
	}
}

// DefaultAlertThresholds returns default alert thresholds
func DefaultAlertThresholds() AlertThresholds {
	return AlertThresholds{
		CriticalTemperature:    85,
		WarningTemperature:     75,
		CriticalMemoryUsage:    95.0,
		WarningMemoryUsage:     85.0,
		CriticalGPUUtilization: 98.0,
		WarningGPUUtilization:  90.0,
		MaxPowerUsage:          300.0,
		MinEfficiencyScore:     0.7,
	}
}

// NewGPUPerformanceMonitor creates a new performance monitoring system
func NewGPUPerformanceMonitor(multiDeviceMgr MultiDeviceManagerInterface, config PerformanceMonitorConfig, logger *log.Logger) *GPUPerformanceMonitor {
	if logger == nil {
		logger = log.Default()
	}

	if config.HistorySize == 0 {
		config.HistorySize = 100
	}

	if config.MonitoringInterval == 0 {
		config.MonitoringInterval = time.Second * 2
	}

	monitor := &GPUPerformanceMonitor{
		multiDeviceMgr:  multiDeviceMgr,
		config:          config,
		logger:          logger,
		maxHistorySize:  config.HistorySize,
		performanceHist: make([]SystemPerformanceSnapshot, 0, config.HistorySize),
		alertThresholds: &AlertThresholds{},
	}

	// Set default thresholds
	*monitor.alertThresholds = DefaultAlertThresholds()

	return monitor
}

// Start begins performance monitoring
func (pm *GPUPerformanceMonitor) Start(ctx context.Context) error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if pm.running {
		return fmt.Errorf("performance monitor is already running")
	}

	if !pm.config.Enabled {
		return fmt.Errorf("performance monitoring is disabled")
	}

	if pm.multiDeviceMgr == nil || !pm.multiDeviceMgr.IsInitialized() {
		return fmt.Errorf("multi-device manager is not initialized")
	}

	pm.ctx, pm.cancel = context.WithCancel(ctx)
	pm.running = true
	pm.startTime = time.Now()

	pm.wg.Add(1)
	go pm.monitoringLoop()

	pm.logger.Printf("GPU performance monitoring started with interval: %v", pm.config.MonitoringInterval)
	return nil
}

// Stop stops performance monitoring
func (pm *GPUPerformanceMonitor) Stop() error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if !pm.running {
		return nil
	}

	pm.cancel()
	pm.running = false

	pm.wg.Wait()

	pm.logger.Printf("GPU performance monitoring stopped. Total snapshots: %d", pm.totalSnapshots)
	return nil
}

// GetCurrentSnapshot returns the latest performance snapshot
func (pm *GPUPerformanceMonitor) GetCurrentSnapshot() (*SystemPerformanceSnapshot, error) {
	pm.snapshotsMu.RLock()
	defer pm.snapshotsMu.RUnlock()

	if pm.currentSnapshot == nil {
		return nil, fmt.Errorf("no performance data available")
	}

	// Return a copy
	snapshot := *pm.currentSnapshot
	return &snapshot, nil
}

// GetPerformanceHistory returns historical performance data
func (pm *GPUPerformanceMonitor) GetPerformanceHistory() []SystemPerformanceSnapshot {
	pm.snapshotsMu.RLock()
	defer pm.snapshotsMu.RUnlock()

	history := make([]SystemPerformanceSnapshot, len(pm.performanceHist))
	copy(history, pm.performanceHist)
	return history
}

// SetAlertThresholds updates alerting thresholds
func (pm *GPUPerformanceMonitor) SetAlertThresholds(thresholds AlertThresholds) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.alertThresholds = &thresholds
}

// SetAlertCallback sets the callback function for alerts
func (pm *GPUPerformanceMonitor) SetAlertCallback(callback func(AlertLevel, string, interface{})) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.alertCallback = callback
}

// IsRunning returns whether monitoring is active
func (pm *GPUPerformanceMonitor) IsRunning() bool {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	return pm.running
}

// GetMonitoringStats returns monitoring statistics
func (pm *GPUPerformanceMonitor) GetMonitoringStats() PerformanceMonitoringStats {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	return PerformanceMonitoringStats{
		Running:            pm.running,
		TotalSnapshots:     pm.totalSnapshots,
		HistorySize:        len(pm.performanceHist),
		LastSnapshotTime:   pm.lastSnapshotTime,
		MonitoringUptime:   time.Since(pm.startTime),
		MonitoringInterval: pm.config.MonitoringInterval,
	}
}

type PerformanceMonitoringStats struct {
	Running            bool          `json:"running"`
	TotalSnapshots     int64         `json:"total_snapshots"`
	HistorySize        int           `json:"history_size"`
	LastSnapshotTime   time.Time     `json:"last_snapshot_time"`
	MonitoringUptime   time.Duration `json:"monitoring_uptime"`
	MonitoringInterval time.Duration `json:"monitoring_interval"`
}

// monitoringLoop runs the continuous monitoring process
func (pm *GPUPerformanceMonitor) monitoringLoop() {
	defer pm.wg.Done()

	ticker := time.NewTicker(pm.config.MonitoringInterval)
	defer ticker.Stop()

	for {
		select {
		case <-pm.ctx.Done():
			return
		case <-ticker.C:
			if err := pm.capturePerformanceSnapshot(); err != nil {
				pm.logger.Printf("Error capturing performance snapshot: %v", err)
			}
		}
	}
}

// capturePerformanceSnapshot collects current system performance data
func (pm *GPUPerformanceMonitor) capturePerformanceSnapshot() error {
	snapshot := &SystemPerformanceSnapshot{
		Timestamp:         time.Now(),
		DevicePerformance: make(map[string]*DevicePerformance),
	}

	// Get active devices
	devices := pm.multiDeviceMgr.GetActiveDevices()
	if len(devices) == 0 {
		return fmt.Errorf("no active devices available")
	}

	// Collect device-specific metrics
	var totalTemp, totalPower float64
	var totalErrors int64
	activeDevices, failedDevices := 0, 0

	for _, device := range devices {
		devicePerf, err := pm.collectDevicePerformance(device)
		if err != nil {
			pm.logger.Printf("Error collecting performance for device %s: %v", device.Device.ID, err)
			failedDevices++
			continue
		}

		snapshot.DevicePerformance[device.Device.ID] = devicePerf
		activeDevices++

		// Aggregate for system health
		totalTemp += float64(devicePerf.ThermalMetrics.CurrentTemp)
		totalPower += devicePerf.BasicMetrics.PowerConsumption
		totalErrors += devicePerf.BasicMetrics.ErrorCount
	}

	// Calculate system health metrics
	snapshot.SystemHealth = pm.calculateSystemHealth(devices, totalTemp, totalPower, totalErrors, activeDevices, failedDevices)

	// Calculate load balancing metrics
	snapshot.LoadBalancing = pm.calculateLoadBalancingMetrics(devices)

	// Calculate system efficiency
	snapshot.SystemEfficiency = pm.calculateSystemEfficiency(snapshot.DevicePerformance)

	// Collect transfer metrics if enabled
	if pm.config.TransferMonitoring {
		snapshot.TransferMetrics = pm.collectTransferMetrics()
	}

	// Collect synchronization metrics if enabled
	if pm.config.SyncMonitoring {
		snapshot.SyncMetrics = pm.collectSyncMetrics()
	}

	// Store snapshot
	pm.storeSnapshot(snapshot)

	// Check alerts if enabled
	if pm.config.AlertingEnabled {
		pm.checkAlerts(snapshot)
	}

	pm.totalSnapshots++
	pm.lastSnapshotTime = time.Now()

	return nil
}

// collectDevicePerformance gathers performance metrics for a single device
func (pm *GPUPerformanceMonitor) collectDevicePerformance(device *ManagedDevice) (*DevicePerformance, error) {
	device.mu.RLock()
	defer device.mu.RUnlock()

	perf := &DevicePerformance{
		DeviceID:     device.Device.ID,
		BasicMetrics: device.Metrics,
	}

	// Calculate utilization metrics
	perf.UtilizationMetrics = UtilizationMetrics{
		GPUUtilization:    device.Metrics.ComputeUtilization,
		MemoryUtilization: device.Metrics.MemoryUtilization,
		UtilizationTrend:  TrendDirectionStable,              // Would need historical data for trend
		PeakUtilization:   device.Metrics.ComputeUtilization, // Simplified
		AvgUtilization:    device.Metrics.ComputeUtilization,
		ActiveTime:        time.Since(device.LastUsed),
	}

	// Get memory stats if available
	if device.MemoryMgr != nil {
		// Note: This is a simplified approach since we don't have direct access to memory stats
		// In a real implementation, we'd need to extend the interface to get detailed memory metrics
		perf.MemoryMetrics = MemoryPerformanceMetrics{
			// These would be populated from actual memory manager stats
			FragmentationPct: 0.0,  // Placeholder
			CacheHitRatio:    0.95, // Placeholder
		}
	}

	// Calculate thermal metrics
	perf.ThermalMetrics = ThermalMetrics{
		CurrentTemp:   device.Metrics.Temperature,
		MaxTemp:       device.Metrics.Temperature, // Simplified
		ThermalStatus: pm.getThermalStatus(device.Metrics.Temperature),
		ThermalTrend:  TrendDirectionStable,
	}

	// Calculate task metrics
	taskTime := device.Metrics.AverageTaskTime
	perf.TaskMetrics = TaskPerformanceMetrics{
		TasksCompleted:  device.Metrics.TasksCompleted,
		AvgTaskTime:     taskTime,
		MinTaskTime:     taskTime, // Simplified
		MaxTaskTime:     taskTime, // Simplified
		TaskSuccessRate: pm.calculateTaskSuccessRate(device.Metrics.TasksCompleted, device.Metrics.ErrorCount),
		FailedTasks:     device.Metrics.ErrorCount,
	}

	// Calculate efficiency score
	perf.EfficiencyScore = pm.calculateDeviceEfficiency(perf)

	return perf, nil
}

// calculateSystemHealth determines overall system health status
func (pm *GPUPerformanceMonitor) calculateSystemHealth(devices []*ManagedDevice, totalTemp, totalPower float64, totalErrors int64, activeDevices, failedDevices int) SystemHealthMetrics {
	health := SystemHealthMetrics{
		ActiveDevices:   activeDevices,
		FailedDevices:   failedDevices,
		TotalErrors:     totalErrors,
		TotalPowerUsage: totalPower,
		SystemUptime:    time.Since(pm.startTime),
	}

	if activeDevices > 0 {
		health.AvgTemperature = totalTemp / float64(activeDevices)
		health.MaxTemperature = totalTemp // Simplified - would need per-device max
	}

	// Determine overall health status
	if failedDevices > 0 || totalErrors > 100 {
		health.OverallHealth = HealthStatusCritical
		health.CriticalAlerts = 1
	} else if health.AvgTemperature > float64(pm.alertThresholds.WarningTemperature) {
		health.OverallHealth = HealthStatusDegraded
		health.WarningAlerts = 1
	} else {
		health.OverallHealth = HealthStatusHealthy
	}

	return health
}

// calculateLoadBalancingMetrics analyzes load distribution
func (pm *GPUPerformanceMonitor) calculateLoadBalancingMetrics(devices []*ManagedDevice) LoadBalancingMetrics {
	config := pm.multiDeviceMgr.GetConfiguration()

	metrics := LoadBalancingMetrics{
		Strategy:         config.Strategy,
		LoadDistribution: make(map[string]float64),
	}

	if len(devices) == 0 {
		return metrics
	}

	// Calculate load distribution
	totalLoad := 0.0
	for _, device := range devices {
		load := device.LoadLevel
		metrics.LoadDistribution[device.Device.ID] = load
		totalLoad += load
	}

	// Calculate load imbalance (standard deviation of loads)
	if len(devices) > 1 {
		avgLoad := totalLoad / float64(len(devices))
		variance := 0.0
		for _, load := range metrics.LoadDistribution {
			variance += (load - avgLoad) * (load - avgLoad)
		}
		variance /= float64(len(devices))
		imbalance := variance / (avgLoad * avgLoad) // Coefficient of variation
		metrics.LoadImbalance = imbalance
		metrics.DistributionEfficiency = 1.0 - imbalance // Simplified efficiency metric
	}

	return metrics
}

// calculateSystemEfficiency computes overall system efficiency metrics
func (pm *GPUPerformanceMonitor) calculateSystemEfficiency(devicePerformance map[string]*DevicePerformance) SystemEfficiencyMetrics {
	if len(devicePerformance) == 0 {
		return SystemEfficiencyMetrics{}
	}

	var totalCompute, totalMemory, totalThermal, totalEfficiency float64
	var totalThroughput float64
	deviceCount := float64(len(devicePerformance))

	for _, perf := range devicePerformance {
		totalCompute += perf.UtilizationMetrics.GPUUtilization
		totalMemory += perf.UtilizationMetrics.MemoryUtilization
		totalThermal += pm.calculateThermalEfficiency(perf.ThermalMetrics)
		totalEfficiency += perf.EfficiencyScore
		totalThroughput += perf.BasicMetrics.ThroughputMBps
	}

	return SystemEfficiencyMetrics{
		OverallEfficiency: totalEfficiency / deviceCount,
		ComputeEfficiency: totalCompute / deviceCount,
		MemoryEfficiency:  totalMemory / deviceCount,
		ThermalEfficiency: totalThermal / deviceCount,
		PowerEfficiency:   pm.calculatePowerEfficiency(devicePerformance),
		ThroughputMBps:    totalThroughput,
		UtilizationScore:  (totalCompute + totalMemory) / (2.0 * deviceCount),
	}
}

// Helper methods for calculations
func (pm *GPUPerformanceMonitor) getThermalStatus(temp int) ThermalStatus {
	if temp >= pm.alertThresholds.CriticalTemperature {
		return ThermalStatusCritical
	} else if temp >= pm.alertThresholds.WarningTemperature {
		return ThermalStatusWarning
	}
	return ThermalStatusNormal
}

func (pm *GPUPerformanceMonitor) calculateTaskSuccessRate(completed, errors int64) float64 {
	total := completed + errors
	if total == 0 {
		return 1.0
	}
	return float64(completed) / float64(total)
}

func (pm *GPUPerformanceMonitor) calculateDeviceEfficiency(perf *DevicePerformance) float64 {
	// Weighted efficiency calculation
	computeWeight := 0.4
	memoryWeight := 0.3
	thermalWeight := 0.2
	taskWeight := 0.1

	computeEff := perf.UtilizationMetrics.GPUUtilization / 100.0
	memoryEff := perf.UtilizationMetrics.MemoryUtilization / 100.0
	thermalEff := pm.calculateThermalEfficiency(perf.ThermalMetrics)
	taskEff := perf.TaskMetrics.TaskSuccessRate

	return computeEff*computeWeight + memoryEff*memoryWeight + thermalEff*thermalWeight + taskEff*taskWeight
}

func (pm *GPUPerformanceMonitor) calculateThermalEfficiency(thermal ThermalMetrics) float64 {
	// Thermal efficiency decreases as temperature approaches critical threshold
	maxTemp := float64(pm.alertThresholds.CriticalTemperature)
	currentTemp := float64(thermal.CurrentTemp)

	if currentTemp >= maxTemp {
		return 0.0
	}

	return 1.0 - (currentTemp / maxTemp)
}

func (pm *GPUPerformanceMonitor) calculatePowerEfficiency(devicePerformance map[string]*DevicePerformance) float64 {
	// Calculate performance per watt
	var totalPerformance, totalPower float64

	for _, perf := range devicePerformance {
		performance := perf.UtilizationMetrics.GPUUtilization / 100.0
		power := perf.BasicMetrics.PowerConsumption

		totalPerformance += performance
		totalPower += power
	}

	if totalPower == 0 {
		return 0
	}

	return totalPerformance / totalPower
}

// collectTransferMetrics gathers data transfer performance metrics
func (pm *GPUPerformanceMonitor) collectTransferMetrics() MultiDeviceTransferMetrics {
	// This would integrate with the multi-GPU memory manager to get transfer stats
	// For now, returning placeholder values
	return MultiDeviceTransferMetrics{
		HostToDeviceRate:     1000.0, // Placeholder
		DeviceToHostRate:     1000.0, // Placeholder
		DeviceToDeviceRate:   2000.0, // Placeholder
		BandwidthUtilization: 0.75,   // Placeholder
	}
}

// collectSyncMetrics gathers synchronization performance metrics
func (pm *GPUPerformanceMonitor) collectSyncMetrics() SynchronizationPerformanceMetrics {
	// This would integrate with the synchronization manager to get sync stats
	// For now, returning placeholder values
	return SynchronizationPerformanceMetrics{
		BarrierOperations:   100,                    // Placeholder
		AvgBarrierTime:      time.Microsecond * 100, // Placeholder
		SyncOverheadPercent: 2.5,                    // Placeholder
		CoordinationLatency: time.Microsecond * 50,  // Placeholder
	}
}

// storeSnapshot adds a performance snapshot to history
func (pm *GPUPerformanceMonitor) storeSnapshot(snapshot *SystemPerformanceSnapshot) {
	pm.snapshotsMu.Lock()
	defer pm.snapshotsMu.Unlock()

	pm.currentSnapshot = snapshot

	// Add to history
	pm.performanceHist = append(pm.performanceHist, *snapshot)

	// Maintain history size limit
	if len(pm.performanceHist) > pm.maxHistorySize {
		pm.performanceHist = pm.performanceHist[1:]
	}
}

// checkAlerts evaluates performance against thresholds and triggers alerts
func (pm *GPUPerformanceMonitor) checkAlerts(snapshot *SystemPerformanceSnapshot) {
	if pm.alertCallback == nil {
		return
	}

	// Check system-level alerts
	if snapshot.SystemHealth.OverallHealth == HealthStatusCritical {
		pm.alertCallback(AlertLevelCritical, "System health critical", snapshot.SystemHealth)
	}

	// Check device-level alerts
	for deviceID, perf := range snapshot.DevicePerformance {
		// Temperature alerts
		if perf.ThermalMetrics.CurrentTemp >= pm.alertThresholds.CriticalTemperature {
			pm.alertCallback(AlertLevelCritical, fmt.Sprintf("Critical temperature on device %s", deviceID), perf.ThermalMetrics)
		} else if perf.ThermalMetrics.CurrentTemp >= pm.alertThresholds.WarningTemperature {
			pm.alertCallback(AlertLevelWarning, fmt.Sprintf("High temperature on device %s", deviceID), perf.ThermalMetrics)
		}

		// Memory alerts
		if perf.UtilizationMetrics.MemoryUtilization >= pm.alertThresholds.CriticalMemoryUsage {
			pm.alertCallback(AlertLevelCritical, fmt.Sprintf("Critical memory usage on device %s", deviceID), perf.UtilizationMetrics)
		} else if perf.UtilizationMetrics.MemoryUtilization >= pm.alertThresholds.WarningMemoryUsage {
			pm.alertCallback(AlertLevelWarning, fmt.Sprintf("High memory usage on device %s", deviceID), perf.UtilizationMetrics)
		}

		// Efficiency alerts
		if perf.EfficiencyScore < pm.alertThresholds.MinEfficiencyScore {
			pm.alertCallback(AlertLevelWarning, fmt.Sprintf("Low efficiency on device %s", deviceID), perf)
		}
	}
}
