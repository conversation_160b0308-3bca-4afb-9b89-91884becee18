package gpu

// SecurityConfig holds global security settings
type SecurityConfig struct {
	EnableMemorySecurity   bool
	EnableModelEncryption  bool
	EnableAccessControl    bool
	EnableIsolation        bool
	EnableSideChannelMitig bool
	// Add more as needed
}

// SecurityManager orchestrates all GPU security features
type SecurityManager struct {
	config           SecurityConfig
	memoryManager    SecureMemoryManager
	modelManager     ModelEncryptionManager
	accessManager    AccessControlManager
	isoManager       IsolationManager
	sideChannelGuard SideChannelMitigator
}

func NewSecurityManager(cfg SecurityConfig) *SecurityManager {
	return &SecurityManager{
		config:           cfg,
		memoryManager:    NewSecureMemoryManager(cfg),
		modelManager:     NewModelEncryptionManager(cfg),
		accessManager:    NewAccessControlManager(cfg),
		isoManager:       NewIsolationManager(cfg),
		sideChannelGuard: NewSideChannelMitigator(cfg),
	}
}

// TODO: Add integration with GPU backend selection and runtime hooks
