package gpu

import (
	"crypto/sha256"
	"encoding/binary"
	"fmt"
	"math"
	"sort"
	"sync"
	"time"
)

// CompressionManager defines the interface for checkpoint compression strategies
type CompressionManager interface {
	Compress(data []byte) ([]byte, error)
	Decompress(compressedData []byte) ([]byte, error)
	Name() string
	CompressionRatio() float64
	DecompressionSpeed() time.Duration // Average decompression time per MB
	SupportsGPU() bool
}

// CompressionConfig configures compression behavior
type CompressionConfig struct {
	PreferSpeed         bool          `json:"prefer_speed"`          // Prioritize decompression speed over ratio
	MaxCompressionTime  time.Duration `json:"max_compression_time"`  // Max time to spend compressing
	MinCompressionRatio float64       `json:"min_compression_ratio"` // Minimum acceptable compression ratio
	GPUAccelerated      bool          `json:"gpu_accelerated"`       // Use GPU for compression when available
	AdaptiveSelection   bool          `json:"adaptive_selection"`    // Automatically select best algorithm
	EnableProfiling     bool          `json:"enable_profiling"`      // Track performance metrics
}

// DataCharacteristics describes the properties of data being compressed
type DataCharacteristics struct {
	DataType        DataType `json:"data_type"`
	Size            int64    `json:"size"`
	Entropy         float64  `json:"entropy"`          // Measure of randomness
	Sparsity        float64  `json:"sparsity"`         // Percentage of zero values
	FloatPrecision  int      `json:"float_precision"`  // Bits of precision needed
	IsTimeSeries    bool     `json:"is_time_series"`   // Time-ordered data
	RepeatPatterns  int      `json:"repeat_patterns"`  // Number of repeating patterns
	CompressionHint string   `json:"compression_hint"` // Manual hint for algorithm selection
}

// DataType represents different types of checkpoint data
type DataType string

const (
	DataTypeTensorFloat32      DataType = "tensor_float32"
	DataTypeTensorFloat64      DataType = "tensor_float64"
	DataTypeTensorInt32        DataType = "tensor_int32"
	DataTypePerformanceMetrics DataType = "performance_metrics"
	DataTypeMemoryState        DataType = "memory_state"
	DataTypeStreamState        DataType = "stream_state"
	DataTypeGeneral            DataType = "general"
)

// CompressionResult contains compression operation results
type CompressionResult struct {
	Algorithm         string                 `json:"algorithm"`
	OriginalSize      int64                  `json:"original_size"`
	CompressedSize    int64                  `json:"compressed_size"`
	CompressionRatio  float64                `json:"compression_ratio"`
	CompressionTime   time.Duration          `json:"compression_time"`
	DecompressionTime time.Duration          `json:"decompression_time"`
	Checksum          string                 `json:"checksum"`
	Metadata          map[string]interface{} `json:"metadata"`
}

// CheckpointCompressor manages compression for GPU checkpoint data
type CheckpointCompressor struct {
	config      CompressionConfig
	compressors map[string]CompressionManager
	logger      Logger
	mu          sync.RWMutex

	// Performance tracking
	compressionStats map[string]*CompressionStats
	adaptiveSelector *AdaptiveSelector
}

// CompressionStats tracks performance metrics for each algorithm
type CompressionStats struct {
	TotalOperations     int64         `json:"total_operations"`
	TotalOriginalSize   int64         `json:"total_original_size"`
	TotalCompressedSize int64         `json:"total_compressed_size"`
	AverageRatio        float64       `json:"average_ratio"`
	AverageCompTime     time.Duration `json:"average_comp_time"`
	AverageDecompTime   time.Duration `json:"average_decomp_time"`
	SuccessRate         float64       `json:"success_rate"`
	LastUsed            time.Time     `json:"last_used"`
}

// AdaptiveSelector chooses the best compression algorithm based on data characteristics
type AdaptiveSelector struct {
	history map[string]*AlgorithmPerformance
	mu      sync.RWMutex
}

// AlgorithmPerformance tracks algorithm performance for different data types
type AlgorithmPerformance struct {
	DataType         DataType      `json:"data_type"`
	Algorithm        string        `json:"algorithm"`
	AverageRatio     float64       `json:"average_ratio"`
	AverageSpeed     time.Duration `json:"average_speed"`
	ReliabilityScore float64       `json:"reliability_score"`
	SampleCount      int           `json:"sample_count"`
}

// DefaultCompressionConfig returns a default configuration
func DefaultCompressionConfig() CompressionConfig {
	return CompressionConfig{
		PreferSpeed:         true,
		MaxCompressionTime:  5 * time.Second,
		MinCompressionRatio: 1.1,   // At least 10% compression
		GPUAccelerated:      false, // Disable by default for compatibility
		AdaptiveSelection:   true,
		EnableProfiling:     true,
	}
}

// NewCheckpointCompressor creates a new checkpoint compressor
func NewCheckpointCompressor(config CompressionConfig, logger Logger) *CheckpointCompressor {
	compressor := &CheckpointCompressor{
		config:           config,
		compressors:      make(map[string]CompressionManager),
		logger:           logger,
		compressionStats: make(map[string]*CompressionStats),
		adaptiveSelector: &AdaptiveSelector{
			history: make(map[string]*AlgorithmPerformance),
		},
	}

	// Register available compression algorithms
	compressor.registerCompressors()

	return compressor
}

// registerCompressors initializes available compression algorithms
func (cc *CheckpointCompressor) registerCompressors() {
	// Fast compression for real-time scenarios
	cc.compressors["lz4"] = &LZ4Compressor{}

	// Balanced compression for general use
	cc.compressors["zstd"] = &ZstdCompressor{Level: 3}
	cc.compressors["zstd_fast"] = &ZstdCompressor{Level: 1}
	cc.compressors["zstd_best"] = &ZstdCompressor{Level: 19}

	// Legacy support
	cc.compressors["gzip"] = &GzipCompressor{Level: 6}

	// Specialized compressors
	// TODO: Fix tensor float compression precision issues
	// cc.compressors["tensor_float"] = &TensorFloatCompressor{}
	// TODO: Fix delta compression reconstruction bug
	// cc.compressors["delta_varint"] = &DeltaVarintCompressor{}
	// TODO: Fix Gorilla time series compression delta reconstruction bug
	// cc.compressors["gorilla_ts"] = &GorillaTimeSeriesCompressor{}
	cc.compressors["sparse_tensor"] = &SparseTensorCompressor{}

	// Initialize stats for each compressor
	for name := range cc.compressors {
		cc.compressionStats[name] = &CompressionStats{
			LastUsed: time.Now(),
		}
	}
}

// CompressCheckpointData compresses checkpoint data using the optimal algorithm
func (cc *CheckpointCompressor) CompressCheckpointData(data []byte, dataType DataType) (*CompressionResult, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("cannot compress empty data")
	}

	// Analyze data characteristics
	characteristics := cc.analyzeData(data, dataType)

	// Select optimal compression algorithm
	algorithmName := cc.selectOptimalAlgorithm(characteristics)

	// Get the compressor
	compressor, exists := cc.compressors[algorithmName]
	if !exists {
		return nil, fmt.Errorf("compressor %s not available", algorithmName)
	}

	// Perform compression with timing
	startTime := time.Now()
	compressedData, err := compressor.Compress(data)
	compressionTime := time.Since(startTime)

	if err != nil {
		cc.updateStats(algorithmName, false, 0, 0, 0, 0)
		return nil, fmt.Errorf("compression failed with %s: %w", algorithmName, err)
	}

	// Verify compression ratio meets minimum requirement
	ratio := float64(len(data)) / float64(len(compressedData))
	var decompressionTime time.Duration

	if ratio < cc.config.MinCompressionRatio {
		cc.logger.Printf("Compression ratio %.2f below minimum %.2f, using uncompressed data",
			ratio, cc.config.MinCompressionRatio)
		algorithmName = "none"
		compressedData = data
		ratio = 1.0
		// Skip decompression verification for uncompressed data
		decompressionTime = 0
	} else {
		// Test decompression speed only for compressed data
		decompStartTime := time.Now()
		_, err = compressor.Decompress(compressedData)
		decompressionTime = time.Since(decompStartTime)

		if err != nil {
			cc.updateStats(algorithmName, false, 0, 0, 0, 0)
			return nil, fmt.Errorf("decompression verification failed: %w", err)
		}
	}

	// Calculate checksum
	checksum := fmt.Sprintf("%x", sha256.Sum256(data))

	// Update statistics
	cc.updateStats(algorithmName, true, int64(len(data)), int64(len(compressedData)),
		compressionTime, decompressionTime)

	// Update adaptive selector
	if cc.config.AdaptiveSelection {
		cc.adaptiveSelector.updatePerformance(dataType, algorithmName, ratio, decompressionTime)
	}

	result := &CompressionResult{
		Algorithm:         algorithmName,
		OriginalSize:      int64(len(data)),
		CompressedSize:    int64(len(compressedData)),
		CompressionRatio:  ratio,
		CompressionTime:   compressionTime,
		DecompressionTime: decompressionTime,
		Checksum:          checksum,
		Metadata: map[string]interface{}{
			"data_type":       string(dataType),
			"entropy":         characteristics.Entropy,
			"sparsity":        characteristics.Sparsity,
			"algorithm_stats": cc.compressionStats[algorithmName],
		},
	}

	cc.logger.Printf("Compressed %d bytes to %d bytes (%.2fx) using %s in %v",
		len(data), len(compressedData), ratio, algorithmName, compressionTime)

	return result, nil
}

// Compress compresses data using the optimal algorithm (implements Compressor interface)
func (cc *CheckpointCompressor) Compress(data []byte) ([]byte, error) {
	// Analyze data and select optimal algorithm
	characteristics := cc.analyzeData(data, DataTypeGeneral)
	algorithmName := cc.selectOptimalAlgorithm(characteristics)

	compressor, exists := cc.compressors[algorithmName]
	if !exists {
		return nil, fmt.Errorf("compressor %s not available", algorithmName)
	}

	return compressor.Compress(data)
}

// Decompress decompresses data using the best available algorithm (implements Compressor interface)
func (cc *CheckpointCompressor) Decompress(compressedData []byte) ([]byte, error) {
	// Try each available compressor until one succeeds
	var lastErr error
	for name, compressor := range cc.compressors {
		data, err := compressor.Decompress(compressedData)
		if err == nil {
			cc.logger.Printf("Successfully decompressed data using %s algorithm", name)
			return data, nil
		}
		lastErr = err
	}

	return nil, fmt.Errorf("failed to decompress data with any available algorithm: %w", lastErr)
}

// DecompressCheckpointData decompresses checkpoint data
func (cc *CheckpointCompressor) DecompressCheckpointData(compressedData []byte, algorithmName string) ([]byte, error) {
	if algorithmName == "none" {
		return compressedData, nil
	}

	compressor, exists := cc.compressors[algorithmName]
	if !exists {
		return nil, fmt.Errorf("compressor %s not available for decompression", algorithmName)
	}

	startTime := time.Now()
	data, err := compressor.Decompress(compressedData)
	decompTime := time.Since(startTime)

	if err != nil {
		return nil, fmt.Errorf("decompression failed with %s: %w", algorithmName, err)
	}

	cc.logger.Printf("Decompressed %d bytes to %d bytes using %s in %v",
		len(compressedData), len(data), algorithmName, decompTime)

	return data, nil
}

// analyzeData examines data characteristics to inform compression selection
func (cc *CheckpointCompressor) analyzeData(data []byte, dataType DataType) DataCharacteristics {
	characteristics := DataCharacteristics{
		DataType: dataType,
		Size:     int64(len(data)),
	}

	// Calculate entropy (measure of randomness)
	characteristics.Entropy = cc.calculateEntropy(data)

	// Calculate sparsity for numeric data
	if dataType == DataTypeTensorFloat32 || dataType == DataTypeTensorFloat64 {
		characteristics.Sparsity = cc.calculateSparsity(data, dataType)
	}

	// Detect time series patterns
	characteristics.IsTimeSeries = cc.isTimeSeries(data, dataType)

	// Count repeating patterns
	characteristics.RepeatPatterns = cc.countRepeatingPatterns(data)

	return characteristics
}

// calculateEntropy computes Shannon entropy of the data
func (cc *CheckpointCompressor) calculateEntropy(data []byte) float64 {
	if len(data) == 0 {
		return 0
	}

	// Count byte frequencies
	freq := make(map[byte]int)
	for _, b := range data {
		freq[b]++
	}

	// Calculate entropy
	entropy := 0.0
	length := float64(len(data))
	for _, count := range freq {
		if count > 0 {
			p := float64(count) / length
			entropy -= p * math.Log2(p)
		}
	}

	return entropy
}

// calculateSparsity calculates the percentage of zero values in numeric data
func (cc *CheckpointCompressor) calculateSparsity(data []byte, dataType DataType) float64 {
	zeroCount := 0
	totalCount := 0

	switch dataType {
	case DataTypeTensorFloat32:
		for i := 0; i+4 <= len(data); i += 4 {
			value := math.Float32frombits(binary.LittleEndian.Uint32(data[i : i+4]))
			if value == 0.0 {
				zeroCount++
			}
			totalCount++
		}
	case DataTypeTensorFloat64:
		for i := 0; i+8 <= len(data); i += 8 {
			value := math.Float64frombits(binary.LittleEndian.Uint64(data[i : i+8]))
			if value == 0.0 {
				zeroCount++
			}
			totalCount++
		}
	}

	if totalCount == 0 {
		return 0
	}
	return float64(zeroCount) / float64(totalCount)
}

// isTimeSeries detects if data represents time-ordered measurements
func (cc *CheckpointCompressor) isTimeSeries(data []byte, dataType DataType) bool {
	return dataType == DataTypePerformanceMetrics
}

// countRepeatingPatterns counts the number of repeating byte sequences
func (cc *CheckpointCompressor) countRepeatingPatterns(data []byte) int {
	if len(data) < 8 {
		return 0
	}

	patterns := make(map[string]int)
	patternSize := 4 // Look for 4-byte patterns

	for i := 0; i <= len(data)-patternSize; i++ {
		pattern := string(data[i : i+patternSize])
		patterns[pattern]++
	}

	repeatingCount := 0
	for _, count := range patterns {
		if count > 1 {
			repeatingCount++
		}
	}

	return repeatingCount
}

// selectOptimalAlgorithm chooses the best compression algorithm based on data characteristics
func (cc *CheckpointCompressor) selectOptimalAlgorithm(characteristics DataCharacteristics) string {
	if cc.config.AdaptiveSelection {
		if selected := cc.adaptiveSelector.selectBest(characteristics); selected != "" {
			// Verify the selected algorithm is available
			if _, exists := cc.compressors[selected]; exists {
				return selected
			}
		}
	}

	// Rule-based selection for cold start or fallback
	// Try preferred algorithms first, fall back if not available
	var candidates []string

	switch {
	case characteristics.DataType == DataTypeTensorFloat32 && characteristics.Sparsity > 0.5:
		candidates = []string{"sparse_tensor", "zstd_best", "zstd"}
	case characteristics.DataType == DataTypeTensorFloat32 || characteristics.DataType == DataTypeTensorFloat64:
		candidates = []string{"tensor_float", "sparse_tensor", "zstd_best", "zstd"}
	case characteristics.IsTimeSeries:
		candidates = []string{"gorilla_ts", "zstd", "lz4"}
	case characteristics.Size > 10*1024*1024: // Large data
		if cc.config.PreferSpeed {
			candidates = []string{"lz4", "zstd_fast", "zstd"}
		} else {
			candidates = []string{"zstd", "zstd_fast", "lz4"}
		}
	case characteristics.Entropy < 4.0: // Low entropy (repetitive data)
		candidates = []string{"zstd_best", "zstd", "gzip"}
	case cc.config.PreferSpeed:
		candidates = []string{"lz4", "zstd_fast", "zstd"}
	default:
		candidates = []string{"zstd", "zstd_fast", "lz4", "gzip"}
	}

	// Return the first available algorithm from the candidate list
	for _, algorithm := range candidates {
		if _, exists := cc.compressors[algorithm]; exists {
			return algorithm
		}
	}

	// Ultimate fallback - return any available compressor
	for name := range cc.compressors {
		return name
	}

	// This should never happen if compressors are properly registered
	return "gzip"
}

// updateStats updates performance statistics for a compression algorithm
func (cc *CheckpointCompressor) updateStats(algorithm string, success bool, originalSize, compressedSize int64,
	compTime, decompTime time.Duration) {
	cc.mu.Lock()
	defer cc.mu.Unlock()

	// Skip stats for "none" algorithm (uncompressed data)
	if algorithm == "none" {
		return
	}

	stats, exists := cc.compressionStats[algorithm]
	if !exists {
		// Initialize stats if they don't exist
		stats = &CompressionStats{
			LastUsed: time.Now(),
		}
		cc.compressionStats[algorithm] = stats
	}

	stats.TotalOperations++
	stats.LastUsed = time.Now()

	if success {
		stats.TotalOriginalSize += originalSize
		stats.TotalCompressedSize += compressedSize

		// Update averages
		if stats.TotalOperations > 0 {
			stats.AverageRatio = float64(stats.TotalOriginalSize) / float64(stats.TotalCompressedSize)
		}

		// Update timing averages (exponential moving average)
		alpha := 0.1
		if stats.AverageCompTime == 0 {
			stats.AverageCompTime = compTime
			stats.AverageDecompTime = decompTime
		} else {
			stats.AverageCompTime = time.Duration(float64(stats.AverageCompTime)*(1-alpha) + float64(compTime)*alpha)
			stats.AverageDecompTime = time.Duration(float64(stats.AverageDecompTime)*(1-alpha) + float64(decompTime)*alpha)
		}

		stats.SuccessRate = (stats.SuccessRate*float64(stats.TotalOperations-1) + 1.0) / float64(stats.TotalOperations)
	} else {
		stats.SuccessRate = (stats.SuccessRate * float64(stats.TotalOperations-1)) / float64(stats.TotalOperations)
	}
}

// GetCompressionStats returns performance statistics for all algorithms
func (cc *CheckpointCompressor) GetCompressionStats() map[string]*CompressionStats {
	cc.mu.RLock()
	defer cc.mu.RUnlock()

	result := make(map[string]*CompressionStats)
	for name, stats := range cc.compressionStats {
		// Create a copy to avoid race conditions
		statsCopy := *stats
		result[name] = &statsCopy
	}
	return result
}

// AdaptiveSelector methods

// updatePerformance updates the performance history for adaptive selection
func (as *AdaptiveSelector) updatePerformance(dataType DataType, algorithm string, ratio float64, speed time.Duration) {
	as.mu.Lock()
	defer as.mu.Unlock()

	key := string(dataType) + ":" + algorithm
	perf, exists := as.history[key]
	if !exists {
		perf = &AlgorithmPerformance{
			DataType:  dataType,
			Algorithm: algorithm,
		}
		as.history[key] = perf
	}

	// Update averages using exponential moving average
	alpha := 0.2
	if perf.SampleCount == 0 {
		perf.AverageRatio = ratio
		perf.AverageSpeed = speed
		perf.ReliabilityScore = 1.0
	} else {
		perf.AverageRatio = perf.AverageRatio*(1-alpha) + ratio*alpha
		perf.AverageSpeed = time.Duration(float64(perf.AverageSpeed)*(1-alpha) + float64(speed)*alpha)
		perf.ReliabilityScore = perf.ReliabilityScore*(1-alpha) + 1.0*alpha // Success increases reliability
	}

	perf.SampleCount++
}

// selectBest selects the best algorithm based on historical performance
func (as *AdaptiveSelector) selectBest(characteristics DataCharacteristics) string {
	as.mu.RLock()
	defer as.mu.RUnlock()

	var candidates []*AlgorithmPerformance
	for _, perf := range as.history {
		if perf.DataType == characteristics.DataType && perf.SampleCount >= 3 {
			candidates = append(candidates, perf)
		}
	}

	if len(candidates) == 0 {
		return "" // No historical data, use rule-based selection
	}

	// Score algorithms based on ratio, speed, and reliability
	type scoredAlgorithm struct {
		algorithm string
		score     float64
	}

	var scored []scoredAlgorithm
	for _, perf := range candidates {
		// Composite score: ratio weight 0.4, speed weight 0.4, reliability weight 0.2
		speedScore := 1.0 / (float64(perf.AverageSpeed.Nanoseconds()) / 1e6) // Higher is better (lower time)
		score := perf.AverageRatio*0.4 + speedScore*0.4 + perf.ReliabilityScore*0.2

		scored = append(scored, scoredAlgorithm{
			algorithm: perf.Algorithm,
			score:     score,
		})
	}

	// Sort by score (descending)
	sort.Slice(scored, func(i, j int) bool {
		return scored[i].score > scored[j].score
	})

	return scored[0].algorithm
}
