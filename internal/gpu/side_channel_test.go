package gpu

import "testing"

func TestSideChannelMitigator_All(t *testing.T) {
	mgr := NewSideChannelMitigator(SecurityConfig{EnableSideChannelMitig: true})
	if err := mgr.MitigateTiming(); err != nil {
		t.<PERSON>("MitigateTiming error: %v", err)
	}
	if err := mgr.MitigatePower(); err != nil {
		t.<PERSON><PERSON>("MitigatePower error: %v", err)
	}
	if err := mgr.MitigateCache(); err != nil {
		t.<PERSON>rf("MitigateCache error: %v", err)
	}
}
