// Package gpu provides GPU resource management functionality
package gpu

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// ResourceManager manages GPU resource allocation and utilization
type ResourceManager struct {
	// Configuration
	config       GPUConfig
	enabled      bool
	manager      *Manager
	resourcePool *ResourcePool

	// State management
	mu     sync.RWMutex
	ctx    context.Context
	cancel context.CancelFunc

	// Resource tracking
	allocations map[string]*ResourceAllocation
	usage       *ResourceUsage
	limits      *ResourceLimits

	// Monitoring
	monitor *ResourceMonitor

	// Logging
	logger *log.Logger
}

// ResourcePool manages GPU resource pools and allocation
type ResourcePool struct {
	// Pool configuration
	maxMemoryPercentage float64
	maxGPUUtilization   float64
	maxConcurrentTasks  int

	// Current state
	allocatedMemory int64
	totalMemory     int64
	activeTasks     int

	// Synchronization
	mu sync.RWMutex
}

// ResourceAllocation represents an allocated GPU resource
type ResourceAllocation struct {
	ID              string           `json:"id"`
	RequesterID     string           `json:"requester_id"`
	DeviceID        int              `json:"device_id"`
	AllocatedMemory int64            `json:"allocated_memory"`
	ReservedMemory  int64            `json:"reserved_memory"`
	Priority        int              `json:"priority"`
	AllocationTime  time.Time        `json:"allocation_time"`
	LastUsed        time.Time        `json:"last_used"`
	MaxDuration     time.Duration    `json:"max_duration"`
	Status          AllocationStatus `json:"status"`
}

// AllocationStatus represents the status of a resource allocation
type AllocationStatus string

const (
	AllocationStatusActive   AllocationStatus = "active"
	AllocationStatusIdle     AllocationStatus = "idle"
	AllocationStatusExpired  AllocationStatus = "expired"
	AllocationStatusReleased AllocationStatus = "released"
)

// ResourceUsage tracks current GPU resource usage
type ResourceUsage struct {
	DeviceID           int       `json:"device_id"`
	TotalMemory        int64     `json:"total_memory"`
	UsedMemory         int64     `json:"used_memory"`
	AllocatedMemory    int64     `json:"allocated_memory"`
	MemoryUtilization  float64   `json:"memory_utilization"`
	GPUUtilization     float64   `json:"gpu_utilization"`
	ActiveAllocations  int       `json:"active_allocations"`
	TotalAllocations   int64     `json:"total_allocations"`
	AllocationFailures int64     `json:"allocation_failures"`
	LastUpdated        time.Time `json:"last_updated"`
}

// ResourceLimits defines limits for GPU resource usage
type ResourceLimits struct {
	MaxMemoryUtilization float64       `json:"max_memory_utilization"`
	MaxGPUUtilization    float64       `json:"max_gpu_utilization"`
	MaxConcurrentTasks   int           `json:"max_concurrent_tasks"`
	MaxAllocationSize    int64         `json:"max_allocation_size"`
	DefaultTaskTimeout   time.Duration `json:"default_task_timeout"`
	CleanupInterval      time.Duration `json:"cleanup_interval"`
}

// AllocationRequest represents a request for GPU resources
type AllocationRequest struct {
	RequesterID     string                `json:"requester_id"`
	RequestedMemory int64                 `json:"requested_memory"`
	Priority        int                   `json:"priority"`
	MaxDuration     time.Duration         `json:"max_duration"`
	Requirements    *ResourceRequirements `json:"requirements,omitempty"`
}

// ResourceRequirements specifies requirements for GPU allocation
type ResourceRequirements struct {
	MinComputeCapability ComputeCapability `json:"min_compute_capability"`
	RequiredFeatures     []string          `json:"required_features"`
	PreferredDeviceID    int               `json:"preferred_device_id"`
	MinMemoryBandwidth   float64           `json:"min_memory_bandwidth"`
	MaxLatency           time.Duration     `json:"max_latency"`
}

// NewResourceManager creates a new GPU resource manager
func NewResourceManager(config GPUConfig, manager *Manager, logger *log.Logger) (*ResourceManager, error) {
	if manager == nil {
		return nil, fmt.Errorf("GPU manager is required")
	}

	if logger == nil {
		logger = log.Default()
	}

	rm := &ResourceManager{
		config:      config,
		enabled:     config.Enabled,
		manager:     manager,
		allocations: make(map[string]*ResourceAllocation),
		logger:      logger,
	}

	// Initialize resource pool
	rm.resourcePool = &ResourcePool{
		maxMemoryPercentage: config.MaxMemoryUtilization,
		maxGPUUtilization:   90.0, // Default limit
		maxConcurrentTasks:  10,   // Default limit
	}

	// Set default limits
	rm.limits = &ResourceLimits{
		MaxMemoryUtilization: config.MaxMemoryUtilization,
		MaxGPUUtilization:    90.0,
		MaxConcurrentTasks:   10,
		MaxAllocationSize:    1024 * 1024 * 1024, // 1GB default
		DefaultTaskTimeout:   time.Hour,          // 1 hour default
		CleanupInterval:      time.Minute * 5,    // 5 minutes cleanup
	}

	// Initialize usage tracking
	rm.usage = &ResourceUsage{
		LastUpdated: time.Now(),
	}

	return rm, nil
}

// Start begins resource management operations
func (rm *ResourceManager) Start(ctx context.Context) error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	if !rm.enabled {
		return fmt.Errorf("GPU resource management is disabled")
	}

	if rm.ctx != nil {
		return fmt.Errorf("resource manager is already running")
	}

	rm.ctx, rm.cancel = context.WithCancel(ctx)

	// Start cleanup routine
	go rm.cleanupRoutine()

	// Start monitoring if not already started
	if rm.monitor != nil {
		if err := rm.monitor.Start(rm.ctx); err != nil {
			rm.logger.Printf("Failed to start resource monitoring: %v", err)
		}
	}

	rm.logger.Println("GPU resource manager started")
	return nil
}

// Stop stops resource management operations
func (rm *ResourceManager) Stop() error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	if rm.cancel != nil {
		rm.cancel()
		rm.ctx = nil
		rm.cancel = nil
	}

	// Stop monitoring
	if rm.monitor != nil {
		if err := rm.monitor.Stop(); err != nil {
			rm.logger.Printf("Failed to stop resource monitoring: %v", err)
		}
	}

	// Release all allocations
	for id := range rm.allocations {
		if err := rm.releaseAllocation(id); err != nil {
			rm.logger.Printf("Failed to release allocation %s: %v", id, err)
		}
	}

	rm.logger.Println("GPU resource manager stopped")
	return nil
}

// AllocateResources allocates GPU resources based on the request
func (rm *ResourceManager) AllocateResources(request AllocationRequest) (*ResourceAllocation, error) {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	if !rm.enabled {
		return nil, fmt.Errorf("GPU resource management is disabled")
	}

	// Validate request
	if err := rm.validateRequest(request); err != nil {
		return nil, fmt.Errorf("invalid allocation request: %w", err)
	}

	// Check resource availability
	if !rm.canAllocate(request) {
		rm.usage.AllocationFailures++
		return nil, fmt.Errorf("insufficient resources available")
	}

	// Select best GPU for allocation
	gpu, err := rm.selectGPU(request)
	if err != nil {
		rm.usage.AllocationFailures++
		return nil, fmt.Errorf("failed to select GPU: %w", err)
	}

	// Create allocation
	allocation := &ResourceAllocation{
		ID:              generateAllocationID(),
		RequesterID:     request.RequesterID,
		DeviceID:        gpu.ID,
		AllocatedMemory: request.RequestedMemory,
		ReservedMemory:  calculateReservedMemory(request.RequestedMemory),
		Priority:        request.Priority,
		AllocationTime:  time.Now(),
		LastUsed:        time.Now(),
		MaxDuration:     request.MaxDuration,
		Status:          AllocationStatusActive,
	}

	// Update resource tracking
	rm.allocations[allocation.ID] = allocation
	rm.resourcePool.allocatedMemory += allocation.AllocatedMemory
	rm.resourcePool.activeTasks++
	rm.usage.TotalAllocations++
	rm.usage.ActiveAllocations++

	rm.logger.Printf("Allocated GPU resources: ID=%s, Device=%d, Memory=%d bytes",
		allocation.ID, allocation.DeviceID, allocation.AllocatedMemory)

	return allocation, nil
}

// ReleaseResources releases allocated GPU resources
func (rm *ResourceManager) ReleaseResources(allocationID string) error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	return rm.releaseAllocation(allocationID)
}

// releaseAllocation releases an allocation (internal method, assumes lock is held)
func (rm *ResourceManager) releaseAllocation(allocationID string) error {
	allocation, exists := rm.allocations[allocationID]
	if !exists {
		return fmt.Errorf("allocation %s not found", allocationID)
	}

	// Update resource tracking
	rm.resourcePool.allocatedMemory -= allocation.AllocatedMemory
	rm.resourcePool.activeTasks--
	rm.usage.ActiveAllocations--

	// Mark as released
	allocation.Status = AllocationStatusReleased
	delete(rm.allocations, allocationID)

	rm.logger.Printf("Released GPU resources: ID=%s, Device=%d", allocationID, allocation.DeviceID)
	return nil
}

// GetResourceUsage returns current resource usage statistics
func (rm *ResourceManager) GetResourceUsage() (*ResourceUsage, error) {
	rm.mu.RLock()
	defer rm.mu.RUnlock()

	// Update usage statistics
	if err := rm.updateUsageStats(); err != nil {
		return nil, fmt.Errorf("failed to update usage stats: %w", err)
	}

	// Return a copy
	usage := *rm.usage
	return &usage, nil
}

// GetActiveAllocations returns all active resource allocations
func (rm *ResourceManager) GetActiveAllocations() ([]*ResourceAllocation, error) {
	rm.mu.RLock()
	defer rm.mu.RUnlock()

	var active []*ResourceAllocation
	for _, allocation := range rm.allocations {
		if allocation.Status == AllocationStatusActive {
			// Return a copy
			allocCopy := *allocation
			active = append(active, &allocCopy)
		}
	}

	return active, nil
}

// UpdateAllocation updates an existing allocation's last used time
func (rm *ResourceManager) UpdateAllocation(allocationID string) error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	allocation, exists := rm.allocations[allocationID]
	if !exists {
		return fmt.Errorf("allocation %s not found", allocationID)
	}

	allocation.LastUsed = time.Now()
	return nil
}

// SetResourceLimits updates resource limits
func (rm *ResourceManager) SetResourceLimits(limits ResourceLimits) error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	// Validate limits
	if limits.MaxMemoryUtilization <= 0 || limits.MaxMemoryUtilization > 100 {
		return fmt.Errorf("max memory utilization must be between 0 and 100")
	}

	if limits.MaxGPUUtilization <= 0 || limits.MaxGPUUtilization > 100 {
		return fmt.Errorf("max GPU utilization must be between 0 and 100")
	}

	rm.limits = &limits
	rm.logger.Printf("Updated resource limits: Memory=%.1f%%, GPU=%.1f%%, Tasks=%d",
		limits.MaxMemoryUtilization, limits.MaxGPUUtilization, limits.MaxConcurrentTasks)

	return nil
}

// SetMonitor sets the resource monitor for usage tracking
func (rm *ResourceManager) SetMonitor(monitor *ResourceMonitor) {
	rm.mu.Lock()
	defer rm.mu.Unlock()
	rm.monitor = monitor
}

// validateRequest validates an allocation request
func (rm *ResourceManager) validateRequest(request AllocationRequest) error {
	if request.RequesterID == "" {
		return fmt.Errorf("requester ID is required")
	}

	if request.RequestedMemory <= 0 {
		return fmt.Errorf("requested memory must be positive")
	}

	if request.RequestedMemory > rm.limits.MaxAllocationSize {
		return fmt.Errorf("requested memory exceeds maximum allocation size")
	}

	if request.MaxDuration <= 0 {
		request.MaxDuration = rm.limits.DefaultTaskTimeout
	}

	return nil
}

// canAllocate checks if resources can be allocated
func (rm *ResourceManager) canAllocate(request AllocationRequest) bool {
	// Check concurrent task limit
	if rm.resourcePool.activeTasks >= rm.limits.MaxConcurrentTasks {
		return false
	}

	// Check memory availability
	if rm.resourcePool.totalMemory > 0 {
		availableMemory := rm.resourcePool.totalMemory - rm.resourcePool.allocatedMemory
		if request.RequestedMemory > availableMemory {
			return false
		}

		// Check against utilization limits
		futureUtilization := float64(rm.resourcePool.allocatedMemory+request.RequestedMemory) / float64(rm.resourcePool.totalMemory) * 100
		if futureUtilization > rm.limits.MaxMemoryUtilization {
			return false
		}
	}

	return true
}

// selectGPU selects the best GPU for allocation
func (rm *ResourceManager) selectGPU(request AllocationRequest) (*GPUInfo, error) {
	gpus, err := rm.manager.GetAvailableGPUs()
	if err != nil {
		return nil, err
	}

	if len(gpus) == 0 {
		return nil, fmt.Errorf("no GPUs available")
	}

	// For now, select the first available GPU
	// TODO: Implement smart GPU selection based on requirements
	return gpus[0], nil
}

// updateUsageStats updates current usage statistics
func (rm *ResourceManager) updateUsageStats() error {
	gpus, err := rm.manager.GetAvailableGPUs()
	if err != nil {
		return err
	}

	if len(gpus) == 0 {
		return fmt.Errorf("no GPUs available for stats")
	}

	// Update with primary GPU stats
	primaryGPU := gpus[0]
	rm.usage.DeviceID = primaryGPU.ID
	rm.usage.TotalMemory = primaryGPU.TotalMemory
	rm.usage.UsedMemory = primaryGPU.TotalMemory - primaryGPU.FreeMemory
	rm.usage.AllocatedMemory = rm.resourcePool.allocatedMemory
	rm.usage.MemoryUtilization = primaryGPU.MemoryUtilization()
	rm.usage.GPUUtilization = primaryGPU.Utilization
	rm.usage.LastUpdated = time.Now()

	// Update resource pool total memory
	rm.resourcePool.totalMemory = primaryGPU.TotalMemory

	return nil
}

// cleanupRoutine periodically cleans up expired allocations
func (rm *ResourceManager) cleanupRoutine() {
	ticker := time.NewTicker(rm.limits.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-rm.ctx.Done():
			return
		case <-ticker.C:
			rm.cleanupExpiredAllocations()
		}
	}
}

// cleanupExpiredAllocations removes expired allocations
func (rm *ResourceManager) cleanupExpiredAllocations() {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	now := time.Now()
	var expired []string

	for id, allocation := range rm.allocations {
		if allocation.MaxDuration > 0 {
			if now.Sub(allocation.AllocationTime) > allocation.MaxDuration {
				expired = append(expired, id)
				allocation.Status = AllocationStatusExpired
			}
		}

		// Mark idle allocations
		if now.Sub(allocation.LastUsed) > time.Minute*30 { // 30 minutes idle
			allocation.Status = AllocationStatusIdle
		}
	}

	// Release expired allocations
	for _, id := range expired {
		if err := rm.releaseAllocation(id); err != nil {
			rm.logger.Printf("Failed to cleanup expired allocation %s: %v", id, err)
		} else {
			rm.logger.Printf("Cleaned up expired allocation: %s", id)
		}
	}
}

// generateAllocationID generates a unique allocation ID
func generateAllocationID() string {
	return fmt.Sprintf("alloc_%d", time.Now().UnixNano())
}

// calculateReservedMemory calculates reserved memory based on allocated memory
func calculateReservedMemory(allocated int64) int64 {
	// Reserve 10% additional memory for overhead
	return allocated + (allocated / 10)
}
