package gpu

import (
	"fmt"
	"log"
	"os"
	"testing"
	"time"
)

// TestAutomaticGarbageCollection tests the complete automatic GC system for Task 75.5
func TestAutomaticGarbageCollection(t *testing.T) {
	logger := log.New(os.Stdout, "TEST_AUTO_GC: ", log.LstdFlags)

	config := DefaultPoolConfig(0)
	config.Strategy = StrategyDynamic
	config.GCInterval = 100 * time.Millisecond // Fast GC for testing
	config.MaxSize = 8 * 1024 * 1024           // 8MB max

	pool, err := NewAdvancedMemoryPool(config, logger)
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	if err := pool.Initialize(); err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Track GC events with detailed information
	var gcEvents []GCEvent
	pool.AddGCHandler(func(freedMemory int64, duration time.Duration, stats MemoryPoolStatistics) {
		gcEvents = append(gcEvents, GCEvent{
			FreedMemory: freedMemory,
			Duration:    duration,
			Timestamp:   time.Now(),
			GCCount:     stats.GCCount,
		})
		t.Logf("Auto GC #%d: freed %d bytes in %v", stats.GCCount, freedMemory, duration)
	})

	// Test 1: Basic automatic GC functionality
	t.Run("BasicAutomaticGC", func(t *testing.T) {
		// Allocate multiple blocks
		var allocations []CUDAMemoryPtr
		for i := 0; i < 6; i++ {
			ptr, err := pool.Allocate(1024 * 1024) // 1MB each
			if err != nil {
				t.Fatalf("Failed to allocate memory: %v", err)
			}
			allocations = append(allocations, ptr)
		}

		// Free half the blocks to create garbage
		for i := 0; i < 3; i++ {
			if err := pool.Free(allocations[i]); err != nil {
				t.Errorf("Failed to free memory: %v", err)
			}
		}

		initialStats := pool.GetStatistics()
		t.Logf("Initial state: %d free blocks, %d bytes free",
			initialStats.FreeBlockCount, initialStats.FreeSize)

		// Wait for automatic GC to run (need to wait > 30 seconds for GC threshold)
		t.Log("Waiting for GC threshold period...")
		time.Sleep(31 * time.Second) // Wait for GC threshold

		// Wait for GC to run
		time.Sleep(200 * time.Millisecond)

		stats := pool.GetStatistics()
		if stats.GCCount == 0 {
			t.Errorf("Expected at least one automatic GC cycle, got %d", stats.GCCount)
		}

		if len(gcEvents) == 0 {
			t.Error("Expected GC events to be recorded")
		}

		// Clean up remaining allocations
		for i := 3; i < len(allocations); i++ {
			pool.Free(allocations[i])
		}
	})

	// Test 2: GC performance impact measurement
	t.Run("GCPerformanceImpact", func(t *testing.T) {
		// Measure allocation performance without GC pressure
		start := time.Now()
		var normalAllocations []CUDAMemoryPtr
		for i := 0; i < 50; i++ {
			ptr, err := pool.Allocate(256 * 1024) // 256KB each
			if err != nil {
				t.Fatalf("Failed to allocate during normal operation: %v", err)
			}
			normalAllocations = append(normalAllocations, ptr)
		}
		normalDuration := time.Since(start)

		// Clean up
		for _, ptr := range normalAllocations {
			pool.Free(ptr)
		}

		// Create GC pressure and measure performance
		var gcAllocations []CUDAMemoryPtr
		for i := 0; i < 20; i++ {
			ptr, err := pool.Allocate(512 * 1024) // 512KB each
			if err != nil {
				t.Fatalf("Failed to allocate during GC test: %v", err)
			}
			gcAllocations = append(gcAllocations, ptr)
		}

		// Free some to create garbage
		for i := 0; i < 10; i++ {
			pool.Free(gcAllocations[i])
		}

		// Measure allocation performance during GC activity
		start = time.Now()
		var gcTestAllocations []CUDAMemoryPtr
		for i := 0; i < 50; i++ {
			ptr, err := pool.Allocate(256 * 1024)
			if err != nil {
				t.Fatalf("Failed to allocate during GC activity: %v", err)
			}
			gcTestAllocations = append(gcTestAllocations, ptr)
		}
		gcDuration := time.Since(start)

		// Performance should not degrade significantly (allow 50% overhead)
		if gcDuration > normalDuration*3/2 {
			t.Errorf("GC caused significant performance impact: normal=%v, with GC=%v",
				normalDuration, gcDuration)
		} else {
			t.Logf("GC performance impact acceptable: normal=%v, with GC=%v",
				normalDuration, gcDuration)
		}

		// Clean up
		for _, ptr := range gcTestAllocations {
			pool.Free(ptr)
		}
		for i := 10; i < len(gcAllocations); i++ {
			pool.Free(gcAllocations[i])
		}
	})

	// Test 3: Memory pressure integration
	t.Run("MemoryPressureIntegration", func(t *testing.T) {
		// Track pressure changes
		var pressureEvents []PressureEvent
		pool.AddPressureHandler(func(oldLevel, newLevel MemoryPressureLevel, stats MemoryPoolStatistics) {
			pressureEvents = append(pressureEvents, PressureEvent{
				OldLevel:  oldLevel,
				NewLevel:  newLevel,
				Timestamp: time.Now(),
				UsedSize:  stats.UsedSize,
				TotalSize: stats.TotalSize,
			})
			t.Logf("Pressure change: %v -> %v (used: %d, total: %d)",
				oldLevel, newLevel, stats.UsedSize, stats.TotalSize)
		})

		// Create high memory pressure
		var allocations []CUDAMemoryPtr
		for i := 0; i < 10; i++ {
			ptr, err := pool.Allocate(512 * 1024) // 512KB each
			if err != nil {
				break // Expected to fail eventually
			}
			allocations = append(allocations, ptr)
		}

		// Free most allocations to reduce pressure
		freeCount := len(allocations) * 3 / 4
		for i := 0; i < freeCount; i++ {
			pool.Free(allocations[i])
		}

		// Verify that GC and pressure system work together
		time.Sleep(200 * time.Millisecond)

		if len(pressureEvents) == 0 {
			t.Error("Expected pressure level changes during test")
		}

		// Clean up
		for i := freeCount; i < len(allocations); i++ {
			pool.Free(allocations[i])
		}
	})

	// Test 4: GC effectiveness measurement
	t.Run("GCEffectiveness", func(t *testing.T) {
		initialStats := pool.GetStatistics()
		initialMemory := initialStats.TotalSize

		// Create and free many blocks to generate garbage
		for cycle := 0; cycle < 3; cycle++ {
			var allocations []CUDAMemoryPtr
			for i := 0; i < 8; i++ {
				ptr, err := pool.Allocate(1024 * 1024) // 1MB each
				if err != nil {
					t.Fatalf("Failed to allocate in effectiveness test: %v", err)
				}
				allocations = append(allocations, ptr)
			}

			// Free all blocks
			for _, ptr := range allocations {
				pool.Free(ptr)
			}

			// Wait for potential GC
			time.Sleep(100 * time.Millisecond)
		}

		// Wait for GC threshold and execution
		time.Sleep(31 * time.Second)
		time.Sleep(300 * time.Millisecond)

		finalStats := pool.GetStatistics()

		// GC should have run at least once during this test
		if finalStats.GCCount <= initialStats.GCCount {
			t.Logf("Warning: Expected GC to run during effectiveness test (initial: %d, final: %d)",
				initialStats.GCCount, finalStats.GCCount)
		}

		t.Logf("Memory before test: %d bytes, after test: %d bytes",
			initialMemory, finalStats.TotalSize)
		t.Logf("GC runs during test: %d", finalStats.GCCount-initialStats.GCCount)
	})
}

// Helper structures for test event tracking
type GCEvent struct {
	FreedMemory int64
	Duration    time.Duration
	Timestamp   time.Time
	GCCount     int64
}

type PressureEvent struct {
	OldLevel  MemoryPressureLevel
	NewLevel  MemoryPressureLevel
	Timestamp time.Time
	UsedSize  int64
	TotalSize int64
}

// TestAutomaticGarbageCollectionFeatures tests the complete automatic GC system for Task 75.5
func TestAutomaticGarbageCollectionFeatures(t *testing.T) {
	logger := log.New(os.Stdout, "TEST_AUTO_GC: ", log.LstdFlags)

	config := DefaultPoolConfig(0)
	config.Strategy = StrategyDynamic
	config.GCInterval = 50 * time.Millisecond // Fast GC for testing
	config.MaxSize = 8 * 1024 * 1024          // 8MB max

	pool, err := NewAdvancedMemoryPool(config, logger)
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	if err := pool.Initialize(); err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Test 1: Verify GC handlers can be registered and work
	t.Run("GC Handler Registration", func(t *testing.T) {
		gcEventCount := 0
		var lastFreedMemory int64
		handlerCalled := make(chan bool, 1)

		pool.AddGCHandler(func(freedMemory int64, duration time.Duration, stats MemoryPoolStatistics) {
			gcEventCount++
			lastFreedMemory = freedMemory
			t.Logf("GC Event %d: Freed %d bytes in %v", gcEventCount, freedMemory, duration)

			// Signal that handler was called
			select {
			case handlerCalled <- true:
			default:
			}
		})

		// Trigger manual GC to test handlers
		pool.performGarbageCollection()

		// Wait for handler to be called (with timeout)
		select {
		case <-handlerCalled:
			t.Logf("GC handler successfully called %d times", gcEventCount)
			// Note: freedMemory may be 0 if no blocks are old enough to collect (30s threshold)
			// This is expected behavior for immediate GC calls
			t.Logf("Last GC freed %d bytes (0 is normal for immediate GC)", lastFreedMemory)
		case <-time.After(100 * time.Millisecond):
			t.Error("GC handler was not called within timeout")
		}
	})

	// Test 2: Verify background GC process is running
	t.Run("Background GC Process", func(t *testing.T) {
		stats1 := pool.GetStatistics()
		initialGCCount := stats1.GCCount

		// Wait for at least one GC cycle
		time.Sleep(150 * time.Millisecond)

		stats2 := pool.GetStatistics()
		finalGCCount := stats2.GCCount

		if finalGCCount <= initialGCCount {
			t.Error("Background GC process does not appear to be running")
		} else {
			t.Logf("Background GC running: %d -> %d GC cycles", initialGCCount, finalGCCount)
		}
	})

	// Test 3: Verify GC integration with memory allocation/deallocation
	t.Run("GC Integration with Memory Operations", func(t *testing.T) {
		// Allocate some memory blocks
		ptrs := make([]CUDAMemoryPtr, 5)
		for i := 0; i < 5; i++ {
			ptr, err := pool.Allocate(1024 * 1024) // 1MB each
			if err != nil {
				t.Fatalf("Failed to allocate memory: %v", err)
			}
			ptrs[i] = ptr
		}

		statsAfterAlloc := pool.GetStatistics()
		t.Logf("After allocation: %d bytes used, %d blocks",
			statsAfterAlloc.UsedSize, statsAfterAlloc.UsedBlockCount)

		// Free all blocks
		for _, ptr := range ptrs {
			if err := pool.Free(ptr); err != nil {
				t.Fatalf("Failed to free memory: %v", err)
			}
		}

		statsAfterFree := pool.GetStatistics()
		t.Logf("After freeing: %d bytes used, %d free blocks",
			statsAfterFree.UsedSize, statsAfterFree.FreeBlockCount)

		// The GC should eventually run and potentially clean up
		// We don't wait for actual cleanup since it requires 30s threshold,
		// but we verify the system is tracking everything correctly
		if statsAfterFree.UsedSize != 0 {
			t.Error("Memory should be marked as free after deallocation")
		}
		if statsAfterFree.FreeBlockCount == 0 {
			t.Error("Should have free blocks after deallocation")
		}
	})
}

// TestGCUnderLoad tests GC behavior under memory allocation load
func TestGCUnderLoad(t *testing.T) {
	logger := log.New(os.Stdout, "TEST_GC_LOAD: ", log.LstdFlags)

	config := DefaultPoolConfig(0)
	config.Strategy = StrategyDynamic
	config.GCInterval = 25 * time.Millisecond // Very fast GC
	config.MaxSize = 16 * 1024 * 1024         // 16MB max

	pool, err := NewAdvancedMemoryPool(config, logger)
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	if err := pool.Initialize(); err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Track GC activity
	gcEvents := 0
	pool.AddGCHandler(func(freedMemory int64, duration time.Duration, stats MemoryPoolStatistics) {
		gcEvents++
	})

	// Simulate memory allocation/deallocation workload
	const iterations = 50
	allocSizes := []int64{512 * 1024, 1024 * 1024, 2 * 1024 * 1024} // 512KB, 1MB, 2MB

	start := time.Now()

	for i := 0; i < iterations; i++ {
		size := allocSizes[i%len(allocSizes)]

		// Allocate
		ptr, err := pool.Allocate(size)
		if err != nil {
			t.Fatalf("Allocation %d failed: %v", i, err)
		}

		// Immediately free (creates free blocks for GC)
		if err := pool.Free(ptr); err != nil {
			t.Fatalf("Deallocation %d failed: %v", i, err)
		}

		// Brief pause to allow GC to run
		if i%10 == 0 {
			time.Sleep(30 * time.Millisecond)
		}
	}

	duration := time.Since(start)
	stats := pool.GetStatistics()

	t.Logf("Load test completed in %v", duration)
	t.Logf("Final stats: %d allocs, %d deallocs, %d GC cycles",
		stats.AllocationCount, stats.DeallocationCount, stats.GCCount)
	t.Logf("Memory: %d bytes total, %d bytes used, %d free blocks",
		stats.TotalSize, stats.UsedSize, stats.FreeBlockCount)
	t.Logf("GC events captured: %d", gcEvents)

	// Verify the system handled the load correctly
	if stats.AllocationCount != int64(iterations) {
		t.Errorf("Expected %d allocations, got %d", iterations, stats.AllocationCount)
	}
	if stats.DeallocationCount != int64(iterations) {
		t.Errorf("Expected %d deallocations, got %d", iterations, stats.DeallocationCount)
	}
	if stats.GCCount == 0 {
		t.Error("Expected at least one GC cycle during load test")
	}
	if stats.UsedSize != 0 {
		t.Error("All memory should be free after load test")
	}
}

// TestGCConfigurationAndTiming tests GC configuration options
func TestGCConfigurationAndTiming(t *testing.T) {
	logger := log.New(os.Stdout, "TEST_GC_CONFIG: ", log.LstdFlags)

	// Test with different GC intervals
	intervals := []time.Duration{
		10 * time.Millisecond,
		50 * time.Millisecond,
		100 * time.Millisecond,
	}

	for _, interval := range intervals {
		t.Run(fmt.Sprintf("GC_Interval_%v", interval), func(t *testing.T) {
			config := DefaultPoolConfig(0)
			config.GCInterval = interval
			config.MaxSize = 4 * 1024 * 1024 // 4MB

			pool, err := NewAdvancedMemoryPool(config, logger)
			if err != nil {
				t.Fatalf("Failed to create pool with interval %v: %v", interval, err)
			}

			if err := pool.Initialize(); err != nil {
				t.Fatalf("Failed to initialize pool: %v", err)
			}

			// Measure GC frequency
			initialStats := pool.GetStatistics()
			time.Sleep(200 * time.Millisecond) // Wait for multiple potential GC cycles
			finalStats := pool.GetStatistics()

			gcCycles := finalStats.GCCount - initialStats.GCCount
			t.Logf("Interval %v: %d GC cycles in 200ms", interval, gcCycles)

			pool.Shutdown()

			// Shorter intervals should generally produce more GC cycles
			// (though this isn't guaranteed due to timing variations)
			if gcCycles == 0 {
				t.Errorf("Expected at least one GC cycle with interval %v", interval)
			}
		})
	}
}
