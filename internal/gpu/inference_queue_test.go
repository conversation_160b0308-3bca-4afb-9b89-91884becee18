package gpu

import (
	"context"
	"sync"
	"testing"
	"time"
)

func TestRequestPriorityString(t *testing.T) {
	tests := []struct {
		priority RequestPriority
		expected string
	}{
		{RequestPriorityLow, "low"},
		{RequestPriorityNormal, "normal"},
		{RequestPriorityHigh, "high"},
		{RequestPriorityCritical, "critical"},
		{RequestPriority(99), "unknown"},
	}

	for _, test := range tests {
		result := test.priority.String()
		if result != test.expected {
			t.<PERSON><PERSON>("Priority %d: expected %s, got %s", test.priority, test.expected, result)
		}
	}
}

func TestDefaultQueueConfig(t *testing.T) {
	config := DefaultQueueConfig()

	if config.MaxQueueSize != 1000 {
		t.<PERSON>rf("Expected MaxQueueSize 1000, got %d", config.MaxQueueSize)
	}

	if config.MaxBatchWaitTime != 50*time.Millisecond {
		t.<PERSON>rf("Expected MaxBatchWaitTime 50ms, got %v", config.MaxBatchWaitTime)
	}

	if !config.EnableSLAMonitoring {
		t.Error("Expected SLA monitoring to be enabled by default")
	}

	if config.DropPolicy != "lowest_priority" {
		t.Errorf("Expected drop policy 'lowest_priority', got %s", config.DropPolicy)
	}
}

func TestNewInferenceQueue(t *testing.T) {
	// Create a mock batch optimizer
	batchOptimizer, err := NewBatchSizeOptimizer(0, DefaultOptimizerConfig())
	if err != nil {
		t.Fatalf("Failed to create batch optimizer: %v", err)
	}
	defer batchOptimizer.Close()

	// Test with default config
	queue, err := NewInferenceQueue(nil, batchOptimizer)
	if err != nil {
		t.Fatalf("Failed to create inference queue: %v", err)
	}
	defer queue.Close()

	// Verify initialization
	if queue.config == nil {
		t.Error("Expected default config to be set")
	}

	if len(queue.queues) != 4 {
		t.Errorf("Expected 4 priority queues, got %d", len(queue.queues))
	}

	if len(queue.metrics.PriorityMetrics) != 4 {
		t.Errorf("Expected 4 priority metric entries, got %d", len(queue.metrics.PriorityMetrics))
	}
}

func TestSubmitRequest(t *testing.T) {
	batchOptimizer, err := NewBatchSizeOptimizer(0, DefaultOptimizerConfig())
	if err != nil {
		t.Fatalf("Failed to create batch optimizer: %v", err)
	}
	defer batchOptimizer.Close()

	queue, err := NewInferenceQueue(nil, batchOptimizer)
	if err != nil {
		t.Fatalf("Failed to create inference queue: %v", err)
	}
	defer queue.Close()

	// Test submitting a valid request
	request := &InferenceRequest{
		Priority:    RequestPriorityNormal,
		Data:        []byte("test data"),
		InputShape:  []int{1, 3, 224, 224},
		ModelID:     "test-model",
		ClientID:    "test-client",
		SLADeadline: time.Now().Add(1 * time.Second),
		Metadata:    map[string]interface{}{"test": "value"},
	}

	err = queue.SubmitRequest(request)
	if err != nil {
		t.Errorf("Failed to submit request: %v", err)
	}

	// Verify request was processed
	time.Sleep(100 * time.Millisecond)

	metrics := queue.GetMetrics()
	if metrics.TotalRequests != 1 {
		t.Errorf("Expected 1 total request, got %d", metrics.TotalRequests)
	}

	// Test submitting nil request
	err = queue.SubmitRequest(nil)
	if err == nil {
		t.Error("Expected error when submitting nil request")
	}
}

func TestRequestPriorityHandling(t *testing.T) {
	batchOptimizer, err := NewBatchSizeOptimizer(0, DefaultOptimizerConfig())
	if err != nil {
		t.Fatalf("Failed to create batch optimizer: %v", err)
	}
	defer batchOptimizer.Close()

	queue, err := NewInferenceQueue(nil, batchOptimizer)
	if err != nil {
		t.Fatalf("Failed to create inference queue: %v", err)
	}
	defer queue.Close()

	// Submit requests with different priorities
	priorities := []RequestPriority{
		RequestPriorityLow,
		RequestPriorityHigh,
		RequestPriorityNormal,
		RequestPriorityCritical,
	}

	for i, priority := range priorities {
		request := &InferenceRequest{
			ID:          string(rune('A' + i)),
			Priority:    priority,
			Data:        []byte("test data"),
			ModelID:     "test-model",
			ClientID:    "test-client",
			SLADeadline: time.Now().Add(1 * time.Second),
		}

		err := queue.SubmitRequest(request)
		if err != nil {
			t.Errorf("Failed to submit request %s: %v", request.ID, err)
		}
	}

	// Wait for requests to be processed into queues
	time.Sleep(200 * time.Millisecond)

	// Get next batch and verify priority order
	ctx := context.Background()
	batch, err := queue.GetNextBatch(ctx)
	if err != nil {
		t.Fatalf("Failed to get next batch: %v", err)
	}

	if len(batch) == 0 {
		t.Fatal("Expected at least one request in batch")
	}

	// Verify highest priority request comes first
	if batch[0].Priority != RequestPriorityCritical {
		t.Errorf("Expected first request to be critical priority, got %s", batch[0].Priority.String())
	}
}

func TestSLAManagement(t *testing.T) {
	batchOptimizer, err := NewBatchSizeOptimizer(0, DefaultOptimizerConfig())
	if err != nil {
		t.Fatalf("Failed to create batch optimizer: %v", err)
	}
	defer batchOptimizer.Close()

	queue, err := NewInferenceQueue(nil, batchOptimizer)
	if err != nil {
		t.Fatalf("Failed to create inference queue: %v", err)
	}
	defer queue.Close()

	// Add client SLA
	sla := &ClientSLA{
		ClientID:         "test-client",
		MaxLatency:       100 * time.Millisecond,
		ThroughputQuota:  100,
		PriorityWeight:   1.5,
		IsGuaranteed:     true,
		FailureThreshold: 0.05,
	}

	queue.AddClientSLA(sla)

	// Verify SLA was added
	queue.mu.RLock()
	storedSLA := queue.clientSLAs["test-client"]
	queue.mu.RUnlock()

	if storedSLA == nil {
		t.Error("Expected SLA to be stored")
	}

	if storedSLA.MaxLatency != 100*time.Millisecond {
		t.Errorf("Expected MaxLatency 100ms, got %v", storedSLA.MaxLatency)
	}

	// Test removing SLA
	queue.RemoveClientSLA("test-client")

	queue.mu.RLock()
	removedSLA := queue.clientSLAs["test-client"]
	queue.mu.RUnlock()

	if removedSLA != nil {
		t.Error("Expected SLA to be removed")
	}
}

func TestBackpressureHandling(t *testing.T) {
	// Create small queue for testing backpressure
	config := &QueueConfig{
		MaxQueueSize:        2,
		MaxBatchWaitTime:    50 * time.Millisecond,
		SLACheckInterval:    1 * time.Second,
		BackpressureEnabled: true,
		DropPolicy:          "lowest_priority",
	}

	batchOptimizer, err := NewBatchSizeOptimizer(0, DefaultOptimizerConfig())
	if err != nil {
		t.Fatalf("Failed to create batch optimizer: %v", err)
	}
	defer batchOptimizer.Close()

	queue, err := NewInferenceQueue(config, batchOptimizer)
	if err != nil {
		t.Fatalf("Failed to create inference queue: %v", err)
	}
	defer queue.Close()

	// Fill the queue to capacity
	for i := 0; i < 2; i++ {
		request := &InferenceRequest{
			ID:       string(rune('A' + i)),
			Priority: RequestPriorityLow,
			Data:     []byte("test data"),
			ModelID:  "test-model",
			ClientID: "test-client",
		}

		err := queue.SubmitRequest(request)
		if err != nil {
			t.Errorf("Failed to submit request %s: %v", request.ID, err)
		}
	}

	// Wait for requests to be processed
	time.Sleep(100 * time.Millisecond)

	// Try to submit one more request to trigger backpressure
	request := &InferenceRequest{
		ID:       "C",
		Priority: RequestPriorityNormal,
		Data:     []byte("test data"),
		ModelID:  "test-model",
		ClientID: "test-client",
	}

	err = queue.SubmitRequest(request)
	// Should succeed due to backpressure handling (dropping lowest priority)
	if err != nil {
		t.Errorf("Expected backpressure to handle queue overflow, got error: %v", err)
	}
}

func TestConcurrentOperations(t *testing.T) {
	batchOptimizer, err := NewBatchSizeOptimizer(0, DefaultOptimizerConfig())
	if err != nil {
		t.Fatalf("Failed to create batch optimizer: %v", err)
	}
	defer batchOptimizer.Close()

	queue, err := NewInferenceQueue(nil, batchOptimizer)
	if err != nil {
		t.Fatalf("Failed to create inference queue: %v", err)
	}
	defer queue.Close()

	ctx := context.Background()
	var wg sync.WaitGroup
	numGoroutines := 10
	requestsPerGoroutine := 5

	// Concurrent submission
	wg.Add(numGoroutines)
	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < requestsPerGoroutine; j++ {
				request := &InferenceRequest{
					Priority: RequestPriority(j % 4), // Cycle through priorities
					Data:     []byte("test data"),
					ModelID:  "test-model",
					ClientID: "test-client",
				}

				err := queue.SubmitRequest(request)
				if err != nil {
					t.Errorf("Goroutine %d: Failed to submit request %d: %v", goroutineID, j, err)
				}
			}
		}(i)
	}

	// Concurrent batch retrieval
	wg.Add(1)
	go func() {
		defer wg.Done()

		for i := 0; i < 10; i++ {
			_, err := queue.GetNextBatch(ctx)
			if err != nil {
				t.Errorf("Failed to get batch %d: %v", i, err)
			}
			time.Sleep(10 * time.Millisecond)
		}
	}()

	wg.Wait()

	// Verify final state
	metrics := queue.GetMetrics()
	expectedRequests := int64(numGoroutines * requestsPerGoroutine)
	if metrics.TotalRequests != expectedRequests {
		t.Errorf("Expected %d total requests, got %d", expectedRequests, metrics.TotalRequests)
	}
}

func TestQueueMetrics(t *testing.T) {
	batchOptimizer, err := NewBatchSizeOptimizer(0, DefaultOptimizerConfig())
	if err != nil {
		t.Fatalf("Failed to create batch optimizer: %v", err)
	}
	defer batchOptimizer.Close()

	queue, err := NewInferenceQueue(nil, batchOptimizer)
	if err != nil {
		t.Fatalf("Failed to create inference queue: %v", err)
	}
	defer queue.Close()

	// Submit some requests
	priorities := []RequestPriority{RequestPriorityLow, RequestPriorityHigh, RequestPriorityNormal}
	for i, priority := range priorities {
		request := &InferenceRequest{
			ID:       string(rune('A' + i)),
			Priority: priority,
			Data:     []byte("test data"),
			ModelID:  "test-model",
			ClientID: "test-client",
		}

		err := queue.SubmitRequest(request)
		if err != nil {
			t.Errorf("Failed to submit request %s: %v", request.ID, err)
		}
	}

	// Wait for metrics to update
	time.Sleep(100 * time.Millisecond)

	metrics := queue.GetMetrics()

	if metrics.TotalRequests != int64(len(priorities)) {
		t.Errorf("Expected %d total requests, got %d", len(priorities), metrics.TotalRequests)
	}

	if metrics.CurrentQueueSize != len(priorities) {
		t.Errorf("Expected queue size %d, got %d", len(priorities), metrics.CurrentQueueSize)
	}

	// Verify priority metrics
	for _, priority := range priorities {
		priorityMetrics := metrics.PriorityMetrics[priority]
		if priorityMetrics.TotalRequests != 1 {
			t.Errorf("Expected 1 request for priority %s, got %d", priority.String(), priorityMetrics.TotalRequests)
		}
	}
}

func TestQueueStatus(t *testing.T) {
	batchOptimizer, err := NewBatchSizeOptimizer(0, DefaultOptimizerConfig())
	if err != nil {
		t.Fatalf("Failed to create batch optimizer: %v", err)
	}
	defer batchOptimizer.Close()

	queue, err := NewInferenceQueue(nil, batchOptimizer)
	if err != nil {
		t.Fatalf("Failed to create inference queue: %v", err)
	}
	defer queue.Close()

	// Submit requests with different priorities
	priorities := []RequestPriority{RequestPriorityLow, RequestPriorityHigh, RequestPriorityHigh}
	for i, priority := range priorities {
		request := &InferenceRequest{
			ID:       string(rune('A' + i)),
			Priority: priority,
			Data:     []byte("test data"),
			ModelID:  "test-model",
			ClientID: "test-client",
		}

		err := queue.SubmitRequest(request)
		if err != nil {
			t.Errorf("Failed to submit request %s: %v", request.ID, err)
		}
	}

	// Wait for requests to be processed
	time.Sleep(100 * time.Millisecond)

	status := queue.GetQueueStatus()

	totalSize, ok := status["total_queue_size"].(int)
	if !ok || totalSize != len(priorities) {
		t.Errorf("Expected total queue size %d, got %v", len(priorities), status["total_queue_size"])
	}

	breakdown, ok := status["priority_breakdown"].(map[string]int)
	if !ok {
		t.Error("Expected priority breakdown to be map[string]int")
	} else {
		if breakdown["low"] != 1 {
			t.Errorf("Expected 1 low priority request, got %d", breakdown["low"])
		}
		if breakdown["high"] != 2 {
			t.Errorf("Expected 2 high priority requests, got %d", breakdown["high"])
		}
	}
}

func TestCompleteRequest(t *testing.T) {
	batchOptimizer, err := NewBatchSizeOptimizer(0, DefaultOptimizerConfig())
	if err != nil {
		t.Fatalf("Failed to create batch optimizer: %v", err)
	}
	defer batchOptimizer.Close()

	queue, err := NewInferenceQueue(nil, batchOptimizer)
	if err != nil {
		t.Fatalf("Failed to create inference queue: %v", err)
	}
	defer queue.Close()

	// Test callback functionality
	var callbackResult *InferenceResult
	var callbackError error
	var callbackCalled bool

	request := &InferenceRequest{
		ID:       "test-request",
		Priority: RequestPriorityNormal,
		Data:     []byte("test data"),
		ModelID:  "test-model",
		ClientID: "test-client",
		ResultCallback: func(result *InferenceResult, err error) {
			callbackResult = result
			callbackError = err
			callbackCalled = true
		},
	}

	result := &InferenceResult{
		RequestID:    "test-request",
		Data:         []byte("result data"),
		OutputShape:  []int{1, 1000},
		ProcessedAt:  time.Now(),
		ProcessTime:  50 * time.Millisecond,
		QueueTime:    10 * time.Millisecond,
		TotalLatency: 60 * time.Millisecond,
	}

	// Test successful completion
	queue.CompleteRequest(request, result, nil)

	// Wait for callback
	time.Sleep(100 * time.Millisecond)

	if !callbackCalled {
		t.Error("Expected callback to be called")
	}

	if callbackResult != result {
		t.Error("Expected callback to receive the correct result")
	}

	if callbackError != nil {
		t.Errorf("Expected no error in callback, got %v", callbackError)
	}

	// Verify metrics
	metrics := queue.GetMetrics()
	if metrics.ProcessedRequests != 1 {
		t.Errorf("Expected 1 processed request, got %d", metrics.ProcessedRequests)
	}

	if metrics.FailedRequests != 0 {
		t.Errorf("Expected 0 failed requests, got %d", metrics.FailedRequests)
	}
}
