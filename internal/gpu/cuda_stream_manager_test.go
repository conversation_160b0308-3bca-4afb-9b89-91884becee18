//go:build cuda && cuda_cgo
// +build cuda,cuda_cgo

package gpu

import (
	"log"
	"os"
	"sync"
	"testing"
	"time"
)

func TestManagedCUDAStream_Creation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	stream, err := NewManagedCUDAStream("test-stream", 0, StreamPriorityNormal, StreamFlagNonBlocking, logger)
	if err != nil {
		t.Fatalf("Failed to create managed CUDA stream: %v", err)
	}
	defer stream.Destroy()

	if stream.GetID() != "test-stream" {
		t.<PERSON>rf("Expected ID 'test-stream', got %s", stream.GetID())
	}

	if stream.GetDeviceID() != 0 {
		t.<PERSON><PERSON>rf("Expected device ID 0, got %d", stream.GetDeviceID())
	}

	if stream.GetPriority() != StreamPriorityNormal {
		t.<PERSON>rf("Expected priority Normal, got %v", stream.GetPriority())
	}

	if stream.GetState() != StreamStateIdle {
		t.Errorf("Expected state Idle, got %v", stream.GetState())
	}

	if stream.IsInUse() {
		t.Error("Expected stream to not be in use initially")
	}
}

func TestManagedCUDAStream_Usage(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	stream, err := NewManagedCUDAStream("test-stream", 0, StreamPriorityHigh, StreamFlagNonBlocking, logger)
	if err != nil {
		t.Fatalf("Failed to create managed CUDA stream: %v", err)
	}
	defer stream.Destroy()

	// Test setting in use
	stream.SetInUse(true)
	if !stream.IsInUse() {
		t.Error("Expected stream to be in use after SetInUse(true)")
	}
	if stream.GetState() != StreamStateBusy {
		t.Errorf("Expected state Busy after SetInUse(true), got %v", stream.GetState())
	}

	// Test usage stats
	usageCount, totalTime, errorCount := stream.GetUsageStats()
	if usageCount != 1 {
		t.Errorf("Expected usage count 1, got %d", usageCount)
	}
	if totalTime != 0 {
		t.Errorf("Expected total time 0, got %v", totalTime)
	}
	if errorCount != 0 {
		t.Errorf("Expected error count 0, got %d", errorCount)
	}

	// Test releasing
	stream.SetInUse(false)
	if stream.IsInUse() {
		t.Error("Expected stream to not be in use after SetInUse(false)")
	}
	if stream.GetState() != StreamStateIdle {
		t.Errorf("Expected state Idle after SetInUse(false), got %v", stream.GetState())
	}

	// Test error handling
	stream.IncrementErrorCount()
	_, _, errorCount = stream.GetUsageStats()
	if errorCount != 1 {
		t.Errorf("Expected error count 1 after increment, got %d", errorCount)
	}
	if stream.GetState() != StreamStateError {
		t.Errorf("Expected state Error after IncrementErrorCount, got %v", stream.GetState())
	}

	// Test time tracking
	testDuration := 100 * time.Millisecond
	stream.UpdateTotalTime(testDuration)
	_, totalTime, _ = stream.GetUsageStats()
	if totalTime != testDuration {
		t.Errorf("Expected total time %v, got %v", testDuration, totalTime)
	}
}

func TestStreamPool_Creation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	minStreams, maxStreams := 2, 8

	pool := NewStreamPool(0, minStreams, maxStreams, logger)
	defer pool.Cleanup()

	stats := pool.GetPoolStats()
	if stats["device_id"] != 0 {
		t.Errorf("Expected device ID 0, got %v", stats["device_id"])
	}
	if stats["min_streams"] != minStreams {
		t.Errorf("Expected min streams %d, got %v", minStreams, stats["min_streams"])
	}
	if stats["max_streams"] != maxStreams {
		t.Errorf("Expected max streams %d, got %v", maxStreams, stats["max_streams"])
	}
	if stats["available"].(int) != minStreams {
		t.Errorf("Expected %d available streams initially, got %v", minStreams, stats["available"])
	}
}

func TestStreamPool_AcquireRelease(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	minStreams, maxStreams := 2, 4

	pool := NewStreamPool(0, minStreams, maxStreams, logger)
	defer pool.Cleanup()

	// Test acquiring streams
	stream1, err := pool.AcquireStream(StreamPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire first stream: %v", err)
	}
	if !stream1.IsInUse() {
		t.Error("Expected acquired stream to be in use")
	}

	stream2, err := pool.AcquireStream(StreamPriorityHigh)
	if err != nil {
		t.Fatalf("Failed to acquire second stream: %v", err)
	}

	// Pool should have no available streams now (started with 2, both acquired)
	stats := pool.GetPoolStats()
	if stats["available"].(int) != 0 {
		t.Errorf("Expected 0 available streams after acquiring 2, got %v", stats["available"])
	}

	// Should create a new stream since we're under max
	stream3, err := pool.AcquireStream(StreamPriorityLow)
	if err != nil {
		t.Fatalf("Failed to acquire third stream: %v", err)
	}

	// Test releasing streams
	err = pool.ReleaseStream(stream1)
	if err != nil {
		t.Fatalf("Failed to release first stream: %v", err)
	}
	if stream1.IsInUse() {
		t.Error("Expected released stream to not be in use")
	}

	err = pool.ReleaseStream(stream2)
	if err != nil {
		t.Fatalf("Failed to release second stream: %v", err)
	}

	err = pool.ReleaseStream(stream3)
	if err != nil {
		t.Fatalf("Failed to release third stream: %v", err)
	}

	// Should have streams available again
	stats = pool.GetPoolStats()
	if stats["available"].(int) < minStreams {
		t.Errorf("Expected at least %d available streams after releasing, got %v", minStreams, stats["available"])
	}
}

func TestStreamPool_PriorityHandling(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	minStreams, maxStreams := 1, 2

	pool := NewStreamPool(0, minStreams, maxStreams, logger)
	defer pool.Cleanup()

	// Acquire the initial stream
	normalStream, err := pool.AcquireStream(StreamPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire normal priority stream: %v", err)
	}

	// Pool should create a high priority stream
	highStream, err := pool.AcquireStream(StreamPriorityHigh)
	if err != nil {
		t.Fatalf("Failed to acquire high priority stream: %v", err)
	}

	if highStream.GetPriority() != StreamPriorityHigh {
		t.Errorf("Expected high priority stream, got %v", highStream.GetPriority())
	}

	// Release streams
	pool.ReleaseStream(normalStream)
	pool.ReleaseStream(highStream)

	// High priority stream should be kept in available queue
	stats := pool.GetPoolStats()
	priorities := stats["priorities"].(map[StreamPriority]int)
	if priorities[StreamPriorityHigh] == 0 {
		t.Error("Expected high priority stream to be retained in pool")
	}
}

func TestStreamPool_MaxCapacity(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	minStreams, maxStreams := 1, 2

	pool := NewStreamPool(0, minStreams, maxStreams, logger)
	defer pool.Cleanup()

	// Acquire all streams
	stream1, err := pool.AcquireStream(StreamPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire first stream: %v", err)
	}

	stream2, err := pool.AcquireStream(StreamPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire second stream: %v", err)
	}

	// Pool is at capacity, next acquisition should find LRU stream
	stream3, err := pool.AcquireStream(StreamPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire third stream (should reuse LRU): %v", err)
	}

	// Should be able to release all streams
	pool.ReleaseStream(stream1)
	pool.ReleaseStream(stream2)
	pool.ReleaseStream(stream3)

	stats := pool.GetPoolStats()
	if stats["total_streams"].(int) > maxStreams {
		t.Errorf("Pool exceeded max capacity: %v > %d", stats["total_streams"], maxStreams)
	}
}

func TestCUDAStreamManager_Initialization(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	manager := NewCUDAStreamManager(logger)
	defer manager.Cleanup()

	devices := []int{0, 1}
	minStreams, maxStreams := 2, 8

	err := manager.Initialize(devices, minStreams, maxStreams)
	if err != nil {
		t.Fatalf("Failed to initialize stream manager: %v", err)
	}

	stats := manager.GetManagerStats()
	if stats["total_pools"] != len(devices) {
		t.Errorf("Expected %d pools, got %v", len(devices), stats["total_pools"])
	}
	if stats["default_device"] != devices[0] {
		t.Errorf("Expected default device %d, got %v", devices[0], stats["default_device"])
	}
	if !stats["started"].(bool) {
		t.Error("Expected manager to be started")
	}

	// Test double initialization
	err = manager.Initialize(devices, minStreams, maxStreams)
	if err == nil {
		t.Error("Expected error on double initialization")
	}
}

func TestCUDAStreamManager_AcquireRelease(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	manager := NewCUDAStreamManager(logger)
	defer manager.Cleanup()

	devices := []int{0}
	err := manager.Initialize(devices, 2, 4)
	if err != nil {
		t.Fatalf("Failed to initialize stream manager: %v", err)
	}

	// Test acquiring stream for specific device
	stream1, err := manager.AcquireStream(0, StreamPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire stream for device 0: %v", err)
	}
	if stream1.GetDeviceID() != 0 {
		t.Errorf("Expected device ID 0, got %d", stream1.GetDeviceID())
	}

	// Test acquiring default stream
	stream2, err := manager.AcquireDefaultStream(StreamPriorityHigh)
	if err != nil {
		t.Fatalf("Failed to acquire default stream: %v", err)
	}

	// Test acquiring stream for non-existent device
	_, err = manager.AcquireStream(99, StreamPriorityNormal)
	if err == nil {
		t.Error("Expected error when acquiring stream for non-existent device")
	}

	// Test releasing streams
	err = manager.ReleaseStream(stream1)
	if err != nil {
		t.Fatalf("Failed to release stream1: %v", err)
	}

	err = manager.ReleaseStream(stream2)
	if err != nil {
		t.Fatalf("Failed to release stream2: %v", err)
	}

	// Test releasing nil stream
	err = manager.ReleaseStream(nil)
	if err == nil {
		t.Error("Expected error when releasing nil stream")
	}
}

func TestCUDAStreamManager_ConcurrentAccess(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	manager := NewCUDAStreamManager(logger)
	defer manager.Cleanup()

	devices := []int{0}
	err := manager.Initialize(devices, 2, 10)
	if err != nil {
		t.Fatalf("Failed to initialize stream manager: %v", err)
	}

	const numGoroutines = 20
	const operationsPerGoroutine = 10

	var wg sync.WaitGroup
	errors := make(chan error, numGoroutines*operationsPerGoroutine)

	// Launch multiple goroutines to stress test concurrent access
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < operationsPerGoroutine; j++ {
				// Acquire stream
				stream, err := manager.AcquireDefaultStream(StreamPriorityNormal)
				if err != nil {
					errors <- err
					return
				}

				// Simulate some work
				time.Sleep(time.Millisecond)

				// Release stream
				err = manager.ReleaseStream(stream)
				if err != nil {
					errors <- err
					return
				}
			}
		}(i)
	}

	wg.Wait()
	close(errors)

	// Check for errors
	for err := range errors {
		t.Errorf("Concurrent access error: %v", err)
	}

	// Verify final state
	stats := manager.GetManagerStats()
	pools := stats["pools"].(map[string]interface{})
	device0Stats := pools["device_0"].(map[string]interface{})

	if device0Stats["total_streams"].(int) > 10 {
		t.Errorf("Too many streams created during concurrent test: %v", device0Stats["total_streams"])
	}
}

func TestStreamPool_LRUEviction(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	minStreams, maxStreams := 1, 2

	pool := NewStreamPool(0, minStreams, maxStreams, logger)
	defer pool.Cleanup()

	// Acquire and release streams to establish usage order
	stream1, _ := pool.AcquireStream(StreamPriorityNormal)
	time.Sleep(10 * time.Millisecond) // Ensure different timestamps

	stream2, _ := pool.AcquireStream(StreamPriorityNormal)
	time.Sleep(10 * time.Millisecond)

	// Release in reverse order to test LRU
	pool.ReleaseStream(stream2) // More recently used
	pool.ReleaseStream(stream1) // Less recently used

	// At capacity, should get the least recently used stream
	stream3, err := pool.AcquireStream(StreamPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire stream for LRU test: %v", err)
	}

	// The returned stream should be the LRU one (stream1's ID should be reused)
	// Note: This is a simplified test; in practice, we'd need to track stream IDs
	if !stream3.IsInUse() {
		t.Error("Expected LRU stream to be marked as in use")
	}

	pool.ReleaseStream(stream3)
}

func TestStreamPool_ErrorHandling(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	pool := NewStreamPool(0, 1, 2, logger)
	defer pool.Cleanup()

	// Test releasing nil stream
	err := pool.ReleaseStream(nil)
	if err == nil {
		t.Error("Expected error when releasing nil stream")
	}

	// Test normal operation after error
	stream, err := pool.AcquireStream(StreamPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire stream after error test: %v", err)
	}

	err = pool.ReleaseStream(stream)
	if err != nil {
		t.Fatalf("Failed to release stream: %v", err)
	}
}

func TestManagedCUDAStream_ThreadSafety(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	stream, err := NewManagedCUDAStream("thread-test", 0, StreamPriorityNormal, StreamFlagNonBlocking, logger)
	if err != nil {
		t.Fatalf("Failed to create managed CUDA stream: %v", err)
	}
	defer stream.Destroy()

	const numGoroutines = 10
	const operationsPerGoroutine = 100

	var wg sync.WaitGroup

	// Test concurrent access to stream metadata
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < operationsPerGoroutine; j++ {
				// These operations should be thread-safe
				_ = stream.GetID()
				_ = stream.GetDeviceID()
				_ = stream.GetPriority()
				_ = stream.GetState()
				_, _, _ = stream.GetUsageStats()

				// Simulate some work
				stream.UpdateTotalTime(time.Microsecond)

				if j%10 == 0 {
					stream.IncrementErrorCount()
				}
			}
		}()
	}

	wg.Wait()

	// Verify final state is consistent
	usageCount, totalTime, errorCount := stream.GetUsageStats()

	if totalTime == 0 {
		t.Error("Expected non-zero total time after concurrent updates")
	}

	expectedErrors := int64(numGoroutines * operationsPerGoroutine / 10)
	if errorCount != expectedErrors {
		t.Errorf("Expected %d errors, got %d", expectedErrors, errorCount)
	}

	// usageCount should still be 0 since we never called SetInUse(true)
	if usageCount != 0 {
		t.Errorf("Expected usage count 0, got %d", usageCount)
	}
}
