//go:build windows

package gpu

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"net/http"
	"sync"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/keepalive"

	clusterpb "neuralmetergo/internal/gpu/proto"
)

// CommunicationConfig holds configuration for cluster communication
type CommunicationConfig struct {
	// gRPC Configuration
	GRPCPort              int           `json:"grpc_port"`
	GRPCMaxMessageSize    int           `json:"grpc_max_message_size"`
	GRPCKeepaliveTime     time.Duration `json:"grpc_keepalive_time"`
	GRPCKeepaliveTimeout  time.Duration `json:"grpc_keepalive_timeout"`
	GRPCConnectionTimeout time.Duration `json:"grpc_connection_timeout"`

	// HTTP Configuration (Windows alternative to ZMQ)
	HTTPPubPort   int    `json:"http_pub_port"`
	HTTPSubPort   int    `json:"http_sub_port"`
	HTTPReqPort   int    `json:"http_req_port"`
	HTTPRepPort   int    `json:"http_rep_port"`
	HTTPTransport string `json:"http_transport"` // http, https

	// Security Configuration
	TLSEnabled bool   `json:"tls_enabled"`
	CertFile   string `json:"cert_file"`
	KeyFile    string `json:"key_file"`
	CAFile     string `json:"ca_file"`
	ServerName string `json:"server_name"`

	// Performance Configuration
	MaxConcurrentStreams uint32        `json:"max_concurrent_streams"`
	BufferSize           int           `json:"buffer_size"`
	RetryAttempts        int           `json:"retry_attempts"`
	RetryDelay           time.Duration `json:"retry_delay"`

	// Node Information
	NodeID   string `json:"node_id"`
	NodeAddr string `json:"node_addr"`
}

// DefaultCommunicationConfig returns default configuration
func DefaultCommunicationConfig() *CommunicationConfig {
	return &CommunicationConfig{
		GRPCPort:              50051,
		GRPCMaxMessageSize:    4 * 1024 * 1024, // 4MB
		GRPCKeepaliveTime:     30 * time.Second,
		GRPCKeepaliveTimeout:  5 * time.Second,
		GRPCConnectionTimeout: 10 * time.Second,
		HTTPPubPort:           5555,
		HTTPSubPort:           5556,
		HTTPReqPort:           5557,
		HTTPRepPort:           5558,
		HTTPTransport:         "http",
		TLSEnabled:            false,
		MaxConcurrentStreams:  100,
		BufferSize:            1000,
		RetryAttempts:         3,
		RetryDelay:            time.Second,
		NodeID:                "node-1",
		NodeAddr:              "localhost",
	}
}

// ClusterCommunicationManager manages cluster communication for Windows
type ClusterCommunicationManager struct {
	config *CommunicationConfig
	logger *log.Logger

	// gRPC components
	grpcServer   *grpc.Server
	grpcClients  map[string]clusterpb.GPUClusterServiceClient
	clientsMutex sync.RWMutex

	// HTTP components (Windows alternative to ZMQ)
	httpServers  []*http.Server
	httpClients  map[string]*http.Client
	subscribers  map[string]chan *HTTPMessage
	subMutex     sync.RWMutex

	// Communication channels
	messageHandlers map[string]MessageHandler
	handlersMutex   sync.RWMutex

	// State management
	isRunning    bool
	shutdownChan chan struct{}
	wg           sync.WaitGroup

	// Service implementation
	serviceImpl *ClusterServiceImpl
}

// MessageHandler defines the interface for handling different message types
type MessageHandler interface {
	HandleMessage(ctx context.Context, message *ZMQMessage) error
}

// ZMQMessage represents a message structure (kept for compatibility)
type ZMQMessage struct {
	Type      string                 `json:"type"`
	Source    string                 `json:"source"`
	Target    string                 `json:"target"`
	Timestamp time.Time              `json:"timestamp"`
	Data      map[string]interface{} `json:"data"`
}

// HTTPMessage represents an HTTP-based message for Windows
type HTTPMessage struct {
	Topic   string     `json:"topic"`
	Message *ZMQMessage `json:"message"`
}

// NewClusterCommunicationManager creates a new communication manager
func NewClusterCommunicationManager(config *CommunicationConfig, logger *log.Logger) (*ClusterCommunicationManager, error) {
	if config == nil {
		config = DefaultCommunicationConfig()
	}

	if logger == nil {
		logger = log.New(log.Writer(), "[ClusterComm] ", log.LstdFlags)
	}

	ccm := &ClusterCommunicationManager{
		config:          config,
		logger:          logger,
		grpcClients:     make(map[string]clusterpb.GPUClusterServiceClient),
		httpClients:     make(map[string]*http.Client),
		subscribers:     make(map[string]chan *HTTPMessage),
		messageHandlers: make(map[string]MessageHandler),
		shutdownChan:    make(chan struct{}),
		serviceImpl:     NewClusterServiceImpl(),
	}

	return ccm, nil
}

// Start initializes and starts the communication manager
func (ccm *ClusterCommunicationManager) Start(ctx context.Context) error {
	ccm.logger.Printf("Starting Cluster Communication Manager (Windows)")

	// Start gRPC server
	if err := ccm.startGRPCServer(); err != nil {
		return fmt.Errorf("failed to start gRPC server: %w", err)
	}

	// Start HTTP-based messaging (Windows alternative to ZMQ)
	if err := ccm.startHTTPMessaging(); err != nil {
		return fmt.Errorf("failed to start HTTP messaging: %w", err)
	}

	ccm.isRunning = true
	ccm.logger.Printf("Cluster Communication Manager started successfully")
	return nil
}

// Stop gracefully stops the communication manager
func (ccm *ClusterCommunicationManager) Stop() error {
	ccm.logger.Printf("Stopping Cluster Communication Manager")
	ccm.isRunning = false

	// Signal shutdown
	close(ccm.shutdownChan)

	// Stop gRPC server
	if ccm.grpcServer != nil {
		ccm.grpcServer.GracefulStop()
	}

	// Stop HTTP servers
	for _, server := range ccm.httpServers {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		server.Shutdown(ctx)
		cancel()
	}

	// Wait for all goroutines to finish
	ccm.wg.Wait()

	ccm.logger.Printf("Cluster Communication Manager stopped")
	return nil
}

// startGRPCServer initializes and starts the gRPC server
func (ccm *ClusterCommunicationManager) startGRPCServer() error {
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", ccm.config.GRPCPort))
	if err != nil {
		return fmt.Errorf("failed to listen on port %d: %w", ccm.config.GRPCPort, err)
	}

	// Configure gRPC server options
	opts := []grpc.ServerOption{
		grpc.MaxRecvMsgSize(ccm.config.GRPCMaxMessageSize),
		grpc.MaxSendMsgSize(ccm.config.GRPCMaxMessageSize),
		grpc.MaxConcurrentStreams(ccm.config.MaxConcurrentStreams),
		grpc.KeepaliveParams(keepalive.ServerParameters{
			Time:    ccm.config.GRPCKeepaliveTime,
			Timeout: ccm.config.GRPCKeepaliveTimeout,
		}),
	}

	// Add TLS if enabled
	if ccm.config.TLSEnabled {
		creds, err := ccm.createTLSCredentials()
		if err != nil {
			return fmt.Errorf("failed to create TLS credentials: %w", err)
		}
		opts = append(opts, grpc.Creds(creds))
	}

	ccm.grpcServer = grpc.NewServer(opts...)

	// Register service
	clusterpb.RegisterGPUClusterServiceServer(ccm.grpcServer, ccm.serviceImpl)

	// Start server in a goroutine
	ccm.wg.Add(1)
	go func() {
		defer ccm.wg.Done()
		ccm.logger.Printf("gRPC server listening on port %d", ccm.config.GRPCPort)
		if err := ccm.grpcServer.Serve(lis); err != nil {
			ccm.logger.Printf("gRPC server error: %v", err)
		}
	}()

	return nil
}

// startHTTPMessaging initializes HTTP-based messaging (Windows alternative to ZMQ)
func (ccm *ClusterCommunicationManager) startHTTPMessaging() error {
	// Create HTTP server for publishing
	pubMux := http.NewServeMux()
	pubMux.HandleFunc("/publish", ccm.handleHTTPPublish)
	
	pubServer := &http.Server{
		Addr:    fmt.Sprintf(":%d", ccm.config.HTTPPubPort),
		Handler: pubMux,
	}
	ccm.httpServers = append(ccm.httpServers, pubServer)

	// Create HTTP server for replies
	repMux := http.NewServeMux()
	repMux.HandleFunc("/request", ccm.handleHTTPRequest)
	
	repServer := &http.Server{
		Addr:    fmt.Sprintf(":%d", ccm.config.HTTPRepPort),
		Handler: repMux,
	}
	ccm.httpServers = append(ccm.httpServers, repServer)

	// Start servers
	ccm.wg.Add(2)
	go func() {
		defer ccm.wg.Done()
		ccm.logger.Printf("HTTP publisher listening on port %d", ccm.config.HTTPPubPort)
		if err := pubServer.ListenAndServe(); err != http.ErrServerClosed {
			ccm.logger.Printf("HTTP publisher error: %v", err)
		}
	}()

	go func() {
		defer ccm.wg.Done()
		ccm.logger.Printf("HTTP reply server listening on port %d", ccm.config.HTTPRepPort)
		if err := repServer.ListenAndServe(); err != http.ErrServerClosed {
			ccm.logger.Printf("HTTP reply server error: %v", err)
		}
	}()

	ccm.logger.Printf("HTTP messaging started - Pub: %d, Rep: %d",
		ccm.config.HTTPPubPort, ccm.config.HTTPRepPort)

	return nil
}

// createTLSCredentials creates TLS credentials for gRPC
func (ccm *ClusterCommunicationManager) createTLSCredentials() (credentials.TransportCredentials, error) {
	if ccm.config.CertFile == "" || ccm.config.KeyFile == "" {
		// Use insecure credentials for development
		return credentials.NewTLS(&tls.Config{InsecureSkipVerify: true}), nil
	}

	cert, err := tls.LoadX509KeyPair(ccm.config.CertFile, ccm.config.KeyFile)
	if err != nil {
		return nil, fmt.Errorf("failed to load key pair: %w", err)
	}

	tlsConfig := &tls.Config{
		Certificates: []tls.Certificate{cert},
		ServerName:   ccm.config.ServerName,
	}

	return credentials.NewTLS(tlsConfig), nil
}

// GetGRPCClient returns or creates a gRPC client for the specified node
func (ccm *ClusterCommunicationManager) GetGRPCClient(nodeAddr string) (clusterpb.GPUClusterServiceClient, error) {
	ccm.clientsMutex.RLock()
	if client, exists := ccm.grpcClients[nodeAddr]; exists {
		ccm.clientsMutex.RUnlock()
		return client, nil
	}
	ccm.clientsMutex.RUnlock()

	ccm.clientsMutex.Lock()
	defer ccm.clientsMutex.Unlock()

	// Double-check after acquiring write lock
	if client, exists := ccm.grpcClients[nodeAddr]; exists {
		return client, nil
	}

	// Create new client connection
	opts := []grpc.DialOption{
		grpc.WithBlock(),
		grpc.WithTimeout(ccm.config.GRPCConnectionTimeout),
		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:                ccm.config.GRPCKeepaliveTime,
			Timeout:             ccm.config.GRPCKeepaliveTimeout,
			PermitWithoutStream: true,
		}),
	}

	if ccm.config.TLSEnabled {
		creds, err := ccm.createTLSCredentials()
		if err != nil {
			return nil, fmt.Errorf("failed to create TLS credentials: %w", err)
		}
		opts = append(opts, grpc.WithTransportCredentials(creds))
	} else {
		opts = append(opts, grpc.WithInsecure())
	}

	conn, err := grpc.Dial(nodeAddr, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %w", nodeAddr, err)
	}

	client := clusterpb.NewGPUClusterServiceClient(conn)
	ccm.grpcClients[nodeAddr] = client

	ccm.logger.Printf("Created gRPC client for node %s", nodeAddr)
	return client, nil
}

// PublishMessage publishes a message via HTTP (Windows alternative to ZMQ)
func (ccm *ClusterCommunicationManager) PublishMessage(topic string, message *ZMQMessage) error {
	message.Source = ccm.config.NodeID
	message.Timestamp = time.Now()

	httpMsg := &HTTPMessage{
		Topic:   topic,
		Message: message,
	}

	data, err := json.Marshal(httpMsg)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	// Send to all subscribers
	ccm.subMutex.RLock()
	for _, ch := range ccm.subscribers {
		select {
		case ch <- httpMsg:
		default:
			// Channel full, skip
		}
	}
	ccm.subMutex.RUnlock()

	ccm.logger.Printf("Published message to topic %s: %s", topic, string(data))
	return nil
}

// SubscribeToNode connects to a node's publisher (HTTP-based)
func (ccm *ClusterCommunicationManager) SubscribeToNode(nodeAddr string) error {
	subID := fmt.Sprintf("%s:%d", nodeAddr, ccm.config.HTTPSubPort)
	
	ccm.subMutex.Lock()
	if _, exists := ccm.subscribers[subID]; exists {
		ccm.subMutex.Unlock()
		return nil // Already subscribed
	}
	
	ch := make(chan *HTTPMessage, ccm.config.BufferSize)
	ccm.subscribers[subID] = ch
	ccm.subMutex.Unlock()

	// Start subscription goroutine
	ccm.wg.Add(1)
	go func() {
		defer ccm.wg.Done()
		ccm.processHTTPSubscription(nodeAddr, ch)
	}()

	ccm.logger.Printf("Subscribed to node %s", nodeAddr)
	return nil
}

// RegisterMessageHandler registers a handler for a specific message type
func (ccm *ClusterCommunicationManager) RegisterMessageHandler(messageType string, handler MessageHandler) {
	ccm.handlersMutex.Lock()
	defer ccm.handlersMutex.Unlock()
	ccm.messageHandlers[messageType] = handler
	ccm.logger.Printf("Registered message handler for type: %s", messageType)
}

// handleHTTPPublish handles HTTP publish requests
func (ccm *ClusterCommunicationManager) handleHTTPPublish(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var httpMsg HTTPMessage
	if err := json.NewDecoder(r.Body).Decode(&httpMsg); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Process the message
	ccm.handleHTTPMessage(&httpMsg)
	
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"status": "received"})
}

// handleHTTPRequest handles HTTP request/reply pattern
func (ccm *ClusterCommunicationManager) handleHTTPRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var httpMsg HTTPMessage
	if err := json.NewDecoder(r.Body).Decode(&httpMsg); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Process request and generate reply
	reply := ccm.handleHTTPRequestMessage(&httpMsg)
	
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(reply)
}

// processHTTPSubscription processes messages from a subscription
func (ccm *ClusterCommunicationManager) processHTTPSubscription(nodeAddr string, ch chan *HTTPMessage) {
	for {
		select {
		case msg := <-ch:
			ccm.handleHTTPMessage(msg)
		case <-ccm.shutdownChan:
			return
		}
	}
}

// handleHTTPMessage processes an HTTP message
func (ccm *ClusterCommunicationManager) handleHTTPMessage(httpMsg *HTTPMessage) {
	ccm.handlersMutex.RLock()
	handler, exists := ccm.messageHandlers[httpMsg.Message.Type]
	ccm.handlersMutex.RUnlock()

	if exists {
		ctx := context.Background()
		if err := handler.HandleMessage(ctx, httpMsg.Message); err != nil {
			ccm.logger.Printf("Error handling message type %s: %v", httpMsg.Message.Type, err)
		}
	} else {
		ccm.logger.Printf("No handler for message type: %s", httpMsg.Message.Type)
	}
}

// handleHTTPRequestMessage processes a request message and returns a reply
func (ccm *ClusterCommunicationManager) handleHTTPRequestMessage(httpMsg *HTTPMessage) *HTTPMessage {
	// Default reply
	reply := &HTTPMessage{
		Topic: "reply",
		Message: &ZMQMessage{
			Type:      "reply",
			Source:    ccm.config.NodeID,
			Target:    httpMsg.Message.Source,
			Timestamp: time.Now(),
			Data:      map[string]interface{}{"status": "processed"},
		},
	}

	// Process with handler if available
	ccm.handlersMutex.RLock()
	handler, exists := ccm.messageHandlers[httpMsg.Message.Type]
	ccm.handlersMutex.RUnlock()

	if exists {
		ctx := context.Background()
		if err := handler.HandleMessage(ctx, httpMsg.Message); err != nil {
			reply.Message.Data = map[string]interface{}{
				"status": "error",
				"error":  err.Error(),
			}
		}
	}

	return reply
}

// GetConfig returns the communication configuration
func (ccm *ClusterCommunicationManager) GetConfig() *CommunicationConfig {
	return ccm.config
}

// IsRunning returns whether the manager is currently running
func (ccm *ClusterCommunicationManager) IsRunning() bool {
	return ccm.isRunning
}

// GetNodeID returns the node ID
func (ccm *ClusterCommunicationManager) GetNodeID() string {
	return ccm.config.NodeID
} 