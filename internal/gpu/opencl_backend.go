//go:build opencl

package gpu

/*
#cgo CFLAGS: -I/usr/include -I/opt/intel/opencl/include -I/usr/local/cuda/include
#cgo LDFLAGS: -lOpenCL

#ifdef __APPLE__
#include <OpenCL/opencl.h>
#else
#include <CL/cl.h>
#endif

#include <stdlib.h>
#include <string.h>

// Helper function to get platform count
cl_uint getOpenCLPlatformCount() {
    cl_uint platformCount = 0;
    clGetPlatformIDs(0, NULL, &platformCount);
    return platformCount;
}

// Helper function to get platforms
int getOpenCLPlatforms(cl_platform_id* platforms, cl_uint maxPlatforms) {
    cl_uint platformCount = 0;
    cl_int err = clGetPlatformIDs(maxPlatforms, platforms, &platformCount);
    if (err != CL_SUCCESS) {
        return -1;
    }
    return (int)platformCount;
}

// Helper function to get device count for a platform
cl_uint getOpenCLDeviceCount(cl_platform_id platform) {
    cl_uint deviceCount = 0;
    clGetDeviceIDs(platform, CL_DEVICE_TYPE_GPU, 0, NULL, &deviceCount);
    return deviceCount;
}

// Helper function to get devices for a platform
int getOpenCLDevices(cl_platform_id platform, cl_device_id* devices, cl_uint maxDevices) {
    cl_uint deviceCount = 0;
    cl_int err = clGetDeviceIDs(platform, CL_DEVICE_TYPE_GPU, maxDevices, devices, &deviceCount);
    if (err != CL_SUCCESS) {
        return -1;
    }
    return (int)deviceCount;
}

// Helper function to get device info
typedef struct {
    char name[256];
    char vendor[128];
    char version[64];
    char driver_version[64];
    cl_ulong global_mem_size;
    cl_ulong local_mem_size;
    cl_ulong max_mem_alloc_size;
    cl_uint max_compute_units;
    cl_uint max_clock_frequency;
    size_t max_work_group_size;
    cl_uint max_work_item_dimensions;
    size_t max_work_item_sizes[3];
    cl_bool image_support;
    cl_device_type device_type;
} OpenCLDeviceInfo;

int getOpenCLDeviceInfo(cl_device_id device, OpenCLDeviceInfo* info) {
    cl_int err;

    // Get device name
    err = clGetDeviceInfo(device, CL_DEVICE_NAME, sizeof(info->name), info->name, NULL);
    if (err != CL_SUCCESS) return -1;

    // Get vendor
    err = clGetDeviceInfo(device, CL_DEVICE_VENDOR, sizeof(info->vendor), info->vendor, NULL);
    if (err != CL_SUCCESS) return -1;

    // Get OpenCL version
    err = clGetDeviceInfo(device, CL_DEVICE_VERSION, sizeof(info->version), info->version, NULL);
    if (err != CL_SUCCESS) return -1;

    // Get driver version
    err = clGetDeviceInfo(device, CL_DRIVER_VERSION, sizeof(info->driver_version), info->driver_version, NULL);
    if (err != CL_SUCCESS) return -1;

    // Get memory info
    err = clGetDeviceInfo(device, CL_DEVICE_GLOBAL_MEM_SIZE, sizeof(info->global_mem_size), &info->global_mem_size, NULL);
    if (err != CL_SUCCESS) return -1;

    err = clGetDeviceInfo(device, CL_DEVICE_LOCAL_MEM_SIZE, sizeof(info->local_mem_size), &info->local_mem_size, NULL);
    if (err != CL_SUCCESS) return -1;

    err = clGetDeviceInfo(device, CL_DEVICE_MAX_MEM_ALLOC_SIZE, sizeof(info->max_mem_alloc_size), &info->max_mem_alloc_size, NULL);
    if (err != CL_SUCCESS) return -1;

    // Get compute info
    err = clGetDeviceInfo(device, CL_DEVICE_MAX_COMPUTE_UNITS, sizeof(info->max_compute_units), &info->max_compute_units, NULL);
    if (err != CL_SUCCESS) return -1;

    err = clGetDeviceInfo(device, CL_DEVICE_MAX_CLOCK_FREQUENCY, sizeof(info->max_clock_frequency), &info->max_clock_frequency, NULL);
    if (err != CL_SUCCESS) return -1;

    err = clGetDeviceInfo(device, CL_DEVICE_MAX_WORK_GROUP_SIZE, sizeof(info->max_work_group_size), &info->max_work_group_size, NULL);
    if (err != CL_SUCCESS) return -1;

    err = clGetDeviceInfo(device, CL_DEVICE_MAX_WORK_ITEM_DIMENSIONS, sizeof(info->max_work_item_dimensions), &info->max_work_item_dimensions, NULL);
    if (err != CL_SUCCESS) return -1;

    err = clGetDeviceInfo(device, CL_DEVICE_MAX_WORK_ITEM_SIZES, sizeof(info->max_work_item_sizes), info->max_work_item_sizes, NULL);
    if (err != CL_SUCCESS) return -1;

    err = clGetDeviceInfo(device, CL_DEVICE_IMAGE_SUPPORT, sizeof(info->image_support), &info->image_support, NULL);
    if (err != CL_SUCCESS) return -1;

    err = clGetDeviceInfo(device, CL_DEVICE_TYPE, sizeof(info->device_type), &info->device_type, NULL);
    if (err != CL_SUCCESS) return -1;

    return 0;
}

// Helper functions for OpenCL operations
cl_context createOpenCLContext(cl_device_id device) {
    cl_int err;
    cl_context context = clCreateContext(NULL, 1, &device, NULL, NULL, &err);
    if (err != CL_SUCCESS) {
        return NULL;
    }
    return context;
}

cl_command_queue createOpenCLCommandQueue(cl_context context, cl_device_id device) {
    cl_int err;
    cl_command_queue queue = clCreateCommandQueue(context, device, 0, &err);
    if (err != CL_SUCCESS) {
        return NULL;
    }
    return queue;
}

cl_mem allocateOpenCLMemory(cl_context context, size_t size, cl_mem_flags flags) {
    cl_int err;
    cl_mem buffer = clCreateBuffer(context, flags, size, NULL, &err);
    if (err != CL_SUCCESS) {
        return NULL;
    }
    return buffer;
}

int copyOpenCLMemory(cl_command_queue queue, cl_mem dst, cl_mem src, size_t size) {
    cl_int err = clEnqueueCopyBuffer(queue, src, dst, 0, 0, size, 0, NULL, NULL);
    return (err == CL_SUCCESS) ? 0 : -1;
}

cl_program createOpenCLProgram(cl_context context, const char* source) {
    cl_int err;
    cl_program program = clCreateProgramWithSource(context, 1, &source, NULL, &err);
    if (err != CL_SUCCESS) {
        return NULL;
    }

    err = clBuildProgram(program, 0, NULL, NULL, NULL, NULL);
    if (err != CL_SUCCESS) {
        clReleaseProgram(program);
        return NULL;
    }

    return program;
}

cl_kernel createOpenCLKernel(cl_program program, const char* kernel_name) {
    cl_int err;
    cl_kernel kernel = clCreateKernel(program, kernel_name, &err);
    if (err != CL_SUCCESS) {
        return NULL;
    }
    return kernel;
}

int executeOpenCLKernel(cl_command_queue queue, cl_kernel kernel,
                       size_t global_work_size[3], size_t local_work_size[3], cl_uint work_dim) {
    cl_int err = clEnqueueNDRangeKernel(queue, kernel, work_dim, NULL,
                                       global_work_size, local_work_size, 0, NULL, NULL);
    return (err == CL_SUCCESS) ? 0 : -1;
}

int synchronizeOpenCL(cl_command_queue queue) {
    cl_int err = clFinish(queue);
    return (err == CL_SUCCESS) ? 0 : -1;
}
*/
import "C"

import (
	"fmt"
	"runtime"
	"sync"
	"unsafe"
)

// OpenCLBackend implements real cross-platform GPU functionality using OpenCL
type OpenCLBackend struct {
	platformID   C.cl_platform_id
	deviceID     C.cl_device_id
	context      C.cl_context
	commandQueue C.cl_command_queue
	initialized  bool
	mutex        sync.RWMutex
	memoryPools  map[string]*OpenCLMemoryPool
	kernelCache  map[string]*OpenCLKernel
	deviceProps  *OpenCLDeviceProperties
}

// OpenCLDeviceProperties holds real GPU device properties from OpenCL
type OpenCLDeviceProperties struct {
	Name              string
	Vendor            string
	Version           string
	DriverVersion     string
	GlobalMemSize     uint64
	LocalMemSize      uint64
	MaxMemAllocSize   uint64
	MaxComputeUnits   int
	MaxClockFrequency int
	MaxWorkGroupSize  int
	MaxWorkItemDims   int
	MaxWorkItemSizes  [3]int
	ImageSupport      bool
	DeviceType        string
	Architecture      string
}

// OpenCLMemoryPool manages GPU memory allocation using real OpenCL APIs
type OpenCLMemoryPool struct {
	buffer     C.cl_mem
	size       uint64
	allocated  uint64
	freeBlocks []OpenCLMemoryBlock
	usedBlocks []OpenCLMemoryBlock
	mutex      sync.RWMutex
}

// OpenCLKernel represents a compiled OpenCL kernel
type OpenCLKernel struct {
	name     string
	source   string
	program  C.cl_program
	kernel   C.cl_kernel
	compiled bool
	mutex    sync.RWMutex
}

// OpenCLMemoryBlock represents a memory allocation
type OpenCLMemoryBlock struct {
	offset uint64
	size   uint64
	ptr    unsafe.Pointer
}

// NewOpenCLBackend creates a new OpenCL backend with real OpenCL initialization
func NewOpenCLBackend(platformIndex, deviceIndex int) (*OpenCLBackend, error) {
	backend := &OpenCLBackend{
		memoryPools: make(map[string]*OpenCLMemoryPool),
		kernelCache: make(map[string]*OpenCLKernel),
	}

	if err := backend.initialize(platformIndex, deviceIndex); err != nil {
		return nil, fmt.Errorf("failed to initialize OpenCL backend: %w", err)
	}

	return backend, nil
}

// initialize sets up the OpenCL backend with real OpenCL runtime calls
func (o *OpenCLBackend) initialize(platformIndex, deviceIndex int) error {
	o.mutex.Lock()
	defer o.mutex.Unlock()

	// Get OpenCL platforms
	platformCount := C.getOpenCLPlatformCount()
	if platformCount == 0 {
		return fmt.Errorf("no OpenCL platforms found")
	}

	if platformIndex >= int(platformCount) {
		return fmt.Errorf("platform index %d not available, only %d platforms found", platformIndex, int(platformCount))
	}

	// Get platform IDs
	platforms := make([]C.cl_platform_id, platformCount)
	result := C.getOpenCLPlatforms(&platforms[0], platformCount)
	if result < 0 {
		return fmt.Errorf("failed to get OpenCL platforms")
	}

	o.platformID = platforms[platformIndex]

	// Get devices for the platform
	deviceCount := C.getOpenCLDeviceCount(o.platformID)
	if deviceCount == 0 {
		return fmt.Errorf("no OpenCL GPU devices found on platform %d", platformIndex)
	}

	if deviceIndex >= int(deviceCount) {
		return fmt.Errorf("device index %d not available, only %d devices found", deviceIndex, int(deviceCount))
	}

	// Get device IDs
	devices := make([]C.cl_device_id, deviceCount)
	result = C.getOpenCLDevices(o.platformID, &devices[0], deviceCount)
	if result < 0 {
		return fmt.Errorf("failed to get OpenCL devices")
	}

	o.deviceID = devices[deviceIndex]

	// Create OpenCL context
	o.context = C.createOpenCLContext(o.deviceID)
	if o.context == nil {
		return fmt.Errorf("failed to create OpenCL context")
	}

	// Create command queue
	o.commandQueue = C.createOpenCLCommandQueue(o.context, o.deviceID)
	if o.commandQueue == nil {
		C.clReleaseContext(o.context)
		return fmt.Errorf("failed to create OpenCL command queue")
	}

	// Get device properties
	if err := o.loadDeviceProperties(); err != nil {
		C.clReleaseCommandQueue(o.commandQueue)
		C.clReleaseContext(o.context)
		return fmt.Errorf("failed to load device properties: %w", err)
	}

	o.initialized = true
	return nil
}

// loadDeviceProperties retrieves real GPU device properties from OpenCL
func (o *OpenCLBackend) loadDeviceProperties() error {
	var cInfo C.OpenCLDeviceInfo
	result := C.getOpenCLDeviceInfo(o.deviceID, &cInfo)
	if result != 0 {
		return fmt.Errorf("failed to get OpenCL device info")
	}

	deviceType := "UNKNOWN"
	switch cInfo.device_type {
	case C.CL_DEVICE_TYPE_CPU:
		deviceType = "CPU"
	case C.CL_DEVICE_TYPE_GPU:
		deviceType = "GPU"
	case C.CL_DEVICE_TYPE_ACCELERATOR:
		deviceType = "ACCELERATOR"
	}

	o.deviceProps = &OpenCLDeviceProperties{
		Name:              C.GoString(&cInfo.name[0]),
		Vendor:            C.GoString(&cInfo.vendor[0]),
		Version:           C.GoString(&cInfo.version[0]),
		DriverVersion:     C.GoString(&cInfo.driver_version[0]),
		GlobalMemSize:     uint64(cInfo.global_mem_size),
		LocalMemSize:      uint64(cInfo.local_mem_size),
		MaxMemAllocSize:   uint64(cInfo.max_mem_alloc_size),
		MaxComputeUnits:   int(cInfo.max_compute_units),
		MaxClockFrequency: int(cInfo.max_clock_frequency),
		MaxWorkGroupSize:  int(cInfo.max_work_group_size),
		MaxWorkItemDims:   int(cInfo.max_work_item_dimensions),
		MaxWorkItemSizes: [3]int{
			int(cInfo.max_work_item_sizes[0]),
			int(cInfo.max_work_item_sizes[1]),
			int(cInfo.max_work_item_sizes[2]),
		},
		ImageSupport: bool(cInfo.image_support != 0),
		DeviceType:   deviceType,
		Architecture: o.detectArchitecture(C.GoString(&cInfo.vendor[0]), C.GoString(&cInfo.name[0])),
	}

	return nil
}

// detectArchitecture determines GPU architecture from vendor and device name
func (o *OpenCLBackend) detectArchitecture(vendor, name string) string {
	switch vendor {
	case "NVIDIA Corporation":
		if openclContains(name, "RTX 30") || openclContains(name, "RTX 3") {
			return "Ampere"
		} else if openclContains(name, "RTX 20") || openclContains(name, "RTX 2") {
			return "Turing"
		} else if openclContains(name, "GTX 16") || openclContains(name, "GTX 1") {
			return "Pascal"
		}
		return "NVIDIA"
	case "Advanced Micro Devices, Inc.", "AMD":
		if openclContains(name, "RX 7") {
			return "RDNA3"
		} else if openclContains(name, "RX 6") {
			return "RDNA2"
		} else if openclContains(name, "RX 5") {
			return "RDNA"
		}
		return "AMD"
	case "Intel(R) Corporation", "Intel":
		if openclContains(name, "Arc") {
			return "Xe-HPG"
		} else if openclContains(name, "Iris Xe") {
			return "Xe-LP"
		}
		return "Intel"
	default:
		return "Unknown"
	}
}

// openclContains checks if a string contains a substring (case insensitive)
func openclContains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(s) > len(substr) &&
			(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
				indexOf(s, substr) >= 0)))
}

// indexOf finds the index of substr in s
func indexOf(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// AllocateMemory allocates GPU memory using real OpenCL APIs
func (o *OpenCLBackend) AllocateMemory(size uint64, memoryType string) (unsafe.Pointer, error) {
	if !o.initialized {
		return nil, fmt.Errorf("backend not initialized")
	}

	var flags C.cl_mem_flags
	switch memoryType {
	case "read_only":
		flags = C.CL_MEM_READ_ONLY
	case "write_only":
		flags = C.CL_MEM_WRITE_ONLY
	case "read_write":
		flags = C.CL_MEM_READ_WRITE
	default:
		flags = C.CL_MEM_READ_WRITE
	}

	buffer := C.allocateOpenCLMemory(o.context, C.size_t(size), flags)
	if buffer == nil {
		return nil, fmt.Errorf("OpenCL memory allocation failed")
	}

	return unsafe.Pointer(buffer), nil
}

// FreeMemory releases GPU memory using real OpenCL APIs
func (o *OpenCLBackend) FreeMemory(ptr unsafe.Pointer, memoryType string) error {
	if !o.initialized {
		return fmt.Errorf("backend not initialized")
	}

	buffer := C.cl_mem(ptr)
	err := C.clReleaseMemObject(buffer)
	if err != C.CL_SUCCESS {
		return fmt.Errorf("OpenCL memory free failed: %d", int(err))
	}

	return nil
}

// CopyMemory transfers data using real OpenCL APIs
func (o *OpenCLBackend) CopyMemory(dst, src unsafe.Pointer, size uint64, direction string) error {
	if !o.initialized {
		return fmt.Errorf("backend not initialized")
	}

	dstBuffer := C.cl_mem(dst)
	srcBuffer := C.cl_mem(src)

	result := C.copyOpenCLMemory(o.commandQueue, dstBuffer, srcBuffer, C.size_t(size))
	if result != 0 {
		return fmt.Errorf("OpenCL memory copy failed: %d", result)
	}

	return nil
}

// CompileKernel compiles OpenCL kernel source code
func (o *OpenCLBackend) CompileKernel(kernelSource, kernelName string) error {
	if !o.initialized {
		return fmt.Errorf("backend not initialized")
	}

	o.mutex.Lock()
	defer o.mutex.Unlock()

	// Check if kernel is already compiled
	if kernel, exists := o.kernelCache[kernelName]; exists && kernel.compiled {
		return nil
	}

	// Compile OpenCL program
	cKernelSource := C.CString(kernelSource)
	defer C.free(unsafe.Pointer(cKernelSource))

	program := C.createOpenCLProgram(o.context, cKernelSource)
	if program == nil {
		return fmt.Errorf("failed to compile OpenCL program: %s", kernelName)
	}

	// Create kernel
	cKernelName := C.CString(kernelName)
	defer C.free(unsafe.Pointer(cKernelName))

	kernelObj := C.createOpenCLKernel(program, cKernelName)
	if kernelObj == nil {
		C.clReleaseProgram(program)
		return fmt.Errorf("failed to create OpenCL kernel: %s", kernelName)
	}

	kernel := &OpenCLKernel{
		name:     kernelName,
		source:   kernelSource,
		program:  program,
		kernel:   kernelObj,
		compiled: true,
	}

	o.kernelCache[kernelName] = kernel
	return nil
}

// ExecuteKernel launches a compiled kernel using real OpenCL APIs
func (o *OpenCLBackend) ExecuteKernel(kernelName string, args []unsafe.Pointer, gridSize, blockSize [3]int) error {
	if !o.initialized {
		return fmt.Errorf("backend not initialized")
	}

	o.mutex.RLock()
	kernel, exists := o.kernelCache[kernelName]
	o.mutex.RUnlock()

	if !exists || !kernel.compiled {
		return fmt.Errorf("kernel not found or not compiled: %s", kernelName)
	}

	// Set kernel arguments
	for i, arg := range args {
		err := C.clSetKernelArg(kernel.kernel, C.cl_uint(i), C.size_t(unsafe.Sizeof(arg)), unsafe.Pointer(&arg))
		if err != C.CL_SUCCESS {
			return fmt.Errorf("failed to set kernel argument %d: %d", i, int(err))
		}
	}

	// Prepare work sizes
	globalWorkSize := [3]C.size_t{C.size_t(gridSize[0]), C.size_t(gridSize[1]), C.size_t(gridSize[2])}
	localWorkSize := [3]C.size_t{C.size_t(blockSize[0]), C.size_t(blockSize[1]), C.size_t(blockSize[2])}

	// Determine work dimensions
	workDim := C.cl_uint(3)
	if gridSize[2] == 1 && blockSize[2] == 1 {
		if gridSize[1] == 1 && blockSize[1] == 1 {
			workDim = 1
		} else {
			workDim = 2
		}
	}

	// Execute kernel
	result := C.executeOpenCLKernel(o.commandQueue, kernel.kernel, &globalWorkSize[0], &localWorkSize[0], workDim)
	if result != 0 {
		return fmt.Errorf("OpenCL kernel execution failed: %d", result)
	}

	return nil
}

// Synchronize waits for all operations to complete using real OpenCL APIs
func (o *OpenCLBackend) Synchronize() error {
	if !o.initialized {
		return fmt.Errorf("backend not initialized")
	}

	result := C.synchronizeOpenCL(o.commandQueue)
	if result != 0 {
		return fmt.Errorf("OpenCL synchronization failed: %d", result)
	}

	return nil
}

// GetDeviceProperties returns real GPU device properties
func (o *OpenCLBackend) GetDeviceProperties() interface{} {
	o.mutex.RLock()
	defer o.mutex.RUnlock()

	if o.deviceProps == nil {
		return nil
	}

	return *o.deviceProps
}

// GetMemoryInfo returns current GPU memory usage using real OpenCL APIs
func (o *OpenCLBackend) GetMemoryInfo() (free, total uint64, err error) {
	if !o.initialized {
		return 0, 0, fmt.Errorf("backend not initialized")
	}

	if o.deviceProps != nil {
		total = o.deviceProps.GlobalMemSize
		// OpenCL doesn't provide direct free memory query, estimate 80%
		free = total * 80 / 100
		return free, total, nil
	}

	return 0, 0, fmt.Errorf("device properties not available")
}

// CreateMemoryPool creates a managed memory pool using real OpenCL APIs
func (o *OpenCLBackend) CreateMemoryPool(name string, size uint64) error {
	if !o.initialized {
		return fmt.Errorf("backend not initialized")
	}

	o.mutex.Lock()
	defer o.mutex.Unlock()

	if _, exists := o.memoryPools[name]; exists {
		return fmt.Errorf("memory pool %s already exists", name)
	}

	// Allocate OpenCL buffer for the pool
	buffer := C.allocateOpenCLMemory(o.context, C.size_t(size), C.CL_MEM_READ_WRITE)
	if buffer == nil {
		return fmt.Errorf("failed to allocate OpenCL memory pool")
	}

	pool := &OpenCLMemoryPool{
		buffer:    buffer,
		size:      size,
		allocated: 0,
		freeBlocks: []OpenCLMemoryBlock{
			{offset: 0, size: size, ptr: unsafe.Pointer(buffer)},
		},
		usedBlocks: []OpenCLMemoryBlock{},
	}

	o.memoryPools[name] = pool
	return nil
}

// GetBackendType returns the backend type identifier
func (o *OpenCLBackend) GetBackendType() string {
	return "opencl"
}

// IsInitialized returns whether the backend is properly initialized
func (o *OpenCLBackend) IsInitialized() bool {
	o.mutex.RLock()
	defer o.mutex.RUnlock()
	return o.initialized
}

// Cleanup releases all resources using real OpenCL APIs
func (o *OpenCLBackend) Cleanup() error {
	if !o.initialized {
		return nil
	}

	o.mutex.Lock()
	defer o.mutex.Unlock()

	// Clean up memory pools
	for name, pool := range o.memoryPools {
		if pool.buffer != nil {
			err := C.clReleaseMemObject(pool.buffer)
			if err != C.CL_SUCCESS {
				fmt.Printf("Warning: failed to release memory pool %s: %d\n", name, int(err))
			}
		}
	}
	o.memoryPools = make(map[string]*OpenCLMemoryPool)

	// Clean up compiled kernels
	for name, kernel := range o.kernelCache {
		if kernel.kernel != nil {
			err := C.clReleaseKernel(kernel.kernel)
			if err != C.CL_SUCCESS {
				fmt.Printf("Warning: failed to release kernel %s: %d\n", name, int(err))
			}
		}
		if kernel.program != nil {
			err := C.clReleaseProgram(kernel.program)
			if err != C.CL_SUCCESS {
				fmt.Printf("Warning: failed to release program %s: %d\n", name, int(err))
			}
		}
	}
	o.kernelCache = make(map[string]*OpenCLKernel)

	// Release OpenCL resources
	if o.commandQueue != nil {
		err := C.clReleaseCommandQueue(o.commandQueue)
		if err != C.CL_SUCCESS {
			fmt.Printf("Warning: failed to release command queue: %d\n", int(err))
		}
	}

	if o.context != nil {
		err := C.clReleaseContext(o.context)
		if err != C.CL_SUCCESS {
			fmt.Printf("Warning: failed to release context: %d\n", int(err))
		}
	}

	o.initialized = false
	return nil
}

// SetGCFinalizer sets up automatic cleanup when the backend is garbage collected
func (o *OpenCLBackend) SetGCFinalizer() {
	runtime.SetFinalizer(o, (*OpenCLBackend).Cleanup)
}
