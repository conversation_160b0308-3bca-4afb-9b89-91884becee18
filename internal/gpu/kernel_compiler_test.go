package gpu

import (
	"os"
	"path/filepath"
	"testing"
	"time"
)

func TestKernelCompiler_Creation(t *testing.T) {
	config := DefaultCompilerConfig()
	config.CacheConfig.CacheDir = filepath.Join(os.TempDir(), "test_kernel_cache")
	defer os.RemoveAll(config.CacheConfig.CacheDir)

	compiler, err := NewKernelCompiler(config)
	if err != nil {
		t.Fatalf("Failed to create kernel compiler: %v", err)
	}

	if compiler == nil {
		t.Fatal("Kernel compiler is nil")
	}

	// Check that at least one compiler is available (OpenCL should always be available)
	languages := compiler.GetSupportedLanguages()
	if len(languages) == 0 {
		t.Fatal("No kernel compilers available")
	}

	// Verify OpenCL is available
	found := false
	for _, lang := range languages {
		if lang == KernelOpenCL {
			found = true
			break
		}
	}
	if !found {
		t.Error("OpenCL compiler should be available")
	}
}

func TestKernelCompiler_CompileKernel(t *testing.T) {
	config := DefaultCompilerConfig()
	config.CacheConfig.CacheDir = filepath.Join(os.TempDir(), "test_kernel_cache")
	defer os.RemoveAll(config.CacheConfig.CacheDir)

	compiler, err := NewKernelCompiler(config)
	if err != nil {
		t.Fatalf("Failed to create kernel compiler: %v", err)
	}

	// Test OpenCL kernel compilation
	source := &KernelSource{
		ID:       "test_kernel_1",
		Name:     "TestKernel",
		Language: KernelOpenCL,
		Type:     KernelTypeCompute,
		Source: `__kernel void test_kernel(__global float* input, __global float* output) {
			int gid = get_global_id(0);
			output[gid] = input[gid] * 2.0f;
		}`,
		Headers:    make(map[string]string),
		Macros:     make(map[string]string),
		Parameters: []KernelParameter{},
		Metadata:   make(map[string]interface{}),
		CreatedAt:  time.Now(),
		ModifiedAt: time.Now(),
	}

	options := DefaultCompilationOptions()
	options.OptimizationLevel = OptimizationMedium

	kernel, err := compiler.CompileKernel(source, options)
	if err != nil {
		t.Fatalf("Failed to compile kernel: %v", err)
	}

	if kernel == nil {
		t.Fatal("Compiled kernel is nil")
	}

	if kernel.SourceID != source.ID {
		t.Errorf("Expected source ID %s, got %s", source.ID, kernel.SourceID)
	}

	if len(kernel.Binary) == 0 {
		t.Error("Compiled kernel binary is empty")
	}

	if kernel.CompilationTime <= 0 {
		t.Error("Compilation time should be positive")
	}
}

func TestKernelCompiler_CacheHit(t *testing.T) {
	config := DefaultCompilerConfig()
	config.CacheConfig.CacheDir = filepath.Join(os.TempDir(), "test_kernel_cache")
	defer os.RemoveAll(config.CacheConfig.CacheDir)

	compiler, err := NewKernelCompiler(config)
	if err != nil {
		t.Fatalf("Failed to create kernel compiler: %v", err)
	}

	source := &KernelSource{
		ID:         "test_kernel_cache",
		Name:       "TestCacheKernel",
		Language:   KernelOpenCL,
		Type:       KernelTypeCompute,
		Source:     "__kernel void test() { }",
		Headers:    make(map[string]string),
		Macros:     make(map[string]string),
		Parameters: []KernelParameter{},
		Metadata:   make(map[string]interface{}),
		CreatedAt:  time.Now(),
		ModifiedAt: time.Now(),
	}

	options := DefaultCompilationOptions()

	// First compilation - should be cache miss
	initialMetrics := compiler.GetMetrics()
	kernel1, err := compiler.CompileKernel(source, options)
	if err != nil {
		t.Fatalf("Failed to compile kernel: %v", err)
	}

	metrics1 := compiler.GetMetrics()
	if metrics1.CacheMisses <= initialMetrics.CacheMisses {
		t.Error("Expected cache miss on first compilation")
	}

	// Second compilation - should be cache hit
	kernel2, err := compiler.CompileKernel(source, options)
	if err != nil {
		t.Fatalf("Failed to compile kernel: %v", err)
	}

	metrics2 := compiler.GetMetrics()
	if metrics2.CacheHits <= metrics1.CacheHits {
		t.Error("Expected cache hit on second compilation")
	}

	// Verify kernels are identical
	if kernel1.Hash != kernel2.Hash {
		t.Error("Cached kernel should have same hash as original")
	}
}

func TestKernelSource_Validation(t *testing.T) {
	config := DefaultCompilerConfig()
	config.CacheConfig.CacheDir = filepath.Join(os.TempDir(), "test_kernel_cache")
	config.EnableFallback = false // Disable fallback to test validation properly
	defer os.RemoveAll(config.CacheConfig.CacheDir)

	compiler, err := NewKernelCompiler(config)
	if err != nil {
		t.Fatalf("Failed to create kernel compiler: %v", err)
	}

	// Test empty source validation
	emptySource := &KernelSource{
		ID:         "empty_kernel",
		Name:       "EmptyKernel",
		Language:   KernelOpenCL,
		Type:       KernelTypeCompute,
		Source:     "",
		Headers:    make(map[string]string),
		Macros:     make(map[string]string),
		Parameters: []KernelParameter{},
		Metadata:   make(map[string]interface{}),
		CreatedAt:  time.Now(),
		ModifiedAt: time.Now(),
	}

	options := DefaultCompilationOptions()
	_, err = compiler.CompileKernel(emptySource, options)
	if err == nil {
		t.Error("Expected error for empty source code")
	}

	// Test invalid language validation
	invalidSource := &KernelSource{
		ID:         "invalid_kernel",
		Name:       "InvalidKernel",
		Language:   KernelLanguage("invalid"),
		Type:       KernelTypeCompute,
		Source:     "__kernel void test() { }",
		Headers:    make(map[string]string),
		Macros:     make(map[string]string),
		Parameters: []KernelParameter{},
		Metadata:   make(map[string]interface{}),
		CreatedAt:  time.Now(),
		ModifiedAt: time.Now(),
	}

	_, err = compiler.CompileKernel(invalidSource, options)
	if err == nil {
		t.Error("Expected error for invalid language")
	}
}

func TestKernelCache_Operations(t *testing.T) {
	config := DefaultCacheConfig()
	config.CacheDir = filepath.Join(os.TempDir(), "test_cache_operations")
	defer os.RemoveAll(config.CacheDir)

	cache, err := NewKernelCache(config)
	if err != nil {
		t.Fatalf("Failed to create kernel cache: %v", err)
	}
	defer cache.Cleanup()

	// Test cache miss
	_, found := cache.Get("nonexistent")
	if found {
		t.Error("Expected cache miss for nonexistent key")
	}

	// Test cache put and get
	kernel := &CompiledKernel{
		ID:       "test_kernel",
		SourceID: "test_source",
		Binary:   []byte("test binary"),
		Hash:     "test_hash",
		Size:     int64(len("test binary")),
	}

	err = cache.Put("test_key", kernel)
	if err != nil {
		t.Fatalf("Failed to put kernel in cache: %v", err)
	}

	retrieved, found := cache.Get("test_key")
	if !found {
		t.Error("Expected cache hit for existing key")
	}

	if retrieved.ID != kernel.ID {
		t.Errorf("Expected kernel ID %s, got %s", kernel.ID, retrieved.ID)
	}

	// Test cache metrics
	metrics := cache.GetMetrics()
	if metrics.Hits == 0 {
		t.Error("Expected at least one cache hit")
	}

	if metrics.EntryCount == 0 {
		t.Error("Expected at least one cache entry")
	}
}

func TestCompilationOptions_Defaults(t *testing.T) {
	options := DefaultCompilationOptions()

	if options.OptimizationLevel != OptimizationMedium {
		t.Errorf("Expected optimization level %s, got %s", OptimizationMedium, options.OptimizationLevel)
	}

	if options.Timeout <= 0 {
		t.Error("Timeout should be positive")
	}

	if options.IncludePaths == nil {
		t.Error("Include paths should be initialized")
	}

	if options.Defines == nil {
		t.Error("Defines should be initialized")
	}

	if options.CompilerFlags == nil {
		t.Error("Compiler flags should be initialized")
	}

	if options.Metadata == nil {
		t.Error("Metadata should be initialized")
	}
}

func TestCompilerConfig_Defaults(t *testing.T) {
	config := DefaultCompilerConfig()

	if !config.CacheConfig.Enabled {
		t.Error("Cache should be enabled by default")
	}

	if config.MaxConcurrentJobs <= 0 {
		t.Error("Max concurrent jobs should be positive")
	}

	if !config.EnableFallback {
		t.Error("Fallback should be enabled by default")
	}

	if len(config.FallbackLanguages) == 0 {
		t.Error("Should have fallback languages configured")
	}

	if config.Timeout <= 0 {
		t.Error("Timeout should be positive")
	}
}

func TestKernelCompiler_GetCompilerInfo(t *testing.T) {
	config := DefaultCompilerConfig()
	config.CacheConfig.CacheDir = filepath.Join(os.TempDir(), "test_compiler_info")
	defer os.RemoveAll(config.CacheConfig.CacheDir)

	compiler, err := NewKernelCompiler(config)
	if err != nil {
		t.Fatalf("Failed to create kernel compiler: %v", err)
	}

	info := compiler.GetCompilerInfo()
	if len(info) == 0 {
		t.Error("Expected compiler info for available compilers")
	}

	// Check that OpenCL info is present
	if openclInfo, exists := info[KernelOpenCL]; exists {
		if available, ok := openclInfo["available"].(bool); !ok || !available {
			t.Error("OpenCL compiler should be marked as available")
		}

		if version, ok := openclInfo["version"].(string); !ok || version == "" {
			t.Error("OpenCL compiler should have version information")
		}
	} else {
		t.Error("OpenCL compiler info should be present")
	}
}

func TestKernelCompiler_AutoOptimization(t *testing.T) {
	config := DefaultCompilerConfig()
	config.CacheConfig.CacheDir = filepath.Join(os.TempDir(), "test_auto_optimization")
	config.AutoOptimization = true
	defer os.RemoveAll(config.CacheConfig.CacheDir)

	compiler, err := NewKernelCompiler(config)
	if err != nil {
		t.Fatalf("Failed to create kernel compiler: %v", err)
	}

	// Test auto optimization for compute kernel
	computeSource := &KernelSource{
		ID:         "compute_kernel",
		Name:       "ComputeKernel",
		Language:   KernelOpenCL,
		Type:       KernelTypeCompute,
		Source:     "__kernel void compute() { }",
		Headers:    make(map[string]string),
		Macros:     make(map[string]string),
		Parameters: []KernelParameter{},
		Metadata:   make(map[string]interface{}),
		CreatedAt:  time.Now(),
		ModifiedAt: time.Now(),
	}

	options := DefaultCompilationOptions()
	options.OptimizationLevel = OptimizationAuto

	kernel, err := compiler.CompileKernel(computeSource, options)
	if err != nil {
		t.Fatalf("Failed to compile compute kernel: %v", err)
	}

	if kernel == nil {
		t.Fatal("Compiled kernel is nil")
	}

	// Test auto optimization for memory kernel
	memorySource := &KernelSource{
		ID:         "memory_kernel",
		Name:       "MemoryKernel",
		Language:   KernelOpenCL,
		Type:       KernelTypeMemory,
		Source:     "__kernel void memory_copy() { }",
		Headers:    make(map[string]string),
		Macros:     make(map[string]string),
		Parameters: []KernelParameter{},
		Metadata:   make(map[string]interface{}),
		CreatedAt:  time.Now(),
		ModifiedAt: time.Now(),
	}

	kernel2, err := compiler.CompileKernel(memorySource, options)
	if err != nil {
		t.Fatalf("Failed to compile memory kernel: %v", err)
	}

	if kernel2 == nil {
		t.Fatal("Compiled kernel is nil")
	}
}

func TestKernelCompiler_Cleanup(t *testing.T) {
	config := DefaultCompilerConfig()
	config.CacheConfig.CacheDir = filepath.Join(os.TempDir(), "test_cleanup")
	defer os.RemoveAll(config.CacheConfig.CacheDir)

	compiler, err := NewKernelCompiler(config)
	if err != nil {
		t.Fatalf("Failed to create kernel compiler: %v", err)
	}

	// Add some cache entries
	source := &KernelSource{
		ID:         "cleanup_test",
		Name:       "CleanupTest",
		Language:   KernelOpenCL,
		Type:       KernelTypeCompute,
		Source:     "__kernel void test() { }",
		Headers:    make(map[string]string),
		Macros:     make(map[string]string),
		Parameters: []KernelParameter{},
		Metadata:   make(map[string]interface{}),
		CreatedAt:  time.Now(),
		ModifiedAt: time.Now(),
	}

	options := DefaultCompilationOptions()
	_, err = compiler.CompileKernel(source, options)
	if err != nil {
		t.Fatalf("Failed to compile kernel: %v", err)
	}

	// Test cleanup
	err = compiler.Cleanup()
	if err != nil {
		t.Errorf("Failed to cleanup compiler: %v", err)
	}
}

func TestKernelCompiler_ClearCache(t *testing.T) {
	config := DefaultCompilerConfig()
	config.CacheConfig.CacheDir = filepath.Join(os.TempDir(), "test_clear_cache")
	defer os.RemoveAll(config.CacheConfig.CacheDir)

	compiler, err := NewKernelCompiler(config)
	if err != nil {
		t.Fatalf("Failed to create kernel compiler: %v", err)
	}

	// Add cache entry
	source := &KernelSource{
		ID:         "clear_test",
		Name:       "ClearTest",
		Language:   KernelOpenCL,
		Type:       KernelTypeCompute,
		Source:     "__kernel void test() { }",
		Headers:    make(map[string]string),
		Macros:     make(map[string]string),
		Parameters: []KernelParameter{},
		Metadata:   make(map[string]interface{}),
		CreatedAt:  time.Now(),
		ModifiedAt: time.Now(),
	}

	options := DefaultCompilationOptions()
	_, err = compiler.CompileKernel(source, options)
	if err != nil {
		t.Fatalf("Failed to compile kernel: %v", err)
	}

	// Verify cache has entries
	cacheMetrics := compiler.GetCacheMetrics()
	if cacheMetrics.EntryCount == 0 {
		t.Error("Expected cache to have entries before clearing")
	}

	// Clear cache
	err = compiler.ClearCache()
	if err != nil {
		t.Errorf("Failed to clear cache: %v", err)
	}

	// Verify cache is empty
	cacheMetrics = compiler.GetCacheMetrics()
	if cacheMetrics.EntryCount != 0 {
		t.Error("Expected cache to be empty after clearing")
	}
}
