package gpu

import (
	"math"
	"testing"
)

// TestTensorElementAccess tests getting and setting individual elements
func TestTensorElementAccess(t *testing.T) {
	// Test Float32 access
	tensor, err := NewTensor(TensorShape{2, 3}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Set and get float32 values
	err = tensor.SetFloat32(3.14, 0, 1)
	if err != nil {
		t.Fatalf("Failed to set float32 value: %v", err)
	}

	value, err := tensor.GetFloat32(0, 1)
	if err != nil {
		t.Fatalf("Failed to get float32 value: %v", err)
	}

	if value != 3.14 {
		t.<PERSON><PERSON><PERSON>("Expected 3.14, got %f", value)
	}

	// Test Int8 access
	int8Tensor, err := NewTensor(TensorShape{2, 2}, TensorInt8, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create int8 tensor: %v", err)
	}
	defer int8Tensor.Free()

	err = int8Tensor.SetInt8(42, 1, 0)
	if err != nil {
		t.Fatalf("Failed to set int8 value: %v", err)
	}

	int8Value, err := int8Tensor.GetInt8(1, 0)
	if err != nil {
		t.Fatalf("Failed to get int8 value: %v", err)
	}

	if int8Value != 42 {
		t.Errorf("Expected 42, got %d", int8Value)
	}

	// Test bounds checking
	_, err = tensor.GetFloat32(10, 10)
	if err == nil {
		t.Error("Expected bounds error for out-of-range access")
	}
}

// TestTensorElementWiseOperations tests add, subtract, multiply, divide
func TestTensorElementWiseOperations(t *testing.T) {
	// Create test tensors
	a, err := NewTensor(TensorShape{2, 2}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor A: %v", err)
	}
	defer a.Free()

	b, err := NewTensor(TensorShape{2, 2}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor B: %v", err)
	}
	defer b.Free()

	// Fill tensors with test data
	// A = [[1, 2], [3, 4]]
	a.SetFloat32(1.0, 0, 0)
	a.SetFloat32(2.0, 0, 1)
	a.SetFloat32(3.0, 1, 0)
	a.SetFloat32(4.0, 1, 1)

	// B = [[5, 6], [7, 8]]
	b.SetFloat32(5.0, 0, 0)
	b.SetFloat32(6.0, 0, 1)
	b.SetFloat32(7.0, 1, 0)
	b.SetFloat32(8.0, 1, 1)

	// Test Addition: A + B = [[6, 8], [10, 12]]
	result, err := a.Add(b)
	if err != nil {
		t.Fatalf("Failed to add tensors: %v", err)
	}
	defer result.Free()

	expected := []float32{6.0, 8.0, 10.0, 12.0}
	for i, exp := range expected {
		row, col := i/2, i%2
		val, err := result.GetFloat32(int64(row), int64(col))
		if err != nil {
			t.Fatalf("Failed to get result value: %v", err)
		}
		if val != exp {
			t.Errorf("Addition result[%d][%d]: expected %f, got %f", row, col, exp, val)
		}
	}

	// Test Subtraction: A - B = [[-4, -4], [-4, -4]]
	result2, err := a.Subtract(b)
	if err != nil {
		t.Fatalf("Failed to subtract tensors: %v", err)
	}
	defer result2.Free()

	expectedSub := []float32{-4.0, -4.0, -4.0, -4.0}
	for i, exp := range expectedSub {
		row, col := i/2, i%2
		val, err := result2.GetFloat32(int64(row), int64(col))
		if err != nil {
			t.Fatalf("Failed to get subtraction result: %v", err)
		}
		if val != exp {
			t.Errorf("Subtraction result[%d][%d]: expected %f, got %f", row, col, exp, val)
		}
	}

	// Test Multiplication: A * B = [[5, 12], [21, 32]]
	result3, err := a.Multiply(b)
	if err != nil {
		t.Fatalf("Failed to multiply tensors: %v", err)
	}
	defer result3.Free()

	expectedMul := []float32{5.0, 12.0, 21.0, 32.0}
	for i, exp := range expectedMul {
		row, col := i/2, i%2
		val, err := result3.GetFloat32(int64(row), int64(col))
		if err != nil {
			t.Fatalf("Failed to get multiplication result: %v", err)
		}
		if val != exp {
			t.Errorf("Multiplication result[%d][%d]: expected %f, got %f", row, col, exp, val)
		}
	}

	// Test Division: A / B = [[0.2, 0.333...], [0.428..., 0.5]]
	result4, err := a.Divide(b)
	if err != nil {
		t.Fatalf("Failed to divide tensors: %v", err)
	}
	defer result4.Free()

	expectedDiv := []float32{1.0 / 5.0, 2.0 / 6.0, 3.0 / 7.0, 4.0 / 8.0}
	for i, exp := range expectedDiv {
		row, col := i/2, i%2
		val, err := result4.GetFloat32(int64(row), int64(col))
		if err != nil {
			t.Fatalf("Failed to get division result: %v", err)
		}
		if math.Abs(float64(val-exp)) > 1e-6 {
			t.Errorf("Division result[%d][%d]: expected %f, got %f", row, col, exp, val)
		}
	}
}

// TestTensorMatrixMultiplication tests matrix multiplication
func TestTensorMatrixMultiplication(t *testing.T) {
	// Create matrices for multiplication
	// A: 2x3 matrix
	a, err := NewTensor(TensorShape{2, 3}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor A: %v", err)
	}
	defer a.Free()

	// B: 3x2 matrix
	b, err := NewTensor(TensorShape{3, 2}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor B: %v", err)
	}
	defer b.Free()

	// A = [[1, 2, 3], [4, 5, 6]]
	a.SetFloat32(1.0, 0, 0)
	a.SetFloat32(2.0, 0, 1)
	a.SetFloat32(3.0, 0, 2)
	a.SetFloat32(4.0, 1, 0)
	a.SetFloat32(5.0, 1, 1)
	a.SetFloat32(6.0, 1, 2)

	// B = [[7, 8], [9, 10], [11, 12]]
	b.SetFloat32(7.0, 0, 0)
	b.SetFloat32(8.0, 0, 1)
	b.SetFloat32(9.0, 1, 0)
	b.SetFloat32(10.0, 1, 1)
	b.SetFloat32(11.0, 2, 0)
	b.SetFloat32(12.0, 2, 1)

	// Perform matrix multiplication
	result, err := a.MatMul(b)
	if err != nil {
		t.Fatalf("Failed to multiply matrices: %v", err)
	}
	defer result.Free()

	// Expected result: C = A * B = [[58, 64], [139, 154]]
	// C[0][0] = 1*7 + 2*9 + 3*11 = 7 + 18 + 33 = 58
	// C[0][1] = 1*8 + 2*10 + 3*12 = 8 + 20 + 36 = 64
	// C[1][0] = 4*7 + 5*9 + 6*11 = 28 + 45 + 66 = 139
	// C[1][1] = 4*8 + 5*10 + 6*12 = 32 + 50 + 72 = 154
	expected := [][]float32{{58.0, 64.0}, {139.0, 154.0}}

	for i := 0; i < 2; i++ {
		for j := 0; j < 2; j++ {
			val, err := result.GetFloat32(int64(i), int64(j))
			if err != nil {
				t.Fatalf("Failed to get matrix multiplication result: %v", err)
			}
			if val != expected[i][j] {
				t.Errorf("MatMul result[%d][%d]: expected %f, got %f", i, j, expected[i][j], val)
			}
		}
	}

	// Test incompatible shapes
	c, err := NewTensor(TensorShape{2, 2}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor C: %v", err)
	}
	defer c.Free()

	_, err = a.MatMul(c) // 2x3 * 2x2 should fail
	if err == nil {
		t.Error("Expected error for incompatible matrix shapes")
	}
}

// TestTensorTranspose tests matrix transpose
func TestTensorTranspose(t *testing.T) {
	// Create a 2x3 matrix
	tensor, err := NewTensor(TensorShape{2, 3}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with test data: [[1, 2, 3], [4, 5, 6]]
	tensor.SetFloat32(1.0, 0, 0)
	tensor.SetFloat32(2.0, 0, 1)
	tensor.SetFloat32(3.0, 0, 2)
	tensor.SetFloat32(4.0, 1, 0)
	tensor.SetFloat32(5.0, 1, 1)
	tensor.SetFloat32(6.0, 1, 2)

	// Transpose
	transposed, err := tensor.Transpose()
	if err != nil {
		t.Fatalf("Failed to transpose tensor: %v", err)
	}
	defer transposed.Free()

	// Check shape
	if transposed.shape[0] != 3 || transposed.shape[1] != 2 {
		t.Errorf("Expected transposed shape [3, 2], got [%d, %d]", transposed.shape[0], transposed.shape[1])
	}

	// Expected transposed: [[1, 4], [2, 5], [3, 6]]
	expected := [][]float32{{1.0, 4.0}, {2.0, 5.0}, {3.0, 6.0}}

	for i := 0; i < 3; i++ {
		for j := 0; j < 2; j++ {
			val, err := transposed.GetFloat32(int64(i), int64(j))
			if err != nil {
				t.Fatalf("Failed to get transposed value: %v", err)
			}
			if val != expected[i][j] {
				t.Errorf("Transpose result[%d][%d]: expected %f, got %f", i, j, expected[i][j], val)
			}
		}
	}
}

// TestTensorFillOperations tests Fill, Zero, and Ones
func TestTensorFillOperations(t *testing.T) {
	tensor, err := NewTensor(TensorShape{2, 3}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Test Fill with specific value
	err = tensor.Fill(float32(7.5))
	if err != nil {
		t.Fatalf("Failed to fill tensor: %v", err)
	}

	// Check all elements are 7.5
	for i := int64(0); i < 2; i++ {
		for j := int64(0); j < 3; j++ {
			val, err := tensor.GetFloat32(i, j)
			if err != nil {
				t.Fatalf("Failed to get filled value: %v", err)
			}
			if val != 7.5 {
				t.Errorf("Fill result[%d][%d]: expected 7.5, got %f", i, j, val)
			}
		}
	}

	// Test Zero
	err = tensor.Zero()
	if err != nil {
		t.Fatalf("Failed to zero tensor: %v", err)
	}

	// Check all elements are 0
	for i := int64(0); i < 2; i++ {
		for j := int64(0); j < 3; j++ {
			val, err := tensor.GetFloat32(i, j)
			if err != nil {
				t.Fatalf("Failed to get zero value: %v", err)
			}
			if val != 0.0 {
				t.Errorf("Zero result[%d][%d]: expected 0.0, got %f", i, j, val)
			}
		}
	}

	// Test Ones
	err = tensor.Ones()
	if err != nil {
		t.Fatalf("Failed to set tensor to ones: %v", err)
	}

	// Check all elements are 1
	for i := int64(0); i < 2; i++ {
		for j := int64(0); j < 3; j++ {
			val, err := tensor.GetFloat32(i, j)
			if err != nil {
				t.Fatalf("Failed to get ones value: %v", err)
			}
			if val != 1.0 {
				t.Errorf("Ones result[%d][%d]: expected 1.0, got %f", i, j, val)
			}
		}
	}
}

// TestTensorOperationValidation tests error handling and validation
func TestTensorOperationValidation(t *testing.T) {
	a, err := NewTensor(TensorShape{2, 2}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor A: %v", err)
	}
	defer a.Free()

	// Test shape mismatch
	b, err := NewTensor(TensorShape{3, 3}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor B: %v", err)
	}
	defer b.Free()

	_, err = a.Add(b)
	if err == nil {
		t.Error("Expected error for shape mismatch in Add operation")
	}

	// Test data type mismatch
	c, err := NewTensor(TensorShape{2, 2}, TensorInt8, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor C: %v", err)
	}
	defer c.Free()

	_, err = a.Add(c)
	if err == nil {
		t.Error("Expected error for data type mismatch in Add operation")
	}

	// Test nil tensor
	_, err = a.Add(nil)
	if err == nil {
		t.Error("Expected error for nil tensor in Add operation")
	}

	// Test wrong data type access
	_, err = a.GetInt8(0, 0)
	if err == nil {
		t.Error("Expected error for wrong data type access")
	}

	err = a.SetInt8(42, 0, 0)
	if err == nil {
		t.Error("Expected error for wrong data type setting")
	}
}

// TestTensorInt8Operations tests operations with int8 data type
func TestTensorInt8Operations(t *testing.T) {
	a, err := NewTensor(TensorShape{2, 2}, TensorInt8, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create int8 tensor A: %v", err)
	}
	defer a.Free()

	b, err := NewTensor(TensorShape{2, 2}, TensorInt8, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create int8 tensor B: %v", err)
	}
	defer b.Free()

	// Fill with test data
	a.SetInt8(10, 0, 0)
	a.SetInt8(20, 0, 1)
	a.SetInt8(30, 1, 0)
	a.SetInt8(40, 1, 1)

	b.SetInt8(5, 0, 0)
	b.SetInt8(10, 0, 1)
	b.SetInt8(15, 1, 0)
	b.SetInt8(20, 1, 1)

	// Test addition
	result, err := a.Add(b)
	if err != nil {
		t.Fatalf("Failed to add int8 tensors: %v", err)
	}
	defer result.Free()

	expected := []int8{15, 30, 45, 60}
	for i, exp := range expected {
		row, col := i/2, i%2
		val, err := result.GetInt8(int64(row), int64(col))
		if err != nil {
			t.Fatalf("Failed to get int8 result: %v", err)
		}
		if val != exp {
			t.Errorf("Int8 addition result[%d][%d]: expected %d, got %d", row, col, exp, val)
		}
	}

	// Test fill operations
	err = a.Fill(int8(127))
	if err != nil {
		t.Fatalf("Failed to fill int8 tensor: %v", err)
	}

	val, err := a.GetInt8(0, 0)
	if err != nil {
		t.Fatalf("Failed to get filled int8 value: %v", err)
	}
	if val != 127 {
		t.Errorf("Expected 127, got %d", val)
	}

	// Test out of range fill
	err = a.Fill(int(200)) // Out of int8 range
	if err == nil {
		t.Error("Expected error for out-of-range int8 fill")
	}
}

// TestTensorDivisionByZero tests division by zero handling
func TestTensorDivisionByZero(t *testing.T) {
	a, err := NewTensor(TensorShape{2, 2}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor A: %v", err)
	}
	defer a.Free()

	b, err := NewTensor(TensorShape{2, 2}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor B: %v", err)
	}
	defer b.Free()

	// Fill A with non-zero values
	a.Fill(float32(1.0))

	// Fill B with zeros
	b.Zero()

	// Divide by zero
	result, err := a.Divide(b)
	if err != nil {
		t.Fatalf("Failed to divide by zero: %v", err)
	}
	defer result.Free()

	// Check that result contains positive infinity
	val, err := result.GetFloat32(0, 0)
	if err != nil {
		t.Fatalf("Failed to get division by zero result: %v", err)
	}

	if !math.IsInf(float64(val), 1) {
		t.Errorf("Expected positive infinity, got %f", val)
	}
}

// BenchmarkTensorOperations benchmarks tensor operations performance
func BenchmarkTensorOperations(b *testing.B) {
	// Create test tensors
	tensor1, _ := NewTensor(TensorShape{100, 100}, TensorFloat32, DeviceCPU, 0)
	defer tensor1.Free()
	tensor2, _ := NewTensor(TensorShape{100, 100}, TensorFloat32, DeviceCPU, 0)
	defer tensor2.Free()

	tensor1.Fill(float32(2.0))
	tensor2.Fill(float32(3.0))

	b.Run("Add", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			result, _ := tensor1.Add(tensor2)
			result.Free()
		}
	})

	b.Run("Multiply", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			result, _ := tensor1.Multiply(tensor2)
			result.Free()
		}
	})

	// Matrix multiplication benchmark
	matA, _ := NewTensor(TensorShape{50, 50}, TensorFloat32, DeviceCPU, 0)
	defer matA.Free()
	matB, _ := NewTensor(TensorShape{50, 50}, TensorFloat32, DeviceCPU, 0)
	defer matB.Free()

	matA.Fill(float32(1.5))
	matB.Fill(float32(2.5))

	b.Run("MatMul", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			result, _ := matA.MatMul(matB)
			result.Free()
		}
	})

	b.Run("Transpose", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			result, _ := tensor1.Transpose()
			result.Free()
		}
	})
}

// Tests for Advanced Tensor Operations

func TestTensorConv2D(t *testing.T) {
	// Test 2D convolution with 3D input (no batch)
	// Input: [2, 4, 4] (channels, height, width)
	// Kernel: [1, 2, 3, 3] (out_channels, in_channels, height, width)
	inputShape := []int64{2, 4, 4}
	kernelShape := []int64{1, 2, 3, 3}

	input, err := NewTensor(inputShape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create input tensor: %v", err)
	}
	defer input.Free()

	kernel, err := NewTensor(kernelShape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create kernel tensor: %v", err)
	}
	defer kernel.Free()

	// Fill input with incrementing values
	for i := int64(0); i < input.NumElements(); i++ {
		input.setElementFromFloat64(i, float64(i)+1)
	}

	// Fill kernel with small values
	for i := int64(0); i < kernel.NumElements(); i++ {
		kernel.setElementFromFloat64(i, 0.1)
	}

	// Test convolution with default parameters
	params := DefaultConvolutionParams()
	result, err := input.Conv2D(kernel, params)
	if err != nil {
		t.Fatalf("Conv2D failed: %v", err)
	}
	defer result.Free()

	// Check output shape: [1, 2, 2] (out_channels, out_height, out_width)
	expectedShape := []int64{1, 2, 2}
	if !equalShapes(result.shape, expectedShape) {
		t.Errorf("Expected shape %v, got %v", expectedShape, result.shape)
	}

	// Verify some output values are reasonable
	val, err := result.getElementAsFloat64(0)
	if err != nil {
		t.Errorf("Failed to get element: %v", err)
	}
	if val <= 0 {
		t.Errorf("Expected positive convolution result, got %f", val)
	}
}

func TestTensorConv2DWithBatch(t *testing.T) {
	// Test 2D convolution with 4D input (with batch)
	// Input: [2, 1, 3, 3] (batch, channels, height, width)
	// Kernel: [1, 1, 2, 2] (out_channels, in_channels, height, width)
	inputShape := []int64{2, 1, 3, 3}
	kernelShape := []int64{1, 1, 2, 2}

	input, err := NewTensor(inputShape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create input tensor: %v", err)
	}
	defer input.Free()

	kernel, err := NewTensor(kernelShape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create kernel tensor: %v", err)
	}
	defer kernel.Free()

	// Fill tensors with test data
	input.Fill(1.0)
	kernel.Fill(0.25) // 2x2 kernel with 0.25 each = sum of 1.0

	params := DefaultConvolutionParams()
	result, err := input.Conv2D(kernel, params)
	if err != nil {
		t.Fatalf("Conv2D failed: %v", err)
	}
	defer result.Free()

	// Check output shape: [2, 1, 2, 2] (batch, out_channels, out_height, out_width)
	expectedShape := []int64{2, 1, 2, 2}
	if !equalShapes(result.shape, expectedShape) {
		t.Errorf("Expected shape %v, got %v", expectedShape, result.shape)
	}

	// Verify output values (should be 1.0 since input=1.0 and kernel sums to 1.0)
	for i := int64(0); i < result.NumElements(); i++ {
		val, err := result.getElementAsFloat64(i)
		if err != nil {
			t.Errorf("Failed to get element %d: %v", i, err)
		}
		if math.Abs(val-1.0) > 1e-6 {
			t.Errorf("Expected ~1.0, got %f at index %d", val, i)
		}
	}
}

func TestTensorMaxPool2D(t *testing.T) {
	// Test max pooling with 3D input
	// Input: [1, 4, 4] (channels, height, width)
	inputShape := []int64{1, 4, 4}

	input, err := NewTensor(inputShape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create input tensor: %v", err)
	}
	defer input.Free()

	// Fill input with known pattern
	values := []float64{
		1, 2, 3, 4,
		5, 6, 7, 8,
		9, 10, 11, 12,
		13, 14, 15, 16,
	}
	for i, val := range values {
		input.setElementFromFloat64(int64(i), val)
	}

	// Test 2x2 max pooling with stride 2
	params := DefaultPoolingParams(2)
	result, err := input.MaxPool2D(params)
	if err != nil {
		t.Fatalf("MaxPool2D failed: %v", err)
	}
	defer result.Free()

	// Check output shape: [1, 2, 2]
	expectedShape := []int64{1, 2, 2}
	if !equalShapes(result.shape, expectedShape) {
		t.Errorf("Expected shape %v, got %v", expectedShape, result.shape)
	}

	// Check max pooling results
	expectedValues := []float64{6, 8, 14, 16} // Max values from each 2x2 window
	for i, expected := range expectedValues {
		val, err := result.getElementAsFloat64(int64(i))
		if err != nil {
			t.Errorf("Failed to get element %d: %v", i, err)
		}
		if val != expected {
			t.Errorf("Expected %f, got %f at index %d", expected, val, i)
		}
	}
}

func TestTensorAvgPool2D(t *testing.T) {
	// Test average pooling with 4D input (with batch)
	// Input: [1, 1, 2, 2] (batch, channels, height, width)
	inputShape := []int64{1, 1, 2, 2}

	input, err := NewTensor(inputShape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create input tensor: %v", err)
	}
	defer input.Free()

	// Fill with values: [1, 2; 3, 4]
	values := []float64{1, 2, 3, 4}
	for i, val := range values {
		input.setElementFromFloat64(int64(i), val)
	}

	// Test 2x2 average pooling
	params := DefaultPoolingParams(2)
	result, err := input.AvgPool2D(params)
	if err != nil {
		t.Fatalf("AvgPool2D failed: %v", err)
	}
	defer result.Free()

	// Check output shape: [1, 1, 1, 1]
	expectedShape := []int64{1, 1, 1, 1}
	if !equalShapes(result.shape, expectedShape) {
		t.Errorf("Expected shape %v, got %v", expectedShape, result.shape)
	}

	// Check average: (1+2+3+4)/4 = 2.5
	val, err := result.getElementAsFloat64(0)
	if err != nil {
		t.Errorf("Failed to get element: %v", err)
	}
	expected := 2.5
	if math.Abs(val-expected) > 1e-6 {
		t.Errorf("Expected %f, got %f", expected, val)
	}
}

func TestTensorReLU(t *testing.T) {
	// Test ReLU activation
	shape := []int64{2, 3}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with mix of positive and negative values
	values := []float64{-2, -1, 0, 1, 2, 3}
	for i, val := range values {
		tensor.setElementFromFloat64(int64(i), val)
	}

	result, err := tensor.ReLU()
	if err != nil {
		t.Fatalf("ReLU failed: %v", err)
	}
	defer result.Free()

	// Check ReLU results: max(0, x)
	expectedValues := []float64{0, 0, 0, 1, 2, 3}
	for i, expected := range expectedValues {
		val, err := result.getElementAsFloat64(int64(i))
		if err != nil {
			t.Errorf("Failed to get element %d: %v", i, err)
		}
		if val != expected {
			t.Errorf("Expected %f, got %f at index %d", expected, val, i)
		}
	}
}

func TestTensorReLUInPlace(t *testing.T) {
	// Test in-place ReLU activation
	shape := []int64{4}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with values
	values := []float64{-5, -1, 2, 10}
	for i, val := range values {
		tensor.setElementFromFloat64(int64(i), val)
	}

	err = tensor.ReLUInPlace()
	if err != nil {
		t.Fatalf("ReLUInPlace failed: %v", err)
	}

	// Check results
	expectedValues := []float64{0, 0, 2, 10}
	for i, expected := range expectedValues {
		val, err := tensor.getElementAsFloat64(int64(i))
		if err != nil {
			t.Errorf("Failed to get element %d: %v", i, err)
		}
		if val != expected {
			t.Errorf("Expected %f, got %f at index %d", expected, val, i)
		}
	}
}

func TestTensorSigmoid(t *testing.T) {
	// Test sigmoid activation
	shape := []int64{3}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with test values
	values := []float64{-1, 0, 1}
	for i, val := range values {
		tensor.setElementFromFloat64(int64(i), val)
	}

	result, err := tensor.Sigmoid()
	if err != nil {
		t.Fatalf("Sigmoid failed: %v", err)
	}
	defer result.Free()

	// Check sigmoid results
	for i := int64(0); i < 3; i++ {
		val, err := result.getElementAsFloat64(i)
		if err != nil {
			t.Errorf("Failed to get element %d: %v", i, err)
		}
		// Sigmoid values should be between 0 and 1
		if val <= 0 || val >= 1 {
			t.Errorf("Sigmoid value should be in (0,1), got %f at index %d", val, i)
		}
	}

	// Check specific values
	val0, _ := result.getElementAsFloat64(0) // sigmoid(-1) ≈ 0.269
	val1, _ := result.getElementAsFloat64(1) // sigmoid(0) = 0.5
	val2, _ := result.getElementAsFloat64(2) // sigmoid(1) ≈ 0.731

	if math.Abs(val1-0.5) > 1e-6 {
		t.Errorf("sigmoid(0) should be 0.5, got %f", val1)
	}
	if val0 >= val1 || val1 >= val2 {
		t.Errorf("Sigmoid should be monotonic increasing")
	}
}

func TestTensorTanh(t *testing.T) {
	// Test tanh activation
	shape := []int64{3}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with test values
	values := []float64{-1, 0, 1}
	for i, val := range values {
		tensor.setElementFromFloat64(int64(i), val)
	}

	result, err := tensor.Tanh()
	if err != nil {
		t.Fatalf("Tanh failed: %v", err)
	}
	defer result.Free()

	// Check tanh results
	val0, _ := result.getElementAsFloat64(0) // tanh(-1) ≈ -0.762
	val1, _ := result.getElementAsFloat64(1) // tanh(0) = 0
	val2, _ := result.getElementAsFloat64(2) // tanh(1) ≈ 0.762

	if math.Abs(val1) > 1e-6 {
		t.Errorf("tanh(0) should be 0, got %f", val1)
	}
	if val0 >= 0 || val2 <= 0 {
		t.Errorf("tanh should be antisymmetric around 0")
	}
	if math.Abs(val0+val2) > 1e-6 {
		t.Errorf("tanh(-x) should equal -tanh(x), got %f and %f", val0, val2)
	}
}

func TestTensorSoftmax(t *testing.T) {
	// Test softmax activation
	shape := []int64{3}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with test values
	values := []float64{1, 2, 3}
	for i, val := range values {
		tensor.setElementFromFloat64(int64(i), val)
	}

	result, err := tensor.Softmax()
	if err != nil {
		t.Fatalf("Softmax failed: %v", err)
	}
	defer result.Free()

	// Check that probabilities sum to 1
	var sum float64
	for i := int64(0); i < 3; i++ {
		val, err := result.getElementAsFloat64(i)
		if err != nil {
			t.Errorf("Failed to get element %d: %v", i, err)
		}
		if val <= 0 || val >= 1 {
			t.Errorf("Softmax probability should be in (0,1), got %f at index %d", val, i)
		}
		sum += val
	}

	if math.Abs(sum-1.0) > 1e-6 {
		t.Errorf("Softmax probabilities should sum to 1, got %f", sum)
	}

	// Check that probabilities are in ascending order (since input is [1,2,3])
	val0, _ := result.getElementAsFloat64(0)
	val1, _ := result.getElementAsFloat64(1)
	val2, _ := result.getElementAsFloat64(2)

	if val0 >= val1 || val1 >= val2 {
		t.Errorf("Softmax should preserve order, got [%f, %f, %f]", val0, val1, val2)
	}
}

func TestTensorSoftmax2D(t *testing.T) {
	// Test softmax on 2D tensor (along last dimension)
	shape := []int64{2, 3}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with test values: [[1,2,3], [4,5,6]]
	values := []float64{1, 2, 3, 4, 5, 6}
	for i, val := range values {
		tensor.setElementFromFloat64(int64(i), val)
	}

	result, err := tensor.Softmax()
	if err != nil {
		t.Fatalf("Softmax failed: %v", err)
	}
	defer result.Free()

	// Check that each row sums to 1
	for row := int64(0); row < 2; row++ {
		var sum float64
		for col := int64(0); col < 3; col++ {
			val, err := result.getElementAsFloat64(row*3 + col)
			if err != nil {
				t.Errorf("Failed to get element [%d,%d]: %v", row, col, err)
			}
			sum += val
		}
		if math.Abs(sum-1.0) > 1e-6 {
			t.Errorf("Row %d softmax should sum to 1, got %f", row, sum)
		}
	}
}

// Benchmark tests for advanced operations

func BenchmarkTensorConv2D(b *testing.B) {
	// Benchmark convolution with realistic sizes
	inputShape := []int64{32, 32, 32}    // 32 channels, 32x32 image
	kernelShape := []int64{64, 32, 3, 3} // 64 output channels, 32 input channels, 3x3 kernel

	input, _ := NewTensor(inputShape, TensorFloat32, DeviceCPU, 0)
	defer input.Free()
	kernel, _ := NewTensor(kernelShape, TensorFloat32, DeviceCPU, 0)
	defer kernel.Free()

	input.Fill(1.0)
	kernel.Fill(0.1)

	params := DefaultConvolutionParams()
	params.PaddingH = 1
	params.PaddingW = 1

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result, _ := input.Conv2D(kernel, params)
		result.Free()
	}
}

func BenchmarkTensorMaxPool2D(b *testing.B) {
	// Benchmark max pooling
	shape := []int64{64, 32, 32} // 64 channels, 32x32 image
	tensor, _ := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	defer tensor.Free()
	tensor.Fill(1.0)

	params := DefaultPoolingParams(2)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result, _ := tensor.MaxPool2D(params)
		result.Free()
	}
}

func BenchmarkTensorReLU(b *testing.B) {
	// Benchmark ReLU activation
	shape := []int64{1000, 1000}
	tensor, _ := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	defer tensor.Free()
	tensor.Fill(-0.5) // Half will be zeroed by ReLU

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result, _ := tensor.ReLU()
		result.Free()
	}
}

func BenchmarkTensorSoftmax(b *testing.B) {
	// Benchmark softmax activation
	shape := []int64{1000, 1000}
	tensor, _ := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	defer tensor.Free()
	tensor.Fill(1.0)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result, _ := tensor.Softmax()
		result.Free()
	}
}

// Helper function to compare tensor shapes
func equalShapes(a, b []int64) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}
