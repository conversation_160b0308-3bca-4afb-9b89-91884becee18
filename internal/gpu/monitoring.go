// Package gpu provides GPU monitoring and resource management functionality
package gpu

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// ResourceMonitor provides comprehensive GPU resource monitoring with history and callbacks
type ResourceMonitor struct {
	// Configuration
	interval         time.Duration
	enabled          bool
	logEvents        bool
	detectionManager *Manager

	// State management
	ctx     context.Context
	cancel  context.CancelFunc
	running bool
	mu      sync.RWMutex
	wg      sync.WaitGroup

	// Metrics storage - uses existing GPUMetrics type from types.go
	metrics     *GPUMetrics
	metricsMu   sync.RWMutex
	metricsHist []GPUMetrics
	maxHistory  int

	// Callbacks
	warningCallback  func(GPUMetrics)
	criticalCallback func(GPUMetrics)
	thresholds       *ResourceThresholds

	// Performance tracking
	lastUpdateTime  time.Time
	updateCount     int64
	totalUpdateTime time.Duration
}

// ResourceThresholds defines warning and critical thresholds for GPU monitoring
type ResourceThresholds struct {
	WarningMemoryUsage     float64 `json:"warning_memory_usage_percent"`
	CriticalMemoryUsage    float64 `json:"critical_memory_usage_percent"`
	WarningGPUUtilization  float64 `json:"warning_gpu_utilization_percent"`
	CriticalGPUUtilization float64 `json:"critical_gpu_utilization_percent"`
	MaxPowerUsage          float64 `json:"max_power_usage_watts"`
}

// ResourceMonitoringConfig configures GPU monitoring behavior
type ResourceMonitoringConfig struct {
	Interval         time.Duration
	Enabled          bool
	LogEvents        bool
	MaxHistorySize   int
	Thresholds       ResourceThresholds
	WarningCallback  func(GPUMetrics)
	CriticalCallback func(GPUMetrics)
}

// NewResourceMonitor creates a new GPU resource monitoring instance
func NewResourceMonitor(manager *Manager, config ResourceMonitoringConfig) *ResourceMonitor {
	if config.MaxHistorySize == 0 {
		config.MaxHistorySize = 100 // Default history size
	}

	if config.Interval == 0 {
		config.Interval = 1 * time.Second // Default monitoring interval
	}

	monitor := &ResourceMonitor{
		interval:         config.Interval,
		enabled:          config.Enabled,
		logEvents:        config.LogEvents,
		detectionManager: manager,
		maxHistory:       config.MaxHistorySize,
		warningCallback:  config.WarningCallback,
		criticalCallback: config.CriticalCallback,
		thresholds:       &config.Thresholds,
		metricsHist:      make([]GPUMetrics, 0, config.MaxHistorySize),
	}

	return monitor
}

// Start begins GPU monitoring
func (m *ResourceMonitor) Start(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("GPU resource monitor is already running")
	}

	if !m.enabled {
		return fmt.Errorf("GPU resource monitoring is disabled")
	}

	if m.detectionManager == nil {
		return fmt.Errorf("GPU detection manager is not configured")
	}

	m.ctx, m.cancel = context.WithCancel(ctx)
	m.running = true

	m.wg.Add(1)
	go m.monitoringLoop()

	if m.logEvents {
		log.Printf("GPU resource monitoring started with interval: %v", m.interval)
	}

	return nil
}

// Stop stops GPU monitoring
func (m *ResourceMonitor) Stop() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil
	}

	m.cancel()
	m.running = false

	// Wait for monitoring loop to finish
	m.wg.Wait()

	if m.logEvents {
		log.Printf("GPU resource monitoring stopped. Total updates: %d, Average latency: %v",
			m.updateCount, m.getAverageLatency())
	}

	return nil
}

// GetCurrentMetrics returns the latest GPU metrics
func (m *ResourceMonitor) GetCurrentMetrics() (*GPUMetrics, error) {
	m.metricsMu.RLock()
	defer m.metricsMu.RUnlock()

	if m.metrics == nil {
		return nil, fmt.Errorf("no metrics available")
	}

	// Return a copy
	metricsCopy := *m.metrics
	return &metricsCopy, nil
}

// GetMetricsHistory returns historical GPU metrics
func (m *ResourceMonitor) GetMetricsHistory() []GPUMetrics {
	m.metricsMu.RLock()
	defer m.metricsMu.RUnlock()

	// Return a copy of the history
	history := make([]GPUMetrics, len(m.metricsHist))
	copy(history, m.metricsHist)
	return history
}

// GetMonitoringStats returns monitoring performance statistics
func (m *ResourceMonitor) GetMonitoringStats() ResourceMonitoringStats {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return ResourceMonitoringStats{
		Running:          m.running,
		UpdateCount:      m.updateCount,
		AverageLatency:   m.getAverageLatency(),
		LastUpdateTime:   m.lastUpdateTime,
		HistorySize:      len(m.metricsHist),
		MonitoringUptime: time.Since(m.lastUpdateTime),
	}
}

// ResourceMonitoringStats provides monitoring performance statistics
type ResourceMonitoringStats struct {
	Running          bool          `json:"running"`
	UpdateCount      int64         `json:"update_count"`
	AverageLatency   time.Duration `json:"average_latency"`
	LastUpdateTime   time.Time     `json:"last_update_time"`
	HistorySize      int           `json:"history_size"`
	MonitoringUptime time.Duration `json:"monitoring_uptime"`
}

// SetThresholds updates monitoring thresholds
func (m *ResourceMonitor) SetThresholds(thresholds ResourceThresholds) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.thresholds = &thresholds
}

// SetCallbacks updates warning and critical callbacks
func (m *ResourceMonitor) SetCallbacks(warningCallback, criticalCallback func(GPUMetrics)) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.warningCallback = warningCallback
	m.criticalCallback = criticalCallback
}

// monitoringLoop runs the continuous monitoring loop
func (m *ResourceMonitor) monitoringLoop() {
	defer m.wg.Done()

	ticker := time.NewTicker(m.interval)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.updateMetrics()
		}
	}
}

// updateMetrics collects current GPU metrics from all available GPUs
func (m *ResourceMonitor) updateMetrics() {
	startTime := time.Now()

	gpus, err := m.detectionManager.GetAvailableGPUs()
	if err != nil {
		if m.logEvents {
			log.Printf("Failed to get available GPUs for monitoring: %v", err)
		}
		return
	}

	// For now, monitor the first available GPU (can be extended to monitor all)
	if len(gpus) == 0 {
		if m.logEvents {
			log.Printf("No GPUs available for monitoring")
		}
		return
	}

	primaryGPU := gpus[0]

	// Try to get metrics from the detection manager first
	metrics, err := m.detectionManager.GetCurrentMetrics(primaryGPU.ID)
	if err != nil {
		// If manager doesn't have metrics, create from GPU info
		metrics = &GPUMetrics{
			DeviceID:          primaryGPU.ID,
			Timestamp:         time.Now(),
			GPUUtilization:    primaryGPU.Utilization,
			MemoryUtilization: primaryGPU.MemoryUtilization(),
			PowerConsumption:  primaryGPU.PowerUsage,
			ClockSpeed:        primaryGPU.ClockRate / 1000,       // Convert kHz to MHz
			MemoryClockSpeed:  primaryGPU.MemoryClockRate / 1000, // Convert kHz to MHz
			FanSpeed:          0,                                 // Not available in basic GPUInfo
		}
	}

	// Update metrics with monitoring latency
	latency := time.Since(startTime)

	// Update stored metrics
	m.metricsMu.Lock()
	m.metrics = metrics
	m.addToHistory(*metrics)
	m.metricsMu.Unlock()

	// Update performance tracking
	m.mu.Lock()
	m.lastUpdateTime = time.Now()
	m.updateCount++
	m.totalUpdateTime += latency
	m.mu.Unlock()

	// Check thresholds and trigger callbacks
	m.checkThresholds(*metrics)

	if m.logEvents {
		log.Printf("GPU metrics updated for device %d in %v", metrics.DeviceID, latency)
	}
}

// addToHistory adds metrics to the historical record
func (m *ResourceMonitor) addToHistory(metrics GPUMetrics) {
	if len(m.metricsHist) >= m.maxHistory {
		// Remove oldest entry
		copy(m.metricsHist[0:], m.metricsHist[1:])
		m.metricsHist = m.metricsHist[:len(m.metricsHist)-1]
	}
	m.metricsHist = append(m.metricsHist, metrics)
}

// checkThresholds checks if metrics exceed configured thresholds
func (m *ResourceMonitor) checkThresholds(metrics GPUMetrics) {
	if m.thresholds == nil {
		return
	}

	// Check memory utilization thresholds
	if metrics.MemoryUtilization >= m.thresholds.CriticalMemoryUsage {
		if m.criticalCallback != nil {
			go m.criticalCallback(metrics)
		}
		if m.logEvents {
			log.Printf("CRITICAL: GPU %d memory utilization %.1f%% exceeds critical threshold %.1f%%",
				metrics.DeviceID, metrics.MemoryUtilization, m.thresholds.CriticalMemoryUsage)
		}
	} else if metrics.MemoryUtilization >= m.thresholds.WarningMemoryUsage {
		if m.warningCallback != nil {
			go m.warningCallback(metrics)
		}
		if m.logEvents {
			log.Printf("WARNING: GPU %d memory utilization %.1f%% exceeds warning threshold %.1f%%",
				metrics.DeviceID, metrics.MemoryUtilization, m.thresholds.WarningMemoryUsage)
		}
	}

	// Check GPU utilization thresholds
	if metrics.GPUUtilization >= m.thresholds.CriticalGPUUtilization {
		if m.criticalCallback != nil {
			go m.criticalCallback(metrics)
		}
		if m.logEvents {
			log.Printf("CRITICAL: GPU %d utilization %.1f%% exceeds critical threshold %.1f%%",
				metrics.DeviceID, metrics.GPUUtilization, m.thresholds.CriticalGPUUtilization)
		}
	} else if metrics.GPUUtilization >= m.thresholds.WarningGPUUtilization {
		if m.warningCallback != nil {
			go m.warningCallback(metrics)
		}
		if m.logEvents {
			log.Printf("WARNING: GPU %d utilization %.1f%% exceeds warning threshold %.1f%%",
				metrics.DeviceID, metrics.GPUUtilization, m.thresholds.WarningGPUUtilization)
		}
	}

	// Check power consumption threshold
	if m.thresholds.MaxPowerUsage > 0 && metrics.PowerConsumption > m.thresholds.MaxPowerUsage {
		if m.criticalCallback != nil {
			go m.criticalCallback(metrics)
		}
		if m.logEvents {
			log.Printf("CRITICAL: GPU %d power consumption %.1fW exceeds maximum threshold %.1fW",
				metrics.DeviceID, metrics.PowerConsumption, m.thresholds.MaxPowerUsage)
		}
	}
}

// getAverageLatency calculates the average monitoring latency
func (m *ResourceMonitor) getAverageLatency() time.Duration {
	if m.updateCount == 0 {
		return 0
	}
	return m.totalUpdateTime / time.Duration(m.updateCount)
}

// IsRunning returns whether the monitor is currently running
func (m *ResourceMonitor) IsRunning() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.running
}

// UpdateInterval updates the monitoring interval
func (m *ResourceMonitor) UpdateInterval(newInterval time.Duration) error {
	if newInterval < time.Millisecond*100 {
		return fmt.Errorf("monitoring interval must be at least 100ms")
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	oldInterval := m.interval
	m.interval = newInterval

	if m.logEvents {
		log.Printf("Updated monitoring interval from %v to %v", oldInterval, newInterval)
	}

	return nil
}
