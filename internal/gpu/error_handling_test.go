package gpu

import (
	"context"
	"errors"
	"log"
	"os"
	"testing"
	"time"
)

func TestGPUErrorTypes(t *testing.T) {
	tests := []struct {
		errorType GPUErrorType
		expected  string
	}{
		{GPUErrorTypeMemory, "memory"},
		{GPUErrorTypeExecution, "execution"},
		{GPUErrorTypeDriver, "driver"},
		{GPUErrorTypeDevice, "device"},
		{GPUErrorTypeContext, "context"},
		{GPUErrorTypeTimeout, "timeout"},
		{GPUErrorTypeOOM, "out_of_memory"},
		{GPUErrorTypeUnknown, "unknown"},
	}

	for _, test := range tests {
		if got := test.errorType.String(); got != test.expected {
			t.Errorf("GPUErrorType.String() = %v, want %v", got, test.expected)
		}
	}
}

func TestBaseGPUError(t *testing.T) {
	err := &BaseGPUError{
		Type:             GPUErrorTypeMemory,
		Severity:         GPUErrorSeverityError,
		Message:          "Test memory error",
		DeviceID:         0,
		Timestamp:        time.Now(),
		Context:          make(map[string]interface{}),
		Recoverable:      true,
		RecoveryStrategy: RecoveryStrategyMemoryCleanup,
		StackTrace:       "test stack trace",
		Metadata:         make(map[string]interface{}),
	}

	if err.GetType() != GPUErrorTypeMemory {
		t.Errorf("GetType() = %v, want %v", err.GetType(), GPUErrorTypeMemory)
	}

	if err.GetSeverity() != GPUErrorSeverityError {
		t.Errorf("GetSeverity() = %v, want %v", err.GetSeverity(), GPUErrorSeverityError)
	}

	if err.GetDeviceID() != 0 {
		t.Errorf("GetDeviceID() = %v, want %v", err.GetDeviceID(), 0)
	}

	if !err.IsRecoverable() {
		t.Error("IsRecoverable() = false, want true")
	}
}

func TestMemoryError(t *testing.T) {
	underlying := errors.New("CUDA out of memory")
	err := NewMemoryError("Memory allocation failed", 0, "tensor", 1024*1024*1024, 512*1024*1024, 2*1024*1024*1024, underlying)

	if err.GetType() != GPUErrorTypeMemory {
		t.Errorf("GetType() = %v, want %v", err.GetType(), GPUErrorTypeMemory)
	}

	if err.AllocationType != "tensor" {
		t.Errorf("AllocationType = %v, want tensor", err.AllocationType)
	}

	if err.RequestedSize != 1024*1024*1024 {
		t.Errorf("RequestedSize = %v, want %v", err.RequestedSize, 1024*1024*1024)
	}

	if err.GetRecoveryStrategy() != RecoveryStrategyMemoryCleanup {
		t.Errorf("GetRecoveryStrategy() = %v, want %v", err.GetRecoveryStrategy(), RecoveryStrategyMemoryCleanup)
	}
}

func TestExecutionError(t *testing.T) {
	underlying := errors.New("Invalid kernel parameters")
	err := NewExecutionError("Kernel launch failed", 1, "matmul_kernel", underlying)

	if err.GetType() != GPUErrorTypeExecution {
		t.Errorf("GetType() = %v, want %v", err.GetType(), GPUErrorTypeExecution)
	}

	if err.KernelName != "matmul_kernel" {
		t.Errorf("KernelName = %v, want matmul_kernel", err.KernelName)
	}

	if err.GetRecoveryStrategy() != RecoveryStrategyRetry {
		t.Errorf("GetRecoveryStrategy() = %v, want %v", err.GetRecoveryStrategy(), RecoveryStrategyRetry)
	}
}

func TestGPUErrorStatistics(t *testing.T) {
	stats := NewGPUErrorStatistics()

	if stats.TotalErrors != 0 {
		t.Errorf("TotalErrors = %v, want 0", stats.TotalErrors)
	}

	// Record some errors
	memErr := NewMemoryError("Memory error", 0, "tensor", 1024, 512, 2048, nil)
	execErr := NewExecutionError("Execution error", 1, "kernel", nil)

	stats.RecordError(memErr)
	stats.RecordError(execErr)

	if stats.TotalErrors != 2 {
		t.Errorf("TotalErrors = %v, want 2", stats.TotalErrors)
	}

	if stats.ErrorsByType[GPUErrorTypeMemory] != 1 {
		t.Errorf("Memory errors = %v, want 1", stats.ErrorsByType[GPUErrorTypeMemory])
	}

	if stats.RecoverableErrors != 2 {
		t.Errorf("RecoverableErrors = %v, want 2", stats.RecoverableErrors)
	}
}

func TestGPUErrorHandler(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	config := DefaultErrorHandlerConfig()
	config.EnableRecovery = false // Disable to avoid starting monitor

	handler, err := NewGPUErrorHandler(logger, config)
	if err != nil {
		t.Errorf("NewGPUErrorHandler() error = %v", err)
	}

	if handler == nil {
		t.Error("Handler should not be nil")
	}

	// Test error handling without recovery
	testErr := NewMemoryError("Test memory error", 0, "tensor", 1024, 512, 2048, nil)

	ctx := context.Background()
	err = handler.HandleError(ctx, testErr)

	// Should return the original error since recovery is disabled
	if err != testErr {
		t.Errorf("HandleError() should return original error when recovery disabled")
	}

	// Check statistics
	stats := handler.GetErrorStatistics()
	if stats.TotalErrors != 1 {
		t.Errorf("TotalErrors = %v, want 1", stats.TotalErrors)
	}

	// Test cleanup
	err = handler.Cleanup()
	if err != nil {
		t.Errorf("Cleanup() error = %v", err)
	}
}

func TestDefaultErrorHandlerConfig(t *testing.T) {
	config := DefaultErrorHandlerConfig()

	if config.MaxRecoveryAttempts != 3 {
		t.Error("MaxRecoveryAttempts should be 3 by default")
	}

	if !config.EnableRecovery {
		t.Error("EnableRecovery should be true by default")
	}

	if !config.StrictGPUMode {
		t.Error("StrictGPUMode should be true by default for GPU-focused product")
	}

	if config.TestMode {
		t.Error("TestMode should be false by default")
	}
}

func TestNewErrorRecoveryStrategies(t *testing.T) {
	// Test that memory errors no longer default to CPU fallback
	memErr := NewMemoryError("Memory allocation failed", 0, "tensor", 2*1024*1024*1024, 1024*1024*1024, 1024*1024*1024, nil)
	if memErr.GetRecoveryStrategy() == RecoveryStrategyFallbackCPU {
		t.Error("Memory errors should not default to CPU fallback strategy")
	}
	if memErr.IsRecoverable() {
		t.Error("Memory errors exceeding total memory should not be recoverable")
	}

	// Test that driver errors are not recoverable and don't use CPU fallback
	driverErr := NewDriverError("Driver error", 0, "12.0", "11.0", 999, nil)
	if driverErr.GetRecoveryStrategy() == RecoveryStrategyFallbackCPU {
		t.Error("Driver errors should not use CPU fallback strategy")
	}
	if driverErr.IsRecoverable() {
		t.Error("Driver errors should not be recoverable")
	}

	// Test that OOM errors suggest GPU-focused solutions
	oomErr := NewOutOfMemoryError("Out of memory", 0, 2*1024*1024*1024, 512*1024*1024, 1024*1024*1024, nil)
	found := false
	for _, action := range oomErr.SuggestedActions {
		if action == "Clear unused GPU memory" {
			found = true
			break
		}
	}
	if !found {
		t.Error("OOM errors should suggest GPU-focused solutions")
	}

	// Ensure no CPU fallback suggestion
	for _, action := range oomErr.SuggestedActions {
		if action == "Switch to CPU fallback" {
			t.Error("OOM errors should not suggest CPU fallback")
		}
	}
}
