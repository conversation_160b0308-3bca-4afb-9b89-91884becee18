package gpu

import (
	"fmt"
	"math"
	"sync"
)

// DriftDetector interface for concept drift detection
type DriftDetector interface {
	AddValue(value float64) bool // Returns true if drift is detected
	Reset()
	GetConfidence() float64
	GetWindowSize() int
}

// DriftDetectorConfig configures drift detection behavior
type DriftDetectorConfig struct {
	WindowSize      int     `json:"window_size"`
	Threshold       float64 `json:"threshold"`
	ConfidenceLevel float64 `json:"confidence_level"`
}

// NewDriftDetector creates a new drift detector based on the specified method
func NewDriftDetector(method string, config DriftDetectorConfig) (DriftDetector, error) {
	switch method {
	case "adwin":
		return NewADWIN(config), nil
	case "page_hinkley":
		return NewPageHinkley(config), nil
	case "spc":
		return NewSPC(config), nil
	default:
		return nil, fmt.Errorf("unknown drift detection method: %s", method)
	}
}

// ADWIN (Adaptive Windowing) drift detector
type <PERSON><PERSON><PERSON> struct {
	window     []float64
	maxSize    int
	delta      float64
	confidence float64
	mu         sync.RWMutex
}

// NewADWIN creates a new ADWIN drift detector
func NewADWIN(config DriftDetectorConfig) *ADWIN {
	return &ADWIN{
		window:     make([]float64, 0),
		maxSize:    config.WindowSize,
		delta:      config.Threshold,
		confidence: config.ConfidenceLevel,
	}
}

// AddValue adds a new value and checks for drift
func (a *ADWIN) AddValue(value float64) bool {
	a.mu.Lock()
	defer a.mu.Unlock()

	a.window = append(a.window, value)
	if len(a.window) > a.maxSize {
		a.window = a.window[1:]
	}

	if len(a.window) < 2 {
		return false
	}

	// Check for drift using adaptive windowing
	return a.detectDrift()
}

// detectDrift performs the ADWIN drift detection algorithm
func (a *ADWIN) detectDrift() bool {
	n := len(a.window)
	if n < 10 { // Need minimum samples
		return false
	}

	// Try different cut points
	for i := 1; i < n; i++ {
		w0 := a.window[:i]
		w1 := a.window[i:]

		mean0 := mean(w0)
		mean1 := mean(w1)

		// Calculate the statistical test
		n0, n1 := float64(len(w0)), float64(len(w1))
		harmonicMean := 1.0 / (1.0/n0 + 1.0/n1)

		// Simplified variance calculation
		var0 := variance(w0, mean0)
		var1 := variance(w1, mean1)
		pooledVar := (var0*n0 + var1*n1) / (n0 + n1)

		// Calculate the bound
		epsilon := math.Sqrt((2.0 * pooledVar * math.Log(2.0/a.delta)) / harmonicMean)

		if math.Abs(mean0-mean1) > epsilon {
			// Drift detected, keep the more recent window
			a.window = w1
			return true
		}
	}

	return false
}

// Reset resets the drift detector
func (a *ADWIN) Reset() {
	a.mu.Lock()
	defer a.mu.Unlock()
	a.window = a.window[:0]
}

// GetConfidence returns the current confidence level
func (a *ADWIN) GetConfidence() float64 {
	return a.confidence
}

// GetWindowSize returns the current window size
func (a *ADWIN) GetWindowSize() int {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return len(a.window)
}

// PageHinkley drift detector
type PageHinkley struct {
	values     []float64
	cumSum     float64
	minSum     float64
	threshold  float64
	lambda     float64 // Magnitude of change to detect
	alpha      float64 // False alarm rate
	confidence float64
	maxSize    int
	mu         sync.RWMutex
}

// NewPageHinkley creates a new Page-Hinkley drift detector
func NewPageHinkley(config DriftDetectorConfig) *PageHinkley {
	return &PageHinkley{
		values:     make([]float64, 0),
		threshold:  config.Threshold,
		lambda:     0.05, // Default magnitude of change
		alpha:      1.0 - config.ConfidenceLevel,
		confidence: config.ConfidenceLevel,
		maxSize:    config.WindowSize,
	}
}

// AddValue adds a new value and checks for drift
func (ph *PageHinkley) AddValue(value float64) bool {
	ph.mu.Lock()
	defer ph.mu.Unlock()

	ph.values = append(ph.values, value)
	if len(ph.values) > ph.maxSize {
		ph.values = ph.values[1:]
		// Recalculate cumulative sum for the new window
		ph.recalculateCumSum()
	} else {
		// Update cumulative sum incrementally
		if len(ph.values) > 1 {
			prevMean := mean(ph.values[:len(ph.values)-1])
			ph.cumSum += value - prevMean - ph.lambda
		}
	}

	// Update minimum cumulative sum
	if ph.cumSum < ph.minSum {
		ph.minSum = ph.cumSum
	}

	// Check for drift
	return (ph.cumSum - ph.minSum) > ph.threshold
}

// recalculateCumSum recalculates the cumulative sum for the current window
func (ph *PageHinkley) recalculateCumSum() {
	if len(ph.values) < 2 {
		ph.cumSum = 0
		ph.minSum = 0
		return
	}

	baseMean := mean(ph.values[:len(ph.values)/2]) // Use first half as baseline
	ph.cumSum = 0
	ph.minSum = 0

	for i := len(ph.values) / 2; i < len(ph.values); i++ {
		ph.cumSum += ph.values[i] - baseMean - ph.lambda
		if ph.cumSum < ph.minSum {
			ph.minSum = ph.cumSum
		}
	}
}

// Reset resets the drift detector
func (ph *PageHinkley) Reset() {
	ph.mu.Lock()
	defer ph.mu.Unlock()
	ph.values = ph.values[:0]
	ph.cumSum = 0
	ph.minSum = 0
}

// GetConfidence returns the current confidence level
func (ph *PageHinkley) GetConfidence() float64 {
	return ph.confidence
}

// GetWindowSize returns the current window size
func (ph *PageHinkley) GetWindowSize() int {
	ph.mu.RLock()
	defer ph.mu.RUnlock()
	return len(ph.values)
}

// SPC (Statistical Process Control) drift detector
type SPC struct {
	values        []float64
	mean          float64
	stdDev        float64
	threshold     float64 // Number of standard deviations
	confidence    float64
	maxSize       int
	violations    int
	maxViolations int
	mu            sync.RWMutex
}

// NewSPC creates a new Statistical Process Control drift detector
func NewSPC(config DriftDetectorConfig) *SPC {
	return &SPC{
		values:        make([]float64, 0),
		threshold:     config.Threshold,
		confidence:    config.ConfidenceLevel,
		maxSize:       config.WindowSize,
		maxViolations: 3, // Number of consecutive violations to trigger drift
	}
}

// AddValue adds a new value and checks for drift
func (spc *SPC) AddValue(value float64) bool {
	spc.mu.Lock()
	defer spc.mu.Unlock()

	spc.values = append(spc.values, value)
	if len(spc.values) > spc.maxSize {
		spc.values = spc.values[1:]
	}

	// Need enough samples to establish control limits
	if len(spc.values) < 30 {
		return false
	}

	// Update control limits
	spc.updateControlLimits()

	// Check if current value violates control limits
	upperLimit := spc.mean + spc.threshold*spc.stdDev
	lowerLimit := spc.mean - spc.threshold*spc.stdDev

	if value > upperLimit || value < lowerLimit {
		spc.violations++
	} else {
		spc.violations = 0 // Reset violations on normal value
	}

	// Trigger drift if we have too many consecutive violations
	return spc.violations >= spc.maxViolations
}

// updateControlLimits updates the mean and standard deviation
func (spc *SPC) updateControlLimits() {
	if len(spc.values) == 0 {
		return
	}

	spc.mean = mean(spc.values)
	spc.stdDev = math.Sqrt(variance(spc.values, spc.mean))
}

// Reset resets the drift detector
func (spc *SPC) Reset() {
	spc.mu.Lock()
	defer spc.mu.Unlock()
	spc.values = spc.values[:0]
	spc.violations = 0
	spc.mean = 0
	spc.stdDev = 0
}

// GetConfidence returns the current confidence level
func (spc *SPC) GetConfidence() float64 {
	return spc.confidence
}

// GetWindowSize returns the current window size
func (spc *SPC) GetWindowSize() int {
	spc.mu.RLock()
	defer spc.mu.RUnlock()
	return len(spc.values)
}

// Utility functions for statistical calculations
func mean(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}

	sum := 0.0
	for _, v := range values {
		sum += v
	}
	return sum / float64(len(values))
}

func variance(values []float64, mean float64) float64 {
	if len(values) <= 1 {
		return 0
	}

	sum := 0.0
	for _, v := range values {
		diff := v - mean
		sum += diff * diff
	}
	return sum / float64(len(values)-1)
}
