package gpu

import (
	"errors"
	"fmt"
	"log"
	"math"
	"sort"
	"sync"
	"time"
	"unsafe"
)

// baseAllocation represents a single memory allocation for build compatibility
type baseAllocation struct {
	ptr       CUDAMemoryPtr
	size      int64
	timestamp time.Time
}

// baseCUDAMemoryPool provides a build-agnostic base memory pool implementation
type baseCUDAMemoryPool struct {
	deviceID        int
	initialSize     int64
	allocations     map[uintptr]*baseAllocation
	totalAllocated  int64
	peakAllocated   int64
	allocationCount int64
	deallocCount    int64
	mutex           sync.RWMutex
	logger          *log.Logger
}

// initialize sets up the base memory pool
func (pool *baseCUDAMemoryPool) initialize() error {
	pool.logger.Printf("Initialized base memory pool for device %d", pool.deviceID)
	return nil
}

// Allocate allocates memory of the given size (mock implementation for testing)
func (pool *baseCUDAMemoryPool) Allocate(size int64) (CUDAMemoryPtr, error) {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	// In a real implementation, this would call CUDA allocation
	// For now, use a mock pointer for build compatibility
	ptr := CUDAMemoryPtr(unsafe.Pointer(uintptr(pool.allocationCount + 1000)))

	// Track allocation
	alloc := &baseAllocation{
		ptr:       ptr,
		size:      size,
		timestamp: time.Now(),
	}

	pool.allocations[uintptr(ptr)] = alloc
	pool.totalAllocated += size
	pool.allocationCount++

	if pool.totalAllocated > pool.peakAllocated {
		pool.peakAllocated = pool.totalAllocated
	}

	pool.logger.Printf("Allocated %d bytes on device %d, ptr: %v", size, pool.deviceID, ptr)
	return ptr, nil
}

// Free releases the allocated memory
func (pool *baseCUDAMemoryPool) Free(ptr CUDAMemoryPtr) error {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	// Find allocation
	alloc, exists := pool.allocations[uintptr(ptr)]
	if !exists {
		return fmt.Errorf("memory pointer not found in pool: %v", ptr)
	}

	// Update tracking
	delete(pool.allocations, uintptr(ptr))
	pool.totalAllocated -= alloc.size
	pool.deallocCount++

	pool.logger.Printf("Freed %d bytes on device %d, ptr: %v", alloc.size, pool.deviceID, ptr)
	return nil
}

// GetTotalAllocated returns total allocated memory
func (pool *baseCUDAMemoryPool) GetTotalAllocated() int64 {
	pool.mutex.RLock()
	defer pool.mutex.RUnlock()
	return pool.totalAllocated
}

// GetPeakAllocated returns peak allocated memory
func (pool *baseCUDAMemoryPool) GetPeakAllocated() int64 {
	pool.mutex.RLock()
	defer pool.mutex.RUnlock()
	return pool.peakAllocated
}

// GetFreeMemory returns available free memory
func (pool *baseCUDAMemoryPool) GetFreeMemory() int64 {
	pool.mutex.RLock()
	defer pool.mutex.RUnlock()
	return 1024 * 1024 * 1024 // Mock 1GB free memory
}

// Reset resets the memory pool
func (pool *baseCUDAMemoryPool) Reset() error {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	// Clear all allocations
	pool.allocations = make(map[uintptr]*baseAllocation)
	pool.totalAllocated = 0
	pool.allocationCount = 0
	pool.deallocCount = 0

	pool.logger.Printf("Reset memory pool for device %d", pool.deviceID)
	return nil
}

// GetStats returns memory pool statistics
func (pool *baseCUDAMemoryPool) GetStats() CUDAMemoryStats {
	pool.mutex.RLock()
	defer pool.mutex.RUnlock()

	// Calculate fragmentation (simplified)
	var fragmentation float64
	if pool.totalAllocated > 0 && len(pool.allocations) > 0 {
		// Simple fragmentation estimate based on allocation count vs total size
		avgAllocSize := float64(pool.totalAllocated) / float64(len(pool.allocations))
		idealAllocations := float64(pool.totalAllocated) / avgAllocSize
		actualAllocations := float64(len(pool.allocations))
		fragmentation = (actualAllocations - idealAllocations) / idealAllocations * 100.0
		if fragmentation < 0 {
			fragmentation = 0
		}
	}

	return CUDAMemoryStats{
		TotalAllocated:    pool.totalAllocated,
		PeakAllocated:     pool.peakAllocated,
		CurrentAllocated:  pool.totalAllocated,
		FreeMemory:        pool.GetFreeMemory(),
		FragmentationPct:  fragmentation,
		AllocationCount:   pool.allocationCount,
		DeallocationCount: pool.deallocCount,
	}
}

// AllocationStrategy defines different memory allocation strategies
type AllocationStrategy int

const (
	StrategyFixed AllocationStrategy = iota
	StrategyDynamic
	StrategyAdaptive
)

func (s AllocationStrategy) String() string {
	switch s {
	case StrategyFixed:
		return "fixed"
	case StrategyDynamic:
		return "dynamic"
	case StrategyAdaptive:
		return "adaptive"
	default:
		return "unknown"
	}
}

// MemoryBlock represents a memory block in the pool
type MemoryBlock struct {
	Ptr       CUDAMemoryPtr `json:"ptr"`
	Size      int64         `json:"size"`
	Used      bool          `json:"used"`
	Timestamp time.Time     `json:"timestamp"`
	RefCount  int32         `json:"ref_count"`
}

// MemoryPressureLevel indicates current memory pressure
type MemoryPressureLevel int

const (
	PressureLow MemoryPressureLevel = iota
	PressureMedium
	PressureHigh
	PressureCritical
)

func (p MemoryPressureLevel) String() string {
	switch p {
	case PressureLow:
		return "low"
	case PressureMedium:
		return "medium"
	case PressureHigh:
		return "high"
	case PressureCritical:
		return "critical"
	default:
		return "unknown"
	}
}

// PoolConfig configures the memory pool behavior
type PoolConfig struct {
	DeviceID                int                `json:"device_id"`
	InitialSize             int64              `json:"initial_size"`
	MaxSize                 int64              `json:"max_size"`
	BlockSize               int64              `json:"block_size"`
	Strategy                AllocationStrategy `json:"strategy"`
	GCInterval              time.Duration      `json:"gc_interval"`
	PressureThresholds      PressureThresholds `json:"pressure_thresholds"`
	FragmentationLimit      float64            `json:"fragmentation_limit"`
	EnableDefragmentation   bool               `json:"enable_defragmentation"`
	DefragmentationInterval time.Duration      `json:"defragmentation_interval"`
}

// PressureThresholds defines memory pressure thresholds
type PressureThresholds struct {
	MediumPercent   float64 `json:"medium_percent"`   // % of max size
	HighPercent     float64 `json:"high_percent"`     // % of max size
	CriticalPercent float64 `json:"critical_percent"` // % of max size
}

// DefaultPoolConfig returns default pool configuration
func DefaultPoolConfig(deviceID int) PoolConfig {
	return PoolConfig{
		DeviceID:    deviceID,
		InitialSize: 256 * 1024 * 1024,      // 256MB
		MaxSize:     2 * 1024 * 1024 * 1024, // 2GB
		BlockSize:   16 * 1024 * 1024,       // 16MB
		Strategy:    StrategyAdaptive,
		GCInterval:  30 * time.Second,
		PressureThresholds: PressureThresholds{
			MediumPercent:   70.0,
			HighPercent:     85.0,
			CriticalPercent: 95.0,
		},
		FragmentationLimit:      25.0, // 25%
		EnableDefragmentation:   true,
		DefragmentationInterval: 5 * time.Minute,
	}
}

// MemoryPoolStatistics contains detailed memory pool statistics
type MemoryPoolStatistics struct {
	DeviceID              int                 `json:"device_id"`
	Strategy              AllocationStrategy  `json:"strategy"`
	TotalSize             int64               `json:"total_size"`
	UsedSize              int64               `json:"used_size"`
	FreeSize              int64               `json:"free_size"`
	BlockCount            int                 `json:"block_count"`
	UsedBlockCount        int                 `json:"used_block_count"`
	FreeBlockCount        int                 `json:"free_block_count"`
	FragmentationPercent  float64             `json:"fragmentation_percent"`
	PressureLevel         MemoryPressureLevel `json:"pressure_level"`
	AllocationCount       int64               `json:"allocation_count"`
	DeallocationCount     int64               `json:"deallocation_count"`
	GCCount               int64               `json:"gc_count"`
	DefragmentationCount  int64               `json:"defragmentation_count"`
	LastGC                time.Time           `json:"last_gc"`
	LastDefragmentation   time.Time           `json:"last_defragmentation"`
	AverageAllocationSize int64               `json:"average_allocation_size"`
	LargestBlock          int64               `json:"largest_block"`
	SmallestBlock         int64               `json:"smallest_block"`
}

// AdvancedMemoryPool represents an advanced GPU memory pool
type AdvancedMemoryPool struct {
	config     PoolConfig
	basePools  map[int]CUDAMemoryPool // Device ID -> Base pool
	blocks     map[CUDAMemoryPtr]*MemoryBlock
	freeBlocks []*MemoryBlock
	usedBlocks []*MemoryBlock

	// Statistics
	stats             MemoryPoolStatistics
	allocationHistory []int64 // Recent allocation sizes for adaptive strategy
	actualSizeHistory []int64 // Track actual allocated block sizes for all strategies
	maxHistorySize    int

	// Synchronization
	mutex       sync.RWMutex
	gcMutex     sync.Mutex
	defragMutex sync.Mutex

	// Background processes
	gcTicker     *time.Ticker
	defragTicker *time.Ticker
	gcDone       chan bool
	defragDone   chan bool

	// Event handlers
	pressureHandlers []MemoryPressureHandler
	gcHandlers       []GCHandler

	logger            *log.Logger
	isInitialized     bool
	shutdownRequested bool
}

// MemoryPressureHandler is called when memory pressure changes
type MemoryPressureHandler func(oldLevel, newLevel MemoryPressureLevel, stats MemoryPoolStatistics)

// GCHandler is called when garbage collection completes
type GCHandler func(freedMemory int64, duration time.Duration, stats MemoryPoolStatistics)

// NewAdvancedMemoryPool creates a new advanced memory pool
func NewAdvancedMemoryPool(config PoolConfig, logger *log.Logger) (*AdvancedMemoryPool, error) {
	if logger == nil {
		logger = log.Default()
	}

	pool := &AdvancedMemoryPool{
		config:            config,
		basePools:         make(map[int]CUDAMemoryPool),
		blocks:            make(map[CUDAMemoryPtr]*MemoryBlock),
		freeBlocks:        make([]*MemoryBlock, 0),
		usedBlocks:        make([]*MemoryBlock, 0),
		allocationHistory: make([]int64, 0),
		actualSizeHistory: make([]int64, 0),
		maxHistorySize:    100,
		gcDone:            make(chan bool, 1),
		defragDone:        make(chan bool, 1),
		pressureHandlers:  make([]MemoryPressureHandler, 0),
		gcHandlers:        make([]GCHandler, 0),
		logger:            logger,
	}

	// Initialize statistics
	pool.stats = MemoryPoolStatistics{
		DeviceID:      config.DeviceID,
		Strategy:      config.Strategy,
		PressureLevel: PressureLow,
	}

	return pool, nil
}

// Initialize initializes the memory pool
func (p *AdvancedMemoryPool) Initialize() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.isInitialized {
		return errors.New("memory pool already initialized")
	}

	// Create base pool for the device
	basePool := &baseCUDAMemoryPool{
		deviceID:        p.config.DeviceID,
		initialSize:     p.config.InitialSize,
		allocations:     make(map[uintptr]*baseAllocation),
		totalAllocated:  0,
		peakAllocated:   0,
		allocationCount: 0,
		deallocCount:    0,
		logger:          p.logger,
	}

	if err := basePool.initialize(); err != nil {
		return fmt.Errorf("failed to initialize base pool: %w", err)
	}

	p.basePools[p.config.DeviceID] = basePool

	// Pre-allocate memory based on strategy
	if err := p.preAllocateMemory(); err != nil {
		return fmt.Errorf("failed to pre-allocate memory: %w", err)
	}

	// Start background processes
	p.startBackgroundProcesses()

	p.isInitialized = true
	p.logger.Printf("Advanced memory pool initialized for device %d with strategy %s",
		p.config.DeviceID, p.config.Strategy)

	return nil
}

// preAllocateMemory pre-allocates memory blocks based on strategy
func (p *AdvancedMemoryPool) preAllocateMemory() error {
	switch p.config.Strategy {
	case StrategyFixed:
		return p.preAllocateFixed()
	case StrategyDynamic:
		// No pre-allocation for dynamic strategy
		return nil
	case StrategyAdaptive:
		return p.preAllocateAdaptive()
	default:
		return fmt.Errorf("unknown allocation strategy: %v", p.config.Strategy)
	}
}

// preAllocateFixed pre-allocates fixed-size blocks
func (p *AdvancedMemoryPool) preAllocateFixed() error {
	basePool := p.basePools[p.config.DeviceID]
	blockCount := p.config.InitialSize / p.config.BlockSize

	for i := int64(0); i < blockCount; i++ {
		ptr, err := basePool.Allocate(p.config.BlockSize)
		if err != nil {
			return fmt.Errorf("failed to pre-allocate block %d: %w", i, err)
		}

		block := &MemoryBlock{
			Ptr:       ptr,
			Size:      p.config.BlockSize,
			Used:      false,
			Timestamp: time.Now(),
			RefCount:  0,
		}

		p.blocks[ptr] = block
		p.freeBlocks = append(p.freeBlocks, block)
	}

	p.stats.TotalSize = blockCount * p.config.BlockSize
	p.stats.BlockCount = int(blockCount)
	p.stats.FreeBlockCount = int(blockCount)
	p.stats.FreeSize = p.stats.TotalSize

	p.logger.Printf("Pre-allocated %d fixed blocks of %d bytes each", blockCount, p.config.BlockSize)
	return nil
}

// preAllocateAdaptive pre-allocates a small set of variable-size blocks
func (p *AdvancedMemoryPool) preAllocateAdaptive() error {
	basePool := p.basePools[p.config.DeviceID]

	// Pre-allocate different sized blocks for common use cases
	sizes := []int64{
		1 * 1024 * 1024,  // 1MB
		4 * 1024 * 1024,  // 4MB
		16 * 1024 * 1024, // 16MB
		64 * 1024 * 1024, // 64MB
	}

	var totalPreAllocated int64
	// Respect both InitialSize and MaxSize limits
	maxPreAllocation := p.config.InitialSize
	if p.config.MaxSize > 0 && p.config.MaxSize < maxPreAllocation {
		maxPreAllocation = p.config.MaxSize
	}

	for _, size := range sizes {
		if totalPreAllocated+size <= maxPreAllocation {
			ptr, err := basePool.Allocate(size)
			if err != nil {
				p.logger.Printf("Warning: failed to pre-allocate %d byte block: %v", size, err)
				continue
			}

			block := &MemoryBlock{
				Ptr:       ptr,
				Size:      size,
				Used:      false,
				Timestamp: time.Now(),
				RefCount:  0,
			}

			p.blocks[ptr] = block
			p.freeBlocks = append(p.freeBlocks, block)
			totalPreAllocated += size
		}
	}

	p.stats.TotalSize = totalPreAllocated
	p.stats.BlockCount = len(p.freeBlocks)
	p.stats.FreeBlockCount = len(p.freeBlocks)
	p.stats.FreeSize = totalPreAllocated

	p.logger.Printf("Pre-allocated %d adaptive blocks totaling %d bytes",
		len(p.freeBlocks), totalPreAllocated)
	return nil
}

// startBackgroundProcesses starts garbage collection and defragmentation
func (p *AdvancedMemoryPool) startBackgroundProcesses() {
	// Start garbage collection
	if p.config.GCInterval > 0 {
		p.gcTicker = time.NewTicker(p.config.GCInterval)
		p.gcDone = make(chan bool, 1) // Buffered channel to prevent blocking
		go p.backgroundGC()
	}

	// Start defragmentation
	if p.config.EnableDefragmentation && p.config.DefragmentationInterval > 0 {
		p.defragTicker = time.NewTicker(p.config.DefragmentationInterval)
		p.defragDone = make(chan bool, 1) // Buffered channel to prevent blocking
		go p.backgroundDefragmentation()
	}
}

// backgroundGC runs periodic garbage collection
func (p *AdvancedMemoryPool) backgroundGC() {
	for {
		// Check if ticker is nil (shutdown case)
		if p.gcTicker == nil {
			return
		}

		select {
		case <-p.gcTicker.C:
			if !p.shutdownRequested && p.gcTicker != nil {
				p.performGarbageCollection()
			}
		case <-p.gcDone:
			return
		}
	}
}

// backgroundDefragmentation runs periodic defragmentation
func (p *AdvancedMemoryPool) backgroundDefragmentation() {
	for {
		// Check if ticker is nil (shutdown case)
		if p.defragTicker == nil {
			return
		}

		select {
		case <-p.defragTicker.C:
			if !p.shutdownRequested && p.defragTicker != nil {
				p.performDefragmentation()
			}
		case <-p.defragDone:
			return
		}
	}
}

// Allocate allocates memory from the pool
func (p *AdvancedMemoryPool) Allocate(size int64) (CUDAMemoryPtr, error) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if !p.isInitialized {
		return nil, errors.New("memory pool not initialized")
	}

	// Update allocation history for adaptive strategy
	if p.config.Strategy == StrategyAdaptive {
		p.updateAllocationHistory(size)
	}

	// Find suitable block
	block, err := p.findSuitableBlock(size)
	if err != nil {
		// Allocate new block if allowed
		return p.allocateNewBlock(size)
	}

	// Mark block as used
	block.Used = true
	block.Timestamp = time.Now()
	block.RefCount = 1

	// Move from free to used list
	p.removeFreeBlock(block)
	p.usedBlocks = append(p.usedBlocks, block)

	// Update actual size history for all strategies (with actual block size)
	p.updateActualSizeHistory(block.Size)

	// Update statistics
	p.stats.AllocationCount++
	p.stats.UsedSize += block.Size
	p.stats.FreeSize -= block.Size
	p.stats.UsedBlockCount++
	p.stats.FreeBlockCount--

	// Check and update pressure level
	p.updatePressureLevel()

	p.logger.Printf("Allocated %d bytes (block size: %d) on device %d, ptr: %v",
		size, block.Size, p.config.DeviceID, block.Ptr)

	return block.Ptr, nil
}

// Free releases memory back to the pool
func (p *AdvancedMemoryPool) Free(ptr CUDAMemoryPtr) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if !p.isInitialized {
		return errors.New("memory pool not initialized")
	}

	block, exists := p.blocks[ptr]
	if !exists {
		return fmt.Errorf("memory pointer not found in pool: %v", ptr)
	}

	if !block.Used {
		return fmt.Errorf("memory block already freed: %v", ptr)
	}

	// Mark block as free
	block.Used = false
	block.Timestamp = time.Now()
	block.RefCount = 0

	// Move from used to free list
	p.removeUsedBlock(block)
	p.freeBlocks = append(p.freeBlocks, block)

	// Update statistics
	p.stats.DeallocationCount++
	p.stats.UsedSize -= block.Size
	p.stats.FreeSize += block.Size
	p.stats.UsedBlockCount--
	p.stats.FreeBlockCount++

	// Check and update pressure level
	p.updatePressureLevel()

	p.logger.Printf("Freed %d bytes on device %d, ptr: %v",
		block.Size, p.config.DeviceID, ptr)

	return nil
}

// findSuitableBlock finds a suitable free block for the requested size
func (p *AdvancedMemoryPool) findSuitableBlock(size int64) (*MemoryBlock, error) {
	if len(p.freeBlocks) == 0 {
		return nil, errors.New("no free blocks available")
	}

	// Enhanced block finding with multiple strategies for better reuse
	var bestBlock *MemoryBlock

	// Strategy 1: Exact match (perfect reuse)
	for _, block := range p.freeBlocks {
		if block.Size == size {
			return block, nil
		}
	}

	// Strategy 2: Best-fit with minimal waste (< 25% waste)
	var bestFit int64 = math.MaxInt64
	for _, block := range p.freeBlocks {
		if block.Size >= size {
			waste := block.Size - size
			wastePercent := float64(waste) / float64(block.Size) * 100.0

			// Prefer blocks with less than 25% waste
			if wastePercent < 25.0 && waste < bestFit {
				bestFit = waste
				bestBlock = block
			}
		}
	}

	if bestBlock != nil {
		return bestBlock, nil
	}

	// Strategy 3: First-fit for larger blocks (allow more waste for efficiency)
	for _, block := range p.freeBlocks {
		if block.Size >= size {
			waste := block.Size - size
			wastePercent := float64(waste) / float64(block.Size) * 100.0

			// For larger requests, allow up to 50% waste to improve allocation speed
			if size >= p.config.BlockSize || wastePercent < 50.0 {
				return block, nil
			}
		}
	}

	// Strategy 4: Last resort - any block that fits
	for _, block := range p.freeBlocks {
		if block.Size >= size {
			return block, nil
		}
	}

	return nil, errors.New("no suitable block found")
}

// allocateNewBlock allocates a new block from the base pool
func (p *AdvancedMemoryPool) allocateNewBlock(size int64) (CUDAMemoryPtr, error) {
	// Check if we can allocate more memory
	if p.stats.TotalSize+size > p.config.MaxSize {
		return nil, fmt.Errorf("allocation would exceed max pool size (%d bytes)", p.config.MaxSize)
	}

	basePool := p.basePools[p.config.DeviceID]

	// Determine actual allocation size based on strategy
	allocSize := p.determineAllocationSize(size)

	ptr, err := basePool.Allocate(allocSize)
	if err != nil {
		return nil, fmt.Errorf("failed to allocate new block: %w", err)
	}

	// Create new block
	block := &MemoryBlock{
		Ptr:       ptr,
		Size:      allocSize,
		Used:      true,
		Timestamp: time.Now(),
		RefCount:  1,
	}

	p.blocks[ptr] = block
	p.usedBlocks = append(p.usedBlocks, block)

	// Update actual size history for all strategies (with actual block size)
	p.updateActualSizeHistory(allocSize)

	// Update statistics
	p.stats.TotalSize += allocSize
	p.stats.BlockCount++
	p.stats.UsedBlockCount++
	p.stats.AllocationCount++
	p.stats.UsedSize += allocSize

	// Check and update pressure level
	p.updatePressureLevel()

	p.logger.Printf("Allocated new block of %d bytes (requested: %d) on device %d",
		allocSize, size, p.config.DeviceID)

	return ptr, nil
}

// determineAllocationSize determines the actual allocation size based on strategy
func (p *AdvancedMemoryPool) determineAllocationSize(requestedSize int64) int64 {
	switch p.config.Strategy {
	case StrategyFixed:
		// Round up to nearest block size multiple
		blocks := (requestedSize + p.config.BlockSize - 1) / p.config.BlockSize
		return blocks * p.config.BlockSize
	case StrategyDynamic:
		// Allocate exactly what was requested
		return requestedSize
	case StrategyAdaptive:
		// Use allocation history to determine optimal size
		return p.adaptiveAllocationSize(requestedSize)
	default:
		return requestedSize
	}
}

// adaptiveAllocationSize calculates allocation size based on history
func (p *AdvancedMemoryPool) adaptiveAllocationSize(requestedSize int64) int64 {
	if len(p.allocationHistory) == 0 {
		return requestedSize
	}

	// Calculate average allocation size from history
	var total int64
	for _, size := range p.allocationHistory {
		total += size
	}
	avgSize := total / int64(len(p.allocationHistory))

	// If requested size is much larger than average, allocate exactly
	if requestedSize > avgSize*2 {
		return requestedSize
	}

	// Otherwise, allocate a bit more to reduce fragmentation
	return requestedSize + (requestedSize / 4) // 25% extra
}

// updateAllocationHistory updates the allocation history for adaptive strategy
func (p *AdvancedMemoryPool) updateAllocationHistory(size int64) {
	p.allocationHistory = append(p.allocationHistory, size)

	// Keep history within bounds
	if len(p.allocationHistory) > p.maxHistorySize {
		p.allocationHistory = p.allocationHistory[1:]
	}
}

// updateActualSizeHistory updates the actual allocated block size history for all strategies
func (p *AdvancedMemoryPool) updateActualSizeHistory(actualSize int64) {
	p.actualSizeHistory = append(p.actualSizeHistory, actualSize)

	// Keep history within bounds
	if len(p.actualSizeHistory) > p.maxHistorySize {
		p.actualSizeHistory = p.actualSizeHistory[1:]
	}
}

// removeFreeBlock removes a block from the free blocks list
func (p *AdvancedMemoryPool) removeFreeBlock(targetBlock *MemoryBlock) {
	for i, block := range p.freeBlocks {
		if block.Ptr == targetBlock.Ptr {
			p.freeBlocks = append(p.freeBlocks[:i], p.freeBlocks[i+1:]...)
			break
		}
	}
}

// removeUsedBlock removes a block from the used blocks list
func (p *AdvancedMemoryPool) removeUsedBlock(targetBlock *MemoryBlock) {
	for i, block := range p.usedBlocks {
		if block.Ptr == targetBlock.Ptr {
			p.usedBlocks = append(p.usedBlocks[:i], p.usedBlocks[i+1:]...)
			break
		}
	}
}

// updatePressureLevel updates the current memory pressure level
func (p *AdvancedMemoryPool) updatePressureLevel() {
	if p.config.MaxSize == 0 {
		return
	}

	// Calculate utilization based on MaxSize, not current TotalSize
	// This gives a true measure of memory pressure relative to the pool's capacity
	utilization := float64(p.stats.UsedSize) / float64(p.config.MaxSize) * 100.0
	oldLevel := p.stats.PressureLevel
	newLevel := PressureLow

	if utilization >= p.config.PressureThresholds.CriticalPercent {
		newLevel = PressureCritical
	} else if utilization >= p.config.PressureThresholds.HighPercent {
		newLevel = PressureHigh
	} else if utilization >= p.config.PressureThresholds.MediumPercent {
		newLevel = PressureMedium
	}

	if newLevel != oldLevel {
		p.stats.PressureLevel = newLevel

		// Notify pressure handlers
		for _, handler := range p.pressureHandlers {
			go handler(oldLevel, newLevel, p.stats)
		}

		p.logger.Printf("Memory pressure level changed from %s to %s (utilization: %.1f%% of max size)",
			oldLevel, newLevel, utilization)
	}
}

// performGarbageCollection performs garbage collection
func (p *AdvancedMemoryPool) performGarbageCollection() {
	p.gcMutex.Lock()
	defer p.gcMutex.Unlock()

	start := time.Now()
	var freedMemory int64

	p.mutex.Lock()

	// Find unused blocks that are old enough to be collected
	now := time.Now()
	gcThreshold := 30 * time.Second // Don't GC blocks freed less than 30 seconds ago

	var blocksToFree []*MemoryBlock
	for _, block := range p.freeBlocks {
		if !block.Used && now.Sub(block.Timestamp) > gcThreshold {
			blocksToFree = append(blocksToFree, block)
		}
	}

	// Free old unused blocks back to base pool
	basePool := p.basePools[p.config.DeviceID]
	for _, block := range blocksToFree {
		if err := basePool.Free(block.Ptr); err != nil {
			p.logger.Printf("Warning: failed to free block during GC: %v", err)
			continue
		}

		// Remove from our tracking
		delete(p.blocks, block.Ptr)
		p.removeFreeBlock(block)

		freedMemory += block.Size
		p.stats.TotalSize -= block.Size
		p.stats.FreeSize -= block.Size
		p.stats.BlockCount--
		p.stats.FreeBlockCount--
	}

	p.stats.GCCount++
	p.stats.LastGC = time.Now()

	p.mutex.Unlock()

	duration := time.Since(start)

	// Notify GC handlers
	for _, handler := range p.gcHandlers {
		go handler(freedMemory, duration, p.stats)
	}

	if freedMemory > 0 {
		p.logger.Printf("Garbage collection completed: freed %d bytes in %v",
			freedMemory, duration)
	}
}

// performDefragmentation performs memory defragmentation
func (p *AdvancedMemoryPool) performDefragmentation() {
	p.defragMutex.Lock()
	defer p.defragMutex.Unlock()

	p.mutex.Lock()
	defer p.mutex.Unlock()

	// Calculate current fragmentation
	fragmentation := p.calculateFragmentation()

	if fragmentation < p.config.FragmentationLimit {
		return // No defragmentation needed
	}

	start := time.Now()
	var coalescedBlocks int
	var freedMemory int64

	// Sort free blocks by size for better coalescing opportunities
	sort.Slice(p.freeBlocks, func(i, j int) bool {
		return p.freeBlocks[i].Size < p.freeBlocks[j].Size
	})

	// Attempt to coalesce small blocks into larger ones
	// This is a simplified coalescing that combines smaller blocks
	coalescedBlocks = p.coalesceSmallBlocks()

	// Remove very small fragmented blocks that are inefficient
	freedMemory = p.removeSmallFragments()

	p.stats.DefragmentationCount++
	p.stats.LastDefragmentation = time.Now()

	duration := time.Since(start)
	newFragmentation := p.calculateFragmentation()

	p.logger.Printf("Defragmentation completed in %v: fragmentation %.1f%% -> %.1f%%, coalesced %d blocks, freed %d bytes",
		duration, fragmentation, newFragmentation, coalescedBlocks, freedMemory)
}

// coalesceSmallBlocks attempts to coalesce small free blocks
func (p *AdvancedMemoryPool) coalesceSmallBlocks() int {
	if len(p.freeBlocks) < 2 {
		return 0
	}

	coalescedCount := 0
	basePool := p.basePools[p.config.DeviceID]

	// Group small blocks (less than half the average size)
	var avgSize int64
	for _, block := range p.freeBlocks {
		avgSize += block.Size
	}
	avgSize /= int64(len(p.freeBlocks))
	threshold := avgSize / 2

	var smallBlocks []*MemoryBlock
	for _, block := range p.freeBlocks {
		if block.Size < threshold && len(smallBlocks) < 10 { // Limit to avoid too much work
			smallBlocks = append(smallBlocks, block)
		}
	}

	// If we have multiple small blocks, try to coalesce them
	if len(smallBlocks) >= 2 {
		var totalSize int64
		for _, block := range smallBlocks {
			totalSize += block.Size
			// Free the small block
			if err := basePool.Free(block.Ptr); err != nil {
				p.logger.Printf("Warning: failed to free small block during coalescing: %v", err)
				continue
			}
			// Remove from tracking
			delete(p.blocks, block.Ptr)
			p.removeFreeBlock(block)
			p.stats.TotalSize -= block.Size
			p.stats.FreeSize -= block.Size
			p.stats.BlockCount--
			p.stats.FreeBlockCount--
		}

		// Allocate a single larger block to replace them
		if totalSize > 0 {
			ptr, err := basePool.Allocate(totalSize)
			if err != nil {
				p.logger.Printf("Warning: failed to allocate coalesced block: %v", err)
			} else {
				// Create new coalesced block
				coalescedBlock := &MemoryBlock{
					Ptr:       ptr,
					Size:      totalSize,
					Used:      false,
					Timestamp: time.Now(),
					RefCount:  0,
				}

				p.blocks[ptr] = coalescedBlock
				p.freeBlocks = append(p.freeBlocks, coalescedBlock)
				p.stats.TotalSize += totalSize
				p.stats.FreeSize += totalSize
				p.stats.BlockCount++
				p.stats.FreeBlockCount++

				coalescedCount = len(smallBlocks)
			}
		}
	}

	return coalescedCount
}

// removeSmallFragments removes very small fragmented blocks
func (p *AdvancedMemoryPool) removeSmallFragments() int64 {
	if len(p.freeBlocks) == 0 {
		return 0
	}

	// Calculate minimum useful block size (1% of average allocation or config block size)
	minSize := p.config.BlockSize / 100
	if p.stats.AllocationCount > 0 {
		avgAllocationSize := p.stats.UsedSize / p.stats.AllocationCount
		if avgAllocationSize > 0 {
			minSize = avgAllocationSize / 100
		}
	}

	if minSize < 1024 {
		minSize = 1024 // At least 1KB
	}

	var freedMemory int64
	basePool := p.basePools[p.config.DeviceID]

	// Remove blocks that are too small to be useful
	var toRemove []*MemoryBlock
	for _, block := range p.freeBlocks {
		if block.Size < minSize {
			toRemove = append(toRemove, block)
		}
	}

	for _, block := range toRemove {
		if err := basePool.Free(block.Ptr); err != nil {
			p.logger.Printf("Warning: failed to free small fragment: %v", err)
			continue
		}

		delete(p.blocks, block.Ptr)
		p.removeFreeBlock(block)
		freedMemory += block.Size
		p.stats.TotalSize -= block.Size
		p.stats.FreeSize -= block.Size
		p.stats.BlockCount--
		p.stats.FreeBlockCount--
	}

	return freedMemory
}

// calculateFragmentation calculates memory fragmentation percentage
func (p *AdvancedMemoryPool) calculateFragmentation() float64 {
	if len(p.freeBlocks) <= 1 {
		return 0.0 // No fragmentation with 0 or 1 free blocks
	}

	if p.stats.FreeSize == 0 {
		return 0.0
	}

	// Calculate fragmentation based on size variance of free blocks
	// More uniform block sizes = less fragmentation
	// High variance in block sizes = more fragmentation

	// Calculate average free block size
	avgBlockSize := float64(p.stats.FreeSize) / float64(len(p.freeBlocks))

	// Calculate variance in block sizes
	var variance float64
	for _, block := range p.freeBlocks {
		diff := float64(block.Size) - avgBlockSize
		variance += diff * diff
	}
	variance /= float64(len(p.freeBlocks))

	// Calculate standard deviation
	stdDev := math.Sqrt(variance)

	// Normalize fragmentation as percentage of average block size
	if avgBlockSize == 0 {
		return 0.0
	}

	fragmentation := (stdDev / avgBlockSize) * 100.0

	// Cap at 100%
	if fragmentation > 100.0 {
		fragmentation = 100.0
	}

	return fragmentation
}

// GetStatistics returns current memory pool statistics
func (p *AdvancedMemoryPool) GetStatistics() MemoryPoolStatistics {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	// Update dynamic statistics
	p.stats.FragmentationPercent = p.calculateFragmentation()

	// Calculate average allocation size from actual allocated block sizes
	if len(p.actualSizeHistory) > 0 {
		var total int64
		for _, size := range p.actualSizeHistory {
			total += size
		}
		p.stats.AverageAllocationSize = total / int64(len(p.actualSizeHistory))
	} else if p.stats.AllocationCount > 0 {
		// Fallback to current calculation if no history available
		p.stats.AverageAllocationSize = p.stats.UsedSize / p.stats.AllocationCount
	}

	// Find largest and smallest blocks
	var largest, smallest int64
	for _, block := range p.blocks {
		if largest == 0 || block.Size > largest {
			largest = block.Size
		}
		if smallest == 0 || block.Size < smallest {
			smallest = block.Size
		}
	}
	p.stats.LargestBlock = largest
	p.stats.SmallestBlock = smallest

	return p.stats
}

// Reset resets the memory pool
func (p *AdvancedMemoryPool) Reset() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	return p.resetInternal()
}

// resetInternal performs the reset without acquiring mutex (assumes caller holds it)
func (p *AdvancedMemoryPool) resetInternal() error {
	if !p.isInitialized {
		return errors.New("memory pool not initialized")
	}

	// Free all blocks through base pool
	basePool := p.basePools[p.config.DeviceID]
	for ptr, block := range p.blocks {
		if err := basePool.Free(block.Ptr); err != nil {
			p.logger.Printf("Warning: failed to free block during reset: %v", err)
		}
		delete(p.blocks, ptr)
	}

	// Clear block lists
	p.freeBlocks = p.freeBlocks[:0]
	p.usedBlocks = p.usedBlocks[:0]
	p.allocationHistory = p.allocationHistory[:0]
	p.actualSizeHistory = p.actualSizeHistory[:0]

	// Reset statistics (keep counters for history)
	oldAllocationCount := p.stats.AllocationCount
	oldDeallocationCount := p.stats.DeallocationCount
	oldGCCount := p.stats.GCCount
	oldDefragCount := p.stats.DefragmentationCount

	p.stats = MemoryPoolStatistics{
		DeviceID:             p.config.DeviceID,
		Strategy:             p.config.Strategy,
		PressureLevel:        PressureLow,
		AllocationCount:      oldAllocationCount,
		DeallocationCount:    oldDeallocationCount,
		GCCount:              oldGCCount,
		DefragmentationCount: oldDefragCount,
	}

	p.logger.Printf("Memory pool reset for device %d", p.config.DeviceID)
	return nil
}

// Shutdown gracefully shuts down the memory pool
func (p *AdvancedMemoryPool) Shutdown() error {
	p.mutex.Lock()

	if !p.isInitialized {
		p.mutex.Unlock()
		return errors.New("memory pool not initialized")
	}

	p.shutdownRequested = true
	p.mutex.Unlock()

	// Stop background processes (do this without holding the main mutex)
	if p.gcTicker != nil {
		p.gcTicker.Stop()
		// Signal shutdown but don't close channel if already closed
		select {
		case p.gcDone <- true:
		default:
		}
		p.gcTicker = nil
	}

	if p.defragTicker != nil {
		p.defragTicker.Stop()
		// Signal shutdown but don't close channel if already closed
		select {
		case p.defragDone <- true:
		default:
		}
		p.defragTicker = nil
	}

	// Now acquire lock for final cleanup
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// Reset the pool (using internal method since we already hold the mutex)
	if err := p.resetInternal(); err != nil {
		return fmt.Errorf("failed to reset pool during shutdown: %w", err)
	}

	p.isInitialized = false
	p.logger.Printf("Memory pool shutdown completed for device %d", p.config.DeviceID)
	return nil
}

// AddPressureHandler adds a memory pressure change handler
func (p *AdvancedMemoryPool) AddPressureHandler(handler MemoryPressureHandler) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.pressureHandlers = append(p.pressureHandlers, handler)
}

// AddGCHandler adds a garbage collection completion handler
func (p *AdvancedMemoryPool) AddGCHandler(handler GCHandler) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.gcHandlers = append(p.gcHandlers, handler)
}

// GetConfig returns the pool configuration
func (p *AdvancedMemoryPool) GetConfig() PoolConfig {
	return p.config
}

// IsInitialized returns whether the pool is initialized
func (p *AdvancedMemoryPool) IsInitialized() bool {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.isInitialized
}
