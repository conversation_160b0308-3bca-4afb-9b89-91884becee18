package gpu

import "testing"

func TestIsolationManager_IsolateRelease(t *testing.T) {
	mgr := NewIsolationManager(SecurityConfig{EnableIsolation: true})
	id := "workload1"
	err := mgr.IsolateWorkload(id)
	if err != nil {
		t.<PERSON><PERSON>("IsolateWorkload error: %v", err)
	}
	status, err := mgr.GetIsolationStatus(id)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetIsolationStatus error: %v", err)
	}
	if status != "isolated" {
		t.<PERSON><PERSON><PERSON>("Expected status 'isolated', got '%s'", status)
	}
	err = mgr.ReleaseIsolation(id)
	if err != nil {
		t.<PERSON><PERSON>("ReleaseIsolation error: %v", err)
	}
}
