package gpu

import (
	"context"
	"crypto/sha256"
	"fmt"
	"log"
	"math"
	"sort"
	"strings"
	"sync"
	"time"
)

// ClusterFaultTolerance manages fault tolerance across the GPU cluster
type ClusterFaultTolerance struct {
	distributor *ClusterWorkloadDistributor
	logger      *log.Logger
	mu          sync.RWMutex
	config      FaultToleranceConfig
	metrics     *FaultToleranceMetrics

	// Fault tolerance state
	nodeStates     map[string]*NodeHealthState
	failureHistory map[string][]FailureEvent
	isRunning      bool
	stopChan       chan struct{}
	wg             sync.WaitGroup

	// Recovery mechanisms
	recoveryQueue chan *RecoveryTask

	// Enhanced heartbeat system
	heartbeatManager *HeartbeatManager
	adaptiveTimeouts *AdaptiveTimeoutManager

	// Checkpointing system
	checkpointManager *CheckpointManager

	// Partial result handling
	resultAggregator *PartialResultAggregator

	// Work reassignment coordination
	migrationCoordinator *TaskMigrationCoordinator
}

// HeartbeatManager manages adaptive heartbeat monitoring
type HeartbeatManager struct {
	nodeHeartbeats map[string]*NodeHeartbeatState
	heartbeatChan  chan *Heartbeat
	config         HeartbeatConfig
	logger         *log.Logger
	mu             sync.RWMutex
	phiDetector    *PhiAccrualDetector
}

// NodeHeartbeatState tracks heartbeat state for a node
type NodeHeartbeatState struct {
	NodeID          string          `json:"node_id"`
	LastHeartbeat   time.Time       `json:"last_heartbeat"`
	HeartbeatCount  int64           `json:"heartbeat_count"`
	AverageInterval time.Duration   `json:"average_interval"`
	IntervalHistory []time.Duration `json:"interval_history"`
	MissedBeats     int             `json:"missed_beats"`
	NetworkLatency  time.Duration   `json:"network_latency"`
	JitterVariance  float64         `json:"jitter_variance"`
	SuspicionLevel  float64         `json:"suspicion_level"`
}

// Heartbeat represents a heartbeat message
type Heartbeat struct {
	NodeID         string                 `json:"node_id"`
	Timestamp      time.Time              `json:"timestamp"`
	SequenceNumber int64                  `json:"sequence_number"`
	NodeStatus     NodeHealthStatus       `json:"node_status"`
	GPUUtilization map[string]float64     `json:"gpu_utilization"`
	MemoryUsage    map[string]float64     `json:"memory_usage"`
	ActiveTasks    []string               `json:"active_tasks"`
	QueueLength    int                    `json:"queue_length"`
	SystemMetrics  SystemMetrics          `json:"system_metrics"`
	NetworkMetrics NetworkMetrics         `json:"network_metrics"`
	CustomData     map[string]interface{} `json:"custom_data"`
}

// SystemMetrics contains system-level metrics
type SystemMetrics struct {
	CPUUsage         float64       `json:"cpu_usage"`
	MemoryUsage      float64       `json:"memory_usage"`
	DiskUsage        float64       `json:"disk_usage"`
	Temperature      float64       `json:"temperature"`
	PowerConsumption float64       `json:"power_consumption"`
	Uptime           time.Duration `json:"uptime"`
}

// NetworkMetrics contains network-level metrics
type NetworkMetrics struct {
	Bandwidth   float64       `json:"bandwidth"`
	Latency     time.Duration `json:"latency"`
	PacketLoss  float64       `json:"packet_loss"`
	Throughput  float64       `json:"throughput"`
	Connections int           `json:"connections"`
}

// HeartbeatConfig configures heartbeat behavior
type HeartbeatConfig struct {
	BaseInterval        time.Duration `json:"base_interval"`
	MaxInterval         time.Duration `json:"max_interval"`
	MinInterval         time.Duration `json:"min_interval"`
	AdaptiveEnabled     bool          `json:"adaptive_enabled"`
	PhiThreshold        float64       `json:"phi_threshold"`
	IntervalHistorySize int           `json:"interval_history_size"`
	JitterTolerance     float64       `json:"jitter_tolerance"`
	MissedBeatThreshold int           `json:"missed_beat_threshold"`
}

// PhiAccrualDetector implements the Phi Accrual failure detector
type PhiAccrualDetector struct {
	threshold       float64
	windowSize      int
	intervalHistory map[string][]time.Duration
	mu              sync.RWMutex
}

// AdaptiveTimeoutManager manages adaptive timeout calculations
type AdaptiveTimeoutManager struct {
	baseTimeout   time.Duration
	timeoutFactor float64
	nodeLatencies map[string]time.Duration
	mu            sync.RWMutex
}

// CheckpointManager manages task checkpointing and recovery
type CheckpointManager struct {
	storage      CheckpointStorage
	config       CheckpointConfig
	activeChecks map[string]*CheckpointOperation
	logger       *log.Logger
	mu           sync.RWMutex
}

// CheckpointStorage interface for checkpoint persistence
type CheckpointStorage interface {
	StoreCheckpoint(checkpoint *Checkpoint) error
	LoadCheckpoint(taskID string, version int) (*Checkpoint, error)
	ListCheckpoints(taskID string) ([]*CheckpointMetadata, error)
	DeleteCheckpoint(taskID string, version int) error
	GetLatestCheckpoint(taskID string) (*Checkpoint, error)
}

// Checkpoint represents a task checkpoint
type Checkpoint struct {
	TaskID         string             `json:"task_id"`
	Version        int                `json:"version"`
	Timestamp      time.Time          `json:"timestamp"`
	NodeID         string             `json:"node_id"`
	State          CheckpointState    `json:"state"`
	Metadata       CheckpointMetadata `json:"metadata"`
	Data           []byte             `json:"data"`
	Incremental    bool               `json:"incremental"`
	ParentHash     string             `json:"parent_hash,omitempty"`
	Hash           string             `json:"hash"`
	CompressedSize int64              `json:"compressed_size"`
	OriginalSize   int64              `json:"original_size"`
}

// CheckpointState represents the execution state at checkpoint
type CheckpointState struct {
	Progress       float64                `json:"progress"`
	Phase          string                 `json:"phase"`
	IterationCount int64                  `json:"iteration_count"`
	ElapsedTime    time.Duration          `json:"elapsed_time"`
	GPUMemoryState map[string][]byte      `json:"gpu_memory_state"`
	Variables      map[string]interface{} `json:"variables"`
	Dependencies   []string               `json:"dependencies"`
	NextOperations []string               `json:"next_operations"`
}

// CheckpointMetadata contains checkpoint metadata
type CheckpointMetadata struct {
	TaskID      string    `json:"task_id"`
	Version     int       `json:"version"`
	Timestamp   time.Time `json:"timestamp"`
	NodeID      string    `json:"node_id"`
	Size        int64     `json:"size"`
	Hash        string    `json:"hash"`
	Incremental bool      `json:"incremental"`
	Description string    `json:"description"`
	Tags        []string  `json:"tags"`
}

// CheckpointConfig configures checkpointing behavior
type CheckpointConfig struct {
	Enabled             bool          `json:"enabled"`
	Interval            time.Duration `json:"interval"`
	MaxVersions         int           `json:"max_versions"`
	CompressionEnabled  bool          `json:"compression_enabled"`
	IncrementalEnabled  bool          `json:"incremental_enabled"`
	ReplicationFactor   int           `json:"replication_factor"`
	StoragePath         string        `json:"storage_path"`
	MaxCheckpointSize   int64         `json:"max_checkpoint_size"`
	AsyncCheckpointing  bool          `json:"async_checkpointing"`
	VerificationEnabled bool          `json:"verification_enabled"`
}

// CheckpointOperation represents an ongoing checkpoint operation
type CheckpointOperation struct {
	TaskID    string    `json:"task_id"`
	StartTime time.Time `json:"start_time"`
	Progress  float64   `json:"progress"`
	Phase     string    `json:"phase"`
	Error     error     `json:"error,omitempty"`
}

// PartialResultAggregator manages partial result collection and aggregation
type PartialResultAggregator struct {
	results     map[string]*TaskResults
	aggregators map[string]ResultAggregatorFunc
	validators  map[string]ResultValidatorFunc
	config      ResultAggregationConfig
	logger      *log.Logger
	mu          sync.RWMutex
}

// TaskResults holds partial results for a task
type TaskResults struct {
	TaskID           string                 `json:"task_id"`
	PartialResults   []*PartialResult       `json:"partial_results"`
	ExpectedParts    int                    `json:"expected_parts"`
	ReceivedParts    int                    `json:"received_parts"`
	LastUpdate       time.Time              `json:"last_update"`
	AggregatedResult interface{}            `json:"aggregated_result,omitempty"`
	Status           ResultStatus           `json:"status"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// PartialResult represents a partial computation result
type PartialResult struct {
	TaskID       string                 `json:"task_id"`
	PartID       string                 `json:"part_id"`
	NodeID       string                 `json:"node_id"`
	Timestamp    time.Time              `json:"timestamp"`
	Data         interface{}            `json:"data"`
	Metadata     map[string]interface{} `json:"metadata"`
	Progress     float64                `json:"progress"`
	Hash         string                 `json:"hash"`
	Size         int64                  `json:"size"`
	Dependencies []string               `json:"dependencies"`
}

// ResultStatus represents the status of result aggregation
type ResultStatus string

const (
	ResultStatusPending    ResultStatus = "pending"
	ResultStatusPartial    ResultStatus = "partial"
	ResultStatusComplete   ResultStatus = "complete"
	ResultStatusFailed     ResultStatus = "failed"
	ResultStatusValidating ResultStatus = "validating"
)

// ResultAggregatorFunc defines how to aggregate partial results
type ResultAggregatorFunc func(results []*PartialResult) (interface{}, error)

// ResultValidatorFunc defines how to validate aggregated results
type ResultValidatorFunc func(result interface{}) error

// ResultAggregationConfig configures result aggregation behavior
type ResultAggregationConfig struct {
	Enabled            bool          `json:"enabled"`
	TimeoutDuration    time.Duration `json:"timeout_duration"`
	ValidationEnabled  bool          `json:"validation_enabled"`
	MaxResultSize      int64         `json:"max_result_size"`
	CompressionEnabled bool          `json:"compression_enabled"`
	PersistenceEnabled bool          `json:"persistence_enabled"`
	ReplicationFactor  int           `json:"replication_factor"`
}

// TaskMigrationCoordinator manages task migration between nodes
type TaskMigrationCoordinator struct {
	migrations map[string]*MigrationOperation
	config     MigrationConfig
	logger     *log.Logger
	mu         sync.RWMutex
}

// MigrationOperation represents an ongoing task migration
type MigrationOperation struct {
	TaskID          string                 `json:"task_id"`
	SourceNodeID    string                 `json:"source_node_id"`
	TargetNodeID    string                 `json:"target_node_id"`
	StartTime       time.Time              `json:"start_time"`
	Phase           MigrationPhase         `json:"phase"`
	Progress        float64                `json:"progress"`
	CheckpointUsed  bool                   `json:"checkpoint_used"`
	DataTransferred int64                  `json:"data_transferred"`
	Error           error                  `json:"error,omitempty"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// MigrationPhase represents the current phase of migration
type MigrationPhase string

const (
	MigrationPhaseInitializing  MigrationPhase = "initializing"
	MigrationPhaseCheckpointing MigrationPhase = "checkpointing"
	MigrationPhaseTransferring  MigrationPhase = "transferring"
	MigrationPhaseValidating    MigrationPhase = "validating"
	MigrationPhaseStarting      MigrationPhase = "starting"
	MigrationPhaseCompleted     MigrationPhase = "completed"
	MigrationPhaseFailed        MigrationPhase = "failed"
)

// MigrationConfig configures task migration behavior
type MigrationConfig struct {
	Enabled                 bool          `json:"enabled"`
	MaxConcurrentMigrations int           `json:"max_concurrent_migrations"`
	TimeoutDuration         time.Duration `json:"timeout_duration"`
	RetryAttempts           int           `json:"retry_attempts"`
	VerificationEnabled     bool          `json:"verification_enabled"`
	PreemptiveEnabled       bool          `json:"preemptive_enabled"`
	LoadBalancingEnabled    bool          `json:"load_balancing_enabled"`
}

// FaultToleranceConfig contains configuration for fault tolerance
type FaultToleranceConfig struct {
	HealthCheckInterval     time.Duration `json:"health_check_interval"`
	NodeTimeoutDuration     time.Duration `json:"node_timeout_duration"`
	MaxConsecutiveFailures  int           `json:"max_consecutive_failures"`
	RecoveryAttemptInterval time.Duration `json:"recovery_attempt_interval"`
	MaxRecoveryAttempts     int           `json:"max_recovery_attempts"`
	EnableAutoRecovery      bool          `json:"enable_auto_recovery"`
	EnableTaskMigration     bool          `json:"enable_task_migration"`
	FailureWindowSize       time.Duration `json:"failure_window_size"`
	NodeQuarantineDuration  time.Duration `json:"node_quarantine_duration"`

	// Enhanced configurations
	HeartbeatConfig         HeartbeatConfig         `json:"heartbeat_config"`
	CheckpointConfig        CheckpointConfig        `json:"checkpoint_config"`
	ResultAggregationConfig ResultAggregationConfig `json:"result_aggregation_config"`
	MigrationConfig         MigrationConfig         `json:"migration_config"`
}

// NodeHealthState tracks the health state of a cluster node
type NodeHealthState struct {
	NodeID              string           `json:"node_id"`
	Status              NodeHealthStatus `json:"status"`
	LastHealthCheck     time.Time        `json:"last_health_check"`
	LastSuccessfulCheck time.Time        `json:"last_successful_check"`
	ConsecutiveFailures int              `json:"consecutive_failures"`
	TotalFailures       int              `json:"total_failures"`
	HealthScore         float64          `json:"health_score"`
	ResponseTime        time.Duration    `json:"response_time"`
	QuarantinedUntil    time.Time        `json:"quarantined_until,omitempty"`
	RecoveryAttempts    int              `json:"recovery_attempts"`
	LastRecoveryAttempt time.Time        `json:"last_recovery_attempt"`
	FailureReasons      []string         `json:"failure_reasons"`
}

// NodeHealthStatus represents the health status of a node
type NodeHealthStatus string

const (
	NodeHealthHealthy     NodeHealthStatus = "healthy"
	NodeHealthDegraded    NodeHealthStatus = "degraded"
	NodeHealthUnhealthy   NodeHealthStatus = "unhealthy"
	NodeHealthQuarantined NodeHealthStatus = "quarantined"
	NodeHealthRecovering  NodeHealthStatus = "recovering"
	NodeHealthUnknown     NodeHealthStatus = "unknown"
)

// FailureEvent represents a failure event
type FailureEvent struct {
	Timestamp     time.Time       `json:"timestamp"`
	NodeID        string          `json:"node_id"`
	FailureType   FailureType     `json:"failure_type"`
	Reason        string          `json:"reason"`
	Severity      FailureSeverity `json:"severity"`
	TasksAffected []string        `json:"tasks_affected"`
	RecoveryTime  time.Duration   `json:"recovery_time,omitempty"`
}

// FailureType represents different types of failures
type FailureType string

const (
	FailureTypeNetwork  FailureType = "network"
	FailureTypeGPU      FailureType = "gpu"
	FailureTypeSystem   FailureType = "system"
	FailureTypeTask     FailureType = "task"
	FailureTypeTimeout  FailureType = "timeout"
	FailureTypeResource FailureType = "resource"
	FailureTypeUnknown  FailureType = "unknown"
)

// FailureSeverity represents the severity of a failure
type FailureSeverity string

const (
	FailureSeverityLow      FailureSeverity = "low"
	FailureSeverityMedium   FailureSeverity = "medium"
	FailureSeverityHigh     FailureSeverity = "high"
	FailureSeverityCritical FailureSeverity = "critical"
)

// RecoveryTask represents a recovery operation
type RecoveryTask struct {
	NodeID         string       `json:"node_id"`
	RecoveryType   RecoveryType `json:"recovery_type"`
	Priority       int          `json:"priority"`
	CreatedAt      time.Time    `json:"created_at"`
	TasksToMigrate []string     `json:"tasks_to_migrate,omitempty"`
	RetryCount     int          `json:"retry_count"`
}

// RecoveryType represents different types of recovery operations
type RecoveryType string

const (
	RecoveryTypeHealthCheck   RecoveryType = "health_check"
	RecoveryTypeTaskMigration RecoveryType = "task_migration"
	RecoveryTypeNodeRestart   RecoveryType = "node_restart"
	RecoveryTypeQuarantine    RecoveryType = "quarantine"
	RecoveryTypeReintegration RecoveryType = "reintegration"
)

// FaultToleranceMetrics tracks fault tolerance performance
type FaultToleranceMetrics struct {
	TotalFailures       int64         `json:"total_failures"`
	RecoveredFailures   int64         `json:"recovered_failures"`
	UnrecoveredFailures int64         `json:"unrecovered_failures"`
	AverageRecoveryTime time.Duration `json:"average_recovery_time"`
	TasksMigrated       int64         `json:"tasks_migrated"`
	NodesQuarantined    int64         `json:"nodes_quarantined"`
	NodesRecovered      int64         `json:"nodes_recovered"`
	ClusterAvailability float64       `json:"cluster_availability"`
	LastUpdated         time.Time     `json:"last_updated"`
}

// DefaultFaultToleranceConfig returns default fault tolerance configuration
func DefaultFaultToleranceConfig() FaultToleranceConfig {
	return FaultToleranceConfig{
		HealthCheckInterval:     time.Second * 30,
		NodeTimeoutDuration:     time.Minute * 2,
		MaxConsecutiveFailures:  3,
		RecoveryAttemptInterval: time.Minute * 5,
		MaxRecoveryAttempts:     5,
		EnableAutoRecovery:      true,
		EnableTaskMigration:     true,
		FailureWindowSize:       time.Hour,
		NodeQuarantineDuration:  time.Hour * 2,

		// Enhanced configurations
		HeartbeatConfig: HeartbeatConfig{
			BaseInterval:        time.Second * 10,
			MaxInterval:         time.Second * 60,
			MinInterval:         time.Second * 5,
			AdaptiveEnabled:     true,
			PhiThreshold:        8.0,
			IntervalHistorySize: 100,
			JitterTolerance:     0.2,
			MissedBeatThreshold: 3,
		},
		CheckpointConfig: CheckpointConfig{
			Enabled:             true,
			Interval:            time.Minute * 5,
			MaxVersions:         10,
			CompressionEnabled:  true,
			IncrementalEnabled:  true,
			ReplicationFactor:   2,
			StoragePath:         "/tmp/neuralmeter_checkpoints",
			MaxCheckpointSize:   100 * 1024 * 1024, // 100MB
			AsyncCheckpointing:  true,
			VerificationEnabled: true,
		},
		ResultAggregationConfig: ResultAggregationConfig{
			Enabled:            true,
			TimeoutDuration:    time.Minute * 10,
			ValidationEnabled:  true,
			MaxResultSize:      50 * 1024 * 1024, // 50MB
			CompressionEnabled: true,
			PersistenceEnabled: true,
			ReplicationFactor:  2,
		},
		MigrationConfig: MigrationConfig{
			Enabled:                 true,
			MaxConcurrentMigrations: 3,
			TimeoutDuration:         time.Minute * 30,
			RetryAttempts:           3,
			VerificationEnabled:     true,
			PreemptiveEnabled:       false,
			LoadBalancingEnabled:    true,
		},
	}
}

// NewClusterFaultTolerance creates a new cluster fault tolerance manager
func NewClusterFaultTolerance(distributor *ClusterWorkloadDistributor, logger *log.Logger) (*ClusterFaultTolerance, error) {
	if logger == nil {
		logger = log.Default()
	}

	config := DefaultFaultToleranceConfig()

	// Initialize heartbeat manager
	heartbeatManager := NewHeartbeatManager(config.HeartbeatConfig, logger)

	// Initialize adaptive timeout manager
	adaptiveTimeouts := &AdaptiveTimeoutManager{
		baseTimeout:   config.NodeTimeoutDuration,
		timeoutFactor: 2.0,
		nodeLatencies: make(map[string]time.Duration),
	}

	// Initialize checkpoint storage and manager
	checkpointStorage, err := NewFileCheckpointStorage(config.CheckpointConfig.StoragePath)
	if err != nil {
		return nil, fmt.Errorf("failed to create checkpoint storage: %w", err)
	}
	checkpointManager := NewCheckpointManager(checkpointStorage, config.CheckpointConfig, logger)

	// Initialize partial result aggregator
	resultAggregator := NewPartialResultAggregator(config.ResultAggregationConfig, logger)

	// Register default aggregator and validator
	resultAggregator.RegisterAggregator("default", func(results []*PartialResult) (interface{}, error) {
		// Simple concatenation aggregator as default
		var combined []interface{}
		for _, result := range results {
			combined = append(combined, result.Data)
		}
		return combined, nil
	})

	resultAggregator.RegisterValidator("default", func(result interface{}) error {
		// Basic validation - ensure result is not nil
		if result == nil {
			return fmt.Errorf("aggregated result is nil")
		}
		return nil
	})

	// Initialize task migration coordinator
	migrationCoordinator := NewTaskMigrationCoordinator(config.MigrationConfig, logger)

	return &ClusterFaultTolerance{
		distributor:          distributor,
		logger:               logger,
		config:               config,
		nodeStates:           make(map[string]*NodeHealthState),
		failureHistory:       make(map[string][]FailureEvent),
		metrics:              &FaultToleranceMetrics{},
		recoveryQueue:        make(chan *RecoveryTask, 100),
		heartbeatManager:     heartbeatManager,
		adaptiveTimeouts:     adaptiveTimeouts,
		checkpointManager:    checkpointManager,
		resultAggregator:     resultAggregator,
		migrationCoordinator: migrationCoordinator,
	}, nil
}

// Start begins fault tolerance monitoring
func (cft *ClusterFaultTolerance) Start(ctx context.Context) error {
	cft.mu.Lock()
	defer cft.mu.Unlock()

	if cft.isRunning {
		return fmt.Errorf("fault tolerance already running")
	}

	cft.isRunning = true
	cft.stopChan = make(chan struct{})

	// Start health monitoring loop
	cft.wg.Add(1)
	go cft.healthMonitoringLoop(ctx)

	// Start recovery processing loop
	cft.wg.Add(1)
	go cft.recoveryProcessingLoop(ctx)

	// Start metrics update loop
	cft.wg.Add(1)
	go cft.metricsUpdateLoop(ctx)

	cft.logger.Printf("Cluster fault tolerance started")
	return nil
}

// Stop stops fault tolerance monitoring
func (cft *ClusterFaultTolerance) Stop() error {
	cft.mu.Lock()
	defer cft.mu.Unlock()

	if !cft.isRunning {
		return fmt.Errorf("fault tolerance not running")
	}

	close(cft.stopChan)
	cft.wg.Wait()
	cft.isRunning = false

	cft.logger.Printf("Cluster fault tolerance stopped")
	return nil
}

// healthMonitoringLoop continuously monitors node health
func (cft *ClusterFaultTolerance) healthMonitoringLoop(ctx context.Context) {
	defer cft.wg.Done()

	ticker := time.NewTicker(cft.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-cft.stopChan:
			return
		case <-ticker.C:
			cft.performHealthChecks(ctx)
		}
	}
}

// recoveryProcessingLoop processes recovery tasks
func (cft *ClusterFaultTolerance) recoveryProcessingLoop(ctx context.Context) {
	defer cft.wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		case <-cft.stopChan:
			return
		case recoveryTask := <-cft.recoveryQueue:
			cft.processRecoveryTask(ctx, recoveryTask)
		}
	}
}

// performHealthChecks checks the health of all cluster nodes
func (cft *ClusterFaultTolerance) performHealthChecks(ctx context.Context) {
	nodes := cft.distributor.resourceDiscovery.GetActiveNodes()

	for _, node := range nodes {
		cft.checkNodeHealth(ctx, node)
	}

	// Check for nodes that haven't been seen recently
	cft.checkMissingNodes()
}

// checkNodeHealth performs health check for a specific node
func (cft *ClusterFaultTolerance) checkNodeHealth(ctx context.Context, node *ClusterNode) {
	cft.mu.Lock()
	defer cft.mu.Unlock()

	nodeState, exists := cft.nodeStates[node.ID]
	if !exists {
		nodeState = &NodeHealthState{
			NodeID:              node.ID,
			Status:              NodeHealthUnknown,
			LastHealthCheck:     time.Now(),
			LastSuccessfulCheck: time.Now(),
		}
		cft.nodeStates[node.ID] = nodeState
	}

	// Perform health check
	healthy, reason, responseTime := cft.performNodeHealthCheck(ctx, node)

	nodeState.LastHealthCheck = time.Now()
	nodeState.ResponseTime = responseTime

	if healthy {
		cft.handleHealthyNode(nodeState)
	} else {
		cft.handleUnhealthyNode(nodeState, reason)
	}

	// Update health score
	cft.calculateHealthScore(nodeState)
}

// performNodeHealthCheck performs the actual health check
func (cft *ClusterFaultTolerance) performNodeHealthCheck(ctx context.Context, node *ClusterNode) (bool, string, time.Duration) {
	startTime := time.Now()

	// Check if node is responding
	if node.Status != NodeStatusActive {
		return false, "node not active", time.Since(startTime)
	}

	// Check GPU health
	for _, gpu := range node.GPUDevices {
		if gpu.Status != GPUStatusAvailable && gpu.Status != GPUStatusBusy {
			return false, fmt.Sprintf("GPU %s unhealthy: %s", gpu.DeviceID, gpu.Status), time.Since(startTime)
		}
	}

	// Check system resources
	if node.SystemInfo != nil {
		// Check memory usage
		memoryUsage := 1.0 - (float64(node.SystemInfo.AvailableMemory) / float64(node.SystemInfo.TotalMemory))
		if memoryUsage > 0.95 {
			return false, "system memory critically low", time.Since(startTime)
		}

		// Check load average
		loadThreshold := float64(node.SystemInfo.CPUCount) * 2.0
		if node.SystemInfo.LoadAverage > loadThreshold {
			return false, "system load too high", time.Since(startTime)
		}
	}

	// Check network connectivity
	responseTime := time.Since(startTime)
	if responseTime > cft.config.NodeTimeoutDuration {
		return false, "network timeout", responseTime
	}

	return true, "", responseTime
}

// handleHealthyNode handles a healthy node
func (cft *ClusterFaultTolerance) handleHealthyNode(nodeState *NodeHealthState) {
	nodeState.LastSuccessfulCheck = time.Now()

	// Reset consecutive failures if node was previously unhealthy
	if nodeState.ConsecutiveFailures > 0 {
		cft.logger.Printf("Node %s recovered after %d consecutive failures",
			nodeState.NodeID, nodeState.ConsecutiveFailures)
		nodeState.ConsecutiveFailures = 0
		cft.metrics.NodesRecovered++
	}

	// Update status based on current state
	if nodeState.Status == NodeHealthQuarantined {
		if time.Now().After(nodeState.QuarantinedUntil) {
			nodeState.Status = NodeHealthRecovering
			cft.scheduleRecovery(nodeState.NodeID, RecoveryTypeReintegration)
		}
	} else {
		nodeState.Status = NodeHealthHealthy
	}
}

// handleUnhealthyNode handles an unhealthy node
func (cft *ClusterFaultTolerance) handleUnhealthyNode(nodeState *NodeHealthState, reason string) {
	nodeState.ConsecutiveFailures++
	nodeState.TotalFailures++
	nodeState.FailureReasons = append(nodeState.FailureReasons, reason)

	// Record failure event
	failureEvent := FailureEvent{
		Timestamp:     time.Now(),
		NodeID:        nodeState.NodeID,
		FailureType:   cft.classifyFailure(reason),
		Reason:        reason,
		Severity:      cft.assessFailureSeverity(nodeState),
		TasksAffected: cft.getAffectedTasks(nodeState.NodeID),
	}

	cft.recordFailureEvent(failureEvent)

	// Update node status
	if nodeState.ConsecutiveFailures >= cft.config.MaxConsecutiveFailures {
		nodeState.Status = NodeHealthUnhealthy

		// Quarantine node if configured
		if cft.config.EnableAutoRecovery {
			cft.quarantineNode(nodeState)
		}
	} else {
		nodeState.Status = NodeHealthDegraded
	}

	// Schedule recovery if enabled
	if cft.config.EnableAutoRecovery {
		cft.scheduleRecovery(nodeState.NodeID, RecoveryTypeHealthCheck)
	}

	cft.logger.Printf("Node %s unhealthy: %s (consecutive failures: %d)",
		nodeState.NodeID, reason, nodeState.ConsecutiveFailures)
}

// quarantineNode quarantines an unhealthy node
func (cft *ClusterFaultTolerance) quarantineNode(nodeState *NodeHealthState) {
	nodeState.Status = NodeHealthQuarantined
	nodeState.QuarantinedUntil = time.Now().Add(cft.config.NodeQuarantineDuration)
	cft.metrics.NodesQuarantined++

	cft.logger.Printf("Node %s quarantined until %v", nodeState.NodeID, nodeState.QuarantinedUntil)

	// Migrate tasks if enabled
	if cft.config.EnableTaskMigration {
		cft.scheduleRecovery(nodeState.NodeID, RecoveryTypeTaskMigration)
	}
}

// scheduleRecovery schedules a recovery task
func (cft *ClusterFaultTolerance) scheduleRecovery(nodeID string, recoveryType RecoveryType) {
	recoveryTask := &RecoveryTask{
		NodeID:       nodeID,
		RecoveryType: recoveryType,
		Priority:     cft.getRecoveryPriority(recoveryType),
		CreatedAt:    time.Now(),
	}

	if recoveryType == RecoveryTypeTaskMigration {
		recoveryTask.TasksToMigrate = cft.getAffectedTasks(nodeID)
	}

	select {
	case cft.recoveryQueue <- recoveryTask:
		cft.logger.Printf("Scheduled %s recovery for node %s", recoveryType, nodeID)
	default:
		cft.logger.Printf("Recovery queue full, dropping recovery task for node %s", nodeID)
	}
}

// processRecoveryTask processes a recovery task
func (cft *ClusterFaultTolerance) processRecoveryTask(ctx context.Context, task *RecoveryTask) {
	cft.mu.Lock()
	nodeState := cft.nodeStates[task.NodeID]
	cft.mu.Unlock()

	if nodeState == nil {
		cft.logger.Printf("Node %s not found for recovery", task.NodeID)
		return
	}

	switch task.RecoveryType {
	case RecoveryTypeHealthCheck:
		cft.performRecoveryHealthCheck(ctx, task, nodeState)
	case RecoveryTypeTaskMigration:
		cft.performTaskMigration(ctx, task, nodeState)
	case RecoveryTypeReintegration:
		cft.performNodeReintegration(ctx, task, nodeState)
	default:
		cft.logger.Printf("Unknown recovery type: %s", task.RecoveryType)
	}
}

// performRecoveryHealthCheck performs a recovery health check
func (cft *ClusterFaultTolerance) performRecoveryHealthCheck(ctx context.Context, task *RecoveryTask, nodeState *NodeHealthState) {
	// Check if enough time has passed since last recovery attempt
	if time.Since(nodeState.LastRecoveryAttempt) < cft.config.RecoveryAttemptInterval {
		return
	}

	nodeState.LastRecoveryAttempt = time.Now()
	nodeState.RecoveryAttempts++

	// Try to get node information
	nodes := cft.distributor.resourceDiscovery.GetActiveNodes()
	var targetNode *ClusterNode
	for _, node := range nodes {
		if node.ID == task.NodeID {
			targetNode = node
			break
		}
	}

	if targetNode != nil {
		healthy, reason, _ := cft.performNodeHealthCheck(ctx, targetNode)
		if healthy {
			cft.handleHealthyNode(nodeState)
			cft.logger.Printf("Recovery health check successful for node %s", task.NodeID)
		} else {
			cft.logger.Printf("Recovery health check failed for node %s: %s", task.NodeID, reason)
		}
	} else {
		cft.logger.Printf("Node %s not found during recovery health check", task.NodeID)
	}
}

// performTaskMigration performs task migration from a failed node
func (cft *ClusterFaultTolerance) performTaskMigration(ctx context.Context, task *RecoveryTask, nodeState *NodeHealthState) {
	if !cft.config.EnableTaskMigration {
		return
	}

	cft.logger.Printf("Migrating %d tasks from node %s", len(task.TasksToMigrate), task.NodeID)

	for _, taskID := range task.TasksToMigrate {
		cft.logger.Printf("Migrating task %s from failed node %s", taskID, task.NodeID)
		cft.metrics.TasksMigrated++
	}
}

func (cft *ClusterFaultTolerance) performNodeReintegration(ctx context.Context, task *RecoveryTask, nodeState *NodeHealthState) {
	cft.logger.Printf("Reintegrating node %s into cluster", task.NodeID)
	nodeState.Status = NodeHealthHealthy
}

// Helper functions
func (cft *ClusterFaultTolerance) classifyFailure(reason string) FailureType {
	switch {
	case strings.Contains(reason, "network") || strings.Contains(reason, "timeout"):
		return FailureTypeNetwork
	case strings.Contains(reason, "GPU") || strings.Contains(reason, "gpu"):
		return FailureTypeGPU
	case strings.Contains(reason, "memory") || strings.Contains(reason, "load"):
		return FailureTypeSystem
	default:
		return FailureTypeUnknown
	}
}

func (cft *ClusterFaultTolerance) assessFailureSeverity(nodeState *NodeHealthState) FailureSeverity {
	if nodeState.ConsecutiveFailures >= cft.config.MaxConsecutiveFailures {
		return FailureSeverityCritical
	} else if nodeState.ConsecutiveFailures >= 2 {
		return FailureSeverityHigh
	} else if nodeState.ConsecutiveFailures >= 1 {
		return FailureSeverityMedium
	}
	return FailureSeverityLow
}

func (cft *ClusterFaultTolerance) getAffectedTasks(nodeID string) []string {
	// This would query the scheduler for tasks running on the node
	return []string{}
}

func (cft *ClusterFaultTolerance) getRecoveryPriority(recoveryType RecoveryType) int {
	switch recoveryType {
	case RecoveryTypeTaskMigration:
		return 1 // Highest priority
	case RecoveryTypeHealthCheck:
		return 2
	case RecoveryTypeReintegration:
		return 3
	default:
		return 4
	}
}

func (cft *ClusterFaultTolerance) recordFailureEvent(event FailureEvent) {
	if cft.failureHistory[event.NodeID] == nil {
		cft.failureHistory[event.NodeID] = make([]FailureEvent, 0)
	}

	cft.failureHistory[event.NodeID] = append(cft.failureHistory[event.NodeID], event)
	cft.metrics.TotalFailures++

	// Keep only recent history
	cutoff := time.Now().Add(-cft.config.FailureWindowSize)
	validHistory := make([]FailureEvent, 0)
	for _, e := range cft.failureHistory[event.NodeID] {
		if e.Timestamp.After(cutoff) {
			validHistory = append(validHistory, e)
		}
	}
	cft.failureHistory[event.NodeID] = validHistory
}

func (cft *ClusterFaultTolerance) calculateHealthScore(nodeState *NodeHealthState) {
	// Simple health score calculation
	baseScore := 1.0

	// Penalize consecutive failures
	failurePenalty := float64(nodeState.ConsecutiveFailures) * 0.2

	// Penalize slow response times
	responsePenalty := 0.0
	if nodeState.ResponseTime > time.Second {
		responsePenalty = float64(nodeState.ResponseTime.Seconds()) * 0.1
	}

	nodeState.HealthScore = baseScore - failurePenalty - responsePenalty
	if nodeState.HealthScore < 0 {
		nodeState.HealthScore = 0
	}
}

func (cft *ClusterFaultTolerance) checkMissingNodes() {
	now := time.Now()
	for nodeID, nodeState := range cft.nodeStates {
		if now.Sub(nodeState.LastHealthCheck) > cft.config.NodeTimeoutDuration*2 {
			cft.logger.Printf("Node %s appears to be missing (last seen: %v)",
				nodeID, nodeState.LastHealthCheck)
			nodeState.Status = NodeHealthUnknown
		}
	}
}

func (cft *ClusterFaultTolerance) metricsUpdateLoop(ctx context.Context) {
	defer cft.wg.Done()

	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-cft.stopChan:
			return
		case <-ticker.C:
			cft.updateMetrics()
		}
	}
}

func (cft *ClusterFaultTolerance) updateMetrics() {
	cft.mu.Lock()
	defer cft.mu.Unlock()

	// Calculate cluster availability
	totalNodes := len(cft.nodeStates)
	healthyNodes := 0

	for _, nodeState := range cft.nodeStates {
		if nodeState.Status == NodeHealthHealthy || nodeState.Status == NodeHealthDegraded {
			healthyNodes++
		}
	}

	if totalNodes > 0 {
		cft.metrics.ClusterAvailability = float64(healthyNodes) / float64(totalNodes)
	}

	cft.metrics.LastUpdated = time.Now()
}

// GetMetrics returns current fault tolerance metrics
func (cft *ClusterFaultTolerance) GetMetrics() FaultToleranceMetrics {
	cft.mu.RLock()
	defer cft.mu.RUnlock()
	return *cft.metrics
}

// GetNodeStates returns current node health states
func (cft *ClusterFaultTolerance) GetNodeStates() map[string]*NodeHealthState {
	cft.mu.RLock()
	defer cft.mu.RUnlock()

	result := make(map[string]*NodeHealthState)
	for k, v := range cft.nodeStates {
		result[k] = v
	}
	return result
}

// ===== ENHANCED HEARTBEAT SYSTEM =====

// NewHeartbeatManager creates a new heartbeat manager
func NewHeartbeatManager(config HeartbeatConfig, logger *log.Logger) *HeartbeatManager {
	if logger == nil {
		logger = log.Default()
	}

	return &HeartbeatManager{
		nodeHeartbeats: make(map[string]*NodeHeartbeatState),
		heartbeatChan:  make(chan *Heartbeat, 1000),
		config:         config,
		logger:         logger,
		phiDetector:    NewPhiAccrualDetector(config.PhiThreshold, config.IntervalHistorySize),
	}
}

// ProcessHeartbeat processes an incoming heartbeat
func (hm *HeartbeatManager) ProcessHeartbeat(heartbeat *Heartbeat) {
	hm.mu.Lock()
	defer hm.mu.Unlock()

	nodeState, exists := hm.nodeHeartbeats[heartbeat.NodeID]
	if !exists {
		nodeState = &NodeHeartbeatState{
			NodeID:          heartbeat.NodeID,
			IntervalHistory: make([]time.Duration, 0, hm.config.IntervalHistorySize),
		}
		hm.nodeHeartbeats[heartbeat.NodeID] = nodeState
	}

	now := time.Now()
	var interval time.Duration

	// Calculate interval if we have a previous heartbeat
	if !nodeState.LastHeartbeat.IsZero() {
		interval = now.Sub(nodeState.LastHeartbeat)
		nodeState.IntervalHistory = append(nodeState.IntervalHistory, interval)

		// Keep only recent intervals
		if len(nodeState.IntervalHistory) > hm.config.IntervalHistorySize {
			nodeState.IntervalHistory = nodeState.IntervalHistory[1:]
		}

		// Update average interval
		nodeState.AverageInterval = hm.calculateAverageInterval(nodeState.IntervalHistory)

		// Calculate jitter variance
		nodeState.JitterVariance = hm.calculateJitterVariance(nodeState.IntervalHistory, nodeState.AverageInterval)

		// Reset missed beats counter
		nodeState.MissedBeats = 0

		// Update Phi Accrual detector
		nodeState.SuspicionLevel = hm.phiDetector.UpdateAndGetPhi(heartbeat.NodeID, interval)
	}

	nodeState.LastHeartbeat = now
	nodeState.HeartbeatCount++
	nodeState.NetworkLatency = heartbeat.NetworkMetrics.Latency

	hm.logger.Printf("Processed heartbeat from node %s (seq: %d, suspicion: %.2f)",
		heartbeat.NodeID, heartbeat.SequenceNumber, nodeState.SuspicionLevel)
}

// CheckNodeSuspicion checks if a node is suspected of failure
func (hm *HeartbeatManager) CheckNodeSuspicion(nodeID string) (bool, float64) {
	hm.mu.RLock()
	defer hm.mu.RUnlock()

	nodeState, exists := hm.nodeHeartbeats[nodeID]
	if !exists {
		return true, math.Inf(1) // Unknown node is highly suspicious
	}

	// Check for missed heartbeats
	expectedInterval := nodeState.AverageInterval
	if expectedInterval == 0 {
		expectedInterval = hm.config.BaseInterval
	}

	timeSinceLastHeartbeat := time.Since(nodeState.LastHeartbeat)
	if timeSinceLastHeartbeat > expectedInterval*2 {
		nodeState.MissedBeats++
		if nodeState.MissedBeats >= hm.config.MissedBeatThreshold {
			return true, nodeState.SuspicionLevel
		}
	}

	return nodeState.SuspicionLevel > hm.config.PhiThreshold, nodeState.SuspicionLevel
}

// GetAdaptiveTimeout calculates adaptive timeout for a node
func (hm *HeartbeatManager) GetAdaptiveTimeout(nodeID string) time.Duration {
	hm.mu.RLock()
	defer hm.mu.RUnlock()

	nodeState, exists := hm.nodeHeartbeats[nodeID]
	if !exists || !hm.config.AdaptiveEnabled {
		return hm.config.BaseInterval
	}

	// Calculate adaptive timeout based on average interval and jitter
	adaptiveTimeout := nodeState.AverageInterval
	if adaptiveTimeout == 0 {
		adaptiveTimeout = hm.config.BaseInterval
	}

	// Add jitter tolerance
	jitterMultiplier := 1.0 + nodeState.JitterVariance*hm.config.JitterTolerance
	adaptiveTimeout = time.Duration(float64(adaptiveTimeout) * jitterMultiplier)

	// Ensure timeout is within bounds
	if adaptiveTimeout < hm.config.MinInterval {
		adaptiveTimeout = hm.config.MinInterval
	}
	if adaptiveTimeout > hm.config.MaxInterval {
		adaptiveTimeout = hm.config.MaxInterval
	}

	return adaptiveTimeout
}

// Helper methods for heartbeat calculations
func (hm *HeartbeatManager) calculateAverageInterval(intervals []time.Duration) time.Duration {
	if len(intervals) == 0 {
		return 0
	}

	var total time.Duration
	for _, interval := range intervals {
		total += interval
	}
	return total / time.Duration(len(intervals))
}

func (hm *HeartbeatManager) calculateJitterVariance(intervals []time.Duration, average time.Duration) float64 {
	if len(intervals) < 2 {
		return 0
	}

	var variance float64
	for _, interval := range intervals {
		diff := float64(interval - average)
		variance += diff * diff
	}
	variance /= float64(len(intervals))

	// Return coefficient of variation (normalized variance)
	if average > 0 {
		return math.Sqrt(variance) / float64(average)
	}
	return 0
}

// ===== PHI ACCRUAL FAILURE DETECTOR =====

// NewPhiAccrualDetector creates a new Phi Accrual failure detector
func NewPhiAccrualDetector(threshold float64, windowSize int) *PhiAccrualDetector {
	return &PhiAccrualDetector{
		threshold:       threshold,
		windowSize:      windowSize,
		intervalHistory: make(map[string][]time.Duration),
	}
}

// UpdateAndGetPhi updates the detector with a new interval and returns the phi value
func (pad *PhiAccrualDetector) UpdateAndGetPhi(nodeID string, interval time.Duration) float64 {
	pad.mu.Lock()
	defer pad.mu.Unlock()

	intervals, exists := pad.intervalHistory[nodeID]
	if !exists {
		intervals = make([]time.Duration, 0, pad.windowSize)
	}

	// Add new interval
	intervals = append(intervals, interval)

	// Keep only recent intervals
	if len(intervals) > pad.windowSize {
		intervals = intervals[1:]
	}

	pad.intervalHistory[nodeID] = intervals

	// Calculate phi value
	if len(intervals) < 2 {
		return 0 // Not enough data
	}

	return pad.calculatePhi(intervals, time.Since(time.Now().Add(-interval)))
}

// calculatePhi calculates the phi value based on interval history
func (pad *PhiAccrualDetector) calculatePhi(intervals []time.Duration, timeSinceLastEvent time.Duration) float64 {
	if len(intervals) == 0 {
		return math.Inf(1)
	}

	// Calculate mean and standard deviation
	var sum time.Duration
	for _, interval := range intervals {
		sum += interval
	}
	mean := float64(sum) / float64(len(intervals))

	var variance float64
	for _, interval := range intervals {
		diff := float64(interval) - mean
		variance += diff * diff
	}
	variance /= float64(len(intervals))
	stddev := math.Sqrt(variance)

	if stddev == 0 {
		return 0
	}

	// Calculate phi using normal distribution
	y := (float64(timeSinceLastEvent) - mean) / stddev
	e := math.Exp(-y * (1.5976 + 0.070566*y*y))
	if timeSinceLastEvent > time.Duration(mean) {
		return -math.Log10(e / (1.0 + e))
	}
	return -math.Log10(1.0 - 1.0/(1.0+e))
}

// ===== CHECKPOINTING SYSTEM =====

// NewCheckpointManager creates a new checkpoint manager
func NewCheckpointManager(storage CheckpointStorage, config CheckpointConfig, logger *log.Logger) *CheckpointManager {
	if logger == nil {
		logger = log.Default()
	}

	return &CheckpointManager{
		storage:      storage,
		config:       config,
		activeChecks: make(map[string]*CheckpointOperation),
		logger:       logger,
	}
}

// CreateCheckpoint creates a checkpoint for a task
func (cm *CheckpointManager) CreateCheckpoint(taskID string, state CheckpointState, data []byte) error {
	if !cm.config.Enabled {
		return fmt.Errorf("checkpointing is disabled")
	}

	cm.mu.Lock()
	defer cm.mu.Unlock()

	// Check if checkpoint operation is already in progress
	if _, exists := cm.activeChecks[taskID]; exists {
		return fmt.Errorf("checkpoint operation already in progress for task %s", taskID)
	}

	// Create checkpoint operation
	operation := &CheckpointOperation{
		TaskID:    taskID,
		StartTime: time.Now(),
		Progress:  0.0,
		Phase:     "initializing",
	}
	cm.activeChecks[taskID] = operation

	// Get next version number
	checkpoints, err := cm.storage.ListCheckpoints(taskID)
	if err != nil {
		delete(cm.activeChecks, taskID)
		return fmt.Errorf("failed to list existing checkpoints: %w", err)
	}

	nextVersion := len(checkpoints) + 1

	// Create checkpoint
	checkpoint := &Checkpoint{
		TaskID:       taskID,
		Version:      nextVersion,
		Timestamp:    time.Now(),
		NodeID:       state.Phase, // Temporary - should get actual node ID
		State:        state,
		Data:         data,
		Incremental:  cm.config.IncrementalEnabled && len(checkpoints) > 0,
		OriginalSize: int64(len(data)),
	}

	// Calculate hash
	checkpoint.Hash = cm.calculateCheckpointHash(checkpoint)

	// Compress if enabled
	if cm.config.CompressionEnabled {
		// Compression logic would go here
		checkpoint.CompressedSize = checkpoint.OriginalSize // Placeholder
	} else {
		checkpoint.CompressedSize = checkpoint.OriginalSize
	}

	// Update operation progress
	operation.Progress = 0.5
	operation.Phase = "storing"

	// Store checkpoint
	if err := cm.storage.StoreCheckpoint(checkpoint); err != nil {
		delete(cm.activeChecks, taskID)
		return fmt.Errorf("failed to store checkpoint: %w", err)
	}

	// Cleanup old checkpoints if needed
	if cm.config.MaxVersions > 0 && len(checkpoints) >= cm.config.MaxVersions {
		cm.cleanupOldCheckpoints(taskID, checkpoints)
	}

	// Complete operation
	operation.Progress = 1.0
	operation.Phase = "completed"
	delete(cm.activeChecks, taskID)

	cm.logger.Printf("Created checkpoint for task %s (version %d, size %d bytes)",
		taskID, nextVersion, checkpoint.OriginalSize)

	return nil
}

// RestoreFromCheckpoint restores a task from its latest checkpoint
func (cm *CheckpointManager) RestoreFromCheckpoint(taskID string) (*Checkpoint, error) {
	checkpoint, err := cm.storage.GetLatestCheckpoint(taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to load latest checkpoint: %w", err)
	}

	// Verify checkpoint integrity
	if cm.config.VerificationEnabled {
		expectedHash := cm.calculateCheckpointHash(checkpoint)
		if checkpoint.Hash != expectedHash {
			return nil, fmt.Errorf("checkpoint integrity check failed for task %s", taskID)
		}
	}

	cm.logger.Printf("Restored task %s from checkpoint (version %d)", taskID, checkpoint.Version)
	return checkpoint, nil
}

// calculateCheckpointHash calculates a hash for checkpoint integrity
func (cm *CheckpointManager) calculateCheckpointHash(checkpoint *Checkpoint) string {
	hasher := sha256.New()
	hasher.Write([]byte(checkpoint.TaskID))
	hasher.Write([]byte(fmt.Sprintf("%d", checkpoint.Version)))
	hasher.Write(checkpoint.Data)
	return fmt.Sprintf("%x", hasher.Sum(nil))
}

// cleanupOldCheckpoints removes old checkpoints beyond the configured limit
func (cm *CheckpointManager) cleanupOldCheckpoints(taskID string, checkpoints []*CheckpointMetadata) {
	if len(checkpoints) <= cm.config.MaxVersions {
		return
	}

	// Sort by version (oldest first)
	sort.Slice(checkpoints, func(i, j int) bool {
		return checkpoints[i].Version < checkpoints[j].Version
	})

	// Delete oldest checkpoints
	toDelete := len(checkpoints) - cm.config.MaxVersions + 1
	for i := 0; i < toDelete; i++ {
		if err := cm.storage.DeleteCheckpoint(taskID, checkpoints[i].Version); err != nil {
			cm.logger.Printf("Failed to delete old checkpoint %s v%d: %v",
				taskID, checkpoints[i].Version, err)
		}
	}
}

// ===== PARTIAL RESULT AGGREGATION =====

// NewPartialResultAggregator creates a new partial result aggregator
func NewPartialResultAggregator(config ResultAggregationConfig, logger *log.Logger) *PartialResultAggregator {
	if logger == nil {
		logger = log.Default()
	}

	return &PartialResultAggregator{
		results:     make(map[string]*TaskResults),
		aggregators: make(map[string]ResultAggregatorFunc),
		validators:  make(map[string]ResultValidatorFunc),
		config:      config,
		logger:      logger,
	}
}

// RegisterAggregator registers a result aggregator for a task type
func (pra *PartialResultAggregator) RegisterAggregator(taskType string, aggregator ResultAggregatorFunc) {
	pra.mu.Lock()
	defer pra.mu.Unlock()
	pra.aggregators[taskType] = aggregator
}

// RegisterValidator registers a result validator for a task type
func (pra *PartialResultAggregator) RegisterValidator(taskType string, validator ResultValidatorFunc) {
	pra.mu.Lock()
	defer pra.mu.Unlock()
	pra.validators[taskType] = validator
}

// AddPartialResult adds a partial result for aggregation
func (pra *PartialResultAggregator) AddPartialResult(result *PartialResult) error {
	if !pra.config.Enabled {
		return fmt.Errorf("result aggregation is disabled")
	}

	pra.mu.Lock()
	defer pra.mu.Unlock()

	taskResults, exists := pra.results[result.TaskID]
	if !exists {
		taskResults = &TaskResults{
			TaskID:         result.TaskID,
			PartialResults: make([]*PartialResult, 0),
			Status:         ResultStatusPending,
			Metadata:       make(map[string]interface{}),
		}
		pra.results[result.TaskID] = taskResults
	}

	// Add the partial result
	taskResults.PartialResults = append(taskResults.PartialResults, result)
	taskResults.ReceivedParts++
	taskResults.LastUpdate = time.Now()
	taskResults.Status = ResultStatusPartial

	pra.logger.Printf("Added partial result for task %s (part %s, progress %.2f%%)",
		result.TaskID, result.PartID, result.Progress*100)

	// Check if we have all expected parts
	if taskResults.ExpectedParts > 0 && taskResults.ReceivedParts >= taskResults.ExpectedParts {
		return pra.aggregateResults(taskResults)
	}

	return nil
}

// aggregateResults aggregates partial results into final result
func (pra *PartialResultAggregator) aggregateResults(taskResults *TaskResults) error {
	taskResults.Status = ResultStatusValidating

	// Find appropriate aggregator
	aggregator, exists := pra.aggregators["default"] // Default aggregator
	if !exists {
		return fmt.Errorf("no aggregator found for task %s", taskResults.TaskID)
	}

	// Aggregate results
	aggregatedResult, err := aggregator(taskResults.PartialResults)
	if err != nil {
		taskResults.Status = ResultStatusFailed
		return fmt.Errorf("failed to aggregate results for task %s: %w", taskResults.TaskID, err)
	}

	taskResults.AggregatedResult = aggregatedResult

	// Validate if validator is available
	if validator, exists := pra.validators["default"]; exists && pra.config.ValidationEnabled {
		if err := validator(aggregatedResult); err != nil {
			taskResults.Status = ResultStatusFailed
			return fmt.Errorf("result validation failed for task %s: %w", taskResults.TaskID, err)
		}
	}

	taskResults.Status = ResultStatusComplete
	pra.logger.Printf("Successfully aggregated results for task %s", taskResults.TaskID)

	return nil
}

// GetTaskResults returns the current results for a task
func (pra *PartialResultAggregator) GetTaskResults(taskID string) (*TaskResults, bool) {
	pra.mu.RLock()
	defer pra.mu.RUnlock()

	results, exists := pra.results[taskID]
	return results, exists
}

// ===== TASK MIGRATION COORDINATION =====

// NewTaskMigrationCoordinator creates a new task migration coordinator
func NewTaskMigrationCoordinator(config MigrationConfig, logger *log.Logger) *TaskMigrationCoordinator {
	if logger == nil {
		logger = log.Default()
	}

	return &TaskMigrationCoordinator{
		migrations: make(map[string]*MigrationOperation),
		config:     config,
		logger:     logger,
	}
}

// MigrateTask migrates a task from source to target node
func (tmc *TaskMigrationCoordinator) MigrateTask(taskID, sourceNodeID, targetNodeID string, useCheckpoint bool) error {
	if !tmc.config.Enabled {
		return fmt.Errorf("task migration is disabled")
	}

	tmc.mu.Lock()
	defer tmc.mu.Unlock()

	// Check if migration is already in progress
	if _, exists := tmc.migrations[taskID]; exists {
		return fmt.Errorf("migration already in progress for task %s", taskID)
	}

	// Check concurrent migration limit
	if len(tmc.migrations) >= tmc.config.MaxConcurrentMigrations {
		return fmt.Errorf("maximum concurrent migrations reached")
	}

	// Create migration operation
	migration := &MigrationOperation{
		TaskID:         taskID,
		SourceNodeID:   sourceNodeID,
		TargetNodeID:   targetNodeID,
		StartTime:      time.Now(),
		Phase:          MigrationPhaseInitializing,
		Progress:       0.0,
		CheckpointUsed: useCheckpoint,
		Metadata:       make(map[string]interface{}),
	}
	tmc.migrations[taskID] = migration

	tmc.logger.Printf("Starting migration of task %s from %s to %s (checkpoint: %v)",
		taskID, sourceNodeID, targetNodeID, useCheckpoint)

	// Perform migration phases
	go tmc.performMigration(migration)

	return nil
}

// performMigration performs the actual migration process
func (tmc *TaskMigrationCoordinator) performMigration(migration *MigrationOperation) {
	defer func() {
		tmc.mu.Lock()
		delete(tmc.migrations, migration.TaskID)
		tmc.mu.Unlock()
	}()

	// Phase 1: Checkpointing (if enabled)
	if migration.CheckpointUsed {
		migration.Phase = MigrationPhaseCheckpointing
		migration.Progress = 0.1

		// Checkpoint logic would go here
		tmc.logger.Printf("Creating checkpoint for task %s", migration.TaskID)
		time.Sleep(100 * time.Millisecond) // Simulate checkpointing
	}

	// Phase 2: Data Transfer
	migration.Phase = MigrationPhaseTransferring
	migration.Progress = 0.3

	// Transfer logic would go here
	tmc.logger.Printf("Transferring data for task %s", migration.TaskID)
	time.Sleep(200 * time.Millisecond)      // Simulate data transfer
	migration.DataTransferred = 1024 * 1024 // 1MB example

	// Phase 3: Validation
	if tmc.config.VerificationEnabled {
		migration.Phase = MigrationPhaseValidating
		migration.Progress = 0.7

		// Validation logic would go here
		tmc.logger.Printf("Validating migration for task %s", migration.TaskID)
		time.Sleep(50 * time.Millisecond) // Simulate validation
	}

	// Phase 4: Starting on target node
	migration.Phase = MigrationPhaseStarting
	migration.Progress = 0.9

	// Start logic would go here
	tmc.logger.Printf("Starting task %s on target node %s", migration.TaskID, migration.TargetNodeID)
	time.Sleep(100 * time.Millisecond) // Simulate task startup

	// Complete migration
	migration.Phase = MigrationPhaseCompleted
	migration.Progress = 1.0

	tmc.logger.Printf("Successfully migrated task %s from %s to %s",
		migration.TaskID, migration.SourceNodeID, migration.TargetNodeID)
}

// GetMigrationStatus returns the status of a migration operation
func (tmc *TaskMigrationCoordinator) GetMigrationStatus(taskID string) (*MigrationOperation, bool) {
	tmc.mu.RLock()
	defer tmc.mu.RUnlock()

	migration, exists := tmc.migrations[taskID]
	return migration, exists
}

// GetActiveMigrations returns all active migration operations
func (tmc *TaskMigrationCoordinator) GetActiveMigrations() map[string]*MigrationOperation {
	tmc.mu.RLock()
	defer tmc.mu.RUnlock()

	migrations := make(map[string]*MigrationOperation)
	for k, v := range tmc.migrations {
		migrations[k] = v
	}
	return migrations
}

// ===== ENHANCED FAULT TOLERANCE GETTERS =====

// GetHeartbeatManager returns the heartbeat manager
func (cft *ClusterFaultTolerance) GetHeartbeatManager() *HeartbeatManager {
	return cft.heartbeatManager
}

// GetCheckpointManager returns the checkpoint manager
func (cft *ClusterFaultTolerance) GetCheckpointManager() *CheckpointManager {
	return cft.checkpointManager
}

// GetResultAggregator returns the partial result aggregator
func (cft *ClusterFaultTolerance) GetResultAggregator() *PartialResultAggregator {
	return cft.resultAggregator
}

// GetMigrationCoordinator returns the task migration coordinator
func (cft *ClusterFaultTolerance) GetMigrationCoordinator() *TaskMigrationCoordinator {
	return cft.migrationCoordinator
}

// GetAdaptiveTimeouts returns the adaptive timeout manager
func (cft *ClusterFaultTolerance) GetAdaptiveTimeouts() *AdaptiveTimeoutManager {
	return cft.adaptiveTimeouts
}

// GetConfig returns the current fault tolerance configuration
func (cft *ClusterFaultTolerance) GetConfig() FaultToleranceConfig {
	return cft.config
}
