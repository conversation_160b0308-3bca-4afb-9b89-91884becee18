package gpu

import (
	"context"
	"fmt"
	"log"
	"math"
	"sync"
	"time"
)

// ClusterLoadBalancer manages load balancing across cluster nodes
type ClusterLoadBalancer struct {
	distributor *ClusterWorkloadDistributor
	logger      *log.Logger
	mu          sync.RWMutex
	config      LoadBalancerConfig
	metrics     *LoadBalancerMetrics

	// Load balancing state
	nodeLoads      map[string]*NodeLoadInfo
	rebalanceTimer *time.Timer
	isRunning      bool
	stop<PERSON>han       chan struct{}
	wg             sync.WaitGroup
}

// LoadBalancerConfig contains configuration for the load balancer
type LoadBalancerConfig struct {
	RebalanceThreshold      float64       `json:"rebalance_threshold"`
	RebalanceInterval       time.Duration `json:"rebalance_interval"`
	LoadUpdateInterval      time.Duration `json:"load_update_interval"`
	MaxLoadImbalance        float64       `json:"max_load_imbalance"`
	MinMigrationBenefit     float64       `json:"min_migration_benefit"`
	MaxConcurrentMigrations int           `json:"max_concurrent_migrations"`
	EnablePredictiveScaling bool          `json:"enable_predictive_scaling"`
	ScalingWindow           time.Duration `json:"scaling_window"`
	LoadSmoothingFactor     float64       `json:"load_smoothing_factor"`
}

// NodeLoadInfo tracks load information for a cluster node
type NodeLoadInfo struct {
	NodeID              string               `json:"node_id"`
	CurrentLoad         float64              `json:"current_load"`
	PredictedLoad       float64              `json:"predicted_load"`
	Capacity            float64              `json:"capacity"`
	Utilization         float64              `json:"utilization"`
	ActiveTasks         int                  `json:"active_tasks"`
	QueuedTasks         int                  `json:"queued_tasks"`
	LoadHistory         []LoadSample         `json:"load_history"`
	LastUpdated         time.Time            `json:"last_updated"`
	IsOverloaded        bool                 `json:"is_overloaded"`
	IsUnderloaded       bool                 `json:"is_underloaded"`
	MigrationCandidates []*TaskMigrationInfo `json:"migration_candidates"`
}

// LoadSample represents a load measurement at a point in time
type LoadSample struct {
	Timestamp time.Time `json:"timestamp"`
	Load      float64   `json:"load"`
	TaskCount int       `json:"task_count"`
}

// TaskMigrationInfo contains information about a task that can be migrated
type TaskMigrationInfo struct {
	TaskID           string        `json:"task_id"`
	CurrentNodeID    string        `json:"current_node_id"`
	TargetNodeID     string        `json:"target_node_id"`
	MigrationBenefit float64       `json:"migration_benefit"`
	MigrationCost    float64       `json:"migration_cost"`
	EstimatedTime    time.Duration `json:"estimated_time"`
	Priority         int           `json:"priority"`
}

// LoadBalancerMetrics tracks load balancer performance
type LoadBalancerMetrics struct {
	TotalRebalances      int64     `json:"total_rebalances"`
	SuccessfulMigrations int64     `json:"successful_migrations"`
	FailedMigrations     int64     `json:"failed_migrations"`
	AverageImbalance     float64   `json:"average_imbalance"`
	LastRebalanceTime    time.Time `json:"last_rebalance_time"`
	LoadVariance         float64   `json:"load_variance"`
	ClusterEfficiency    float64   `json:"cluster_efficiency"`
}

// DefaultLoadBalancerConfig returns default load balancer configuration
func DefaultLoadBalancerConfig() LoadBalancerConfig {
	return LoadBalancerConfig{
		RebalanceThreshold:      0.3, // 30% load imbalance threshold
		RebalanceInterval:       time.Minute * 5,
		LoadUpdateInterval:      time.Second * 30,
		MaxLoadImbalance:        0.5, // 50% maximum acceptable imbalance
		MinMigrationBenefit:     0.1, // 10% minimum benefit to migrate
		MaxConcurrentMigrations: 3,
		EnablePredictiveScaling: true,
		ScalingWindow:           time.Hour,
		LoadSmoothingFactor:     0.7,
	}
}

// NewClusterLoadBalancer creates a new cluster load balancer
func NewClusterLoadBalancer(distributor *ClusterWorkloadDistributor, logger *log.Logger) (*ClusterLoadBalancer, error) {
	if logger == nil {
		logger = log.Default()
	}

	return &ClusterLoadBalancer{
		distributor: distributor,
		logger:      logger,
		config:      DefaultLoadBalancerConfig(),
		nodeLoads:   make(map[string]*NodeLoadInfo),
		metrics:     &LoadBalancerMetrics{},
	}, nil
}

// Start begins load balancer operation
func (clb *ClusterLoadBalancer) Start(ctx context.Context) error {
	clb.mu.Lock()
	defer clb.mu.Unlock()

	if clb.isRunning {
		return fmt.Errorf("load balancer already running")
	}

	clb.isRunning = true
	clb.stopChan = make(chan struct{})

	// Start load monitoring loop
	clb.wg.Add(1)
	go clb.loadMonitoringLoop(ctx)

	// Start rebalancing loop
	clb.wg.Add(1)
	go clb.rebalancingLoop(ctx)

	clb.logger.Printf("Cluster load balancer started")
	return nil
}

// Stop stops the load balancer
func (clb *ClusterLoadBalancer) Stop() error {
	clb.mu.Lock()
	defer clb.mu.Unlock()

	if !clb.isRunning {
		return fmt.Errorf("load balancer not running")
	}

	close(clb.stopChan)
	clb.wg.Wait()
	clb.isRunning = false

	clb.logger.Printf("Cluster load balancer stopped")
	return nil
}

// loadMonitoringLoop continuously monitors node loads
func (clb *ClusterLoadBalancer) loadMonitoringLoop(ctx context.Context) {
	defer clb.wg.Done()

	ticker := time.NewTicker(clb.config.LoadUpdateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-clb.stopChan:
			return
		case <-ticker.C:
			clb.updateNodeLoads()
		}
	}
}

// rebalancingLoop periodically checks for rebalancing opportunities
func (clb *ClusterLoadBalancer) rebalancingLoop(ctx context.Context) {
	defer clb.wg.Done()

	ticker := time.NewTicker(clb.config.RebalanceInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-clb.stopChan:
			return
		case <-ticker.C:
			clb.evaluateRebalancing(ctx)
		}
	}
}

// updateNodeLoads updates load information for all cluster nodes
func (clb *ClusterLoadBalancer) updateNodeLoads() {
	nodes := clb.distributor.resourceDiscovery.GetActiveNodes()

	clb.mu.Lock()
	defer clb.mu.Unlock()

	for _, node := range nodes {
		loadInfo := clb.calculateNodeLoad(node)
		clb.nodeLoads[node.ID] = loadInfo

		// Update load history
		clb.updateLoadHistory(loadInfo)

		// Calculate predicted load if enabled
		if clb.config.EnablePredictiveScaling {
			loadInfo.PredictedLoad = clb.predictFutureLoad(loadInfo)
		}
	}

	// Update cluster-wide metrics
	clb.updateClusterMetrics()
}

// calculateNodeLoad calculates current load for a node
func (clb *ClusterLoadBalancer) calculateNodeLoad(node *ClusterNode) *NodeLoadInfo {
	loadInfo := &NodeLoadInfo{
		NodeID:      node.ID,
		LastUpdated: time.Now(),
	}

	if node.WorkloadMetrics != nil {
		// Calculate load based on multiple factors
		taskLoad := float64(node.WorkloadMetrics.ActiveTasks) / 10.0  // Normalize to 10 max tasks
		queueLoad := float64(node.WorkloadMetrics.QueueLength) / 20.0 // Normalize to 20 max queue

		// System resource load
		var resourceLoad float64
		if node.SystemInfo != nil {
			cpuLoad := node.SystemInfo.LoadAverage / float64(node.SystemInfo.CPUCount)
			memoryLoad := 1.0 - (float64(node.SystemInfo.AvailableMemory) / float64(node.SystemInfo.TotalMemory))
			resourceLoad = (cpuLoad + memoryLoad) / 2.0
		}

		// GPU utilization load
		var gpuLoad float64
		if len(node.GPUDevices) > 0 {
			totalUtilization := 0.0
			for _, gpu := range node.GPUDevices {
				if gpu.Utilization != nil {
					totalUtilization += gpu.Utilization.ComputePercent / 100.0
				}
			}
			gpuLoad = totalUtilization / float64(len(node.GPUDevices))
		}

		// Weighted combination of load factors
		loadInfo.CurrentLoad = (taskLoad*0.4 + queueLoad*0.2 + resourceLoad*0.2 + gpuLoad*0.2)
		loadInfo.ActiveTasks = node.WorkloadMetrics.ActiveTasks
		loadInfo.QueuedTasks = node.WorkloadMetrics.QueueLength

		// Calculate capacity (simplified)
		loadInfo.Capacity = float64(len(node.GPUDevices)) * 2.0 // 2 tasks per GPU
		if loadInfo.Capacity > 0 {
			loadInfo.Utilization = float64(loadInfo.ActiveTasks) / loadInfo.Capacity
		}

		// Determine if node is overloaded or underloaded
		loadInfo.IsOverloaded = loadInfo.CurrentLoad > 0.8
		loadInfo.IsUnderloaded = loadInfo.CurrentLoad < 0.3
	}

	return loadInfo
}

// updateLoadHistory maintains load history for trend analysis
func (clb *ClusterLoadBalancer) updateLoadHistory(loadInfo *NodeLoadInfo) {
	sample := LoadSample{
		Timestamp: time.Now(),
		Load:      loadInfo.CurrentLoad,
		TaskCount: loadInfo.ActiveTasks,
	}

	loadInfo.LoadHistory = append(loadInfo.LoadHistory, sample)

	// Keep only recent history (last hour)
	cutoff := time.Now().Add(-time.Hour)
	validHistory := make([]LoadSample, 0)
	for _, sample := range loadInfo.LoadHistory {
		if sample.Timestamp.After(cutoff) {
			validHistory = append(validHistory, sample)
		}
	}
	loadInfo.LoadHistory = validHistory
}

// predictFutureLoad predicts future load based on historical trends
func (clb *ClusterLoadBalancer) predictFutureLoad(loadInfo *NodeLoadInfo) float64 {
	if len(loadInfo.LoadHistory) < 3 {
		return loadInfo.CurrentLoad // Not enough data for prediction
	}

	// Simple linear trend prediction
	history := loadInfo.LoadHistory
	n := len(history)

	// Calculate trend over last few samples
	recentSamples := 5
	if n < recentSamples {
		recentSamples = n
	}

	startIdx := n - recentSamples
	trend := (history[n-1].Load - history[startIdx].Load) / float64(recentSamples-1)

	// Predict load 5 minutes into the future
	predictedLoad := loadInfo.CurrentLoad + (trend * 5.0)

	// Apply smoothing
	smoothingFactor := clb.config.LoadSmoothingFactor
	return smoothingFactor*loadInfo.CurrentLoad + (1-smoothingFactor)*predictedLoad
}

// evaluateRebalancing checks if rebalancing is needed and performs it
func (clb *ClusterLoadBalancer) evaluateRebalancing(ctx context.Context) {
	clb.mu.RLock()

	// Calculate load imbalance
	imbalance := clb.calculateLoadImbalance()

	clb.mu.RUnlock()

	if imbalance > clb.config.RebalanceThreshold {
		clb.logger.Printf("Load imbalance detected: %.2f%%, initiating rebalancing", imbalance*100)
		clb.performRebalancing(ctx)
	}
}

// calculateLoadImbalance calculates the current load imbalance across nodes
func (clb *ClusterLoadBalancer) calculateLoadImbalance() float64 {
	if len(clb.nodeLoads) < 2 {
		return 0.0
	}

	var minLoad, maxLoad float64 = math.Inf(1), math.Inf(-1)
	var totalLoad float64
	nodeCount := 0

	for _, loadInfo := range clb.nodeLoads {
		load := loadInfo.CurrentLoad
		if clb.config.EnablePredictiveScaling {
			load = loadInfo.PredictedLoad
		}

		if load < minLoad {
			minLoad = load
		}
		if load > maxLoad {
			maxLoad = load
		}
		totalLoad += load
		nodeCount++
	}

	if nodeCount == 0 || maxLoad == 0 {
		return 0.0
	}

	// Calculate imbalance as (max - min) / max
	return (maxLoad - minLoad) / maxLoad
}

// performRebalancing performs load rebalancing across the cluster
func (clb *ClusterLoadBalancer) performRebalancing(ctx context.Context) {
	clb.mu.Lock()
	defer clb.mu.Unlock()

	// Find migration opportunities
	migrations := clb.findMigrationOpportunities()

	if len(migrations) == 0 {
		clb.logger.Printf("No beneficial migrations found")
		return
	}

	// Execute migrations (up to max concurrent)
	maxMigrations := clb.config.MaxConcurrentMigrations
	if len(migrations) < maxMigrations {
		maxMigrations = len(migrations)
	}

	successfulMigrations := 0
	for i := 0; i < maxMigrations; i++ {
		if clb.executeMigration(ctx, migrations[i]) {
			successfulMigrations++
		}
	}

	// Update metrics
	clb.metrics.TotalRebalances++
	clb.metrics.SuccessfulMigrations += int64(successfulMigrations)
	clb.metrics.FailedMigrations += int64(maxMigrations - successfulMigrations)
	clb.metrics.LastRebalanceTime = time.Now()

	clb.logger.Printf("Rebalancing completed: %d/%d migrations successful",
		successfulMigrations, maxMigrations)
}

// findMigrationOpportunities identifies tasks that can be migrated for better balance
func (clb *ClusterLoadBalancer) findMigrationOpportunities() []*TaskMigrationInfo {
	migrations := make([]*TaskMigrationInfo, 0)

	// Find overloaded and underloaded nodes
	var overloadedNodes, underloadedNodes []*NodeLoadInfo
	for _, loadInfo := range clb.nodeLoads {
		if loadInfo.IsOverloaded {
			overloadedNodes = append(overloadedNodes, loadInfo)
		} else if loadInfo.IsUnderloaded {
			underloadedNodes = append(underloadedNodes, loadInfo)
		}
	}

	// For each overloaded node, find migration targets
	for _, overloaded := range overloadedNodes {
		for _, underloaded := range underloadedNodes {
			migration := clb.evaluateMigration(overloaded, underloaded)
			if migration != nil && migration.MigrationBenefit > clb.config.MinMigrationBenefit {
				migrations = append(migrations, migration)
			}
		}
	}

	return migrations
}

// evaluateMigration evaluates the benefit of migrating a task between nodes
func (clb *ClusterLoadBalancer) evaluateMigration(source, target *NodeLoadInfo) *TaskMigrationInfo {
	// Simplified migration evaluation
	// In practice, this would consider specific tasks and their characteristics

	loadDifference := source.CurrentLoad - target.CurrentLoad
	if loadDifference < clb.config.MinMigrationBenefit {
		return nil
	}

	// Calculate migration benefit and cost
	benefit := loadDifference * 0.5 // Moving half the load difference
	cost := 0.1                     // Fixed migration cost (simplified)

	if benefit <= cost {
		return nil
	}

	return &TaskMigrationInfo{
		TaskID:           fmt.Sprintf("task-from-%s", source.NodeID), // Simplified
		CurrentNodeID:    source.NodeID,
		TargetNodeID:     target.NodeID,
		MigrationBenefit: benefit,
		MigrationCost:    cost,
		EstimatedTime:    time.Minute * 2, // Simplified
		Priority:         1,
	}
}

// executeMigration executes a task migration
func (clb *ClusterLoadBalancer) executeMigration(ctx context.Context, migration *TaskMigrationInfo) bool {
	// This is a placeholder for actual migration logic
	// In practice, this would coordinate with the scheduler and task execution system

	clb.logger.Printf("Executing migration: %s from %s to %s (benefit: %.2f)",
		migration.TaskID, migration.CurrentNodeID, migration.TargetNodeID, migration.MigrationBenefit)

	// Simulate migration time
	time.Sleep(time.Millisecond * 100)

	// For now, assume migrations succeed
	return true
}

// updateClusterMetrics updates cluster-wide load balancing metrics
func (clb *ClusterLoadBalancer) updateClusterMetrics() {
	if len(clb.nodeLoads) == 0 {
		return
	}

	// Calculate load variance
	var totalLoad float64
	loads := make([]float64, 0, len(clb.nodeLoads))

	for _, loadInfo := range clb.nodeLoads {
		load := loadInfo.CurrentLoad
		totalLoad += load
		loads = append(loads, load)
	}

	avgLoad := totalLoad / float64(len(loads))

	var variance float64
	for _, load := range loads {
		variance += math.Pow(load-avgLoad, 2)
	}
	variance /= float64(len(loads))

	clb.metrics.LoadVariance = variance
	clb.metrics.AverageImbalance = clb.calculateLoadImbalance()

	// Calculate cluster efficiency (inverse of load variance)
	clb.metrics.ClusterEfficiency = math.Max(0, 1.0-variance)
}

// GetMetrics returns current load balancer metrics
func (clb *ClusterLoadBalancer) GetMetrics() LoadBalancerMetrics {
	clb.mu.RLock()
	defer clb.mu.RUnlock()
	return *clb.metrics
}

// GetNodeLoads returns current node load information
func (clb *ClusterLoadBalancer) GetNodeLoads() map[string]*NodeLoadInfo {
	clb.mu.RLock()
	defer clb.mu.RUnlock()

	// Return a copy to avoid concurrent access issues
	result := make(map[string]*NodeLoadInfo)
	for k, v := range clb.nodeLoads {
		result[k] = v
	}
	return result
}
