package gpu

import (
	"context"
	"fmt"
	"log"
	"math"
	"sync"
	"time"
)

// WorkloadDistributor handles distribution of tasks across multiple GPU devices
type WorkloadDistributor struct {
	deviceManager    *MultiDeviceManager
	strategy         LoadBalancingStrategy
	logger           *log.Logger
	mu               sync.RWMutex
	lastRoundRobin   int
	distributionHist []DistributionRecord
	config           DistributorConfig
}

// DistributorConfig configures the workload distributor behavior
type DistributorConfig struct {
	MemoryWeightFactor        float64       `json:"memory_weight_factor"`      // 0.0 - 1.0
	ComputeWeightFactor       float64       `json:"compute_weight_factor"`     // 0.0 - 1.0
	UtilizationWeightFactor   float64       `json:"utilization_weight_factor"` // 0.0 - 1.0
	TaskHistoryWeight         float64       `json:"task_history_weight"`       // 0.0 - 1.0
	RebalancingThreshold      float64       `json:"rebalancing_threshold"`     // 0.0 - 1.0
	MinTasksBeforeRebalancing int           `json:"min_tasks_before_rebalancing"`
	MaxHistorySize            int           `json:"max_history_size"`
	UpdateInterval            time.Duration `json:"update_interval"`
	EnableAdaptiveWeighting   bool          `json:"enable_adaptive_weighting"`
}

// WorkloadTask represents a task to be distributed
type WorkloadTask struct {
	ID                string                 `json:"id"`
	Priority          TaskPriority           `json:"priority"`
	EstimatedDuration time.Duration          `json:"estimated_duration"`
	MemoryRequirement int64                  `json:"memory_requirement"` // bytes
	ComputeIntensity  ComputeIntensity       `json:"compute_intensity"`
	Dependencies      []string               `json:"dependencies"`
	DeviceHint        string                 `json:"device_hint"` // Preferred device ID
	Metadata          map[string]interface{} `json:"metadata"`
	CreatedAt         time.Time              `json:"created_at"`
}

// TaskPriority defines the urgency of a task
type TaskPriority int

const (
	PriorityLow TaskPriority = iota
	PriorityNormal
	PriorityHigh
	PriorityCritical
)

// ComputeIntensity defines the computational requirements of a task
type ComputeIntensity int

const (
	ComputeLight ComputeIntensity = iota
	ComputeModerate
	ComputeHeavy
	ComputeIntensive
)

// DistributionRecord tracks task assignment history
type DistributionRecord struct {
	TaskID      string    `json:"task_id"`
	DeviceID    string    `json:"device_id"`
	Strategy    string    `json:"strategy"`
	Score       float64   `json:"score"`
	AssignedAt  time.Time `json:"assigned_at"`
	CompletedAt time.Time `json:"completed_at"`
	Success     bool      `json:"success"`
}

// DeviceScore represents a device's suitability for a task
type DeviceScore struct {
	DeviceID         string  `json:"device_id"`
	Score            float64 `json:"score"`
	MemoryScore      float64 `json:"memory_score"`
	ComputeScore     float64 `json:"compute_score"`
	UtilizationScore float64 `json:"utilization_score"`
	HistoryScore     float64 `json:"history_score"`
	Available        bool    `json:"available"`
	Reason           string  `json:"reason"`
}

// NewWorkloadDistributor creates a new workload distributor
func NewWorkloadDistributor(deviceManager *MultiDeviceManager, strategy LoadBalancingStrategy, logger *log.Logger) *WorkloadDistributor {
	if logger == nil {
		logger = log.Default()
	}

	return &WorkloadDistributor{
		deviceManager:    deviceManager,
		strategy:         strategy,
		logger:           logger,
		lastRoundRobin:   -1,
		distributionHist: make([]DistributionRecord, 0),
		config:           DefaultDistributorConfig(),
	}
}

// DefaultDistributorConfig returns default configuration for the distributor
func DefaultDistributorConfig() DistributorConfig {
	return DistributorConfig{
		MemoryWeightFactor:        0.3,
		ComputeWeightFactor:       0.3,
		UtilizationWeightFactor:   0.3,
		TaskHistoryWeight:         0.1,
		RebalancingThreshold:      0.2,
		MinTasksBeforeRebalancing: 5,
		MaxHistorySize:            1000,
		UpdateInterval:            time.Second * 5,
		EnableAdaptiveWeighting:   true,
	}
}

// AssignTask distributes a task to the most suitable device based on the current strategy
func (wd *WorkloadDistributor) AssignTask(ctx context.Context, task *WorkloadTask) (*ManagedDevice, error) {
	wd.mu.Lock()
	defer wd.mu.Unlock()

	if !wd.deviceManager.IsInitialized() {
		return nil, fmt.Errorf("device manager not initialized")
	}

	devices := wd.deviceManager.GetActiveDevices()
	if len(devices) == 0 {
		return nil, fmt.Errorf("no active devices available")
	}

	var selectedDevice *ManagedDevice
	var err error

	switch wd.strategy {
	case LoadBalanceRoundRobin:
		selectedDevice, err = wd.assignRoundRobin(devices)
	case LoadBalanceMemoryBased:
		selectedDevice, err = wd.assignMemoryBased(task, devices)
	case LoadBalanceComputeBased:
		selectedDevice, err = wd.assignComputeBased(task, devices)
	case LoadBalanceDynamic:
		selectedDevice, err = wd.assignDynamic(task, devices)
	case LoadBalanceWeighted:
		selectedDevice, err = wd.assignWeighted(task, devices)
	default:
		return nil, fmt.Errorf("unknown load balancing strategy: %v", wd.strategy)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to assign task using %s strategy: %w", wd.strategy, err)
	}

	if selectedDevice == nil {
		return nil, fmt.Errorf("no suitable device found for task %s", task.ID)
	}

	// Record the assignment
	record := DistributionRecord{
		TaskID:     task.ID,
		DeviceID:   selectedDevice.Device.ID,
		Strategy:   wd.strategy.String(),
		AssignedAt: time.Now(),
	}

	wd.addDistributionRecord(record)
	wd.logger.Printf("Assigned task %s to device %s using %s strategy",
		task.ID, selectedDevice.Device.ID, wd.strategy)

	return selectedDevice, nil
}

// assignRoundRobin implements round-robin distribution
func (wd *WorkloadDistributor) assignRoundRobin(devices []*ManagedDevice) (*ManagedDevice, error) {
	if len(devices) == 0 {
		return nil, fmt.Errorf("no devices available")
	}

	wd.lastRoundRobin = (wd.lastRoundRobin + 1) % len(devices)
	return devices[wd.lastRoundRobin], nil
}

// assignMemoryBased implements memory-availability-based distribution
func (wd *WorkloadDistributor) assignMemoryBased(task *WorkloadTask, devices []*ManagedDevice) (*ManagedDevice, error) {
	var bestDevice *ManagedDevice
	var bestScore float64 = -1

	for _, device := range devices {
		device.mu.RLock()
		metrics := device.Metrics
		device.mu.RUnlock()

		// Calculate available memory as a fraction
		availableMemory := 1.0 - metrics.MemoryUtilization

		// Consider task memory requirement if specified
		memoryScore := availableMemory
		if task.MemoryRequirement > 0 && device.Device.MemoryTotal > 0 {
			requiredFraction := float64(task.MemoryRequirement) / float64(device.Device.MemoryTotal)
			if requiredFraction > availableMemory {
				continue // Skip devices without enough memory
			}
			// Prefer devices with more headroom after allocation
			memoryScore = availableMemory - requiredFraction
		}

		if memoryScore > bestScore {
			bestScore = memoryScore
			bestDevice = device
		}
	}

	if bestDevice == nil {
		return nil, fmt.Errorf("no device has sufficient memory for task %s", task.ID)
	}

	return bestDevice, nil
}

// assignComputeBased implements compute-capability-based distribution
func (wd *WorkloadDistributor) assignComputeBased(task *WorkloadTask, devices []*ManagedDevice) (*ManagedDevice, error) {
	var bestDevice *ManagedDevice
	var bestScore float64 = -1

	for _, device := range devices {
		device.mu.RLock()
		metrics := device.Metrics
		device.mu.RUnlock()

		// Calculate compute score based on device capability and current utilization
		computeCapability := float64(device.Device.ComputeUnits) // Higher is better
		utilization := metrics.ComputeUtilization                // Lower is better

		// Adjust score based on task compute intensity
		intensityMultiplier := 1.0
		switch task.ComputeIntensity {
		case ComputeLight:
			intensityMultiplier = 0.5 // Prefer less powerful devices
		case ComputeModerate:
			intensityMultiplier = 0.8
		case ComputeHeavy:
			intensityMultiplier = 1.2
		case ComputeIntensive:
			intensityMultiplier = 1.5 // Prefer more powerful devices
		}

		score := (computeCapability * intensityMultiplier) * (1.0 - utilization)

		if score > bestScore {
			bestScore = score
			bestDevice = device
		}
	}

	if bestDevice == nil {
		return nil, fmt.Errorf("no suitable device found for compute-based assignment")
	}

	return bestDevice, nil
}

// assignDynamic implements dynamic distribution based on real-time metrics
func (wd *WorkloadDistributor) assignDynamic(task *WorkloadTask, devices []*ManagedDevice) (*ManagedDevice, error) {
	scores := make([]DeviceScore, 0, len(devices))

	for _, device := range devices {
		score := wd.calculateDeviceScore(task, device)
		scores = append(scores, score)
	}

	// Find the best scoring available device
	var bestDevice *ManagedDevice
	var bestScore float64 = -1

	for i, score := range scores {
		if score.Available && score.Score > bestScore {
			bestScore = score.Score
			bestDevice = devices[i]
		}
	}

	if bestDevice == nil {
		return nil, fmt.Errorf("no suitable device found for dynamic assignment")
	}

	return bestDevice, nil
}

// assignWeighted implements weighted distribution using device weights
func (wd *WorkloadDistributor) assignWeighted(task *WorkloadTask, devices []*ManagedDevice) (*ManagedDevice, error) {
	var bestDevice *ManagedDevice
	var bestScore float64 = -1

	for _, device := range devices {
		device.mu.RLock()
		metrics := device.Metrics
		device.mu.RUnlock()

		// Get device weight from configuration
		deviceWeight := wd.deviceManager.config.DeviceWeights[device.Device.ID]
		if deviceWeight == 0 {
			deviceWeight = 1.0 // Default weight
		}

		// Calculate weighted score considering current utilization
		utilizationFactor := 1.0 - ((metrics.MemoryUtilization + metrics.ComputeUtilization) / 2.0)
		score := deviceWeight * utilizationFactor

		// Apply task priority multiplier
		switch task.Priority {
		case PriorityHigh:
			score *= 1.2
		case PriorityCritical:
			score *= 1.5
		}

		if score > bestScore {
			bestScore = score
			bestDevice = device
		}
	}

	if bestDevice == nil {
		return nil, fmt.Errorf("no suitable device found for weighted assignment")
	}

	return bestDevice, nil
}

// calculateDeviceScore computes a comprehensive score for device-task matching
func (wd *WorkloadDistributor) calculateDeviceScore(task *WorkloadTask, device *ManagedDevice) DeviceScore {
	device.mu.RLock()
	metrics := device.Metrics
	device.mu.RUnlock()

	score := DeviceScore{
		DeviceID:  device.Device.ID,
		Available: device.IsActive,
	}

	if !device.IsActive {
		score.Reason = "device inactive"
		return score
	}

	// Memory score (0-1, higher is better)
	availableMemory := 1.0 - metrics.MemoryUtilization
	if task.MemoryRequirement > 0 && device.Device.MemoryTotal > 0 {
		requiredFraction := float64(task.MemoryRequirement) / float64(device.Device.MemoryTotal)
		if requiredFraction > availableMemory {
			score.Available = false
			score.Reason = "insufficient memory"
			return score
		}
		score.MemoryScore = availableMemory - requiredFraction
	} else {
		score.MemoryScore = availableMemory
	}

	// Compute score (0-1, higher is better)
	computeCapability := math.Min(float64(device.Device.ComputeUnits)/128.0, 1.0) // Normalize
	computeAvailability := 1.0 - metrics.ComputeUtilization
	score.ComputeScore = (computeCapability + computeAvailability) / 2.0

	// Utilization score (0-1, higher is better for less utilized devices)
	avgUtilization := (metrics.MemoryUtilization + metrics.ComputeUtilization) / 2.0
	score.UtilizationScore = 1.0 - avgUtilization

	// History score based on past performance
	score.HistoryScore = wd.calculateHistoryScore(device.Device.ID)

	// Combine scores with weights
	score.Score = (score.MemoryScore * wd.config.MemoryWeightFactor) +
		(score.ComputeScore * wd.config.ComputeWeightFactor) +
		(score.UtilizationScore * wd.config.UtilizationWeightFactor) +
		(score.HistoryScore * wd.config.TaskHistoryWeight)

	// Apply task priority multiplier
	switch task.Priority {
	case PriorityHigh:
		score.Score *= 1.1
	case PriorityCritical:
		score.Score *= 1.2
	}

	return score
}

// calculateHistoryScore computes a score based on historical performance
func (wd *WorkloadDistributor) calculateHistoryScore(deviceID string) float64 {
	if len(wd.distributionHist) == 0 {
		return 0.5 // Neutral score when no history
	}

	var successCount, totalCount int
	var avgCompletionTime time.Duration

	for _, record := range wd.distributionHist {
		if record.DeviceID == deviceID && !record.CompletedAt.IsZero() {
			totalCount++
			if record.Success {
				successCount++
			}
			if !record.CompletedAt.IsZero() && !record.AssignedAt.IsZero() {
				avgCompletionTime += record.CompletedAt.Sub(record.AssignedAt)
			}
		}
	}

	if totalCount == 0 {
		return 0.5 // Neutral score when no relevant history
	}

	successRate := float64(successCount) / float64(totalCount)

	// Simple history score based on success rate
	// Could be enhanced with completion time analysis
	return successRate
}

// addDistributionRecord adds a distribution record to history
func (wd *WorkloadDistributor) addDistributionRecord(record DistributionRecord) {
	wd.distributionHist = append(wd.distributionHist, record)

	// Limit history size
	if len(wd.distributionHist) > wd.config.MaxHistorySize {
		wd.distributionHist = wd.distributionHist[len(wd.distributionHist)-wd.config.MaxHistorySize:]
	}
}

// CompleteTask marks a task as completed and updates distribution history
func (wd *WorkloadDistributor) CompleteTask(taskID string, success bool) error {
	wd.mu.Lock()
	defer wd.mu.Unlock()

	// Find the most recent record for this task
	for i := len(wd.distributionHist) - 1; i >= 0; i-- {
		if wd.distributionHist[i].TaskID == taskID && wd.distributionHist[i].CompletedAt.IsZero() {
			wd.distributionHist[i].CompletedAt = time.Now()
			wd.distributionHist[i].Success = success
			wd.logger.Printf("Marked task %s as completed (success: %v)", taskID, success)
			return nil
		}
	}

	return fmt.Errorf("task %s not found in distribution history", taskID)
}

// GetDistributionStatistics returns statistics about task distribution
func (wd *WorkloadDistributor) GetDistributionStatistics() map[string]interface{} {
	wd.mu.RLock()
	defer wd.mu.RUnlock()

	stats := make(map[string]interface{})
	deviceCounts := make(map[string]int)
	deviceSuccessCounts := make(map[string]int)

	for _, record := range wd.distributionHist {
		deviceCounts[record.DeviceID]++
		if record.Success {
			deviceSuccessCounts[record.DeviceID]++
		}
	}

	stats["total_tasks"] = len(wd.distributionHist)
	stats["device_distribution"] = deviceCounts
	stats["device_success_rates"] = make(map[string]float64)

	for deviceID, count := range deviceCounts {
		successCount := deviceSuccessCounts[deviceID]
		stats["device_success_rates"].(map[string]float64)[deviceID] = float64(successCount) / float64(count)
	}

	stats["current_strategy"] = wd.strategy.String()
	stats["last_round_robin"] = wd.lastRoundRobin

	return stats
}

// SetStrategy changes the distribution strategy
func (wd *WorkloadDistributor) SetStrategy(strategy LoadBalancingStrategy) {
	wd.mu.Lock()
	defer wd.mu.Unlock()

	oldStrategy := wd.strategy
	wd.strategy = strategy
	wd.logger.Printf("Changed distribution strategy from %s to %s", oldStrategy, strategy)
}

// GetStrategy returns the current distribution strategy
func (wd *WorkloadDistributor) GetStrategy() LoadBalancingStrategy {
	wd.mu.RLock()
	defer wd.mu.RUnlock()
	return wd.strategy
}

// SetConfig updates the distributor configuration
func (wd *WorkloadDistributor) SetConfig(config DistributorConfig) error {
	wd.mu.Lock()
	defer wd.mu.Unlock()

	// Validate configuration
	if err := validateDistributorConfig(config); err != nil {
		return fmt.Errorf("invalid distributor configuration: %w", err)
	}

	wd.config = config
	wd.logger.Printf("Updated distributor configuration")
	return nil
}

// GetConfig returns the current distributor configuration
func (wd *WorkloadDistributor) GetConfig() DistributorConfig {
	wd.mu.RLock()
	defer wd.mu.RUnlock()
	return wd.config
}

// validateDistributorConfig validates the distributor configuration
func validateDistributorConfig(config DistributorConfig) error {
	if config.MemoryWeightFactor < 0 || config.MemoryWeightFactor > 1 {
		return fmt.Errorf("memory weight factor must be between 0 and 1")
	}
	if config.ComputeWeightFactor < 0 || config.ComputeWeightFactor > 1 {
		return fmt.Errorf("compute weight factor must be between 0 and 1")
	}
	if config.UtilizationWeightFactor < 0 || config.UtilizationWeightFactor > 1 {
		return fmt.Errorf("utilization weight factor must be between 0 and 1")
	}
	if config.TaskHistoryWeight < 0 || config.TaskHistoryWeight > 1 {
		return fmt.Errorf("task history weight must be between 0 and 1")
	}
	if config.RebalancingThreshold < 0 || config.RebalancingThreshold > 1 {
		return fmt.Errorf("rebalancing threshold must be between 0 and 1")
	}
	if config.MinTasksBeforeRebalancing < 0 {
		return fmt.Errorf("min tasks before rebalancing must be non-negative")
	}
	if config.MaxHistorySize <= 0 {
		return fmt.Errorf("max history size must be positive")
	}
	return nil
}
