package gpu

import (
	"fmt"
	"log"
	"math"
	"math/rand"
	"time"
)

// DeepLearningModel interface for neural network models
type DeepLearningModel interface {
	Train(data []WorkloadDataPoint) error
	Predict(data []WorkloadDataPoint) (int, float64, error)
	GetAccuracy() float64
	GetModelType() string
	SaveModel(path string) error
	LoadModel(path string) error
}

// LSTMModel implements Long Short-Term Memory neural network
type LSTMModel struct {
	InputSize    int
	HiddenSize   int
	OutputSize   int
	SequenceLen  int
	LearningRate float64
	Epochs       int

	// LSTM Cell parameters
	WeightIh [][]float64 // Input-to-hidden weights [4*hidden_size x input_size]
	WeightHh [][]float64 // Hidden-to-hidden weights [4*hidden_size x hidden_size]
	BiasIh   []float64   // Input-to-hidden bias [4*hidden_size]
	BiasHh   []float64   // Hidden-to-hidden bias [4*hidden_size]

	// Output layer parameters
	WeightOut [][]float64 // Output weights [output_size x hidden_size]
	BiasOut   []float64   // Output bias [output_size]

	// Training state
	Accuracy           float64
	TrainLoss          []float64
	ValidationAccuracy float64

	// Feature engineering
	FeatureEngineer *FeatureEngineer

	// Normalization parameters
	FeatureMins []float64
	FeatureMaxs []float64
	TargetMin   float64
	TargetMax   float64
}

// GRUModel implements Gated Recurrent Unit neural network
type GRUModel struct {
	InputSize    int
	HiddenSize   int
	OutputSize   int
	SequenceLen  int
	LearningRate float64
	Epochs       int

	// GRU Cell parameters
	WeightIh [][]float64 // Input-to-hidden weights [3*hidden_size x input_size]
	WeightHh [][]float64 // Hidden-to-hidden weights [3*hidden_size x hidden_size]
	BiasIh   []float64   // Input-to-hidden bias [3*hidden_size]
	BiasHh   []float64   // Hidden-to-hidden bias [3*hidden_size]

	// Output layer parameters
	WeightOut [][]float64 // Output weights [output_size x hidden_size]
	BiasOut   []float64   // Output bias [output_size]

	// Training state
	Accuracy           float64
	TrainLoss          []float64
	ValidationAccuracy float64

	// Feature engineering
	FeatureEngineer *FeatureEngineer

	// Normalization parameters
	FeatureMins []float64
	FeatureMaxs []float64
	TargetMin   float64
	TargetMax   float64
}

// NewLSTMModel creates a new LSTM model with specified parameters
func NewLSTMModel(inputSize, hiddenSize, outputSize, sequenceLen int, learningRate float64, epochs int) *LSTMModel {
	model := &LSTMModel{
		InputSize:       inputSize,
		HiddenSize:      hiddenSize,
		OutputSize:      outputSize,
		SequenceLen:     sequenceLen,
		LearningRate:    learningRate,
		Epochs:          epochs,
		FeatureEngineer: NewFeatureEngineer(),
		TrainLoss:       make([]float64, 0),
	}

	model.initializeWeights()
	return model
}

// NewGRUModel creates a new GRU model with specified parameters
func NewGRUModel(inputSize, hiddenSize, outputSize, sequenceLen int, learningRate float64, epochs int) *GRUModel {
	model := &GRUModel{
		InputSize:       inputSize,
		HiddenSize:      hiddenSize,
		OutputSize:      outputSize,
		SequenceLen:     sequenceLen,
		LearningRate:    learningRate,
		Epochs:          epochs,
		FeatureEngineer: NewFeatureEngineer(),
		TrainLoss:       make([]float64, 0),
	}

	model.initializeWeights()
	return model
}

// Initialize weights for LSTM model using Xavier initialization
func (m *LSTMModel) initializeWeights() {
	rand.Seed(time.Now().UnixNano())

	// Initialize input-to-hidden weights (4 gates: input, forget, output, candidate)
	m.WeightIh = make([][]float64, 4*m.HiddenSize)
	for i := range m.WeightIh {
		m.WeightIh[i] = make([]float64, m.InputSize)
		for j := range m.WeightIh[i] {
			m.WeightIh[i][j] = (rand.Float64()*2 - 1) * math.Sqrt(6.0/float64(m.InputSize+m.HiddenSize))
		}
	}

	// Initialize hidden-to-hidden weights
	m.WeightHh = make([][]float64, 4*m.HiddenSize)
	for i := range m.WeightHh {
		m.WeightHh[i] = make([]float64, m.HiddenSize)
		for j := range m.WeightHh[i] {
			m.WeightHh[i][j] = (rand.Float64()*2 - 1) * math.Sqrt(6.0/float64(m.HiddenSize+m.HiddenSize))
		}
	}

	// Initialize biases
	m.BiasIh = make([]float64, 4*m.HiddenSize)
	m.BiasHh = make([]float64, 4*m.HiddenSize)

	// Initialize forget gate bias to 1 (helps with gradient flow)
	for i := m.HiddenSize; i < 2*m.HiddenSize; i++ {
		m.BiasIh[i] = 1.0
		m.BiasHh[i] = 1.0
	}

	// Initialize output layer weights
	m.WeightOut = make([][]float64, m.OutputSize)
	for i := range m.WeightOut {
		m.WeightOut[i] = make([]float64, m.HiddenSize)
		for j := range m.WeightOut[i] {
			m.WeightOut[i][j] = (rand.Float64()*2 - 1) * math.Sqrt(6.0/float64(m.HiddenSize+m.OutputSize))
		}
	}

	m.BiasOut = make([]float64, m.OutputSize)
}

// Initialize weights for GRU model using Xavier initialization
func (m *GRUModel) initializeWeights() {
	rand.Seed(time.Now().UnixNano())

	// Initialize input-to-hidden weights (3 gates: reset, update, new)
	m.WeightIh = make([][]float64, 3*m.HiddenSize)
	for i := range m.WeightIh {
		m.WeightIh[i] = make([]float64, m.InputSize)
		for j := range m.WeightIh[i] {
			m.WeightIh[i][j] = (rand.Float64()*2 - 1) * math.Sqrt(6.0/float64(m.InputSize+m.HiddenSize))
		}
	}

	// Initialize hidden-to-hidden weights
	m.WeightHh = make([][]float64, 3*m.HiddenSize)
	for i := range m.WeightHh {
		m.WeightHh[i] = make([]float64, m.HiddenSize)
		for j := range m.WeightHh[i] {
			m.WeightHh[i][j] = (rand.Float64()*2 - 1) * math.Sqrt(6.0/float64(m.HiddenSize+m.HiddenSize))
		}
	}

	// Initialize biases
	m.BiasIh = make([]float64, 3*m.HiddenSize)
	m.BiasHh = make([]float64, 3*m.HiddenSize)

	// Initialize output layer weights
	m.WeightOut = make([][]float64, m.OutputSize)
	for i := range m.WeightOut {
		m.WeightOut[i] = make([]float64, m.HiddenSize)
		for j := range m.WeightOut[i] {
			m.WeightOut[i][j] = (rand.Float64()*2 - 1) * math.Sqrt(6.0/float64(m.HiddenSize+m.OutputSize))
		}
	}

	m.BiasOut = make([]float64, m.OutputSize)
}

// Activation functions
func sigmoid(x float64) float64 {
	return 1.0 / (1.0 + math.Exp(-x))
}

func tanh(x float64) float64 {
	return math.Tanh(x)
}

func sigmoidDerivative(x float64) float64 {
	s := sigmoid(x)
	return s * (1 - s)
}

func tanhDerivative(x float64) float64 {
	t := tanh(x)
	return 1 - t*t
}

// LSTM cell forward pass
func (m *LSTMModel) lstmCellForward(input []float64, prevHidden, prevCell []float64) ([]float64, []float64) {
	// Calculate input gates
	inputGate := make([]float64, m.HiddenSize)
	forgetGate := make([]float64, m.HiddenSize)
	outputGate := make([]float64, m.HiddenSize)
	candidateGate := make([]float64, m.HiddenSize)

	for i := 0; i < m.HiddenSize; i++ {
		// Input gate
		val := m.BiasIh[i] + m.BiasHh[i]
		for j := 0; j < m.InputSize; j++ {
			val += m.WeightIh[i][j] * input[j]
		}
		for j := 0; j < m.HiddenSize; j++ {
			val += m.WeightHh[i][j] * prevHidden[j]
		}
		inputGate[i] = sigmoid(val)

		// Forget gate
		val = m.BiasIh[i+m.HiddenSize] + m.BiasHh[i+m.HiddenSize]
		for j := 0; j < m.InputSize; j++ {
			val += m.WeightIh[i+m.HiddenSize][j] * input[j]
		}
		for j := 0; j < m.HiddenSize; j++ {
			val += m.WeightHh[i+m.HiddenSize][j] * prevHidden[j]
		}
		forgetGate[i] = sigmoid(val)

		// Output gate
		val = m.BiasIh[i+2*m.HiddenSize] + m.BiasHh[i+2*m.HiddenSize]
		for j := 0; j < m.InputSize; j++ {
			val += m.WeightIh[i+2*m.HiddenSize][j] * input[j]
		}
		for j := 0; j < m.HiddenSize; j++ {
			val += m.WeightHh[i+2*m.HiddenSize][j] * prevHidden[j]
		}
		outputGate[i] = sigmoid(val)

		// Candidate gate
		val = m.BiasIh[i+3*m.HiddenSize] + m.BiasHh[i+3*m.HiddenSize]
		for j := 0; j < m.InputSize; j++ {
			val += m.WeightIh[i+3*m.HiddenSize][j] * input[j]
		}
		for j := 0; j < m.HiddenSize; j++ {
			val += m.WeightHh[i+3*m.HiddenSize][j] * prevHidden[j]
		}
		candidateGate[i] = tanh(val)
	}

	// Update cell state and hidden state
	newCell := make([]float64, m.HiddenSize)
	newHidden := make([]float64, m.HiddenSize)

	for i := 0; i < m.HiddenSize; i++ {
		newCell[i] = forgetGate[i]*prevCell[i] + inputGate[i]*candidateGate[i]
		newHidden[i] = outputGate[i] * tanh(newCell[i])
	}

	return newHidden, newCell
}

// GRU cell forward pass
func (m *GRUModel) gruCellForward(input []float64, prevHidden []float64) []float64 {
	// Calculate reset and update gates
	resetGate := make([]float64, m.HiddenSize)
	updateGate := make([]float64, m.HiddenSize)

	for i := 0; i < m.HiddenSize; i++ {
		// Reset gate
		val := m.BiasIh[i] + m.BiasHh[i]
		for j := 0; j < m.InputSize; j++ {
			val += m.WeightIh[i][j] * input[j]
		}
		for j := 0; j < m.HiddenSize; j++ {
			val += m.WeightHh[i][j] * prevHidden[j]
		}
		resetGate[i] = sigmoid(val)

		// Update gate
		val = m.BiasIh[i+m.HiddenSize] + m.BiasHh[i+m.HiddenSize]
		for j := 0; j < m.InputSize; j++ {
			val += m.WeightIh[i+m.HiddenSize][j] * input[j]
		}
		for j := 0; j < m.HiddenSize; j++ {
			val += m.WeightHh[i+m.HiddenSize][j] * prevHidden[j]
		}
		updateGate[i] = sigmoid(val)
	}

	// Calculate new gate
	newGate := make([]float64, m.HiddenSize)
	for i := 0; i < m.HiddenSize; i++ {
		val := m.BiasIh[i+2*m.HiddenSize] + m.BiasHh[i+2*m.HiddenSize]
		for j := 0; j < m.InputSize; j++ {
			val += m.WeightIh[i+2*m.HiddenSize][j] * input[j]
		}
		for j := 0; j < m.HiddenSize; j++ {
			val += m.WeightHh[i+2*m.HiddenSize][j] * (resetGate[j] * prevHidden[j])
		}
		newGate[i] = tanh(val)
	}

	// Update hidden state
	newHidden := make([]float64, m.HiddenSize)
	for i := 0; i < m.HiddenSize; i++ {
		newHidden[i] = (1-updateGate[i])*prevHidden[i] + updateGate[i]*newGate[i]
	}

	return newHidden
}

// Prepare sequences from data for training
func (m *LSTMModel) prepareSequences(data []WorkloadDataPoint) ([][]WorkloadDataPoint, []float64) {
	if len(data) < m.SequenceLen+1 {
		return nil, nil
	}

	sequences := make([][]WorkloadDataPoint, 0)
	targets := make([]float64, 0)

	for i := 0; i <= len(data)-m.SequenceLen-1; i++ {
		sequence := data[i : i+m.SequenceLen]
		target := float64(data[i+m.SequenceLen].QueueLength) // Predict next queue length

		sequences = append(sequences, sequence)
		targets = append(targets, target)
	}

	return sequences, targets
}

// Prepare sequences from data for training (GRU version)
func (m *GRUModel) prepareSequences(data []WorkloadDataPoint) ([][]WorkloadDataPoint, []float64) {
	if len(data) < m.SequenceLen+1 {
		return nil, nil
	}

	sequences := make([][]WorkloadDataPoint, 0)
	targets := make([]float64, 0)

	for i := 0; i <= len(data)-m.SequenceLen-1; i++ {
		sequence := data[i : i+m.SequenceLen]
		target := float64(data[i+m.SequenceLen].QueueLength) // Predict next queue length

		sequences = append(sequences, sequence)
		targets = append(targets, target)
	}

	return sequences, targets
}

// Normalize features for training
func (m *LSTMModel) normalizeFeatures(data []WorkloadDataPoint) []WorkloadDataPoint {
	if len(data) == 0 {
		return data
	}

	// Extract feature vectors
	featureVectors := make([][]float64, len(data))
	for i, point := range data {
		enrichedPoint := m.FeatureEngineer.ExtractAllFeatures(point, data[:i])
		featureVectors[i] = m.FeatureEngineer.ExtractFeatureVector(enrichedPoint)
	}

	// Calculate min/max for normalization
	if len(featureVectors) > 0 && len(featureVectors[0]) > 0 {
		m.FeatureMins = make([]float64, len(featureVectors[0]))
		m.FeatureMaxs = make([]float64, len(featureVectors[0]))

		// Initialize with first vector
		copy(m.FeatureMins, featureVectors[0])
		copy(m.FeatureMaxs, featureVectors[0])

		// Find min/max across all vectors
		for _, vector := range featureVectors {
			for j, val := range vector {
				if val < m.FeatureMins[j] {
					m.FeatureMins[j] = val
				}
				if val > m.FeatureMaxs[j] {
					m.FeatureMaxs[j] = val
				}
			}
		}
	}

	// Normalize target values (queue length)
	targets := make([]float64, len(data))
	for i, point := range data {
		targets[i] = float64(point.QueueLength)
	}

	if len(targets) > 0 {
		m.TargetMin = targets[0]
		m.TargetMax = targets[0]

		for _, target := range targets {
			if target < m.TargetMin {
				m.TargetMin = target
			}
			if target > m.TargetMax {
				m.TargetMax = target
			}
		}
	}

	return m.FeatureEngineer.NormalizeFeatures(data)
}

// Normalize features for training (GRU version)
func (m *GRUModel) normalizeFeatures(data []WorkloadDataPoint) []WorkloadDataPoint {
	if len(data) == 0 {
		return data
	}

	// Extract feature vectors
	featureVectors := make([][]float64, len(data))
	for i, point := range data {
		enrichedPoint := m.FeatureEngineer.ExtractAllFeatures(point, data[:i])
		featureVectors[i] = m.FeatureEngineer.ExtractFeatureVector(enrichedPoint)
	}

	// Calculate min/max for normalization
	if len(featureVectors) > 0 && len(featureVectors[0]) > 0 {
		m.FeatureMins = make([]float64, len(featureVectors[0]))
		m.FeatureMaxs = make([]float64, len(featureVectors[0]))

		// Initialize with first vector
		copy(m.FeatureMins, featureVectors[0])
		copy(m.FeatureMaxs, featureVectors[0])

		// Find min/max across all vectors
		for _, vector := range featureVectors {
			for j, val := range vector {
				if val < m.FeatureMins[j] {
					m.FeatureMins[j] = val
				}
				if val > m.FeatureMaxs[j] {
					m.FeatureMaxs[j] = val
				}
			}
		}
	}

	// Normalize target values (queue length)
	targets := make([]float64, len(data))
	for i, point := range data {
		targets[i] = float64(point.QueueLength)
	}

	if len(targets) > 0 {
		m.TargetMin = targets[0]
		m.TargetMax = targets[0]

		for _, target := range targets {
			if target < m.TargetMin {
				m.TargetMin = target
			}
			if target > m.TargetMax {
				m.TargetMax = target
			}
		}
	}

	return m.FeatureEngineer.NormalizeFeatures(data)
}

// Train the LSTM model
func (m *LSTMModel) Train(data []WorkloadDataPoint) error {
	if len(data) < m.SequenceLen+10 { // Need enough data for training
		return fmt.Errorf("insufficient training data: need at least %d points, got %d", m.SequenceLen+10, len(data))
	}

	log.Printf("Training LSTM model with %d data points", len(data))

	// Normalize features
	normalizedData := m.normalizeFeatures(data)

	// Prepare training sequences
	sequences, targets := m.prepareSequences(normalizedData)
	if len(sequences) == 0 {
		return fmt.Errorf("failed to prepare training sequences")
	}

	// Split into training and validation sets (80/20)
	splitIndex := int(0.8 * float64(len(sequences)))
	trainSequences := sequences[:splitIndex]
	trainTargets := targets[:splitIndex]
	valSequences := sequences[splitIndex:]
	valTargets := targets[splitIndex:]

	log.Printf("Training with %d sequences, validating with %d sequences", len(trainSequences), len(valSequences))

	// Training loop
	for epoch := 0; epoch < m.Epochs; epoch++ {
		totalLoss := 0.0

		// Shuffle training data
		indices := make([]int, len(trainSequences))
		for i := range indices {
			indices[i] = i
		}
		rand.Shuffle(len(indices), func(i, j int) {
			indices[i], indices[j] = indices[j], indices[i]
		})

		// Train on each sequence
		for _, idx := range indices {
			sequence := trainSequences[idx]
			target := trainTargets[idx]

			// Forward pass
			prediction, loss := m.forwardPass(sequence, target)
			totalLoss += loss

			// Backward pass (simplified gradient descent)
			m.backwardPass(sequence, target, prediction)
		}

		avgLoss := totalLoss / float64(len(trainSequences))
		m.TrainLoss = append(m.TrainLoss, avgLoss)

		// Validate every 10 epochs
		if epoch%10 == 0 {
			valAccuracy := m.validateModel(valSequences, valTargets)
			m.ValidationAccuracy = valAccuracy
			log.Printf("Epoch %d: Loss=%.4f, Validation Accuracy=%.2f%%", epoch, avgLoss, valAccuracy*100)
		}
	}

	// Final validation
	m.Accuracy = m.validateModel(valSequences, valTargets)
	log.Printf("LSTM training completed. Final accuracy: %.2f%%", m.Accuracy*100)

	return nil
}

// Train the GRU model
func (m *GRUModel) Train(data []WorkloadDataPoint) error {
	if len(data) < m.SequenceLen+10 { // Need enough data for training
		return fmt.Errorf("insufficient training data: need at least %d points, got %d", m.SequenceLen+10, len(data))
	}

	log.Printf("Training GRU model with %d data points", len(data))

	// Normalize features
	normalizedData := m.normalizeFeatures(data)

	// Prepare training sequences
	sequences, targets := m.prepareSequences(normalizedData)
	if len(sequences) == 0 {
		return fmt.Errorf("failed to prepare training sequences")
	}

	// Split into training and validation sets (80/20)
	splitIndex := int(0.8 * float64(len(sequences)))
	trainSequences := sequences[:splitIndex]
	trainTargets := targets[:splitIndex]
	valSequences := sequences[splitIndex:]
	valTargets := targets[splitIndex:]

	log.Printf("Training with %d sequences, validating with %d sequences", len(trainSequences), len(valSequences))

	// Training loop
	for epoch := 0; epoch < m.Epochs; epoch++ {
		totalLoss := 0.0

		// Shuffle training data
		indices := make([]int, len(trainSequences))
		for i := range indices {
			indices[i] = i
		}
		rand.Shuffle(len(indices), func(i, j int) {
			indices[i], indices[j] = indices[j], indices[i]
		})

		// Train on each sequence
		for _, idx := range indices {
			sequence := trainSequences[idx]
			target := trainTargets[idx]

			// Forward pass
			prediction, loss := m.forwardPass(sequence, target)
			totalLoss += loss

			// Backward pass (simplified gradient descent)
			m.backwardPass(sequence, target, prediction)
		}

		avgLoss := totalLoss / float64(len(trainSequences))
		m.TrainLoss = append(m.TrainLoss, avgLoss)

		// Validate every 10 epochs
		if epoch%10 == 0 {
			valAccuracy := m.validateModel(valSequences, valTargets)
			m.ValidationAccuracy = valAccuracy
			log.Printf("Epoch %d: Loss=%.4f, Validation Accuracy=%.2f%%", epoch, avgLoss, valAccuracy*100)
		}
	}

	// Final validation
	m.Accuracy = m.validateModel(valSequences, valTargets)
	log.Printf("GRU training completed. Final accuracy: %.2f%%", m.Accuracy*100)

	return nil
}

// Forward pass for LSTM
func (m *LSTMModel) forwardPass(sequence []WorkloadDataPoint, target float64) (float64, float64) {
	// Initialize hidden and cell states
	hidden := make([]float64, m.HiddenSize)
	cell := make([]float64, m.HiddenSize)

	// Process sequence
	for _, point := range sequence {
		enrichedPoint := m.FeatureEngineer.ExtractAllFeatures(point, sequence)
		input := m.FeatureEngineer.ExtractFeatureVector(enrichedPoint)

		// Normalize input
		if len(m.FeatureMins) > 0 && len(m.FeatureMaxs) > 0 {
			input = m.FeatureEngineer.NormalizeFeatureVector(input, m.FeatureMins, m.FeatureMaxs)
		}

		// LSTM cell forward pass
		hidden, cell = m.lstmCellForward(input, hidden, cell)
	}

	// Output layer
	output := 0.0
	for i := 0; i < m.HiddenSize; i++ {
		output += m.WeightOut[0][i]*hidden[i] + m.BiasOut[0]
	}

	// Denormalize output
	if m.TargetMax != m.TargetMin {
		output = output*(m.TargetMax-m.TargetMin) + m.TargetMin
	}

	// Calculate loss (MSE)
	loss := math.Pow(output-target, 2) / 2.0

	return output, loss
}

// Forward pass for GRU
func (m *GRUModel) forwardPass(sequence []WorkloadDataPoint, target float64) (float64, float64) {
	// Initialize hidden state
	hidden := make([]float64, m.HiddenSize)

	// Process sequence
	for _, point := range sequence {
		enrichedPoint := m.FeatureEngineer.ExtractAllFeatures(point, sequence)
		input := m.FeatureEngineer.ExtractFeatureVector(enrichedPoint)

		// Normalize input
		if len(m.FeatureMins) > 0 && len(m.FeatureMaxs) > 0 {
			input = m.FeatureEngineer.NormalizeFeatureVector(input, m.FeatureMins, m.FeatureMaxs)
		}

		// GRU cell forward pass
		hidden = m.gruCellForward(input, hidden)
	}

	// Output layer
	output := 0.0
	for i := 0; i < m.HiddenSize; i++ {
		output += m.WeightOut[0][i]*hidden[i] + m.BiasOut[0]
	}

	// Denormalize output
	if m.TargetMax != m.TargetMin {
		output = output*(m.TargetMax-m.TargetMin) + m.TargetMin
	}

	// Calculate loss (MSE)
	loss := math.Pow(output-target, 2) / 2.0

	return output, loss
}

// Simplified backward pass for LSTM (gradient descent)
func (m *LSTMModel) backwardPass(sequence []WorkloadDataPoint, target, prediction float64) {
	// Calculate output error
	outputError := prediction - target

	// Update output layer weights (simplified)
	for i := 0; i < m.HiddenSize; i++ {
		m.WeightOut[0][i] -= m.LearningRate * outputError * 0.1 // Simplified gradient
	}
	m.BiasOut[0] -= m.LearningRate * outputError

	// Update LSTM weights (simplified - in practice would use BPTT)
	for i := 0; i < len(m.WeightIh); i++ {
		for j := 0; j < len(m.WeightIh[i]); j++ {
			m.WeightIh[i][j] -= m.LearningRate * outputError * 0.001 // Very small update
		}
	}
}

// Simplified backward pass for GRU (gradient descent)
func (m *GRUModel) backwardPass(sequence []WorkloadDataPoint, target, prediction float64) {
	// Calculate output error
	outputError := prediction - target

	// Update output layer weights (simplified)
	for i := 0; i < m.HiddenSize; i++ {
		m.WeightOut[0][i] -= m.LearningRate * outputError * 0.1 // Simplified gradient
	}
	m.BiasOut[0] -= m.LearningRate * outputError

	// Update GRU weights (simplified - in practice would use BPTT)
	for i := 0; i < len(m.WeightIh); i++ {
		for j := 0; j < len(m.WeightIh[i]); j++ {
			m.WeightIh[i][j] -= m.LearningRate * outputError * 0.001 // Very small update
		}
	}
}

// Validate model performance
func (m *LSTMModel) validateModel(sequences [][]WorkloadDataPoint, targets []float64) float64 {
	if len(sequences) == 0 {
		return 0.0
	}

	correct := 0
	total := len(sequences)

	for i, sequence := range sequences {
		prediction, _ := m.forwardPass(sequence, targets[i])

		// Calculate accuracy based on percentage error
		error := math.Abs(prediction-targets[i]) / math.Max(targets[i], 1.0)
		if error < 0.1 { // Within 10% is considered correct
			correct++
		}
	}

	return float64(correct) / float64(total)
}

// Validate model performance (GRU version)
func (m *GRUModel) validateModel(sequences [][]WorkloadDataPoint, targets []float64) float64 {
	if len(sequences) == 0 {
		return 0.0
	}

	correct := 0
	total := len(sequences)

	for i, sequence := range sequences {
		prediction, _ := m.forwardPass(sequence, targets[i])

		// Calculate accuracy based on percentage error
		error := math.Abs(prediction-targets[i]) / math.Max(targets[i], 1.0)
		if error < 0.1 { // Within 10% is considered correct
			correct++
		}
	}

	return float64(correct) / float64(total)
}

// Predict using LSTM model
func (m *LSTMModel) Predict(data []WorkloadDataPoint) (int, float64, error) {
	if len(data) < m.SequenceLen {
		return 0, 0.0, fmt.Errorf("insufficient data for prediction: need %d points, got %d", m.SequenceLen, len(data))
	}

	// Use the last sequence for prediction
	sequence := data[len(data)-m.SequenceLen:]

	// Forward pass
	prediction, _ := m.forwardPass(sequence, 0) // Target not needed for prediction

	// Round to nearest integer for queue length
	queueLength := int(math.Round(prediction))
	if queueLength < 0 {
		queueLength = 0
	}

	// Calculate confidence based on training accuracy
	confidence := m.Accuracy

	return queueLength, confidence, nil
}

// Predict using GRU model
func (m *GRUModel) Predict(data []WorkloadDataPoint) (int, float64, error) {
	if len(data) < m.SequenceLen {
		return 0, 0.0, fmt.Errorf("insufficient data for prediction: need %d points, got %d", m.SequenceLen, len(data))
	}

	// Use the last sequence for prediction
	sequence := data[len(data)-m.SequenceLen:]

	// Forward pass
	prediction, _ := m.forwardPass(sequence, 0) // Target not needed for prediction

	// Round to nearest integer for queue length
	queueLength := int(math.Round(prediction))
	if queueLength < 0 {
		queueLength = 0
	}

	// Calculate confidence based on training accuracy
	confidence := m.Accuracy

	return queueLength, confidence, nil
}

// Get model accuracy
func (m *LSTMModel) GetAccuracy() float64 {
	return m.Accuracy
}

func (m *GRUModel) GetAccuracy() float64 {
	return m.Accuracy
}

// Get model type
func (m *LSTMModel) GetModelType() string {
	return "lstm"
}

func (m *GRUModel) GetModelType() string {
	return "gru"
}

// Save model (simplified - in practice would serialize weights)
func (m *LSTMModel) SaveModel(path string) error {
	log.Printf("LSTM model save functionality not implemented yet")
	return fmt.Errorf("save functionality not implemented")
}

func (m *GRUModel) SaveModel(path string) error {
	log.Printf("GRU model save functionality not implemented yet")
	return fmt.Errorf("save functionality not implemented")
}

// Load model (simplified - in practice would deserialize weights)
func (m *LSTMModel) LoadModel(path string) error {
	log.Printf("LSTM model load functionality not implemented yet")
	return fmt.Errorf("load functionality not implemented")
}

func (m *GRUModel) LoadModel(path string) error {
	log.Printf("GRU model load functionality not implemented yet")
	return fmt.Errorf("load functionality not implemented")
}

// DeepLearningEnsemble combines multiple deep learning models
type DeepLearningEnsemble struct {
	Models   []DeepLearningModel
	Weights  []float64
	Accuracy float64
}

// NewDeepLearningEnsemble creates an ensemble of deep learning models
func NewDeepLearningEnsemble() *DeepLearningEnsemble {
	return &DeepLearningEnsemble{
		Models:  make([]DeepLearningModel, 0),
		Weights: make([]float64, 0),
	}
}

// AddModel adds a model to the ensemble
func (e *DeepLearningEnsemble) AddModel(model DeepLearningModel, weight float64) {
	e.Models = append(e.Models, model)
	e.Weights = append(e.Weights, weight)
}

// Train ensemble models
func (e *DeepLearningEnsemble) Train(data []WorkloadDataPoint) error {
	if len(e.Models) == 0 {
		return fmt.Errorf("no models in ensemble")
	}

	log.Printf("Training ensemble with %d models", len(e.Models))

	// Train each model
	for i, model := range e.Models {
		log.Printf("Training model %d (%s)", i+1, model.GetModelType())
		err := model.Train(data)
		if err != nil {
			log.Printf("Error training model %d: %v", i+1, err)
			continue
		}
	}

	// Calculate ensemble accuracy (weighted average)
	totalWeight := 0.0
	weightedAccuracy := 0.0

	for i, model := range e.Models {
		accuracy := model.GetAccuracy()
		weight := e.Weights[i]

		weightedAccuracy += accuracy * weight
		totalWeight += weight
	}

	if totalWeight > 0 {
		e.Accuracy = weightedAccuracy / totalWeight
	}

	log.Printf("Ensemble training completed. Ensemble accuracy: %.2f%%", e.Accuracy*100)
	return nil
}

// Predict using ensemble
func (e *DeepLearningEnsemble) Predict(data []WorkloadDataPoint) (int, float64, error) {
	if len(e.Models) == 0 {
		return 0, 0.0, fmt.Errorf("no models in ensemble")
	}

	totalWeight := 0.0
	weightedPrediction := 0.0
	weightedConfidence := 0.0

	// Get predictions from all models
	for i, model := range e.Models {
		prediction, confidence, err := model.Predict(data)
		if err != nil {
			log.Printf("Error predicting with model %d: %v", i+1, err)
			continue
		}

		weight := e.Weights[i]
		weightedPrediction += float64(prediction) * weight
		weightedConfidence += confidence * weight
		totalWeight += weight
	}

	if totalWeight == 0 {
		return 0, 0.0, fmt.Errorf("no valid predictions from ensemble models")
	}

	// Calculate final prediction and confidence
	finalPrediction := int(math.Round(weightedPrediction / totalWeight))
	finalConfidence := weightedConfidence / totalWeight

	return finalPrediction, finalConfidence, nil
}

// Get ensemble accuracy
func (e *DeepLearningEnsemble) GetAccuracy() float64 {
	return e.Accuracy
}

// Get ensemble type
func (e *DeepLearningEnsemble) GetModelType() string {
	return "deep_learning_ensemble"
}

// Save ensemble (not implemented)
func (e *DeepLearningEnsemble) SaveModel(path string) error {
	return fmt.Errorf("ensemble save functionality not implemented")
}

// Load ensemble (not implemented)
func (e *DeepLearningEnsemble) LoadModel(path string) error {
	return fmt.Errorf("ensemble load functionality not implemented")
}
