package gpu

import (
	"log"
	"os"
	"sync"
	"testing"
	"time"
)

func TestManagedMetalBuffer_Creation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	buffer, err := NewManagedMetalBuffer("test-buffer", 0, "test-queue", BufferPriorityNormal, BufferOptionNone, logger)
	if err != nil {
		t.Fatalf("Failed to create managed Metal buffer: %v", err)
	}
	defer buffer.Destroy()

	if buffer.GetID() != "test-buffer" {
		t.<PERSON>rf("Expected ID 'test-buffer', got %s", buffer.GetID())
	}

	if buffer.GetDeviceID() != 0 {
		t.<PERSON><PERSON><PERSON>("Expected device ID 0, got %d", buffer.GetDeviceID())
	}

	if buffer.GetQueueID() != "test-queue" {
		t.Errorf("Expected queue ID 'test-queue', got %s", buffer.GetQueueID())
	}

	if buffer.GetPriority() != BufferPriorityNormal {
		t.<PERSON><PERSON><PERSON>("Expected priority %d, got %d", BufferPriorityNormal, buffer.GetPriority())
	}

	if buffer.IsInUse() {
		t.Error("Buffer should not be in use initially")
	}

	if buffer.GetState() != BufferStateIdle {
		t.Errorf("Expected idle state, got %d", buffer.GetState())
	}
}

func TestManagedMetalBuffer_Usage(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	buffer, err := NewManagedMetalBuffer("test-buffer", 0, "test-queue", BufferPriorityNormal, BufferOptionNone, logger)
	if err != nil {
		t.Fatalf("Failed to create managed Metal buffer: %v", err)
	}
	defer buffer.Destroy()

	// Test setting in use
	buffer.SetInUse(true)
	if !buffer.IsInUse() {
		t.Error("Buffer should be in use after SetInUse(true)")
	}

	// Test usage statistics
	usageCount, totalTime, errorCount, commitCount, completeCount := buffer.GetUsageStats()
	if usageCount != 1 {
		t.Errorf("Expected usage count 1, got %d", usageCount)
	}

	// Test commit operation
	err = buffer.CommitBuffer()
	if err != nil {
		t.Errorf("Failed to commit buffer: %v", err)
	}

	// Check commit count
	_, _, _, commitCount, _ = buffer.GetUsageStats()
	if commitCount != 1 {
		t.Errorf("Expected commit count 1, got %d", commitCount)
	}

	// Test completion
	err = buffer.WaitForCompletion()
	if err != nil {
		t.Errorf("Failed to wait for completion: %v", err)
	}

	// Check completion statistics
	_, _, _, _, completeCount = buffer.GetUsageStats()
	if completeCount != 1 {
		t.Errorf("Expected complete count 1, got %d", completeCount)
	}

	// Test time tracking
	buffer.UpdateTotalTime(100 * time.Millisecond)
	_, totalTime, _, _, _ = buffer.GetUsageStats()
	if totalTime != 100*time.Millisecond {
		t.Errorf("Expected total time 100ms, got %v", totalTime)
	}

	// Test error tracking
	buffer.IncrementErrorCount()
	_, _, errorCount, _, _ = buffer.GetUsageStats()
	if errorCount != 1 {
		t.Errorf("Expected error count 1, got %d", errorCount)
	}
}

func TestBufferPool_Creation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	pool := NewBufferPool(0, "test-queue", 2, 5, logger)
	defer pool.Cleanup()

	stats := pool.GetPoolStats()
	if stats["device_id"] != 0 {
		t.Errorf("Expected device ID 0, got %v", stats["device_id"])
	}
	if stats["total"].(int) != 2 {
		t.Errorf("Expected 2 initial buffers, got %v", stats["total"])
	}
	if stats["available"].(int) != 2 {
		t.Errorf("Expected 2 available buffers, got %v", stats["available"])
	}
}

func TestBufferPool_AcquireRelease(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	pool := NewBufferPool(0, "test-queue", 2, 5, logger)
	defer pool.Cleanup()

	// Acquire normal priority buffer
	buffer1, err := pool.AcquireBuffer(BufferPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire buffer: %v", err)
	}

	if !buffer1.IsInUse() {
		t.Error("Acquired buffer should be in use")
	}

	// Acquire high priority buffer
	buffer2, err := pool.AcquireBuffer(BufferPriorityHigh)
	if err != nil {
		t.Fatalf("Failed to acquire high priority buffer: %v", err)
	}

	// Try to acquire when none available (should create new)
	buffer3, err := pool.AcquireBuffer(BufferPriorityLow)
	if err != nil {
		t.Fatalf("Failed to acquire third buffer: %v", err)
	}

	// Release buffers
	err = pool.ReleaseBuffer(buffer1)
	if err != nil {
		t.Errorf("Failed to release buffer: %v", err)
	}

	if buffer1.IsInUse() {
		t.Error("Released buffer should not be in use")
	}

	err = pool.ReleaseBuffer(buffer2)
	if err != nil {
		t.Errorf("Failed to release buffer: %v", err)
	}

	err = pool.ReleaseBuffer(buffer3)
	if err != nil {
		t.Errorf("Failed to release buffer: %v", err)
	}
}

func TestBufferPool_PriorityHandling(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	pool := NewBufferPool(0, "test-queue", 1, 3, logger)
	defer pool.Cleanup()

	// Acquire the normal priority buffer
	buffer1, err := pool.AcquireBuffer(BufferPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire normal priority buffer: %v", err)
	}

	// Request high priority, should create new high priority buffer
	buffer2, err := pool.AcquireBuffer(BufferPriorityHigh)
	if err != nil {
		t.Fatalf("Failed to acquire high priority buffer: %v", err)
	}

	if buffer2.GetPriority() != BufferPriorityHigh {
		t.Errorf("Expected high priority buffer, got priority %d", buffer2.GetPriority())
	}

	// Release buffers
	pool.ReleaseBuffer(buffer1)
	pool.ReleaseBuffer(buffer2)
}

func TestBufferPool_MaxCapacity(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	pool := NewBufferPool(0, "test-queue", 1, 2, logger)
	defer pool.Cleanup()

	// Acquire first buffer
	buffer1, err := pool.AcquireBuffer(BufferPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire first buffer: %v", err)
	}

	// Acquire second buffer (should create new one)
	buffer2, err := pool.AcquireBuffer(BufferPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire second buffer: %v", err)
	}

	// Try to acquire third buffer (should use LRU)
	// First release one buffer to make LRU work
	pool.ReleaseBuffer(buffer1)
	buffer3, err := pool.AcquireBuffer(BufferPriorityNormal)
	if err != nil {
		t.Errorf("Failed to acquire third buffer (should reuse LRU): %v", err)
	}

	// Clean up
	pool.ReleaseBuffer(buffer2)
	if buffer3 != nil {
		pool.ReleaseBuffer(buffer3)
	}
}

func TestMetalBufferManager_Initialization(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	manager := NewMetalBufferManager(logger)
	defer manager.Cleanup()

	devices := []int{0, 1}
	queues := map[int]string{
		0: "queue-0",
		1: "queue-1",
	}

	err := manager.Initialize(devices, queues, 2, 5)
	if err != nil {
		t.Fatalf("Failed to initialize manager: %v", err)
	}

	stats := manager.GetManagerStats()
	if stats["total_pools"].(int) != 2 {
		t.Errorf("Expected 2 pools, got %v", stats["total_pools"])
	}
	if stats["default_device"].(int) != 0 {
		t.Errorf("Expected default device 0, got %v", stats["default_device"])
	}
}

func TestMetalBufferManager_AcquireRelease(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	manager := NewMetalBufferManager(logger)
	defer manager.Cleanup()

	devices := []int{0}
	queues := map[int]string{0: "queue-0"}

	err := manager.Initialize(devices, queues, 2, 5)
	if err != nil {
		t.Fatalf("Failed to initialize manager: %v", err)
	}

	// Acquire buffer
	buffer1, err := manager.AcquireBuffer(0, "queue-0", BufferPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire buffer: %v", err)
	}

	// Acquire using default device
	buffer2, err := manager.AcquireDefaultBuffer(BufferPriorityHigh)
	if err != nil {
		t.Fatalf("Failed to acquire default buffer: %v", err)
	}

	// Release buffers
	err = manager.ReleaseBuffer(buffer1)
	if err != nil {
		t.Errorf("Failed to release buffer: %v", err)
	}

	err = manager.ReleaseBuffer(buffer2)
	if err != nil {
		t.Errorf("Failed to release buffer: %v", err)
	}

	// Check statistics
	stats := manager.GetManagerStats()
	if stats["total_acquires"].(int64) != 2 {
		t.Errorf("Expected 2 acquires, got %v", stats["total_acquires"])
	}
	if stats["total_releases"].(int64) != 2 {
		t.Errorf("Expected 2 releases, got %v", stats["total_releases"])
	}
}

func TestMetalBufferManager_ConcurrentAccess(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	manager := NewMetalBufferManager(logger)
	defer manager.Cleanup()

	devices := []int{0}
	queues := map[int]string{0: "queue-0"}

	err := manager.Initialize(devices, queues, 2, 10)
	if err != nil {
		t.Fatalf("Failed to initialize manager: %v", err)
	}

	const numGoroutines = 10
	var wg sync.WaitGroup
	errors := make(chan error, numGoroutines)

	// Concurrent acquire and release
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			buffer, err := manager.AcquireBuffer(0, "queue-0", BufferPriorityNormal)
			if err != nil {
				errors <- err
				return
			}

			// Simulate some work
			time.Sleep(time.Millisecond)

			err = manager.ReleaseBuffer(buffer)
			if err != nil {
				errors <- err
				return
			}
		}(i)
	}

	wg.Wait()
	close(errors)

	// Check for errors
	for err := range errors {
		t.Errorf("Concurrent access error: %v", err)
	}
}

func TestBufferPool_LRUEviction(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	pool := NewBufferPool(0, "test-queue", 1, 2, logger)
	defer pool.Cleanup()

	// Acquire first buffer
	buffer1, err := pool.AcquireBuffer(BufferPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire first buffer: %v", err)
	}

	// Create second buffer by requesting when none available
	buffer2, err := pool.AcquireBuffer(BufferPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire second buffer: %v", err)
	}

	// Release buffer2 first, then buffer1 (buffer2 will be LRU)
	pool.ReleaseBuffer(buffer2)
	time.Sleep(10 * time.Millisecond) // Ensure time difference
	pool.ReleaseBuffer(buffer1)

	// Acquire again - should get buffer2 (LRU)
	buffer3, err := pool.AcquireBuffer(BufferPriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire third buffer: %v", err)
	}

	pool.ReleaseBuffer(buffer3)
}

func TestBufferPool_ErrorHandling(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	pool := NewBufferPool(0, "test-queue", 1, 2, logger)
	defer pool.Cleanup()

	// Test releasing nil buffer
	err := pool.ReleaseBuffer(nil)
	if err == nil {
		t.Error("Expected error when releasing nil buffer")
	}

	// Test releasing buffer from wrong device
	buffer, err := NewManagedMetalBuffer("wrong-device", 1, "queue", BufferPriorityNormal, BufferOptionNone, logger)
	if err != nil {
		t.Fatalf("Failed to create buffer for error test: %v", err)
	}
	defer buffer.Destroy()

	err = pool.ReleaseBuffer(buffer)
	if err == nil {
		t.Error("Expected error when releasing buffer from wrong device")
	}
}

func TestManagedMetalBuffer_ThreadSafety(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	buffer, err := NewManagedMetalBuffer("thread-test", 0, "queue", BufferPriorityNormal, BufferOptionNone, logger)
	if err != nil {
		t.Fatalf("Failed to create buffer: %v", err)
	}
	defer buffer.Destroy()

	const numGoroutines = 50
	var wg sync.WaitGroup

	// Concurrent access to buffer methods
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			// Test concurrent reads
			_ = buffer.GetID()
			_ = buffer.GetDeviceID()
			_ = buffer.GetPriority()
			_ = buffer.IsInUse()
			_ = buffer.GetState()

			// Test concurrent state changes
			buffer.SetInUse(true)
			buffer.SetInUse(false)
			buffer.UpdateTotalTime(time.Millisecond)
			buffer.IncrementErrorCount()
		}()
	}

	wg.Wait()
}

func TestBufferPriority_Constants(t *testing.T) {
	if BufferPriorityLow >= BufferPriorityNormal {
		t.Error("Low priority should be less than normal priority")
	}
	if BufferPriorityNormal >= BufferPriorityHigh {
		t.Error("Normal priority should be less than high priority")
	}
}

func TestMockMetalCommandBuffer(t *testing.T) {
	buffer := &MockMetalCommandBuffer{}

	// Test creation
	err := buffer.Create(0, 0, BufferOptionNone)
	if err != nil {
		t.Errorf("Failed to create mock buffer: %v", err)
	}

	if !buffer.created {
		t.Error("Buffer should be marked as created")
	}

	if buffer.GetHandle() == 0 {
		t.Error("Buffer should have a handle")
	}

	// Test commit
	err = buffer.Commit()
	if err != nil {
		t.Errorf("Failed to commit buffer: %v", err)
	}

	if !buffer.committed {
		t.Error("Buffer should be marked as committed")
	}

	// Test wait for completion
	err = buffer.WaitUntilCompleted()
	if err != nil {
		t.Errorf("Failed to wait for completion: %v", err)
	}

	if !buffer.completed {
		t.Error("Buffer should be marked as completed")
	}

	// Test release
	err = buffer.Release()
	if err != nil {
		t.Errorf("Failed to release buffer: %v", err)
	}

	if !buffer.released {
		t.Error("Buffer should be marked as released")
	}
}
