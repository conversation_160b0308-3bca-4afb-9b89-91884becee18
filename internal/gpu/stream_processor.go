package gpu

import (
	"sync"
	"time"
)

// StreamProcessor handles real-time data streaming for online learning
type StreamProcessor struct {
	buffer             []WorkloadDataPoint
	config             StreamProcessorConfig
	mu                 sync.RWMutex
	backpressureActive bool
	droppedCount       int64
	processedCount     int64
}

// StreamProcessorConfig configures stream processing behavior
type StreamProcessorConfig struct {
	BufferSize            int           `json:"buffer_size"`
	ProcessingDelay       time.Duration `json:"processing_delay"`
	BackpressureThreshold int           `json:"backpressure_threshold"`
}

// NewStreamProcessor creates a new stream processor
func NewStreamProcessor(config StreamProcessorConfig) *StreamProcessor {
	return &StreamProcessor{
		buffer: make([]WorkloadDataPoint, 0, config.BufferSize),
		config: config,
	}
}

// AddDataPoint adds a new data point to the stream buffer
func (sp *StreamProcessor) AddDataPoint(dataPoint WorkloadDataPoint) error {
	sp.mu.Lock()
	defer sp.mu.Unlock()

	// Check for backpressure
	if len(sp.buffer) >= sp.config.BackpressureThreshold {
		sp.backpressureActive = true

		// Drop oldest data points if buffer is full
		if len(sp.buffer) >= sp.config.BufferSize {
			sp.buffer = sp.buffer[1:]
			sp.droppedCount++
		}
	} else {
		sp.backpressureActive = false
	}

	sp.buffer = append(sp.buffer, dataPoint)
	return nil
}

// ProcessBatch processes and returns a batch of data points
func (sp *StreamProcessor) ProcessBatch() []WorkloadDataPoint {
	sp.mu.Lock()
	defer sp.mu.Unlock()

	if len(sp.buffer) == 0 {
		return nil
	}

	// Return all buffered data and clear buffer
	batch := make([]WorkloadDataPoint, len(sp.buffer))
	copy(batch, sp.buffer)
	sp.buffer = sp.buffer[:0]
	sp.processedCount += int64(len(batch))

	return batch
}

// GetBufferSize returns the current buffer size
func (sp *StreamProcessor) GetBufferSize() int {
	sp.mu.RLock()
	defer sp.mu.RUnlock()
	return len(sp.buffer)
}

// IsBackpressureActive returns whether backpressure is currently active
func (sp *StreamProcessor) IsBackpressureActive() bool {
	sp.mu.RLock()
	defer sp.mu.RUnlock()
	return sp.backpressureActive
}

// GetStatistics returns stream processing statistics
func (sp *StreamProcessor) GetStatistics() StreamProcessorStats {
	sp.mu.RLock()
	defer sp.mu.RUnlock()

	return StreamProcessorStats{
		BufferSize:         len(sp.buffer),
		BufferCapacity:     sp.config.BufferSize,
		BackpressureActive: sp.backpressureActive,
		DroppedCount:       sp.droppedCount,
		ProcessedCount:     sp.processedCount,
		UtilizationPercent: float64(len(sp.buffer)) / float64(sp.config.BufferSize) * 100,
	}
}

// StreamProcessorStats contains statistics about stream processing
type StreamProcessorStats struct {
	BufferSize         int     `json:"buffer_size"`
	BufferCapacity     int     `json:"buffer_capacity"`
	BackpressureActive bool    `json:"backpressure_active"`
	DroppedCount       int64   `json:"dropped_count"`
	ProcessedCount     int64   `json:"processed_count"`
	UtilizationPercent float64 `json:"utilization_percent"`
}

// Clear clears the stream buffer
func (sp *StreamProcessor) Clear() {
	sp.mu.Lock()
	defer sp.mu.Unlock()
	sp.buffer = sp.buffer[:0]
	sp.backpressureActive = false
}

// GetConfig returns the current configuration
func (sp *StreamProcessor) GetConfig() StreamProcessorConfig {
	return sp.config
}

// UpdateConfig updates the stream processor configuration
func (sp *StreamProcessor) UpdateConfig(config StreamProcessorConfig) {
	sp.mu.Lock()
	defer sp.mu.Unlock()

	// If buffer size is reduced, trim buffer if necessary
	if config.BufferSize < len(sp.buffer) {
		sp.buffer = sp.buffer[:config.BufferSize]
	}

	sp.config = config
}
