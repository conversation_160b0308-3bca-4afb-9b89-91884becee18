package gpu

import (
	"fmt"
	"math"
	"sort"
	"sync"
	"unsafe"
)

/*
#include <string.h>
*/
import "C"

// INT4 data type is now defined in tensor.go

// QuantizationMethod represents different quantization algorithms
type QuantizationMethod int

const (
	QuantizationSymmetric  QuantizationMethod = iota // Symmetric quantization (zero-point = 0)
	QuantizationAsymmetric                           // Asymmetric quantization (learned zero-point)
	QuantizationPerChannel                           // Per-channel quantization
	QuantizationLearned                              // Learned Step Size Quantization (LSQ)
	QuantizationAdaptive                             // Adaptive Rounding Quantization (ARQ)
)

// CalibrationMethod represents different calibration algorithms
type CalibrationMethod int

const (
	CalibrationMSE          CalibrationMethod = iota // Mean Squared Error calibration
	CalibrationKLDivergence                          // Kullback-Leibler divergence calibration
	CalibrationEntropy                               // Entropy-based calibration
)

// SampleSelectionMethod represents different sample selection algorithms
type SampleSelectionMethod int

const (
	SampleSelectionRandom         SampleSelectionMethod = iota // Random sampling
	SampleSelectionGreedyCoreset                               // Greedy coreset selection
	SampleSelectionDensityBased                                // Density-based sampling
	SampleSelectionUncertainty                                 // Uncertainty-based selection
	SampleSelectionActiveLearning                              // Active learning techniques
)

// String returns string representation of quantization method
func (qm QuantizationMethod) String() string {
	switch qm {
	case QuantizationSymmetric:
		return "symmetric"
	case QuantizationAsymmetric:
		return "asymmetric"
	case QuantizationPerChannel:
		return "per_channel"
	case QuantizationLearned:
		return "learned"
	case QuantizationAdaptive:
		return "adaptive"
	default:
		return "unknown"
	}
}

// String returns string representation of calibration method
func (cm CalibrationMethod) String() string {
	switch cm {
	case CalibrationMSE:
		return "mse"
	case CalibrationKLDivergence:
		return "kl_divergence"
	case CalibrationEntropy:
		return "entropy"
	default:
		return "unknown"
	}
}

// String returns string representation of sample selection method
func (ssm SampleSelectionMethod) String() string {
	switch ssm {
	case SampleSelectionRandom:
		return "random"
	case SampleSelectionGreedyCoreset:
		return "greedy_coreset"
	case SampleSelectionDensityBased:
		return "density_based"
	case SampleSelectionUncertainty:
		return "uncertainty"
	case SampleSelectionActiveLearning:
		return "active_learning"
	default:
		return "unknown"
	}
}

// CalibrationDatasetConfig holds configuration for calibration dataset management
type CalibrationDatasetConfig struct {
	MaxSampleSize          int                   `json:"max_sample_size"`          // Maximum number of calibration samples
	MinSampleSize          int                   `json:"min_sample_size"`          // Minimum number of calibration samples
	SelectionMethod        SampleSelectionMethod `json:"selection_method"`         // Sample selection algorithm
	CalibrationMethod      CalibrationMethod     `json:"calibration_method"`       // Calibration algorithm
	EnablePreprocessing    bool                  `json:"enable_preprocessing"`     // Whether to preprocess calibration data
	EnableDataAugmentation bool                  `json:"enable_data_augmentation"` // Whether to apply data augmentation
	IncludeOutliers        bool                  `json:"include_outliers"`         // Whether to include outlier samples
	OutlierThreshold       float32               `json:"outlier_threshold"`        // Threshold for outlier detection
	ClusterCount           int                   `json:"cluster_count"`            // Number of clusters for density-based sampling
	DiversityWeight        float32               `json:"diversity_weight"`         // Weight for diversity in sample selection
}

// CalibrationSample represents a single calibration sample with metadata
type CalibrationSample struct {
	Data               *Tensor                `json:"data"`                // The tensor data
	Label              int32                  `json:"label,omitempty"`     // Optional label for supervised calibration
	Weight             float32                `json:"weight"`              // Sample weight for importance sampling
	ClusterID          int                    `json:"cluster_id"`          // Cluster assignment for density-based methods
	UncertaintyScore   float32                `json:"uncertainty_score"`   // Uncertainty score for active learning
	IsOutlier          bool                   `json:"is_outlier"`          // Whether this sample is an outlier
	SelectionIteration int                    `json:"selection_iteration"` // Iteration when this sample was selected
	Metadata           map[string]interface{} `json:"metadata,omitempty"`  // Additional metadata
}

// CalibrationDataset manages a collection of calibration samples
type CalibrationDataset struct {
	Samples       []*CalibrationSample     `json:"samples"`
	Config        CalibrationDatasetConfig `json:"config"`
	Statistics    CalibrationStatistics    `json:"statistics"`
	Preprocessing PreprocessingPipeline    `json:"preprocessing"`
	mutex         sync.RWMutex
}

// CalibrationStatistics holds statistics about the calibration dataset
type CalibrationStatistics struct {
	TotalSamples    int     `json:"total_samples"`
	SelectedSamples int     `json:"selected_samples"`
	OutlierCount    int     `json:"outlier_count"`
	ClusterCount    int     `json:"cluster_count"`
	MeanValue       float32 `json:"mean_value"`
	StdValue        float32 `json:"std_value"`
	MinValue        float32 `json:"min_value"`
	MaxValue        float32 `json:"max_value"`
	Coverage        float32 `json:"coverage"`  // Feature space coverage
	Diversity       float32 `json:"diversity"` // Sample diversity score
	LastUpdated     int64   `json:"last_updated"`
}

// PreprocessingPipeline defines preprocessing operations for calibration data
type PreprocessingPipeline struct {
	EnableNormalization bool           `json:"enable_normalization"`
	NormalizationMean   float32        `json:"normalization_mean"`
	NormalizationStd    float32        `json:"normalization_std"`
	EnableAugmentation  bool           `json:"enable_augmentation"`
	AugmentationFactor  float32        `json:"augmentation_factor"`
	TargetPrecision     TensorDataType `json:"target_precision"`
	BatchSize           int            `json:"batch_size"`
}

// QuantizationParams holds parameters for quantization
type QuantizationParams struct {
	Scale     float32 // Quantization scale factor
	ZeroPoint int32   // Zero-point offset
	MinValue  float32 // Minimum value in the range
	MaxValue  float32 // Maximum value in the range
}

// QuantizationConfig holds configuration for quantization operations
type QuantizationConfig struct {
	Method                 QuantizationMethod       // Quantization algorithm to use
	TargetType             TensorDataType           // Target quantized data type (INT8 or INT4)
	UseCalibration         bool                     // Whether to use calibration dataset
	PreserveAccuracy       bool                     // Whether to apply accuracy preservation techniques
	PerChannelQuantization bool                     // Whether to use per-channel quantization
	CalibrationConfig      CalibrationDatasetConfig // Calibration dataset configuration
}

// QuantizationEngine manages quantization operations
type QuantizationEngine struct {
	config             QuantizationConfig
	calibrationData    []*Tensor                     // Legacy calibration dataset (deprecated)
	calibrationDataset *CalibrationDataset           // Advanced calibration dataset
	quantizationCache  map[string]QuantizationParams // Cache for computed quantization parameters
	mutex              sync.RWMutex
}

// NewQuantizationEngine creates a new quantization engine
func NewQuantizationEngine(config QuantizationConfig) *QuantizationEngine {
	return &QuantizationEngine{
		config:             config,
		calibrationData:    make([]*Tensor, 0),
		calibrationDataset: NewCalibrationDataset(config.CalibrationConfig),
		quantizationCache:  make(map[string]QuantizationParams),
	}
}

// NewCalibrationDataset creates a new calibration dataset
func NewCalibrationDataset(config CalibrationDatasetConfig) *CalibrationDataset {
	return &CalibrationDataset{
		Samples: make([]*CalibrationSample, 0),
		Config:  config,
		Statistics: CalibrationStatistics{
			LastUpdated: getCurrentTimestamp(),
		},
		Preprocessing: PreprocessingPipeline{
			EnableNormalization: config.EnablePreprocessing,
			EnableAugmentation:  config.EnableDataAugmentation,
			TargetPrecision:     TensorFloat32,
			BatchSize:           32,
		},
	}
}

// AddCalibrationSample adds a sample to the calibration dataset
func (cd *CalibrationDataset) AddCalibrationSample(data *Tensor, weight float32) error {
	cd.mutex.Lock()
	defer cd.mutex.Unlock()

	if data == nil {
		return fmt.Errorf("calibration data cannot be nil")
	}

	if data.device != DeviceCPU {
		return fmt.Errorf("calibration data must be on CPU for processing")
	}

	sample := &CalibrationSample{
		Data:               data,
		Weight:             weight,
		ClusterID:          -1, // Will be assigned during clustering
		UncertaintyScore:   0.0,
		IsOutlier:          false,
		SelectionIteration: len(cd.Samples),
		Metadata:           make(map[string]interface{}),
	}

	cd.Samples = append(cd.Samples, sample)
	cd.updateStatistics()

	return nil
}

// SelectRepresentativeSamples selects representative samples from the dataset
func (cd *CalibrationDataset) SelectRepresentativeSamples(count int) ([]*CalibrationSample, error) {
	cd.mutex.RLock()
	defer cd.mutex.RUnlock()

	if count <= 0 {
		return nil, fmt.Errorf("sample count must be positive")
	}

	if len(cd.Samples) == 0 {
		return nil, fmt.Errorf("no calibration samples available")
	}

	// Don't select more samples than available
	if count > len(cd.Samples) {
		count = len(cd.Samples)
	}

	switch cd.Config.SelectionMethod {
	case SampleSelectionRandom:
		return cd.randomSelection(count)
	case SampleSelectionGreedyCoreset:
		return cd.greedyCoresetSelection(count)
	case SampleSelectionDensityBased:
		return cd.densityBasedSelection(count)
	case SampleSelectionUncertainty:
		return cd.uncertaintyBasedSelection(count)
	case SampleSelectionActiveLearning:
		return cd.activeLearningSelection(count)
	default:
		return cd.randomSelection(count)
	}
}

// randomSelection performs random sampling
func (cd *CalibrationDataset) randomSelection(count int) ([]*CalibrationSample, error) {
	selected := make([]*CalibrationSample, 0, count)
	indices := make([]int, len(cd.Samples))
	for i := range indices {
		indices[i] = i
	}

	// Simple random shuffle (Fisher-Yates)
	for i := len(indices) - 1; i > 0; i-- {
		j := int(math.Mod(float64(i*7+13), float64(i+1))) // Simple pseudo-random
		indices[i], indices[j] = indices[j], indices[i]
	}

	for i := 0; i < count && i < len(indices); i++ {
		selected = append(selected, cd.Samples[indices[i]])
	}

	return selected, nil
}

// greedyCoresetSelection performs greedy coreset selection
func (cd *CalibrationDataset) greedyCoresetSelection(count int) ([]*CalibrationSample, error) {
	if len(cd.Samples) == 0 {
		return nil, fmt.Errorf("no samples available")
	}

	selected := make([]*CalibrationSample, 0, count)
	remaining := make([]*CalibrationSample, len(cd.Samples))
	copy(remaining, cd.Samples)

	// Select first sample randomly
	firstIdx := 0
	if len(remaining) > 1 {
		firstIdx = int(math.Mod(float64(len(remaining)*17+23), float64(len(remaining))))
	}
	selected = append(selected, remaining[firstIdx])
	remaining = append(remaining[:firstIdx], remaining[firstIdx+1:]...)

	// Greedily select samples that maximize distance to already selected samples
	for len(selected) < count && len(remaining) > 0 {
		maxDist := float32(-1)
		maxIdx := 0

		for i, candidate := range remaining {
			minDistToSelected := float32(math.Inf(1))

			for _, selectedSample := range selected {
				dist, err := cd.computeDistance(candidate, selectedSample)
				if err != nil {
					continue
				}
				if dist < minDistToSelected {
					minDistToSelected = dist
				}
			}

			if minDistToSelected > maxDist {
				maxDist = minDistToSelected
				maxIdx = i
			}
		}

		selected = append(selected, remaining[maxIdx])
		remaining = append(remaining[:maxIdx], remaining[maxIdx+1:]...)
	}

	return selected, nil
}

// densityBasedSelection performs density-based sampling
func (cd *CalibrationDataset) densityBasedSelection(count int) ([]*CalibrationSample, error) {
	// First, cluster the samples
	err := cd.clusterSamples()
	if err != nil {
		return nil, err
	}

	// Count samples per cluster
	clusterCounts := make(map[int]int)
	for _, sample := range cd.Samples {
		clusterCounts[sample.ClusterID]++
	}

	// Select samples proportionally from each cluster
	selected := make([]*CalibrationSample, 0, count)
	samplesPerCluster := count / len(clusterCounts)
	if samplesPerCluster == 0 {
		samplesPerCluster = 1
	}

	for clusterID := range clusterCounts {
		clusterSamples := make([]*CalibrationSample, 0)
		for _, sample := range cd.Samples {
			if sample.ClusterID == clusterID {
				clusterSamples = append(clusterSamples, sample)
			}
		}

		// Select samples from this cluster
		selectedFromCluster := samplesPerCluster
		if selectedFromCluster > len(clusterSamples) {
			selectedFromCluster = len(clusterSamples)
		}

		for i := 0; i < selectedFromCluster && len(selected) < count; i++ {
			selected = append(selected, clusterSamples[i])
		}
	}

	return selected, nil
}

// uncertaintyBasedSelection performs uncertainty-based sampling
func (cd *CalibrationDataset) uncertaintyBasedSelection(count int) ([]*CalibrationSample, error) {
	// Calculate uncertainty scores for all samples
	err := cd.computeUncertaintyScores()
	if err != nil {
		return nil, err
	}

	// Sort samples by uncertainty score (descending)
	sortedSamples := make([]*CalibrationSample, len(cd.Samples))
	copy(sortedSamples, cd.Samples)

	sort.Slice(sortedSamples, func(i, j int) bool {
		return sortedSamples[i].UncertaintyScore > sortedSamples[j].UncertaintyScore
	})

	// Select top uncertain samples
	selected := make([]*CalibrationSample, 0, count)
	for i := 0; i < count && i < len(sortedSamples); i++ {
		selected = append(selected, sortedSamples[i])
	}

	return selected, nil
}

// activeLearningSelection performs active learning-based sampling
func (cd *CalibrationDataset) activeLearningSelection(count int) ([]*CalibrationSample, error) {
	// Combine uncertainty and diversity for active learning
	err := cd.computeUncertaintyScores()
	if err != nil {
		return nil, err
	}

	selected := make([]*CalibrationSample, 0, count)
	remaining := make([]*CalibrationSample, len(cd.Samples))
	copy(remaining, cd.Samples)

	for len(selected) < count && len(remaining) > 0 {
		maxScore := float32(-1)
		maxIdx := 0

		for i, candidate := range remaining {
			// Combine uncertainty and diversity scores
			uncertaintyScore := candidate.UncertaintyScore
			diversityScore := cd.computeDiversityScore(candidate, selected)

			// Weighted combination
			combinedScore := cd.Config.DiversityWeight*diversityScore +
				(1.0-cd.Config.DiversityWeight)*uncertaintyScore

			if combinedScore > maxScore {
				maxScore = combinedScore
				maxIdx = i
			}
		}

		selected = append(selected, remaining[maxIdx])
		remaining = append(remaining[:maxIdx], remaining[maxIdx+1:]...)
	}

	return selected, nil
}

// computeDistance computes distance between two calibration samples
func (cd *CalibrationDataset) computeDistance(sample1, sample2 *CalibrationSample) (float32, error) {
	// Simple Euclidean distance on flattened tensors
	if sample1.Data.NumElements() != sample2.Data.NumElements() {
		return 0, fmt.Errorf("tensors must have same number of elements")
	}

	sumSquaredDiff := float32(0)
	count := int64(0)

	for i := int64(0); i < sample1.Data.NumElements(); i++ {
		val1, err1 := cd.getElementAsFloat32(sample1.Data, i)
		val2, err2 := cd.getElementAsFloat32(sample2.Data, i)

		if err1 != nil || err2 != nil {
			continue
		}

		diff := val1 - val2
		sumSquaredDiff += diff * diff
		count++
	}

	if count == 0 {
		return 0, fmt.Errorf("no valid elements found")
	}

	return float32(math.Sqrt(float64(sumSquaredDiff / float32(count)))), nil
}

// clusterSamples performs k-means clustering on the samples
func (cd *CalibrationDataset) clusterSamples() error {
	if len(cd.Samples) == 0 {
		return fmt.Errorf("no samples to cluster")
	}

	clusterCount := cd.Config.ClusterCount
	if clusterCount <= 0 {
		clusterCount = int(math.Min(float64(len(cd.Samples)/10), 10)) // Default: 10% of samples or 10, whichever is smaller
		if clusterCount < 1 {
			clusterCount = 1
		}
	}

	// Simple k-means clustering implementation
	// Initialize cluster centers randomly
	centers := make([]*CalibrationSample, clusterCount)
	for i := 0; i < clusterCount && i < len(cd.Samples); i++ {
		idx := int(math.Mod(float64(i*31+47), float64(len(cd.Samples))))
		centers[i] = cd.Samples[idx]
	}

	// Assign samples to clusters
	for _, sample := range cd.Samples {
		minDist := float32(math.Inf(1))
		bestCluster := 0

		for j, center := range centers {
			if center == nil {
				continue
			}
			dist, err := cd.computeDistance(sample, center)
			if err != nil {
				continue
			}
			if dist < minDist {
				minDist = dist
				bestCluster = j
			}
		}

		sample.ClusterID = bestCluster
	}

	return nil
}

// computeUncertaintyScores computes uncertainty scores for all samples
func (cd *CalibrationDataset) computeUncertaintyScores() error {
	for _, sample := range cd.Samples {
		// Simple uncertainty measure based on variance of tensor values
		variance, err := cd.computeTensorVariance(sample.Data)
		if err != nil {
			sample.UncertaintyScore = 0
			continue
		}

		// Normalize uncertainty score
		sample.UncertaintyScore = float32(math.Min(variance/1000.0, 1.0)) // Scale and cap at 1.0
	}

	return nil
}

// computeDiversityScore computes how diverse a candidate is from selected samples
func (cd *CalibrationDataset) computeDiversityScore(candidate *CalibrationSample, selected []*CalibrationSample) float32 {
	if len(selected) == 0 {
		return 1.0 // Maximum diversity if no samples selected yet
	}

	minDist := float32(math.Inf(1))
	for _, selectedSample := range selected {
		dist, err := cd.computeDistance(candidate, selectedSample)
		if err != nil {
			continue
		}
		if dist < minDist {
			minDist = dist
		}
	}

	// Normalize diversity score (assuming max reasonable distance is 10)
	return float32(math.Min(float64(minDist)/10.0, 1.0))
}

// computeTensorVariance computes variance of tensor values
func (cd *CalibrationDataset) computeTensorVariance(tensor *Tensor) (float64, error) {
	if tensor.NumElements() == 0 {
		return 0, fmt.Errorf("empty tensor")
	}

	// Compute mean
	sum := float64(0)
	count := int64(0)

	for i := int64(0); i < tensor.NumElements(); i++ {
		val, err := cd.getElementAsFloat32(tensor, i)
		if err != nil {
			continue
		}
		sum += float64(val)
		count++
	}

	if count == 0 {
		return 0, fmt.Errorf("no valid elements")
	}

	mean := sum / float64(count)

	// Compute variance
	sumSquaredDiff := float64(0)
	for i := int64(0); i < tensor.NumElements(); i++ {
		val, err := cd.getElementAsFloat32(tensor, i)
		if err != nil {
			continue
		}
		diff := float64(val) - mean
		sumSquaredDiff += diff * diff
	}

	return sumSquaredDiff / float64(count), nil
}

// getElementAsFloat32 gets an element from tensor as float32
func (cd *CalibrationDataset) getElementAsFloat32(tensor *Tensor, index int64) (float32, error) {
	if tensor == nil {
		return 0, fmt.Errorf("tensor is nil")
	}

	if index < 0 || index >= tensor.NumElements() {
		return 0, fmt.Errorf("index out of bounds")
	}

	switch tensor.dtype {
	case TensorFloat32:
		data := (*[1 << 30]float32)(unsafe.Pointer(tensor.data))
		return data[index], nil
	case TensorInt8:
		data := (*[1 << 30]int8)(unsafe.Pointer(tensor.data))
		return float32(data[index]), nil
	case TensorInt4:
		// Handle INT4 data (2 values per byte)
		byteIndex := index / 2
		bitOffset := (index % 2) * 4
		data := (*[1 << 30]uint8)(unsafe.Pointer(tensor.data))
		value := (data[byteIndex] >> bitOffset) & 0x0F
		// Convert from unsigned 4-bit to signed
		if value >= 8 {
			return float32(int8(value) - 16), nil
		}
		return float32(value), nil
	default:
		return 0, fmt.Errorf("unsupported tensor data type: %s", tensor.dtype.String())
	}
}

// updateStatistics updates the dataset statistics
func (cd *CalibrationDataset) updateStatistics() {
	cd.Statistics.TotalSamples = len(cd.Samples)
	cd.Statistics.OutlierCount = 0
	cd.Statistics.LastUpdated = getCurrentTimestamp()

	if len(cd.Samples) == 0 {
		return
	}

	// Compute basic statistics
	sum := float64(0)
	count := int64(0)
	minVal := float32(math.Inf(1))
	maxVal := float32(math.Inf(-1))

	for _, sample := range cd.Samples {
		if sample.IsOutlier {
			cd.Statistics.OutlierCount++
		}

		// Compute statistics on tensor values
		for i := int64(0); i < sample.Data.NumElements(); i++ {
			val, err := cd.getElementAsFloat32(sample.Data, i)
			if err != nil {
				continue
			}

			sum += float64(val)
			count++

			if val < minVal {
				minVal = val
			}
			if val > maxVal {
				maxVal = val
			}
		}
	}

	if count > 0 {
		cd.Statistics.MeanValue = float32(sum / float64(count))
		cd.Statistics.MinValue = minVal
		cd.Statistics.MaxValue = maxVal

		// Compute standard deviation
		sumSquaredDiff := float64(0)
		for _, sample := range cd.Samples {
			for i := int64(0); i < sample.Data.NumElements(); i++ {
				val, err := cd.getElementAsFloat32(sample.Data, i)
				if err != nil {
					continue
				}
				diff := float64(val) - float64(cd.Statistics.MeanValue)
				sumSquaredDiff += diff * diff
			}
		}
		cd.Statistics.StdValue = float32(math.Sqrt(sumSquaredDiff / float64(count)))
	}
}

// GetStatistics returns the current dataset statistics
func (cd *CalibrationDataset) GetStatistics() CalibrationStatistics {
	cd.mutex.RLock()
	defer cd.mutex.RUnlock()
	return cd.Statistics
}

// GetSampleCount returns the number of samples in the dataset
func (cd *CalibrationDataset) GetSampleCount() int {
	cd.mutex.RLock()
	defer cd.mutex.RUnlock()
	return len(cd.Samples)
}

// Clear removes all samples from the dataset
func (cd *CalibrationDataset) Clear() {
	cd.mutex.Lock()
	defer cd.mutex.Unlock()
	cd.Samples = cd.Samples[:0]
	cd.updateStatistics()
}

// Legacy methods for backward compatibility

// AddCalibrationData adds a tensor to the calibration dataset (legacy method)
func (qe *QuantizationEngine) AddCalibrationData(tensor *Tensor) error {
	return qe.calibrationDataset.AddCalibrationSample(tensor, 1.0)
}

// ClearCalibrationData removes all calibration data (legacy method)
func (qe *QuantizationEngine) ClearCalibrationData() {
	qe.calibrationDataset.Clear()
}

// ComputeQuantizationParams calculates scale and zero-point for quantization
func (qe *QuantizationEngine) ComputeQuantizationParams(tensor *Tensor) (QuantizationParams, error) {
	if tensor == nil {
		return QuantizationParams{}, fmt.Errorf("input tensor cannot be nil")
	}

	if tensor.device != DeviceCPU {
		return QuantizationParams{}, fmt.Errorf("quantization parameter calculation requires CPU tensor")
	}

	// Create cache key based on tensor properties
	cacheKey := fmt.Sprintf("%s_%v_%s", tensor.dtype.String(), tensor.shape, qe.config.Method.String())

	// Check cache first
	if params, exists := qe.quantizationCache[cacheKey]; exists {
		return params, nil
	}

	var params QuantizationParams
	var err error

	switch qe.config.Method {
	case QuantizationSymmetric:
		params, err = qe.computeSymmetricParams(tensor)
	case QuantizationAsymmetric:
		params, err = qe.computeAsymmetricParams(tensor)
	case QuantizationPerChannel:
		params, err = qe.computePerChannelParams(tensor)
	case QuantizationLearned:
		params, err = qe.computeLearnedParams(tensor)
	case QuantizationAdaptive:
		params, err = qe.computeAdaptiveParams(tensor)
	default:
		return QuantizationParams{}, fmt.Errorf("unsupported quantization method: %s", qe.config.Method)
	}

	if err != nil {
		return QuantizationParams{}, err
	}

	// Cache the computed parameters
	qe.quantizationCache[cacheKey] = params

	return params, nil
}

// computeSymmetricParams calculates symmetric quantization parameters
func (qe *QuantizationEngine) computeSymmetricParams(tensor *Tensor) (QuantizationParams, error) {
	// Find the maximum absolute value in the tensor
	maxAbs, err := qe.findMaxAbsValue(tensor)
	if err != nil {
		return QuantizationParams{}, err
	}

	// Get quantization range based on target type
	_, qmax := qe.getQuantizationRange(qe.config.TargetType)

	// For symmetric quantization, zero-point is 0
	scale := maxAbs / float32(qmax)

	return QuantizationParams{
		Scale:     scale,
		ZeroPoint: 0,
		MinValue:  -maxAbs,
		MaxValue:  maxAbs,
	}, nil
}

// computeAsymmetricParams calculates asymmetric quantization parameters
func (qe *QuantizationEngine) computeAsymmetricParams(tensor *Tensor) (QuantizationParams, error) {
	// Find min and max values in the tensor
	minVal, maxVal, err := qe.findMinMaxValues(tensor)
	if err != nil {
		return QuantizationParams{}, err
	}

	// Get quantization range based on target type
	qmin, qmax := qe.getQuantizationRange(qe.config.TargetType)

	// Calculate scale and zero-point
	scale := (maxVal - minVal) / float32(qmax-qmin)
	zeroPoint := int32(float32(qmin) - minVal/scale)

	// Clamp zero-point to valid range
	if zeroPoint < int32(qmin) {
		zeroPoint = int32(qmin)
	}
	if zeroPoint > int32(qmax) {
		zeroPoint = int32(qmax)
	}

	return QuantizationParams{
		Scale:     scale,
		ZeroPoint: zeroPoint,
		MinValue:  minVal,
		MaxValue:  maxVal,
	}, nil
}

// computePerChannelParams calculates per-channel quantization parameters
func (qe *QuantizationEngine) computePerChannelParams(tensor *Tensor) (QuantizationParams, error) {
	// For simplicity, this implementation uses the first channel
	// In a full implementation, this would return per-channel parameters

	if tensor.Rank() < 2 {
		return qe.computeAsymmetricParams(tensor)
	}

	// Extract first channel for parameter calculation
	// This is a simplified approach - full implementation would handle all channels
	channelTensor, err := qe.extractChannel(tensor, 0)
	if err != nil {
		return QuantizationParams{}, err
	}

	return qe.computeAsymmetricParams(channelTensor)
}

// computeLearnedParams calculates learned quantization parameters (LSQ)
func (qe *QuantizationEngine) computeLearnedParams(tensor *Tensor) (QuantizationParams, error) {
	// Start with asymmetric parameters as baseline
	params, err := qe.computeAsymmetricParams(tensor)
	if err != nil {
		return QuantizationParams{}, err
	}

	// Apply learned adjustments if calibration data is available
	if len(qe.calibrationData) > 0 {
		adjustedScale, err := qe.learnOptimalScale(tensor, params.Scale)
		if err == nil {
			params.Scale = adjustedScale
		}
	}

	return params, nil
}

// computeAdaptiveParams calculates adaptive rounding quantization parameters
func (qe *QuantizationEngine) computeAdaptiveParams(tensor *Tensor) (QuantizationParams, error) {
	// Start with asymmetric parameters
	params, err := qe.computeAsymmetricParams(tensor)
	if err != nil {
		return QuantizationParams{}, err
	}

	// Apply adaptive rounding adjustments
	adjustedParams, err := qe.applyAdaptiveRounding(tensor, params)
	if err != nil {
		return params, nil // Fall back to basic params if adaptive fails
	}

	return adjustedParams, nil
}

// QuantizeTensor quantizes a tensor to the specified target type
func (qe *QuantizationEngine) QuantizeTensor(tensor *Tensor) (*Tensor, error) {
	if tensor == nil {
		return nil, fmt.Errorf("input tensor cannot be nil")
	}

	// Compute quantization parameters
	params, err := qe.ComputeQuantizationParams(tensor)
	if err != nil {
		return nil, fmt.Errorf("failed to compute quantization parameters: %v", err)
	}

	// Create quantized tensor
	quantizedTensor, err := NewTensor(tensor.shape, qe.config.TargetType, tensor.device, tensor.deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to create quantized tensor: %v", err)
	}

	// Set memory pool if available
	if tensor.memPool != nil {
		quantizedTensor.SetMemoryPool(tensor.memPool)
	}

	// Perform quantization based on target type
	switch qe.config.TargetType {
	case TensorInt8:
		err = qe.quantizeToInt8(tensor, quantizedTensor, params)
	case TensorInt4:
		err = qe.quantizeToInt4(tensor, quantizedTensor, params)
	default:
		return nil, fmt.Errorf("unsupported quantization target type: %s", qe.config.TargetType)
	}

	if err != nil {
		quantizedTensor.Free()
		return nil, fmt.Errorf("quantization failed: %v", err)
	}

	return quantizedTensor, nil
}

// DequantizeTensor converts a quantized tensor back to floating-point
func (qe *QuantizationEngine) DequantizeTensor(quantizedTensor *Tensor, params QuantizationParams) (*Tensor, error) {
	if quantizedTensor == nil {
		return nil, fmt.Errorf("quantized tensor cannot be nil")
	}

	// Create dequantized tensor (always float32)
	dequantizedTensor, err := NewTensor(quantizedTensor.shape, TensorFloat32, quantizedTensor.device, quantizedTensor.deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to create dequantized tensor: %v", err)
	}

	// Set memory pool if available
	if quantizedTensor.memPool != nil {
		dequantizedTensor.SetMemoryPool(quantizedTensor.memPool)
	}

	// Perform dequantization based on source type
	switch quantizedTensor.dtype {
	case TensorInt8:
		err = qe.dequantizeFromInt8(quantizedTensor, dequantizedTensor, params)
	case TensorInt4:
		err = qe.dequantizeFromInt4(quantizedTensor, dequantizedTensor, params)
	default:
		return nil, fmt.Errorf("unsupported dequantization source type: %s", quantizedTensor.dtype)
	}

	if err != nil {
		dequantizedTensor.Free()
		return nil, fmt.Errorf("dequantization failed: %v", err)
	}

	return dequantizedTensor, nil
}

// Helper methods

// findMaxAbsValue finds the maximum absolute value in a tensor
func (qe *QuantizationEngine) findMaxAbsValue(tensor *Tensor) (float32, error) {
	if tensor.device != DeviceCPU {
		return 0, fmt.Errorf("CPU tensor required for max abs calculation")
	}

	numElements := tensor.NumElements()
	maxAbs := float32(0)

	for i := int64(0); i < numElements; i++ {
		val, err := qe.getElementAsFloat32(tensor, i)
		if err != nil {
			return 0, err
		}

		absVal := float32(math.Abs(float64(val)))
		if absVal > maxAbs {
			maxAbs = absVal
		}
	}

	return maxAbs, nil
}

// findMinMaxValues finds the minimum and maximum values in a tensor
func (qe *QuantizationEngine) findMinMaxValues(tensor *Tensor) (float32, float32, error) {
	if tensor.device != DeviceCPU {
		return 0, 0, fmt.Errorf("CPU tensor required for min/max calculation")
	}

	numElements := tensor.NumElements()
	if numElements == 0 {
		return 0, 0, fmt.Errorf("tensor has no elements")
	}

	// Initialize with first element
	firstVal, err := qe.getElementAsFloat32(tensor, 0)
	if err != nil {
		return 0, 0, err
	}

	minVal := firstVal
	maxVal := firstVal

	for i := int64(1); i < numElements; i++ {
		val, err := qe.getElementAsFloat32(tensor, i)
		if err != nil {
			return 0, 0, err
		}

		if val < minVal {
			minVal = val
		}
		if val > maxVal {
			maxVal = val
		}
	}

	return minVal, maxVal, nil
}

// getQuantizationRange returns the quantization range for a given data type
func (qe *QuantizationEngine) getQuantizationRange(dtype TensorDataType) (int32, int32) {
	switch dtype {
	case TensorInt8:
		return -128, 127 // 8-bit signed integer range
	case TensorInt4:
		return -8, 7 // 4-bit signed integer range
	default:
		return -128, 127 // Default to INT8 range
	}
}

// getElementAsFloat32 gets an element from tensor as float32 regardless of original type
func (qe *QuantizationEngine) getElementAsFloat32(tensor *Tensor, index int64) (float32, error) {
	switch tensor.dtype {
	case TensorFloat32:
		byteOffset := index * int64(tensor.dtype.Size())
		ptr := unsafe.Pointer(uintptr(tensor.Data()) + uintptr(byteOffset))
		return *(*float32)(ptr), nil
	case TensorInt8:
		byteOffset := index * int64(tensor.dtype.Size())
		ptr := unsafe.Pointer(uintptr(tensor.Data()) + uintptr(byteOffset))
		return float32(*(*int8)(ptr)), nil
	case TensorInt4:
		byteOffset := index * int64(tensor.dtype.Size())
		ptr := unsafe.Pointer(uintptr(tensor.Data()) + uintptr(byteOffset))
		return float32(*(*int8)(ptr)), nil // INT4 stored as int8
	case TensorInt32:
		byteOffset := index * int64(tensor.dtype.Size())
		ptr := unsafe.Pointer(uintptr(tensor.Data()) + uintptr(byteOffset))
		return float32(*(*int32)(ptr)), nil
	default:
		return 0, fmt.Errorf("unsupported tensor data type for float32 conversion: %s", tensor.dtype)
	}
}

// extractChannel extracts a specific channel from a multi-channel tensor
func (qe *QuantizationEngine) extractChannel(tensor *Tensor, channel int) (*Tensor, error) {
	// This is a simplified implementation
	// In practice, this would extract the specified channel based on tensor layout
	return tensor, nil
}

// learnOptimalScale learns an optimal scale factor using calibration data
func (qe *QuantizationEngine) learnOptimalScale(tensor *Tensor, initialScale float32) (float32, error) {
	// Simplified LSQ implementation
	// In practice, this would use gradient-based optimization
	return initialScale * 0.95, nil // Slight adjustment as placeholder
}

// applyAdaptiveRounding applies adaptive rounding quantization adjustments
func (qe *QuantizationEngine) applyAdaptiveRounding(tensor *Tensor, params QuantizationParams) (QuantizationParams, error) {
	// Simplified ARQ implementation
	// In practice, this would analyze activation distributions and adjust rounding
	adjustedParams := params
	adjustedParams.Scale *= 0.98 // Slight scale adjustment as placeholder
	return adjustedParams, nil
}

// quantizeToInt8 quantizes tensor values to INT8
func (qe *QuantizationEngine) quantizeToInt8(src, dst *Tensor, params QuantizationParams) error {
	if src.device != DeviceCPU || dst.device != DeviceCPU {
		return fmt.Errorf("CPU tensors required for quantization")
	}

	numElements := src.NumElements()

	for i := int64(0); i < numElements; i++ {
		// Get source value as float32
		srcVal, err := qe.getElementAsFloat32(src, i)
		if err != nil {
			return err
		}

		// Quantize: q = round(x/scale) + zero_point
		quantizedVal := int32(math.Round(float64(srcVal/params.Scale))) + params.ZeroPoint

		// Clamp to INT8 range
		if quantizedVal < -128 {
			quantizedVal = -128
		}
		if quantizedVal > 127 {
			quantizedVal = 127
		}

		// Store quantized value
		byteOffset := i * int64(dst.dtype.Size())
		ptr := unsafe.Pointer(uintptr(dst.Data()) + uintptr(byteOffset))
		*(*int8)(ptr) = int8(quantizedVal)
	}

	return nil
}

// quantizeToInt4 quantizes tensor values to INT4
func (qe *QuantizationEngine) quantizeToInt4(src, dst *Tensor, params QuantizationParams) error {
	if src.device != DeviceCPU || dst.device != DeviceCPU {
		return fmt.Errorf("CPU tensors required for quantization")
	}

	numElements := src.NumElements()

	for i := int64(0); i < numElements; i++ {
		// Get source value as float32
		srcVal, err := qe.getElementAsFloat32(src, i)
		if err != nil {
			return err
		}

		// Quantize: q = round(x/scale) + zero_point
		quantizedVal := int32(math.Round(float64(srcVal/params.Scale))) + params.ZeroPoint

		// Clamp to INT4 range (-8 to 7)
		if quantizedVal < -8 {
			quantizedVal = -8
		}
		if quantizedVal > 7 {
			quantizedVal = 7
		}

		// Store quantized value (packed as int8 for now)
		byteOffset := i * int64(dst.dtype.Size())
		ptr := unsafe.Pointer(uintptr(dst.Data()) + uintptr(byteOffset))
		*(*int8)(ptr) = int8(quantizedVal)
	}

	return nil
}

// dequantizeFromInt8 dequantizes INT8 values back to float32
func (qe *QuantizationEngine) dequantizeFromInt8(src, dst *Tensor, params QuantizationParams) error {
	if src.device != DeviceCPU || dst.device != DeviceCPU {
		return fmt.Errorf("CPU tensors required for dequantization")
	}

	numElements := src.NumElements()

	for i := int64(0); i < numElements; i++ {
		// Get quantized value
		byteOffset := i * int64(src.dtype.Size())
		ptr := unsafe.Pointer(uintptr(src.Data()) + uintptr(byteOffset))
		quantizedVal := int32(*(*int8)(ptr))

		// Dequantize: x = scale * (q - zero_point)
		dequantizedVal := params.Scale * float32(quantizedVal-params.ZeroPoint)

		// Store dequantized value
		dstByteOffset := i * int64(dst.dtype.Size())
		dstPtr := unsafe.Pointer(uintptr(dst.Data()) + uintptr(dstByteOffset))
		*(*float32)(dstPtr) = dequantizedVal
	}

	return nil
}

// dequantizeFromInt4 dequantizes INT4 values back to float32
func (qe *QuantizationEngine) dequantizeFromInt4(src, dst *Tensor, params QuantizationParams) error {
	if src.device != DeviceCPU || dst.device != DeviceCPU {
		return fmt.Errorf("CPU tensors required for dequantization")
	}

	numElements := src.NumElements()

	for i := int64(0); i < numElements; i++ {
		// Get quantized value (stored as int8)
		byteOffset := i * int64(src.dtype.Size())
		ptr := unsafe.Pointer(uintptr(src.Data()) + uintptr(byteOffset))
		quantizedVal := int32(*(*int8)(ptr))

		// Dequantize: x = scale * (q - zero_point)
		dequantizedVal := params.Scale * float32(quantizedVal-params.ZeroPoint)

		// Store dequantized value
		dstByteOffset := i * int64(dst.dtype.Size())
		dstPtr := unsafe.Pointer(uintptr(dst.Data()) + uintptr(dstByteOffset))
		*(*float32)(dstPtr) = dequantizedVal
	}

	return nil
}

// GetQuantizationInfo returns information about current quantization settings
func (qe *QuantizationEngine) GetQuantizationInfo() map[string]interface{} {
	return map[string]interface{}{
		"method":              qe.config.Method.String(),
		"target_type":         qe.getTargetTypeString(),
		"use_calibration":     qe.config.UseCalibration,
		"preserve_accuracy":   qe.config.PreserveAccuracy,
		"per_channel":         qe.config.PerChannelQuantization,
		"calibration_samples": qe.calibrationDataset.GetSampleCount(),
		"cached_params":       len(qe.quantizationCache),
	}
}

// getTargetTypeString returns string representation including INT4
func (qe *QuantizationEngine) getTargetTypeString() string {
	return qe.config.TargetType.String()
}

// LayerType represents different types of neural network layers
type LayerType string

const (
	LayerTypeConvolution   LayerType = "Convolution"
	LayerTypeLinear        LayerType = "Linear"
	LayerTypeActivation    LayerType = "Activation"
	LayerTypePooling       LayerType = "Pooling"
	LayerTypeNormalization LayerType = "Normalization"
	LayerTypeAttention     LayerType = "Attention"
	LayerTypeEmbedding     LayerType = "Embedding"
	LayerTypeOther         LayerType = "Other"
)

// Using existing PrecisionMode type from config_interface.go for consistency

// SensitivityMetrics contains sensitivity analysis results for a layer
type SensitivityMetrics struct {
	GradientMagnitude  float32   `json:"gradient_magnitude"`
	HessianTrace       float32   `json:"hessian_trace"`
	ActivationVariance float32   `json:"activation_variance"`
	WeightVariance     float32   `json:"weight_variance"`
	OutputSensitivity  float32   `json:"output_sensitivity"`
	ChannelSensitivity []float32 `json:"channel_sensitivity,omitempty"`
	OverallSensitivity float32   `json:"overall_sensitivity"`
	LastUpdated        int64     `json:"last_updated"`
}

// Layer represents a neural network layer with quantization information
type Layer struct {
	ID                 string                         `json:"id"`
	Name               string                         `json:"name"`
	Type               LayerType                      `json:"type"`
	InputShape         []int64                        `json:"input_shape"`
	OutputShape        []int64                        `json:"output_shape"`
	WeightShape        []int64                        `json:"weight_shape,omitempty"`
	BiasShape          []int64                        `json:"bias_shape,omitempty"`
	AssignedPrecision  PrecisionMode                  `json:"assigned_precision"`
	OriginalPrecision  PrecisionMode                  `json:"original_precision"`
	SensitivityScore   *SensitivityMetrics            `json:"sensitivity_score,omitempty"`
	QuantizationParams map[string]*QuantizationParams `json:"quantization_params,omitempty"`
	HardwareHints      map[string]interface{}         `json:"hardware_hints,omitempty"`
	IsQuantized        bool                           `json:"is_quantized"`
	MemoryFootprint    int64                          `json:"memory_footprint"`
	mutex              sync.RWMutex
}

// NewLayer creates a new layer with default settings
func NewLayer(id, name string, layerType LayerType) *Layer {
	return &Layer{
		ID:                 id,
		Name:               name,
		Type:               layerType,
		AssignedPrecision:  PrecisionFP32,
		OriginalPrecision:  PrecisionFP32,
		QuantizationParams: make(map[string]*QuantizationParams),
		HardwareHints:      make(map[string]interface{}),
		IsQuantized:        false,
	}
}

// SetPrecision sets the precision level for this layer
func (l *Layer) SetPrecision(precision PrecisionMode) {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	l.AssignedPrecision = precision
}

// GetPrecision returns the current precision level
func (l *Layer) GetPrecision() PrecisionMode {
	l.mutex.RLock()
	defer l.mutex.RUnlock()
	return l.AssignedPrecision
}

// UpdateSensitivity updates the sensitivity metrics for this layer
func (l *Layer) UpdateSensitivity(metrics *SensitivityMetrics) {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	l.SensitivityScore = metrics
}

// GetSensitivity returns the current sensitivity metrics
func (l *Layer) GetSensitivity() *SensitivityMetrics {
	l.mutex.RLock()
	defer l.mutex.RUnlock()
	return l.SensitivityScore
}

// EstimateMemoryFootprint calculates the memory footprint for this layer at current precision
func (l *Layer) EstimateMemoryFootprint() int64 {
	var totalElements int64 = 1

	// Calculate weight elements
	for _, dim := range l.WeightShape {
		totalElements *= dim
	}

	// Add bias elements if present
	if len(l.BiasShape) > 0 {
		biasElements := int64(1)
		for _, dim := range l.BiasShape {
			biasElements *= dim
		}
		totalElements += biasElements
	}

	// Calculate bytes per element based on precision
	var bytesPerElement int64
	switch l.AssignedPrecision {
	case PrecisionFP32:
		bytesPerElement = 4
	case PrecisionFP16:
		bytesPerElement = 2
	case PrecisionINT8:
		bytesPerElement = 1
	case PrecisionINT4:
		bytesPerElement = 1 // 2 values per byte, but we round up
	default:
		bytesPerElement = 4
	}

	l.MemoryFootprint = totalElements * bytesPerElement
	return l.MemoryFootprint
}

// MixedPrecisionConfig contains configuration for mixed-precision quantization
type MixedPrecisionConfig struct {
	EnableMixedPrecision     bool                        `json:"enable_mixed_precision"`
	SensitivityThresholds    map[string]float32          `json:"sensitivity_thresholds"`
	LayerTypePrecisionMap    map[LayerType]PrecisionMode `json:"layer_type_precision_map"`
	HardwareConstraints      *HardwareConstraints        `json:"hardware_constraints,omitempty"`
	AutoPrecisionAssignment  bool                        `json:"auto_precision_assignment"`
	PreserveSensitiveLayers  bool                        `json:"preserve_sensitive_layers"`
	MemoryBudgetMB           int64                       `json:"memory_budget_mb"`
	TargetAccuracyThreshold  float32                     `json:"target_accuracy_threshold"`
	EnablePerChannelAnalysis bool                        `json:"enable_per_channel_analysis"`
}

// HardwareConstraints contains hardware-specific constraints for precision assignment
type HardwareConstraints struct {
	SupportedPrecisions []PrecisionMode        `json:"supported_precisions"`
	PreferredPrecisions []PrecisionMode        `json:"preferred_precisions"`
	MemoryBandwidthGBps float32                `json:"memory_bandwidth_gbps"`
	ComputeCapabilities map[string]interface{} `json:"compute_capabilities"`
	MaxMemoryMB         int64                  `json:"max_memory_mb"`
}

// DefaultMixedPrecisionConfig returns a default mixed-precision configuration
func DefaultMixedPrecisionConfig() MixedPrecisionConfig {
	return MixedPrecisionConfig{
		EnableMixedPrecision: true,
		SensitivityThresholds: map[string]float32{
			"high":   0.8,
			"medium": 0.5,
			"low":    0.2,
		},
		LayerTypePrecisionMap: map[LayerType]PrecisionMode{
			LayerTypeConvolution:   PrecisionINT8,
			LayerTypeLinear:        PrecisionINT8,
			LayerTypeActivation:    PrecisionFP16,
			LayerTypePooling:       PrecisionFP16,
			LayerTypeNormalization: PrecisionFP32,
			LayerTypeAttention:     PrecisionFP16,
			LayerTypeEmbedding:     PrecisionINT8,
			LayerTypeOther:         PrecisionFP32,
		},
		AutoPrecisionAssignment:  true,
		PreserveSensitiveLayers:  true,
		MemoryBudgetMB:           1024,
		TargetAccuracyThreshold:  0.95,
		EnablePerChannelAnalysis: true,
	}
}

// MixedPrecisionEngine manages mixed-precision quantization
type MixedPrecisionEngine struct {
	config             MixedPrecisionConfig
	layers             []*Layer
	quantizationEngine *QuantizationEngine
	sensitivityCache   map[string]*SensitivityMetrics
	hardwareInfo       *GPUInfo
	mutex              sync.RWMutex
}

// NewMixedPrecisionEngine creates a new mixed-precision quantization engine
func NewMixedPrecisionEngine(config MixedPrecisionConfig, hardwareInfo *GPUInfo) *MixedPrecisionEngine {
	return &MixedPrecisionEngine{
		config: config,
		layers: make([]*Layer, 0),
		quantizationEngine: NewQuantizationEngine(QuantizationConfig{
			Method:     QuantizationSymmetric,
			TargetType: TensorInt8,
		}),
		sensitivityCache: make(map[string]*SensitivityMetrics),
		hardwareInfo:     hardwareInfo,
	}
}

// AddLayer adds a layer to the mixed-precision engine
func (mpe *MixedPrecisionEngine) AddLayer(layer *Layer) {
	mpe.mutex.Lock()
	defer mpe.mutex.Unlock()
	mpe.layers = append(mpe.layers, layer)
}

// GetLayers returns all layers managed by this engine
func (mpe *MixedPrecisionEngine) GetLayers() []*Layer {
	mpe.mutex.RLock()
	defer mpe.mutex.RUnlock()
	layersCopy := make([]*Layer, len(mpe.layers))
	copy(layersCopy, mpe.layers)
	return layersCopy
}

// AnalyzeSensitivity performs sensitivity analysis on all layers
func (mpe *MixedPrecisionEngine) AnalyzeSensitivity(calibrationData []*Tensor) error {
	if len(calibrationData) == 0 {
		return fmt.Errorf("calibration data is required for sensitivity analysis")
	}

	mpe.mutex.Lock()
	defer mpe.mutex.Unlock()

	for _, layer := range mpe.layers {
		metrics, err := mpe.computeLayerSensitivity(layer, calibrationData)
		if err != nil {
			return fmt.Errorf("failed to compute sensitivity for layer %s: %v", layer.ID, err)
		}

		layer.UpdateSensitivity(metrics)
		mpe.sensitivityCache[layer.ID] = metrics
	}

	return nil
}

// computeLayerSensitivity computes sensitivity metrics for a single layer
func (mpe *MixedPrecisionEngine) computeLayerSensitivity(layer *Layer, calibrationData []*Tensor) (*SensitivityMetrics, error) {
	metrics := &SensitivityMetrics{
		LastUpdated: getCurrentTimestamp(),
	}

	// Compute gradient-based sensitivity
	gradientMag, err := mpe.computeGradientMagnitude(layer, calibrationData)
	if err != nil {
		return nil, err
	}
	metrics.GradientMagnitude = gradientMag

	// Compute Hessian-based sensitivity
	hessianTrace, err := mpe.computeHessianTrace(layer, calibrationData)
	if err != nil {
		return nil, err
	}
	metrics.HessianTrace = hessianTrace

	// Compute activation variance
	activationVar, err := mpe.computeActivationVariance(layer, calibrationData)
	if err != nil {
		return nil, err
	}
	metrics.ActivationVariance = activationVar

	// Compute weight variance (if layer has weights)
	if len(layer.WeightShape) > 0 {
		weightVar, err := mpe.computeWeightVariance(layer)
		if err != nil {
			return nil, err
		}
		metrics.WeightVariance = weightVar
	}

	// Compute per-channel sensitivity if enabled
	if mpe.config.EnablePerChannelAnalysis && layer.Type == LayerTypeConvolution {
		channelSens, err := mpe.computePerChannelSensitivity(layer, calibrationData)
		if err != nil {
			return nil, err
		}
		metrics.ChannelSensitivity = channelSens
	}

	// Compute overall sensitivity score
	metrics.OverallSensitivity = mpe.computeOverallSensitivity(metrics)

	return metrics, nil
}

// computeGradientMagnitude computes the magnitude of gradients for a layer
func (mpe *MixedPrecisionEngine) computeGradientMagnitude(layer *Layer, calibrationData []*Tensor) (float32, error) {
	// Simplified gradient magnitude computation
	// In a real implementation, this would require backpropagation through the layer
	var totalMagnitude float64 = 0.0
	var count int64 = 0

	for _, tensor := range calibrationData {
		if tensor == nil || tensor.Size() == 0 {
			continue
		}

		// Approximate gradient magnitude using tensor statistics
		mean, err := mpe.computeTensorMean(tensor)
		if err != nil {
			continue
		}

		variance, err := mpe.computeTensorVariance(tensor, mean)
		if err != nil {
			continue
		}

		// Use variance as a proxy for gradient magnitude
		totalMagnitude += math.Sqrt(variance)
		count++
	}

	if count == 0 {
		return 0.0, nil
	}

	return float32(totalMagnitude / float64(count)), nil
}

// computeHessianTrace computes an approximation of the Hessian trace for a layer
func (mpe *MixedPrecisionEngine) computeHessianTrace(layer *Layer, calibrationData []*Tensor) (float32, error) {
	// Simplified Hessian trace approximation
	// In practice, this would use second-order derivatives
	var totalTrace float64 = 0.0
	var count int64 = 0

	for _, tensor := range calibrationData {
		if tensor == nil || tensor.Size() == 0 {
			continue
		}

		// Approximate Hessian trace using tensor second moments
		secondMoment, err := mpe.computeTensorSecondMoment(tensor)
		if err != nil {
			continue
		}

		totalTrace += secondMoment
		count++
	}

	if count == 0 {
		return 0.0, nil
	}

	return float32(totalTrace / float64(count)), nil
}

// computeActivationVariance computes the variance of activations for a layer
func (mpe *MixedPrecisionEngine) computeActivationVariance(layer *Layer, calibrationData []*Tensor) (float32, error) {
	var totalVariance float64 = 0.0
	var count int64 = 0

	for _, tensor := range calibrationData {
		if tensor == nil || tensor.Size() == 0 {
			continue
		}

		mean, err := mpe.computeTensorMean(tensor)
		if err != nil {
			continue
		}

		variance, err := mpe.computeTensorVariance(tensor, mean)
		if err != nil {
			continue
		}

		totalVariance += variance
		count++
	}

	if count == 0 {
		return 0.0, nil
	}

	return float32(totalVariance / float64(count)), nil
}

// computeWeightVariance computes the variance of weights for a layer
func (mpe *MixedPrecisionEngine) computeWeightVariance(layer *Layer) (float32, error) {
	// In a real implementation, this would access the actual layer weights
	// For now, we'll return a placeholder based on layer characteristics

	var totalElements int64 = 1
	for _, dim := range layer.WeightShape {
		totalElements *= dim
	}

	// Use layer size as a proxy for weight variance
	// Larger layers tend to have more distributed weights
	variance := 1.0 / math.Sqrt(float64(totalElements))
	return float32(variance), nil
}

// computePerChannelSensitivity computes per-channel sensitivity for convolutional layers
func (mpe *MixedPrecisionEngine) computePerChannelSensitivity(layer *Layer, calibrationData []*Tensor) ([]float32, error) {
	if layer.Type != LayerTypeConvolution || len(layer.WeightShape) < 4 {
		return nil, fmt.Errorf("per-channel sensitivity only supported for convolutional layers")
	}

	numChannels := layer.WeightShape[0] // Assuming NCHW format
	channelSensitivity := make([]float32, numChannels)

	for i := int64(0); i < numChannels; i++ {
		// Compute sensitivity for each channel
		// This is a simplified implementation
		sensitivity := float32(1.0 / (1.0 + float64(i)*0.1))
		channelSensitivity[i] = sensitivity
	}

	return channelSensitivity, nil
}

// computeOverallSensitivity computes an overall sensitivity score from individual metrics
func (mpe *MixedPrecisionEngine) computeOverallSensitivity(metrics *SensitivityMetrics) float32 {
	// Weighted combination of different sensitivity metrics
	weights := map[string]float32{
		"gradient":   0.3,
		"hessian":    0.3,
		"activation": 0.2,
		"weight":     0.2,
	}

	overallScore := weights["gradient"]*metrics.GradientMagnitude +
		weights["hessian"]*metrics.HessianTrace +
		weights["activation"]*metrics.ActivationVariance +
		weights["weight"]*metrics.WeightVariance

	// Normalize to [0, 1] range
	return float32(math.Min(1.0, math.Max(0.0, float64(overallScore))))
}

// AssignPrecisions automatically assigns precision levels to all layers
func (mpe *MixedPrecisionEngine) AssignPrecisions() error {
	if !mpe.config.AutoPrecisionAssignment {
		return fmt.Errorf("automatic precision assignment is disabled")
	}

	mpe.mutex.Lock()
	defer mpe.mutex.Unlock()

	// Sort layers by sensitivity (most sensitive first)
	layersBySensitivity := make([]*Layer, len(mpe.layers))
	copy(layersBySensitivity, mpe.layers)

	sort.Slice(layersBySensitivity, func(i, j int) bool {
		sensI := layersBySensitivity[i].GetSensitivity()
		sensJ := layersBySensitivity[j].GetSensitivity()

		if sensI == nil && sensJ == nil {
			return false
		}
		if sensI == nil {
			return false
		}
		if sensJ == nil {
			return true
		}

		return sensI.OverallSensitivity > sensJ.OverallSensitivity
	})

	// Assign precisions based on sensitivity and hardware constraints
	memoryBudget := mpe.config.MemoryBudgetMB * 1024 * 1024 // Convert to bytes
	currentMemoryUsage := int64(0)

	for _, layer := range layersBySensitivity {
		precision := mpe.determineBestPrecision(layer, memoryBudget-currentMemoryUsage)
		layer.SetPrecision(precision)

		// Update memory usage
		layer.EstimateMemoryFootprint()
		currentMemoryUsage += layer.MemoryFootprint

		// Check if we've exceeded memory budget
		if currentMemoryUsage > memoryBudget {
			// Fallback to lower precision for remaining layers
			for _, remainingLayer := range layersBySensitivity {
				if remainingLayer.GetPrecision() == remainingLayer.OriginalPrecision {
					remainingLayer.SetPrecision(mpe.getLowestSupportedPrecision())
				}
			}
			break
		}
	}

	return nil
}

// determineBestPrecision determines the best precision for a layer given constraints
func (mpe *MixedPrecisionEngine) determineBestPrecision(layer *Layer, availableMemory int64) PrecisionMode {
	sensitivity := layer.GetSensitivity()

	// If no sensitivity data, use layer type defaults
	if sensitivity == nil {
		if defaultPrecision, exists := mpe.config.LayerTypePrecisionMap[layer.Type]; exists {
			return defaultPrecision
		}
		return PrecisionFP32
	}

	// Determine precision based on sensitivity thresholds
	overallSensitivity := sensitivity.OverallSensitivity

	var targetPrecision PrecisionMode
	if overallSensitivity >= mpe.config.SensitivityThresholds["high"] {
		targetPrecision = PrecisionFP32
	} else if overallSensitivity >= mpe.config.SensitivityThresholds["medium"] {
		targetPrecision = PrecisionFP16
	} else if overallSensitivity >= mpe.config.SensitivityThresholds["low"] {
		targetPrecision = PrecisionINT8
	} else {
		targetPrecision = PrecisionINT4
	}

	// Check hardware constraints
	targetPrecision = mpe.applyHardwareConstraints(targetPrecision)

	// Check memory constraints
	targetPrecision = mpe.applyMemoryConstraints(layer, targetPrecision, availableMemory)

	return targetPrecision
}

// applyHardwareConstraints applies hardware-specific precision constraints
func (mpe *MixedPrecisionEngine) applyHardwareConstraints(targetPrecision PrecisionMode) PrecisionMode {
	if mpe.config.HardwareConstraints == nil {
		return targetPrecision
	}

	// Check if target precision is supported
	for _, supported := range mpe.config.HardwareConstraints.SupportedPrecisions {
		if supported == targetPrecision {
			return targetPrecision
		}
	}

	// Find the closest supported precision
	precisionOrder := []PrecisionMode{PrecisionFP32, PrecisionFP16, PrecisionINT8, PrecisionINT4}
	targetIndex := -1
	for i, p := range precisionOrder {
		if p == targetPrecision {
			targetIndex = i
			break
		}
	}

	if targetIndex == -1 {
		return PrecisionFP32 // Default fallback
	}

	// Find closest supported precision
	for offset := 0; offset < len(precisionOrder); offset++ {
		// Check higher precision first
		if targetIndex-offset >= 0 {
			candidate := precisionOrder[targetIndex-offset]
			for _, supported := range mpe.config.HardwareConstraints.SupportedPrecisions {
				if supported == candidate {
					return candidate
				}
			}
		}

		// Then check lower precision
		if targetIndex+offset < len(precisionOrder) {
			candidate := precisionOrder[targetIndex+offset]
			for _, supported := range mpe.config.HardwareConstraints.SupportedPrecisions {
				if supported == candidate {
					return candidate
				}
			}
		}
	}

	return PrecisionFP32 // Final fallback
}

// applyMemoryConstraints applies memory-based precision constraints
func (mpe *MixedPrecisionEngine) applyMemoryConstraints(layer *Layer, targetPrecision PrecisionMode, availableMemory int64) PrecisionMode {
	// Estimate memory usage for target precision
	originalPrecision := layer.AssignedPrecision
	layer.SetPrecision(targetPrecision)
	requiredMemory := layer.EstimateMemoryFootprint()
	layer.SetPrecision(originalPrecision) // Restore original

	if requiredMemory <= availableMemory {
		return targetPrecision
	}

	// Try lower precisions
	precisionOrder := []PrecisionMode{PrecisionINT4, PrecisionINT8, PrecisionFP16, PrecisionFP32}
	for _, precision := range precisionOrder {
		layer.SetPrecision(precision)
		requiredMemory = layer.EstimateMemoryFootprint()
		layer.SetPrecision(originalPrecision) // Restore original

		if requiredMemory <= availableMemory {
			return precision
		}
	}

	return PrecisionINT4 // Most memory-efficient option
}

// getLowestSupportedPrecision returns the lowest precision supported by hardware
func (mpe *MixedPrecisionEngine) getLowestSupportedPrecision() PrecisionMode {
	if mpe.config.HardwareConstraints == nil || len(mpe.config.HardwareConstraints.SupportedPrecisions) == 0 {
		return PrecisionINT4
	}

	precisionOrder := []PrecisionMode{PrecisionINT4, PrecisionINT8, PrecisionFP16, PrecisionFP32}
	for _, precision := range precisionOrder {
		for _, supported := range mpe.config.HardwareConstraints.SupportedPrecisions {
			if supported == precision {
				return precision
			}
		}
	}

	return mpe.config.HardwareConstraints.SupportedPrecisions[0] // First supported precision
}

// QuantizeLayer quantizes a specific layer using the assigned precision
func (mpe *MixedPrecisionEngine) QuantizeLayer(layer *Layer, weights *Tensor) (*Tensor, error) {
	if layer == nil || weights == nil {
		return nil, fmt.Errorf("layer and weights cannot be nil")
	}

	precision := layer.GetPrecision()

	// Convert precision level to quantization method
	switch precision {
	case PrecisionINT8:
		// Update quantization engine config for INT8
		mpe.quantizationEngine.config.Method = QuantizationSymmetric
		mpe.quantizationEngine.config.TargetType = TensorInt8
	case PrecisionINT4:
		// Update quantization engine config for INT4
		mpe.quantizationEngine.config.Method = QuantizationAsymmetric
		mpe.quantizationEngine.config.TargetType = TensorInt4
	case PrecisionFP16:
		// For FP16, we'd need to implement FP16 tensor type
		// For now, return original tensor
		return weights, nil
	case PrecisionFP32:
		// No quantization needed
		return weights, nil
	default:
		return nil, fmt.Errorf("unsupported precision level: %s", precision)
	}

	// Use the existing quantization engine
	quantizedTensor, err := mpe.quantizationEngine.QuantizeTensor(weights)
	if err != nil {
		return nil, fmt.Errorf("failed to quantize layer %s: %v", layer.ID, err)
	}

	// Store quantization parameters in the layer
	params, err := mpe.quantizationEngine.ComputeQuantizationParams(weights)
	if err == nil {
		if layer.QuantizationParams == nil {
			layer.QuantizationParams = make(map[string]*QuantizationParams)
		}
		layer.QuantizationParams["weights"] = &params
	}

	layer.IsQuantized = true
	return quantizedTensor, nil
}

// GetMixedPrecisionSummary returns a summary of the mixed-precision configuration
func (mpe *MixedPrecisionEngine) GetMixedPrecisionSummary() map[string]interface{} {
	mpe.mutex.RLock()
	defer mpe.mutex.RUnlock()

	summary := make(map[string]interface{})
	precisionCounts := make(map[PrecisionMode]int)
	totalMemory := int64(0)
	sensitivityStats := make(map[string]float32)

	var totalSensitivity float32 = 0.0
	var minSensitivity float32 = 1.0
	var maxSensitivity float32 = 0.0
	sensitiveLayerCount := 0

	for _, layer := range mpe.layers {
		precision := layer.GetPrecision()
		precisionCounts[precision]++
		totalMemory += layer.MemoryFootprint

		if sensitivity := layer.GetSensitivity(); sensitivity != nil {
			sens := sensitivity.OverallSensitivity
			totalSensitivity += sens
			if sens > maxSensitivity {
				maxSensitivity = sens
			}
			if sens < minSensitivity {
				minSensitivity = sens
			}
			if sens >= mpe.config.SensitivityThresholds["high"] {
				sensitiveLayerCount++
			}
		}
	}

	if len(mpe.layers) > 0 {
		sensitivityStats["average"] = totalSensitivity / float32(len(mpe.layers))
		sensitivityStats["min"] = minSensitivity
		sensitivityStats["max"] = maxSensitivity
	}

	summary["total_layers"] = len(mpe.layers)
	summary["precision_distribution"] = precisionCounts
	summary["total_memory_bytes"] = totalMemory
	summary["total_memory_mb"] = float64(totalMemory) / (1024 * 1024)
	summary["memory_budget_mb"] = mpe.config.MemoryBudgetMB
	summary["memory_utilization"] = float64(totalMemory) / float64(mpe.config.MemoryBudgetMB*1024*1024)
	summary["sensitivity_statistics"] = sensitivityStats
	summary["sensitive_layers_count"] = sensitiveLayerCount
	summary["mixed_precision_enabled"] = mpe.config.EnableMixedPrecision

	return summary
}

// Helper functions for tensor statistics

func (mpe *MixedPrecisionEngine) computeTensorMean(tensor *Tensor) (float64, error) {
	if tensor == nil || tensor.Size() == 0 {
		return 0.0, fmt.Errorf("invalid tensor")
	}

	sum := float64(0)
	size := tensor.Size()

	for i := int64(0); i < size; i++ {
		val, err := mpe.quantizationEngine.getElementAsFloat32(tensor, i)
		if err != nil {
			return 0.0, err
		}
		sum += float64(val)
	}

	return sum / float64(size), nil
}

func (mpe *MixedPrecisionEngine) computeTensorVariance(tensor *Tensor, mean float64) (float64, error) {
	if tensor == nil || tensor.Size() == 0 {
		return 0.0, fmt.Errorf("invalid tensor")
	}

	sumSquaredDiff := float64(0)
	size := tensor.Size()

	for i := int64(0); i < size; i++ {
		val, err := mpe.quantizationEngine.getElementAsFloat32(tensor, i)
		if err != nil {
			return 0.0, err
		}
		diff := float64(val) - mean
		sumSquaredDiff += diff * diff
	}

	return sumSquaredDiff / float64(size), nil
}

func (mpe *MixedPrecisionEngine) computeTensorSecondMoment(tensor *Tensor) (float64, error) {
	if tensor == nil || tensor.Size() == 0 {
		return 0.0, fmt.Errorf("invalid tensor")
	}

	sumSquared := float64(0)
	size := tensor.Size()

	for i := int64(0); i < size; i++ {
		val, err := mpe.quantizationEngine.getElementAsFloat32(tensor, i)
		if err != nil {
			return 0.0, err
		}
		sumSquared += float64(val) * float64(val)
	}

	return sumSquared / float64(size), nil
}

func getCurrentTimestamp() int64 {
	// In a real implementation, this would return current Unix timestamp
	return 1640995200 // Placeholder timestamp
}

// Advanced calibration methods for the QuantizationEngine

// CalibrateWithKLDivergence performs KL divergence-based calibration
func (qe *QuantizationEngine) CalibrateWithKLDivergence(tensor *Tensor, numBins int) (QuantizationParams, error) {
	if tensor == nil {
		return QuantizationParams{}, fmt.Errorf("input tensor cannot be nil")
	}

	if numBins <= 0 {
		numBins = 2048 // Default number of bins for histogram
	}

	// Get calibration samples
	calibrationSamples, err := qe.calibrationDataset.SelectRepresentativeSamples(qe.config.CalibrationConfig.MaxSampleSize)
	if err != nil {
		return QuantizationParams{}, fmt.Errorf("failed to select calibration samples: %v", err)
	}

	if len(calibrationSamples) == 0 {
		// Fallback to asymmetric quantization if no calibration data
		return qe.computeAsymmetricParams(tensor)
	}

	// Find the global min/max across all calibration samples
	globalMin := float32(math.Inf(1))
	globalMax := float32(math.Inf(-1))

	for _, sample := range calibrationSamples {
		min, max, err := qe.findMinMaxValues(sample.Data)
		if err != nil {
			continue
		}
		if min < globalMin {
			globalMin = min
		}
		if max > globalMax {
			globalMax = max
		}
	}

	// Create reference histogram from calibration data
	refHistogram, binEdges, err := qe.createHistogram(calibrationSamples, numBins, globalMin, globalMax)
	if err != nil {
		return QuantizationParams{}, err
	}

	// Get quantization range
	qmin, qmax := qe.getQuantizationRange(qe.config.TargetType)

	// Find optimal threshold using KL divergence
	bestThreshold := globalMax
	minKLDiv := float64(math.Inf(1))

	// Try different thresholds
	numThresholds := 100
	thresholdStep := (globalMax - globalMin) / float32(numThresholds)

	for i := 1; i < numThresholds; i++ {
		threshold := globalMin + float32(i)*thresholdStep

		// Create quantized histogram with this threshold
		quantizedHist, err := qe.createQuantizedHistogram(refHistogram, binEdges, threshold, qmin, qmax)
		if err != nil {
			continue
		}

		// Compute KL divergence
		klDiv := qe.computeKLDivergence(refHistogram, quantizedHist)
		if klDiv < minKLDiv {
			minKLDiv = klDiv
			bestThreshold = threshold
		}
	}

	// Compute final quantization parameters using the best threshold
	scale := (bestThreshold - globalMin) / float32(qmax-qmin)
	zeroPoint := int32(float32(qmin) - globalMin/scale)

	// Clamp zero-point to valid range
	if zeroPoint < int32(qmin) {
		zeroPoint = int32(qmin)
	}
	if zeroPoint > int32(qmax) {
		zeroPoint = int32(qmax)
	}

	return QuantizationParams{
		Scale:     scale,
		ZeroPoint: zeroPoint,
		MinValue:  globalMin,
		MaxValue:  bestThreshold,
	}, nil
}

// CalibrateWithMSE performs MSE-based calibration
func (qe *QuantizationEngine) CalibrateWithMSE(tensor *Tensor) (QuantizationParams, error) {
	if tensor == nil {
		return QuantizationParams{}, fmt.Errorf("input tensor cannot be nil")
	}

	// Get calibration samples
	calibrationSamples, err := qe.calibrationDataset.SelectRepresentativeSamples(qe.config.CalibrationConfig.MaxSampleSize)
	if err != nil {
		return QuantizationParams{}, fmt.Errorf("failed to select calibration samples: %v", err)
	}

	if len(calibrationSamples) == 0 {
		// Fallback to asymmetric quantization if no calibration data
		return qe.computeAsymmetricParams(tensor)
	}

	// Find the global min/max across all calibration samples
	globalMin := float32(math.Inf(1))
	globalMax := float32(math.Inf(-1))

	for _, sample := range calibrationSamples {
		min, max, err := qe.findMinMaxValues(sample.Data)
		if err != nil {
			continue
		}
		if min < globalMin {
			globalMin = min
		}
		if max > globalMax {
			globalMax = max
		}
	}

	// Get quantization range
	qmin, qmax := qe.getQuantizationRange(qe.config.TargetType)

	// Find optimal scale and zero-point by minimizing MSE
	bestScale := (globalMax - globalMin) / float32(qmax-qmin)
	bestZeroPoint := int32(float32(qmin) - globalMin/bestScale)
	minMSE := float64(math.Inf(1))

	// Try different scale factors around the initial estimate
	scaleRange := bestScale * 0.5 // Search within ±50% of initial scale
	numScales := 50

	for i := 0; i < numScales; i++ {
		scale := bestScale - scaleRange + (2*scaleRange*float32(i))/float32(numScales-1)
		if scale <= 0 {
			continue
		}

		// Try different zero-points
		for zp := qmin; zp <= qmax; zp += (qmax - qmin) / 20 {
			zeroPoint := int32(zp)

			// Compute MSE for this scale and zero-point combination
			totalMSE := float64(0)
			totalElements := int64(0)

			for _, sample := range calibrationSamples {
				mse, count, err := qe.computeQuantizationMSE(sample.Data, scale, zeroPoint, qmin, qmax)
				if err != nil {
					continue
				}
				totalMSE += mse * float64(count)
				totalElements += count
			}

			if totalElements > 0 {
				avgMSE := totalMSE / float64(totalElements)
				if avgMSE < minMSE {
					minMSE = avgMSE
					bestScale = scale
					bestZeroPoint = zeroPoint
				}
			}
		}
	}

	// Clamp zero-point to valid range
	if bestZeroPoint < int32(qmin) {
		bestZeroPoint = int32(qmin)
	}
	if bestZeroPoint > int32(qmax) {
		bestZeroPoint = int32(qmax)
	}

	return QuantizationParams{
		Scale:     bestScale,
		ZeroPoint: bestZeroPoint,
		MinValue:  globalMin,
		MaxValue:  globalMax,
	}, nil
}

// createHistogram creates a histogram from calibration samples
func (qe *QuantizationEngine) createHistogram(samples []*CalibrationSample, numBins int, minVal, maxVal float32) ([]int64, []float32, error) {
	if len(samples) == 0 {
		return nil, nil, fmt.Errorf("no calibration samples provided")
	}

	histogram := make([]int64, numBins)
	binEdges := make([]float32, numBins+1)

	// Create bin edges
	binWidth := (maxVal - minVal) / float32(numBins)
	for i := 0; i <= numBins; i++ {
		binEdges[i] = minVal + float32(i)*binWidth
	}

	// Fill histogram
	for _, sample := range samples {
		for i := int64(0); i < sample.Data.NumElements(); i++ {
			val, err := qe.calibrationDataset.getElementAsFloat32(sample.Data, i)
			if err != nil {
				continue
			}

			// Find appropriate bin
			binIndex := int((val - minVal) / binWidth)
			if binIndex < 0 {
				binIndex = 0
			}
			if binIndex >= numBins {
				binIndex = numBins - 1
			}

			histogram[binIndex] += int64(sample.Weight)
		}
	}

	return histogram, binEdges, nil
}

// createQuantizedHistogram creates a histogram after quantization with given threshold
func (qe *QuantizationEngine) createQuantizedHistogram(refHistogram []int64, binEdges []float32, threshold float32, qmin, qmax int32) ([]int64, error) {
	quantizedHist := make([]int64, len(refHistogram))

	scale := threshold / float32(qmax-qmin)

	for i, count := range refHistogram {
		if i >= len(binEdges)-1 {
			continue
		}

		// Get the center value of this bin
		binCenter := (binEdges[i] + binEdges[i+1]) / 2

		// Quantize and dequantize the bin center
		quantized := int32(binCenter/scale) + qmin
		if quantized < qmin {
			quantized = qmin
		}
		if quantized > qmax {
			quantized = qmax
		}

		dequantized := float32(quantized-qmin) * scale

		// Find which bin the dequantized value falls into
		newBinIndex := int((dequantized - binEdges[0]) / ((binEdges[len(binEdges)-1] - binEdges[0]) / float32(len(binEdges)-1)))
		if newBinIndex < 0 {
			newBinIndex = 0
		}
		if newBinIndex >= len(quantizedHist) {
			newBinIndex = len(quantizedHist) - 1
		}

		quantizedHist[newBinIndex] += count
	}

	return quantizedHist, nil
}

// computeKLDivergence computes KL divergence between two histograms
func (qe *QuantizationEngine) computeKLDivergence(p, q []int64) float64 {
	if len(p) != len(q) {
		return math.Inf(1)
	}

	// Convert to probabilities
	pSum := int64(0)
	qSum := int64(0)
	for i := range p {
		pSum += p[i]
		qSum += q[i]
	}

	if pSum == 0 || qSum == 0 {
		return math.Inf(1)
	}

	klDiv := float64(0)
	epsilon := 1e-10 // Small value to avoid log(0)

	for i := range p {
		pProb := float64(p[i])/float64(pSum) + epsilon
		qProb := float64(q[i])/float64(qSum) + epsilon

		if pProb > epsilon {
			klDiv += pProb * math.Log(pProb/qProb)
		}
	}

	return klDiv
}

// computeQuantizationMSE computes MSE for quantization with given parameters
func (qe *QuantizationEngine) computeQuantizationMSE(tensor *Tensor, scale float32, zeroPoint int32, qmin, qmax int32) (float64, int64, error) {
	if tensor == nil {
		return 0, 0, fmt.Errorf("tensor is nil")
	}

	totalMSE := float64(0)
	count := int64(0)

	for i := int64(0); i < tensor.NumElements(); i++ {
		original, err := qe.getElementAsFloat32(tensor, i)
		if err != nil {
			continue
		}

		// Quantize
		quantized := int32(original/scale) + zeroPoint
		if quantized < qmin {
			quantized = qmin
		}
		if quantized > qmax {
			quantized = qmax
		}

		// Dequantize
		dequantized := float32(quantized-zeroPoint) * scale

		// Compute squared error
		error := float64(original - dequantized)
		totalMSE += error * error
		count++
	}

	if count == 0 {
		return 0, 0, fmt.Errorf("no valid elements found")
	}

	return totalMSE / float64(count), count, nil
}

// GetCalibrationDataset returns the calibration dataset
func (qe *QuantizationEngine) GetCalibrationDataset() *CalibrationDataset {
	qe.mutex.RLock()
	defer qe.mutex.RUnlock()
	return qe.calibrationDataset
}

// UpdateCalibrationConfig updates the calibration configuration
func (qe *QuantizationEngine) UpdateCalibrationConfig(config CalibrationDatasetConfig) {
	qe.mutex.Lock()
	defer qe.mutex.Unlock()
	qe.config.CalibrationConfig = config
	qe.calibrationDataset.Config = config
}

// PerformCalibration performs calibration using the configured method
func (qe *QuantizationEngine) PerformCalibration(tensor *Tensor) (QuantizationParams, error) {
	if !qe.config.UseCalibration {
		// Use standard quantization if calibration is disabled
		return qe.ComputeQuantizationParams(tensor)
	}

	switch qe.config.CalibrationConfig.CalibrationMethod {
	case CalibrationKLDivergence:
		return qe.CalibrateWithKLDivergence(tensor, 2048)
	case CalibrationMSE:
		return qe.CalibrateWithMSE(tensor)
	case CalibrationEntropy:
		// For now, use KL divergence as entropy-based method
		return qe.CalibrateWithKLDivergence(tensor, 1024)
	default:
		return qe.CalibrateWithMSE(tensor) // Default to MSE
	}
}

// DefaultCalibrationConfig returns a default calibration configuration
func DefaultCalibrationConfig() CalibrationDatasetConfig {
	return CalibrationDatasetConfig{
		MaxSampleSize:          1000,
		MinSampleSize:          100,
		SelectionMethod:        SampleSelectionGreedyCoreset,
		CalibrationMethod:      CalibrationKLDivergence,
		EnablePreprocessing:    true,
		EnableDataAugmentation: false,
		IncludeOutliers:        true,
		OutlierThreshold:       2.0, // 2 standard deviations
		ClusterCount:           10,
		DiversityWeight:        0.3,
	}
}

// getCurrentTimestamp returns current timestamp in milliseconds (defined elsewhere)
