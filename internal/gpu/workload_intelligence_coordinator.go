package gpu

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// WorkloadIntelligenceCoordinator provides intelligent multi-GPU workload distribution
// focusing on application-level coordination without thermal control
type WorkloadIntelligenceCoordinator struct {
	multiDeviceMgr *MultiDeviceManager
	performanceMon *GPUPerformanceMonitor
	abstractionMgr *AbstractionManager
	logger         *log.Logger

	// Configuration
	config IntelligenceConfig

	// State management
	mu        sync.RWMutex
	isRunning bool
	ctx       context.Context
	cancel    context.CancelFunc

	// Intelligence data
	deviceScores    map[string]*DeviceIntelligenceScore
	taskHistory     []TaskExecutionRecord
	rebalanceEvents []RebalanceEvent

	// Metrics
	totalTasksRouted  int64
	successfulRoutes  int64
	rebalanceCount    int64
	lastRebalanceTime time.Time
}

// IntelligenceConfig contains configuration for workload intelligence
type IntelligenceConfig struct {
	// Scoring weights (must sum to 1.0) - no thermal control
	UtilizationWeight float64 `json:"utilization_weight"`
	MemoryWeight      float64 `json:"memory_weight"`
	PerformanceWeight float64 `json:"performance_weight"`
	EfficiencyWeight  float64 `json:"efficiency_weight"`

	// Thresholds
	MaxUtilizationThreshold float64 `json:"max_utilization_threshold"`
	MaxMemoryThreshold      float64 `json:"max_memory_threshold"`
	MinEfficiencyThreshold  float64 `json:"min_efficiency_threshold"`

	// Rebalancing
	RebalanceInterval    time.Duration `json:"rebalance_interval"`
	RebalanceThreshold   float64       `json:"rebalance_threshold"`
	MinTasksForRebalance int           `json:"min_tasks_for_rebalance"`

	// History and learning
	TaskHistorySize       int  `json:"task_history_size"`
	PerformanceWindowSize int  `json:"performance_window_size"`
	LearningEnabled       bool `json:"learning_enabled"`

	// Cloud compatibility
	GracefulDegradation bool     `json:"graceful_degradation"`
	RequiredMetrics     []string `json:"required_metrics"`
	FallbackStrategy    string   `json:"fallback_strategy"`
}

// DeviceIntelligenceScore represents the intelligence scoring for a device
type DeviceIntelligenceScore struct {
	DeviceID          string    `json:"device_id"`
	OverallScore      float64   `json:"overall_score"`
	UtilizationScore  float64   `json:"utilization_score"`
	MemoryScore       float64   `json:"memory_score"`
	PerformanceScore  float64   `json:"performance_score"`
	EfficiencyScore   float64   `json:"efficiency_score"`
	LastUpdated       time.Time `json:"last_updated"`
	IsAvailable       bool      `json:"is_available"`
	ReasonUnavailable string    `json:"reason_unavailable,omitempty"`
}

// TaskExecutionRecord tracks task execution for learning
type TaskExecutionRecord struct {
	TaskID      string        `json:"task_id"`
	DeviceID    string        `json:"device_id"`
	TaskType    string        `json:"task_type"`
	StartTime   time.Time     `json:"start_time"`
	EndTime     time.Time     `json:"end_time"`
	Duration    time.Duration `json:"duration"`
	Success     bool          `json:"success"`
	MemoryUsed  int64         `json:"memory_used"`
	Performance float64       `json:"performance"`
	DeviceScore float64       `json:"device_score_at_assignment"`
}

// RebalanceEvent tracks rebalancing operations
type RebalanceEvent struct {
	Timestamp    time.Time `json:"timestamp"`
	Trigger      string    `json:"trigger"`
	TasksMoved   int       `json:"tasks_moved"`
	FromDevices  []string  `json:"from_devices"`
	ToDevices    []string  `json:"to_devices"`
	Improvement  float64   `json:"improvement"`
	Success      bool      `json:"success"`
	ErrorMessage string    `json:"error_message,omitempty"`
}

// WorkloadRequest represents a request for GPU workload assignment
type WorkloadRequest struct {
	TaskID           string            `json:"task_id"`
	TaskType         string            `json:"task_type"`
	Priority         int               `json:"priority"`
	MemoryRequired   int64             `json:"memory_required"`
	ComputeIntensity float64           `json:"compute_intensity"`
	EstimatedTime    time.Duration     `json:"estimated_time"`
	Deadline         *time.Time        `json:"deadline,omitempty"`
	Constraints      TaskConstraints   `json:"constraints"`
	Metadata         map[string]string `json:"metadata"`
}

// TaskConstraints defines constraints for task placement (no thermal constraints)
type TaskConstraints struct {
	RequiredVendor     string   `json:"required_vendor,omitempty"`
	ExcludedDevices    []string `json:"excluded_devices,omitempty"`
	PreferredDevices   []string `json:"preferred_devices,omitempty"`
	MinMemoryAvailable int64    `json:"min_memory_available,omitempty"`
	RequiredFeatures   []string `json:"required_features,omitempty"`
}

// WorkloadAssignment represents the result of workload assignment
type WorkloadAssignment struct {
	Request            *WorkloadRequest           `json:"request"`
	AssignedDevice     *ManagedDevice             `json:"assigned_device"`
	DeviceScore        *DeviceIntelligenceScore   `json:"device_score"`
	AssignmentReason   string                     `json:"assignment_reason"`
	AlternativeDevices []*DeviceIntelligenceScore `json:"alternative_devices,omitempty"`
	AssignedAt         time.Time                  `json:"assigned_at"`
	EstimatedStart     time.Time                  `json:"estimated_start"`
	EstimatedEnd       time.Time                  `json:"estimated_end"`
}

// DefaultIntelligenceConfig returns default configuration (no thermal control)
func DefaultIntelligenceConfig() IntelligenceConfig {
	return IntelligenceConfig{
		// Scoring weights (sum to 1.0) - thermal weight redistributed
		UtilizationWeight: 0.35,
		MemoryWeight:      0.35,
		PerformanceWeight: 0.20,
		EfficiencyWeight:  0.10,

		// Thresholds
		MaxUtilizationThreshold: 0.85,
		MaxMemoryThreshold:      0.90,
		MinEfficiencyThreshold:  0.60,

		// Rebalancing
		RebalanceInterval:    time.Minute * 5,
		RebalanceThreshold:   0.20, // 20% improvement needed
		MinTasksForRebalance: 3,

		// History and learning
		TaskHistorySize:       1000,
		PerformanceWindowSize: 100,
		LearningEnabled:       true,

		// Cloud compatibility
		GracefulDegradation: true,
		RequiredMetrics:     []string{"utilization", "memory"},
		FallbackStrategy:    "round_robin",
	}
}

// NewWorkloadIntelligenceCoordinator creates a new workload intelligence coordinator
func NewWorkloadIntelligenceCoordinator(
	multiDeviceMgr *MultiDeviceManager,
	performanceMon *GPUPerformanceMonitor,
	abstractionMgr *AbstractionManager,
	config IntelligenceConfig,
	logger *log.Logger,
) (*WorkloadIntelligenceCoordinator, error) {
	if logger == nil {
		logger = log.Default()
	}

	// Validate configuration
	if err := validateIntelligenceConfig(config); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	coordinator := &WorkloadIntelligenceCoordinator{
		multiDeviceMgr:  multiDeviceMgr,
		performanceMon:  performanceMon,
		abstractionMgr:  abstractionMgr,
		logger:          logger,
		config:          config,
		deviceScores:    make(map[string]*DeviceIntelligenceScore),
		taskHistory:     make([]TaskExecutionRecord, 0, config.TaskHistorySize),
		rebalanceEvents: make([]RebalanceEvent, 0),
	}

	return coordinator, nil
}

// Start begins the coordination process
func (wic *WorkloadIntelligenceCoordinator) Start(ctx context.Context) error {
	wic.mu.Lock()
	defer wic.mu.Unlock()

	if wic.isRunning {
		return fmt.Errorf("coordinator is already running")
	}

	wic.ctx, wic.cancel = context.WithCancel(ctx)
	wic.isRunning = true

	// Start monitoring and rebalancing goroutines
	go wic.monitoringLoop()
	go wic.rebalancingLoop()

	wic.logger.Printf("Workload intelligence coordinator started")
	return nil
}

// Stop terminates the coordination process
func (wic *WorkloadIntelligenceCoordinator) Stop() error {
	wic.mu.Lock()
	defer wic.mu.Unlock()

	if !wic.isRunning {
		return fmt.Errorf("coordinator is not running")
	}

	wic.cancel()
	wic.isRunning = false

	wic.logger.Printf("Workload intelligence coordinator stopped")
	return nil
}

// AssignWorkload assigns a workload to the best available GPU
func (wic *WorkloadIntelligenceCoordinator) AssignWorkload(ctx context.Context, request *WorkloadRequest) (*WorkloadAssignment, error) {
	if !wic.isRunning {
		return nil, fmt.Errorf("coordinator is not running")
	}

	// Update device scores
	if err := wic.updateDeviceScores(); err != nil {
		wic.logger.Printf("Warning: Failed to update device scores: %v", err)
	}

	// Find best device
	assignment, err := wic.findBestDevice(request)
	if err != nil {
		return nil, fmt.Errorf("failed to find suitable device: %w", err)
	}

	// Record assignment
	wic.recordTaskAssignment(request, assignment)

	wic.mu.Lock()
	wic.totalTasksRouted++
	wic.successfulRoutes++
	wic.mu.Unlock()

	return assignment, nil
}

// updateDeviceScores updates intelligence scores for all devices
func (wic *WorkloadIntelligenceCoordinator) updateDeviceScores() error {
	devices := wic.multiDeviceMgr.GetActiveDevices()
	if len(devices) == 0 {
		return fmt.Errorf("no active devices available")
	}

	// Get current performance snapshot
	snapshot, err := wic.performanceMon.GetCurrentSnapshot()
	if err != nil {
		// Graceful degradation - use basic metrics if available
		if wic.config.GracefulDegradation {
			return wic.updateDeviceScoresBasic(devices)
		}
		return fmt.Errorf("failed to get performance snapshot: %w", err)
	}

	wic.mu.Lock()
	defer wic.mu.Unlock()

	for _, device := range devices {
		score := wic.calculateDeviceScore(device, snapshot)
		wic.deviceScores[device.Device.ID] = score
	}

	return nil
}

// updateDeviceScoresBasic provides fallback scoring with basic metrics
func (wic *WorkloadIntelligenceCoordinator) updateDeviceScoresBasic(devices []*ManagedDevice) error {
	wic.mu.Lock()
	defer wic.mu.Unlock()

	for _, device := range devices {
		score := &DeviceIntelligenceScore{
			DeviceID:         device.Device.ID,
			OverallScore:     wic.calculateBasicScore(device),
			UtilizationScore: 1.0 - device.LoadLevel, // Invert load level
			MemoryScore:      0.5,                    // Default when unavailable
			PerformanceScore: 0.5,                    // Default when unavailable
			EfficiencyScore:  0.5,                    // Default when unavailable
			LastUpdated:      time.Now(),
			IsAvailable:      device.IsActive,
		}

		if !device.IsActive {
			score.ReasonUnavailable = "device inactive"
		}

		wic.deviceScores[device.Device.ID] = score
	}

	return nil
}

// calculateDeviceScore calculates comprehensive device score
func (wic *WorkloadIntelligenceCoordinator) calculateDeviceScore(device *ManagedDevice, snapshot *SystemPerformanceSnapshot) *DeviceIntelligenceScore {
	if device == nil {
		return &DeviceIntelligenceScore{
			DeviceID:          "unknown",
			IsAvailable:       false,
			ReasonUnavailable: "device is nil",
			LastUpdated:       time.Now(),
		}
	}

	// Get device performance data
	var perf *DevicePerformance
	if snapshot != nil {
		perf = snapshot.DevicePerformance[device.Device.ID]
	}

	// Calculate individual scores
	utilScore := wic.calculateUtilizationScore(device, perf)
	memScore := wic.calculateMemoryScore(device, perf)
	perfScore := wic.calculatePerformanceScore(device, perf)

	// Use enhanced efficiency scoring if AbstractionManager is available
	var effScore float64
	if wic.abstractionMgr != nil {
		effScore = wic.calculateEnhancedEfficiencyScore(device, perf)
	} else {
		effScore = wic.calculateEfficiencyScore(device, perf)
	}

	// Calculate overall weighted score (no thermal control)
	overallScore := (utilScore * wic.config.UtilizationWeight) +
		(memScore * wic.config.MemoryWeight) +
		(perfScore * wic.config.PerformanceWeight) +
		(effScore * wic.config.EfficiencyWeight)

	return &DeviceIntelligenceScore{
		DeviceID:         device.Device.ID,
		OverallScore:     overallScore,
		UtilizationScore: utilScore,
		MemoryScore:      memScore,
		PerformanceScore: perfScore,
		EfficiencyScore:  effScore,
		LastUpdated:      time.Now(),
		IsAvailable:      device.IsActive && overallScore > 0.1,
	}
}

// calculateBasicScore provides a fallback scoring mechanism
func (wic *WorkloadIntelligenceCoordinator) calculateBasicScore(device *ManagedDevice) float64 {
	if device == nil || !device.IsActive {
		return 0.0
	}

	// Basic scoring based on device properties
	score := 0.5 // Base score for active device

	// Memory-based scoring
	if device.Device.MemoryTotal > 0 {
		memoryGB := float64(device.Device.MemoryTotal) / (1024 * 1024 * 1024)
		if memoryGB >= 8 {
			score += 0.3
		} else if memoryGB >= 4 {
			score += 0.2
		} else {
			score += 0.1
		}
	}

	// Vendor-based scoring (no thermal considerations)
	switch device.Device.Vendor {
	case "NVIDIA":
		score += 0.1 // CUDA ecosystem bonus
	case "Apple":
		score += 0.15 // Unified memory bonus
	case "AMD":
		score += 0.05 // ROCm support
	}

	if score > 1.0 {
		score = 1.0
	}
	if score < 0.0 {
		score = 0.0
	}

	return score
}

// calculateUtilizationScore calculates score based on GPU utilization
func (wic *WorkloadIntelligenceCoordinator) calculateUtilizationScore(device *ManagedDevice, perf *DevicePerformance) float64 {
	if perf != nil && perf.UtilizationMetrics.GPUUtilization >= 0 {
		utilization := perf.UtilizationMetrics.GPUUtilization
		if utilization >= 1.0 {
			return 0.0
		}
		return 1.0 - utilization
	}

	// Fallback to device load level
	return 1.0 - device.LoadLevel
}

// calculateMemoryScore calculates score based on available memory
func (wic *WorkloadIntelligenceCoordinator) calculateMemoryScore(device *ManagedDevice, perf *DevicePerformance) float64 {
	if perf != nil && perf.MemoryMetrics.TotalMemory > 0 {
		usageRatio := float64(perf.MemoryMetrics.UsedMemory) / float64(perf.MemoryMetrics.TotalMemory)
		if usageRatio >= 1.0 {
			return 0.0
		}
		return 1.0 - usageRatio
	}

	// Fallback to basic memory calculation
	if device.Device.MemoryTotal > 0 {
		memoryUsed := device.Device.MemoryTotal - device.Device.MemoryFree
		usageRatio := float64(memoryUsed) / float64(device.Device.MemoryTotal)
		if usageRatio >= 1.0 {
			return 0.0
		}
		return 1.0 - usageRatio
	}

	return 0.5 // Default when unavailable
}

// calculatePerformanceScore calculates score based on recent performance
func (wic *WorkloadIntelligenceCoordinator) calculatePerformanceScore(device *ManagedDevice, perf *DevicePerformance) float64 {
	if perf != nil {
		// Use task success rate and throughput as performance indicators
		successRate := perf.TaskMetrics.TaskSuccessRate / 100.0 // Convert percentage to 0-1
		tasksPerSecond := perf.TaskMetrics.TasksPerSecond

		// Normalize tasks per second (assuming 10 tasks/sec as good performance)
		normalizedThroughput := tasksPerSecond / 10.0
		if normalizedThroughput > 1.0 {
			normalizedThroughput = 1.0
		}

		// Combine success rate and throughput
		return (successRate * 0.7) + (normalizedThroughput * 0.3)
	}

	return 0.5 // Default when unavailable
}

// calculateEfficiencyScore calculates basic efficiency from available data
func (wic *WorkloadIntelligenceCoordinator) calculateEfficiencyScore(device *ManagedDevice, perf *DevicePerformance) float64 {
	if perf != nil && perf.EfficiencyScore > 0 {
		return perf.EfficiencyScore
	}

	// Basic efficiency based on device characteristics
	efficiency := 0.5

	// Vendor-specific efficiency adjustments (no thermal considerations)
	switch device.Device.Vendor {
	case "NVIDIA":
		efficiency += 0.1 // CUDA efficiency
	case "Apple":
		efficiency += 0.2 // Unified memory efficiency
	case "AMD":
		efficiency += 0.05 // ROCm efficiency
	}

	if efficiency > 1.0 {
		efficiency = 1.0
	}

	return efficiency
}

// calculateEnhancedEfficiencyScore calculates efficiency using GPU abstraction layer capabilities
func (wic *WorkloadIntelligenceCoordinator) calculateEnhancedEfficiencyScore(device *ManagedDevice, perf *DevicePerformance) float64 {
	// Start with basic efficiency calculation
	basicEfficiency := wic.calculateEfficiencyScore(device, perf)

	// If no AbstractionManager available, return basic score
	if wic.abstractionMgr == nil {
		return basicEfficiency
	}

	// Get device capabilities from abstraction layer
	capabilities, err := wic.abstractionMgr.GetCapabilities(device.Device)
	if err != nil {
		wic.logger.Printf("Failed to get capabilities for device %s: %v", device.Device.ID, err)
		return basicEfficiency
	}

	// Calculate capability-based efficiency bonuses (no thermal control)
	efficiencyMultiplier := 1.0

	// Feature-based bonuses
	if wic.abstractionMgr.SupportsFeature(device.Device, FeatureFloat16) {
		efficiencyMultiplier += 0.10 // 10% bonus for FP16 support
	}
	if wic.abstractionMgr.SupportsFeature(device.Device, FeatureInt8) {
		efficiencyMultiplier += 0.05 // 5% bonus for INT8 support
	}
	if wic.abstractionMgr.SupportsFeature(device.Device, FeatureUnifiedMemory) {
		efficiencyMultiplier += 0.15 // 15% bonus for unified memory
	}
	if wic.abstractionMgr.SupportsFeature(device.Device, FeatureTensorOps) {
		efficiencyMultiplier += 0.20 // 20% bonus for tensor operations
	}

	// Vendor-specific optimizations using abstraction layer
	switch device.Device.Vendor {
	case "NVIDIA":
		if wic.abstractionMgr.SupportsFeature(device.Device, FeatureAsyncCompute) {
			efficiencyMultiplier += 0.10 // CUDA async copy efficiency
		}
	case "Apple":
		if wic.abstractionMgr.SupportsFeature(device.Device, FeatureUnifiedMemory) {
			efficiencyMultiplier += 0.25 // Extra bonus for Apple unified memory
		}
	case "AMD":
		if wic.abstractionMgr.SupportsFeature(device.Device, FeatureMemoryCoherence) {
			efficiencyMultiplier += 0.10 // ROCm memory coherence efficiency
		}
	}

	// Cap the multiplier to prevent excessive bonuses
	if efficiencyMultiplier > 2.0 {
		efficiencyMultiplier = 2.0
	}

	enhancedEfficiency := basicEfficiency * efficiencyMultiplier
	if enhancedEfficiency > 1.0 {
		enhancedEfficiency = 1.0
	}

	// Use capabilities to validate the calculation (avoid unused variable)
	_ = capabilities

	return enhancedEfficiency
}

// validateDeviceCompatibility checks device compatibility using abstraction layer
func (wic *WorkloadIntelligenceCoordinator) validateDeviceCompatibility(device *ManagedDevice, constraints TaskConstraints) (bool, string) {
	// If no AbstractionManager available, use basic validation
	if wic.abstractionMgr == nil {
		return true, "abstraction layer not available"
	}

	// Check required features using abstraction layer
	for _, requiredFeature := range constraints.RequiredFeatures {
		var feature GPUFeature
		switch requiredFeature {
		case "fp16":
			feature = FeatureFloat16
		case "int8":
			feature = FeatureInt8
		case "unified_memory":
			feature = FeatureUnifiedMemory
		case "tensor_ops":
			feature = FeatureTensorOps
		case "async_copy":
			feature = FeatureAsyncCompute
		case "peer_to_peer":
			feature = FeatureMemoryCoherence
		default:
			wic.logger.Printf("Unknown required feature: %s", requiredFeature)
			continue
		}

		if !wic.abstractionMgr.SupportsFeature(device.Device, feature) {
			return false, fmt.Sprintf("device does not support required feature: %s", requiredFeature)
		}
	}

	return true, "all requirements met"
}

// findBestDevice finds the best device for a workload request
func (wic *WorkloadIntelligenceCoordinator) findBestDevice(request *WorkloadRequest) (*WorkloadAssignment, error) {
	wic.mu.RLock()
	defer wic.mu.RUnlock()

	// Filter devices by constraints
	candidates := wic.filterDevicesByConstraints(request.Constraints)
	if len(candidates) == 0 {
		return nil, fmt.Errorf("no devices meet the specified constraints")
	}

	// Find device with highest score
	var bestDevice *DeviceIntelligenceScore
	for _, candidate := range candidates {
		if candidate.IsAvailable && (bestDevice == nil || candidate.OverallScore > bestDevice.OverallScore) {
			bestDevice = candidate
		}
	}

	if bestDevice == nil {
		return nil, fmt.Errorf("no available devices found")
	}

	// Get the managed device
	devices := wic.multiDeviceMgr.GetActiveDevices()
	var managedDevice *ManagedDevice
	for _, device := range devices {
		if device.Device.ID == bestDevice.DeviceID {
			managedDevice = device
			break
		}
	}

	if managedDevice == nil {
		return nil, fmt.Errorf("managed device not found for ID: %s", bestDevice.DeviceID)
	}

	// Create assignment
	assignment := &WorkloadAssignment{
		Request:          request,
		AssignedDevice:   managedDevice,
		DeviceScore:      bestDevice,
		AssignmentReason: wic.generateAssignmentReason(bestDevice, candidates),
		AlternativeDevices: func() []*DeviceIntelligenceScore {
			if len(candidates) <= 3 {
				return candidates
			}
			return candidates[:3]
		}(), // Top 3 alternatives
		AssignedAt:     time.Now(),
		EstimatedStart: time.Now(),
		EstimatedEnd:   time.Now().Add(request.EstimatedTime),
	}

	return assignment, nil
}

// filterDevicesByConstraints filters devices based on task constraints
func (wic *WorkloadIntelligenceCoordinator) filterDevicesByConstraints(constraints TaskConstraints) []*DeviceIntelligenceScore {
	var filtered []*DeviceIntelligenceScore

	for _, score := range wic.deviceScores {
		if !score.IsAvailable {
			continue
		}

		// Check if device is excluded
		if wic.isDeviceExcluded(score.DeviceID, constraints.ExcludedDevices) {
			continue
		}

		// Get managed device for additional checks
		devices := wic.multiDeviceMgr.GetActiveDevices()
		var device *ManagedDevice
		for _, d := range devices {
			if d.Device.ID == score.DeviceID {
				device = d
				break
			}
		}

		if device == nil {
			continue
		}

		// Check vendor requirement
		if constraints.RequiredVendor != "" && device.Device.Vendor != constraints.RequiredVendor {
			continue
		}

		// Check memory requirement
		if constraints.MinMemoryAvailable > 0 {
			availableMemory := int64(device.Device.MemoryFree)
			if availableMemory < constraints.MinMemoryAvailable {
				continue
			}
		}

		// Check device compatibility using abstraction layer
		if compatible, reason := wic.validateDeviceCompatibility(device, constraints); !compatible {
			wic.logger.Printf("Device %s filtered out: %s", device.Device.ID, reason)
			continue
		}

		filtered = append(filtered, score)
	}

	return filtered
}

// isDeviceExcluded checks if a device is in the excluded list
func (wic *WorkloadIntelligenceCoordinator) isDeviceExcluded(deviceID string, excludedDevices []string) bool {
	for _, excluded := range excludedDevices {
		if deviceID == excluded {
			return true
		}
	}
	return false
}

// generateAssignmentReason generates a human-readable reason for device assignment
func (wic *WorkloadIntelligenceCoordinator) generateAssignmentReason(selected *DeviceIntelligenceScore, candidates []*DeviceIntelligenceScore) string {
	if len(candidates) == 1 {
		return "Only available device meeting constraints"
	}

	return fmt.Sprintf("Best overall score: %.2f (utilization: %.2f, memory: %.2f, performance: %.2f, efficiency: %.2f)",
		selected.OverallScore,
		selected.UtilizationScore,
		selected.MemoryScore,
		selected.PerformanceScore,
		selected.EfficiencyScore)
}

// recordTaskAssignment records a task assignment for learning
func (wic *WorkloadIntelligenceCoordinator) recordTaskAssignment(request *WorkloadRequest, assignment *WorkloadAssignment) {
	record := TaskExecutionRecord{
		TaskID:      request.TaskID,
		DeviceID:    assignment.AssignedDevice.Device.ID,
		TaskType:    request.TaskType,
		StartTime:   assignment.AssignedAt,
		DeviceScore: assignment.DeviceScore.OverallScore,
	}

	wic.mu.Lock()
	defer wic.mu.Unlock()

	// Add to history with size limit
	if len(wic.taskHistory) >= wic.config.TaskHistorySize {
		wic.taskHistory = wic.taskHistory[1:]
	}
	wic.taskHistory = append(wic.taskHistory, record)
}

// monitoringLoop periodically updates device scores
func (wic *WorkloadIntelligenceCoordinator) monitoringLoop() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-wic.ctx.Done():
			return
		case <-ticker.C:
			if err := wic.updateDeviceScores(); err != nil {
				wic.logger.Printf("Failed to update device scores: %v", err)
			}
		}
	}
}

// rebalancingLoop periodically checks for rebalancing opportunities
func (wic *WorkloadIntelligenceCoordinator) rebalancingLoop() {
	ticker := time.NewTicker(wic.config.RebalanceInterval)
	defer ticker.Stop()

	for {
		select {
		case <-wic.ctx.Done():
			return
		case <-ticker.C:
			if err := wic.checkAndRebalance(); err != nil {
				wic.logger.Printf("Rebalancing check failed: %v", err)
			}
		}
	}
}

// checkAndRebalance checks if rebalancing is needed and performs it
func (wic *WorkloadIntelligenceCoordinator) checkAndRebalance() error {
	// Basic rebalancing logic - can be enhanced based on needs
	wic.logger.Printf("Rebalancing check completed (placeholder implementation)")
	return nil
}

// GetDeviceScores returns current device scores
func (wic *WorkloadIntelligenceCoordinator) GetDeviceScores() map[string]*DeviceIntelligenceScore {
	wic.mu.RLock()
	defer wic.mu.RUnlock()

	scores := make(map[string]*DeviceIntelligenceScore)
	for k, v := range wic.deviceScores {
		scores[k] = v
	}
	return scores
}

// GetTaskHistory returns task execution history
func (wic *WorkloadIntelligenceCoordinator) GetTaskHistory() []TaskExecutionRecord {
	wic.mu.RLock()
	defer wic.mu.RUnlock()

	history := make([]TaskExecutionRecord, len(wic.taskHistory))
	copy(history, wic.taskHistory)
	return history
}

// GetRebalanceEvents returns rebalancing event history
func (wic *WorkloadIntelligenceCoordinator) GetRebalanceEvents() []RebalanceEvent {
	wic.mu.RLock()
	defer wic.mu.RUnlock()

	events := make([]RebalanceEvent, len(wic.rebalanceEvents))
	copy(events, wic.rebalanceEvents)
	return events
}

// GetStats returns coordination statistics
func (wic *WorkloadIntelligenceCoordinator) GetStats() map[string]interface{} {
	wic.mu.RLock()
	defer wic.mu.RUnlock()

	successRate := 0.0
	if wic.totalTasksRouted > 0 {
		successRate = float64(wic.successfulRoutes) / float64(wic.totalTasksRouted)
	}

	return map[string]interface{}{
		"is_running":          wic.isRunning,
		"total_tasks_routed":  wic.totalTasksRouted,
		"successful_routes":   wic.successfulRoutes,
		"success_rate":        successRate,
		"rebalance_count":     wic.rebalanceCount,
		"last_rebalance_time": wic.lastRebalanceTime,
		"active_devices":      len(wic.deviceScores),
		"task_history_size":   len(wic.taskHistory),
		"rebalance_events":    len(wic.rebalanceEvents),
		"scope_compliance":    "no_thermal_control",
	}
}

// validateIntelligenceConfig validates the configuration
func validateIntelligenceConfig(config IntelligenceConfig) error {
	// Check weight sum (should be close to 1.0)
	totalWeight := config.UtilizationWeight + config.MemoryWeight +
		config.PerformanceWeight + config.EfficiencyWeight

	if totalWeight < 0.99 || totalWeight > 1.01 {
		return fmt.Errorf("scoring weights must sum to 1.0, got %.2f", totalWeight)
	}

	// Check individual weights are non-negative
	if config.UtilizationWeight < 0 || config.MemoryWeight < 0 ||
		config.PerformanceWeight < 0 || config.EfficiencyWeight < 0 {
		return fmt.Errorf("all scoring weights must be non-negative")
	}

	// Check thresholds are valid
	if config.MaxUtilizationThreshold <= 0 || config.MaxUtilizationThreshold > 1 {
		return fmt.Errorf("max utilization threshold must be between 0 and 1")
	}

	if config.MaxMemoryThreshold <= 0 || config.MaxMemoryThreshold > 1 {
		return fmt.Errorf("max memory threshold must be between 0 and 1")
	}

	return nil
}
