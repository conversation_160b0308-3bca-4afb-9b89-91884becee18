package gpu

import (
	"log"
	"os"
	"testing"
	"time"
)

func TestModelSelectionOptimizer(t *testing.T) {
	// Create test data
	testData := generateTestWorkloadData(100)

	// Create predictor and feature engineer
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    10,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	featureEngineer := NewFeatureEngineer()

	// Create logger
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	// Create optimizer with test configuration
	optimizerConfig := ModelSelectionConfig{
		CVFolds:              3, // Reduced for faster testing
		TimeSeriesCV:         true,
		ValidationWindowSize: time.Hour,

		UseAIC:   true,
		UseBIC:   true,
		UseFIC:   false,
		ICWeight: 0.3,

		EnableBayesianOpt:   false, // Disabled for faster testing
		BayesianIterations:  10,
		AcquisitionFunction: "ei",
		ExplorationWeight:   0.01,

		EnableEnsemble:    false, // Disabled for faster testing
		MaxEnsembleModels: 3,
		EnsembleMethod:    "averaging",

		PrimaryMetric:        "accuracy",
		SecondaryMetrics:     []string{"rmse"},
		PerformanceThreshold: 0.5,

		EnableFeatureSelection: false, // Disabled for faster testing
		FeatureSelectionMethod: "rfe",
		MaxFeatures:            20,

		MaxIterations:       10, // Reduced for faster testing
		Timeout:             time.Minute * 2,
		EarlyStoppingRounds: 5,

		MaxParallelJobs: 2,
		EnableParallel:  false, // Disabled for simpler testing
	}

	optimizer := NewModelSelectionOptimizer(optimizerConfig, predictor, featureEngineer, logger)

	// Test optimization
	result, err := optimizer.OptimizeModels(testData, "avg_utilization")
	if err != nil {
		t.Fatalf("Optimization failed: %v", err)
	}

	// Validate results
	if result == nil {
		t.Fatal("Expected optimization result, got nil")
	}

	if result.BestModel == nil {
		t.Fatal("Expected best model, got nil")
	}

	if len(result.AllCandidates) == 0 {
		t.Fatal("Expected model candidates, got none")
	}

	if result.TotalTime <= 0 {
		t.Error("Expected positive total time")
	}

	if result.Iterations <= 0 {
		t.Error("Expected positive iterations")
	}

	t.Logf("Optimization completed successfully:")
	t.Logf("- Best model: %s (score: %.4f)", result.BestModel.ModelType, result.BestModel.MeanScore)
	t.Logf("- Total candidates: %d", len(result.AllCandidates))
	t.Logf("- Total time: %v", result.TotalTime)
	t.Logf("- Iterations: %d", result.Iterations)
}

func TestModelSelectionConfig(t *testing.T) {
	// Test default configuration
	config := DefaultModelSelectionConfig()

	// Validate default values
	if config.CVFolds != 5 {
		t.Errorf("Expected CVFolds=5, got %d", config.CVFolds)
	}

	if !config.TimeSeriesCV {
		t.Error("Expected TimeSeriesCV=true")
	}

	if !config.UseAIC {
		t.Error("Expected UseAIC=true")
	}

	if !config.UseBIC {
		t.Error("Expected UseBIC=true")
	}

	if config.PrimaryMetric != "accuracy" {
		t.Errorf("Expected PrimaryMetric=accuracy, got %s", config.PrimaryMetric)
	}

	if config.MaxIterations != 100 {
		t.Errorf("Expected MaxIterations=100, got %d", config.MaxIterations)
	}
}

func TestModelCandidate(t *testing.T) {
	// Create test candidate
	candidate := &ModelCandidate{
		ID:        "test_model_1",
		ModelType: "linear_regression",
		Hyperparameters: map[string]interface{}{
			"learning_rate": 0.01,
			"epochs":        100,
		},
		CVScores:     []float64{0.8, 0.85, 0.82, 0.87, 0.83},
		MeanScore:    0.834,
		StdScore:     0.025,
		AIC:          120.5,
		BIC:          125.3,
		FIC:          122.1,
		TrainingTime: time.Millisecond * 500,
		Features:     []string{"feature1", "feature2", "feature3"},
		IsEnsemble:   false,
	}

	// Validate candidate properties
	if candidate.ID != "test_model_1" {
		t.Errorf("Expected ID=test_model_1, got %s", candidate.ID)
	}

	if candidate.ModelType != "linear_regression" {
		t.Errorf("Expected ModelType=linear_regression, got %s", candidate.ModelType)
	}

	if len(candidate.CVScores) != 5 {
		t.Errorf("Expected 5 CV scores, got %d", len(candidate.CVScores))
	}

	if candidate.MeanScore != 0.834 {
		t.Errorf("Expected MeanScore=0.834, got %f", candidate.MeanScore)
	}

	if len(candidate.Features) != 3 {
		t.Errorf("Expected 3 features, got %d", len(candidate.Features))
	}
}

func TestHyperparameterSpace(t *testing.T) {
	// Create test hyperparameter space
	space := HyperparameterSpace{
		ModelType: "lstm",
		Parameters: map[string]ParameterRange{
			"hidden_size": {
				Type: "int",
				Min:  16,
				Max:  128,
			},
			"learning_rate": {
				Type:     "float",
				Min:      0.001,
				Max:      0.1,
				LogScale: true,
			},
			"activation": {
				Type:   "categorical",
				Values: []interface{}{"relu", "tanh", "sigmoid"},
			},
		},
	}

	// Validate space properties
	if space.ModelType != "lstm" {
		t.Errorf("Expected ModelType=lstm, got %s", space.ModelType)
	}

	if len(space.Parameters) != 3 {
		t.Errorf("Expected 3 parameters, got %d", len(space.Parameters))
	}

	// Validate hidden_size parameter
	hiddenSize, exists := space.Parameters["hidden_size"]
	if !exists {
		t.Error("Expected hidden_size parameter")
	}
	if hiddenSize.Type != "int" {
		t.Errorf("Expected hidden_size type=int, got %s", hiddenSize.Type)
	}

	// Validate learning_rate parameter
	learningRate, exists := space.Parameters["learning_rate"]
	if !exists {
		t.Error("Expected learning_rate parameter")
	}
	if learningRate.Type != "float" {
		t.Errorf("Expected learning_rate type=float, got %s", learningRate.Type)
	}
	if !learningRate.LogScale {
		t.Error("Expected learning_rate LogScale=true")
	}

	// Validate activation parameter
	activation, exists := space.Parameters["activation"]
	if !exists {
		t.Error("Expected activation parameter")
	}
	if activation.Type != "categorical" {
		t.Errorf("Expected activation type=categorical, got %s", activation.Type)
	}
	if len(activation.Values) != 3 {
		t.Errorf("Expected 3 activation values, got %d", len(activation.Values))
	}
}

func TestOptimizationResult(t *testing.T) {
	// Create test candidates
	candidates := []*ModelCandidate{
		{
			ID:        "model_1",
			ModelType: "linear_regression",
			MeanScore: 0.85,
		},
		{
			ID:        "model_2",
			ModelType: "lstm",
			MeanScore: 0.88,
		},
		{
			ID:         "model_3",
			ModelType:  "ensemble",
			MeanScore:  0.91,
			IsEnsemble: true,
		},
	}

	// Create optimization result
	result := &OptimizationResult{
		BestModel:     candidates[2], // Highest score
		TopModels:     candidates,
		AllCandidates: candidates,
		OptimizationLog: []OptimizationStep{
			{
				Iteration: 1,
				ModelType: "linear_regression",
				Score:     0.85,
			},
			{
				Iteration: 2,
				ModelType: "lstm",
				Score:     0.88,
			},
		},
		TotalTime:        time.Second * 30,
		Iterations:       2,
		ConvergedEarly:   false,
		SelectedFeatures: []string{"feature1", "feature2"},
	}

	// Validate result properties
	if result.BestModel.MeanScore != 0.91 {
		t.Errorf("Expected best model score=0.91, got %f", result.BestModel.MeanScore)
	}

	if len(result.TopModels) != 3 {
		t.Errorf("Expected 3 top models, got %d", len(result.TopModels))
	}

	if len(result.AllCandidates) != 3 {
		t.Errorf("Expected 3 candidates, got %d", len(result.AllCandidates))
	}

	if len(result.OptimizationLog) != 2 {
		t.Errorf("Expected 2 optimization steps, got %d", len(result.OptimizationLog))
	}

	if result.Iterations != 2 {
		t.Errorf("Expected 2 iterations, got %d", result.Iterations)
	}

	if len(result.SelectedFeatures) != 2 {
		t.Errorf("Expected 2 selected features, got %d", len(result.SelectedFeatures))
	}
}

func TestCrossValidation(t *testing.T) {
	// Create test data
	testData := generateTestWorkloadData(50)

	// Create predictor and feature engineer
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    5,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	featureEngineer := NewFeatureEngineer()

	// Create logger
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	// Create optimizer
	optimizerConfig := DefaultModelSelectionConfig()
	optimizerConfig.CVFolds = 3
	optimizerConfig.EnableBayesianOpt = false
	optimizerConfig.EnableEnsemble = false
	optimizerConfig.EnableFeatureSelection = false

	optimizer := NewModelSelectionOptimizer(optimizerConfig, predictor, featureEngineer, logger)

	// Test time series cross-validation
	hyperparams := map[string]interface{}{
		"learning_rate": 0.01,
		"epochs":        50,
	}

	scores, err := optimizer.performCrossValidation(
		"linear_regression",
		hyperparams,
		testData,
		"avg_utilization",
		[]string{"feature1", "feature2"},
	)

	if err != nil {
		t.Fatalf("Cross-validation failed: %v", err)
	}

	if len(scores) == 0 {
		t.Fatal("Expected CV scores, got none")
	}

	// Validate scores are reasonable
	for i, score := range scores {
		if score < 0 || score > 1 {
			t.Errorf("CV score %d out of range [0,1]: %f", i, score)
		}
	}

	t.Logf("Cross-validation completed with %d scores", len(scores))
	for i, score := range scores {
		t.Logf("  Fold %d: %.4f", i+1, score)
	}
}

func TestScoreCalculation(t *testing.T) {
	// Create test optimizer
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    5,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	featureEngineer := NewFeatureEngineer()
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	optimizerConfig := DefaultModelSelectionConfig()
	optimizer := NewModelSelectionOptimizer(optimizerConfig, predictor, featureEngineer, logger)

	// Test data
	predictions := []float64{0.8, 0.85, 0.9, 0.75, 0.82}
	actuals := []float64{0.82, 0.88, 0.87, 0.78, 0.85}

	// Test accuracy calculation
	accuracy := optimizer.calculateAccuracy(predictions, actuals)
	if accuracy < 0 || accuracy > 1 {
		t.Errorf("Accuracy out of range [0,1]: %f", accuracy)
	}

	// Test RMSE calculation
	rmse := optimizer.calculateRMSE(predictions, actuals)
	if rmse < 0 {
		t.Errorf("RMSE should be non-negative: %f", rmse)
	}

	// Test MAE calculation
	mae := optimizer.calculateMAE(predictions, actuals)
	if mae < 0 {
		t.Errorf("MAE should be non-negative: %f", mae)
	}

	// Test score calculation with different metrics
	accuracyScore := optimizer.calculateScore(predictions, actuals, "accuracy")
	rmseScore := optimizer.calculateScore(predictions, actuals, "rmse")
	maeScore := optimizer.calculateScore(predictions, actuals, "mae")

	if accuracyScore < 0 || accuracyScore > 1 {
		t.Errorf("Accuracy score out of range [0,1]: %f", accuracyScore)
	}

	// RMSE and MAE scores are negative (for maximization)
	if rmseScore > 0 {
		t.Errorf("RMSE score should be negative: %f", rmseScore)
	}

	if maeScore > 0 {
		t.Errorf("MAE score should be negative: %f", maeScore)
	}

	t.Logf("Score calculations:")
	t.Logf("  Accuracy: %.4f", accuracyScore)
	t.Logf("  RMSE: %.4f", -rmseScore) // Show positive value
	t.Logf("  MAE: %.4f", -maeScore)   // Show positive value
}

func TestDefaultHyperparameterSpaces(t *testing.T) {
	spaces := createDefaultHyperparameterSpaces()

	if len(spaces) == 0 {
		t.Fatal("Expected hyperparameter spaces, got none")
	}

	// Test LSTM space
	lstmSpace, exists := spaces["lstm"]
	if !exists {
		t.Error("Expected LSTM hyperparameter space")
	} else {
		if lstmSpace.ModelType != "lstm" {
			t.Errorf("Expected LSTM model type, got %s", lstmSpace.ModelType)
		}

		if len(lstmSpace.Parameters) == 0 {
			t.Error("Expected LSTM parameters")
		}

		// Check for expected parameters
		expectedParams := []string{"hidden_size", "learning_rate", "epochs"}
		for _, param := range expectedParams {
			if _, exists := lstmSpace.Parameters[param]; !exists {
				t.Errorf("Expected LSTM parameter %s", param)
			}
		}
	}

	// Test linear regression space
	lrSpace, exists := spaces["linear_regression"]
	if !exists {
		t.Error("Expected linear regression hyperparameter space")
	} else {
		if lrSpace.ModelType != "linear_regression" {
			t.Errorf("Expected linear_regression model type, got %s", lrSpace.ModelType)
		}
	}

	t.Logf("Found %d hyperparameter spaces", len(spaces))
	for modelType := range spaces {
		t.Logf("  - %s", modelType)
	}
}

func TestStatisticalFunctions(t *testing.T) {
	// Test data
	values := []float64{1.0, 2.0, 3.0, 4.0, 5.0}

	// Test mean calculation
	mean := calculateMean(values)
	expectedMean := 3.0
	if mean != expectedMean {
		t.Errorf("Expected mean=%.1f, got %.1f", expectedMean, mean)
	}

	// Test standard deviation calculation
	std := calculateStd(values)
	expectedStd := 1.5811388300841898 // sqrt(2.5)
	if absOptimizer(std-expectedStd) > 1e-10 {
		t.Errorf("Expected std=%.10f, got %.10f", expectedStd, std)
	}

	// Test empty slice
	emptyMean := calculateMean([]float64{})
	if emptyMean != 0.0 {
		t.Errorf("Expected mean of empty slice to be 0, got %f", emptyMean)
	}

	emptyStd := calculateStd([]float64{})
	if emptyStd != 0.0 {
		t.Errorf("Expected std of empty slice to be 0, got %f", emptyStd)
	}

	// Test single value
	singleStd := calculateStd([]float64{5.0})
	if singleStd != 0.0 {
		t.Errorf("Expected std of single value to be 0, got %f", singleStd)
	}
}

// Helper function to generate test workload data
func generateTestWorkloadData(count int) []WorkloadDataPoint {
	var data []WorkloadDataPoint
	baseTime := time.Now().Add(-time.Hour * 24)

	for i := 0; i < count; i++ {
		// Create realistic workload patterns
		timeOffset := time.Duration(i) * time.Minute * 15
		timestamp := baseTime.Add(timeOffset)

		// Simulate daily patterns
		hour := timestamp.Hour()
		var utilization float64

		// Higher utilization during work hours
		if hour >= 9 && hour <= 17 {
			utilization = 0.6 + float64(i%10)*0.03 // 0.6-0.9 range
		} else {
			utilization = 0.2 + float64(i%5)*0.04 // 0.2-0.4 range
		}

		// Add some noise
		utilization += (float64(i%7) - 3) * 0.01

		// Ensure bounds
		if utilization < 0.1 {
			utilization = 0.1
		}
		if utilization > 1.0 {
			utilization = 1.0
		}

		point := WorkloadDataPoint{
			Timestamp:      timestamp,
			QueueLength:    int(utilization * 20), // Scale queue length
			ActiveTasks:    int(utilization * 10), // Scale active tasks
			AvgUtilization: utilization,
			NodesActive:    int(utilization*5) + 1, // At least 1 node
		}

		data = append(data, point)
	}

	return data
}

// Helper function for absolute value
func absOptimizer(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}

// Benchmark tests
func BenchmarkModelOptimization(b *testing.B) {
	// Create test data
	testData := generateTestWorkloadData(50)

	// Create predictor and feature engineer
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    10,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	featureEngineer := NewFeatureEngineer()

	// Create logger
	logger := log.New(os.Stdout, "BENCH: ", log.LstdFlags)

	// Create optimizer with minimal configuration for benchmarking
	optimizerConfig := ModelSelectionConfig{
		CVFolds:                3,
		TimeSeriesCV:           true,
		EnableBayesianOpt:      false,
		EnableEnsemble:         false,
		EnableFeatureSelection: false,
		MaxIterations:          5,
		PrimaryMetric:          "accuracy",
	}

	optimizer := NewModelSelectionOptimizer(optimizerConfig, predictor, featureEngineer, logger)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := optimizer.OptimizeModels(testData, "avg_utilization")
		if err != nil {
			b.Fatalf("Optimization failed: %v", err)
		}
	}
}

func BenchmarkCrossValidation(b *testing.B) {
	// Create test data
	testData := generateTestWorkloadData(30)

	// Create predictor and feature engineer
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    5,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	featureEngineer := NewFeatureEngineer()

	// Create logger
	logger := log.New(os.Stdout, "BENCH: ", log.LstdFlags)

	// Create optimizer
	optimizerConfig := DefaultModelSelectionConfig()
	optimizerConfig.CVFolds = 3

	optimizer := NewModelSelectionOptimizer(optimizerConfig, predictor, featureEngineer, logger)

	hyperparams := map[string]interface{}{
		"learning_rate": 0.01,
		"epochs":        50,
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := optimizer.performCrossValidation(
			"linear_regression",
			hyperparams,
			testData,
			"avg_utilization",
			[]string{"feature1", "feature2"},
		)
		if err != nil {
			b.Fatalf("Cross-validation failed: %v", err)
		}
	}
}

func BenchmarkScoreCalculation(b *testing.B) {
	// Create test optimizer
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    5,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	featureEngineer := NewFeatureEngineer()
	logger := log.New(os.Stdout, "BENCH: ", log.LstdFlags)

	optimizerConfig := DefaultModelSelectionConfig()
	optimizer := NewModelSelectionOptimizer(optimizerConfig, predictor, featureEngineer, logger)

	// Test data
	predictions := []float64{0.8, 0.85, 0.9, 0.75, 0.82, 0.88, 0.91, 0.77, 0.84, 0.89}
	actuals := []float64{0.82, 0.88, 0.87, 0.78, 0.85, 0.86, 0.93, 0.79, 0.81, 0.87}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_ = optimizer.calculateScore(predictions, actuals, "accuracy")
	}
}
