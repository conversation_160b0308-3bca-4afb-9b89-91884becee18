package gpu

import (
	"log"
	"testing"
	"time"
)

func TestPoolConfig(t *testing.T) {
	config := DefaultPoolConfig(0)

	if config.DeviceID != 0 {
		t.<PERSON><PERSON><PERSON>("Expected DeviceID 0, got %d", config.DeviceID)
	}

	if config.Strategy != StrategyAdaptive {
		t.<PERSON>("Expected StrategyAdaptive, got %v", config.Strategy)
	}

	if config.InitialSize != 256*1024*1024 {
		t.<PERSON>("Expected InitialSize 256MB, got %d", config.InitialSize)
	}

	if config.MaxSize != 2*1024*1024*1024 {
		t<PERSON><PERSON><PERSON><PERSON>("Expected MaxSize 2GB, got %d", config.MaxSize)
	}
}

func TestAllocationStrategy(t *testing.T) {
	strategies := []struct {
		strategy AllocationStrategy
		expected string
	}{
		{StrategyFixed, "fixed"},
		{StrategyDynamic, "dynamic"},
		{StrategyAdaptive, "adaptive"},
		{AllocationStrategy(999), "unknown"},
	}

	for _, test := range strategies {
		result := test.strategy.String()
		if result != test.expected {
			t.<PERSON>("Strategy %v: expected %s, got %s", test.strategy, test.expected, result)
		}
	}
}

func TestMemoryPressureLevel(t *testing.T) {
	levels := []struct {
		level    MemoryPressureLevel
		expected string
	}{
		{PressureLow, "low"},
		{PressureMedium, "medium"},
		{PressureHigh, "high"},
		{PressureCritical, "critical"},
		{MemoryPressureLevel(999), "unknown"},
	}

	for _, test := range levels {
		result := test.level.String()
		if result != test.expected {
			t.Errorf("Level %v: expected %s, got %s", test.level, test.expected, result)
		}
	}
}

func TestAdvancedMemoryPoolCreation(t *testing.T) {
	config := DefaultPoolConfig(0)
	logger := log.Default()

	pool, err := NewAdvancedMemoryPool(config, logger)
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	if pool == nil {
		t.Fatal("Pool is nil")
	}

	if pool.config.DeviceID != config.DeviceID {
		t.Errorf("Expected DeviceID %d, got %d", config.DeviceID, pool.config.DeviceID)
	}

	if pool.isInitialized {
		t.Error("Pool should not be initialized yet")
	}
}

func TestAdvancedMemoryPoolInitialization(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.Strategy = StrategyFixed
	config.InitialSize = 64 * 1024 * 1024 // 64MB
	config.BlockSize = 16 * 1024 * 1024   // 16MB

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	err = pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}

	if !pool.IsInitialized() {
		t.Error("Pool should be initialized")
	}

	stats := pool.GetStatistics()
	expectedBlocks := config.InitialSize / config.BlockSize

	if stats.BlockCount != int(expectedBlocks) {
		t.Errorf("Expected %d blocks, got %d", expectedBlocks, stats.BlockCount)
	}

	if stats.Strategy != StrategyFixed {
		t.Errorf("Expected StrategyFixed, got %v", stats.Strategy)
	}

	if stats.TotalSize != config.InitialSize {
		t.Errorf("Expected total size %d, got %d", config.InitialSize, stats.TotalSize)
	}

	// Test double initialization
	err = pool.Initialize()
	if err == nil {
		t.Error("Expected error on double initialization")
	}

	// Cleanup
	pool.Shutdown()
}

func TestFixedStrategyAllocation(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.Strategy = StrategyFixed
	config.InitialSize = 64 * 1024 * 1024 // 64MB
	config.BlockSize = 16 * 1024 * 1024   // 16MB
	config.GCInterval = 0                 // Disable background GC for testing
	config.EnableDefragmentation = false  // Disable defrag for testing

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	err = pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Allocate memory
	ptr1, err := pool.Allocate(8 * 1024 * 1024) // 8MB request
	if err != nil {
		t.Fatalf("Failed to allocate memory: %v", err)
	}

	if ptr1 == 0 {
		t.Error("Expected non-zero pointer")
	}

	stats := pool.GetStatistics()
	if stats.UsedBlockCount != 1 {
		t.Errorf("Expected 1 used block, got %d", stats.UsedBlockCount)
	}

	if stats.UsedSize != config.BlockSize {
		t.Errorf("Expected used size %d, got %d", config.BlockSize, stats.UsedSize)
	}

	// Allocate more memory
	ptr2, err := pool.Allocate(16 * 1024 * 1024) // 16MB request
	if err != nil {
		t.Fatalf("Failed to allocate second block: %v", err)
	}

	if ptr2 == 0 || ptr2 == ptr1 {
		t.Error("Expected different non-zero pointer")
	}

	// Free memory
	err = pool.Free(ptr1)
	if err != nil {
		t.Fatalf("Failed to free memory: %v", err)
	}

	stats = pool.GetStatistics()
	if stats.UsedBlockCount != 1 {
		t.Errorf("Expected 1 used block after free, got %d", stats.UsedBlockCount)
	}

	if stats.FreeBlockCount != 3 { // Should have 3 free blocks (original 4 - 1 used)
		t.Errorf("Expected 3 free blocks, got %d", stats.FreeBlockCount)
	}

	// Test double free
	err = pool.Free(ptr1)
	if err == nil {
		t.Error("Expected error on double free")
	}

	// Test free of invalid pointer
	err = pool.Free(CUDAMemoryPtr(999999))
	if err == nil {
		t.Error("Expected error on free of invalid pointer")
	}
}

func TestDynamicStrategyAllocation(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.Strategy = StrategyDynamic
	config.InitialSize = 0 // No pre-allocation for dynamic
	config.GCInterval = 0
	config.EnableDefragmentation = false

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	err = pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Should start with no blocks
	stats := pool.GetStatistics()
	if stats.BlockCount != 0 {
		t.Errorf("Expected 0 blocks initially, got %d", stats.BlockCount)
	}

	// Allocate exactly what we request
	requestSize := 10 * 1024 * 1024 // 10MB
	ptr, err := pool.Allocate(int64(requestSize))
	if err != nil {
		t.Fatalf("Failed to allocate memory: %v", err)
	}

	stats = pool.GetStatistics()
	if stats.UsedSize != int64(requestSize) {
		t.Errorf("Expected used size %d, got %d", requestSize, stats.UsedSize)
	}

	// Free and check
	err = pool.Free(ptr)
	if err != nil {
		t.Fatalf("Failed to free memory: %v", err)
	}

	stats = pool.GetStatistics()
	if stats.UsedSize != 0 {
		t.Errorf("Expected used size 0 after free, got %d", stats.UsedSize)
	}
}

func TestAdaptiveStrategyAllocation(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.Strategy = StrategyAdaptive
	config.GCInterval = 0
	config.EnableDefragmentation = false

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	err = pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Should have some pre-allocated blocks
	stats := pool.GetStatistics()
	if stats.BlockCount == 0 {
		t.Error("Expected some pre-allocated blocks for adaptive strategy")
	}

	initialFreeBlocks := stats.FreeBlockCount

	// Allocate memory
	ptr, err := pool.Allocate(2 * 1024 * 1024) // 2MB
	if err != nil {
		t.Fatalf("Failed to allocate memory: %v", err)
	}

	stats = pool.GetStatistics()
	if stats.UsedBlockCount == 0 {
		t.Error("Expected at least one used block")
	}

	if stats.FreeBlockCount != initialFreeBlocks-1 {
		t.Errorf("Expected %d free blocks, got %d", initialFreeBlocks-1, stats.FreeBlockCount)
	}

	// Free memory
	err = pool.Free(ptr)
	if err != nil {
		t.Fatalf("Failed to free memory: %v", err)
	}
}

func TestMemoryPressureDetection(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.Strategy = StrategyFixed
	config.InitialSize = 64 * 1024 * 1024 // 64MB
	config.BlockSize = 16 * 1024 * 1024   // 16MB (4 blocks total)
	config.MaxSize = 64 * 1024 * 1024     // Same as initial - no growth
	config.PressureThresholds = PressureThresholds{
		MediumPercent:   50.0, // 2 blocks
		HighPercent:     75.0, // 3 blocks
		CriticalPercent: 90.0, // Almost all blocks
	}
	config.GCInterval = 0
	config.EnableDefragmentation = false

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	err = pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Track pressure level changes
	var pressureChanges []MemoryPressureLevel
	pool.AddPressureHandler(func(oldLevel, newLevel MemoryPressureLevel, stats MemoryPoolStatistics) {
		pressureChanges = append(pressureChanges, newLevel)
	})

	// Should start at low pressure
	stats := pool.GetStatistics()
	if stats.PressureLevel != PressureLow {
		t.Errorf("Expected PressureLow initially, got %v", stats.PressureLevel)
	}

	// Allocate to trigger medium pressure (2 blocks = 50%)
	ptr1, _ := pool.Allocate(16 * 1024 * 1024)
	ptr2, _ := pool.Allocate(16 * 1024 * 1024)

	// Give time for pressure handler to run
	time.Sleep(10 * time.Millisecond)

	stats = pool.GetStatistics()
	if stats.PressureLevel != PressureMedium {
		t.Errorf("Expected PressureMedium, got %v", stats.PressureLevel)
	}

	// Allocate to trigger high pressure (3 blocks = 75%)
	ptr3, _ := pool.Allocate(16 * 1024 * 1024)

	time.Sleep(10 * time.Millisecond)

	stats = pool.GetStatistics()
	if stats.PressureLevel != PressureHigh {
		t.Errorf("Expected PressureHigh, got %v", stats.PressureLevel)
	}

	// Free some memory to reduce pressure
	pool.Free(ptr1)
	pool.Free(ptr2)
	pool.Free(ptr3)

	time.Sleep(10 * time.Millisecond)

	stats = pool.GetStatistics()
	if stats.PressureLevel != PressureLow {
		t.Errorf("Expected PressureLow after freeing, got %v", stats.PressureLevel)
	}

	// Check that we received pressure change notifications
	if len(pressureChanges) < 2 {
		t.Errorf("Expected at least 2 pressure changes, got %d", len(pressureChanges))
	}
}

func TestGarbageCollection(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.Strategy = StrategyDynamic
	config.GCInterval = 50 * time.Millisecond // Fast GC for testing
	config.EnableDefragmentation = false

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	err = pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Track GC events
	var gcEvents []int64
	pool.AddGCHandler(func(freedMemory int64, duration time.Duration, stats MemoryPoolStatistics) {
		gcEvents = append(gcEvents, freedMemory)
	})

	// Allocate and immediately free to create garbage
	ptr1, _ := pool.Allocate(1 * 1024 * 1024)
	ptr2, _ := pool.Allocate(2 * 1024 * 1024)

	pool.Free(ptr1)
	pool.Free(ptr2)

	initialStats := pool.GetStatistics()
	initialGCCount := initialStats.GCCount

	// Wait for GC to run
	time.Sleep(100 * time.Millisecond)

	stats := pool.GetStatistics()
	if stats.GCCount <= initialGCCount {
		t.Error("Expected GC to run at least once")
	}

	// Manual GC test
	pool.performGarbageCollection()

	stats = pool.GetStatistics()
	if stats.LastGC.IsZero() {
		t.Error("Expected LastGC to be set")
	}
}

func TestMemoryPoolReset(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.Strategy = StrategyFixed
	config.GCInterval = 0
	config.EnableDefragmentation = false

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	err = pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Allocate some memory
	ptr1, _ := pool.Allocate(1 * 1024 * 1024)
	ptr2, _ := pool.Allocate(2 * 1024 * 1024)

	statsBefore := pool.GetStatistics()
	if statsBefore.UsedBlockCount == 0 {
		t.Error("Expected some used blocks before reset")
	}

	// Reset the pool
	err = pool.Reset()
	if err != nil {
		t.Fatalf("Failed to reset pool: %v", err)
	}

	statsAfter := pool.GetStatistics()
	if statsAfter.UsedSize != 0 {
		t.Errorf("Expected used size 0 after reset, got %d", statsAfter.UsedSize)
	}

	if statsAfter.UsedBlockCount != 0 {
		t.Errorf("Expected 0 used blocks after reset, got %d", statsAfter.UsedBlockCount)
	}

	// Verify that old pointers are invalid
	err = pool.Free(ptr1)
	if err == nil {
		t.Error("Expected error when freeing pointer after reset")
	}

	err = pool.Free(ptr2)
	if err == nil {
		t.Error("Expected error when freeing pointer after reset")
	}
}

func TestMemoryPoolShutdown(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.GCInterval = 100 * time.Millisecond
	config.DefragmentationInterval = 200 * time.Millisecond

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	err = pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}

	if !pool.IsInitialized() {
		t.Error("Pool should be initialized")
	}

	// Shutdown the pool
	err = pool.Shutdown()
	if err != nil {
		t.Fatalf("Failed to shutdown pool: %v", err)
	}

	if pool.IsInitialized() {
		t.Error("Pool should not be initialized after shutdown")
	}

	// Test operations after shutdown
	_, err = pool.Allocate(1024)
	if err == nil {
		t.Error("Expected error when allocating after shutdown")
	}

	err = pool.Free(CUDAMemoryPtr(1000))
	if err == nil {
		t.Error("Expected error when freeing after shutdown")
	}

	// Test double shutdown
	err = pool.Shutdown()
	if err == nil {
		t.Error("Expected error on double shutdown")
	}
}

func TestFragmentationCalculation(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.Strategy = StrategyDynamic
	config.GCInterval = 0
	config.EnableDefragmentation = false
	config.BlockSize = 1 * 1024 * 1024 // 1MB

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	err = pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Create fragmentation by allocating and freeing alternating blocks
	ptrs := make([]CUDAMemoryPtr, 0)

	// Allocate several blocks
	for i := 0; i < 5; i++ {
		ptr, err := pool.Allocate(512 * 1024) // 512KB each
		if err != nil {
			t.Fatalf("Failed to allocate block %d: %v", i, err)
		}
		ptrs = append(ptrs, ptr)
	}

	// Free every other block to create fragmentation
	for i := 1; i < len(ptrs); i += 2 {
		err := pool.Free(ptrs[i])
		if err != nil {
			t.Fatalf("Failed to free block %d: %v", i, err)
		}
	}

	stats := pool.GetStatistics()

	// Should have some fragmentation now
	if stats.FragmentationPercent < 0 {
		t.Errorf("Expected non-negative fragmentation, got %f", stats.FragmentationPercent)
	}

	// Test with no free blocks
	for i := 0; i < len(ptrs); i += 2 {
		pool.Free(ptrs[i])
	}

	// Allocate one big block
	bigPtr, _ := pool.Allocate(1 * 1024 * 1024)

	stats = pool.GetStatistics()

	// Fragmentation should be low with a single block
	if stats.FragmentationPercent > 50.0 {
		t.Errorf("Expected low fragmentation with single block, got %f", stats.FragmentationPercent)
	}

	pool.Free(bigPtr)
}

func TestMaxSizeLimit(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.Strategy = StrategyDynamic
	config.InitialSize = 0
	config.MaxSize = 10 * 1024 * 1024 // 10MB max
	config.GCInterval = 0
	config.EnableDefragmentation = false

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	err = pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Allocate up to the limit
	ptr1, err := pool.Allocate(5 * 1024 * 1024) // 5MB
	if err != nil {
		t.Fatalf("Failed to allocate within limit: %v", err)
	}

	ptr2, err := pool.Allocate(4 * 1024 * 1024) // 4MB (total 9MB)
	if err != nil {
		t.Fatalf("Failed to allocate within limit: %v", err)
	}

	// This should exceed the limit
	_, err = pool.Allocate(2 * 1024 * 1024) // 2MB (would be 11MB total)
	if err == nil {
		t.Error("Expected error when exceeding max size limit")
	}

	// Free some memory and try again
	err = pool.Free(ptr1)
	if err != nil {
		t.Fatalf("Failed to free memory: %v", err)
	}

	// Now this should work
	ptr3, err := pool.Allocate(2 * 1024 * 1024) // 2MB
	if err != nil {
		t.Fatalf("Failed to allocate after freeing: %v", err)
	}

	pool.Free(ptr2)
	pool.Free(ptr3)
}

func TestStatisticsAccuracy(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.Strategy = StrategyFixed
	config.InitialSize = 64 * 1024 * 1024
	config.BlockSize = 16 * 1024 * 1024
	config.GCInterval = 0
	config.EnableDefragmentation = false

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	err = pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	initialStats := pool.GetStatistics()

	// Verify initial statistics
	if initialStats.TotalSize != config.InitialSize {
		t.Errorf("Expected total size %d, got %d", config.InitialSize, initialStats.TotalSize)
	}

	if initialStats.FreeSize != config.InitialSize {
		t.Errorf("Expected free size %d, got %d", config.InitialSize, initialStats.FreeSize)
	}

	if initialStats.UsedSize != 0 {
		t.Errorf("Expected used size 0, got %d", initialStats.UsedSize)
	}

	// Allocate and verify statistics
	ptr, _ := pool.Allocate(8 * 1024 * 1024)

	stats := pool.GetStatistics()

	if stats.UsedSize != config.BlockSize {
		t.Errorf("Expected used size %d, got %d", config.BlockSize, stats.UsedSize)
	}

	if stats.FreeSize != config.InitialSize-config.BlockSize {
		t.Errorf("Expected free size %d, got %d",
			config.InitialSize-config.BlockSize, stats.FreeSize)
	}

	if stats.AllocationCount != 1 {
		t.Errorf("Expected allocation count 1, got %d", stats.AllocationCount)
	}

	// Free and verify
	pool.Free(ptr)

	stats = pool.GetStatistics()

	if stats.DeallocationCount != 1 {
		t.Errorf("Expected deallocation count 1, got %d", stats.DeallocationCount)
	}

	if stats.UsedSize != 0 {
		t.Errorf("Expected used size 0 after free, got %d", stats.UsedSize)
	}

	if stats.AverageAllocationSize != config.BlockSize {
		t.Errorf("Expected average allocation size %d, got %d",
			config.BlockSize, stats.AverageAllocationSize)
	}
}

func TestAllocationHistoryTracking(t *testing.T) {
	config := DefaultPoolConfig(0)
	config.Strategy = StrategyAdaptive
	config.GCInterval = 0
	config.EnableDefragmentation = false

	pool, err := NewAdvancedMemoryPool(config, log.Default())
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}

	err = pool.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}
	defer pool.Shutdown()

	// Make several allocations of different sizes
	sizes := []int64{
		1 * 1024 * 1024, // 1MB
		2 * 1024 * 1024, // 2MB
		4 * 1024 * 1024, // 4MB
		2 * 1024 * 1024, // 2MB
		3 * 1024 * 1024, // 3MB
	}

	ptrs := make([]CUDAMemoryPtr, 0)

	for i, size := range sizes {
		ptr, err := pool.Allocate(size)
		if err != nil {
			t.Fatalf("Failed to allocate %d bytes (iteration %d): %v", size, i, err)
		}
		ptrs = append(ptrs, ptr)
	}

	// Verify allocation history is being tracked
	if len(pool.allocationHistory) != len(sizes) {
		t.Errorf("Expected allocation history length %d, got %d",
			len(sizes), len(pool.allocationHistory))
	}

	// Verify the sizes are tracked correctly
	for i, expectedSize := range sizes {
		if i < len(pool.allocationHistory) && pool.allocationHistory[i] != expectedSize {
			t.Errorf("Expected allocation history[%d] = %d, got %d",
				i, expectedSize, pool.allocationHistory[i])
		}
	}

	// Clean up
	for _, ptr := range ptrs {
		pool.Free(ptr)
	}
}
