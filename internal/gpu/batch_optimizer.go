package gpu

import (
	"context"
	"fmt"
	"math"
	"sort"
	"sync"
	"time"
)

// OptimizationStrategy defines the interface for different batch size optimization algorithms
type OptimizationStrategy interface {
	// OptimizeBatchSize returns the recommended batch size based on current metrics
	OptimizeBatchSize(ctx context.Context, metrics *GPUPerformanceMetrics, history []*BatchPerformanceRecord) (*BatchSizeRecommendation, error)

	// UpdateWithFeedback provides performance feedback to improve future recommendations
	UpdateWithFeedback(ctx context.Context, batchSize int, performance *BatchPerformanceRecord) error

	// Name returns the strategy name for logging and monitoring
	Name() string

	// Reset clears any internal state for fresh optimization
	Reset() error
}

// BatchSizeRecommendation contains the optimization result with confidence metrics
type BatchSizeRecommendation struct {
	RecommendedSize    int                    `json:"recommended_size"`
	Confidence         float64                `json:"confidence"`          // 0-1 confidence score
	ExpectedThroughput float64                `json:"expected_throughput"` // requests/second
	ExpectedLatency    time.Duration          `json:"expected_latency"`
	MemoryUtilization  float64                `json:"memory_utilization"` // 0-1
	Strategy           string                 `json:"strategy"`
	Metadata           map[string]interface{} `json:"metadata"`
	Timestamp          time.Time              `json:"timestamp"`
}

// GPUPerformanceMetrics contains real-time GPU performance data
type GPUPerformanceMetrics struct {
	DeviceID           int       `json:"device_id"`
	MemoryUsed         uint64    `json:"memory_used"`         // bytes
	MemoryTotal        uint64    `json:"memory_total"`        // bytes
	MemoryUtilization  float64   `json:"memory_utilization"`  // 0-1
	ComputeUtilization float64   `json:"compute_utilization"` // 0-1
	Temperature        float64   `json:"temperature"`         // Celsius
	PowerUsage         float64   `json:"power_usage"`         // Watts
	ClockSpeed         int       `json:"clock_speed"`         // MHz
	MemoryClockSpeed   int       `json:"memory_clock_speed"`  // MHz
	ActiveStreams      int       `json:"active_streams"`
	QueuedRequests     int       `json:"queued_requests"`
	Timestamp          time.Time `json:"timestamp"`
}

// BatchPerformanceRecord stores performance data for a specific batch execution
type BatchPerformanceRecord struct {
	BatchSize        int                    `json:"batch_size"`
	Throughput       float64                `json:"throughput"`           // requests/second
	Latency          time.Duration          `json:"latency"`              // end-to-end latency
	MemoryPeak       uint64                 `json:"memory_peak"`          // peak memory usage
	MemoryAllocation uint64                 `json:"memory_allocation"`    // memory allocated for batch
	ComputeTime      time.Duration          `json:"compute_time"`         // actual GPU compute time
	QueueTime        time.Duration          `json:"queue_time"`           // time spent in queue
	Success          bool                   `json:"success"`              // whether batch completed successfully
	ErrorType        string                 `json:"error_type,omitempty"` // error classification if failed
	Timestamp        time.Time              `json:"timestamp"`
	GPUMetrics       *GPUPerformanceMetrics `json:"gpu_metrics,omitempty"`
}

// BatchPerformanceMonitor provides simplified performance monitoring for batch optimization
type BatchPerformanceMonitor struct {
	deviceID int
	mu       sync.RWMutex
}

// NewBatchPerformanceMonitor creates a new performance monitor for batch optimization
func NewBatchPerformanceMonitor(deviceID int) *BatchPerformanceMonitor {
	return &BatchPerformanceMonitor{
		deviceID: deviceID,
	}
}

// GetCurrentMetrics returns current GPU performance metrics (mock implementation)
func (m *BatchPerformanceMonitor) GetCurrentMetrics(ctx context.Context) (*GPUPerformanceMetrics, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// Mock implementation - in production this would query actual GPU metrics
	return &GPUPerformanceMetrics{
		DeviceID:           m.deviceID,
		MemoryUsed:         1024 * 1024 * 1024,     // 1GB
		MemoryTotal:        8 * 1024 * 1024 * 1024, // 8GB
		MemoryUtilization:  0.5,                    // 50%
		ComputeUtilization: 0.7,                    // 70%
		Temperature:        65.0,                   // 65°C
		PowerUsage:         150.0,                  // 150W
		ClockSpeed:         1500,                   // 1500 MHz
		MemoryClockSpeed:   6000,                   // 6000 MHz
		ActiveStreams:      4,
		QueuedRequests:     10,
		Timestamp:          time.Now(),
	}, nil
}

// Close releases resources
func (m *BatchPerformanceMonitor) Close() error {
	return nil
}

// BatchSizeOptimizer coordinates dynamic batch size optimization
type BatchSizeOptimizer struct {
	mu                 sync.RWMutex
	strategies         map[string]OptimizationStrategy
	activeStrategy     string
	performanceMonitor *BatchPerformanceMonitor
	history            []*BatchPerformanceRecord
	maxHistorySize     int
	deviceID           int
	config             *OptimizerConfig

	// Metrics and monitoring
	totalRecommendations int64
	successfulBatches    int64
	failedBatches        int64
	avgThroughput        float64
	avgLatency           time.Duration
	lastOptimization     time.Time
}

// OptimizerConfig contains configuration parameters for the batch size optimizer
type OptimizerConfig struct {
	MinBatchSize         int           `json:"min_batch_size"`
	MaxBatchSize         int           `json:"max_batch_size"`
	DefaultBatchSize     int           `json:"default_batch_size"`
	OptimizationInterval time.Duration `json:"optimization_interval"`
	MemoryThreshold      float64       `json:"memory_threshold"`      // 0-1, memory usage threshold
	TemperatureThreshold float64       `json:"temperature_threshold"` // Celsius
	MaxHistorySize       int           `json:"max_history_size"`
	ConfidenceThreshold  float64       `json:"confidence_threshold"` // minimum confidence to apply recommendation
	AdaptiveTimeout      bool          `json:"adaptive_timeout"`
	EnableThermalControl bool          `json:"enable_thermal_control"`
}

// DefaultOptimizerConfig returns sensible default configuration
func DefaultOptimizerConfig() *OptimizerConfig {
	return &OptimizerConfig{
		MinBatchSize:         1,
		MaxBatchSize:         128,
		DefaultBatchSize:     8,
		OptimizationInterval: 30 * time.Second,
		MemoryThreshold:      0.85, // 85% memory usage threshold
		TemperatureThreshold: 85.0, // 85°C temperature threshold
		MaxHistorySize:       1000,
		ConfidenceThreshold:  0.7,
		AdaptiveTimeout:      true,
		EnableThermalControl: true,
	}
}

// NewBatchSizeOptimizer creates a new batch size optimizer
func NewBatchSizeOptimizer(deviceID int, config *OptimizerConfig) (*BatchSizeOptimizer, error) {
	if config == nil {
		config = DefaultOptimizerConfig()
	}

	monitor := NewBatchPerformanceMonitor(deviceID)

	optimizer := &BatchSizeOptimizer{
		strategies:         make(map[string]OptimizationStrategy),
		performanceMonitor: monitor,
		history:            make([]*BatchPerformanceRecord, 0, config.MaxHistorySize),
		maxHistorySize:     config.MaxHistorySize,
		deviceID:           deviceID,
		config:             config,
		lastOptimization:   time.Now(),
	}

	// Initialize default strategies
	if err := optimizer.initializeStrategies(); err != nil {
		return nil, fmt.Errorf("failed to initialize strategies: %w", err)
	}

	return optimizer, nil
}

// initializeStrategies sets up the available optimization strategies
func (o *BatchSizeOptimizer) initializeStrategies() error {
	// Binary Search Strategy
	binaryStrategy := NewBinarySearchStrategy(o.config)
	o.strategies["binary_search"] = binaryStrategy

	// Heuristic Strategy
	heuristicStrategy := NewHeuristicStrategy(o.config)
	o.strategies["heuristic"] = heuristicStrategy

	// Multi-Armed Bandit Strategy
	banditStrategy := NewBanditStrategy(o.config)
	o.strategies["bandit"] = banditStrategy

	// Set default strategy
	o.activeStrategy = "heuristic"

	return nil
}

// SetStrategy changes the active optimization strategy
func (o *BatchSizeOptimizer) SetStrategy(name string) error {
	o.mu.Lock()
	defer o.mu.Unlock()

	if _, exists := o.strategies[name]; !exists {
		return fmt.Errorf("strategy %s not found", name)
	}

	o.activeStrategy = name
	return nil
}

// GetRecommendation returns the current batch size recommendation
func (o *BatchSizeOptimizer) GetRecommendation(ctx context.Context) (*BatchSizeRecommendation, error) {
	o.mu.Lock()
	defer o.mu.Unlock()

	// Get current GPU metrics
	metrics, err := o.performanceMonitor.GetCurrentMetrics(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get GPU metrics: %w", err)
	}

	// Check thermal constraints
	if o.config.EnableThermalControl && metrics.Temperature > o.config.TemperatureThreshold {
		return &BatchSizeRecommendation{
			RecommendedSize:    o.config.MinBatchSize,
			Confidence:         1.0,
			ExpectedThroughput: 0.0,
			ExpectedLatency:    time.Duration(0),
			MemoryUtilization:  metrics.MemoryUtilization,
			Strategy:           "thermal_throttle",
			Metadata: map[string]interface{}{
				"temperature": metrics.Temperature,
				"threshold":   o.config.TemperatureThreshold,
			},
			Timestamp: time.Now(),
		}, nil
	}

	// Check memory constraints
	if metrics.MemoryUtilization > o.config.MemoryThreshold {
		// Reduce batch size if memory usage is too high
		reductionFactor := math.Max(0.1, 1.0-(metrics.MemoryUtilization-o.config.MemoryThreshold))
		reducedSize := int(float64(o.config.DefaultBatchSize) * reductionFactor)
		if reducedSize < o.config.MinBatchSize {
			reducedSize = o.config.MinBatchSize
		}

		return &BatchSizeRecommendation{
			RecommendedSize:    reducedSize,
			Confidence:         0.9,
			ExpectedThroughput: 0.0,
			ExpectedLatency:    time.Duration(0),
			MemoryUtilization:  metrics.MemoryUtilization,
			Strategy:           "memory_constrained",
			Metadata: map[string]interface{}{
				"memory_utilization": metrics.MemoryUtilization,
				"memory_threshold":   o.config.MemoryThreshold,
				"reduction_factor":   reductionFactor,
			},
			Timestamp: time.Now(),
		}, nil
	}

	// Get recommendation from active strategy
	strategy := o.strategies[o.activeStrategy]
	recommendation, err := strategy.OptimizeBatchSize(ctx, metrics, o.history)
	if err != nil {
		return nil, fmt.Errorf("strategy %s failed: %w", o.activeStrategy, err)
	}

	// Validate recommendation against constraints
	if recommendation.RecommendedSize < o.config.MinBatchSize {
		recommendation.RecommendedSize = o.config.MinBatchSize
	}
	if recommendation.RecommendedSize > o.config.MaxBatchSize {
		recommendation.RecommendedSize = o.config.MaxBatchSize
	}

	o.totalRecommendations++
	return recommendation, nil
}

// RecordPerformance records the performance of a completed batch
func (o *BatchSizeOptimizer) RecordPerformance(record *BatchPerformanceRecord) error {
	o.mu.Lock()
	defer o.mu.Unlock()

	// Add to history
	o.history = append(o.history, record)

	// Trim history if needed
	if len(o.history) > o.maxHistorySize {
		o.history = o.history[len(o.history)-o.maxHistorySize:]
	}

	// Update statistics
	if record.Success {
		o.successfulBatches++
		o.updateAverageMetrics(record)
	} else {
		o.failedBatches++
	}

	// Provide feedback to active strategy
	strategy := o.strategies[o.activeStrategy]
	return strategy.UpdateWithFeedback(context.Background(), record.BatchSize, record)
}

// updateAverageMetrics updates running averages for monitoring
func (o *BatchSizeOptimizer) updateAverageMetrics(record *BatchPerformanceRecord) {
	totalBatches := float64(o.successfulBatches)

	// Update average throughput
	o.avgThroughput = ((o.avgThroughput * (totalBatches - 1)) + record.Throughput) / totalBatches

	// Update average latency
	avgLatencyNs := ((float64(o.avgLatency.Nanoseconds()) * (totalBatches - 1)) + float64(record.Latency.Nanoseconds())) / totalBatches
	o.avgLatency = time.Duration(int64(avgLatencyNs))
}

// GetPerformanceHistory returns the recent performance history
func (o *BatchSizeOptimizer) GetPerformanceHistory() []*BatchPerformanceRecord {
	o.mu.RLock()
	defer o.mu.RUnlock()

	// Return a copy to prevent modification
	history := make([]*BatchPerformanceRecord, len(o.history))
	copy(history, o.history)
	return history
}

// GetStatistics returns current optimization statistics
func (o *BatchSizeOptimizer) GetStatistics() map[string]interface{} {
	o.mu.RLock()
	defer o.mu.RUnlock()

	successRate := 0.0
	if o.totalRecommendations > 0 {
		successRate = float64(o.successfulBatches) / float64(o.totalRecommendations)
	}

	return map[string]interface{}{
		"device_id":             o.deviceID,
		"active_strategy":       o.activeStrategy,
		"total_recommendations": o.totalRecommendations,
		"successful_batches":    o.successfulBatches,
		"failed_batches":        o.failedBatches,
		"success_rate":          successRate,
		"avg_throughput":        o.avgThroughput,
		"avg_latency_ms":        float64(o.avgLatency.Nanoseconds()) / 1e6,
		"history_size":          len(o.history),
		"last_optimization":     o.lastOptimization,
	}
}

// ShouldOptimize returns whether it's time to run optimization
func (o *BatchSizeOptimizer) ShouldOptimize() bool {
	o.mu.RLock()
	defer o.mu.RUnlock()

	return time.Since(o.lastOptimization) >= o.config.OptimizationInterval
}

// GetAvailableStrategies returns the list of available optimization strategies
func (o *BatchSizeOptimizer) GetAvailableStrategies() []string {
	o.mu.RLock()
	defer o.mu.RUnlock()

	strategies := make([]string, 0, len(o.strategies))
	for name := range o.strategies {
		strategies = append(strategies, name)
	}
	sort.Strings(strategies)
	return strategies
}

// Close shuts down the optimizer and releases resources
func (o *BatchSizeOptimizer) Close() error {
	o.mu.Lock()
	defer o.mu.Unlock()

	if o.performanceMonitor != nil {
		return o.performanceMonitor.Close()
	}

	return nil
}

// AdaptiveTimeout calculates timeout based on current load and latency requirements
func (o *BatchSizeOptimizer) AdaptiveTimeout(currentLoad float64, maxLatency time.Duration) time.Duration {
	if !o.config.AdaptiveTimeout {
		return 10 * time.Millisecond // Fixed timeout
	}

	baseTimeout := 10 * time.Millisecond
	loadFactor := math.Max(0.1, math.Min(1.0, currentLoad))
	adaptiveTimeout := time.Duration(float64(baseTimeout) / loadFactor)

	// Ensure we don't exceed maximum latency budget
	if maxLatency > 0 && adaptiveTimeout > maxLatency/2 {
		adaptiveTimeout = maxLatency / 2
	}

	return adaptiveTimeout
}
