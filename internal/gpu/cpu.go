package gpu

import (
	"fmt"
	"log"
	"runtime"
	"time"
)

// CPUDetector implements GPUDetector for CPU fallback
type CPUDetector struct {
	logger      *log.Logger
	initialized bool
}

// NewCPUDetector creates a new CPU fallback detector
func NewCPUDetector(logger *log.Logger) GPUDetector {
	if logger == nil {
		logger = log.Default()
	}

	return &CPUDetector{
		logger: logger,
	}
}

// Detect returns a single CPU "GPU" entry as fallback
func (c *CPUDetector) Detect() ([]*GPUInfo, error) {
	if !c.initialized {
		return nil, fmt.Errorf("CPU detector not initialized")
	}

	cpuInfo := &GPUInfo{
		ID:                      0,
		Name:                    fmt.Sprintf("CPU (%s %s)", runtime.GOOS, runtime.GOARCH),
		Type:                    GPUTypeCPU,
		TotalMemory:             c.estimateSystemMemory(),
		FreeMemory:              c.estimateSystemMemory() / 2, // Rough estimate
		ComputeCapability:       ComputeCapability{Major: 1, Minor: 0},
		MultiProcessorCount:     runtime.NumCPU(),
		ClockRate:               2000000, // 2 GHz estimate in kHz
		MemoryClockRate:         1600000, // 1.6 GHz estimate in kHz
		MemoryBusWidth:          64,      // 64-bit
		MaxThreadsPerBlock:      1024,    // Conservative estimate
		MaxBlockDimensions:      [3]int{1024, 1024, 64},
		MaxGridDimensions:       [3]int{65535, 65535, 65535},
		WarpSize:                32,        // Common value
		MaxSharedMemoryPerBlock: 48 * 1024, // 48KB
		Available:               true,

		PowerUsage:  0, // Not available
		Utilization: 0, // Will be updated if monitoring
	}

	return []*GPUInfo{cpuInfo}, nil
}

// GetInfo retrieves information about the CPU "GPU"
func (c *CPUDetector) GetInfo(deviceID int) (*GPUInfo, error) {
	if !c.initialized {
		return nil, fmt.Errorf("CPU detector not initialized")
	}

	if deviceID != 0 {
		return nil, fmt.Errorf("CPU detector only supports device ID 0")
	}

	gpus, err := c.Detect()
	if err != nil {
		return nil, err
	}

	if len(gpus) == 0 {
		return nil, fmt.Errorf("no CPU device found")
	}

	return gpus[0], nil
}

// GetMetrics retrieves basic CPU metrics
func (c *CPUDetector) GetMetrics(deviceID int) (*GPUMetrics, error) {
	if !c.initialized {
		return nil, fmt.Errorf("CPU detector not initialized")
	}

	if deviceID != 0 {
		return nil, fmt.Errorf("CPU detector only supports device ID 0")
	}

	// Basic CPU metrics - limited information available
	metrics := &GPUMetrics{
		DeviceID:          deviceID,
		Timestamp:         time.Now(),
		GPUUtilization:    0,    // CPU utilization would require more complex monitoring
		MemoryUtilization: 0,    // System memory utilization would require OS calls
		PowerConsumption:  0,    // Not available
		ClockSpeed:        2000, // Estimated 2 GHz
		MemoryClockSpeed:  1600, // Estimated 1.6 GHz
		FanSpeed:          0,    // Not available
	}

	return metrics, nil
}

// IsSupported always returns true for CPU fallback
func (c *CPUDetector) IsSupported() bool {
	return true
}

// Initialize performs initialization for CPU detector
func (c *CPUDetector) Initialize() error {
	c.initialized = true
	c.logger.Println("CPU fallback detector initialized")
	return nil
}

// Cleanup performs cleanup operations
func (c *CPUDetector) Cleanup() error {
	c.initialized = false
	c.logger.Println("CPU fallback detector cleaned up")
	return nil
}

// estimateSystemMemory provides a rough estimate of system memory
func (c *CPUDetector) estimateSystemMemory() int64 {
	// This is a very rough estimate - in a real implementation,
	// you would use system calls to get actual memory information
	// For now, return a conservative estimate based on CPU count
	cpuCount := runtime.NumCPU()

	// Estimate 2GB per CPU core as a baseline
	estimatedGB := cpuCount * 2
	if estimatedGB < 4 {
		estimatedGB = 4 // Minimum 4GB
	}
	if estimatedGB > 64 {
		estimatedGB = 64 // Maximum 64GB estimate
	}

	return int64(estimatedGB) * 1024 * 1024 * 1024 // Convert to bytes
}
