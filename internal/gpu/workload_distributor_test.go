package gpu

import (
	"context"
	"log"
	"testing"
	"time"
)

func TestWorkloadDistributorCreation(t *testing.T) {
	config := DefaultMultiDeviceConfig()
	config.MonitoringInterval = 0 // Disable monitoring for test

	logger := log.Default()
	deviceMgr, err := NewMultiDeviceManager(config, logger)
	if err != nil {
		t.Fatalf("Failed to create device manager: %v", err)
	}

	distributor := NewWorkloadDistributor(deviceMgr, LoadBalanceRoundRobin, logger)
	if distributor == nil {
		t.Fatal("Distributor is nil")
	}

	if distributor.GetStrategy() != LoadBalanceRoundRobin {
		t.Error("Strategy not set correctly")
	}
}

func TestWorkloadTask(t *testing.T) {
	task := &WorkloadTask{
		ID:                "test-task-1",
		Priority:          PriorityNormal,
		EstimatedDuration: time.Minute * 5,
		MemoryRequirement: 1024 * 1024 * 100, // 100MB
		ComputeIntensity:  ComputeModerate,
		Dependencies:      []string{},
		CreatedAt:         time.Now(),
		Metadata:          map[string]interface{}{"type": "test"},
	}

	if task.ID != "test-task-1" {
		t.Error("Task ID not set correctly")
	}

	if task.Priority != PriorityNormal {
		t.Error("Task priority not set correctly")
	}

	if task.ComputeIntensity != ComputeModerate {
		t.Error("Task compute intensity not set correctly")
	}
}

func TestTaskPriorityAndComputeIntensityEnums(t *testing.T) {
	priorities := []TaskPriority{PriorityLow, PriorityNormal, PriorityHigh, PriorityCritical}
	if len(priorities) != 4 {
		t.Error("Unexpected number of priority levels")
	}

	intensities := []ComputeIntensity{ComputeLight, ComputeModerate, ComputeHeavy, ComputeIntensive}
	if len(intensities) != 4 {
		t.Error("Unexpected number of compute intensity levels")
	}
}

func TestDistributorConfig(t *testing.T) {
	config := DefaultDistributorConfig()

	err := validateDistributorConfig(config)
	if err != nil {
		t.Errorf("Default configuration should be valid: %v", err)
	}

	// Test invalid configs
	invalidConfigs := []DistributorConfig{
		{MemoryWeightFactor: -0.1}, // Negative weight
		{MemoryWeightFactor: 1.1},  // Weight > 1
		{ComputeWeightFactor: -0.1},
		{UtilizationWeightFactor: 1.5},
		{TaskHistoryWeight: -0.1},
		{RebalancingThreshold: 1.5},
		{MinTasksBeforeRebalancing: -1},
		{MaxHistorySize: 0},
	}

	for i, cfg := range invalidConfigs {
		err := validateDistributorConfig(cfg)
		if err == nil {
			t.Errorf("Invalid config %d should have failed validation", i)
		}
	}
}

func TestDistributorConfigManagement(t *testing.T) {
	config := DefaultMultiDeviceConfig()
	config.MonitoringInterval = 0

	logger := log.Default()
	deviceMgr, err := NewMultiDeviceManager(config, logger)
	if err != nil {
		t.Fatalf("Failed to create device manager: %v", err)
	}

	distributor := NewWorkloadDistributor(deviceMgr, LoadBalanceRoundRobin, logger)

	// Test getting default config
	defaultConfig := distributor.GetConfig()
	if defaultConfig.MemoryWeightFactor != 0.3 {
		t.Error("Default memory weight factor incorrect")
	}

	// Test setting valid config
	newConfig := DefaultDistributorConfig()
	newConfig.MemoryWeightFactor = 0.5
	err = distributor.SetConfig(newConfig)
	if err != nil {
		t.Errorf("Failed to set valid config: %v", err)
	}

	// Verify config was set
	retrievedConfig := distributor.GetConfig()
	if retrievedConfig.MemoryWeightFactor != 0.5 {
		t.Error("Config was not updated correctly")
	}

	// Test setting invalid config
	invalidConfig := DefaultDistributorConfig()
	invalidConfig.MemoryWeightFactor = -0.1
	err = distributor.SetConfig(invalidConfig)
	if err == nil {
		t.Error("Invalid config should have been rejected")
	}
}

func TestDistributorStrategyManagement(t *testing.T) {
	config := DefaultMultiDeviceConfig()
	config.MonitoringInterval = 0

	logger := log.Default()
	deviceMgr, err := NewMultiDeviceManager(config, logger)
	if err != nil {
		t.Fatalf("Failed to create device manager: %v", err)
	}

	distributor := NewWorkloadDistributor(deviceMgr, LoadBalanceRoundRobin, logger)

	// Test initial strategy
	if distributor.GetStrategy() != LoadBalanceRoundRobin {
		t.Error("Initial strategy incorrect")
	}

	// Test changing strategy
	distributor.SetStrategy(LoadBalanceMemoryBased)
	if distributor.GetStrategy() != LoadBalanceMemoryBased {
		t.Error("Strategy was not changed correctly")
	}

	// Test all strategies
	strategies := []LoadBalancingStrategy{
		LoadBalanceRoundRobin,
		LoadBalanceMemoryBased,
		LoadBalanceComputeBased,
		LoadBalanceDynamic,
		LoadBalanceWeighted,
	}

	for _, strategy := range strategies {
		distributor.SetStrategy(strategy)
		if distributor.GetStrategy() != strategy {
			t.Errorf("Failed to set strategy %v", strategy)
		}
	}
}

func TestTaskCompletionTracking(t *testing.T) {
	config := DefaultMultiDeviceConfig()
	config.MonitoringInterval = 0

	logger := log.Default()
	deviceMgr, err := NewMultiDeviceManager(config, logger)
	if err != nil {
		t.Fatalf("Failed to create device manager: %v", err)
	}

	distributor := NewWorkloadDistributor(deviceMgr, LoadBalanceRoundRobin, logger)

	// Test completing non-existent task
	err = distributor.CompleteTask("non-existent", true)
	if err == nil {
		t.Error("Completing non-existent task should fail")
	}

	// Test statistics on empty distributor
	stats := distributor.GetDistributionStatistics()
	if stats["total_tasks"].(int) != 0 {
		t.Error("Empty distributor should have 0 total tasks")
	}
}

func TestDistributionRecord(t *testing.T) {
	record := DistributionRecord{
		TaskID:      "test-task",
		DeviceID:    "device-1",
		Strategy:    "round_robin",
		Score:       0.8,
		AssignedAt:  time.Now(),
		CompletedAt: time.Time{}, // Not completed yet
		Success:     false,
	}

	if record.TaskID != "test-task" {
		t.Error("Task ID not set correctly in record")
	}

	if record.CompletedAt.IsZero() != true {
		t.Error("Completion time should be zero for incomplete task")
	}
}

func TestDeviceScore(t *testing.T) {
	score := DeviceScore{
		DeviceID:         "device-1",
		Score:            0.75,
		MemoryScore:      0.8,
		ComputeScore:     0.7,
		UtilizationScore: 0.9,
		HistoryScore:     0.6,
		Available:        true,
		Reason:           "",
	}

	if !score.Available {
		t.Error("Device should be available")
	}

	if score.Score != 0.75 {
		t.Error("Score not set correctly")
	}
}

func TestUnknownStrategy(t *testing.T) {
	config := DefaultMultiDeviceConfig()
	config.MonitoringInterval = 0

	logger := log.Default()
	deviceMgr, err := NewMultiDeviceManager(config, logger)
	if err != nil {
		t.Fatalf("Failed to create device manager: %v", err)
	}

	distributor := NewWorkloadDistributor(deviceMgr, LoadBalancingStrategy(999), logger)

	task := &WorkloadTask{
		ID:                "test-task",
		Priority:          PriorityNormal,
		EstimatedDuration: time.Minute,
		ComputeIntensity:  ComputeLight,
		CreatedAt:         time.Now(),
	}

	ctx := context.Background()
	_, err = distributor.AssignTask(ctx, task)
	if err == nil {
		t.Error("Unknown strategy should cause an error")
	}

	if err != nil && len(err.Error()) > 0 {
		// Just check that we get some error - the exact message format may vary
		t.Logf("Got expected error: %v", err)
	}
}

func TestDistributorStatistics(t *testing.T) {
	config := DefaultMultiDeviceConfig()
	config.MonitoringInterval = 0

	logger := log.Default()
	deviceMgr, err := NewMultiDeviceManager(config, logger)
	if err != nil {
		t.Fatalf("Failed to create device manager: %v", err)
	}

	distributor := NewWorkloadDistributor(deviceMgr, LoadBalanceRoundRobin, logger)

	// Get statistics from empty distributor
	stats := distributor.GetDistributionStatistics()

	if stats["total_tasks"].(int) != 0 {
		t.Error("New distributor should have 0 total tasks")
	}

	if stats["current_strategy"].(string) != "round_robin" {
		t.Error("Current strategy should be round_robin")
	}

	deviceDist, ok := stats["device_distribution"].(map[string]int)
	if !ok {
		t.Error("Device distribution should be a map[string]int")
	}

	if len(deviceDist) != 0 {
		t.Error("Device distribution should be empty for new distributor")
	}
}
