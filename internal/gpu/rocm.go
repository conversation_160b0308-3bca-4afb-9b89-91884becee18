package gpu

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"neuralmetergo/internal/gpu/types"
)

// ROCmDetector implements GPUDetector for ROCm devices with runtime detection
type ROCmDetector struct {
	logger      *log.<PERSON><PERSON>
	initialized bool
	deviceCount int
	available   bool
}

// NewROCmDetector creates a new ROCm detector with runtime detection
func NewROCmDetector(logger *log.Logger) GPUDetector {
	if logger == nil {
		logger = log.Default()
	}

	detector := &ROCmDetector{
		logger: logger,
	}

	// Check if ROCm is available at runtime
	detector.available = detector.checkROCmAvailability()
	
	return detector
}

// checkROCmAvailability checks if ROCm is available on the system
func (r *ROCmDetector) checkROCmAvailability() bool {
	// Check for rocm-smi
	if _, err := exec.LookPath("rocm-smi"); err == nil {
		r.logger.Printf("Found rocm-smi command")
		return true
	}

	// Check for ROCm installation directories
	rocmPaths := []string{
		"/opt/rocm",
		"/usr/lib/x86_64-linux-gnu/rocm",
	}

	for _, path := range rocmPaths {
		if _, err := os.Stat(path); err == nil {
			r.logger.Printf("Found ROCm installation at %s", path)
			return true
		}
	}

	r.logger.Printf("ROCm not available on this system")
	return false
}

// IsSupported returns true if ROCm is available
func (r *ROCmDetector) IsSupported() bool {
	return r.available
}

// Initialize initializes the ROCm detector
func (r *ROCmDetector) Initialize() error {
	if !r.available {
		return fmt.Errorf("ROCm not available")
	}

	if r.initialized {
		return nil
	}

	// Get device count using rocm-smi
	cmd := exec.Command("rocm-smi", "--showid")
	output, err := cmd.Output()
	if err != nil {
		r.logger.Printf("Failed to get ROCm device count: %v", err)
		r.deviceCount = 0
		return err
	}

	// Parse device count from output
	lines := strings.Split(string(output), "\n")
	count := 0
	for _, line := range lines {
		if strings.Contains(line, "GPU[") {
			count++
		}
	}
	r.deviceCount = count

	r.initialized = true
	r.logger.Printf("ROCm initialized with %d devices", r.deviceCount)
	return nil
}

// Detect detects available ROCm devices
func (r *ROCmDetector) Detect() ([]*GPUInfo, error) {
	if !r.available {
		return nil, fmt.Errorf("ROCm not available")
	}

	if err := r.Initialize(); err != nil {
		return nil, err
	}

	var devices []*GPUInfo

	for i := 0; i < r.deviceCount; i++ {
		name := r.getDeviceName(i)
		memory := r.getDeviceMemory(i)

		device := &GPUInfo{
			ID:          i,
			Name:        name,
			Type:        GPUTypeROCm,
			TotalMemory: int64(memory),
			FreeMemory:  int64(memory), // Simplified
		}
		devices = append(devices, device)
	}

	return devices, nil
}

// GetInfo returns information about a specific device
func (r *ROCmDetector) GetInfo(deviceID int) (*GPUInfo, error) {
	if !r.available {
		return nil, fmt.Errorf("ROCm not available")
	}

	if deviceID >= r.deviceCount {
		return nil, fmt.Errorf("device %d not found", deviceID)
	}

	name := r.getDeviceName(deviceID)
	memory := r.getDeviceMemory(deviceID)

	return &GPUInfo{
		ID:          deviceID,
		Name:        name,
		Type:        GPUTypeROCm,
		TotalMemory: int64(memory),
		FreeMemory:  int64(memory),
	}, nil
}

// GetMetrics retrieves real-time metrics for a specific GPU
func (r *ROCmDetector) GetMetrics(deviceID int) (*GPUMetrics, error) {
	if !r.available {
		return nil, fmt.Errorf("ROCm not available")
	}

	if deviceID >= r.deviceCount {
		return nil, fmt.Errorf("device %d not found", deviceID)
	}

	return &GPUMetrics{
		DeviceID:          deviceID,
		Timestamp:         time.Now(),
		GPUUtilization:    0.0, // Would need rocm-smi parsing
		MemoryUtilization: 0.0, // Would need rocm-smi parsing
		PowerConsumption:  0.0, // Would need rocm-smi parsing
		ClockSpeed:        0,    // Would need rocm-smi parsing
		MemoryClockSpeed:  0,    // Would need rocm-smi parsing
		FanSpeed:          0,    // Would need rocm-smi parsing
	}, nil
}

// Cleanup cleans up the ROCm detector
func (r *ROCmDetector) Cleanup() error {
	r.initialized = false
	return nil
}

func (r *ROCmDetector) getDeviceName(deviceID int) string {
	cmd := exec.Command("rocm-smi", "--showproductname")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Sprintf("ROCm GPU %d", deviceID)
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, fmt.Sprintf("GPU[%d]", deviceID)) {
			parts := strings.Split(line, ":")
			if len(parts) > 1 {
				return strings.TrimSpace(parts[1])
			}
		}
	}

	return fmt.Sprintf("ROCm GPU %d", deviceID)
}

func (r *ROCmDetector) getDeviceMemory(deviceID int) uint64 {
	cmd := exec.Command("rocm-smi", "--showmeminfo", "vram", "--device", strconv.Itoa(deviceID))
	output, err := cmd.Output()
	if err != nil {
		return 0
	}

	// Parse memory from output (simplified)
	var memory uint64
	fmt.Sscanf(string(output), "%d", &memory)
	return memory * 1024 * 1024 // Convert MB to bytes
}

// ROCmBackendWrapper wraps ROCmDetector to implement types.GPUBackend interface
type ROCmBackendWrapper struct {
	detector GPUDetector
}

// Name returns the backend name
func (r *ROCmBackendWrapper) Name() string {
	return "ROCm"
}

// Version returns the backend version
func (r *ROCmBackendWrapper) Version() string {
	return "1.0"
}

// Platform returns the platform name
func (r *ROCmBackendWrapper) Platform() string {
	return "Linux"
}

// EnumerateDevices lists all ROCm devices
func (r *ROCmBackendWrapper) EnumerateDevices(ctx context.Context) ([]types.GPUDevice, error) {
	gpuInfos, err := r.detector.Detect()
	if err != nil {
		return nil, err
	}

	var devices []types.GPUDevice
	for _, info := range gpuInfos {
		device := types.GPUDevice{
			ID:      fmt.Sprintf("rocm:%d", info.ID),
			Name:    info.Name,
			Backend: "ROCm",
		}
		devices = append(devices, device)
	}

	return devices, nil
}

// GetDevice returns a specific device by ID
func (r *ROCmBackendWrapper) GetDevice(deviceID string) (*types.GPUDevice, error) {
	var id int
	fmt.Sscanf(deviceID, "rocm:%d", &id)
	
	info, err := r.detector.GetInfo(id)
	if err != nil {
		return nil, err
	}

	return &types.GPUDevice{
		ID:      deviceID,
		Name:    info.Name,
		Backend: "ROCm",
	}, nil
}

// GetCapabilities returns device capabilities
func (r *ROCmBackendWrapper) GetCapabilities(device *types.GPUDevice) (*types.GPUCapability, error) {
	return &types.GPUCapability{
		Features: map[types.GPUFeature]bool{
			types.FeatureCompute: true,
		},
	}, nil
}

// SupportsFeature checks if device supports a feature
func (r *ROCmBackendWrapper) SupportsFeature(device *types.GPUDevice, feature types.GPUFeature) bool {
	return feature == types.FeatureCompute
}

// CreateContext creates a GPU context for the device
func (r *ROCmBackendWrapper) CreateContext(device *types.GPUDevice) (types.GPUContext, error) {
	var deviceID int
	fmt.Sscanf(device.ID, "rocm:%d", &deviceID)
	
	return &ROCmContext{
		deviceID: deviceID,
		detector: r.detector,
	}, nil
}

// CreateMemoryManager creates a memory manager
func (r *ROCmBackendWrapper) CreateMemoryManager(ctx types.GPUContext) (types.GPUMemoryManager, error) {
	return &ROCmMemoryManager{ctx: ctx}, nil
}

// CreateExecutor creates an executor
func (r *ROCmBackendWrapper) CreateExecutor(ctx types.GPUContext) (types.GPUExecutor, error) {
	return &ROCmExecutor{ctx: ctx}, nil
}

// ROCmContext implements types.GPUContext
type ROCmContext struct {
	deviceID int
	detector GPUDetector
}

func (r *ROCmContext) GetDevice() *types.GPUDevice {
	return &types.GPUDevice{
		ID:      fmt.Sprintf("rocm:%d", r.deviceID),
		Backend: "ROCm",
	}
}

func (r *ROCmContext) IsValid() bool {
	return true
}

func (r *ROCmContext) Synchronize() error {
	return nil
}

func (r *ROCmContext) GetDeviceID() string {
	return fmt.Sprintf("rocm:%d", r.deviceID)
}

func (r *ROCmContext) GetBackend() string {
	return "ROCm"
}

func (r *ROCmContext) Destroy() error {
	return nil
}

// ROCmMemoryManager implements types.GPUMemoryManager
type ROCmMemoryManager struct {
	ctx types.GPUContext
}

func (r *ROCmMemoryManager) Allocate(size uint64) (types.GPUMemory, error) {
	return &ROCmMemory{size: size}, nil
}

func (r *ROCmMemoryManager) AllocateType(size uint64, memType types.MemoryType) (types.GPUMemory, error) {
	return &ROCmMemory{size: size}, nil
}

func (r *ROCmMemoryManager) GetStats() types.GPUMemoryStats {
	return types.GPUMemoryStats{}
}

func (r *ROCmMemoryManager) Cleanup() error {
	return nil
}

// ROCmMemory implements types.GPUMemory
type ROCmMemory struct {
	size uint64
}

func (r *ROCmMemory) Ptr() uintptr {
	return 0
}

func (r *ROCmMemory) Size() uint64 {
	return r.size
}

func (r *ROCmMemory) Type() types.MemoryType {
	return types.MemoryTypeDiscrete
}

func (r *ROCmMemory) Free() error {
	return nil
}

func (r *ROCmMemory) CopyFrom(src []byte) error {
	return nil
}

func (r *ROCmMemory) CopyTo(dst []byte) error {
	return nil
}

func (r *ROCmMemory) CopyFromGPU(src types.GPUMemory) error {
	return nil
}

// ROCmExecutor implements types.GPUExecutor
type ROCmExecutor struct {
	ctx types.GPUContext
}

func (r *ROCmExecutor) CreateKernel(source string, entryPoint string) (types.GPUKernel, error) {
	return &ROCmKernel{name: entryPoint}, nil
}

func (r *ROCmExecutor) CreateStream() (types.GPUStream, error) {
	return &ROCmStream{id: "rocm_stream"}, nil
}

func (r *ROCmExecutor) CreateEvent() (types.GPUEvent, error) {
	return &ROCmEvent{}, nil
}

func (r *ROCmExecutor) Synchronize() error {
	return nil
}

func (r *ROCmExecutor) GetStreams() []types.GPUStream {
	return []types.GPUStream{}
}

// ROCmKernel implements types.GPUKernel
type ROCmKernel struct {
	name string
}

func (r *ROCmKernel) GetName() string {
	return r.name
}

func (r *ROCmKernel) Launch(grid types.GridDimension, block types.GridDimension, args []interface{}, stream types.GPUStream) error {
	return nil
}

func (r *ROCmKernel) GetAttributes() map[string]interface{} {
	return map[string]interface{}{}
}

func (r *ROCmKernel) Destroy() error {
	return nil
}

// ROCmStream implements types.GPUStream
type ROCmStream struct {
	id string
}

func (r *ROCmStream) ID() string {
	return r.id
}

func (r *ROCmStream) Submit(kernel types.GPUKernel, grid types.GridDimension, block types.GridDimension, args []interface{}) error {
	return nil
}

func (r *ROCmStream) Synchronize() error {
	return nil
}

func (r *ROCmStream) Query() types.StreamState {
	return 0
}

func (r *ROCmStream) Destroy() error {
	return nil
}

// ROCmEvent implements types.GPUEvent
type ROCmEvent struct{}

func (r *ROCmEvent) ID() string {
	return "rocm_event"
}

func (r *ROCmEvent) Record(stream types.GPUStream) error {
	return nil
}

func (r *ROCmEvent) Wait() error {
	return nil
}

func (r *ROCmEvent) Query() types.EventState {
	return 0
}

func (r *ROCmEvent) ElapsedTime(start types.GPUEvent) (time.Duration, error) {
	return 0, nil
}

func (r *ROCmEvent) Destroy() error {
	return nil
} 