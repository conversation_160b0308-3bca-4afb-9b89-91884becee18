package gpu

import (
	"fmt"
	"log"
	"sync"
	"sync/atomic"
	"time"
)

// APIType represents the different GPU APIs supported
type APIType int

const (
	APIAny    APIType = -1 // Special value for auto-assignment to any API
	APICuda   APIType = 0
	APIOpenCL APIType = 1
	APIMetal  APIType = 2
)

func (api APIType) String() string {
	switch api {
	case APIAny:
		return "Any"
	case APICuda:
		return "CUDA"
	case APIOpenCL:
		return "OpenCL"
	case APIMetal:
		return "Metal"
	default:
		return "Unknown"
	}
}

// UnifiedPriority represents priority levels across all APIs
type UnifiedPriority int

const (
	UnifiedPriorityLow    UnifiedPriority = 0
	UnifiedPriorityNormal UnifiedPriority = 1
	UnifiedPriorityMedium UnifiedPriority = 1 // Alias for Normal for compatibility
	UnifiedPriorityHigh   UnifiedPriority = 2
)

// StreamManagerInterface defines the interface for stream managers
type StreamManagerInterface interface {
	Initialize(devices []int, minStreams, maxStreams int) error
	Cleanup() error
	GetManagerStats() map[string]interface{}
}

// QueueManagerInterface defines the interface for queue managers
type QueueManagerInterface interface {
	Initialize(devices []int, contexts map[int]string, minQueues, maxQueues int) error
	Cleanup() error
	GetManagerStats() map[string]interface{}
}

// BufferManagerInterface defines the interface for buffer managers
type BufferManagerInterface interface {
	Initialize(devices []int, queues map[int]string, minBuffers, maxBuffers int) error
	Cleanup() error
	GetManagerStats() map[string]interface{}
}

// ExecutionState represents the state of execution across APIs
type ExecutionState int

const (
	ExecutionStateIdle ExecutionState = iota
	ExecutionStatePending
	ExecutionStateRunning
	ExecutionStateCompleted
	ExecutionStateError
)

// SyncEvent represents a synchronization event that can be used across APIs
type SyncEvent struct {
	id           string
	apiType      APIType
	deviceID     int
	eventType    EventType
	state        ExecutionState
	createdAt    time.Time
	resolvedAt   *time.Time
	dependencies []*SyncEvent
	dependents   []*SyncEvent
	userData     interface{}

	// Thread safety
	mu sync.RWMutex
}

// EventType defines different types of synchronization events
type EventType int

const (
	EventTypeStreamComplete EventType = iota
	EventTypeQueueComplete
	EventTypeBufferComplete
	EventTypeBarrier
	EventTypeMemoryTransfer
	EventTypeCustom
)

func (et EventType) String() string {
	switch et {
	case EventTypeStreamComplete:
		return "StreamComplete"
	case EventTypeQueueComplete:
		return "QueueComplete"
	case EventTypeBufferComplete:
		return "BufferComplete"
	case EventTypeBarrier:
		return "Barrier"
	case EventTypeMemoryTransfer:
		return "MemoryTransfer"
	case EventTypeCustom:
		return "Custom"
	default:
		return "Unknown"
	}
}

// NewSyncEvent creates a new synchronization event
func NewSyncEvent(id string, apiType APIType, deviceID int, eventType EventType) *SyncEvent {
	return &SyncEvent{
		id:           id,
		apiType:      apiType,
		deviceID:     deviceID,
		eventType:    eventType,
		state:        ExecutionStatePending,
		createdAt:    time.Now(),
		dependencies: make([]*SyncEvent, 0),
		dependents:   make([]*SyncEvent, 0),
	}
}

// Event management methods
func (e *SyncEvent) GetID() string {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.id
}

func (e *SyncEvent) GetAPIType() APIType {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.apiType
}

func (e *SyncEvent) GetDeviceID() int {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.deviceID
}

func (e *SyncEvent) GetEventType() EventType {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.eventType
}

func (e *SyncEvent) GetState() ExecutionState {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.state
}

func (e *SyncEvent) SetState(state ExecutionState) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.state = state
	if state == ExecutionStateCompleted || state == ExecutionStateError {
		now := time.Now()
		e.resolvedAt = &now
	}
}

func (e *SyncEvent) AddDependency(dependency *SyncEvent) error {
	if dependency == nil {
		return fmt.Errorf("cannot add nil dependency")
	}
	if e == dependency {
		return fmt.Errorf("cannot add self as dependency")
	}

	e.mu.Lock()
	defer e.mu.Unlock()

	// Check for circular dependencies
	if e.hasCircularDependency(dependency) {
		return fmt.Errorf("adding dependency would create circular dependency")
	}

	e.dependencies = append(e.dependencies, dependency)
	dependency.addDependent(e)
	return nil
}

func (e *SyncEvent) addDependent(dependent *SyncEvent) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.dependents = append(e.dependents, dependent)
}

func (e *SyncEvent) hasCircularDependency(newDep *SyncEvent) bool {
	visited := make(map[string]bool)
	// Check if the new dependency has 'e' in its dependency chain
	return newDep.checkCircular(e, visited)
}

func (e *SyncEvent) checkCircular(target *SyncEvent, visited map[string]bool) bool {
	if visited[e.id] {
		return true
	}
	if e == target {
		return true
	}

	visited[e.id] = true
	for _, dep := range e.dependencies {
		if dep.checkCircular(target, visited) {
			return true
		}
	}
	return false
}

func (e *SyncEvent) IsReady() bool {
	e.mu.RLock()
	defer e.mu.RUnlock()

	for _, dep := range e.dependencies {
		if dep.GetState() != ExecutionStateCompleted {
			return false
		}
	}
	return true
}

func (e *SyncEvent) GetDependencies() []*SyncEvent {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return append([]*SyncEvent(nil), e.dependencies...)
}

func (e *SyncEvent) GetDependents() []*SyncEvent {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return append([]*SyncEvent(nil), e.dependents...)
}

func (e *SyncEvent) SetUserData(data interface{}) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.userData = data
}

func (e *SyncEvent) GetUserData() interface{} {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.userData
}

// UnifiedResource represents a resource that can be managed across APIs
type UnifiedResource struct {
	id           string
	apiType      APIType
	deviceID     int
	priority     UnifiedPriority
	nativeHandle interface{} // Could be CUDAStream, OpenCLQueue, or MetalBuffer
	state        ExecutionState
	inUse        bool
	lastUsed     time.Time
	createdAt    time.Time

	// Thread safety
	mu sync.RWMutex
}

// NewUnifiedResource creates a new unified resource
func NewUnifiedResource(id string, apiType APIType, deviceID int, priority UnifiedPriority, nativeHandle interface{}) *UnifiedResource {
	return &UnifiedResource{
		id:           id,
		apiType:      apiType,
		deviceID:     deviceID,
		priority:     priority,
		nativeHandle: nativeHandle,
		state:        ExecutionStateIdle,
		createdAt:    time.Now(),
		lastUsed:     time.Now(),
	}
}

func (r *UnifiedResource) GetID() string {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.id
}

func (r *UnifiedResource) GetAPIType() APIType {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.apiType
}

func (r *UnifiedResource) GetDeviceID() int {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.deviceID
}

func (r *UnifiedResource) GetPriority() UnifiedPriority {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.priority
}

func (r *UnifiedResource) GetNativeHandle() interface{} {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.nativeHandle
}

func (r *UnifiedResource) GetState() ExecutionState {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.state
}

func (r *UnifiedResource) SetState(state ExecutionState) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.state = state
}

func (r *UnifiedResource) IsInUse() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.inUse
}

func (r *UnifiedResource) SetInUse(inUse bool) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.inUse = inUse
	if inUse {
		r.lastUsed = time.Now()
	}
}

func (r *UnifiedResource) GetLastUsed() time.Time {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.lastUsed
}

// CrossAPISyncManager coordinates synchronization across all GPU APIs
type CrossAPISyncManager struct {
	// Resource management
	resources map[string]*UnifiedResource // key: resource_id
	events    map[string]*SyncEvent       // key: event_id

	// API-specific managers (using interfaces to avoid build constraint issues)
	cudaManager   StreamManagerInterface
	openclManager QueueManagerInterface
	metalManager  BufferManagerInterface

	// Statistics
	totalEvents         int64
	totalResources      int64
	totalSyncs          int64
	totalErrors         int64
	avgSyncTime         time.Duration
	maxConcurrentEvents int64

	// Configuration
	maxEvents         int
	syncTimeout       time.Duration
	enableCrossDevice bool
	enableCrossAPI    bool
	logger            *log.Logger

	// Thread safety
	mu sync.RWMutex
}

// NewCrossAPISyncManager creates a new cross-API synchronization manager
func NewCrossAPISyncManager(logger *log.Logger) *CrossAPISyncManager {
	return &CrossAPISyncManager{
		resources:         make(map[string]*UnifiedResource),
		events:            make(map[string]*SyncEvent),
		maxEvents:         10000,
		syncTimeout:       30 * time.Second,
		enableCrossDevice: true,
		enableCrossAPI:    true,
		logger:            logger,
	}
}

// Initialize initializes the cross-API sync manager with the API-specific managers
func (sm *CrossAPISyncManager) Initialize(
	cudaManager StreamManagerInterface,
	openclManager QueueManagerInterface,
	metalManager BufferManagerInterface) error {

	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.cudaManager = cudaManager
	sm.openclManager = openclManager
	sm.metalManager = metalManager

	sm.logger.Printf("Cross-API Sync Manager initialized with CUDA: %v, OpenCL: %v, Metal: %v",
		cudaManager != nil, openclManager != nil, metalManager != nil)
	return nil
}

// RegisterResource registers a unified resource with the manager
func (sm *CrossAPISyncManager) RegisterResource(resource *UnifiedResource) error {
	if resource == nil {
		return fmt.Errorf("cannot register nil resource")
	}

	sm.mu.Lock()
	defer sm.mu.Unlock()

	if _, exists := sm.resources[resource.GetID()]; exists {
		return fmt.Errorf("resource with ID %s already registered", resource.GetID())
	}

	sm.resources[resource.GetID()] = resource
	atomic.AddInt64(&sm.totalResources, 1)
	sm.logger.Printf("Registered resource %s (API: %s, Device: %d)",
		resource.GetID(), resource.GetAPIType().String(), resource.GetDeviceID())
	return nil
}

// UnregisterResource removes a resource from the manager
func (sm *CrossAPISyncManager) UnregisterResource(resourceID string) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	resource, exists := sm.resources[resourceID]
	if !exists {
		return fmt.Errorf("resource with ID %s not found", resourceID)
	}

	if resource.IsInUse() {
		return fmt.Errorf("cannot unregister resource %s: still in use", resourceID)
	}

	delete(sm.resources, resourceID)
	sm.logger.Printf("Unregistered resource %s", resourceID)
	return nil
}

// CreateEvent creates a new synchronization event
func (sm *CrossAPISyncManager) CreateEvent(id string, apiType APIType, deviceID int, eventType EventType) (*SyncEvent, error) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if _, exists := sm.events[id]; exists {
		return nil, fmt.Errorf("event with ID %s already exists", id)
	}

	if len(sm.events) >= sm.maxEvents {
		return nil, fmt.Errorf("maximum number of events (%d) reached", sm.maxEvents)
	}

	event := NewSyncEvent(id, apiType, deviceID, eventType)
	sm.events[id] = event
	atomic.AddInt64(&sm.totalEvents, 1)

	sm.logger.Printf("Created event %s (API: %s, Device: %d, Type: %s)",
		id, apiType.String(), deviceID, eventType.String())
	return event, nil
}

// WaitForEvent waits for a single event to complete
func (sm *CrossAPISyncManager) WaitForEvent(eventID string) error {
	sm.mu.RLock()
	event, exists := sm.events[eventID]
	sm.mu.RUnlock()

	if !exists {
		return fmt.Errorf("event with ID %s not found", eventID)
	}

	return sm.waitForEventCompletion(event)
}

// WaitForEvents waits for multiple events to complete
func (sm *CrossAPISyncManager) WaitForEvents(eventIDs []string) error {
	events := make([]*SyncEvent, 0, len(eventIDs))

	sm.mu.RLock()
	for _, eventID := range eventIDs {
		if event, exists := sm.events[eventID]; exists {
			events = append(events, event)
		} else {
			sm.mu.RUnlock()
			return fmt.Errorf("event with ID %s not found", eventID)
		}
	}
	sm.mu.RUnlock()

	// Wait for all events concurrently
	errChan := make(chan error, len(events))
	for _, event := range events {
		go func(e *SyncEvent) {
			errChan <- sm.waitForEventCompletion(e)
		}(event)
	}

	// Collect results
	for i := 0; i < len(events); i++ {
		if err := <-errChan; err != nil {
			return err
		}
	}

	return nil
}

// CreateBarrier creates a barrier event that waits for multiple events
func (sm *CrossAPISyncManager) CreateBarrier(barrierID string, waitEvents []string) (*SyncEvent, error) {
	barrier, err := sm.CreateEvent(barrierID, APICuda, 0, EventTypeBarrier)
	if err != nil {
		return nil, err
	}

	// Add dependencies on all wait events
	sm.mu.RLock()
	for _, eventID := range waitEvents {
		if event, exists := sm.events[eventID]; exists {
			barrier.AddDependency(event)
		} else {
			sm.mu.RUnlock()
			return nil, fmt.Errorf("wait event %s not found", eventID)
		}
	}
	sm.mu.RUnlock()

	sm.logger.Printf("Created barrier %s waiting for %d events", barrierID, len(waitEvents))
	return barrier, nil
}

// AcquireUnifiedResource acquires a resource based on preferences
func (sm *CrossAPISyncManager) AcquireUnifiedResource(apiType APIType, deviceID int, priority UnifiedPriority) (*UnifiedResource, error) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	var bestResource *UnifiedResource
	bestScore := -1

	for _, resource := range sm.resources {
		if resource.IsInUse() {
			continue
		}

		score := sm.calculateResourceScore(resource, apiType, deviceID, priority)
		if score > bestScore {
			bestScore = score
			bestResource = resource
		}
	}

	if bestResource == nil {
		return nil, fmt.Errorf("no available resource found")
	}

	bestResource.SetInUse(true)
	sm.logger.Printf("Acquired resource %s (score: %d)", bestResource.GetID(), bestScore)
	return bestResource, nil
}

// ReleaseUnifiedResource releases a resource back to the pool
func (sm *CrossAPISyncManager) ReleaseUnifiedResource(resourceID string) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	resource, exists := sm.resources[resourceID]
	if !exists {
		return fmt.Errorf("resource with ID %s not found", resourceID)
	}

	if !resource.IsInUse() {
		return fmt.Errorf("resource %s is not in use", resourceID)
	}

	resource.SetInUse(false)
	resource.SetState(ExecutionStateIdle)
	sm.logger.Printf("Released resource %s", resourceID)
	return nil
}

// SynchronizeAcrossAPIs creates cross-API synchronization points
func (sm *CrossAPISyncManager) SynchronizeAcrossAPIs(syncID string, apiEvents map[APIType][]string) error {
	if !sm.enableCrossAPI {
		return fmt.Errorf("cross-API synchronization is disabled")
	}

	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		sm.updateAvgSyncTime(duration)
		atomic.AddInt64(&sm.totalSyncs, 1)
	}()

	// Collect all event IDs
	var allEventIDs []string
	for _, eventIDs := range apiEvents {
		allEventIDs = append(allEventIDs, eventIDs...)
	}

	// Wait for all events
	err := sm.WaitForEvents(allEventIDs)
	if err != nil {
		atomic.AddInt64(&sm.totalErrors, 1)
		return fmt.Errorf("cross-API synchronization failed: %w", err)
	}

	sm.logger.Printf("Created cross-API synchronization %s with %d events", syncID, len(allEventIDs))
	return nil
}

// GetManagerStats returns statistics about the manager
func (sm *CrossAPISyncManager) GetManagerStats() map[string]interface{} {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	// Count resources by API
	apiCounts := map[string]int{
		"CUDA":   0,
		"OpenCL": 0,
		"Metal":  0,
	}

	for _, resource := range sm.resources {
		apiCounts[resource.GetAPIType().String()]++
	}

	// Count events by state
	stateCounts := map[string]int{
		"idle":      0,
		"pending":   0,
		"running":   0,
		"completed": 0,
		"error":     0,
	}

	for _, event := range sm.events {
		switch event.GetState() {
		case ExecutionStateIdle:
			stateCounts["idle"]++
		case ExecutionStatePending:
			stateCounts["pending"]++
		case ExecutionStateRunning:
			stateCounts["running"]++
		case ExecutionStateCompleted:
			stateCounts["completed"]++
		case ExecutionStateError:
			stateCounts["error"]++
		}
	}

	return map[string]interface{}{
		"total_resources":       sm.totalResources,
		"total_events":          sm.totalEvents,
		"total_syncs":           sm.totalSyncs,
		"total_errors":          sm.totalErrors,
		"avg_sync_time_ms":      sm.avgSyncTime.Milliseconds(),
		"max_concurrent_events": sm.maxConcurrentEvents,
		"active_resources":      len(sm.resources),
		"active_events":         len(sm.events),
		"resources_by_api":      apiCounts,
		"events_by_state":       stateCounts,
		"cross_device_enabled":  sm.enableCrossDevice,
		"cross_api_enabled":     sm.enableCrossAPI,
		"max_events":            sm.maxEvents,
		"sync_timeout_ms":       sm.syncTimeout.Milliseconds(),
	}
}

// Cleanup releases all resources and clears all events
func (sm *CrossAPISyncManager) Cleanup() error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	// Release all resources
	for _, resource := range sm.resources {
		resource.SetInUse(false)
		resource.SetState(ExecutionStateIdle)
	}

	// Clear all events
	for _, event := range sm.events {
		event.SetState(ExecutionStateError)
	}

	sm.resources = make(map[string]*UnifiedResource)
	sm.events = make(map[string]*SyncEvent)

	sm.logger.Printf("Cross-API Sync Manager cleaned up")
	return nil
}

// Private helper methods

func (sm *CrossAPISyncManager) waitForEventCompletion(event *SyncEvent) error {
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		sm.updateAvgSyncTime(duration)
	}()

	timeout := time.NewTimer(sm.syncTimeout)
	defer timeout.Stop()

	ticker := time.NewTicker(10 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-timeout.C:
			atomic.AddInt64(&sm.totalErrors, 1)
			return fmt.Errorf("timeout waiting for event %s", event.GetID())

		case <-ticker.C:
			if event.IsReady() {
				if event.GetState() == ExecutionStatePending {
					event.SetState(ExecutionStateRunning)
					// Simulate execution
					time.Sleep(time.Millisecond)
					event.SetState(ExecutionStateCompleted)
				}
				return nil
			}
		}
	}
}

func (sm *CrossAPISyncManager) calculateResourceScore(resource *UnifiedResource, preferredAPI APIType, deviceID int, priority UnifiedPriority) int {
	score := 0

	// Prefer exact API match
	if resource.GetAPIType() == preferredAPI {
		score += 100
	}

	// Prefer exact device match
	if resource.GetDeviceID() == deviceID {
		score += 50
	}

	// Prefer higher priority resources for high priority requests
	if priority == UnifiedPriorityHigh && resource.GetPriority() == UnifiedPriorityHigh {
		score += 30
	} else if priority == UnifiedPriorityNormal && resource.GetPriority() != UnifiedPriorityLow {
		score += 20
	}

	// Prefer recently used resources (cache locality)
	age := time.Since(resource.GetLastUsed())
	if age < time.Minute {
		score += 10
	}

	return score
}

func (sm *CrossAPISyncManager) updateAvgSyncTime(duration time.Duration) {
	// Simple moving average update
	sm.avgSyncTime = (sm.avgSyncTime + duration) / 2
}

// Helper functions for converting between API-specific types and unified types
// These will use compile-time checks to see if types are available

// Priority conversion functions with fallback implementations

// ConvertCudaPriority converts CUDA stream priority to unified priority
func ConvertCudaPriority(priority interface{}) UnifiedPriority {
	// Since StreamPriority may not be available due to build constraints,
	// we use interface{} and do type checking at runtime
	if p, ok := priority.(int); ok {
		switch p {
		case 0: // StreamPriorityLow
			return UnifiedPriorityLow
		case 2: // StreamPriorityHigh
			return UnifiedPriorityHigh
		default: // StreamPriorityNormal
			return UnifiedPriorityNormal
		}
	}
	return UnifiedPriorityNormal
}

// ConvertOpenCLPriority converts OpenCL queue priority to unified priority
func ConvertOpenCLPriority(priority interface{}) UnifiedPriority {
	if p, ok := priority.(int); ok {
		switch p {
		case 0: // QueuePriorityLow
			return UnifiedPriorityLow
		case 2: // QueuePriorityHigh
			return UnifiedPriorityHigh
		default: // QueuePriorityNormal
			return UnifiedPriorityNormal
		}
	}
	return UnifiedPriorityNormal
}

// ConvertMetalPriority converts Metal buffer priority to unified priority
func ConvertMetalPriority(metalPriority BufferPriority) UnifiedPriority {
	switch metalPriority {
	case BufferPriorityLow:
		return UnifiedPriorityLow
	case BufferPriorityHigh:
		return UnifiedPriorityHigh
	default:
		return UnifiedPriorityNormal
	}
}

// ConvertToAPIPriority converts unified priority to API-specific priority
func ConvertToAPIPriority(unified UnifiedPriority, apiType APIType) interface{} {
	switch apiType {
	case APICuda:
		switch unified {
		case UnifiedPriorityLow:
			return 0 // StreamPriorityLow
		case UnifiedPriorityHigh:
			return 2 // StreamPriorityHigh
		default:
			return 1 // StreamPriorityNormal
		}
	case APIOpenCL:
		switch unified {
		case UnifiedPriorityLow:
			return 0 // QueuePriorityLow
		case UnifiedPriorityHigh:
			return 2 // QueuePriorityHigh
		default:
			return 1 // QueuePriorityNormal
		}
	case APIMetal:
		switch unified {
		case UnifiedPriorityLow:
			return BufferPriorityLow
		case UnifiedPriorityHigh:
			return BufferPriorityHigh
		default:
			return BufferPriorityNormal
		}
	default:
		return nil
	}
}
