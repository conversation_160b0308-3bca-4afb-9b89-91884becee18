package gpu

import (
	"math"
	"testing"
	"unsafe"
)

func TestQuantizationEngine_NewQuantizationEngine(t *testing.T) {
	config := QuantizationConfig{
		Method:                 QuantizationSymmetric,
		TargetType:             TensorInt8,
		UseCalibration:         true,
		PreserveAccuracy:       true,
		PerChannelQuantization: false,
	}

	engine := NewQuantizationEngine(config)

	if engine == nil {
		t.Fatal("Expected non-nil quantization engine")
	}

	if engine.config.Method != QuantizationSymmetric {
		t.<PERSON><PERSON>("Expected method %s, got %s", QuantizationSymmetric, engine.config.Method)
	}

	if engine.config.TargetType != TensorInt8 {
		t.Errorf("Expected target type %s, got %s", TensorInt8, engine.config.TargetType)
	}

	if len(engine.calibrationData) != 0 {
		t.Errorf("Expected empty calibration data, got %d items", len(engine.calibrationData))
	}

	if len(engine.quantizationCache) != 0 {
		t.<PERSON><PERSON><PERSON>("Expected empty quantization cache, got %d items", len(engine.quantizationCache))
	}
}

func TestQuantizationEngine_AddCalibrationData(t *testing.T) {
	config := QuantizationConfig{
		Method:     QuantizationSymmetric,
		TargetType: TensorInt8,
	}
	engine := NewQuantizationEngine(config)

	// Test with valid CPU tensor
	tensor, err := NewTensor(TensorShape{10, 10}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	err = engine.AddCalibrationData(tensor)
	if err != nil {
		t.Errorf("Failed to add calibration data: %v", err)
	}

	if engine.GetCalibrationDataset().GetSampleCount() != 1 {
		t.Errorf("Expected 1 calibration tensor, got %d", engine.GetCalibrationDataset().GetSampleCount())
	}

	// Test with nil tensor
	err = engine.AddCalibrationData(nil)
	if err == nil {
		t.Error("Expected error for nil tensor")
	}

	// Test clear calibration data
	engine.ClearCalibrationData()
	if engine.GetCalibrationDataset().GetSampleCount() != 0 {
		t.Errorf("Expected empty calibration data after clear, got %d items", engine.GetCalibrationDataset().GetSampleCount())
	}
}

func TestQuantizationEngine_SymmetricQuantization(t *testing.T) {
	config := QuantizationConfig{
		Method:     QuantizationSymmetric,
		TargetType: TensorInt8,
	}
	engine := NewQuantizationEngine(config)

	// Create test tensor with known values
	tensor, err := NewTensor(TensorShape{4}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with test values: [-2.0, -1.0, 1.0, 2.0]
	testValues := []float32{-2.0, -1.0, 1.0, 2.0}
	for i, val := range testValues {
		err = tensor.SetFloat32(val, int64(i))
		if err != nil {
			t.Fatalf("Failed to set tensor value: %v", err)
		}
	}

	// Compute quantization parameters
	params, err := engine.ComputeQuantizationParams(tensor)
	if err != nil {
		t.Fatalf("Failed to compute quantization parameters: %v", err)
	}

	// For symmetric quantization, zero-point should be 0
	if params.ZeroPoint != 0 {
		t.Errorf("Expected zero-point 0 for symmetric quantization, got %d", params.ZeroPoint)
	}

	// Scale should be maxAbs / qmax = 2.0 / 127
	expectedScale := float32(2.0 / 127.0)
	if math.Abs(float64(params.Scale-expectedScale)) > 1e-6 {
		t.Errorf("Expected scale %f, got %f", expectedScale, params.Scale)
	}

	// Test quantization
	quantizedTensor, err := engine.QuantizeTensor(tensor)
	if err != nil {
		t.Fatalf("Failed to quantize tensor: %v", err)
	}
	defer quantizedTensor.Free()

	// Verify quantized values
	expectedQuantized := []int8{-127, -64, 64, 127}
	for i, expected := range expectedQuantized {
		val, err := quantizedTensor.GetInt8(int64(i))
		if err != nil {
			t.Fatalf("Failed to get quantized value: %v", err)
		}

		// Allow some tolerance for rounding
		if math.Abs(float64(val-expected)) > 1 {
			t.Errorf("Expected quantized value %d, got %d at index %d", expected, val, i)
		}
	}

	// Test dequantization
	dequantizedTensor, err := engine.DequantizeTensor(quantizedTensor, params)
	if err != nil {
		t.Fatalf("Failed to dequantize tensor: %v", err)
	}
	defer dequantizedTensor.Free()

	// Verify dequantized values are close to original
	for i, originalVal := range testValues {
		val, err := dequantizedTensor.GetFloat32(int64(i))
		if err != nil {
			t.Fatalf("Failed to get dequantized value: %v", err)
		}

		// Allow tolerance for quantization error
		if math.Abs(float64(val-originalVal)) > 0.1 {
			t.Errorf("Expected dequantized value %f, got %f at index %d", originalVal, val, i)
		}
	}
}

func TestQuantizationEngine_AsymmetricQuantization(t *testing.T) {
	config := QuantizationConfig{
		Method:     QuantizationAsymmetric,
		TargetType: TensorInt8,
	}
	engine := NewQuantizationEngine(config)

	// Create test tensor with asymmetric range [0.0, 3.0]
	tensor, err := NewTensor(TensorShape{4}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with test values: [0.0, 1.0, 2.0, 3.0]
	testValues := []float32{0.0, 1.0, 2.0, 3.0}
	for i, val := range testValues {
		err = tensor.SetFloat32(val, int64(i))
		if err != nil {
			t.Fatalf("Failed to set tensor value: %v", err)
		}
	}

	// Compute quantization parameters
	params, err := engine.ComputeQuantizationParams(tensor)
	if err != nil {
		t.Fatalf("Failed to compute quantization parameters: %v", err)
	}

	// Verify min/max values
	if params.MinValue != 0.0 {
		t.Errorf("Expected min value 0.0, got %f", params.MinValue)
	}
	if params.MaxValue != 3.0 {
		t.Errorf("Expected max value 3.0, got %f", params.MaxValue)
	}

	// Scale should be (maxVal - minVal) / (qmax - qmin) = 3.0 / 255
	expectedScale := float32(3.0 / 255.0)
	if math.Abs(float64(params.Scale-expectedScale)) > 1e-6 {
		t.Errorf("Expected scale %f, got %f", expectedScale, params.Scale)
	}

	// Test quantization and dequantization
	quantizedTensor, err := engine.QuantizeTensor(tensor)
	if err != nil {
		t.Fatalf("Failed to quantize tensor: %v", err)
	}
	defer quantizedTensor.Free()

	dequantizedTensor, err := engine.DequantizeTensor(quantizedTensor, params)
	if err != nil {
		t.Fatalf("Failed to dequantize tensor: %v", err)
	}
	defer dequantizedTensor.Free()

	// Verify dequantized values are close to original
	for i, originalVal := range testValues {
		val, err := dequantizedTensor.GetFloat32(int64(i))
		if err != nil {
			t.Fatalf("Failed to get dequantized value: %v", err)
		}

		// Allow tolerance for quantization error
		if math.Abs(float64(val-originalVal)) > 0.1 {
			t.Errorf("Expected dequantized value %f, got %f at index %d", originalVal, val, i)
		}
	}
}

func TestQuantizationEngine_INT4Quantization(t *testing.T) {
	config := QuantizationConfig{
		Method:     QuantizationSymmetric,
		TargetType: TensorInt4,
	}
	engine := NewQuantizationEngine(config)

	// Create test tensor
	tensor, err := NewTensor(TensorShape{4}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with values in INT4 range: [-1.4, -0.7, 0.7, 1.4]
	testValues := []float32{-1.4, -0.7, 0.7, 1.4}
	for i, val := range testValues {
		err = tensor.SetFloat32(val, int64(i))
		if err != nil {
			t.Fatalf("Failed to set tensor value: %v", err)
		}
	}

	// Test quantization
	quantizedTensor, err := engine.QuantizeTensor(tensor)
	if err != nil {
		t.Fatalf("Failed to quantize tensor: %v", err)
	}
	defer quantizedTensor.Free()

	// Verify tensor type is INT4
	if quantizedTensor.dtype != TensorInt4 {
		t.Errorf("Expected tensor type INT4, got %s", quantizedTensor.dtype)
	}

	// Compute quantization parameters for dequantization
	params, err := engine.ComputeQuantizationParams(tensor)
	if err != nil {
		t.Fatalf("Failed to compute quantization parameters: %v", err)
	}

	// Test dequantization
	dequantizedTensor, err := engine.DequantizeTensor(quantizedTensor, params)
	if err != nil {
		t.Fatalf("Failed to dequantize tensor: %v", err)
	}
	defer dequantizedTensor.Free()

	// Verify dequantized values are reasonably close to original
	for i, originalVal := range testValues {
		val, err := dequantizedTensor.GetFloat32(int64(i))
		if err != nil {
			t.Fatalf("Failed to get dequantized value: %v", err)
		}

		// INT4 has limited precision, so allow larger tolerance
		if math.Abs(float64(val-originalVal)) > 0.3 {
			t.Errorf("Expected dequantized value %f, got %f at index %d", originalVal, val, i)
		}
	}
}

func TestQuantizationEngine_PerChannelQuantization(t *testing.T) {
	config := QuantizationConfig{
		Method:     QuantizationPerChannel,
		TargetType: TensorInt8,
	}
	engine := NewQuantizationEngine(config)

	// Create 2D tensor (2 channels)
	tensor, err := NewTensor(TensorShape{2, 4}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with test values
	testValues := [][]float32{
		{-2.0, -1.0, 1.0, 2.0}, // Channel 0
		{-4.0, -2.0, 2.0, 4.0}, // Channel 1
	}

	for i, channel := range testValues {
		for j, val := range channel {
			err = tensor.SetFloat32(val, int64(i), int64(j))
			if err != nil {
				t.Fatalf("Failed to set tensor value: %v", err)
			}
		}
	}

	// Test quantization
	quantizedTensor, err := engine.QuantizeTensor(tensor)
	if err != nil {
		t.Fatalf("Failed to quantize tensor: %v", err)
	}
	defer quantizedTensor.Free()

	// Compute quantization parameters
	params, err := engine.ComputeQuantizationParams(tensor)
	if err != nil {
		t.Fatalf("Failed to compute quantization parameters: %v", err)
	}

	// Test dequantization
	dequantizedTensor, err := engine.DequantizeTensor(quantizedTensor, params)
	if err != nil {
		t.Fatalf("Failed to dequantize tensor: %v", err)
	}
	defer dequantizedTensor.Free()

	// Verify shape is preserved
	if !compareTensorShapes(tensor.shape, dequantizedTensor.shape) {
		t.Errorf("Shape mismatch: original %v, dequantized %v", tensor.shape, dequantizedTensor.shape)
	}
}

func TestQuantizationEngine_LearnedQuantization(t *testing.T) {
	config := QuantizationConfig{
		Method:         QuantizationLearned,
		TargetType:     TensorInt8,
		UseCalibration: true,
	}
	engine := NewQuantizationEngine(config)

	// Add calibration data
	calibTensor, err := NewTensor(TensorShape{10}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create calibration tensor: %v", err)
	}
	defer calibTensor.Free()

	// Fill calibration tensor with random values
	for i := int64(0); i < 10; i++ {
		val := float32(i) * 0.1
		err = calibTensor.SetFloat32(val, i)
		if err != nil {
			t.Fatalf("Failed to set calibration value: %v", err)
		}
	}

	err = engine.AddCalibrationData(calibTensor)
	if err != nil {
		t.Fatalf("Failed to add calibration data: %v", err)
	}

	// Test tensor
	tensor, err := NewTensor(TensorShape{4}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	testValues := []float32{-1.0, -0.5, 0.5, 1.0}
	for i, val := range testValues {
		err = tensor.SetFloat32(val, int64(i))
		if err != nil {
			t.Fatalf("Failed to set tensor value: %v", err)
		}
	}

	// Test quantization with learned parameters
	quantizedTensor, err := engine.QuantizeTensor(tensor)
	if err != nil {
		t.Fatalf("Failed to quantize tensor: %v", err)
	}
	defer quantizedTensor.Free()

	// Verify quantization succeeded
	if quantizedTensor.dtype != TensorInt8 {
		t.Errorf("Expected tensor type INT8, got %s", quantizedTensor.dtype)
	}
}

func TestQuantizationEngine_AdaptiveQuantization(t *testing.T) {
	config := QuantizationConfig{
		Method:     QuantizationAdaptive,
		TargetType: TensorInt8,
	}
	engine := NewQuantizationEngine(config)

	// Create test tensor
	tensor, err := NewTensor(TensorShape{4}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	testValues := []float32{-2.0, -1.0, 1.0, 2.0}
	for i, val := range testValues {
		err = tensor.SetFloat32(val, int64(i))
		if err != nil {
			t.Fatalf("Failed to set tensor value: %v", err)
		}
	}

	// Test adaptive quantization
	params, err := engine.ComputeQuantizationParams(tensor)
	if err != nil {
		t.Fatalf("Failed to compute adaptive quantization parameters: %v", err)
	}

	// Verify parameters are reasonable
	if params.Scale <= 0 {
		t.Errorf("Expected positive scale, got %f", params.Scale)
	}

	// Test quantization
	quantizedTensor, err := engine.QuantizeTensor(tensor)
	if err != nil {
		t.Fatalf("Failed to quantize tensor: %v", err)
	}
	defer quantizedTensor.Free()

	// Verify quantization succeeded
	if quantizedTensor.dtype != TensorInt8 {
		t.Errorf("Expected tensor type INT8, got %s", quantizedTensor.dtype)
	}
}

func TestQuantizationEngine_ParameterCaching(t *testing.T) {
	config := QuantizationConfig{
		Method:     QuantizationSymmetric,
		TargetType: TensorInt8,
	}
	engine := NewQuantizationEngine(config)

	// Create test tensor
	tensor, err := NewTensor(TensorShape{4}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	testValues := []float32{-1.0, -0.5, 0.5, 1.0}
	for i, val := range testValues {
		err = tensor.SetFloat32(val, int64(i))
		if err != nil {
			t.Fatalf("Failed to set tensor value: %v", err)
		}
	}

	// First computation should cache parameters
	params1, err := engine.ComputeQuantizationParams(tensor)
	if err != nil {
		t.Fatalf("Failed to compute quantization parameters: %v", err)
	}

	// Verify cache has one entry
	if len(engine.quantizationCache) != 1 {
		t.Errorf("Expected 1 cached parameter set, got %d", len(engine.quantizationCache))
	}

	// Second computation should use cached parameters
	params2, err := engine.ComputeQuantizationParams(tensor)
	if err != nil {
		t.Fatalf("Failed to compute quantization parameters: %v", err)
	}

	// Verify parameters are identical (from cache)
	if params1.Scale != params2.Scale {
		t.Errorf("Cached parameters differ: scale %f vs %f", params1.Scale, params2.Scale)
	}
	if params1.ZeroPoint != params2.ZeroPoint {
		t.Errorf("Cached parameters differ: zero-point %d vs %d", params1.ZeroPoint, params2.ZeroPoint)
	}
}

func TestQuantizationEngine_GetQuantizationInfo(t *testing.T) {
	config := QuantizationConfig{
		Method:                 QuantizationAsymmetric,
		TargetType:             TensorInt4,
		UseCalibration:         true,
		PreserveAccuracy:       true,
		PerChannelQuantization: true,
	}
	engine := NewQuantizationEngine(config)

	info := engine.GetQuantizationInfo()

	if info["method"] != "asymmetric" {
		t.Errorf("Expected method 'asymmetric', got '%v'", info["method"])
	}

	if info["target_type"] != "int4" {
		t.Errorf("Expected target_type 'int4', got '%v'", info["target_type"])
	}

	if info["use_calibration"] != true {
		t.Errorf("Expected use_calibration true, got %v", info["use_calibration"])
	}

	if info["preserve_accuracy"] != true {
		t.Errorf("Expected preserve_accuracy true, got %v", info["preserve_accuracy"])
	}

	if info["per_channel"] != true {
		t.Errorf("Expected per_channel true, got %v", info["per_channel"])
	}

	if info["calibration_samples"] != 0 {
		t.Errorf("Expected 0 calibration_samples, got %v", info["calibration_samples"])
	}

	if info["cached_params"] != 0 {
		t.Errorf("Expected 0 cached_params, got %v", info["cached_params"])
	}
}

func TestQuantizationEngine_ErrorHandling(t *testing.T) {
	config := QuantizationConfig{
		Method:     QuantizationSymmetric,
		TargetType: TensorInt8,
	}
	engine := NewQuantizationEngine(config)

	// Test with nil tensor
	_, err := engine.ComputeQuantizationParams(nil)
	if err == nil {
		t.Error("Expected error for nil tensor")
	}

	// Test quantization with nil tensor
	_, err = engine.QuantizeTensor(nil)
	if err == nil {
		t.Error("Expected error for nil tensor")
	}

	// Test dequantization with nil tensor
	params := QuantizationParams{Scale: 1.0, ZeroPoint: 0}
	_, err = engine.DequantizeTensor(nil, params)
	if err == nil {
		t.Error("Expected error for nil tensor")
	}
}

// Helper function to compare tensor shapes
func compareTensorShapes(shape1, shape2 TensorShape) bool {
	if len(shape1) != len(shape2) {
		return false
	}
	for i, dim := range shape1 {
		if dim != shape2[i] {
			return false
		}
	}
	return true
}

// Benchmark tests
func BenchmarkQuantizationEngine_SymmetricQuantization(b *testing.B) {
	config := QuantizationConfig{
		Method:     QuantizationSymmetric,
		TargetType: TensorInt8,
	}
	engine := NewQuantizationEngine(config)

	// Create test tensor
	tensor, err := NewTensor(TensorShape{100, 100}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		b.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with random values
	for i := int64(0); i < tensor.NumElements(); i++ {
		val := float32(i%200-100) * 0.01 // Values from -1.0 to 1.0
		byteOffset := i * int64(tensor.dtype.Size())
		ptr := unsafe.Pointer(uintptr(tensor.Data()) + uintptr(byteOffset))
		*(*float32)(ptr) = val
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		quantizedTensor, err := engine.QuantizeTensor(tensor)
		if err != nil {
			b.Fatalf("Failed to quantize tensor: %v", err)
		}
		quantizedTensor.Free()
	}
}

func BenchmarkQuantizationEngine_INT4Quantization(b *testing.B) {
	config := QuantizationConfig{
		Method:     QuantizationSymmetric,
		TargetType: TensorInt4,
	}
	engine := NewQuantizationEngine(config)

	// Create test tensor
	tensor, err := NewTensor(TensorShape{100, 100}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		b.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with random values
	for i := int64(0); i < tensor.NumElements(); i++ {
		val := float32(i%16-8) * 0.1 // Values from -0.8 to 0.8
		byteOffset := i * int64(tensor.dtype.Size())
		ptr := unsafe.Pointer(uintptr(tensor.Data()) + uintptr(byteOffset))
		*(*float32)(ptr) = val
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		quantizedTensor, err := engine.QuantizeTensor(tensor)
		if err != nil {
			b.Fatalf("Failed to quantize tensor: %v", err)
		}
		quantizedTensor.Free()
	}
}

func BenchmarkQuantizationEngine_Dequantization(b *testing.B) {
	config := QuantizationConfig{
		Method:     QuantizationSymmetric,
		TargetType: TensorInt8,
	}
	engine := NewQuantizationEngine(config)

	// Create and quantize test tensor
	tensor, err := NewTensor(TensorShape{100, 100}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		b.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with test values
	for i := int64(0); i < tensor.NumElements(); i++ {
		val := float32(i%200-100) * 0.01
		byteOffset := i * int64(tensor.dtype.Size())
		ptr := unsafe.Pointer(uintptr(tensor.Data()) + uintptr(byteOffset))
		*(*float32)(ptr) = val
	}

	quantizedTensor, err := engine.QuantizeTensor(tensor)
	if err != nil {
		b.Fatalf("Failed to quantize tensor: %v", err)
	}
	defer quantizedTensor.Free()

	params, err := engine.ComputeQuantizationParams(tensor)
	if err != nil {
		b.Fatalf("Failed to compute quantization parameters: %v", err)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		dequantizedTensor, err := engine.DequantizeTensor(quantizedTensor, params)
		if err != nil {
			b.Fatalf("Failed to dequantize tensor: %v", err)
		}
		dequantizedTensor.Free()
	}
}
