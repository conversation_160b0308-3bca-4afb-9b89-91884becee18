package gpu

import (
	"log"
	"os"
	"testing"
	"time"
)

// TestClusterFaultToleranceCreation tests that we can create the main components
func TestClusterFaultToleranceCreation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	// Test HeartbeatManager creation
	config := HeartbeatConfig{
		BaseInterval:        time.Second,
		MaxInterval:         10 * time.Second,
		MinInterval:         100 * time.Millisecond,
		AdaptiveEnabled:     true,
		PhiThreshold:        8.0,
		IntervalHistorySize: 10,
		JitterTolerance:     0.1,
		MissedBeatThreshold: 3,
	}

	hbManager := NewHeartbeatManager(config, logger)
	if hbManager == nil {
		t.Fatal("Failed to create HeartbeatManager")
	}

	// Test PhiAccrualDetector creation
	phiDetector := NewPhiAccrualDetector(8.0, 10)
	if phiDetector == nil {
		t.<PERSON><PERSON>("Failed to create PhiAccrualDetector")
	}

	// Test CheckpointStorage creation
	storage, err := NewFileCheckpointStorage("test_checkpoints")
	if err != nil {
		t.Fatalf("Failed to create CheckpointStorage: %v", err)
	}

	// Test CheckpointManager creation
	checkpointConfig := CheckpointConfig{
		Enabled:             true,
		Interval:            5 * time.Second,
		MaxVersions:         10,
		CompressionEnabled:  true,
		IncrementalEnabled:  true,
		ReplicationFactor:   1,
		StoragePath:         "test_checkpoints",
		MaxCheckpointSize:   1024 * 1024, // 1MB
		VerificationEnabled: true,
	}

	checkpointManager := NewCheckpointManager(storage, checkpointConfig, logger)
	if checkpointManager == nil {
		t.Fatal("Failed to create CheckpointManager")
	}

	// Test PartialResultAggregator creation
	resultConfig := ResultAggregationConfig{
		Enabled:            true,
		TimeoutDuration:    30 * time.Second,
		ValidationEnabled:  true,
		MaxResultSize:      10 * 1024 * 1024, // 10MB
		CompressionEnabled: true,
		PersistenceEnabled: true,
		ReplicationFactor:  1,
	}

	resultAggregator := NewPartialResultAggregator(resultConfig, logger)
	if resultAggregator == nil {
		t.Fatal("Failed to create PartialResultAggregator")
	}

	// Test TaskMigrationCoordinator creation
	migrationConfig := MigrationConfig{
		Enabled:                 true,
		MaxConcurrentMigrations: 5,
		TimeoutDuration:         60 * time.Second,
		RetryAttempts:           3,
		VerificationEnabled:     true,
		PreemptiveEnabled:       false,
		LoadBalancingEnabled:    true,
	}

	migrationCoordinator := NewTaskMigrationCoordinator(migrationConfig, logger)
	if migrationCoordinator == nil {
		t.Fatal("Failed to create TaskMigrationCoordinator")
	}

	t.Log("All fault tolerance components created successfully")
}

// TestCheckpointStorageBasic tests basic checkpoint storage functionality
func TestCheckpointStorageBasic(t *testing.T) {
	storage, err := NewFileCheckpointStorage("test_checkpoints")
	if err != nil {
		t.Fatalf("Failed to create checkpoint storage: %v", err)
	}

	// Test save and load
	checkpoint := &Checkpoint{
		TaskID:    "test-task-1",
		Version:   1,
		Timestamp: time.Now(),
		NodeID:    "test-node-1",
		State: CheckpointState{
			Progress:       50.0,
			Phase:          "processing",
			IterationCount: 100,
			ElapsedTime:    time.Minute,
			Variables:      map[string]interface{}{"key": "value"},
		},
		Data:           []byte("test data"),
		Incremental:    false,
		Hash:           "test-hash",
		CompressedSize: 9,
		OriginalSize:   9,
	}

	err = storage.StoreCheckpoint(checkpoint)
	if err != nil {
		t.Fatalf("Failed to store checkpoint: %v", err)
	}

	loadedCheckpoint, err := storage.LoadCheckpoint("test-task-1", 1)
	if err != nil {
		t.Fatalf("Failed to load checkpoint: %v", err)
	}

	if loadedCheckpoint.TaskID != checkpoint.TaskID {
		t.Errorf("Expected task ID %s, got %s", checkpoint.TaskID, loadedCheckpoint.TaskID)
	}

	if loadedCheckpoint.State.Progress != checkpoint.State.Progress {
		t.Errorf("Expected progress %f, got %f", checkpoint.State.Progress, loadedCheckpoint.State.Progress)
	}

	t.Log("Checkpoint storage test passed")
}

// TestHeartbeatProcessing tests basic heartbeat processing
func TestHeartbeatProcessing(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	config := HeartbeatConfig{
		BaseInterval:        time.Second,
		MaxInterval:         10 * time.Second,
		MinInterval:         100 * time.Millisecond,
		AdaptiveEnabled:     true,
		PhiThreshold:        8.0,
		IntervalHistorySize: 10,
		JitterTolerance:     0.1,
		MissedBeatThreshold: 3,
	}

	hbManager := NewHeartbeatManager(config, logger)
	if hbManager == nil {
		t.Fatal("Failed to create HeartbeatManager")
	}

	// Test heartbeat processing
	heartbeat := &Heartbeat{
		NodeID:         "test-node-1",
		Timestamp:      time.Now(),
		SequenceNumber: 1,
		NodeStatus:     NodeHealthHealthy,
		GPUUtilization: map[string]float64{"gpu-0": 75.0},
		MemoryUsage:    map[string]float64{"gpu-0": 60.0},
		ActiveTasks:    []string{"task-1", "task-2"},
		QueueLength:    5,
		SystemMetrics: SystemMetrics{
			CPUUsage:    25.0,
			MemoryUsage: 45.0,
			DiskUsage:   30.0,
		},
		NetworkMetrics: NetworkMetrics{
			Bandwidth:  100.0,
			Latency:    time.Millisecond * 5,
			PacketLoss: 0.1,
		},
	}

	hbManager.ProcessHeartbeat(heartbeat)

	// Check if node is suspicious (should not be after one heartbeat)
	isSuspicious, suspicionLevel := hbManager.CheckNodeSuspicion("test-node-1")
	if isSuspicious {
		t.Errorf("Node should not be suspicious after first heartbeat, suspicion level: %f", suspicionLevel)
	}

	t.Log("Heartbeat processing test passed")
}
