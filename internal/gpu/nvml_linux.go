//go:build linux && cuda && cgo
// +build linux,cuda,cgo

package gpu

/*
#cgo LDFLAGS: -lnvidia-ml
#include <nvml.h>
*/
import "C"
import "fmt"

// initNVML initialises NVML once. Subsequent calls are no-ops.
func initNVML() error {
	ret := C.nvmlInit_v2()
	if ret == C.NVML_SUCCESS || ret == C.NVML_ERROR_ALREADY_INITIALIZED {
		return nil
	}
	return fmt.Errorf("nvmlInit failed: %d", int(ret))
}

// shutdownNVML is provided for completeness but currently unused.
func shutdownNVML() {
	C.nvmlShutdown()
}

// nvmlDeviceHandle returns the NVML handle for a CUDA device index.
func nvmlDeviceHandle(index int) (C.nvmlDevice_t, error) {
	var handle C.nvmlDevice_t
	ret := C.nvmlDeviceGetHandleByIndex_v2(C.uint(index), &handle)
	if ret != C.NVML_SUCCESS {
		return nil, fmt.Errorf("nvmlDeviceGetHandleByIndex failed: %d", int(ret))
	}
	return handle, nil
}

// nvmlSMCount returns the number of streaming multiprocessors for the device.
func nvmlSMCount(index int) (int, error) {
	if err := initNVML(); err != nil {
		return 0, err
	}
	handle, err := nvmlDeviceHandle(index)
	if err != nil {
		return 0, err
	}
	var count C.uint
	// NVML provides nvmlDeviceGetNumGpuCores (introduced Pascal+) which returns physical SMs
	ret := C.nvmlDeviceGetNumGpuCores(handle, &count)
	if ret != C.NVML_SUCCESS {
		return 0, fmt.Errorf("nvmlDeviceGetNumGpuCores failed: %d", int(ret))
	}
	return int(count), nil
}

// nvmlLinkWidthLane returns the negotiated PCIe link width (#lanes).
func nvmlLinkWidthLane(index int) (int, error) {
	if err := initNVML(); err != nil {
		return 0, err
	}
	handle, err := nvmlDeviceHandle(index)
	if err != nil {
		return 0, err
	}
	var lanes C.uint
	ret := C.nvmlDeviceGetMaxPcieLinkWidth(handle, &lanes)
	if ret != C.NVML_SUCCESS {
		return 0, fmt.Errorf("nvmlDeviceGetMaxPcieLinkWidth failed: %d", int(ret))
	}
	return int(lanes), nil
}

// UpdateGPUInfoNVML populates missing fields in GPUInfo using NVML if available.
// It is a no-op when called on unsupported devices or if NVML queries fail.
func UpdateGPUInfoNVML(info *GPUInfo) {
	if info == nil {
		return
	}

	// Populate Streaming Multiprocessor count
	if info.MultiProcessorCount == 0 {
		if sm, err := nvmlSMCount(info.ID); err == nil && sm > 0 {
			info.MultiProcessorCount = sm
		}
	}

	// Populate memory bus width (convert lanes → bits)
	if info.MemoryBusWidth == 0 {
		if lanes, err := nvmlLinkWidthLane(info.ID); err == nil && lanes > 0 {
			info.MemoryBusWidth = lanes * 16
		}
	}
}
