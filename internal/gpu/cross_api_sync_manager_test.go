package gpu

import (
	"fmt"
	"log"
	"os"
	"sync"
	"testing"
	"time"
)

func TestNewCrossAPISyncManager(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewCrossAPISyncManager(logger)

	if manager == nil {
		t.<PERSON>("NewCrossAPISyncManager returned nil")
	}

	if manager.resources == nil {
		t.<PERSON>rror("Resources map not initialized")
	}

	if manager.events == nil {
		t.Error("Events map not initialized")
	}

	if manager.logger != logger {
		t.<PERSON>rror("Logger not set correctly")
	}

	// Check default values
	if manager.maxEvents != 10000 {
		t.<PERSON>rrorf("Expected maxEvents to be 10000, got %d", manager.maxEvents)
	}

	if manager.syncTimeout != 30*time.Second {
		t.Errorf("Expected syncTimeout to be 30s, got %v", manager.syncTimeout)
	}

	if !manager.enableCrossDevice {
		t.Error("Expected enableCrossDevice to be true by default")
	}

	if !manager.enableCrossAPI {
		t.Error("Expected enableCrossAPI to be true by default")
	}
}

func TestSyncEventCreation(t *testing.T) {
	event := NewSyncEvent("test-event", APICuda, 0, EventTypeStreamComplete)

	if event == nil {
		t.Fatal("NewSyncEvent returned nil")
	}

	if event.GetID() != "test-event" {
		t.Errorf("Expected ID 'test-event', got '%s'", event.GetID())
	}

	if event.GetAPIType() != APICuda {
		t.Errorf("Expected APIType %v, got %v", APICuda, event.GetAPIType())
	}

	if event.GetDeviceID() != 0 {
		t.Errorf("Expected DeviceID 0, got %d", event.GetDeviceID())
	}

	if event.GetEventType() != EventTypeStreamComplete {
		t.Errorf("Expected EventType %v, got %v", EventTypeStreamComplete, event.GetEventType())
	}

	if event.GetState() != ExecutionStatePending {
		t.Errorf("Expected initial state %v, got %v", ExecutionStatePending, event.GetState())
	}

	if !event.IsReady() {
		t.Error("Event with no dependencies should be ready")
	}
}

func TestSyncEventDependencies(t *testing.T) {
	event1 := NewSyncEvent("event1", APICuda, 0, EventTypeStreamComplete)
	event2 := NewSyncEvent("event2", APIOpenCL, 1, EventTypeQueueComplete)
	event3 := NewSyncEvent("event3", APIMetal, 2, EventTypeBufferComplete)

	// Test adding valid dependency
	err := event2.AddDependency(event1)
	if err != nil {
		t.Errorf("Failed to add valid dependency: %v", err)
	}

	deps := event2.GetDependencies()
	if len(deps) != 1 || deps[0] != event1 {
		t.Error("Dependency not added correctly")
	}

	// Event2 should not be ready since event1 is still pending
	if event2.IsReady() {
		t.Error("Event with pending dependencies should not be ready")
	}

	// Complete event1
	event1.SetState(ExecutionStateCompleted)

	// Now event2 should be ready
	if !event2.IsReady() {
		t.Error("Event should be ready after dependencies are completed")
	}

	// Test circular dependency prevention
	err = event1.AddDependency(event2)
	if err == nil {
		t.Error("Should have prevented circular dependency")
	}

	// Test self-dependency prevention
	err = event3.AddDependency(event3)
	if err == nil {
		t.Error("Should have prevented self-dependency")
	}

	// Test nil dependency
	err = event3.AddDependency(nil)
	if err == nil {
		t.Error("Should have prevented nil dependency")
	}
}

func TestUnifiedResourceManagement(t *testing.T) {
	resource := NewUnifiedResource("test-resource", APICuda, 0, UnifiedPriorityHigh, "mock-handle")

	if resource == nil {
		t.Fatal("NewUnifiedResource returned nil")
	}

	if resource.GetID() != "test-resource" {
		t.Errorf("Expected ID 'test-resource', got '%s'", resource.GetID())
	}

	if resource.GetAPIType() != APICuda {
		t.Errorf("Expected APIType %v, got %v", APICuda, resource.GetAPIType())
	}

	if resource.GetDeviceID() != 0 {
		t.Errorf("Expected DeviceID 0, got %d", resource.GetDeviceID())
	}

	if resource.GetPriority() != UnifiedPriorityHigh {
		t.Errorf("Expected priority %v, got %v", UnifiedPriorityHigh, resource.GetPriority())
	}

	if resource.GetNativeHandle() != "mock-handle" {
		t.Error("Native handle not set correctly")
	}

	if resource.IsInUse() {
		t.Error("New resource should not be in use")
	}

	// Test setting in use
	resource.SetInUse(true)
	if !resource.IsInUse() {
		t.Error("Resource should be in use after SetInUse(true)")
	}

	// Test state changes
	resource.SetState(ExecutionStateRunning)
	if resource.GetState() != ExecutionStateRunning {
		t.Errorf("Expected state %v, got %v", ExecutionStateRunning, resource.GetState())
	}
}

func TestCrossAPISyncManagerResourceManagement(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewCrossAPISyncManager(logger)

	// Test resource registration
	resource := NewUnifiedResource("test-resource", APICuda, 0, UnifiedPriorityHigh, "mock-handle")
	err := manager.RegisterResource(resource)
	if err != nil {
		t.Errorf("Failed to register resource: %v", err)
	}

	stats := manager.GetManagerStats()
	if stats["total_resources"].(int64) != 1 {
		t.Error("Resource count not updated after registration")
	}

	// Test duplicate registration
	err = manager.RegisterResource(resource)
	if err == nil {
		t.Error("Should have prevented duplicate resource registration")
	}

	// Test resource acquisition
	acquired, err := manager.AcquireUnifiedResource(APICuda, 0, UnifiedPriorityHigh)
	if err != nil {
		t.Errorf("Failed to acquire resource: %v", err)
	}

	if acquired == nil {
		t.Error("AcquireUnifiedResource returned nil")
	}

	if acquired.GetID() != "test-resource" {
		t.Error("Acquired wrong resource")
	}

	if !acquired.IsInUse() {
		t.Error("Acquired resource should be marked as in use")
	}

	// Test resource release
	err = manager.ReleaseUnifiedResource(acquired.GetID())
	if err != nil {
		t.Errorf("Failed to release resource: %v", err)
	}

	if acquired.IsInUse() {
		t.Error("Released resource should not be in use")
	}

	// Test resource unregistration
	err = manager.UnregisterResource(resource.GetID())
	if err != nil {
		t.Errorf("Failed to unregister resource: %v", err)
	}

	stats = manager.GetManagerStats()
	if stats["active_resources"].(int) != 0 {
		t.Error("Active resource count not updated after unregistration")
	}
}

func TestCrossAPISyncManagerEventManagement(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewCrossAPISyncManager(logger)

	// Test event creation
	event, err := manager.CreateEvent("test-event", APICuda, 0, EventTypeStreamComplete)
	if err != nil {
		t.Errorf("Failed to create event: %v", err)
	}

	if event == nil {
		t.Error("CreateEvent returned nil")
	}

	if event.GetID() != "test-event" {
		t.Error("Event ID not set correctly")
	}

	stats := manager.GetManagerStats()
	if stats["total_events"].(int64) != 1 {
		t.Error("Event count not updated after creation")
	}

	// Test duplicate event creation
	_, err = manager.CreateEvent("test-event", APICuda, 0, EventTypeStreamComplete)
	if err == nil {
		t.Error("Should have prevented duplicate event creation")
	}

	// Test waiting for event
	err = manager.WaitForEvent("test-event")
	if err != nil {
		t.Errorf("Failed to wait for event: %v", err)
	}

	// Test waiting for multiple events
	event2, _ := manager.CreateEvent("test-event-2", APIOpenCL, 1, EventTypeQueueComplete)
	err = manager.WaitForEvents([]string{"test-event", "test-event-2"})
	if err != nil {
		t.Errorf("Failed to wait for multiple events: %v", err)
	}

	// Verify events are completed
	if event.GetState() != ExecutionStateCompleted {
		t.Error("Event should be completed after waiting")
	}

	if event2.GetState() != ExecutionStateCompleted {
		t.Error("Event2 should be completed after waiting")
	}
}

func TestCrossAPISyncManagerBarriers(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewCrossAPISyncManager(logger)

	// Create some events to wait for
	event1, _ := manager.CreateEvent("event1", APICuda, 0, EventTypeStreamComplete)
	event2, _ := manager.CreateEvent("event2", APIOpenCL, 1, EventTypeQueueComplete)

	// Create barrier
	barrier, err := manager.CreateBarrier("test-barrier", []string{"event1", "event2"})
	if err != nil {
		t.Errorf("Failed to create barrier: %v", err)
	}

	if barrier == nil {
		t.Error("CreateBarrier returned nil")
	}

	if barrier.GetEventType() != EventTypeBarrier {
		t.Error("Barrier should have EventTypeBarrier")
	}

	// Barrier should not be ready until dependencies are met
	if barrier.IsReady() {
		t.Error("Barrier should not be ready with pending dependencies")
	}

	// Complete dependencies
	event1.SetState(ExecutionStateCompleted)
	event2.SetState(ExecutionStateCompleted)

	// Now barrier should be ready
	if !barrier.IsReady() {
		t.Error("Barrier should be ready after dependencies are completed")
	}
}

func TestCrossAPISyncManagerCrossAPISync(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewCrossAPISyncManager(logger)

	// Create events for different APIs
	cudaEvent, _ := manager.CreateEvent("cuda-event", APICuda, 0, EventTypeStreamComplete)
	openclEvent, _ := manager.CreateEvent("opencl-event", APIOpenCL, 1, EventTypeQueueComplete)
	metalEvent, _ := manager.CreateEvent("metal-event", APIMetal, 2, EventTypeBufferComplete)

	// Test cross-API synchronization
	apiEvents := map[APIType][]string{
		APICuda:   {"cuda-event"},
		APIOpenCL: {"opencl-event"},
		APIMetal:  {"metal-event"},
	}

	err := manager.SynchronizeAcrossAPIs("cross-sync", apiEvents)
	if err != nil {
		t.Errorf("Failed to synchronize across APIs: %v", err)
	}

	// Verify all events are completed
	if cudaEvent.GetState() != ExecutionStateCompleted {
		t.Error("CUDA event should be completed after cross-API sync")
	}

	if openclEvent.GetState() != ExecutionStateCompleted {
		t.Error("OpenCL event should be completed after cross-API sync")
	}

	if metalEvent.GetState() != ExecutionStateCompleted {
		t.Error("Metal event should be completed after cross-API sync")
	}

	stats := manager.GetManagerStats()
	if stats["total_syncs"].(int64) != 1 {
		t.Error("Cross-API sync count not updated")
	}
}

func TestPriorityConversions(t *testing.T) {
	// Test CUDA priority conversions using integer constants
	if ConvertCudaPriority(0) != UnifiedPriorityLow {
		t.Error("CUDA low priority conversion failed")
	}
	if ConvertCudaPriority(1) != UnifiedPriorityNormal {
		t.Error("CUDA normal priority conversion failed")
	}
	if ConvertCudaPriority(2) != UnifiedPriorityHigh {
		t.Error("CUDA high priority conversion failed")
	}

	// Test OpenCL priority conversions
	if ConvertOpenCLPriority(0) != UnifiedPriorityLow {
		t.Error("OpenCL low priority conversion failed")
	}
	if ConvertOpenCLPriority(1) != UnifiedPriorityNormal {
		t.Error("OpenCL normal priority conversion failed")
	}
	if ConvertOpenCLPriority(2) != UnifiedPriorityHigh {
		t.Error("OpenCL high priority conversion failed")
	}

	// Test Metal priority conversions
	if ConvertMetalPriority(BufferPriorityLow) != UnifiedPriorityLow {
		t.Error("Metal low priority conversion failed")
	}
	if ConvertMetalPriority(BufferPriorityNormal) != UnifiedPriorityNormal {
		t.Error("Metal normal priority conversion failed")
	}
	if ConvertMetalPriority(BufferPriorityHigh) != UnifiedPriorityHigh {
		t.Error("Metal high priority conversion failed")
	}

	// Test unified to API-specific conversions
	cudaPrio := ConvertToAPIPriority(UnifiedPriorityHigh, APICuda)
	if cudaPrio != 2 { // StreamPriorityHigh equivalent
		t.Error("Unified to CUDA high priority conversion failed")
	}

	openclPrio := ConvertToAPIPriority(UnifiedPriorityHigh, APIOpenCL)
	if openclPrio != 2 { // QueuePriorityHigh equivalent
		t.Error("Unified to OpenCL high priority conversion failed")
	}

	metalPrio := ConvertToAPIPriority(UnifiedPriorityHigh, APIMetal)
	if metalPrio != BufferPriorityHigh {
		t.Error("Unified to Metal high priority conversion failed")
	}
}

func TestConcurrentEventManagement(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewCrossAPISyncManager(logger)

	const numEvents = 100
	const numGoroutines = 10

	var wg sync.WaitGroup
	eventIDs := make([]string, numEvents)

	// Create events concurrently
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(start int) {
			defer wg.Done()
			for j := 0; j < numEvents/numGoroutines; j++ {
				idx := start + j
				eventID := fmt.Sprintf("event-%d", idx)
				eventIDs[idx] = eventID

				apiType := APIType(idx % 3) // Cycle through API types
				deviceID := idx % 4         // Cycle through device IDs

				_, err := manager.CreateEvent(eventID, apiType, deviceID, EventTypeStreamComplete)
				if err != nil {
					t.Errorf("Failed to create event %s: %v", eventID, err)
				}
			}
		}(i * (numEvents / numGoroutines))
	}

	wg.Wait()

	// Verify all events were created
	stats := manager.GetManagerStats()
	if stats["total_events"].(int64) != numEvents {
		t.Errorf("Expected %d events, got %d", numEvents, stats["total_events"].(int64))
	}

	// Wait for all events concurrently
	err := manager.WaitForEvents(eventIDs)
	if err != nil {
		t.Errorf("Failed to wait for events: %v", err)
	}
}

func TestResourceScoring(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewCrossAPISyncManager(logger)

	// Create resources with different characteristics
	resource1 := NewUnifiedResource("exact-match", APICuda, 0, UnifiedPriorityHigh, "handle1")
	resource2 := NewUnifiedResource("api-match", APICuda, 1, UnifiedPriorityNormal, "handle2")
	resource3 := NewUnifiedResource("device-match", APIOpenCL, 0, UnifiedPriorityLow, "handle3")
	resource4 := NewUnifiedResource("no-match", APIMetal, 2, UnifiedPriorityLow, "handle4")

	// Set recent usage for resource1
	resource1.SetInUse(true)
	resource1.SetInUse(false) // This updates lastUsed

	// Calculate scores for preferred CUDA device 0 high priority
	score1 := manager.calculateResourceScore(resource1, APICuda, 0, UnifiedPriorityHigh)
	score2 := manager.calculateResourceScore(resource2, APICuda, 0, UnifiedPriorityHigh)
	score3 := manager.calculateResourceScore(resource3, APICuda, 0, UnifiedPriorityHigh)
	score4 := manager.calculateResourceScore(resource4, APICuda, 0, UnifiedPriorityHigh)

	// Resource1 should have highest score (exact API + device + priority match + recent usage)
	if score1 <= score2 || score1 <= score3 || score1 <= score4 {
		t.Errorf("Resource1 should have highest score. Scores: %d, %d, %d, %d", score1, score2, score3, score4)
	}

	// Resource2 should score higher than resource3 (API match vs device match)
	if score2 <= score3 {
		t.Errorf("Resource2 should score higher than resource3. Scores: %d vs %d", score2, score3)
	}

	// Resource4 should have lowest score (no matches)
	if score4 >= score3 || score4 >= score2 {
		t.Errorf("Resource4 should have lowest score. Scores: %d, %d, %d, %d", score1, score2, score3, score4)
	}
}

func TestManagerCleanup(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewCrossAPISyncManager(logger)

	// Create some resources and events
	resource := NewUnifiedResource("test-resource", APICuda, 0, UnifiedPriorityHigh, "handle")
	manager.RegisterResource(resource)

	event, _ := manager.CreateEvent("test-event", APICuda, 0, EventTypeStreamComplete)

	// Acquire resource
	acquired, _ := manager.AcquireUnifiedResource(APICuda, 0, UnifiedPriorityHigh)
	if !acquired.IsInUse() {
		t.Error("Resource should be in use after acquisition")
	}

	// Cleanup
	err := manager.Cleanup()
	if err != nil {
		t.Errorf("Cleanup failed: %v", err)
	}

	// Verify cleanup
	if acquired.IsInUse() {
		t.Error("Resource should not be in use after cleanup")
	}

	if event.GetState() != ExecutionStateError {
		t.Error("Events should be in error state after cleanup")
	}

	stats := manager.GetManagerStats()
	if stats["active_resources"].(int) != 0 {
		t.Error("Should have no active resources after cleanup")
	}

	if stats["active_events"].(int) != 0 {
		t.Error("Should have no active events after cleanup")
	}
}

func TestAPITypeString(t *testing.T) {
	if APICuda.String() != "CUDA" {
		t.Errorf("Expected 'CUDA', got '%s'", APICuda.String())
	}
	if APIOpenCL.String() != "OpenCL" {
		t.Errorf("Expected 'OpenCL', got '%s'", APIOpenCL.String())
	}
	if APIMetal.String() != "Metal" {
		t.Errorf("Expected 'Metal', got '%s'", APIMetal.String())
	}

	unknownAPI := APIType(999)
	if unknownAPI.String() != "Unknown" {
		t.Errorf("Expected 'Unknown', got '%s'", unknownAPI.String())
	}
}

func TestEventTypeString(t *testing.T) {
	testCases := []struct {
		eventType EventType
		expected  string
	}{
		{EventTypeStreamComplete, "StreamComplete"},
		{EventTypeQueueComplete, "QueueComplete"},
		{EventTypeBufferComplete, "BufferComplete"},
		{EventTypeBarrier, "Barrier"},
		{EventTypeMemoryTransfer, "MemoryTransfer"},
		{EventTypeCustom, "Custom"},
	}

	for _, tc := range testCases {
		if tc.eventType.String() != tc.expected {
			t.Errorf("Expected '%s', got '%s'", tc.expected, tc.eventType.String())
		}
	}

	unknownType := EventType(999)
	if unknownType.String() != "Unknown" {
		t.Errorf("Expected 'Unknown', got '%s'", unknownType.String())
	}
}

func TestEventUserData(t *testing.T) {
	event := NewSyncEvent("test-event", APICuda, 0, EventTypeStreamComplete)

	// Test setting and getting user data
	testData := map[string]interface{}{
		"key1": "value1",
		"key2": 42,
	}

	event.SetUserData(testData)

	userData := event.GetUserData()
	if userData == nil {
		t.Error("User data should not be nil")
	}

	dataMap, ok := userData.(map[string]interface{})
	if !ok {
		t.Error("User data should be of expected type")
	}

	if dataMap["key1"] != "value1" || dataMap["key2"] != 42 {
		t.Error("User data not stored correctly")
	}
}

func TestComplexDependencyChain(t *testing.T) {
	// Create a complex dependency chain: A -> B -> C, A -> D -> C
	eventA := NewSyncEvent("A", APICuda, 0, EventTypeStreamComplete)
	eventB := NewSyncEvent("B", APIOpenCL, 1, EventTypeQueueComplete)
	eventC := NewSyncEvent("C", APIMetal, 2, EventTypeBufferComplete)
	eventD := NewSyncEvent("D", APICuda, 0, EventTypeStreamComplete)

	// Build dependencies
	err := eventB.AddDependency(eventA)
	if err != nil {
		t.Errorf("Failed to add dependency B->A: %v", err)
	}

	err = eventC.AddDependency(eventB)
	if err != nil {
		t.Errorf("Failed to add dependency C->B: %v", err)
	}

	err = eventD.AddDependency(eventA)
	if err != nil {
		t.Errorf("Failed to add dependency D->A: %v", err)
	}

	err = eventC.AddDependency(eventD)
	if err != nil {
		t.Errorf("Failed to add dependency C->D: %v", err)
	}

	// Initially, only A should be ready
	if !eventA.IsReady() {
		t.Error("Event A should be ready (no dependencies)")
	}
	if eventB.IsReady() || eventC.IsReady() || eventD.IsReady() {
		t.Error("Events B, C, D should not be ready initially")
	}

	// Complete A
	eventA.SetState(ExecutionStateCompleted)

	// Now B and D should be ready
	if !eventB.IsReady() || !eventD.IsReady() {
		t.Error("Events B and D should be ready after A completes")
	}
	if eventC.IsReady() {
		t.Error("Event C should not be ready yet")
	}

	// Complete B and D
	eventB.SetState(ExecutionStateCompleted)
	eventD.SetState(ExecutionStateCompleted)

	// Now C should be ready
	if !eventC.IsReady() {
		t.Error("Event C should be ready after B and D complete")
	}
}
