package gpu

import (
	"fmt"
	"log"
	"sync"
	"time"
)

// SynchronizationManager handles coordination and synchronization across multiple GPUs
type SynchronizationManager struct {
	devices       []*ManagedDevice
	barriers      map[string]*Barrier
	events        map[string]*CrossDeviceEvent
	mutex         sync.RWMutex
	logger        *log.Logger
	isInitialized bool
}

// Barrier represents a synchronization barrier for coordinating multiple devices
type Barrier struct {
	ID              string
	ExpectedDevices int
	ArrivedDevices  int
	WaitingDevices  map[string]bool
	Done            chan bool
	mutex           sync.Mutex
	created         time.Time
	timeout         time.Duration
}

// CrossDeviceEvent represents an event that can be shared across devices
type CrossDeviceEvent struct {
	ID        string
	DeviceID  string
	Timestamp time.Time
	Data      interface{}
	Signaled  bool
	Waiters   []chan bool
	mutex     sync.RWMutex
}

// NewSynchronizationManager creates a new synchronization manager
func NewSynchronizationManager(logger *log.Logger) *SynchronizationManager {
	if logger == nil {
		logger = log.Default()
	}

	return &SynchronizationManager{
		barriers:      make(map[string]*Barrier),
		events:        make(map[string]*CrossDeviceEvent),
		logger:        logger,
		isInitialized: false,
	}
}

// Initialize initializes the synchronization manager with managed devices
func (sm *SynchronizationManager) Initialize(devices []*ManagedDevice) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	if sm.isInitialized {
		return fmt.Errorf("synchronization manager already initialized")
	}

	sm.devices = make([]*ManagedDevice, len(devices))
	copy(sm.devices, devices)

	sm.isInitialized = true
	sm.logger.Printf("Synchronization manager initialized with %d devices", len(devices))

	return nil
}

// CreateBarrier creates a new synchronization barrier
func (sm *SynchronizationManager) CreateBarrier(barrierID string, expectedDevices int, timeout time.Duration) (*Barrier, error) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	if !sm.isInitialized {
		return nil, fmt.Errorf("synchronization manager not initialized")
	}

	if _, exists := sm.barriers[barrierID]; exists {
		return nil, fmt.Errorf("barrier %s already exists", barrierID)
	}

	if expectedDevices <= 0 {
		return nil, fmt.Errorf("expected devices must be positive")
	}

	barrier := &Barrier{
		ID:              barrierID,
		ExpectedDevices: expectedDevices,
		ArrivedDevices:  0,
		WaitingDevices:  make(map[string]bool),
		Done:            make(chan bool, expectedDevices), // Buffered to prevent blocking
		created:         time.Now(),
		timeout:         timeout,
	}

	sm.barriers[barrierID] = barrier
	sm.logger.Printf("Created barrier %s expecting %d devices", barrierID, expectedDevices)

	// Start timeout handler if timeout is specified
	if timeout > 0 {
		go sm.handleBarrierTimeout(barrier)
	}

	return barrier, nil
}

// WaitAtBarrier waits for a device at the specified barrier
func (sm *SynchronizationManager) WaitAtBarrier(barrierID string, deviceID string) error {
	sm.mutex.RLock()
	barrier, exists := sm.barriers[barrierID]
	sm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("barrier %s not found", barrierID)
	}

	barrier.mutex.Lock()

	// Check if device already arrived
	if barrier.WaitingDevices[deviceID] {
		barrier.mutex.Unlock()
		return fmt.Errorf("device %s already waiting at barrier %s", deviceID, barrierID)
	}

	// Mark device as arrived
	barrier.WaitingDevices[deviceID] = true
	barrier.ArrivedDevices++

	sm.logger.Printf("Device %s arrived at barrier %s (%d/%d)",
		deviceID, barrierID, barrier.ArrivedDevices, barrier.ExpectedDevices)

	// Check if all devices have arrived
	if barrier.ArrivedDevices >= barrier.ExpectedDevices {
		// Signal all waiting devices
		for i := 0; i < barrier.ExpectedDevices; i++ {
			select {
			case barrier.Done <- true:
			default:
				// Channel is full, which shouldn't happen with proper buffering
			}
		}
		sm.logger.Printf("Barrier %s released - all devices arrived", barrierID)
		barrier.mutex.Unlock()
		return nil
	}

	barrier.mutex.Unlock()

	// Wait for barrier to be released
	select {
	case <-barrier.Done:
		return nil
	case <-time.After(barrier.timeout):
		return fmt.Errorf("timeout waiting at barrier %s", barrierID)
	}
}

// handleBarrierTimeout handles barrier timeout
func (sm *SynchronizationManager) handleBarrierTimeout(barrier *Barrier) {
	select {
	case <-time.After(barrier.timeout):
		barrier.mutex.Lock()
		if barrier.ArrivedDevices < barrier.ExpectedDevices {
			sm.logger.Printf("Barrier %s timed out (%d/%d devices arrived)",
				barrier.ID, barrier.ArrivedDevices, barrier.ExpectedDevices)

			// Signal timeout to any waiting devices
			for i := 0; i < barrier.ArrivedDevices; i++ {
				select {
				case barrier.Done <- false: // false indicates timeout
				default:
				}
			}
		}
		barrier.mutex.Unlock()
	}
}

// RemoveBarrier removes a barrier after it's no longer needed
func (sm *SynchronizationManager) RemoveBarrier(barrierID string) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	barrier, exists := sm.barriers[barrierID]
	if !exists {
		return fmt.Errorf("barrier %s not found", barrierID)
	}

	// Close the done channel to signal any remaining waiters
	close(barrier.Done)
	delete(sm.barriers, barrierID)

	sm.logger.Printf("Removed barrier %s", barrierID)
	return nil
}

// CreateEvent creates a new cross-device event
func (sm *SynchronizationManager) CreateEvent(eventID string, deviceID string, data interface{}) (*CrossDeviceEvent, error) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	if !sm.isInitialized {
		return nil, fmt.Errorf("synchronization manager not initialized")
	}

	if _, exists := sm.events[eventID]; exists {
		return nil, fmt.Errorf("event %s already exists", eventID)
	}

	event := &CrossDeviceEvent{
		ID:        eventID,
		DeviceID:  deviceID,
		Timestamp: time.Now(),
		Data:      data,
		Signaled:  false,
		Waiters:   make([]chan bool, 0),
	}

	sm.events[eventID] = event
	sm.logger.Printf("Created event %s from device %s", eventID, deviceID)

	return event, nil
}

// SignalEvent signals a cross-device event
func (sm *SynchronizationManager) SignalEvent(eventID string) error {
	sm.mutex.RLock()
	event, exists := sm.events[eventID]
	sm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("event %s not found", eventID)
	}

	event.mutex.Lock()
	defer event.mutex.Unlock()

	if event.Signaled {
		return fmt.Errorf("event %s already signaled", eventID)
	}

	event.Signaled = true

	// Notify all waiters
	for _, waiter := range event.Waiters {
		select {
		case waiter <- true:
		default:
			// Waiter channel is full or closed
		}
	}

	sm.logger.Printf("Signaled event %s", eventID)
	return nil
}

// WaitForEvent waits for a cross-device event to be signaled
func (sm *SynchronizationManager) WaitForEvent(eventID string, timeout time.Duration) error {
	sm.mutex.RLock()
	event, exists := sm.events[eventID]
	sm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("event %s not found", eventID)
	}

	event.mutex.RLock()
	if event.Signaled {
		event.mutex.RUnlock()
		return nil // Already signaled
	}

	// Create waiter channel
	waiter := make(chan bool, 1)
	event.Waiters = append(event.Waiters, waiter)
	event.mutex.RUnlock()

	// Wait for signal or timeout
	select {
	case <-waiter:
		return nil
	case <-time.After(timeout):
		return fmt.Errorf("timeout waiting for event %s", eventID)
	}
}

// RemoveEvent removes an event after it's no longer needed
func (sm *SynchronizationManager) RemoveEvent(eventID string) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	event, exists := sm.events[eventID]
	if !exists {
		return fmt.Errorf("event %s not found", eventID)
	}

	// Close all waiter channels
	event.mutex.Lock()
	for _, waiter := range event.Waiters {
		close(waiter)
	}
	event.mutex.Unlock()

	delete(sm.events, eventID)
	sm.logger.Printf("Removed event %s", eventID)
	return nil
}

// GetActiveBarriers returns information about active barriers
func (sm *SynchronizationManager) GetActiveBarriers() map[string]*Barrier {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	result := make(map[string]*Barrier)
	for id, barrier := range sm.barriers {
		result[id] = barrier
	}
	return result
}

// GetActiveEvents returns information about active events
func (sm *SynchronizationManager) GetActiveEvents() map[string]*CrossDeviceEvent {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	result := make(map[string]*CrossDeviceEvent)
	for id, event := range sm.events {
		result[id] = event
	}
	return result
}

// IsInitialized returns whether the synchronization manager is initialized
func (sm *SynchronizationManager) IsInitialized() bool {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	return sm.isInitialized
}

// Cleanup cleans up all synchronization resources
func (sm *SynchronizationManager) Cleanup() error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	if !sm.isInitialized {
		return nil
	}

	var errors []error

	// Clean up all barriers
	for barrierID := range sm.barriers {
		if err := sm.removeBarrierUnsafe(barrierID); err != nil {
			errors = append(errors, fmt.Errorf("failed to remove barrier %s: %w", barrierID, err))
		}
	}

	// Clean up all events
	for eventID := range sm.events {
		if err := sm.removeEventUnsafe(eventID); err != nil {
			errors = append(errors, fmt.Errorf("failed to remove event %s: %w", eventID, err))
		}
	}

	sm.devices = nil
	sm.isInitialized = false

	if len(errors) > 0 {
		return fmt.Errorf("cleanup completed with errors: %v", errors)
	}

	sm.logger.Println("Synchronization manager cleanup completed successfully")
	return nil
}

// removeBarrierUnsafe removes a barrier without acquiring mutex (for internal use)
func (sm *SynchronizationManager) removeBarrierUnsafe(barrierID string) error {
	barrier, exists := sm.barriers[barrierID]
	if !exists {
		return fmt.Errorf("barrier %s not found", barrierID)
	}

	close(barrier.Done)
	delete(sm.barriers, barrierID)
	return nil
}

// removeEventUnsafe removes an event without acquiring mutex (for internal use)
func (sm *SynchronizationManager) removeEventUnsafe(eventID string) error {
	event, exists := sm.events[eventID]
	if !exists {
		return fmt.Errorf("event %s not found", eventID)
	}

	event.mutex.Lock()
	for _, waiter := range event.Waiters {
		close(waiter)
	}
	event.mutex.Unlock()

	delete(sm.events, eventID)
	return nil
}
