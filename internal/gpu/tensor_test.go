package gpu

import (
	"fmt"
	"log"
	"os"
	"testing"
)

// TestTensorDataTypes tests tensor data type functionality
func TestTensorDataTypes(t *testing.T) {
	tests := []struct {
		dtype    TensorDataType
		expected string
		size     int
	}{
		{TensorFloat32, "float32", 4},
		{TensorFloat16, "float16", 2},
		{TensorInt8, "int8", 1},
		{TensorInt32, "int32", 4},
		{TensorBool, "bool", 1},
	}

	for _, test := range tests {
		t.Run(test.expected, func(t *testing.T) {
			if test.dtype.String() != test.expected {
				t.<PERSON>rf("Expected string %s, got %s", test.expected, test.dtype.String())
			}
			if test.dtype.Size() != test.size {
				t.Errorf("Expected size %d, got %d", test.size, test.dtype.Size())
			}
		})
	}
}

// TestTensorShape tests tensor shape functionality
func TestTensorShape(t *testing.T) {
	t.Run("Valid Shape", func(t *testing.T) {
		shape := TensorShape{2, 3, 4}

		if !shape.IsValid() {
			t.<PERSON>rror("Expected shape to be valid")
		}

		if shape.NumElements() != 24 {
			t.Errorf("Expected 24 elements, got %d", shape.NumElements())
		}

		if shape.Rank() != 3 {
			t.Errorf("Expected rank 3, got %d", shape.Rank())
		}

		clone := shape.Clone()
		if len(clone) != len(shape) {
			t.Error("Clone has different length")
		}

		// Modify original, clone should be unaffected
		shape[0] = 999
		if clone[0] == 999 {
			t.Error("Clone was affected by original modification")
		}
	})

	t.Run("Invalid Shape", func(t *testing.T) {
		shape := TensorShape{2, 0, 4}
		if shape.IsValid() {
			t.Error("Expected shape to be invalid")
		}
	})

	t.Run("Empty Shape", func(t *testing.T) {
		shape := TensorShape{}
		if shape.NumElements() != 0 {
			t.Errorf("Expected 0 elements for empty shape, got %d", shape.NumElements())
		}
	})
}

// TestTensorStrides tests stride calculation
func TestTensorStrides(t *testing.T) {
	tests := []struct {
		shape           TensorShape
		expectedStrides TensorStrides
	}{
		{TensorShape{4}, TensorStrides{1}},
		{TensorShape{2, 3}, TensorStrides{3, 1}},
		{TensorShape{2, 3, 4}, TensorStrides{12, 4, 1}},
		{TensorShape{5, 4, 3, 2}, TensorStrides{24, 6, 2, 1}},
	}

	for i, test := range tests {
		t.Run(fmt.Sprintf("Test%d", i), func(t *testing.T) {
			strides := ComputeStrides(test.shape)

			if len(strides) != len(test.expectedStrides) {
				t.Errorf("Expected %d strides, got %d", len(test.expectedStrides), len(strides))
				return
			}

			for j, stride := range strides {
				if stride != test.expectedStrides[j] {
					t.Errorf("Expected stride[%d] = %d, got %d", j, test.expectedStrides[j], stride)
				}
			}
		})
	}
}

// TestTensorCreation tests basic tensor creation
func TestTensorCreation(t *testing.T) {
	t.Run("CPU Tensor Creation", func(t *testing.T) {
		shape := TensorShape{2, 3, 4}
		tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
		if err != nil {
			t.Fatalf("Failed to create tensor: %v", err)
		}
		defer tensor.Free()

		if tensor.Shape().String() != shape.String() {
			t.Errorf("Expected shape %v, got %v", shape, tensor.Shape())
		}

		if tensor.DataType() != TensorFloat32 {
			t.Errorf("Expected data type %v, got %v", TensorFloat32, tensor.DataType())
		}

		if tensor.Device() != DeviceCPU {
			t.Errorf("Expected device %v, got %v", DeviceCPU, tensor.Device())
		}

		if tensor.Size() != 24*4 { // 24 elements * 4 bytes each
			t.Errorf("Expected size 96 bytes, got %d", tensor.Size())
		}

		if tensor.NumElements() != 24 {
			t.Errorf("Expected 24 elements, got %d", tensor.NumElements())
		}

		if tensor.Rank() != 3 {
			t.Errorf("Expected rank 3, got %d", tensor.Rank())
		}

		if !tensor.IsContiguous() {
			t.Error("Expected tensor to be contiguous")
		}

		if tensor.Data() == nil {
			t.Error("Expected non-nil data pointer")
		}
	})

	t.Run("GPU Tensor Creation with Memory Pool", func(t *testing.T) {
		// Create memory pool for GPU allocation
		logger := log.New(os.Stdout, "TEST_TENSOR: ", log.LstdFlags)
		config := DefaultPoolConfig(0)
		config.MaxSize = 1024 * 1024 // 1MB

		pool, err := NewAdvancedMemoryPool(config, logger)
		if err != nil {
			t.Fatalf("Failed to create memory pool: %v", err)
		}
		defer pool.Shutdown()

		if err := pool.Initialize(); err != nil {
			t.Fatalf("Failed to initialize memory pool: %v", err)
		}

		shape := TensorShape{10, 10}
		// For GPU tensors, we need to create them differently since they need the pool
		// For now, let's create a CPU tensor and test the memory pool integration separately
		tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
		if err != nil {
			t.Fatalf("Failed to create tensor: %v", err)
		}
		defer tensor.Free()

		// Set memory pool for future GPU operations
		tensor.SetMemoryPool(pool)

		if tensor.Device() != DeviceCPU {
			t.Errorf("Expected device %v, got %v", DeviceCPU, tensor.Device())
		}

		if tensor.Size() != 100*4 { // 100 elements * 4 bytes each
			t.Errorf("Expected size 400 bytes, got %d", tensor.Size())
		}
	})

	t.Run("Invalid Tensor Creation", func(t *testing.T) {
		// Invalid shape
		_, err := NewTensor(TensorShape{2, 0, 4}, TensorFloat32, DeviceCPU, 0)
		if err == nil {
			t.Error("Expected error for invalid shape")
		}

		// Zero elements
		_, err = NewTensor(TensorShape{}, TensorFloat32, DeviceCPU, 0)
		if err == nil {
			t.Error("Expected error for empty shape")
		}
	})
}

// TestTensorIndexing tests tensor indexing functionality
func TestTensorIndexing(t *testing.T) {
	tensor, err := NewTensor(TensorShape{2, 3, 4}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	t.Run("Linear Index Calculation", func(t *testing.T) {
		tests := []struct {
			indices  []int64
			expected int64
		}{
			{[]int64{0, 0, 0}, 0},
			{[]int64{0, 0, 1}, 1},
			{[]int64{0, 1, 0}, 4},
			{[]int64{1, 0, 0}, 12},
			{[]int64{1, 2, 3}, 23}, // (1*12) + (2*4) + (3*1) = 12 + 8 + 3 = 23
		}

		for _, test := range tests {
			index, err := tensor.linearIndex(test.indices...)
			if err != nil {
				t.Errorf("Unexpected error for indices %v: %v", test.indices, err)
				continue
			}
			if index != test.expected {
				t.Errorf("Expected index %d for indices %v, got %d",
					test.expected, test.indices, index)
			}
		}
	})

	t.Run("Invalid Index Access", func(t *testing.T) {
		// Wrong number of indices
		_, err := tensor.linearIndex(0, 0)
		if err == nil {
			t.Error("Expected error for wrong number of indices")
		}

		// Out of bounds
		_, err = tensor.linearIndex(2, 0, 0)
		if err == nil {
			t.Error("Expected error for out of bounds index")
		}

		_, err = tensor.linearIndex(0, 3, 0)
		if err == nil {
			t.Error("Expected error for out of bounds index")
		}

		_, err = tensor.linearIndex(-1, 0, 0)
		if err == nil {
			t.Error("Expected error for negative index")
		}
	})
}

// TestTensorViews tests tensor view functionality
func TestTensorViews(t *testing.T) {
	tensor, err := NewTensor(TensorShape{2, 3, 4}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	t.Run("Valid View", func(t *testing.T) {
		// Reshape 2x3x4 to 6x4
		view, err := tensor.View(TensorShape{6, 4})
		if err != nil {
			t.Fatalf("Failed to create view: %v", err)
		}
		defer view.Free()

		if view.NumElements() != tensor.NumElements() {
			t.Error("View should have same number of elements")
		}

		if view.Shape().String() != "[6 4]" {
			t.Errorf("Expected view shape [6 4], got %v", view.Shape())
		}

		// Views should share the same data pointer
		if view.Data() != tensor.Data() {
			t.Error("View should share data with original tensor")
		}

		if view.ownsMemory {
			t.Error("View should not own memory")
		}
	})

	t.Run("Invalid View", func(t *testing.T) {
		// Different number of elements
		_, err := tensor.View(TensorShape{2, 2, 4})
		if err == nil {
			t.Error("Expected error for view with different number of elements")
		}

		// Invalid shape
		_, err = tensor.View(TensorShape{2, 0, 4})
		if err == nil {
			t.Error("Expected error for invalid view shape")
		}
	})
}

// TestTensorSlicing tests tensor slicing functionality
func TestTensorSlicing(t *testing.T) {
	tensor, err := NewTensor(TensorShape{4, 3, 2}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	t.Run("Valid Slice", func(t *testing.T) {
		// Slice [1:3, 0:2, 0:2] from [4, 3, 2]
		starts := []int64{1, 0, 0}
		ends := []int64{3, 2, 2}

		slice, err := tensor.Slice(starts, ends)
		if err != nil {
			t.Fatalf("Failed to create slice: %v", err)
		}
		defer slice.Free()

		expectedShape := TensorShape{2, 2, 2} // [3-1, 2-0, 2-0]
		if slice.Shape().String() != expectedShape.String() {
			t.Errorf("Expected slice shape %v, got %v", expectedShape, slice.Shape())
		}

		if slice.NumElements() != 8 {
			t.Errorf("Expected 8 elements in slice, got %d", slice.NumElements())
		}

		if slice.ownsMemory {
			t.Error("Slice should not own memory")
		}
	})

	t.Run("Negative Index Slice", func(t *testing.T) {
		// Slice [-2:, :, :] equivalent to [2:4, 0:3, 0:2]
		starts := []int64{-2, 0, 0}
		ends := []int64{-1, 3, 2}

		slice, err := tensor.Slice(starts, ends)
		if err != nil {
			t.Fatalf("Failed to create slice with negative indices: %v", err)
		}
		defer slice.Free()

		expectedShape := TensorShape{1, 3, 2} // [4-1-2, 3-0, 2-0] = [1, 3, 2] where -1 becomes 3
		if slice.Shape().String() != expectedShape.String() {
			t.Errorf("Expected slice shape %v, got %v", expectedShape, slice.Shape())
		}
	})

	t.Run("Invalid Slice", func(t *testing.T) {
		// Wrong number of dimensions
		_, err := tensor.Slice([]int64{0, 0}, []int64{2, 2})
		if err == nil {
			t.Error("Expected error for wrong number of slice dimensions")
		}

		// Out of bounds
		_, err = tensor.Slice([]int64{0, 0, 0}, []int64{5, 2, 2})
		if err == nil {
			t.Error("Expected error for out of bounds slice")
		}

		// Invalid range
		_, err = tensor.Slice([]int64{2, 0, 0}, []int64{1, 2, 2})
		if err == nil {
			t.Error("Expected error for invalid slice range")
		}
	})
}

// TestTensorMemoryManagement tests memory management and reference counting
func TestTensorMemoryManagement(t *testing.T) {
	t.Run("Reference Counting", func(t *testing.T) {
		tensor, err := NewTensor(TensorShape{10, 10}, TensorFloat32, DeviceCPU, 0)
		if err != nil {
			t.Fatalf("Failed to create tensor: %v", err)
		}

		// Create multiple views
		view1, err := tensor.View(TensorShape{100})
		if err != nil {
			t.Fatalf("Failed to create view1: %v", err)
		}

		view2, err := tensor.View(TensorShape{20, 5})
		if err != nil {
			t.Fatalf("Failed to create view2: %v", err)
		}

		// Reference count should be 3 (original + 2 views)
		if *tensor.refCount != 3 {
			t.Errorf("Expected reference count 3, got %d", *tensor.refCount)
		}

		// Free one view
		view1.Free()
		if *tensor.refCount != 2 {
			t.Errorf("Expected reference count 2 after freeing view1, got %d", *tensor.refCount)
		}

		// Free second view
		view2.Free()
		if *tensor.refCount != 1 {
			t.Errorf("Expected reference count 1 after freeing view2, got %d", *tensor.refCount)
		}

		// Free original tensor
		tensor.Free()
	})

	t.Run("Memory Ownership", func(t *testing.T) {
		tensor, err := NewTensor(TensorShape{5, 5}, TensorFloat32, DeviceCPU, 0)
		if err != nil {
			t.Fatalf("Failed to create tensor: %v", err)
		}

		if !tensor.ownsMemory {
			t.Error("Original tensor should own memory")
		}

		view, err := tensor.View(TensorShape{25})
		if err != nil {
			t.Fatalf("Failed to create view: %v", err)
		}

		if view.ownsMemory {
			t.Error("View should not own memory")
		}

		view.Free()
		tensor.Free()
	})
}

// TestTensorCloning tests tensor cloning functionality
func TestTensorCloning(t *testing.T) {
	t.Run("CPU Tensor Clone", func(t *testing.T) {
		original, err := NewTensor(TensorShape{2, 3}, TensorFloat32, DeviceCPU, 0)
		if err != nil {
			t.Fatalf("Failed to create original tensor: %v", err)
		}
		defer original.Free()

		clone, err := original.Clone()
		if err != nil {
			t.Fatalf("Failed to clone tensor: %v", err)
		}
		defer clone.Free()

		// Check that shapes match
		if clone.Shape().String() != original.Shape().String() {
			t.Error("Clone should have same shape as original")
		}

		// Check that data types match
		if clone.DataType() != original.DataType() {
			t.Error("Clone should have same data type as original")
		}

		// Check that they have different data pointers (deep copy)
		if clone.Data() == original.Data() {
			t.Error("Clone should have different data pointer than original")
		}

		// Check that clone owns its memory
		if !clone.ownsMemory {
			t.Error("Clone should own its memory")
		}

		// Check reference counts are independent
		if clone.refCount == original.refCount {
			t.Error("Clone should have independent reference count")
		}
	})

	t.Run("GPU Tensor Clone Not Implemented", func(t *testing.T) {
		// This test verifies that GPU cloning returns appropriate error
		// until GPU operations are implemented
		// For now, create a CPU tensor and pretend it's GPU for testing
		gpu_tensor, err := NewTensor(TensorShape{2, 2}, TensorFloat32, DeviceCPU, 0)
		if err != nil {
			t.Fatalf("Failed to create tensor: %v", err)
		}
		defer gpu_tensor.Free()

		// Manually set device to GPU to test the error path
		gpu_tensor.device = DeviceGPU

		_, err = gpu_tensor.Clone()
		if err == nil {
			t.Error("Expected error for GPU tensor cloning (not yet implemented)")
		}
	})
}

// TestTensorString tests string representation
func TestTensorString(t *testing.T) {
	tensor, err := NewTensor(TensorShape{2, 3, 4}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	str := tensor.String()
	expected := "Tensor(shape=[2 3 4], dtype=float32, device=cpu, size=96 bytes)"
	if str != expected {
		t.Errorf("Expected string %q, got %q", expected, str)
	}
}

// TestTensorContiguity tests contiguity checking
func TestTensorContiguity(t *testing.T) {
	tensor, err := NewTensor(TensorShape{2, 3, 4}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Original tensor should be contiguous
	if !tensor.IsContiguous() {
		t.Error("Original tensor should be contiguous")
	}

	// View with same layout should be contiguous
	view, err := tensor.View(TensorShape{6, 4})
	if err != nil {
		t.Fatalf("Failed to create view: %v", err)
	}
	defer view.Free()

	if !view.IsContiguous() {
		t.Error("View with compatible layout should be contiguous")
	}
}

// BenchmarkTensorCreation benchmarks tensor creation performance
func BenchmarkTensorCreation(b *testing.B) {
	shapes := []TensorShape{
		{100},
		{10, 10},
		{32, 32},
		{8, 8, 8},
		{4, 4, 4, 4},
	}

	for _, shape := range shapes {
		b.Run(fmt.Sprintf("Shape%v", shape), func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
				if err != nil {
					b.Fatalf("Failed to create tensor: %v", err)
				}
				tensor.Free()
			}
		})
	}
}

// BenchmarkTensorIndexing benchmarks tensor indexing performance
func BenchmarkTensorIndexing(b *testing.B) {
	tensor, err := NewTensor(TensorShape{100, 100, 100}, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		b.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := tensor.linearIndex(50, 50, 50)
		if err != nil {
			b.Fatalf("Indexing failed: %v", err)
		}
	}
}
