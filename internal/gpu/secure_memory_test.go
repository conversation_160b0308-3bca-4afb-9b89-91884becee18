package gpu

import (
	"testing"
)

func TestSecureAllocate_ValidSize(t *testing.T) {
	mgr := NewSecureMemoryManager(SecurityConfig{EnableMemorySecurity: true})
	buf, err := mgr.SecureAllocate(128)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if len(buf) != 128 {
		t.<PERSON><PERSON>("Expected buffer of size 128, got %d", len(buf))
	}
}

func TestSecureAllocate_InvalidSize(t *testing.T) {
	mgr := NewSecureMemoryManager(SecurityConfig{EnableMemorySecurity: true})
	_, err := mgr.SecureAllocate(0)
	if err == nil {
		t.Error("Expected error for zero size allocation")
	}
	_, err = mgr.SecureAllocate(-10)
	if err == nil {
		t.Error("Expected error for negative size allocation")
	}
}

func TestSecureSanitize(t *testing.T) {
	mgr := NewSecureMemoryManager(SecurityConfig{EnableMemorySecurity: true})
	buf, _ := mgr.SecureAllocate(16)
	for i := range buf {
		buf[i] = byte(i + 1)
	}
	err := mgr.SecureSanitize(buf)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	for i, b := range buf {
		if b != 0 {
			t.Errorf("Expected buf[%d] to be 0 after sanitize, got %d", i, b)
		}
	}
}

func TestSecureFree(t *testing.T) {
	mgr := NewSecureMemoryManager(SecurityConfig{EnableMemorySecurity: true})
	buf, _ := mgr.SecureAllocate(8)
	for i := range buf {
		buf[i] = 0xFF
	}
	err := mgr.SecureFree(buf)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	for i, b := range buf {
		if b != 0 {
			t.Errorf("Expected buf[%d] to be 0 after free, got %d", i, b)
		}
	}
}

func TestSecureFree_Nil(t *testing.T) {
	mgr := NewSecureMemoryManager(SecurityConfig{EnableMemorySecurity: true})
	err := mgr.SecureFree(nil)
	if err != nil {
		t.Errorf("Expected no error for nil buffer, got: %v", err)
	}
}
