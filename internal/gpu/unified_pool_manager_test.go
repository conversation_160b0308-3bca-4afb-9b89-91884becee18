package gpu

import (
	"context"
	"fmt"
	"log"
	"os"
	"testing"
	"time"
)

// WorkloadType represents different types of GPU workloads for testing
type TestWorkloadType int

const (
	TestWorkloadCompute TestWorkloadType = iota
	TestWorkloadMemoryTransfer
	TestWorkloadInference
	TestWorkloadTraining
	TestWorkloadGraphics
	TestWorkloadGeneral
)

func (wt TestWorkloadType) String() string {
	switch wt {
	case TestWorkloadCompute:
		return "Compute"
	case TestWorkloadMemoryTransfer:
		return "MemoryTransfer"
	case TestWorkloadInference:
		return "Inference"
	case TestWorkloadTraining:
		return "Training"
	case TestWorkloadGraphics:
		return "Graphics"
	case TestWorkloadGeneral:
		return "General"
	default:
		return "Unknown"
	}
}

// TestResourceRequest represents a request for GPU resources
type TestResourceRequest struct {
	RequestID         string
	APIType           APIType
	DeviceID          int // -1 for auto-assignment
	Priority          UnifiedPriority
	WorkloadType      TestWorkloadType
	EstimatedTime     time.Duration
	RequiresDedicated bool
	Constraints       map[string]interface{}
	Context           context.Context
}

// TestResourceAllocation represents an allocated resource
type TestResourceAllocation struct {
	AllocationID    string
	RequestID       string
	APIType         APIType
	DeviceID        int
	ResourceHandle  interface{}
	Priority        UnifiedPriority
	WorkloadType    TestWorkloadType
	AllocatedAt     time.Time
	EstimatedEnd    time.Time
	ActualEnd       *time.Time
	IsActive        bool
	UnifiedResource *UnifiedResource
}

// TestDeviceCapability represents the capabilities of a GPU device
type TestDeviceCapability struct {
	DeviceID             int
	APIType              APIType
	MaxConcurrentStreams int
	ComputeCapability    float64
	MemoryBandwidth      float64
	TensorCoreSupport    bool
	SupportedWorkloads   []TestWorkloadType
	CurrentLoad          float64
	AvailableStreams     int
	QueueDepth           int
}

// TestLoadBalancingStrategy defines different load balancing approaches
type TestLoadBalancingStrategy int

const (
	TestStrategyRoundRobin TestLoadBalancingStrategy = iota
	TestStrategyLeastLoaded
	TestStrategyWeightedCapacity
	TestStrategyWorkloadAware
	TestStrategyLatencyOptimal
)

// MockUnifiedPoolManager provides unified pooling and auto-assignment functionality
type MockUnifiedPoolManager struct {
	devices               map[int]*TestDeviceCapability
	activeAllocations     map[string]*TestResourceAllocation
	requestQueue          []*TestResourceRequest
	strategy              TestLoadBalancingStrategy
	totalAllocations      int64
	successfulAllocations int64
	logger                *log.Logger
}

// NewMockUnifiedPoolManager creates a new mock unified pool manager for testing
func NewMockUnifiedPoolManager(logger *log.Logger) *MockUnifiedPoolManager {
	return &MockUnifiedPoolManager{
		devices:           make(map[int]*TestDeviceCapability),
		activeAllocations: make(map[string]*TestResourceAllocation),
		requestQueue:      make([]*TestResourceRequest, 0),
		strategy:          TestStrategyWeightedCapacity,
		logger:            logger,
	}
}

// Initialize sets up mock devices for testing
func (upm *MockUnifiedPoolManager) Initialize() error {
	// Mock CUDA device
	upm.devices[0] = &TestDeviceCapability{
		DeviceID:             0,
		APIType:              APICuda,
		MaxConcurrentStreams: 32,
		ComputeCapability:    8.6,
		MemoryBandwidth:      900.0,
		TensorCoreSupport:    true,
		SupportedWorkloads:   []TestWorkloadType{TestWorkloadCompute, TestWorkloadTraining, TestWorkloadInference, TestWorkloadMemoryTransfer, TestWorkloadGeneral},
		CurrentLoad:          0.0,
		AvailableStreams:     32,
		QueueDepth:           0,
	}

	// Mock OpenCL device
	upm.devices[1] = &TestDeviceCapability{
		DeviceID:             1,
		APIType:              APIOpenCL,
		MaxConcurrentStreams: 16,
		ComputeCapability:    7.5,
		MemoryBandwidth:      600.0,
		TensorCoreSupport:    false,
		SupportedWorkloads:   []TestWorkloadType{TestWorkloadCompute, TestWorkloadInference, TestWorkloadMemoryTransfer, TestWorkloadGeneral},
		CurrentLoad:          0.0,
		AvailableStreams:     16,
		QueueDepth:           0,
	}

	// Mock Metal device
	upm.devices[2] = &TestDeviceCapability{
		DeviceID:             2,
		APIType:              APIMetal,
		MaxConcurrentStreams: 24,
		ComputeCapability:    9.0,
		MemoryBandwidth:      800.0,
		TensorCoreSupport:    true,
		SupportedWorkloads:   []TestWorkloadType{TestWorkloadCompute, TestWorkloadGraphics, TestWorkloadInference, TestWorkloadGeneral},
		CurrentLoad:          0.0,
		AvailableStreams:     24,
		QueueDepth:           0,
	}

	upm.logger.Printf("Mock Unified Pool Manager initialized with %d devices", len(upm.devices))
	return nil
}

// RequestResource allocates a resource based on the request
func (upm *MockUnifiedPoolManager) RequestResource(req *TestResourceRequest) (*TestResourceAllocation, error) {
	if req.RequestID == "" {
		req.RequestID = "test_req_" + time.Now().Format("20060102150405")
	}

	upm.totalAllocations++

	// Select optimal device
	deviceID, apiType, err := upm.selectOptimalDevice(req)
	if err != nil {
		return nil, err
	}

	// Create mock resource handle
	resourceHandle := upm.createMockResource(apiType, deviceID)
	unifiedRes := NewUnifiedResource(
		resourceHandle.(string),
		apiType,
		deviceID,
		req.Priority,
		resourceHandle,
	)

	// Create allocation
	allocation := &TestResourceAllocation{
		AllocationID:    "alloc_" + req.RequestID,
		RequestID:       req.RequestID,
		APIType:         apiType,
		DeviceID:        deviceID,
		ResourceHandle:  resourceHandle,
		Priority:        req.Priority,
		WorkloadType:    req.WorkloadType,
		AllocatedAt:     time.Now(),
		EstimatedEnd:    time.Now().Add(req.EstimatedTime),
		IsActive:        true,
		UnifiedResource: unifiedRes,
	}

	upm.activeAllocations[allocation.AllocationID] = allocation
	upm.successfulAllocations++

	// Update device metrics
	if device, exists := upm.devices[deviceID]; exists {
		device.AvailableStreams--
		device.CurrentLoad = upm.calculateDeviceLoad(deviceID)
	}

	upm.logger.Printf("Allocated %s resource on device %d for request %s",
		apiType.String(), deviceID, req.RequestID)

	return allocation, nil
}

// selectOptimalDevice chooses the best device for a request
func (upm *MockUnifiedPoolManager) selectOptimalDevice(req *TestResourceRequest) (int, APIType, error) {
	if req.DeviceID >= 0 {
		// Specific device requested - still need to validate capabilities
		device, exists := upm.devices[req.DeviceID]
		if !exists {
			return -1, APICuda, fmt.Errorf("device %d not available", req.DeviceID)
		}

		// Check if device has available streams
		if device.AvailableStreams <= 0 {
			return -1, APICuda, fmt.Errorf("device %d has no available streams", req.DeviceID)
		}

		// Check API compatibility (APIAny means accept any device API)
		if req.APIType != APIAny && req.APIType != device.APIType {
			return -1, APICuda, fmt.Errorf("device %d does not support API %v", req.DeviceID, req.APIType)
		}

		// Check workload support - TestWorkloadGeneral can handle general workloads only
		workloadSupported := false
		hasGeneral := false
		for _, workload := range device.SupportedWorkloads {
			if workload == req.WorkloadType {
				workloadSupported = true
				break
			}
			if workload == TestWorkloadGeneral {
				hasGeneral = true
			}
		}
		// If device only supports General and request is for General, that's OK
		// But General devices should not handle specialized workloads
		if !workloadSupported && req.WorkloadType == TestWorkloadGeneral && hasGeneral {
			workloadSupported = true
		}
		if !workloadSupported {
			return -1, APICuda, fmt.Errorf("device %d does not support workload %v", req.DeviceID, req.WorkloadType)
		}

		return req.DeviceID, device.APIType, nil
	}

	// Auto-assignment based on strategy
	candidates := upm.getDeviceCandidates(req)
	if len(candidates) == 0 {
		return -1, APICuda, fmt.Errorf("no suitable devices available")
	}

	switch upm.strategy {
	case TestStrategyRoundRobin:
		return upm.selectRoundRobin(candidates), candidates[0].APIType, nil
	case TestStrategyLeastLoaded:
		return upm.selectLeastLoaded(candidates), candidates[0].APIType, nil
	case TestStrategyWeightedCapacity:
		return upm.selectWeightedCapacity(candidates), candidates[0].APIType, nil
	case TestStrategyWorkloadAware:
		return upm.selectWorkloadAware(candidates, req.WorkloadType)
	default:
		return candidates[0].DeviceID, candidates[0].APIType, nil
	}
}

// getDeviceCandidates filters devices that can handle the request
func (upm *MockUnifiedPoolManager) getDeviceCandidates(req *TestResourceRequest) []*TestDeviceCapability {
	var candidates []*TestDeviceCapability

	for _, device := range upm.devices {
		if device.AvailableStreams <= 0 {
			continue
		}

		// Check API compatibility - APIAny means accept any device API
		// Otherwise, request must match device API type exactly
		if req.APIType != APIAny && req.APIType != device.APIType {
			continue
		}

		// Check workload support - TestWorkloadGeneral can handle general workloads only
		workloadSupported := false
		hasGeneral := false
		for _, workload := range device.SupportedWorkloads {
			if workload == req.WorkloadType {
				workloadSupported = true
				break
			}
			if workload == TestWorkloadGeneral {
				hasGeneral = true
			}
		}
		// If device only supports General and request is for General, that's OK
		// But General devices should not handle specialized workloads
		if !workloadSupported && req.WorkloadType == TestWorkloadGeneral && hasGeneral {
			workloadSupported = true
		}
		if !workloadSupported {
			continue
		}

		// Check current load
		if device.CurrentLoad > 0.9 && req.Priority != UnifiedPriorityHigh {
			continue
		}

		candidates = append(candidates, device)
	}

	return candidates
}

// Selection strategies
func (upm *MockUnifiedPoolManager) selectRoundRobin(candidates []*TestDeviceCapability) int {
	minAllocs := int64(^uint64(0) >> 1)
	var selected *TestDeviceCapability

	for _, device := range candidates {
		allocCount := upm.getDeviceAllocationCount(device.DeviceID)
		if allocCount < minAllocs {
			minAllocs = allocCount
			selected = device
		}
	}

	return selected.DeviceID
}

func (upm *MockUnifiedPoolManager) selectLeastLoaded(candidates []*TestDeviceCapability) int {
	bestDevice := candidates[0]
	minLoad := bestDevice.CurrentLoad

	for _, device := range candidates[1:] {
		if device.CurrentLoad < minLoad {
			minLoad = device.CurrentLoad
			bestDevice = device
		}
	}

	return bestDevice.DeviceID
}

func (upm *MockUnifiedPoolManager) selectWeightedCapacity(candidates []*TestDeviceCapability) int {
	type scoredDevice struct {
		device *TestDeviceCapability
		score  float64
	}

	var scored []scoredDevice
	for _, device := range candidates {
		score := device.ComputeCapability*0.4 +
			device.MemoryBandwidth*0.3 +
			float64(device.AvailableStreams)*0.2 +
			(1.0-device.CurrentLoad)*0.1

		scored = append(scored, scoredDevice{device: device, score: score})
	}

	// Find best score
	best := scored[0]
	for _, s := range scored[1:] {
		if s.score > best.score {
			best = s
		}
	}

	return best.device.DeviceID
}

func (upm *MockUnifiedPoolManager) selectWorkloadAware(candidates []*TestDeviceCapability, workload TestWorkloadType) (int, APIType, error) {
	type scoredDevice struct {
		device *TestDeviceCapability
		score  float64
	}

	var scored []scoredDevice
	for _, device := range candidates {
		score := 0.0

		switch workload {
		case TestWorkloadTraining:
			if device.TensorCoreSupport {
				score += 50.0
			}
			score += device.ComputeCapability * 20.0
			score += device.MemoryBandwidth * 15.0
		case TestWorkloadInference:
			score += device.ComputeCapability * 25.0
			score += device.MemoryBandwidth * 10.0
			score += float64(device.AvailableStreams) * 5.0
		case TestWorkloadMemoryTransfer:
			score += device.MemoryBandwidth * 40.0
			score += float64(device.AvailableStreams) * 10.0
		case TestWorkloadCompute:
			score += device.ComputeCapability * 30.0
			score += float64(device.AvailableStreams) * 15.0
		default:
			score += device.ComputeCapability*10.0 + device.MemoryBandwidth*10.0
		}

		// Penalty for high current load
		score *= (1.0 - device.CurrentLoad)

		scored = append(scored, scoredDevice{device: device, score: score})
	}

	// Find best score
	best := scored[0]
	for _, s := range scored[1:] {
		if s.score > best.score {
			best = s
		}
	}

	return best.device.DeviceID, best.device.APIType, nil
}

// createMockResource creates a mock resource handle for testing
func (upm *MockUnifiedPoolManager) createMockResource(apiType APIType, deviceID int) interface{} {
	switch apiType {
	case APICuda:
		return fmt.Sprintf("cuda_stream_%d_%d", deviceID, time.Now().UnixNano())
	case APIOpenCL:
		return fmt.Sprintf("opencl_queue_%d_%d", deviceID, time.Now().UnixNano())
	case APIMetal:
		return fmt.Sprintf("metal_buffer_%d_%d", deviceID, time.Now().UnixNano())
	default:
		return fmt.Sprintf("generic_resource_%d_%d", deviceID, time.Now().UnixNano())
	}
}

// ReleaseResource releases an allocated resource
func (upm *MockUnifiedPoolManager) ReleaseResource(allocationID string) error {
	allocation, exists := upm.activeAllocations[allocationID]
	if !exists {
		return fmt.Errorf("allocation %s not found", allocationID)
	}

	now := time.Now()
	allocation.ActualEnd = &now
	allocation.IsActive = false

	// Update device metrics
	if device, exists := upm.devices[allocation.DeviceID]; exists {
		device.AvailableStreams++
		device.CurrentLoad = upm.calculateDeviceLoad(allocation.DeviceID)
	}

	delete(upm.activeAllocations, allocationID)

	upm.logger.Printf("Released allocation %s for device %d", allocationID, allocation.DeviceID)
	return nil
}

// Utility functions
func (upm *MockUnifiedPoolManager) calculateDeviceLoad(deviceID int) float64 {
	allocCount := float64(upm.getDeviceAllocationCount(deviceID))
	device := upm.devices[deviceID]
	if device == nil {
		return 0.0
	}
	maxStreams := float64(device.MaxConcurrentStreams)
	if maxStreams == 0 {
		return 0.0
	}
	return allocCount / maxStreams
}

func (upm *MockUnifiedPoolManager) getDeviceAllocationCount(deviceID int) int64 {
	count := int64(0)
	for _, allocation := range upm.activeAllocations {
		if allocation.DeviceID == deviceID && allocation.IsActive {
			count++
		}
	}
	return count
}

// SetStrategy sets the load balancing strategy
func (upm *MockUnifiedPoolManager) SetStrategy(strategy TestLoadBalancingStrategy) {
	upm.strategy = strategy
}

// GetManagerStats returns statistics
func (upm *MockUnifiedPoolManager) GetManagerStats() map[string]interface{} {
	deviceStats := make(map[string]interface{})
	for deviceID, device := range upm.devices {
		deviceStats[fmt.Sprintf("device_%d", deviceID)] = map[string]interface{}{
			"api_type":          device.APIType.String(),
			"current_load":      device.CurrentLoad,
			"available_streams": device.AvailableStreams,
			"queue_depth":       device.QueueDepth,
		}
	}

	successRate := 0.0
	if upm.totalAllocations > 0 {
		successRate = float64(upm.successfulAllocations) / float64(upm.totalAllocations)
	}

	return map[string]interface{}{
		"total_allocations":      upm.totalAllocations,
		"successful_allocations": upm.successfulAllocations,
		"success_rate":           successRate,
		"active_allocations":     len(upm.activeAllocations),
		"devices":                deviceStats,
	}
}

// Test functions
func TestNewMockUnifiedPoolManager(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewMockUnifiedPoolManager(logger)

	if manager == nil {
		t.Fatal("NewMockUnifiedPoolManager returned nil")
	}

	if err := manager.Initialize(); err != nil {
		t.Fatalf("Failed to initialize manager: %v", err)
	}

	stats := manager.GetManagerStats()
	if stats["total_allocations"].(int64) != 0 {
		t.Error("Initial total allocations should be 0")
	}
}

func TestBasicResourceAllocation(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewMockUnifiedPoolManager(logger)

	if err := manager.Initialize(); err != nil {
		t.Fatalf("Failed to initialize manager: %v", err)
	}

	// Test CUDA allocation
	req := &TestResourceRequest{
		RequestID:     "test_cuda_req",
		APIType:       APICuda,
		DeviceID:      -1, // Auto-assign
		Priority:      UnifiedPriorityMedium,
		WorkloadType:  TestWorkloadCompute,
		EstimatedTime: time.Second * 5,
	}

	allocation, err := manager.RequestResource(req)
	if err != nil {
		t.Fatalf("Failed to allocate CUDA resource: %v", err)
	}

	if allocation == nil {
		t.Fatal("Allocation returned nil")
	}

	if allocation.RequestID != req.RequestID {
		t.Errorf("Expected request ID %s, got %s", req.RequestID, allocation.RequestID)
	}

	if allocation.APIType != APICuda {
		t.Errorf("Expected CUDA API type, got %v", allocation.APIType)
	}

	// Test resource release
	if err := manager.ReleaseResource(allocation.AllocationID); err != nil {
		t.Fatalf("Failed to release resource: %v", err)
	}
}

func TestAutoAssignmentStrategies(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewMockUnifiedPoolManager(logger)

	if err := manager.Initialize(); err != nil {
		t.Fatalf("Failed to initialize manager: %v", err)
	}

	strategies := []TestLoadBalancingStrategy{
		TestStrategyRoundRobin,
		TestStrategyLeastLoaded,
		TestStrategyWeightedCapacity,
		TestStrategyWorkloadAware,
	}

	for _, strategy := range strategies {
		manager.SetStrategy(strategy)

		req := &TestResourceRequest{
			RequestID:     fmt.Sprintf("test_req_%d", strategy),
			APIType:       APIAny, // Allow any API
			DeviceID:      -1,     // Auto-assign
			Priority:      UnifiedPriorityMedium,
			WorkloadType:  TestWorkloadCompute,
			EstimatedTime: time.Second * 2,
		}

		allocation, err := manager.RequestResource(req)
		if err != nil {
			t.Errorf("Failed to allocate with strategy %d: %v", strategy, err)
			continue
		}

		if allocation == nil {
			t.Errorf("Allocation returned nil for strategy %d", strategy)
			continue
		}

		t.Logf("Strategy %d assigned device %d (API: %s)",
			strategy, allocation.DeviceID, allocation.APIType.String())

		// Release the resource
		if err := manager.ReleaseResource(allocation.AllocationID); err != nil {
			t.Errorf("Failed to release resource for strategy %d: %v", strategy, err)
		}
	}
}

func TestWorkloadAwareAllocation(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewMockUnifiedPoolManager(logger)

	if err := manager.Initialize(); err != nil {
		t.Fatalf("Failed to initialize manager: %v", err)
	}

	manager.SetStrategy(TestStrategyWorkloadAware)

	workloads := []TestWorkloadType{
		TestWorkloadTraining,
		TestWorkloadInference,
		TestWorkloadMemoryTransfer,
		TestWorkloadCompute,
		TestWorkloadGraphics,
	}

	for _, workload := range workloads {
		req := &TestResourceRequest{
			RequestID:     fmt.Sprintf("test_workload_%s", workload.String()),
			APIType:       APIAny, // Allow any API
			DeviceID:      -1,     // Auto-assign
			Priority:      UnifiedPriorityMedium,
			WorkloadType:  workload,
			EstimatedTime: time.Second * 3,
		}

		allocation, err := manager.RequestResource(req)
		if err != nil {
			t.Errorf("Failed to allocate for workload %s: %v", workload.String(), err)
			continue
		}

		if allocation == nil {
			t.Errorf("Allocation returned nil for workload %s", workload.String())
			continue
		}

		t.Logf("Workload %s assigned to device %d (API: %s)",
			workload.String(), allocation.DeviceID, allocation.APIType.String())

		// Release the resource
		if err := manager.ReleaseResource(allocation.AllocationID); err != nil {
			t.Errorf("Failed to release resource for workload %s: %v", workload.String(), err)
		}
	}
}

func TestConcurrentAllocations(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewMockUnifiedPoolManager(logger)

	if err := manager.Initialize(); err != nil {
		t.Fatalf("Failed to initialize manager: %v", err)
	}

	// Allocate multiple resources concurrently
	numAllocs := 10
	allocations := make([]*TestResourceAllocation, 0, numAllocs)

	for i := 0; i < numAllocs; i++ {
		req := &TestResourceRequest{
			RequestID:     fmt.Sprintf("concurrent_req_%d", i),
			APIType:       APIAny, // Allow any API
			DeviceID:      -1,     // Auto-assign
			Priority:      UnifiedPriorityMedium,
			WorkloadType:  TestWorkloadCompute,
			EstimatedTime: time.Second * 1,
		}

		allocation, err := manager.RequestResource(req)
		if err != nil {
			t.Errorf("Failed to allocate resource %d: %v", i, err)
			continue
		}

		allocations = append(allocations, allocation)
	}

	// Verify load distribution
	stats := manager.GetManagerStats()
	t.Logf("Concurrent allocations stats: %+v", stats)

	// Release all allocations
	for _, allocation := range allocations {
		if err := manager.ReleaseResource(allocation.AllocationID); err != nil {
			t.Errorf("Failed to release allocation %s: %v", allocation.AllocationID, err)
		}
	}

	// Verify cleanup
	finalStats := manager.GetManagerStats()
	if finalStats["active_allocations"].(int) != 0 {
		t.Error("Not all allocations were properly released")
	}
}

func TestSpecificDeviceAllocation(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewMockUnifiedPoolManager(logger)

	if err := manager.Initialize(); err != nil {
		t.Fatalf("Failed to initialize manager: %v", err)
	}

	// Test specific device allocation
	req := &TestResourceRequest{
		RequestID:     "specific_device_req",
		APIType:       APIOpenCL,
		DeviceID:      1, // Specific OpenCL device
		Priority:      UnifiedPriorityHigh,
		WorkloadType:  TestWorkloadCompute,
		EstimatedTime: time.Second * 2,
	}

	allocation, err := manager.RequestResource(req)
	if err != nil {
		t.Fatalf("Failed to allocate specific device: %v", err)
	}

	if allocation.DeviceID != 1 {
		t.Errorf("Expected device 1, got device %d", allocation.DeviceID)
	}

	if allocation.APIType != APIOpenCL {
		t.Errorf("Expected OpenCL API, got %v", allocation.APIType)
	}

	// Release the resource
	if err := manager.ReleaseResource(allocation.AllocationID); err != nil {
		t.Fatalf("Failed to release resource: %v", err)
	}
}

func TestAllocationFailures(t *testing.T) {
	logger := log.New(os.Stdout, "test: ", log.LstdFlags)
	manager := NewMockUnifiedPoolManager(logger)

	if err := manager.Initialize(); err != nil {
		t.Fatalf("Failed to initialize manager: %v", err)
	}

	// Test allocation with invalid device
	req := &TestResourceRequest{
		RequestID:     "invalid_device_req",
		APIType:       APICuda,
		DeviceID:      999, // Non-existent device
		Priority:      UnifiedPriorityMedium,
		WorkloadType:  TestWorkloadCompute,
		EstimatedTime: time.Second * 1,
	}

	_, err := manager.RequestResource(req)
	if err == nil {
		t.Error("Expected error for invalid device, got nil")
	}

	// Test allocation with unsupported workload
	req = &TestResourceRequest{
		RequestID:     "unsupported_workload_req",
		APIType:       APIOpenCL,
		DeviceID:      1,
		Priority:      UnifiedPriorityMedium,
		WorkloadType:  TestWorkloadGraphics, // OpenCL device doesn't support graphics
		EstimatedTime: time.Second * 1,
	}

	_, err = manager.RequestResource(req)
	if err == nil {
		t.Error("Expected error for unsupported workload, got nil")
	}
}

func BenchmarkResourceAllocation(b *testing.B) {
	logger := log.New(os.Stdout, "bench: ", log.LstdFlags)
	manager := NewMockUnifiedPoolManager(logger)

	if err := manager.Initialize(); err != nil {
		b.Fatalf("Failed to initialize manager: %v", err)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		req := &TestResourceRequest{
			RequestID:     fmt.Sprintf("bench_req_%d", i),
			APIType:       APIAny,
			DeviceID:      -1,
			Priority:      UnifiedPriorityMedium,
			WorkloadType:  TestWorkloadCompute,
			EstimatedTime: time.Millisecond * 100,
		}

		allocation, err := manager.RequestResource(req)
		if err != nil {
			b.Fatalf("Allocation failed: %v", err)
		}

		if err := manager.ReleaseResource(allocation.AllocationID); err != nil {
			b.Fatalf("Release failed: %v", err)
		}
	}
}
