package gpu

type IsolationConfig struct {
	EnableHardwareVirtualization bool
	EnableContainerIsolation     bool
	// Add more as needed
}

type IsolationManager interface {
	IsolateWorkload(workloadID string) error
	ReleaseIsolation(workloadID string) error
	GetIsolationStatus(workloadID string) (string, error)
}

type DefaultIsolationManager struct {
	config IsolationConfig
}

func NewIsolationManager(cfg SecurityConfig) IsolationManager {
	return &DefaultIsolationManager{
		config: IsolationConfig{
			EnableHardwareVirtualization: cfg.EnableIsolation,
			EnableContainerIsolation:     cfg.EnableIsolation,
		},
	}
}

func (m *DefaultIsolationManager) IsolateWorkload(workloadID string) error {
	// TODO: Integrate with vGPU/MxGPU/GVT-g/Metal or container runtime
	return nil
}

func (m *DefaultIsolationManager) ReleaseIsolation(workloadID string) error {
	// TODO: Release isolation
	return nil
}

func (m *DefaultIsolationManager) GetIsolationStatus(workloadID string) (string, error) {
	// TODO: Query isolation status
	return "isolated", nil
}
