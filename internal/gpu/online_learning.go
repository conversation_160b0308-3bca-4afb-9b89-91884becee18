package gpu

import (
	"context"
	"fmt"
	"log"
	"math"
	"sync"
	"time"
)

// OnlineLearningConfig configures online learning behavior
type OnlineLearningConfig struct {
	// Incremental learning settings
	EnableIncrementalLearning bool          `json:"enable_incremental_learning"`
	LearningRate              float64       `json:"learning_rate"`
	LearningRateDecay         float64       `json:"learning_rate_decay"`
	MinLearningRate           float64       `json:"min_learning_rate"`
	BatchSize                 int           `json:"batch_size"`
	UpdateInterval            time.Duration `json:"update_interval"`

	// Concept drift detection
	EnableDriftDetection bool    `json:"enable_drift_detection"`
	DriftThreshold       float64 `json:"drift_threshold"`
	DriftWindowSize      int     `json:"drift_window_size"`
	DriftConfidenceLevel float64 `json:"drift_confidence_level"`
	DriftDetectionMethod string  `json:"drift_detection_method"` // "adwin", "page_hinkley", "spc"

	// Model versioning
	EnableModelVersioning bool          `json:"enable_model_versioning"`
	MaxModelVersions      int           `json:"max_model_versions"`
	ModelEvaluationPeriod time.Duration `json:"model_evaluation_period"`
	PerformanceThreshold  float64       `json:"performance_threshold"`
	RollbackOnDegradation bool          `json:"rollback_on_degradation"`

	// Experience replay
	EnableExperienceReplay bool `json:"enable_experience_replay"`
	ReplayBufferSize       int  `json:"replay_buffer_size"`
	ReplayBatchSize        int  `json:"replay_batch_size"`
	ReplayFrequency        int  `json:"replay_frequency"` // Every N updates

	// Streaming data
	EnableStreaming       bool          `json:"enable_streaming"`
	StreamBufferSize      int           `json:"stream_buffer_size"`
	StreamProcessingDelay time.Duration `json:"stream_processing_delay"`
	BackpressureThreshold int           `json:"backpressure_threshold"`
}

// DefaultOnlineLearningConfig returns default configuration
func DefaultOnlineLearningConfig() OnlineLearningConfig {
	return OnlineLearningConfig{
		EnableIncrementalLearning: true,
		LearningRate:              0.01,
		LearningRateDecay:         0.999,
		MinLearningRate:           0.001,
		BatchSize:                 10,
		UpdateInterval:            time.Minute * 5,

		EnableDriftDetection: true,
		DriftThreshold:       0.1,
		DriftWindowSize:      100,
		DriftConfidenceLevel: 0.95,
		DriftDetectionMethod: "adwin",

		EnableModelVersioning: true,
		MaxModelVersions:      10,
		ModelEvaluationPeriod: time.Hour,
		PerformanceThreshold:  0.8,
		RollbackOnDegradation: true,

		EnableExperienceReplay: true,
		ReplayBufferSize:       1000,
		ReplayBatchSize:        20,
		ReplayFrequency:        5,

		EnableStreaming:       true,
		StreamBufferSize:      500,
		StreamProcessingDelay: time.Second,
		BackpressureThreshold: 400,
	}
}

// OnlineLearningManager manages online learning for workload prediction
type OnlineLearningManager struct {
	config           OnlineLearningConfig
	predictor        *WorkloadPredictor
	driftDetector    DriftDetector
	modelRegistry    *ModelRegistry
	experienceBuffer *ExperienceBuffer
	streamProcessor  *StreamProcessor
	parameterServer  *ParameterServer

	// State management
	isRunning       bool
	updateCount     int64
	lastUpdate      time.Time
	currentAccuracy float64

	// Synchronization
	mu       sync.RWMutex
	stopChan chan struct{}
	wg       sync.WaitGroup

	logger *log.Logger
}

// NewOnlineLearningManager creates a new online learning manager
func NewOnlineLearningManager(
	config OnlineLearningConfig,
	predictor *WorkloadPredictor,
	logger *log.Logger,
) (*OnlineLearningManager, error) {
	olm := &OnlineLearningManager{
		config:    config,
		predictor: predictor,
		logger:    logger,
		stopChan:  make(chan struct{}),
	}

	// Initialize drift detector
	var err error
	olm.driftDetector, err = NewDriftDetector(config.DriftDetectionMethod, DriftDetectorConfig{
		WindowSize:      config.DriftWindowSize,
		Threshold:       config.DriftThreshold,
		ConfidenceLevel: config.DriftConfidenceLevel,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create drift detector: %w", err)
	}

	// Initialize model registry
	if config.EnableModelVersioning {
		olm.modelRegistry = NewModelRegistry(ModelRegistryConfig{
			MaxVersions:          config.MaxModelVersions,
			EvaluationPeriod:     config.ModelEvaluationPeriod,
			PerformanceThreshold: config.PerformanceThreshold,
		})
	}

	// Initialize experience buffer
	if config.EnableExperienceReplay {
		olm.experienceBuffer = NewExperienceBuffer(config.ReplayBufferSize)
	}

	// Initialize stream processor
	if config.EnableStreaming {
		olm.streamProcessor = NewStreamProcessor(StreamProcessorConfig{
			BufferSize:            config.StreamBufferSize,
			ProcessingDelay:       config.StreamProcessingDelay,
			BackpressureThreshold: config.BackpressureThreshold,
		})
	}

	// Initialize parameter server
	olm.parameterServer = NewParameterServer()

	return olm, nil
}

// Start begins online learning operations
func (olm *OnlineLearningManager) Start(ctx context.Context) error {
	olm.mu.Lock()
	defer olm.mu.Unlock()

	if olm.isRunning {
		return fmt.Errorf("online learning manager already running")
	}

	olm.isRunning = true
	olm.lastUpdate = time.Now()

	// Start stream processor if enabled
	if olm.config.EnableStreaming && olm.streamProcessor != nil {
		olm.wg.Add(1)
		go olm.runStreamProcessor(ctx)
	}

	// Start periodic update loop
	olm.wg.Add(1)
	go olm.runUpdateLoop(ctx)

	// Start model evaluation loop if versioning is enabled
	if olm.config.EnableModelVersioning && olm.modelRegistry != nil {
		olm.wg.Add(1)
		go olm.runModelEvaluationLoop(ctx)
	}

	olm.logger.Println("Online learning manager started")
	return nil
}

// Stop stops online learning operations
func (olm *OnlineLearningManager) Stop() error {
	olm.mu.Lock()
	defer olm.mu.Unlock()

	if !olm.isRunning {
		return nil
	}

	olm.isRunning = false
	close(olm.stopChan)
	olm.wg.Wait()

	olm.logger.Println("Online learning manager stopped")
	return nil
}

// ProcessDataPoint processes a new data point for online learning
func (olm *OnlineLearningManager) ProcessDataPoint(dataPoint WorkloadDataPoint) error {
	olm.mu.Lock()
	defer olm.mu.Unlock()

	// Add to experience buffer if enabled
	if olm.config.EnableExperienceReplay && olm.experienceBuffer != nil {
		olm.experienceBuffer.Add(dataPoint)
	}

	// Add to stream processor if enabled
	if olm.config.EnableStreaming && olm.streamProcessor != nil {
		return olm.streamProcessor.AddDataPoint(dataPoint)
	}

	// Process immediately if streaming is disabled
	return olm.processDataPointInternal(dataPoint)
}

// processDataPointInternal handles the actual processing logic
func (olm *OnlineLearningManager) processDataPointInternal(dataPoint WorkloadDataPoint) error {
	// Generate prediction for drift detection
	if olm.config.EnableDriftDetection {
		prediction, err := olm.predictor.GetPrediction(time.Hour)
		if err == nil {
			predictionError := math.Abs(float64(prediction.PredictedQueue - dataPoint.QueueLength))

			// Check for concept drift
			if olm.driftDetector.AddValue(predictionError) {
				olm.logger.Printf("Concept drift detected! Triggering model retraining")
				return olm.handleConceptDrift(dataPoint)
			}
		}
	}

	// Perform incremental learning update
	if olm.config.EnableIncrementalLearning {
		return olm.performIncrementalUpdate(dataPoint)
	}

	return nil
}

// performIncrementalUpdate performs incremental model updates
func (olm *OnlineLearningManager) performIncrementalUpdate(dataPoint WorkloadDataPoint) error {
	// Check if it's time for an update
	if time.Since(olm.lastUpdate) < olm.config.UpdateInterval {
		return nil
	}

	// Collect batch of recent data
	var batch []WorkloadDataPoint
	if olm.config.EnableExperienceReplay && olm.experienceBuffer != nil {
		// Use experience replay
		replayBatch := olm.experienceBuffer.Sample(olm.config.ReplayBatchSize)
		batch = append(batch, replayBatch...)
	}

	// Add current data point
	batch = append(batch, dataPoint)

	// Perform incremental learning on the batch
	if len(batch) >= olm.config.BatchSize {
		err := olm.updateModelIncremental(batch)
		if err != nil {
			return fmt.Errorf("incremental update failed: %w", err)
		}

		olm.updateCount++
		olm.lastUpdate = time.Now()

		// Update parameter server
		if olm.parameterServer != nil {
			params := olm.extractModelParameters()
			olm.parameterServer.Update(params)
		}
	}

	return nil
}

// GetStatus returns the current status of online learning
func (olm *OnlineLearningManager) GetStatus() OnlineLearningStatus {
	olm.mu.RLock()
	defer olm.mu.RUnlock()

	status := OnlineLearningStatus{
		IsRunning:       olm.isRunning,
		UpdateCount:     olm.updateCount,
		LastUpdate:      olm.lastUpdate,
		CurrentAccuracy: olm.currentAccuracy,
		LearningRate:    olm.config.LearningRate,
	}

	if olm.modelRegistry != nil {
		status.ModelVersions = olm.modelRegistry.GetVersionCount()
		if best := olm.modelRegistry.GetBestVersion(); best != nil {
			status.BestModelAccuracy = best.Accuracy
		}
	}

	if olm.streamProcessor != nil {
		status.StreamBufferSize = olm.streamProcessor.GetBufferSize()
		status.BackpressureActive = olm.streamProcessor.IsBackpressureActive()
	}

	if olm.experienceBuffer != nil {
		status.ExperienceBufferSize = olm.experienceBuffer.Size()
	}

	return status
}

// OnlineLearningStatus represents the current status of online learning
type OnlineLearningStatus struct {
	IsRunning            bool      `json:"is_running"`
	UpdateCount          int64     `json:"update_count"`
	LastUpdate           time.Time `json:"last_update"`
	CurrentAccuracy      float64   `json:"current_accuracy"`
	LearningRate         float64   `json:"learning_rate"`
	ModelVersions        int       `json:"model_versions"`
	BestModelAccuracy    float64   `json:"best_model_accuracy"`
	StreamBufferSize     int       `json:"stream_buffer_size"`
	BackpressureActive   bool      `json:"backpressure_active"`
	ExperienceBufferSize int       `json:"experience_buffer_size"`
}

// Helper methods for internal operations
func (olm *OnlineLearningManager) updateModelIncremental(batch []WorkloadDataPoint) error {
	// Get current model accuracy before update
	oldAccuracy := olm.predictor.model.GetAccuracy()

	// Train on the new batch (this will be incremental for supporting models)
	err := olm.predictor.model.Train(batch)
	if err != nil {
		return err
	}

	// Get new accuracy
	newAccuracy := olm.predictor.model.GetAccuracy()
	olm.currentAccuracy = newAccuracy

	// Check for performance degradation
	if olm.config.RollbackOnDegradation && newAccuracy < oldAccuracy*olm.config.PerformanceThreshold {
		olm.logger.Printf("Performance degradation detected: %.4f -> %.4f", oldAccuracy, newAccuracy)

		if olm.modelRegistry != nil {
			// Attempt to rollback to a previous version
			return olm.rollbackToPreviousModel()
		}
	}

	// Update learning rate with decay
	olm.updateLearningRate()

	return nil
}

func (olm *OnlineLearningManager) handleConceptDrift(dataPoint WorkloadDataPoint) error {
	olm.logger.Println("Handling concept drift - triggering model retraining")

	// Save current model version if versioning is enabled
	if olm.modelRegistry != nil {
		version := ModelVersion{
			ID:         fmt.Sprintf("v%d_%d", time.Now().Unix(), olm.updateCount),
			Timestamp:  time.Now(),
			Accuracy:   olm.currentAccuracy,
			ModelType:  olm.predictor.config.ModelType,
			Parameters: olm.extractModelParameters(),
			Performance: ModelPerformance{
				Accuracy: olm.currentAccuracy,
				RMSE:     0.0, // Would be calculated in real implementation
				MAE:      0.0, // Would be calculated in real implementation
			},
		}
		olm.modelRegistry.SaveVersion(version)
	}

	// Retrain model with recent data including the new point
	var recentData []WorkloadDataPoint
	if olm.experienceBuffer != nil {
		recentData = olm.experienceBuffer.GetRecent(olm.config.DriftWindowSize)
	}
	recentData = append(recentData, dataPoint)

	// Full retraining on recent data
	return olm.predictor.model.Train(recentData)
}

func (olm *OnlineLearningManager) rollbackToPreviousModel() error {
	if olm.modelRegistry == nil {
		return fmt.Errorf("model registry not available for rollback")
	}

	bestVersion := olm.modelRegistry.GetBestVersion()
	if bestVersion == nil {
		return fmt.Errorf("no previous model version available for rollback")
	}

	olm.logger.Printf("Rolling back to model version: %s", bestVersion.ID)

	// Restore model parameters (simplified - would need model-specific logic)
	olm.restoreModelParameters(bestVersion.Parameters)
	olm.currentAccuracy = bestVersion.Accuracy

	return nil
}

func (olm *OnlineLearningManager) updateLearningRate() {
	currentRate := olm.config.LearningRate * math.Pow(olm.config.LearningRateDecay, float64(olm.updateCount))
	if currentRate < olm.config.MinLearningRate {
		currentRate = olm.config.MinLearningRate
	}
	olm.config.LearningRate = currentRate
}

func (olm *OnlineLearningManager) extractModelParameters() map[string]interface{} {
	// This would be model-specific in a real implementation
	return map[string]interface{}{
		"learning_rate": olm.config.LearningRate,
		"accuracy":      olm.currentAccuracy,
		"update_count":  olm.updateCount,
		"last_update":   olm.lastUpdate,
	}
}

func (olm *OnlineLearningManager) restoreModelParameters(params map[string]interface{}) {
	// This would be model-specific in a real implementation
	if lr, ok := params["learning_rate"].(float64); ok {
		olm.config.LearningRate = lr
	}
	if count, ok := params["update_count"].(int64); ok {
		olm.updateCount = count
	}
}

// Background processing loops
func (olm *OnlineLearningManager) runStreamProcessor(ctx context.Context) {
	defer olm.wg.Done()

	ticker := time.NewTicker(olm.config.StreamProcessingDelay)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-olm.stopChan:
			return
		case <-ticker.C:
			if olm.streamProcessor != nil {
				batch := olm.streamProcessor.ProcessBatch()
				for _, dataPoint := range batch {
					if err := olm.processDataPointInternal(dataPoint); err != nil {
						olm.logger.Printf("Error processing streamed data point: %v", err)
					}
				}
			}
		}
	}
}

func (olm *OnlineLearningManager) runUpdateLoop(ctx context.Context) {
	defer olm.wg.Done()

	ticker := time.NewTicker(olm.config.UpdateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-olm.stopChan:
			return
		case <-ticker.C:
			// Perform periodic maintenance tasks
			olm.performPeriodicMaintenance()
		}
	}
}

func (olm *OnlineLearningManager) runModelEvaluationLoop(ctx context.Context) {
	defer olm.wg.Done()

	ticker := time.NewTicker(olm.config.ModelEvaluationPeriod)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-olm.stopChan:
			return
		case <-ticker.C:
			// Evaluate current model performance
			olm.evaluateModelPerformance()
		}
	}
}

func (olm *OnlineLearningManager) performPeriodicMaintenance() {
	olm.mu.Lock()
	defer olm.mu.Unlock()

	// Clean up old model versions
	if olm.modelRegistry != nil {
		olm.modelRegistry.Cleanup()
	}

	// Update parameter server
	if olm.parameterServer != nil {
		params := olm.extractModelParameters()
		olm.parameterServer.Update(params)
	}

	// Log current status
	olm.logger.Printf("Online learning status - Updates: %d, Accuracy: %.4f, Learning Rate: %.6f",
		olm.updateCount, olm.currentAccuracy, olm.config.LearningRate)
}

func (olm *OnlineLearningManager) evaluateModelPerformance() {
	olm.mu.Lock()
	defer olm.mu.Unlock()

	// Get recent test data for evaluation
	if olm.experienceBuffer == nil {
		return
	}

	testData := olm.experienceBuffer.Sample(50) // Sample for evaluation
	if len(testData) < 10 {
		return
	}

	// Calculate performance metrics
	var totalError float64
	validPredictions := 0

	for _, dataPoint := range testData {
		prediction, err := olm.predictor.GetPrediction(time.Hour)
		if err == nil {
			error := math.Abs(float64(prediction.PredictedQueue - dataPoint.QueueLength))
			totalError += error
			validPredictions++
		}
	}

	if validPredictions > 0 {
		avgError := totalError / float64(validPredictions)
		accuracy := math.Max(0, 1.0-avgError/100.0) // Simplified accuracy calculation
		olm.currentAccuracy = accuracy

		olm.logger.Printf("Model evaluation - Accuracy: %.4f, Avg Error: %.2f", accuracy, avgError)

		// Save performance snapshot if versioning is enabled
		if olm.modelRegistry != nil {
			version := ModelVersion{
				ID:         fmt.Sprintf("eval_%d", time.Now().Unix()),
				Timestamp:  time.Now(),
				Accuracy:   accuracy,
				ModelType:  olm.predictor.config.ModelType,
				Parameters: olm.extractModelParameters(),
				Performance: ModelPerformance{
					Accuracy: accuracy,
					RMSE:     avgError,
					MAE:      avgError,
				},
			}
			olm.modelRegistry.SaveVersion(version)
		}
	}
}
