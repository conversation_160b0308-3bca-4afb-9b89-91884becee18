package gpu

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"net/http"
	"sync"
	"time"
)

// SimpleCommunicationConfig holds basic configuration for cluster communication
type SimpleCommunicationConfig struct {
	Port              int           `json:"port"`
	MaxMessageSize    int           `json:"max_message_size"`
	ConnectionTimeout time.Duration `json:"connection_timeout"`
	HeartbeatInterval time.Duration `json:"heartbeat_interval"`
	RetryAttempts     int           `json:"retry_attempts"`
	RetryBackoff      time.Duration `json:"retry_backoff"`
}

// DefaultSimpleCommunicationConfig returns default configuration
func DefaultSimpleCommunicationConfig() *SimpleCommunicationConfig {
	return &SimpleCommunicationConfig{
		Port:              8080,
		MaxMessageSize:    4 * 1024 * 1024, // 4MB
		ConnectionTimeout: 10 * time.Second,
		HeartbeatInterval: 10 * time.Second,
		RetryAttempts:     3,
		RetryBackoff:      time.Second,
	}
}

// MessageType represents different types of cluster messages
type MessageType string

const (
	MessageTypeTaskAssignment MessageType = "task_assignment"
	MessageTypeTaskStatus     MessageType = "task_status"
	MessageTypeTaskComplete   MessageType = "task_complete"
	MessageTypeNodeStatus     MessageType = "node_status"
	MessageTypeHeartbeat      MessageType = "heartbeat"
	MessageTypeDataTransfer   MessageType = "data_transfer"
	MessageTypeResult         MessageType = "result"
)

// ClusterMessage represents a generic cluster communication message
type ClusterMessage struct {
	ID        string                 `json:"id"`
	Type      MessageType            `json:"type"`
	Source    string                 `json:"source"`
	Target    string                 `json:"target"`
	Timestamp time.Time              `json:"timestamp"`
	Data      map[string]interface{} `json:"data"`
	Priority  int                    `json:"priority"`
}

// TaskAssignmentData represents task assignment message data
type TaskAssignmentData struct {
	TaskID            string    `json:"task_id"`
	TargetNodeID      string    `json:"target_node_id"`
	Priority          int       `json:"priority"`
	MemoryRequirement int64     `json:"memory_requirement"`
	ComputeIntensity  string    `json:"compute_intensity"`
	DataLocation      []string  `json:"data_location"`
	DataSize          int64     `json:"data_size"`
	Deadline          time.Time `json:"deadline"`
}

// NodeStatusData represents node status message data
type NodeStatusData struct {
	NodeID           string    `json:"node_id"`
	Status           string    `json:"status"`
	CPUUsage         float64   `json:"cpu_usage"`
	MemoryUsage      float64   `json:"memory_usage"`
	GPUUsage         []float64 `json:"gpu_usage"`
	NetworkBandwidth float64   `json:"network_bandwidth"`
	ActiveTasks      int       `json:"active_tasks"`
	QueueLength      int       `json:"queue_length"`
	LastHeartbeat    time.Time `json:"last_heartbeat"`
}

// ResultData represents task result message data
type ResultData struct {
	TaskID        string                 `json:"task_id"`
	SourceNodeID  string                 `json:"source_node_id"`
	Success       bool                   `json:"success"`
	ResultData    []byte                 `json:"result_data"`
	ExecutionTime time.Duration          `json:"execution_time"`
	ErrorMessage  string                 `json:"error_message"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// ClusterMessageHandler defines the interface for handling different message types
type ClusterMessageHandler interface {
	HandleMessage(ctx context.Context, msg *ClusterMessage) error
}

// SimpleClusterCommunicator handles cluster communication using HTTP and JSON
type SimpleClusterCommunicator struct {
	nodeID   string
	config   *SimpleCommunicationConfig
	logger   *log.Logger
	server   *http.Server
	handlers map[MessageType]ClusterMessageHandler
	mu       sync.RWMutex
	running  bool
	ctx      context.Context
	cancel   context.CancelFunc
}

// NewSimpleClusterCommunicator creates a new simple cluster communicator
func NewSimpleClusterCommunicator(nodeID string, config *SimpleCommunicationConfig, logger *log.Logger) *SimpleClusterCommunicator {
	if config == nil {
		config = DefaultSimpleCommunicationConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &SimpleClusterCommunicator{
		nodeID:   nodeID,
		config:   config,
		logger:   logger,
		handlers: make(map[MessageType]ClusterMessageHandler),
		ctx:      ctx,
		cancel:   cancel,
	}
}

// RegisterHandler registers a message handler for a specific message type
func (scc *SimpleClusterCommunicator) RegisterHandler(msgType MessageType, handler ClusterMessageHandler) {
	scc.mu.Lock()
	defer scc.mu.Unlock()
	scc.handlers[msgType] = handler
}

// Start starts the communication server
func (scc *SimpleClusterCommunicator) Start() error {
	scc.mu.Lock()
	defer scc.mu.Unlock()

	if scc.running {
		return fmt.Errorf("communicator already running")
	}

	mux := http.NewServeMux()
	mux.HandleFunc("/message", scc.handleMessage)
	mux.HandleFunc("/health", scc.handleHealth)
	mux.HandleFunc("/status", scc.handleStatus)

	scc.server = &http.Server{
		Addr:         fmt.Sprintf(":%d", scc.config.Port),
		Handler:      mux,
		ReadTimeout:  scc.config.ConnectionTimeout,
		WriteTimeout: scc.config.ConnectionTimeout,
	}

	listener, err := net.Listen("tcp", scc.server.Addr)
	if err != nil {
		return fmt.Errorf("failed to listen on port %d: %v", scc.config.Port, err)
	}

	scc.running = true
	scc.logger.Printf("SimpleClusterCommunicator started on port %d", scc.config.Port)

	go func() {
		if err := scc.server.Serve(listener); err != nil && err != http.ErrServerClosed {
			scc.logger.Printf("Server error: %v", err)
		}
	}()

	return nil
}

// Stop stops the communication server
func (scc *SimpleClusterCommunicator) Stop() error {
	scc.mu.Lock()
	defer scc.mu.Unlock()

	if !scc.running {
		return nil
	}

	scc.cancel()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := scc.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("failed to shutdown server: %v", err)
	}

	scc.running = false
	scc.logger.Printf("SimpleClusterCommunicator stopped")
	return nil
}

// IsRunning returns whether the communicator is running
func (scc *SimpleClusterCommunicator) IsRunning() bool {
	scc.mu.RLock()
	defer scc.mu.RUnlock()
	return scc.running
}

// GetNodeID returns the node ID
func (scc *SimpleClusterCommunicator) GetNodeID() string {
	return scc.nodeID
}

// SendMessage sends a message to a target node
func (scc *SimpleClusterCommunicator) SendMessage(ctx context.Context, targetAddr string, msg *ClusterMessage) error {
	msg.Source = scc.nodeID
	msg.Timestamp = time.Now()

	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %v", err)
	}

	if len(data) > scc.config.MaxMessageSize {
		return fmt.Errorf("message size %d exceeds maximum %d", len(data), scc.config.MaxMessageSize)
	}

	url := fmt.Sprintf("http://%s/message", targetAddr)

	for attempt := 0; attempt < scc.config.RetryAttempts; attempt++ {
		req, err := http.NewRequestWithContext(ctx, "POST", url, nil)
		if err != nil {
			return fmt.Errorf("failed to create request: %v", err)
		}

		req.Header.Set("Content-Type", "application/json")

		client := &http.Client{
			Timeout: scc.config.ConnectionTimeout,
		}

		resp, err := client.Do(req)
		if err != nil {
			if attempt < scc.config.RetryAttempts-1 {
				scc.logger.Printf("Send attempt %d failed, retrying: %v", attempt+1, err)
				time.Sleep(scc.config.RetryBackoff * time.Duration(attempt+1))
				continue
			}
			return fmt.Errorf("failed to send message after %d attempts: %v", scc.config.RetryAttempts, err)
		}

		resp.Body.Close()

		if resp.StatusCode == http.StatusOK {
			return nil
		}

		if attempt < scc.config.RetryAttempts-1 {
			scc.logger.Printf("Send attempt %d returned status %d, retrying", attempt+1, resp.StatusCode)
			time.Sleep(scc.config.RetryBackoff * time.Duration(attempt+1))
		}
	}

	return fmt.Errorf("failed to send message after %d attempts", scc.config.RetryAttempts)
}

// BroadcastMessage broadcasts a message to multiple nodes
func (scc *SimpleClusterCommunicator) BroadcastMessage(ctx context.Context, targetAddrs []string, msg *ClusterMessage) error {
	var wg sync.WaitGroup
	errors := make(chan error, len(targetAddrs))

	for _, addr := range targetAddrs {
		wg.Add(1)
		go func(targetAddr string) {
			defer wg.Done()
			if err := scc.SendMessage(ctx, targetAddr, msg); err != nil {
				errors <- fmt.Errorf("failed to send to %s: %v", targetAddr, err)
			}
		}(addr)
	}

	wg.Wait()
	close(errors)

	var errorList []error
	for err := range errors {
		errorList = append(errorList, err)
	}

	if len(errorList) > 0 {
		return fmt.Errorf("broadcast failed for %d/%d nodes: %v", len(errorList), len(targetAddrs), errorList)
	}

	return nil
}

// handleMessage handles incoming messages
func (scc *SimpleClusterCommunicator) handleMessage(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var msg ClusterMessage
	if err := json.NewDecoder(r.Body).Decode(&msg); err != nil {
		scc.logger.Printf("Failed to decode message: %v", err)
		http.Error(w, "Invalid message format", http.StatusBadRequest)
		return
	}

	scc.mu.RLock()
	handler, exists := scc.handlers[msg.Type]
	scc.mu.RUnlock()

	if !exists {
		scc.logger.Printf("No handler for message type: %s", msg.Type)
		http.Error(w, "No handler for message type", http.StatusBadRequest)
		return
	}

	ctx, cancel := context.WithTimeout(r.Context(), scc.config.ConnectionTimeout)
	defer cancel()

	if err := handler.HandleMessage(ctx, &msg); err != nil {
		scc.logger.Printf("Handler error for message type %s: %v", msg.Type, err)
		http.Error(w, "Handler error", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))
}

// handleHealth handles health check requests
func (scc *SimpleClusterCommunicator) handleHealth(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	response := map[string]interface{}{
		"status":    "healthy",
		"node_id":   scc.nodeID,
		"timestamp": time.Now(),
		"running":   scc.IsRunning(),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// handleStatus handles status requests
func (scc *SimpleClusterCommunicator) handleStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	scc.mu.RLock()
	handlerCount := len(scc.handlers)
	scc.mu.RUnlock()

	response := map[string]interface{}{
		"node_id":   scc.nodeID,
		"status":    "active",
		"port":      scc.config.Port,
		"handlers":  handlerCount,
		"running":   scc.IsRunning(),
		"timestamp": time.Now(),
		"config": map[string]interface{}{
			"max_message_size":   scc.config.MaxMessageSize,
			"connection_timeout": scc.config.ConnectionTimeout.String(),
			"heartbeat_interval": scc.config.HeartbeatInterval.String(),
			"retry_attempts":     scc.config.RetryAttempts,
			"retry_backoff":      scc.config.RetryBackoff.String(),
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// CreateTaskAssignmentMessage creates a task assignment message
func CreateTaskAssignmentMessage(taskID, targetNodeID string, data *TaskAssignmentData) *ClusterMessage {
	return &ClusterMessage{
		ID:       fmt.Sprintf("task-assign-%s-%d", taskID, time.Now().UnixNano()),
		Type:     MessageTypeTaskAssignment,
		Target:   targetNodeID,
		Priority: data.Priority,
		Data: map[string]interface{}{
			"task_id":            data.TaskID,
			"target_node_id":     data.TargetNodeID,
			"priority":           data.Priority,
			"memory_requirement": data.MemoryRequirement,
			"compute_intensity":  data.ComputeIntensity,
			"data_location":      data.DataLocation,
			"data_size":          data.DataSize,
			"deadline":           data.Deadline,
		},
	}
}

// CreateNodeStatusMessage creates a node status message
func CreateNodeStatusMessage(nodeID string, data *NodeStatusData) *ClusterMessage {
	return &ClusterMessage{
		ID:       fmt.Sprintf("node-status-%s-%d", nodeID, time.Now().UnixNano()),
		Type:     MessageTypeNodeStatus,
		Priority: 1,
		Data: map[string]interface{}{
			"node_id":           data.NodeID,
			"status":            data.Status,
			"cpu_usage":         data.CPUUsage,
			"memory_usage":      data.MemoryUsage,
			"gpu_usage":         data.GPUUsage,
			"network_bandwidth": data.NetworkBandwidth,
			"active_tasks":      data.ActiveTasks,
			"queue_length":      data.QueueLength,
			"last_heartbeat":    data.LastHeartbeat,
		},
	}
}

// CreateResultMessage creates a result message
func CreateResultMessage(taskID, sourceNodeID string, data *ResultData) *ClusterMessage {
	return &ClusterMessage{
		ID:       fmt.Sprintf("result-%s-%d", taskID, time.Now().UnixNano()),
		Type:     MessageTypeResult,
		Priority: 2,
		Data: map[string]interface{}{
			"task_id":        data.TaskID,
			"source_node_id": data.SourceNodeID,
			"success":        data.Success,
			"result_data":    data.ResultData,
			"execution_time": data.ExecutionTime,
			"error_message":  data.ErrorMessage,
			"metadata":       data.Metadata,
		},
	}
}

// CreateHeartbeatMessage creates a heartbeat message
func CreateHeartbeatMessage(nodeID string) *ClusterMessage {
	return &ClusterMessage{
		ID:       fmt.Sprintf("heartbeat-%s-%d", nodeID, time.Now().UnixNano()),
		Type:     MessageTypeHeartbeat,
		Priority: 0,
		Data: map[string]interface{}{
			"node_id":   nodeID,
			"timestamp": time.Now(),
		},
	}
}
