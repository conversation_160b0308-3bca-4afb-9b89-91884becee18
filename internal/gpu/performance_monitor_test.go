package gpu

import (
	"context"
	"log"
	"os"
	"testing"
	"time"
)

// MockMultiDeviceManager for testing
type MockMultiDeviceManager struct {
	devices       []*ManagedDevice
	isInitialized bool
	config        MultiDeviceConfig
}

func (m *MockMultiDeviceManager) GetActiveDevices() []*ManagedDevice {
	return m.devices
}

func (m *MockMultiDeviceManager) IsInitialized() bool {
	return m.isInitialized
}

func (m *MockMultiDeviceManager) GetConfiguration() MultiDeviceConfig {
	return m.config
}

// createMockDevice creates a mock device for testing
func createMockDevice(id string, temp int, memUtil, computeUtil float64) *ManagedDevice {
	device := &GPUDevice{
		ID:   id,
		Name: "Mock GPU " + id,
	}

	metrics := &DeviceMetrics{
		ID:                 id,
		MemoryUtilization:  memUtil,
		ComputeUtilization: computeUtil,
		Temperature:        temp,
		PowerConsumption:   150.0,
		TasksCompleted:     100,
		AverageTaskTime:    time.Millisecond * 50,
		ErrorCount:         5,
		LastUpdate:         time.Now(),
		ThroughputMBps:     1000.0,
	}

	return &ManagedDevice{
		Device:    device,
		Metrics:   metrics,
		LoadLevel: computeUtil / 100.0,
		LastUsed:  time.Now(),
		IsActive:  true,
	}
}

func TestGPUPerformanceMonitor_Creation(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	// Create mock multi-device manager
	mockMgr := &MockMultiDeviceManager{
		devices:       []*ManagedDevice{},
		isInitialized: true,
		config:        DefaultMultiDeviceConfig(),
	}

	config := DefaultPerformanceMonitorConfig()
	monitor := NewGPUPerformanceMonitor(mockMgr, config, logger)

	if monitor == nil {
		t.Fatal("Failed to create performance monitor")
	}

	if monitor.config.MonitoringInterval != config.MonitoringInterval {
		t.Errorf("Expected monitoring interval %v, got %v", config.MonitoringInterval, monitor.config.MonitoringInterval)
	}

	if monitor.maxHistorySize != config.HistorySize {
		t.Errorf("Expected history size %d, got %d", config.HistorySize, monitor.maxHistorySize)
	}

	if !monitor.config.Enabled {
		t.Error("Expected monitoring to be enabled by default")
	}
}

func TestGPUPerformanceMonitor_StartStop(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	// Create mock devices
	device1 := createMockDevice("gpu0", 70, 80.0, 85.0)
	device2 := createMockDevice("gpu1", 75, 75.0, 90.0)

	mockMgr := &MockMultiDeviceManager{
		devices:       []*ManagedDevice{device1, device2},
		isInitialized: true,
		config:        DefaultMultiDeviceConfig(),
	}

	config := DefaultPerformanceMonitorConfig()
	config.MonitoringInterval = time.Millisecond * 100 // Fast for testing
	monitor := NewGPUPerformanceMonitor(mockMgr, config, logger)

	// Test starting
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	if monitor.IsRunning() {
		t.Error("Monitor should not be running initially")
	}

	err := monitor.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start monitor: %v", err)
	}

	if !monitor.IsRunning() {
		t.Error("Monitor should be running after start")
	}

	// Let it run for a short time to capture some snapshots
	time.Sleep(time.Millisecond * 250)

	// Test stopping
	err = monitor.Stop()
	if err != nil {
		t.Fatalf("Failed to stop monitor: %v", err)
	}

	if monitor.IsRunning() {
		t.Error("Monitor should not be running after stop")
	}

	// Check that we captured some performance data
	stats := monitor.GetMonitoringStats()
	if stats.TotalSnapshots == 0 {
		t.Error("Expected to capture at least one performance snapshot")
	}
}

func TestGPUPerformanceMonitor_PerformanceSnapshot(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	// Create mock devices with different characteristics
	device1 := createMockDevice("gpu0", 70, 80.0, 85.0) // Normal operation
	device2 := createMockDevice("gpu1", 90, 95.0, 98.0) // High utilization
	device3 := createMockDevice("gpu2", 45, 30.0, 40.0) // Low utilization

	mockMgr := &MockMultiDeviceManager{
		devices:       []*ManagedDevice{device1, device2, device3},
		isInitialized: true,
		config:        DefaultMultiDeviceConfig(),
	}

	config := DefaultPerformanceMonitorConfig()
	monitor := NewGPUPerformanceMonitor(mockMgr, config, logger)

	// Manually capture a snapshot for testing
	err := monitor.capturePerformanceSnapshot()
	if err != nil {
		t.Fatalf("Failed to capture performance snapshot: %v", err)
	}

	snapshot, err := monitor.GetCurrentSnapshot()
	if err != nil {
		t.Fatalf("Failed to get current snapshot: %v", err)
	}

	// Verify snapshot structure
	if snapshot.Timestamp.IsZero() {
		t.Error("Snapshot timestamp should not be zero")
	}

	if len(snapshot.DevicePerformance) != 3 {
		t.Errorf("Expected 3 devices in snapshot, got %d", len(snapshot.DevicePerformance))
	}

	// Verify device performance data
	for deviceID, perf := range snapshot.DevicePerformance {
		if perf.DeviceID != deviceID {
			t.Errorf("Device ID mismatch: expected %s, got %s", deviceID, perf.DeviceID)
		}

		if perf.BasicMetrics == nil {
			t.Errorf("Basic metrics should not be nil for device %s", deviceID)
		}

		if perf.EfficiencyScore < 0 || perf.EfficiencyScore > 1 {
			t.Errorf("Efficiency score should be between 0 and 1, got %f for device %s", perf.EfficiencyScore, deviceID)
		}
	}

	// Verify system health
	health := snapshot.SystemHealth
	if health.ActiveDevices != 3 {
		t.Errorf("Expected 3 active devices, got %d", health.ActiveDevices)
	}

	if health.FailedDevices != 0 {
		t.Errorf("Expected 0 failed devices, got %d", health.FailedDevices)
	}

	if health.OverallHealth == "" {
		t.Error("Overall health status should not be empty")
	}

	// Verify load balancing metrics
	loadBalance := snapshot.LoadBalancing
	if len(loadBalance.LoadDistribution) != 3 {
		t.Errorf("Expected load distribution for 3 devices, got %d", len(loadBalance.LoadDistribution))
	}

	// Verify system efficiency
	efficiency := snapshot.SystemEfficiency
	if efficiency.OverallEfficiency < 0 || efficiency.OverallEfficiency > 1 {
		t.Errorf("Overall efficiency should be between 0 and 1, got %f", efficiency.OverallEfficiency)
	}
}

func TestGPUPerformanceMonitor_AlertSystem(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	// Create a device with critical temperature
	criticalDevice := createMockDevice("gpu0", 90, 96.0, 99.0) // Critical temp and memory

	mockMgr := &MockMultiDeviceManager{
		devices:       []*ManagedDevice{criticalDevice},
		isInitialized: true,
		config:        DefaultMultiDeviceConfig(),
	}

	config := DefaultPerformanceMonitorConfig()
	monitor := NewGPUPerformanceMonitor(mockMgr, config, logger)

	// Set up alert tracking
	var alertsReceived []AlertLevel
	var alertMessages []string

	monitor.SetAlertCallback(func(level AlertLevel, message string, data interface{}) {
		alertsReceived = append(alertsReceived, level)
		alertMessages = append(alertMessages, message)
	})

	// Capture snapshot to trigger alerts
	err := monitor.capturePerformanceSnapshot()
	if err != nil {
		t.Fatalf("Failed to capture performance snapshot: %v", err)
	}

	// Should have received critical alerts for temperature and memory
	if len(alertsReceived) == 0 {
		t.Error("Expected to receive alerts for critical conditions")
	}

	hasCriticalAlert := false
	for _, level := range alertsReceived {
		if level == AlertLevelCritical {
			hasCriticalAlert = true
			break
		}
	}

	if !hasCriticalAlert {
		t.Error("Expected to receive at least one critical alert")
	}
}

func TestGPUPerformanceMonitor_HistoryManagement(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	device := createMockDevice("gpu0", 70, 80.0, 85.0)

	mockMgr := &MockMultiDeviceManager{
		devices:       []*ManagedDevice{device},
		isInitialized: true,
		config:        DefaultMultiDeviceConfig(),
	}

	config := DefaultPerformanceMonitorConfig()
	config.HistorySize = 3 // Small history for testing
	monitor := NewGPUPerformanceMonitor(mockMgr, config, logger)

	// Capture multiple snapshots
	for i := 0; i < 5; i++ {
		err := monitor.capturePerformanceSnapshot()
		if err != nil {
			t.Fatalf("Failed to capture snapshot %d: %v", i, err)
		}
		time.Sleep(time.Millisecond * 10) // Small delay to ensure different timestamps
	}

	history := monitor.GetPerformanceHistory()
	if len(history) > 3 {
		t.Errorf("History should be limited to %d entries, got %d", config.HistorySize, len(history))
	}

	// Verify history is in chronological order
	for i := 1; i < len(history); i++ {
		if history[i].Timestamp.Before(history[i-1].Timestamp) {
			t.Error("History should be in chronological order")
		}
	}
}

func TestGPUPerformanceMonitor_ConfigurationValidation(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	mockMgr := &MockMultiDeviceManager{
		devices:       []*ManagedDevice{},
		isInitialized: false, // Not initialized
		config:        DefaultMultiDeviceConfig(),
	}

	config := DefaultPerformanceMonitorConfig()
	monitor := NewGPUPerformanceMonitor(mockMgr, config, logger)

	ctx := context.Background()

	// Should fail to start with uninitialized multi-device manager
	err := monitor.Start(ctx)
	if err == nil {
		t.Error("Expected error when starting with uninitialized multi-device manager")
	}

	// Test with disabled monitoring
	config.Enabled = false
	monitor = NewGPUPerformanceMonitor(mockMgr, config, logger)

	err = monitor.Start(ctx)
	if err == nil {
		t.Error("Expected error when starting with disabled monitoring")
	}
}

func TestGPUPerformanceMonitor_EfficiencyCalculations(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	// Create devices with known characteristics for testing calculations
	highEffDevice := createMockDevice("gpu0", 60, 70.0, 80.0) // Good efficiency
	lowEffDevice := createMockDevice("gpu1", 85, 95.0, 99.0)  // Poor efficiency (hot, high utilization)

	mockMgr := &MockMultiDeviceManager{
		devices:       []*ManagedDevice{highEffDevice, lowEffDevice},
		isInitialized: true,
		config:        DefaultMultiDeviceConfig(),
	}

	config := DefaultPerformanceMonitorConfig()
	monitor := NewGPUPerformanceMonitor(mockMgr, config, logger)

	err := monitor.capturePerformanceSnapshot()
	if err != nil {
		t.Fatalf("Failed to capture performance snapshot: %v", err)
	}

	snapshot, err := monitor.GetCurrentSnapshot()
	if err != nil {
		t.Fatalf("Failed to get snapshot: %v", err)
	}

	// Verify efficiency calculations
	highEffPerf := snapshot.DevicePerformance["gpu0"]
	lowEffPerf := snapshot.DevicePerformance["gpu1"]

	// The low efficiency device has very high utilization (99%) which contributes heavily to efficiency
	// But it should have lower thermal efficiency due to high temperature
	if highEffPerf.ThermalMetrics.ThermalStatus == ThermalStatusNormal &&
		lowEffPerf.ThermalMetrics.ThermalStatus != ThermalStatusNormal {
		// This is the expected behavior - thermal status should differ
		t.Logf("Thermal status correctly differs: %s vs %s",
			highEffPerf.ThermalMetrics.ThermalStatus, lowEffPerf.ThermalMetrics.ThermalStatus)
	} else {
		t.Errorf("Expected different thermal status between devices")
	}

	// Verify both devices have reasonable efficiency scores
	if highEffPerf.EfficiencyScore < 0 || highEffPerf.EfficiencyScore > 1 {
		t.Errorf("High efficiency device score out of range: %f", highEffPerf.EfficiencyScore)
	}
	if lowEffPerf.EfficiencyScore < 0 || lowEffPerf.EfficiencyScore > 1 {
		t.Errorf("Low efficiency device score out of range: %f", lowEffPerf.EfficiencyScore)
	}

	// Verify thermal status calculations
	if highEffPerf.ThermalMetrics.ThermalStatus != ThermalStatusNormal {
		t.Errorf("Expected normal thermal status for cool device, got %s", highEffPerf.ThermalMetrics.ThermalStatus)
	}

	if lowEffPerf.ThermalMetrics.ThermalStatus == ThermalStatusNormal {
		t.Errorf("Expected warning/critical thermal status for hot device, got %s", lowEffPerf.ThermalMetrics.ThermalStatus)
	}
}

func TestGPUPerformanceMonitor_DefaultConfigurations(t *testing.T) {
	// Test default performance monitor config
	config := DefaultPerformanceMonitorConfig()

	if !config.Enabled {
		t.Error("Default config should have monitoring enabled")
	}

	if config.MonitoringInterval <= 0 {
		t.Error("Default config should have positive monitoring interval")
	}

	if config.HistorySize <= 0 {
		t.Error("Default config should have positive history size")
	}

	// Test default alert thresholds
	thresholds := DefaultAlertThresholds()

	if thresholds.CriticalTemperature <= thresholds.WarningTemperature {
		t.Error("Critical temperature should be higher than warning temperature")
	}

	if thresholds.CriticalMemoryUsage <= thresholds.WarningMemoryUsage {
		t.Error("Critical memory usage should be higher than warning memory usage")
	}

	if thresholds.MinEfficiencyScore < 0 || thresholds.MinEfficiencyScore > 1 {
		t.Error("Minimum efficiency score should be between 0 and 1")
	}
}

func TestGPUPerformanceMonitor_MonitoringStats(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	device := createMockDevice("gpu0", 70, 80.0, 85.0)

	mockMgr := &MockMultiDeviceManager{
		devices:       []*ManagedDevice{device},
		isInitialized: true,
		config:        DefaultMultiDeviceConfig(),
	}

	config := DefaultPerformanceMonitorConfig()
	monitor := NewGPUPerformanceMonitor(mockMgr, config, logger)

	// Capture a few snapshots
	for i := 0; i < 3; i++ {
		err := monitor.capturePerformanceSnapshot()
		if err != nil {
			t.Fatalf("Failed to capture snapshot: %v", err)
		}
	}

	stats := monitor.GetMonitoringStats()

	if stats.TotalSnapshots != 3 {
		t.Errorf("Expected 3 snapshots, got %d", stats.TotalSnapshots)
	}

	if stats.HistorySize != 3 {
		t.Errorf("Expected history size of 3, got %d", stats.HistorySize)
	}

	if stats.MonitoringInterval != config.MonitoringInterval {
		t.Errorf("Expected monitoring interval %v, got %v", config.MonitoringInterval, stats.MonitoringInterval)
	}

	if stats.LastSnapshotTime.IsZero() {
		t.Error("Last snapshot time should not be zero")
	}
}
