package gpu

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// RecoveryEngine handles fast GPU state recovery from checkpoints
type RecoveryEngine struct {
	serializer        *GPUStateSerializer
	checkpointManager *IncrementalCheckpointManager
	compressor        *CheckpointCompressor
	storage           CheckpointStorage
	config            RecoveryConfig
	logger            Logger
	mu                sync.RWMutex

	// Recovery state tracking
	activeRecoveries map[string]*RecoveryOperation
	heartbeats       map[string]*HeartbeatTracker
	lastKnownGood    map[string]*GPUStateSnapshot
	metrics          *RecoveryMetrics
}

// RecoveryConfig configures the recovery engine behavior
type RecoveryConfig struct {
	// Basic settings
	Enabled              bool          `json:"enabled"`
	MaxRecoveryTime      time.Duration `json:"max_recovery_time"`
	CriticalStateTimeout time.Duration `json:"critical_state_timeout"`
	FullRecoveryTimeout  time.Duration `json:"full_recovery_timeout"`

	// Performance settings
	AsyncRecovery          bool `json:"async_recovery"`
	ParallelRestoreWorkers int  `json:"parallel_restore_workers"`
	PriorityRestoreEnabled bool `json:"priority_restore_enabled"`
	MemoryPreallocation    bool `json:"memory_preallocation"`

	// Monitoring settings
	HeartbeatInterval     time.Duration `json:"heartbeat_interval"`
	HealthCheckInterval   time.Duration `json:"health_check_interval"`
	RecoveryRetryAttempts int           `json:"recovery_retry_attempts"`
	RecoveryRetryDelay    time.Duration `json:"recovery_retry_delay"`

	// Validation settings
	VerifyRecoveredState  bool `json:"verify_recovered_state"`
	ChecksumValidation    bool `json:"checksum_validation"`
	StateConsistencyCheck bool `json:"state_consistency_check"`
	PerformanceValidation bool `json:"performance_validation"`
}

// RecoveryOperation tracks an ongoing recovery operation
type RecoveryOperation struct {
	TaskID           string                   `json:"task_id"`
	StartTime        time.Time                `json:"start_time"`
	Phase            RecoveryPhase            `json:"phase"`
	Progress         float64                  `json:"progress"`
	Error            error                    `json:"error,omitempty"`
	RecoveryType     RecoveryOperationType    `json:"recovery_type"`
	CheckpointUsed   string                   `json:"checkpoint_used"`
	ComponentsStatus map[string]string        `json:"components_status"`
	Metrics          RecoveryOperationMetrics `json:"metrics"`
	Context          context.Context          `json:"-"`
	CancelFunc       context.CancelFunc       `json:"-"`
}

// RecoveryPhase represents the current phase of recovery
type RecoveryPhase string

const (
	RecoveryPhaseInitializing      RecoveryPhase = "initializing"
	RecoveryPhaseLoadingCheckpoint RecoveryPhase = "loading_checkpoint"
	RecoveryPhaseRestoringCritical RecoveryPhase = "restoring_critical"
	RecoveryPhaseRestoringFull     RecoveryPhase = "restoring_full"
	RecoveryPhaseValidating        RecoveryPhase = "validating"
	RecoveryPhaseCompleted         RecoveryPhase = "completed"
	RecoveryPhaseFailed            RecoveryPhase = "failed"
)

// RecoveryOperationType represents the type of recovery being performed
type RecoveryOperationType string

const (
	RecoveryOperationTypeFull        RecoveryOperationType = "full"
	RecoveryOperationTypeIncremental RecoveryOperationType = "incremental"
	RecoveryOperationTypeCritical    RecoveryOperationType = "critical"
	RecoveryOperationTypePartial     RecoveryOperationType = "partial"
)

// HeartbeatTracker tracks system health and triggers recovery when needed
type HeartbeatTracker struct {
	TaskID              string                 `json:"task_id"`
	LastHeartbeat       time.Time              `json:"last_heartbeat"`
	LastHealthCheck     time.Time              `json:"last_health_check"`
	ConsecutiveFailures int                    `json:"consecutive_failures"`
	IsHealthy           bool                   `json:"is_healthy"`
	ComponentStatus     map[string]bool        `json:"component_status"`
	Metadata            map[string]interface{} `json:"metadata"`
}

// RecoveryMetrics tracks overall recovery system performance
type RecoveryMetrics struct {
	TotalRecoveries        int64                           `json:"total_recoveries"`
	SuccessfulRecoveries   int64                           `json:"successful_recoveries"`
	FailedRecoveries       int64                           `json:"failed_recoveries"`
	AverageRecoveryTime    time.Duration                   `json:"average_recovery_time"`
	FastestRecoveryTime    time.Duration                   `json:"fastest_recovery_time"`
	SlowestRecoveryTime    time.Duration                   `json:"slowest_recovery_time"`
	RecoveryTypeStats      map[RecoveryOperationType]int64 `json:"recovery_type_stats"`
	ComponentRecoveryStats map[string]int64                `json:"component_recovery_stats"`
}

// RecoveryOperationMetrics tracks metrics for a specific recovery operation
type RecoveryOperationMetrics struct {
	TotalTime           time.Duration `json:"total_time"`
	CriticalRestoreTime time.Duration `json:"critical_restore_time"`
	FullRestoreTime     time.Duration `json:"full_restore_time"`
	ValidationTime      time.Duration `json:"validation_time"`
	TensorsRestored     int           `json:"tensors_restored"`
	MemoryPoolsRestored int           `json:"memory_pools_restored"`
	StreamsRestored     int           `json:"streams_restored"`
	DataRestored        int64         `json:"data_restored"`
	CompressionRatio    float64       `json:"compression_ratio"`
}

// DefaultRecoveryConfig returns default recovery configuration
func DefaultRecoveryConfig() RecoveryConfig {
	return RecoveryConfig{
		Enabled:                true,
		MaxRecoveryTime:        5 * time.Minute,
		CriticalStateTimeout:   30 * time.Second,
		FullRecoveryTimeout:    2 * time.Minute,
		AsyncRecovery:          true,
		ParallelRestoreWorkers: 4,
		PriorityRestoreEnabled: true,
		MemoryPreallocation:    true,
		HeartbeatInterval:      10 * time.Second,
		HealthCheckInterval:    30 * time.Second,
		RecoveryRetryAttempts:  3,
		RecoveryRetryDelay:     5 * time.Second,
		VerifyRecoveredState:   true,
		ChecksumValidation:     true,
		StateConsistencyCheck:  true,
		PerformanceValidation:  false, // Disabled by default for speed
	}
}

// NewRecoveryEngine creates a new recovery engine
func NewRecoveryEngine(
	serializer *GPUStateSerializer,
	checkpointManager *IncrementalCheckpointManager,
	compressor *CheckpointCompressor,
	storage CheckpointStorage,
	config RecoveryConfig,
	logger Logger,
) *RecoveryEngine {
	return &RecoveryEngine{
		serializer:        serializer,
		checkpointManager: checkpointManager,
		compressor:        compressor,
		storage:           storage,
		config:            config,
		logger:            logger,
		activeRecoveries:  make(map[string]*RecoveryOperation),
		heartbeats:        make(map[string]*HeartbeatTracker),
		lastKnownGood:     make(map[string]*GPUStateSnapshot),
		metrics: &RecoveryMetrics{
			RecoveryTypeStats:      make(map[RecoveryOperationType]int64),
			ComponentRecoveryStats: make(map[string]int64),
		},
	}
}

// StartHeartbeatMonitoring starts monitoring system health for a task
func (re *RecoveryEngine) StartHeartbeatMonitoring(taskID string) {
	if !re.config.Enabled {
		return
	}

	re.mu.Lock()
	defer re.mu.Unlock()

	tracker := &HeartbeatTracker{
		TaskID:              taskID,
		LastHeartbeat:       time.Now(),
		LastHealthCheck:     time.Now(),
		ConsecutiveFailures: 0,
		IsHealthy:           true,
		ComponentStatus:     make(map[string]bool),
		Metadata:            make(map[string]interface{}),
	}

	re.heartbeats[taskID] = tracker

	// Start heartbeat monitoring goroutine
	go re.monitorHeartbeat(taskID)
}

// StopHeartbeatMonitoring stops monitoring system health for a task
func (re *RecoveryEngine) StopHeartbeatMonitoring(taskID string) {
	re.mu.Lock()
	defer re.mu.Unlock()
	delete(re.heartbeats, taskID)
}

// SendHeartbeat updates the heartbeat for a task
func (re *RecoveryEngine) SendHeartbeat(taskID string, componentStatus map[string]bool) {
	re.mu.Lock()
	defer re.mu.Unlock()

	tracker, exists := re.heartbeats[taskID]
	if !exists {
		return
	}

	tracker.LastHeartbeat = time.Now()
	tracker.ComponentStatus = componentStatus
	tracker.ConsecutiveFailures = 0
	tracker.IsHealthy = true
}

// FastRecovery performs emergency recovery with the specified type
func (re *RecoveryEngine) FastRecovery(taskID string, recoveryType RecoveryOperationType) error {
	ctx, cancel := context.WithTimeout(context.Background(), re.config.CriticalStateTimeout)
	defer cancel()

	re.logger.Printf("Starting fast recovery for task %s with type %s", taskID, recoveryType)

	// Use the provided recovery operation type directly
	operationType := recoveryType

	// Create recovery operation
	operation := &RecoveryOperation{
		TaskID:           taskID,
		StartTime:        time.Now(),
		Phase:            RecoveryPhaseInitializing,
		Progress:         0.0,
		RecoveryType:     operationType, // Now using correct type
		ComponentsStatus: make(map[string]string),
		Context:          ctx,
		CancelFunc:       cancel,
	}

	re.mu.Lock()
	re.activeRecoveries[taskID] = operation
	re.mu.Unlock()

	defer func() {
		cancel()
		re.mu.Lock()
		delete(re.activeRecoveries, taskID)
		re.mu.Unlock()
	}()

	// Perform recovery with retries
	var err error
	for attempt := 0; attempt < re.config.RecoveryRetryAttempts; attempt++ {
		if attempt > 0 {
			time.Sleep(re.config.RecoveryRetryDelay)
			re.logger.Printf("Recovery attempt %d/%d for task %s",
				attempt+1, re.config.RecoveryRetryAttempts, taskID)
		}

		err = re.performRecovery(operation)
		if err == nil {
			break
		}

		re.logger.Errorf("Recovery attempt %d failed for task %s: %v",
			attempt+1, taskID, err)
	}

	// Update metrics
	re.updateRecoveryMetrics(operation, err)

	if err != nil {
		operation.Phase = RecoveryPhaseFailed
		operation.Error = err
		return fmt.Errorf("recovery failed after %d attempts: %w",
			re.config.RecoveryRetryAttempts, err)
	}

	operation.Phase = RecoveryPhaseCompleted
	operation.Progress = 1.0
	re.logger.Printf("Recovery completed successfully for task %s in %v",
		taskID, time.Since(operation.StartTime))

	return nil
}

// RestoreCriticalState restores only essential components for immediate functionality
func (re *RecoveryEngine) RestoreCriticalState(taskID string, snapshot *GPUStateSnapshot) error {
	startTime := time.Now()

	re.logger.Printf("Starting critical state restoration for task %s", taskID)

	// Restore critical tensors first (those marked as essential or actively used)
	criticalTensors := re.identifyCriticalTensors(snapshot.TensorStates)
	for _, tensorState := range criticalTensors {
		if err := re.serializer.restoreTensor(&tensorState); err != nil {
			return fmt.Errorf("failed to restore critical tensor %s: %w", tensorState.ID, err)
		}
	}

	// Restore minimal memory pool state to enable basic operations
	if err := re.restoreMinimalMemoryState(&snapshot.MemoryState); err != nil {
		return fmt.Errorf("failed to restore minimal memory state: %w", err)
	}

	// Restore essential stream managers
	if err := re.restoreEssentialStreams(&snapshot.StreamState); err != nil {
		return fmt.Errorf("failed to restore essential streams: %w", err)
	}

	re.logger.Printf("Critical state restoration completed for task %s in %v",
		taskID, time.Since(startTime))

	return nil
}

// AsyncFullRecovery performs full state recovery asynchronously
func (re *RecoveryEngine) AsyncFullRecovery(taskID string, snapshot *GPUStateSnapshot) <-chan error {
	resultChan := make(chan error, 1)

	go func() {
		defer close(resultChan)

		startTime := time.Now()
		re.logger.Printf("Starting async full recovery for task %s", taskID)

		// Restore complete state
		err := re.serializer.RestoreGPUState(snapshot)
		if err != nil {
			resultChan <- fmt.Errorf("async full recovery failed: %w", err)
			return
		}

		// Validate recovered state if enabled
		if re.config.VerifyRecoveredState {
			if err := re.validateRecoveredState(taskID, snapshot); err != nil {
				resultChan <- fmt.Errorf("state validation failed: %w", err)
				return
			}
		}

		re.logger.Printf("Async full recovery completed for task %s in %v",
			taskID, time.Since(startTime))

		resultChan <- nil
	}()

	return resultChan
}

// RestoreFromIncrementalCheckpoint restores state from incremental checkpoint
func (re *RecoveryEngine) RestoreFromIncrementalCheckpoint(checkpoint *IncrementalCheckpoint) error {
	if !checkpoint.IsIncremental {
		return fmt.Errorf("checkpoint is not incremental")
	}

	// Get base snapshot for this checkpoint chain
	baseSnapshot, exists := re.lastKnownGood[checkpoint.TaskID]
	if !exists {
		return fmt.Errorf("base snapshot not found for incremental restore of task %s", checkpoint.TaskID)
	}

	// Decompress delta data
	deltaData, err := re.compressor.DecompressCheckpointData(checkpoint.DeltaData, "delta")
	if err != nil {
		return fmt.Errorf("failed to decompress delta data: %w", err)
	}

	// Apply delta changes to base snapshot
	for _, change := range checkpoint.Changes {
		if err := re.applyDeltaChange(baseSnapshot, change, deltaData); err != nil {
			return fmt.Errorf("failed to apply delta change: %w", err)
		}
	}

	// Restore the modified snapshot
	if err := re.serializer.RestoreGPUState(baseSnapshot); err != nil {
		return fmt.Errorf("failed to restore modified snapshot: %w", err)
	}

	// Update last known good state
	re.lastKnownGood[checkpoint.TaskID] = baseSnapshot

	return nil
}

// GetRecoveryStatus returns the status of an ongoing recovery operation
func (re *RecoveryEngine) GetRecoveryStatus(taskID string) (*RecoveryOperation, bool) {
	re.mu.RLock()
	defer re.mu.RUnlock()
	operation, exists := re.activeRecoveries[taskID]
	return operation, exists
}

// GetRecoveryMetrics returns overall recovery metrics
func (re *RecoveryEngine) GetRecoveryMetrics() *RecoveryMetrics {
	re.mu.RLock()
	defer re.mu.RUnlock()

	// Create a copy to avoid race conditions
	metrics := *re.metrics
	metrics.RecoveryTypeStats = make(map[RecoveryOperationType]int64)
	metrics.ComponentRecoveryStats = make(map[string]int64)

	for k, v := range re.metrics.RecoveryTypeStats {
		metrics.RecoveryTypeStats[k] = v
	}
	for k, v := range re.metrics.ComponentRecoveryStats {
		metrics.ComponentRecoveryStats[k] = v
	}

	return &metrics
}

// SaveLastKnownGoodState saves a snapshot as the last known good state
func (re *RecoveryEngine) SaveLastKnownGoodState(taskID string, snapshot *GPUStateSnapshot) {
	re.mu.Lock()
	defer re.mu.Unlock()
	re.lastKnownGood[taskID] = snapshot
}

// Private helper methods

func (re *RecoveryEngine) monitorHeartbeat(taskID string) {
	ticker := time.NewTicker(re.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			re.mu.Lock()
			tracker, exists := re.heartbeats[taskID]
			if !exists {
				re.mu.Unlock()
				return // Monitoring stopped
			}

			// Check if heartbeat is stale
			if time.Since(tracker.LastHeartbeat) > re.config.HeartbeatInterval*2 {
				tracker.ConsecutiveFailures++
				tracker.IsHealthy = false

				// Trigger recovery if threshold exceeded
				if tracker.ConsecutiveFailures >= 3 {
					re.mu.Unlock()
					re.logger.Printf("Triggering automatic recovery for task %s due to heartbeat failure", taskID)
					go re.FastRecovery(taskID, "node_restart")
					return
				}
			}
			re.mu.Unlock()
		}
	}
}

func (re *RecoveryEngine) performRecovery(operation *RecoveryOperation) error {
	// Phase 1: Load checkpoint
	operation.Phase = RecoveryPhaseLoadingCheckpoint
	operation.Progress = 0.1

	checkpoint, err := re.findLatestCheckpoint(operation.TaskID)
	if err != nil {
		return fmt.Errorf("failed to find checkpoint: %w", err)
	}
	operation.CheckpointUsed = operation.TaskID

	// Phase 2: Restore critical state
	operation.Phase = RecoveryPhaseRestoringCritical
	operation.Progress = 0.3

	criticalStart := time.Now()
	if err := re.RestoreCriticalState(operation.TaskID, checkpoint); err != nil {
		return fmt.Errorf("critical state restoration failed: %w", err)
	}
	operation.Metrics.CriticalRestoreTime = time.Since(criticalStart)

	// Phase 3: Async full recovery
	operation.Phase = RecoveryPhaseRestoringFull
	operation.Progress = 0.6

	fullStart := time.Now()
	if re.config.AsyncRecovery {
		resultChan := re.AsyncFullRecovery(operation.TaskID, checkpoint)
		select {
		case err := <-resultChan:
			if err != nil {
				return fmt.Errorf("async full recovery failed: %w", err)
			}
		case <-operation.Context.Done():
			return fmt.Errorf("recovery timeout")
		}
	} else {
		if err := re.serializer.RestoreGPUState(checkpoint); err != nil {
			return fmt.Errorf("full recovery failed: %w", err)
		}
	}
	operation.Metrics.FullRestoreTime = time.Since(fullStart)

	// Phase 4: Validation
	operation.Phase = RecoveryPhaseValidating
	operation.Progress = 0.9

	validationStart := time.Now()
	if re.config.VerifyRecoveredState {
		if err := re.validateRecoveredState(operation.TaskID, checkpoint); err != nil {
			return fmt.Errorf("validation failed: %w", err)
		}
	}
	operation.Metrics.ValidationTime = time.Since(validationStart)

	operation.Metrics.TotalTime = time.Since(operation.StartTime)
	return nil
}

func (re *RecoveryEngine) findLatestCheckpoint(taskID string) (*GPUStateSnapshot, error) {
	// First try to get from last known good
	if snapshot, exists := re.lastKnownGood[taskID]; exists {
		return snapshot, nil
	}

	// Try to load from storage
	// This is a placeholder - actual implementation would query the storage
	return nil, fmt.Errorf("no checkpoint found for task %s", taskID)
}

func (re *RecoveryEngine) identifyCriticalTensors(tensorStates []TensorState) []TensorState {
	var critical []TensorState

	// Identify critical tensors based on naming patterns and metadata
	for _, tensor := range tensorStates {
		// Consider tensors with specific patterns as critical
		if re.isCriticalTensor(tensor) {
			critical = append(critical, tensor)
		}
	}

	return critical
}

func (re *RecoveryEngine) isCriticalTensor(tensor TensorState) bool {
	// Basic heuristics for identifying critical tensors
	criticalPatterns := []string{"weight", "bias", "embedding", "attention"}

	for _, pattern := range criticalPatterns {
		if len(tensor.Name) > 0 && contains(tensor.Name, pattern) {
			return true
		}
	}

	// Consider large tensors as potentially critical
	if tensor.Size > 1024*1024 { // 1MB threshold
		return true
	}

	return false
}

func (re *RecoveryEngine) restoreMinimalMemoryState(state *MemoryPoolState) error {
	// Restore only essential memory allocations
	re.logger.Printf("Restoring minimal memory state for device %d", state.DeviceID)

	// This is a simplified implementation - actual implementation would
	// restore only the most critical memory blocks
	return nil
}

func (re *RecoveryEngine) restoreEssentialStreams(state *StreamManagerState) error {
	// Restore only the most critical streams for immediate functionality
	re.logger.Printf("Restoring essential streams for device %d", state.DeviceID)

	// This is a simplified implementation - actual implementation would
	// restore high-priority streams first
	return nil
}

func (re *RecoveryEngine) applyDeltaChange(snapshot *GPUStateSnapshot, change DeltaChange, deltaData []byte) error {
	switch change.Type {
	case DeltaChangeTensorModified:
		return re.applyTensorDelta(snapshot, change, deltaData)
	case DeltaChangeMemoryModified:
		return re.applyMemoryDelta(snapshot, change, deltaData)
	case DeltaChangeStreamModified:
		return re.applyStreamDelta(snapshot, change, deltaData)
	default:
		return fmt.Errorf("unknown delta change type: %s", change.Type)
	}
}

func (re *RecoveryEngine) applyTensorDelta(snapshot *GPUStateSnapshot, change DeltaChange, deltaData []byte) error {
	// Find the tensor in the snapshot
	for i, tensor := range snapshot.TensorStates {
		if tensor.ID == change.TensorID {
			// Apply delta to tensor data
			// This is a simplified implementation
			snapshot.TensorStates[i].Data = deltaData
			return nil
		}
	}
	return fmt.Errorf("tensor %s not found in snapshot", change.TensorID)
}

func (re *RecoveryEngine) applyMemoryDelta(snapshot *GPUStateSnapshot, change DeltaChange, deltaData []byte) error {
	// Apply memory pool changes
	// This is a simplified implementation
	re.logger.Printf("Applying memory delta for pool %s", change.PoolID)
	return nil
}

func (re *RecoveryEngine) applyStreamDelta(snapshot *GPUStateSnapshot, change DeltaChange, deltaData []byte) error {
	// Apply stream state changes
	// This is a simplified implementation
	re.logger.Printf("Applying stream delta for stream %s", change.StreamID)
	return nil
}

func (re *RecoveryEngine) validateRecoveredState(taskID string, snapshot *GPUStateSnapshot) error {
	// Perform various validation checks
	if re.config.ChecksumValidation {
		if err := re.validateChecksums(snapshot); err != nil {
			return fmt.Errorf("checksum validation failed: %w", err)
		}
	}

	if re.config.StateConsistencyCheck {
		if err := re.validateStateConsistency(snapshot); err != nil {
			return fmt.Errorf("state consistency check failed: %w", err)
		}
	}

	return nil
}

func (re *RecoveryEngine) validateChecksums(snapshot *GPUStateSnapshot) error {
	// Validate checksums for all components
	for _, tensor := range snapshot.TensorStates {
		if tensor.Checksum != "" {
			// Validate tensor checksum
			// This is a simplified implementation
		}
	}
	return nil
}

func (re *RecoveryEngine) validateStateConsistency(snapshot *GPUStateSnapshot) error {
	// Check that the recovered state is internally consistent
	// This is a simplified implementation
	return nil
}

func (re *RecoveryEngine) updateRecoveryMetrics(operation *RecoveryOperation, err error) {
	re.mu.Lock()
	defer re.mu.Unlock()

	re.metrics.TotalRecoveries++

	if err == nil {
		re.metrics.SuccessfulRecoveries++
	} else {
		re.metrics.FailedRecoveries++
	}

	// Update timing metrics
	totalTime := operation.Metrics.TotalTime
	if totalTime > 0 {
		if re.metrics.FastestRecoveryTime == 0 || totalTime < re.metrics.FastestRecoveryTime {
			re.metrics.FastestRecoveryTime = totalTime
		}
		if totalTime > re.metrics.SlowestRecoveryTime {
			re.metrics.SlowestRecoveryTime = totalTime
		}

		// Update average (simplified calculation)
		re.metrics.AverageRecoveryTime = (re.metrics.AverageRecoveryTime + totalTime) / 2
	}

	// Update recovery type stats
	re.metrics.RecoveryTypeStats[operation.RecoveryType]++
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			(len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					containsMiddle(s, substr))))
}

func containsMiddle(s, substr string) bool {
	for i := 1; i < len(s)-len(substr)+1; i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
