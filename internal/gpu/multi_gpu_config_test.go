//go:build legacytest
// +build legacytest

package gpu

import (
	"context"
	"log"
	"os"
	"path/filepath"
	"testing"
)

// TestConfigurationProfileManager_BasicOperations tests basic profile management
func TestConfigurationProfileManager_BasicOperations(t *testing.T) {
	tempDir := t.TempDir()
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	mgr := NewConfigurationProfileManager(tempDir, logger)

	// Test creating a profile
	profile, err := mgr.CreateProfile("test_gaming", UseCaseGaming, "Test gaming profile")
	if err != nil {
		t.Fatalf("Failed to create profile: %v", err)
	}

	if profile.Name != "test_gaming" {
		t.<PERSON>rrorf("Expected profile name 'test_gaming', got '%s'", profile.Name)
	}

	if profile.UseCase != UseCaseGaming {
		t.Errorf("Expected use case gaming, got %v", profile.UseCase)
	}

	// Test getting a profile
	retrieved, err := mgr.GetProfile("test_gaming")
	if err != nil {
		t.Fatalf("Failed to get profile: %v", err)
	}

	if retrieved.Name != profile.Name {
		t.Errorf("Retrieved profile name mismatch: expected %s, got %s", profile.Name, retrieved.Name)
	}

	// Test listing profiles
	profiles := mgr.ListProfiles()
	if len(profiles) == 0 {
		t.Error("Expected at least one profile (built-in profiles should exist)")
	}

	// Test setting active profile
	err = mgr.SetActiveProfile("test_gaming")
	if err != nil {
		t.Fatalf("Failed to set active profile: %v", err)
	}

	active := mgr.GetActiveProfile()
	if active == nil || active.Name != "test_gaming" {
		t.Error("Active profile not set correctly")
	}
}

// TestConfigurationProfileManager_BuiltInProfiles tests built-in profiles
func TestConfigurationProfileManager_BuiltInProfiles(t *testing.T) {
	tempDir := t.TempDir()
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	mgr := NewConfigurationProfileManager(tempDir, logger)

	expectedProfiles := []string{"gaming", "ml_training", "ml_inference", "rendering", "compute"}

	for _, profileName := range expectedProfiles {
		profile, err := mgr.GetProfile(profileName)
		if err != nil {
			t.Errorf("Built-in profile '%s' not found: %v", profileName, err)
			continue
		}

		if !profile.Recommended {
			t.Errorf("Built-in profile '%s' should be marked as recommended", profileName)
		}

		if profile.Config == nil {
			t.Errorf("Built-in profile '%s' has nil config", profileName)
		}
	}
}

// TestConfigurationProfileManager_Persistence tests saving and loading profiles
func TestConfigurationProfileManager_Persistence(t *testing.T) {
	tempDir := t.TempDir()
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	mgr := NewConfigurationProfileManager(tempDir, logger)

	// Create a test profile
	profile, err := mgr.CreateProfile("test_custom", UseCaseCustom, "Custom test profile")
	if err != nil {
		t.Fatalf("Failed to create profile: %v", err)
	}

	// Modify the profile config
	profile.Config.Description = "Modified description"
	profile.Config.AutoTuningEnabled = true

	// Save the profile
	err = mgr.SaveProfile("test_custom")
	if err != nil {
		t.Fatalf("Failed to save profile: %v", err)
	}

	// Check that the file was created
	filename := filepath.Join(tempDir, "test_custom.json")
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		t.Fatalf("Profile file was not created: %s", filename)
	}

	// Create a new manager and load the profile
	mgr2 := NewConfigurationProfileManager(tempDir, logger)

	loadedProfile, err := mgr2.GetProfile("test_custom")
	if err != nil {
		t.Fatalf("Failed to get loaded profile: %v", err)
	}

	if loadedProfile.Config.Description != "Modified description" {
		t.Errorf("Profile config not loaded correctly: expected 'Modified description', got '%s'",
			loadedProfile.Config.Description)
	}

	if !loadedProfile.Config.AutoTuningEnabled {
		t.Error("Profile auto tuning setting not loaded correctly")
	}
}

// TestDynamicTuningEngine_BasicOperations tests tuning engine functionality
func TestDynamicTuningEngine_BasicOperations(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	engine := NewDynamicTuningEngine(logger)

	// Test initial state
	if engine.enabled {
		t.Error("Tuning engine should start disabled")
	}

	if engine.isRunning {
		t.Error("Tuning engine should not be running initially")
	}

	// Test enabling
	engine.enabled = true

	// Test starting without performance monitor (should fail)
	ctx := context.Background()
	err := engine.Start(ctx, nil)
	if err == nil {
		t.Error("Starting tuning engine without performance monitor should fail")
	}

	// Test stopping when not running
	err = engine.Stop()
	if err != nil {
		t.Errorf("Stopping non-running engine should not error: %v", err)
	}
}

// TestConfigurationValidator_BasicValidation tests configuration validation
func TestConfigurationValidator_BasicValidation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	validator := NewConfigurationValidator(logger)

	// Test valid configuration
	validConfig := DefaultMultiGPUConfig()
	results := validator.ValidateConfiguration(validConfig)

	// Check for errors
	for _, result := range results {
		if result.Severity == ValidationSeverityError {
			t.Errorf("Valid configuration failed validation: %s", result.Message)
		}
	}

	// Test invalid configuration - negative device count
	invalidConfig := DefaultMultiGPUConfig()
	invalidConfig.DeviceConfig.MaxDevices = -1

	results = validator.ValidateConfiguration(invalidConfig)

	hasError := false
	for _, result := range results {
		if result.Severity == ValidationSeverityError {
			hasError = true
			break
		}
	}

	if !hasError {
		t.Error("Invalid configuration (negative device count) should fail validation")
	}
}

// TestUseCaseCustomization tests use case specific customizations
func TestUseCaseCustomization(t *testing.T) {
	tempDir := t.TempDir()
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	mgr := NewConfigurationProfileManager(tempDir, logger)

	testCases := []struct {
		useCase          UseCaseType
		expectedStrategy LoadBalancingStrategy
	}{
		{UseCaseGaming, LoadBalanceDynamic},
		{UseCaseMLTraining, LoadBalanceMemoryBased},
		{UseCaseMLInference, LoadBalanceComputeBased},
		{UseCaseRendering, LoadBalanceWeighted},
		{UseCaseCompute, LoadBalanceComputeBased},
	}

	for _, tc := range testCases {
		profile, err := mgr.CreateProfile(string(tc.useCase)+"_test", tc.useCase, "Test profile")
		if err != nil {
			t.Fatalf("Failed to create profile for %s: %v", tc.useCase, err)
		}

		if profile.Config.DeviceConfig.Strategy != tc.expectedStrategy {
			t.Errorf("Use case %s: expected strategy %v, got %v",
				tc.useCase, tc.expectedStrategy, profile.Config.DeviceConfig.Strategy)
		}
	}
}

// TestDefaultConfigurations tests that default configurations are valid
func TestDefaultConfigurations(t *testing.T) {
	// Test default multi-GPU config
	config := DefaultMultiGPUConfig()
	if config == nil {
		t.Fatal("Default multi-GPU config is nil")
	}

	if config.ProfileName == "" {
		t.Error("Default config should have a profile name")
	}

	if config.Version == "" {
		t.Error("Default config should have a version")
	}

	// Test default synchronization config
	syncConfig := DefaultSynchronizationConfig()
	if syncConfig.DefaultBarrierTimeout <= 0 {
		t.Error("Default barrier timeout should be positive")
	}

	if syncConfig.MessageQueueSize <= 0 {
		t.Error("Default message queue size should be positive")
	}

	// Test default tuning parameters
	tuningParams := DefaultTuningParameters()
	if tuningParams.ThermalTargetTemp <= 0 {
		t.Error("Default thermal target temp should be positive")
	}

	if tuningParams.PowerLimit <= 0 {
		t.Error("Default power limit should be positive")
	}
}

// TestTuningEventHistory tests tuning event recording
func TestTuningEventHistory(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	engine := NewDynamicTuningEngine(logger)

	// Create a test tuning action
	action := TuningAction{
		Type:      TuningTypeThermal,
		Parameter: "fan_speed",
		OldValue:  50,
		NewValue:  75,
		Reason:    "Temperature too high",
	}

	// Apply the action
	err := engine.applyTuningAction(action)
	if err != nil {
		t.Fatalf("Failed to apply tuning action: %v", err)
	}

	// Check that the event was recorded
	engine.mu.RLock()
	historyLen := len(engine.tuningHistory)
	engine.mu.RUnlock()

	if historyLen != 1 {
		t.Errorf("Expected 1 tuning event in history, got %d", historyLen)
	}

	engine.mu.RLock()
	event := engine.tuningHistory[0]
	engine.mu.RUnlock()

	if event.TuningType != TuningTypeThermal {
		t.Errorf("Expected tuning type thermal, got %v", event.TuningType)
	}

	if event.Parameter != "fan_speed" {
		t.Errorf("Expected parameter 'fan_speed', got '%s'", event.Parameter)
	}

	if event.NewValue != 75 {
		t.Errorf("Expected new value 75, got %v", event.NewValue)
	}
}

// TestConfigurationProfileManager_ErrorHandling tests error handling
func TestConfigurationProfileManager_ErrorHandling(t *testing.T) {
	tempDir := t.TempDir()
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	mgr := NewConfigurationProfileManager(tempDir, logger)

	// Test creating duplicate profile
	_, err := mgr.CreateProfile("duplicate", UseCaseGaming, "First profile")
	if err != nil {
		t.Fatalf("Failed to create first profile: %v", err)
	}

	_, err = mgr.CreateProfile("duplicate", UseCaseGaming, "Second profile")
	if err == nil {
		t.Error("Creating duplicate profile should fail")
	}

	// Test getting non-existent profile
	_, err = mgr.GetProfile("non_existent")
	if err == nil {
		t.Error("Getting non-existent profile should fail")
	}

	// Test setting active profile to non-existent
	err = mgr.SetActiveProfile("non_existent")
	if err == nil {
		t.Error("Setting active profile to non-existent should fail")
	}

	// Test saving non-existent profile
	err = mgr.SaveProfile("non_existent")
	if err == nil {
		t.Error("Saving non-existent profile should fail")
	}
}
