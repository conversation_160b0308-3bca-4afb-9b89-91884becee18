package gpu

import (
	"math/rand"
	"sync"
	"time"
)

// Experience<PERSON>uffer implements experience replay for online learning
type ExperienceBuffer struct {
	buffer  []WorkloadDataPoint
	maxSize int
	mu      sync.RWMutex
	rand    *rand.Rand
}

// NewExperienceBuffer creates a new experience buffer
func NewExperienceBuffer(maxSize int) *ExperienceBuffer {
	return &ExperienceBuffer{
		buffer:  make([]WorkloadDataPoint, 0, maxSize),
		maxSize: maxSize,
		rand:    rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// Add adds a new experience to the buffer
func (eb *ExperienceBuffer) Add(dataPoint WorkloadDataPoint) {
	eb.mu.Lock()
	defer eb.mu.Unlock()

	if len(eb.buffer) >= eb.maxSize {
		// Remove oldest entry (FIFO)
		eb.buffer = eb.buffer[1:]
	}

	eb.buffer = append(eb.buffer, dataPoint)
}

// Sample returns a random sample of experiences
func (eb *ExperienceBuffer) Sample(n int) []WorkloadDataPoint {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	if n >= len(eb.buffer) {
		// Return all experiences
		result := make([]WorkloadDataPoint, len(eb.buffer))
		copy(result, eb.buffer)
		return result
	}

	// Random sampling without replacement
	indices := eb.rand.Perm(len(eb.buffer))[:n]
	result := make([]WorkloadDataPoint, n)
	for i, idx := range indices {
		result[i] = eb.buffer[idx]
	}

	return result
}

// GetRecent returns the most recent n experiences
func (eb *ExperienceBuffer) GetRecent(n int) []WorkloadDataPoint {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	if n >= len(eb.buffer) {
		result := make([]WorkloadDataPoint, len(eb.buffer))
		copy(result, eb.buffer)
		return result
	}

	// Return the last n experiences
	start := len(eb.buffer) - n
	result := make([]WorkloadDataPoint, n)
	copy(result, eb.buffer[start:])
	return result
}

// Size returns the current number of experiences in the buffer
func (eb *ExperienceBuffer) Size() int {
	eb.mu.RLock()
	defer eb.mu.RUnlock()
	return len(eb.buffer)
}

// Clear removes all experiences from the buffer
func (eb *ExperienceBuffer) Clear() {
	eb.mu.Lock()
	defer eb.mu.Unlock()
	eb.buffer = eb.buffer[:0]
}

// GetAll returns all experiences in the buffer
func (eb *ExperienceBuffer) GetAll() []WorkloadDataPoint {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	result := make([]WorkloadDataPoint, len(eb.buffer))
	copy(result, eb.buffer)
	return result
}

// IsFull returns true if the buffer is at capacity
func (eb *ExperienceBuffer) IsFull() bool {
	eb.mu.RLock()
	defer eb.mu.RUnlock()
	return len(eb.buffer) >= eb.maxSize
}

// GetCapacity returns the maximum capacity of the buffer
func (eb *ExperienceBuffer) GetCapacity() int {
	return eb.maxSize
}

// GetOldest returns the oldest experience in the buffer
func (eb *ExperienceBuffer) GetOldest() *WorkloadDataPoint {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	if len(eb.buffer) == 0 {
		return nil
	}

	return &eb.buffer[0]
}

// GetNewest returns the newest experience in the buffer
func (eb *ExperienceBuffer) GetNewest() *WorkloadDataPoint {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	if len(eb.buffer) == 0 {
		return nil
	}

	return &eb.buffer[len(eb.buffer)-1]
}

// SampleByAge returns experiences from a specific time range
func (eb *ExperienceBuffer) SampleByAge(since time.Time, n int) []WorkloadDataPoint {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	// Filter experiences by age
	var candidates []WorkloadDataPoint
	for _, exp := range eb.buffer {
		if exp.Timestamp.After(since) {
			candidates = append(candidates, exp)
		}
	}

	if len(candidates) == 0 {
		return nil
	}

	if n >= len(candidates) {
		return candidates
	}

	// Random sampling from candidates
	indices := eb.rand.Perm(len(candidates))[:n]
	result := make([]WorkloadDataPoint, n)
	for i, idx := range indices {
		result[i] = candidates[idx]
	}

	return result
}

// GetStatistics returns buffer statistics
func (eb *ExperienceBuffer) GetStatistics() ExperienceBufferStats {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	if len(eb.buffer) == 0 {
		return ExperienceBufferStats{
			Size:     0,
			Capacity: eb.maxSize,
		}
	}

	// Calculate basic statistics
	var totalQueue float64
	var totalUtilization float64
	minTime := eb.buffer[0].Timestamp
	maxTime := eb.buffer[0].Timestamp

	for _, exp := range eb.buffer {
		totalQueue += float64(exp.QueueLength)
		totalUtilization += exp.AvgUtilization

		if exp.Timestamp.Before(minTime) {
			minTime = exp.Timestamp
		}
		if exp.Timestamp.After(maxTime) {
			maxTime = exp.Timestamp
		}
	}

	return ExperienceBufferStats{
		Size:               len(eb.buffer),
		Capacity:           eb.maxSize,
		AvgQueueLength:     totalQueue / float64(len(eb.buffer)),
		AvgUtilization:     totalUtilization / float64(len(eb.buffer)),
		OldestTimestamp:    minTime,
		NewestTimestamp:    maxTime,
		TimeSpan:           maxTime.Sub(minTime),
		UtilizationPercent: float64(len(eb.buffer)) / float64(eb.maxSize) * 100,
	}
}

// ExperienceBufferStats contains statistics about the experience buffer
type ExperienceBufferStats struct {
	Size               int           `json:"size"`
	Capacity           int           `json:"capacity"`
	AvgQueueLength     float64       `json:"avg_queue_length"`
	AvgUtilization     float64       `json:"avg_utilization"`
	OldestTimestamp    time.Time     `json:"oldest_timestamp"`
	NewestTimestamp    time.Time     `json:"newest_timestamp"`
	TimeSpan           time.Duration `json:"time_span"`
	UtilizationPercent float64       `json:"utilization_percent"`
}
