package gpu

import (
	"context"
	"fmt"
	"log"
	"math"
	"sort"
	"sync"
	"time"
)

// ClusterDynamicScaling manages dynamic scaling of the GPU cluster
type ClusterDynamicScaling struct {
	coordinator     *GPUResourceDiscovery
	distributor     *ClusterWorkloadDistributor
	faultTolerance  *ClusterFaultTolerance
	logger          *log.Logger
	mu              sync.RWMutex
	config          DynamicScalingConfig
	metrics         *ScalingMetrics
	predictor       *WorkloadPredictor
	nodeManager     *NodeLifecycleManager
	scalingDecision *ScalingDecisionEngine
	featureEngineer *FeatureEngineer
	isRunning       bool
	stopChan        chan struct{}
	wg              sync.WaitGroup
}

// DynamicScalingConfig configures the dynamic scaling behavior
type DynamicScalingConfig struct {
	// Reactive scaling thresholds
	QueueLengthScaleUpThreshold   int     `json:"queue_length_scale_up_threshold"`
	QueueLengthScaleDownThreshold int     `json:"queue_length_scale_down_threshold"`
	UtilizationScaleUpThreshold   float64 `json:"utilization_scale_up_threshold"`
	UtilizationScaleDownThreshold float64 `json:"utilization_scale_down_threshold"`

	// Timing controls
	ScaleUpCooldown      time.Duration `json:"scale_up_cooldown"`
	ScaleDownCooldown    time.Duration `json:"scale_down_cooldown"`
	ScaleDownGracePeriod time.Duration `json:"scale_down_grace_period"`
	MonitoringInterval   time.Duration `json:"monitoring_interval"`

	// Scaling limits
	MinNodes          int `json:"min_nodes"`
	MaxNodes          int `json:"max_nodes"`
	MaxScaleUpNodes   int `json:"max_scale_up_nodes"`
	MaxScaleDownNodes int `json:"max_scale_down_nodes"`

	// Node integration settings
	NodeWarmupPeriod          time.Duration `json:"node_warmup_period"`
	NodeInitializationTimeout time.Duration `json:"node_initialization_timeout"`

	// Predictive scaling
	EnablePredictiveScaling bool          `json:"enable_predictive_scaling"`
	PredictionWindow        time.Duration `json:"prediction_window"`
	PredictionAccuracy      float64       `json:"prediction_accuracy"`

	// Cost optimization
	EnableCostOptimization bool    `json:"enable_cost_optimization"`
	CostPerNodeHour        float64 `json:"cost_per_node_hour"`

	// Workload redistribution
	TaskMigrationTimeout    time.Duration `json:"task_migration_timeout"`
	MaxConcurrentMigrations int           `json:"max_concurrent_migrations"`
}

// ScalingMetrics tracks scaling performance and decisions
type ScalingMetrics struct {
	TotalScaleUpEvents   int64         `json:"total_scale_up_events"`
	TotalScaleDownEvents int64         `json:"total_scale_down_events"`
	SuccessfulScaleUps   int64         `json:"successful_scale_ups"`
	SuccessfulScaleDowns int64         `json:"successful_scale_downs"`
	FailedScaleUps       int64         `json:"failed_scale_ups"`
	FailedScaleDowns     int64         `json:"failed_scale_downs"`
	AverageScaleUpTime   time.Duration `json:"average_scale_up_time"`
	AverageScaleDownTime time.Duration `json:"average_scale_down_time"`
	NodesAdded           int64         `json:"nodes_added"`
	NodesRemoved         int64         `json:"nodes_removed"`
	TasksMigrated        int64         `json:"tasks_migrated"`
	CostSavings          float64       `json:"cost_savings"`
	LastScaleUpTime      time.Time     `json:"last_scale_up_time"`
	LastScaleDownTime    time.Time     `json:"last_scale_down_time"`
	LastUpdated          time.Time     `json:"last_updated"`
}

// WorkloadPredictor predicts future workload patterns
type WorkloadPredictor struct {
	historicalData []WorkloadDataPoint
	model          PredictionModel
	config         PredictionConfig
	mu             sync.RWMutex
}

// WorkloadDataPoint represents a historical workload measurement with enhanced features
type WorkloadDataPoint struct {
	// Basic metrics (existing)
	Timestamp      time.Time `json:"timestamp"`
	QueueLength    int       `json:"queue_length"`
	ActiveTasks    int       `json:"active_tasks"`
	AvgUtilization float64   `json:"avg_utilization"`
	NodesActive    int       `json:"nodes_active"`

	// Temporal features
	HourOfDay    int     `json:"hour_of_day"`   // 0-23
	DayOfWeek    int     `json:"day_of_week"`   // 0-6 (Sunday=0)
	DayOfMonth   int     `json:"day_of_month"`  // 1-31
	MonthOfYear  int     `json:"month_of_year"` // 1-12
	IsWeekend    bool    `json:"is_weekend"`
	IsHoliday    bool    `json:"is_holiday"`
	HourSin      float64 `json:"hour_sin"` // Cyclical encoding
	HourCos      float64 `json:"hour_cos"`
	DayOfWeekSin float64 `json:"day_of_week_sin"`
	DayOfWeekCos float64 `json:"day_of_week_cos"`

	// Rolling statistics (1h, 6h, 24h windows)
	QueueLengthMA1h   float64 `json:"queue_length_ma_1h"`
	QueueLengthMA6h   float64 `json:"queue_length_ma_6h"`
	QueueLengthMA24h  float64 `json:"queue_length_ma_24h"`
	UtilizationMA1h   float64 `json:"utilization_ma_1h"`
	UtilizationMA6h   float64 `json:"utilization_ma_6h"`
	UtilizationMA24h  float64 `json:"utilization_ma_24h"`
	UtilizationStd1h  float64 `json:"utilization_std_1h"`
	UtilizationStd6h  float64 `json:"utilization_std_6h"`
	UtilizationStd24h float64 `json:"utilization_std_24h"`

	// Lag features
	QueueLengthLag1h  int     `json:"queue_length_lag_1h"`
	QueueLengthLag6h  int     `json:"queue_length_lag_6h"`
	QueueLengthLag24h int     `json:"queue_length_lag_24h"`
	UtilizationLag1h  float64 `json:"utilization_lag_1h"`
	UtilizationLag6h  float64 `json:"utilization_lag_6h"`
	UtilizationLag24h float64 `json:"utilization_lag_24h"`

	// Rate of change features
	QueueLengthRateOfChange float64 `json:"queue_length_rate_of_change"` // per hour
	UtilizationRateOfChange float64 `json:"utilization_rate_of_change"`
	ActiveTasksRateOfChange float64 `json:"active_tasks_rate_of_change"`

	// Application-specific metrics
	TaskTypes              map[string]int `json:"task_types"` // Distribution of task types
	AvgTaskDuration        time.Duration  `json:"avg_task_duration"`
	MaxTaskDuration        time.Duration  `json:"max_task_duration"`
	MinTaskDuration        time.Duration  `json:"min_task_duration"`
	TaskDurationStd        time.Duration  `json:"task_duration_std"`
	HighPriorityTasks      int            `json:"high_priority_tasks"`
	MediumPriorityTasks    int            `json:"medium_priority_tasks"`
	LowPriorityTasks       int            `json:"low_priority_tasks"`
	AvgMemoryUsagePerTask  float64        `json:"avg_memory_usage_per_task"` // MB
	PeakMemoryUsagePerTask float64        `json:"peak_memory_usage_per_task"`
	AvgGPUUtilPerTask      float64        `json:"avg_gpu_util_per_task"` // %
	PeakGPUUtilPerTask     float64        `json:"peak_gpu_util_per_task"`

	// User/Project activity
	ActiveUsers         int            `json:"active_users"`
	ActiveProjects      int            `json:"active_projects"`
	TaskSubmissionRate  float64        `json:"task_submission_rate"` // tasks per minute
	UserDistribution    map[string]int `json:"user_distribution"`    // tasks per user
	ProjectDistribution map[string]int `json:"project_distribution"` // tasks per project

	// External factors - System health
	CPUUtilization        float64 `json:"cpu_utilization"`         // %
	MemoryUtilization     float64 `json:"memory_utilization"`      // %
	NetworkBandwidthUsage float64 `json:"network_bandwidth_usage"` // Mbps
	StorageIOPS           float64 `json:"storage_iops"`
	StorageThroughput     float64 `json:"storage_throughput"` // MB/s

	// External factors - Cluster state
	NodesInMaintenance    int           `json:"nodes_in_maintenance"`
	RecentScaleUpEvents   int           `json:"recent_scale_up_events"`   // Last hour
	RecentScaleDownEvents int           `json:"recent_scale_down_events"` // Last hour
	TimeSinceLastScaling  time.Duration `json:"time_since_last_scaling"`
	ClusterHealthScore    float64       `json:"cluster_health_score"` // 0-100

	// External factors - Environmental
	DataCenterTemperature   float64 `json:"datacenter_temperature"`    // Celsius
	PowerUsageEffectiveness float64 `json:"power_usage_effectiveness"` // PUE
	CoolingEfficiency       float64 `json:"cooling_efficiency"`        // %

	// Cost-related features
	CurrentSpotPrice float64 `json:"current_spot_price"`  // $/hour
	ElectricityCost  float64 `json:"electricity_cost"`    // $/kWh
	TimeOfUsePricing float64 `json:"time_of_use_pricing"` // multiplier

	// Feature interactions (computed)
	QueueUtilizationProduct float64 `json:"queue_utilization_product"`
	TasksPerNodeRatio       float64 `json:"tasks_per_node_ratio"`
	MemoryPressureIndex     float64 `json:"memory_pressure_index"`
	LoadVariabilityIndex    float64 `json:"load_variability_index"`

	// Polynomial features (computed)
	UtilizationSquared float64 `json:"utilization_squared"`
	QueueLengthSquared float64 `json:"queue_length_squared"`
	QueueLengthCubed   float64 `json:"queue_length_cubed"`

	// Time series decomposition components
	TrendComponent    float64 `json:"trend_component"`
	SeasonalComponent float64 `json:"seasonal_component"`
	ResidualComponent float64 `json:"residual_component"`
}

// PredictionModel interface for different prediction algorithms
type PredictionModel interface {
	Train(data []WorkloadDataPoint) error
	Predict(horizon time.Duration) (WorkloadPrediction, error)
	GetAccuracy() float64
}

// WorkloadPrediction represents predicted workload metrics
type WorkloadPrediction struct {
	Timestamp         time.Time     `json:"timestamp"`
	PredictedQueue    int           `json:"predicted_queue"`
	PredictedTasks    int           `json:"predicted_tasks"`
	PredictedNodes    int           `json:"predicted_nodes"`
	Confidence        float64       `json:"confidence"`
	RecommendedAction ScalingAction `json:"recommended_action"`
}

// PredictionConfig configures workload prediction
type PredictionConfig struct {
	HistoryWindow    time.Duration `json:"history_window"`
	MinDataPoints    int           `json:"min_data_points"`
	ModelType        string        `json:"model_type"`
	UpdateInterval   time.Duration `json:"update_interval"`
	SeasonalPatterns bool          `json:"seasonal_patterns"`
}

// NodeLifecycleManager manages node addition and removal
type NodeLifecycleManager struct {
	pendingNodes  map[string]*NodeIntegration
	removingNodes map[string]*NodeRemoval
	nodeProviders []NodeProvider
	config        NodeLifecycleConfig
	logger        *log.Logger
	mu            sync.RWMutex
}

// NodeIntegration tracks the integration process for new nodes
type NodeIntegration struct {
	NodeID        string                 `json:"node_id"`
	StartTime     time.Time              `json:"start_time"`
	Phase         IntegrationPhase       `json:"phase"`
	Progress      float64                `json:"progress"`
	EstimatedTime time.Duration          `json:"estimated_time"`
	Error         error                  `json:"error,omitempty"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// NodeRemoval tracks the removal process for nodes
type NodeRemoval struct {
	NodeID         string                 `json:"node_id"`
	StartTime      time.Time              `json:"start_time"`
	Phase          RemovalPhase           `json:"phase"`
	Progress       float64                `json:"progress"`
	TasksToMigrate []string               `json:"tasks_to_migrate"`
	MigratedTasks  []string               `json:"migrated_tasks"`
	Error          error                  `json:"error,omitempty"`
	Metadata       map[string]interface{} `json:"metadata"`
}

// IntegrationPhase represents stages of node integration
type IntegrationPhase string

const (
	IntegrationPhaseProvisioning IntegrationPhase = "provisioning"
	IntegrationPhaseInitializing IntegrationPhase = "initializing"
	IntegrationPhaseConfiguring  IntegrationPhase = "configuring"
	IntegrationPhaseWarmingUp    IntegrationPhase = "warming_up"
	IntegrationPhaseReady        IntegrationPhase = "ready"
	IntegrationPhaseFailed       IntegrationPhase = "failed"
)

// RemovalPhase represents stages of node removal
type RemovalPhase string

const (
	RemovalPhaseDraining     RemovalPhase = "draining"
	RemovalPhaseMigrating    RemovalPhase = "migrating"
	RemovalPhaseShuttingDown RemovalPhase = "shutting_down"
	RemovalPhaseComplete     RemovalPhase = "complete"
	RemovalPhaseFailed       RemovalPhase = "failed"
)

// NodeProvider interface for different node provisioning systems
type NodeProvider interface {
	ProvisionNode(ctx context.Context, spec NodeSpec) (*ClusterNode, error)
	TerminateNode(ctx context.Context, nodeID string) error
	GetNodeStatus(ctx context.Context, nodeID string) (NodeProviderStatus, error)
	ListAvailableSpecs() []NodeSpec
	EstimateProvisioningTime(spec NodeSpec) time.Duration
	GetCostPerHour(spec NodeSpec) float64
}

// NodeSpec defines the specification for a new node
type NodeSpec struct {
	GPUType          GPUType `json:"gpu_type"`
	GPUCount         int     `json:"gpu_count"`
	MemoryGB         int     `json:"memory_gb"`
	CPUCores         int     `json:"cpu_cores"`
	StorageGB        int     `json:"storage_gb"`
	NetworkBandwidth float64 `json:"network_bandwidth"`
	Region           string  `json:"region"`
	InstanceType     string  `json:"instance_type"`
	SpotInstance     bool    `json:"spot_instance"`
}

// NodeProviderStatus represents the status from a node provider
type NodeProviderStatus struct {
	State    string                 `json:"state"`
	Progress float64                `json:"progress"`
	Message  string                 `json:"message"`
	Metadata map[string]interface{} `json:"metadata"`
}

// NodeLifecycleConfig configures node lifecycle management
type NodeLifecycleConfig struct {
	DefaultNodeSpec         NodeSpec      `json:"default_node_spec"`
	MaxProvisioningTime     time.Duration `json:"max_provisioning_time"`
	MaxTerminationTime      time.Duration `json:"max_termination_time"`
	HealthCheckInterval     time.Duration `json:"health_check_interval"`
	EnableSpotInstances     bool          `json:"enable_spot_instances"`
	PreferredRegions        []string      `json:"preferred_regions"`
	MaxConcurrentOperations int           `json:"max_concurrent_operations"`
	NodeWarmupPeriod        time.Duration `json:"node_warmup_period"`
}

// ScalingDecisionEngine makes intelligent scaling decisions
type ScalingDecisionEngine struct {
	config    ScalingDecisionConfig
	history   []ScalingDecision
	evaluator *MetricsEvaluator
	mu        sync.RWMutex
}

// ScalingDecision represents a scaling decision and its outcome
type ScalingDecision struct {
	Timestamp     time.Time     `json:"timestamp"`
	Action        ScalingAction `json:"action"`
	Reason        string        `json:"reason"`
	TargetNodes   int           `json:"target_nodes"`
	CurrentNodes  int           `json:"current_nodes"`
	QueueLength   int           `json:"queue_length"`
	Utilization   float64       `json:"utilization"`
	Confidence    float64       `json:"confidence"`
	Success       bool          `json:"success"`
	ExecutionTime time.Duration `json:"execution_time"`
	Error         error         `json:"error,omitempty"`
}

// ScalingAction defines the type of scaling action
type ScalingAction string

const (
	ScalingActionNone      ScalingAction = "none"
	ScalingActionScaleUp   ScalingAction = "scale_up"
	ScalingActionScaleDown ScalingAction = "scale_down"
	ScalingActionOptimize  ScalingAction = "optimize"
)

// ScalingDecisionConfig configures the decision engine
type ScalingDecisionConfig struct {
	DecisionInterval    time.Duration `json:"decision_interval"`
	ConfidenceThreshold float64       `json:"confidence_threshold"`
	MaxDecisionHistory  int           `json:"max_decision_history"`
	WeightQueueLength   float64       `json:"weight_queue_length"`
	WeightUtilization   float64       `json:"weight_utilization"`
	WeightCost          float64       `json:"weight_cost"`
	WeightLatency       float64       `json:"weight_latency"`
}

// MetricsEvaluator evaluates cluster metrics for scaling decisions
type MetricsEvaluator struct {
	metrics ClusterMetrics
	trends  MetricsTrends
	mu      sync.RWMutex
}

// ClusterMetrics represents current cluster state
type ClusterMetrics struct {
	ActiveNodes       int           `json:"active_nodes"`
	QueueLength       int           `json:"queue_length"`
	ActiveTasks       int           `json:"active_tasks"`
	AvgGPUUtilization float64       `json:"avg_gpu_utilization"`
	AvgMemoryUsage    float64       `json:"avg_memory_usage"`
	AvgNetworkLatency time.Duration `json:"avg_network_latency"`
	TotalThroughput   float64       `json:"total_throughput"`
	CostPerHour       float64       `json:"cost_per_hour"`
	Timestamp         time.Time     `json:"timestamp"`
}

// MetricsTrends tracks trends in cluster metrics
type MetricsTrends struct {
	QueueLengthTrend TrendDirection `json:"queue_length_trend"`
	UtilizationTrend TrendDirection `json:"utilization_trend"`
	ThroughputTrend  TrendDirection `json:"throughput_trend"`
	LatencyTrend     TrendDirection `json:"latency_trend"`
	TrendConfidence  float64        `json:"trend_confidence"`
	TrendDuration    time.Duration  `json:"trend_duration"`
}

// TrendDirection indicates the direction of a metric trend
// TrendDirection is imported from performance_monitor.go

// DefaultDynamicScalingConfig returns a default configuration
func DefaultDynamicScalingConfig() DynamicScalingConfig {
	return DynamicScalingConfig{
		QueueLengthScaleUpThreshold:   100,
		QueueLengthScaleDownThreshold: 20,
		UtilizationScaleUpThreshold:   0.8,
		UtilizationScaleDownThreshold: 0.4,
		ScaleUpCooldown:               5 * time.Minute,
		ScaleDownCooldown:             15 * time.Minute,
		ScaleDownGracePeriod:          10 * time.Minute,
		MonitoringInterval:            30 * time.Second,
		MinNodes:                      1,
		MaxNodes:                      100,
		MaxScaleUpNodes:               5,
		MaxScaleDownNodes:             2,
		NodeWarmupPeriod:              2 * time.Minute,
		NodeInitializationTimeout:     10 * time.Minute,
		EnablePredictiveScaling:       true,
		PredictionWindow:              30 * time.Minute,
		PredictionAccuracy:            0.7,
		EnableCostOptimization:        true,
		CostPerNodeHour:               2.50,
		TaskMigrationTimeout:          5 * time.Minute,
		MaxConcurrentMigrations:       3,
	}
}

// NewClusterDynamicScaling creates a new dynamic scaling manager
func NewClusterDynamicScaling(
	coordinator *GPUResourceDiscovery,
	distributor *ClusterWorkloadDistributor,
	faultTolerance *ClusterFaultTolerance,
	config DynamicScalingConfig,
	logger *log.Logger,
) (*ClusterDynamicScaling, error) {
	if coordinator == nil {
		return nil, fmt.Errorf("coordinator cannot be nil")
	}
	if distributor == nil {
		return nil, fmt.Errorf("distributor cannot be nil")
	}
	if logger == nil {
		logger = log.Default()
	}

	scaling := &ClusterDynamicScaling{
		coordinator:    coordinator,
		distributor:    distributor,
		faultTolerance: faultTolerance,
		logger:         logger,
		config:         config,
		metrics:        &ScalingMetrics{LastUpdated: time.Now()},
		stopChan:       make(chan struct{}),
	}

	// Initialize sub-components
	scaling.predictor = NewWorkloadPredictor(PredictionConfig{
		HistoryWindow:    24 * time.Hour,
		MinDataPoints:    10,
		ModelType:        "linear_regression",
		UpdateInterval:   10 * time.Minute,
		SeasonalPatterns: true,
	})

	scaling.nodeManager = NewNodeLifecycleManager(NodeLifecycleConfig{
		DefaultNodeSpec: NodeSpec{
			GPUType:          GPUTypeCUDA,
			GPUCount:         1,
			MemoryGB:         32,
			CPUCores:         8,
			StorageGB:        500,
			NetworkBandwidth: 1000.0,
			Region:           "us-west-2",
			InstanceType:     "g5.2xlarge",
			SpotInstance:     false,
		},
		MaxProvisioningTime:     15 * time.Minute,
		MaxTerminationTime:      5 * time.Minute,
		HealthCheckInterval:     30 * time.Second,
		EnableSpotInstances:     true,
		PreferredRegions:        []string{"us-west-2", "us-east-1"},
		MaxConcurrentOperations: 5,
		NodeWarmupPeriod:        config.NodeWarmupPeriod,
	}, logger)

	scaling.scalingDecision = NewScalingDecisionEngine(ScalingDecisionConfig{
		DecisionInterval:    1 * time.Minute,
		ConfidenceThreshold: 0.7,
		MaxDecisionHistory:  1000,
		WeightQueueLength:   0.4,
		WeightUtilization:   0.3,
		WeightCost:          0.2,
		WeightLatency:       0.1,
	})

	return scaling, nil
}

// Start begins the dynamic scaling monitoring and decision-making process
func (cds *ClusterDynamicScaling) Start(ctx context.Context) error {
	cds.mu.Lock()
	defer cds.mu.Unlock()

	if cds.isRunning {
		return fmt.Errorf("dynamic scaling is already running")
	}

	cds.isRunning = true
	cds.logger.Printf("Starting dynamic cluster scaling with config: %+v", cds.config)

	// Start monitoring goroutine
	cds.wg.Add(1)
	go cds.monitoringLoop(ctx)

	// Start decision-making goroutine
	cds.wg.Add(1)
	go cds.decisionLoop(ctx)

	// Start prediction update goroutine if enabled
	if cds.config.EnablePredictiveScaling {
		cds.wg.Add(1)
		go cds.predictionLoop(ctx)
	}

	// Start metrics update goroutine
	cds.wg.Add(1)
	go cds.metricsLoop(ctx)

	return nil
}

// Stop stops the dynamic scaling process
func (cds *ClusterDynamicScaling) Stop() error {
	cds.mu.Lock()
	defer cds.mu.Unlock()

	if !cds.isRunning {
		return fmt.Errorf("dynamic scaling is not running")
	}

	cds.logger.Printf("Stopping dynamic cluster scaling")
	close(cds.stopChan)
	cds.wg.Wait()
	cds.isRunning = false

	return nil
}

// monitoringLoop continuously monitors cluster metrics
func (cds *ClusterDynamicScaling) monitoringLoop(ctx context.Context) {
	defer cds.wg.Done()
	ticker := time.NewTicker(cds.config.MonitoringInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-cds.stopChan:
			return
		case <-ticker.C:
			cds.collectMetrics()
		}
	}
}

// decisionLoop makes scaling decisions based on collected metrics
func (cds *ClusterDynamicScaling) decisionLoop(ctx context.Context) {
	defer cds.wg.Done()
	ticker := time.NewTicker(cds.scalingDecision.config.DecisionInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-cds.stopChan:
			return
		case <-ticker.C:
			cds.makeScalingDecision(ctx)
		}
	}
}

// predictionLoop updates workload predictions
func (cds *ClusterDynamicScaling) predictionLoop(ctx context.Context) {
	defer cds.wg.Done()
	ticker := time.NewTicker(cds.predictor.config.UpdateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-cds.stopChan:
			return
		case <-ticker.C:
			cds.updatePredictions()
		}
	}
}

// metricsLoop updates scaling metrics
func (cds *ClusterDynamicScaling) metricsLoop(ctx context.Context) {
	defer cds.wg.Done()
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-cds.stopChan:
			return
		case <-ticker.C:
			cds.updateMetrics()
		}
	}
}

// collectMetrics gathers current cluster metrics
func (cds *ClusterDynamicScaling) collectMetrics() {
	// Implementation would collect metrics from coordinator, distributor, etc.
	cds.logger.Printf("Collecting cluster metrics for scaling decisions")
}

// makeScalingDecision evaluates current state and makes scaling decisions
func (cds *ClusterDynamicScaling) makeScalingDecision(ctx context.Context) {
	metrics := cds.scalingDecision.evaluator.GetCurrentMetrics()

	// Check cooldown periods
	if !cds.canScale() {
		return
	}

	// Determine scaling action
	action := cds.determineScalingAction(metrics)

	if action != ScalingActionNone {
		cds.executeScalingAction(ctx, action, metrics)
	}
}

// updatePredictions updates workload predictions
func (cds *ClusterDynamicScaling) updatePredictions() {
	// Create base data point with core metrics
	basePoint := WorkloadDataPoint{
		Timestamp:      time.Now(),
		QueueLength:    cds.getCurrentQueueLength(),
		ActiveTasks:    cds.getCurrentActiveTasks(),
		AvgUtilization: cds.getCurrentUtilization(),
		NodesActive:    cds.getCurrentActiveNodes(),
	}

	// Enhance with feature engineering if we have a feature engineer
	if cds.featureEngineer != nil {
		// Get historical data for feature computation
		historicalData := cds.predictor.GetHistoricalData()

		// Extract all enhanced features
		enhancedPoint := cds.featureEngineer.ExtractAllFeatures(basePoint, historicalData)
		cds.predictor.AddDataPoint(enhancedPoint)
	} else {
		// Fallback to basic data point
		cds.predictor.AddDataPoint(basePoint)
	}

	// Generate prediction
	prediction, err := cds.predictor.model.Predict(cds.config.PredictionWindow)
	if err != nil {
		cds.logger.Printf("Failed to generate workload prediction: %v", err)
		return
	}

	cds.logger.Printf("Workload prediction: %+v", prediction)
}

// updateMetrics updates scaling performance metrics
func (cds *ClusterDynamicScaling) updateMetrics() {
	cds.mu.Lock()
	defer cds.mu.Unlock()
	cds.metrics.LastUpdated = time.Now()
}

// Helper methods for metric collection
func (cds *ClusterDynamicScaling) getCurrentQueueLength() int {
	// Implementation would get queue length from distributor
	return 0
}

func (cds *ClusterDynamicScaling) getCurrentActiveTasks() int {
	// Implementation would get active tasks from distributor
	return 0
}

func (cds *ClusterDynamicScaling) getCurrentUtilization() float64 {
	// Implementation would get utilization from coordinator
	return 0.0
}

func (cds *ClusterDynamicScaling) getCurrentActiveNodes() int {
	// Implementation would get active nodes from coordinator
	return 1
}

// canScale checks if scaling is allowed based on cooldown periods
func (cds *ClusterDynamicScaling) canScale() bool {
	cds.mu.RLock()
	defer cds.mu.RUnlock()

	now := time.Now()

	// Check scale-up cooldown
	if now.Sub(cds.metrics.LastScaleUpTime) < cds.config.ScaleUpCooldown {
		return false
	}

	// Check scale-down cooldown
	if now.Sub(cds.metrics.LastScaleDownTime) < cds.config.ScaleDownCooldown {
		return false
	}

	return true
}

// determineScalingAction decides what scaling action to take
func (cds *ClusterDynamicScaling) determineScalingAction(metrics ClusterMetrics) ScalingAction {
	// Check scale-up conditions
	if metrics.QueueLength > cds.config.QueueLengthScaleUpThreshold ||
		metrics.AvgGPUUtilization > cds.config.UtilizationScaleUpThreshold {
		if metrics.ActiveNodes < cds.config.MaxNodes {
			return ScalingActionScaleUp
		}
	}

	// Check scale-down conditions
	if metrics.QueueLength < cds.config.QueueLengthScaleDownThreshold &&
		metrics.AvgGPUUtilization < cds.config.UtilizationScaleDownThreshold {
		if metrics.ActiveNodes > cds.config.MinNodes {
			return ScalingActionScaleDown
		}
	}

	return ScalingActionNone
}

// executeScalingAction performs the determined scaling action
func (cds *ClusterDynamicScaling) executeScalingAction(ctx context.Context, action ScalingAction, metrics ClusterMetrics) {
	startTime := time.Now()

	decision := ScalingDecision{
		Timestamp:    startTime,
		Action:       action,
		CurrentNodes: metrics.ActiveNodes,
		QueueLength:  metrics.QueueLength,
		Utilization:  metrics.AvgGPUUtilization,
	}

	var err error

	switch action {
	case ScalingActionScaleUp:
		err = cds.scaleUp(ctx, cds.calculateNodesNeeded(metrics))
		decision.Reason = "High queue length or utilization"

	case ScalingActionScaleDown:
		err = cds.scaleDown(ctx, 1)
		decision.Reason = "Low queue length and utilization"
	}

	decision.ExecutionTime = time.Since(startTime)
	decision.Success = err == nil
	decision.Error = err

	cds.recordScalingDecision(decision)

	if err != nil {
		cds.logger.Printf("Failed to execute scaling action %s: %v", action, err)
	} else {
		cds.logger.Printf("Successfully executed scaling action %s in %v", action, decision.ExecutionTime)
	}
}

// calculateNodesNeeded determines how many nodes to add
func (cds *ClusterDynamicScaling) calculateNodesNeeded(metrics ClusterMetrics) int {
	tasksPerNode := 10 // This would be configurable
	nodesNeeded := int(math.Ceil(float64(metrics.QueueLength) / float64(tasksPerNode)))

	// Limit to max scale-up nodes
	if nodesNeeded > cds.config.MaxScaleUpNodes {
		nodesNeeded = cds.config.MaxScaleUpNodes
	}

	return nodesNeeded
}

// scaleUp adds nodes to the cluster
func (cds *ClusterDynamicScaling) scaleUp(ctx context.Context, nodeCount int) error {
	cds.logger.Printf("Scaling up cluster by %d nodes", nodeCount)

	for i := 0; i < nodeCount; i++ {
		err := cds.nodeManager.AddNode(ctx, cds.nodeManager.config.DefaultNodeSpec)
		if err != nil {
			return fmt.Errorf("failed to add node %d: %w", i+1, err)
		}
	}

	cds.mu.Lock()
	cds.metrics.TotalScaleUpEvents++
	cds.metrics.SuccessfulScaleUps++
	cds.metrics.NodesAdded += int64(nodeCount)
	cds.metrics.LastScaleUpTime = time.Now()
	cds.mu.Unlock()

	return nil
}

// scaleDown removes nodes from the cluster
func (cds *ClusterDynamicScaling) scaleDown(ctx context.Context, nodeCount int) error {
	cds.logger.Printf("Scaling down cluster by %d nodes", nodeCount)

	// Select nodes for removal (least utilized first)
	nodesToRemove := cds.selectNodesForRemoval(nodeCount)

	for _, nodeID := range nodesToRemove {
		err := cds.nodeManager.RemoveNode(ctx, nodeID)
		if err != nil {
			return fmt.Errorf("failed to remove node %s: %w", nodeID, err)
		}
	}

	cds.mu.Lock()
	cds.metrics.TotalScaleDownEvents++
	cds.metrics.SuccessfulScaleDowns++
	cds.metrics.NodesRemoved += int64(len(nodesToRemove))
	cds.metrics.LastScaleDownTime = time.Now()
	cds.mu.Unlock()

	return nil
}

// selectNodesForRemoval chooses which nodes to remove during scale-down
func (cds *ClusterDynamicScaling) selectNodesForRemoval(count int) []string {
	// Get all nodes and their utilization
	nodes := cds.coordinator.GetActiveNodes()

	// Sort by utilization (ascending - remove least utilized first)
	sort.Slice(nodes, func(i, j int) bool {
		utilizationI := cds.calculateNodeGPUUtilization(nodes[i])
		utilizationJ := cds.calculateNodeGPUUtilization(nodes[j])
		return utilizationI < utilizationJ
	})

	// Select nodes for removal
	var nodesToRemove []string
	for i := 0; i < count && i < len(nodes); i++ {
		nodesToRemove = append(nodesToRemove, nodes[i].ID)
	}

	return nodesToRemove
}

// recordScalingDecision records a scaling decision for analysis
func (cds *ClusterDynamicScaling) recordScalingDecision(decision ScalingDecision) {
	cds.scalingDecision.mu.Lock()
	defer cds.scalingDecision.mu.Unlock()

	cds.scalingDecision.history = append(cds.scalingDecision.history, decision)

	// Limit history size
	maxHistory := cds.scalingDecision.config.MaxDecisionHistory
	if len(cds.scalingDecision.history) > maxHistory {
		cds.scalingDecision.history = cds.scalingDecision.history[len(cds.scalingDecision.history)-maxHistory:]
	}
}

// GetMetrics returns current scaling metrics
func (cds *ClusterDynamicScaling) GetMetrics() ScalingMetrics {
	cds.mu.RLock()
	defer cds.mu.RUnlock()
	return *cds.metrics
}

// GetConfig returns the current scaling configuration
func (cds *ClusterDynamicScaling) GetConfig() DynamicScalingConfig {
	cds.mu.RLock()
	defer cds.mu.RUnlock()
	return cds.config
}

// UpdateConfig updates the scaling configuration
func (cds *ClusterDynamicScaling) UpdateConfig(config DynamicScalingConfig) error {
	cds.mu.Lock()
	defer cds.mu.Unlock()

	cds.config = config
	cds.logger.Printf("Updated dynamic scaling configuration: %+v", config)
	return nil
}

// GetScalingHistory returns recent scaling decisions
func (cds *ClusterDynamicScaling) GetScalingHistory(limit int) []ScalingDecision {
	cds.scalingDecision.mu.RLock()
	defer cds.scalingDecision.mu.RUnlock()

	history := cds.scalingDecision.history
	if limit > 0 && len(history) > limit {
		history = history[len(history)-limit:]
	}

	return append([]ScalingDecision{}, history...)
}

// IsRunning returns whether the dynamic scaling is currently active
func (cds *ClusterDynamicScaling) IsRunning() bool {
	cds.mu.RLock()
	defer cds.mu.RUnlock()
	return cds.isRunning
}

// NewScalingDecisionEngine creates a new scaling decision engine
func NewScalingDecisionEngine(config ScalingDecisionConfig) *ScalingDecisionEngine {
	return &ScalingDecisionEngine{
		config:    config,
		history:   make([]ScalingDecision, 0),
		evaluator: &MetricsEvaluator{},
	}
}

// calculateNodeGPUUtilization calculates the average GPU utilization for a node
func (cds *ClusterDynamicScaling) calculateNodeGPUUtilization(node *ClusterNode) float64 {
	if len(node.GPUDevices) == 0 {
		return 0.0
	}

	totalUtilization := 0.0
	for _, gpu := range node.GPUDevices {
		if gpu.Utilization != nil {
			totalUtilization += gpu.Utilization.ComputePercent
		}
	}

	return totalUtilization / float64(len(node.GPUDevices))
}

// GetCurrentMetrics returns the current cluster metrics
func (me *MetricsEvaluator) GetCurrentMetrics() ClusterMetrics {
	me.mu.RLock()
	defer me.mu.RUnlock()
	return me.metrics
}
