//go:build (linux || windows) && cuda

package backends

/*
#cgo CFLAGS: -I/usr/local/cuda/include
#cgo LDFLAGS: -L/usr/local/cuda/lib64 -lcuda -lcudart
#include "cuda_shared.h"
*/
import "C"

import (
	"fmt"
	"unsafe"

	"neuralmetergo/internal/gpu/types"
)

// CUDAContext implements the unified GPUContext interface for CUDA
type CUDAContext struct {
	device       *types.GPUDevice
	deviceIndex  int
	valid        bool
	nativeHandle uintptr
	cudaContext  C.CUcontext
	cudaDevice   C.CUdevice
}

// NewCUDAContext creates a new CUDA context
func NewCUDAContext(device *types.GPUDevice, deviceIndex int) (*CUDAContext, error) {
	ctx := &CUDAContext{
		device:      device,
		deviceIndex: deviceIndex,
		valid:       false,
	}

	// Initialize the CUDA context
	if err := ctx.initialize(); err != nil {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 2100,
			fmt.Sprintf("Failed to initialize CUDA context: %v", err), deviceIndex)
	}

	ctx.valid = true
	return ctx, nil
}

// GetDevice returns the GPU device associated with this context
func (ctx *CUDAContext) GetDevice() *types.GPUDevice {
	return ctx.device
}

// IsValid returns whether the context is valid
func (ctx *CUDAContext) IsValid() bool {
	return ctx.valid && ctx.cudaContext != nil
}

// GetDeviceID returns the device ID
func (ctx *CUDAContext) GetDeviceID() string {
	return ctx.device.ID
}

// GetBackend returns the backend name
func (ctx *CUDAContext) GetBackend() string {
	return "CUDA"
}

// Synchronize waits for all operations in the context to complete
func (ctx *CUDAContext) Synchronize() error {
	if !ctx.valid {
		return types.NewGPUError(types.ErrorTypeExecution, 2104, "context is invalid", ctx.deviceIndex)
	}

	// Set current context
	if result := C.cuda_ctx_set_current(ctx.cudaContext); result != C.CUDA_SUCCESS {
		return types.NewGPUError(types.ErrorTypeExecution, 2102,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), ctx.deviceIndex)
	}

	// Synchronize device
	if err := C.cuda_device_synchronize(); err != C.cudaSuccess {
		return types.NewGPUError(types.ErrorTypeExecution, 2103,
			fmt.Sprintf("CUDA device synchronization failed: %s", C.GoString(C.cuda_get_error_string(err))), ctx.deviceIndex)
	}

	return nil
}

// Destroy releases the CUDA context
func (ctx *CUDAContext) Destroy() error {
	if !ctx.valid {
		return types.NewGPUError(types.ErrorTypeExecution, 2104, "context is invalid", ctx.deviceIndex)
	}

	if err := ctx.cleanup(); err != nil {
		return types.NewGPUError(types.ErrorTypeCleanup, 2101,
			fmt.Sprintf("Failed to cleanup CUDA context: %v", err), ctx.deviceIndex)
	}

	ctx.valid = false
	ctx.nativeHandle = 0
	return nil
}

// GetNativeHandle returns the native CUDA context handle
func (ctx *CUDAContext) GetNativeHandle() uintptr {
	return ctx.nativeHandle
}

// GetDeviceIndex returns the CUDA device index
func (ctx *CUDAContext) GetDeviceIndex() int {
	return ctx.deviceIndex
}

// Private methods

func (ctx *CUDAContext) initialize() error {
	// Get CUDA device
	if result := C.cuda_device_get(&ctx.cudaDevice, C.int(ctx.deviceIndex)); result != C.CUDA_SUCCESS {
		return fmt.Errorf("failed to get CUDA device %d: %s", ctx.deviceIndex, C.GoString(C.cuda_get_cu_error_string(result)))
	}

	// Create CUDA context
	if result := C.cuda_ctx_create(&ctx.cudaContext, 0, ctx.cudaDevice); result != C.CUDA_SUCCESS {
		return fmt.Errorf("failed to create CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result)))
	}

	ctx.nativeHandle = uintptr(unsafe.Pointer(ctx.cudaContext))
	return nil
}

func (ctx *CUDAContext) cleanup() error {
	if ctx.cudaContext != nil {
		if result := C.cuda_ctx_destroy(ctx.cudaContext); result != C.CUDA_SUCCESS {
			return fmt.Errorf("failed to destroy CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result)))
		}
		ctx.cudaContext = nil
	}
	return nil
}
