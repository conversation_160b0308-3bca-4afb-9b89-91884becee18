#ifndef CUDA_SHARED_H
#define CUDA_SHARED_H

#include <cuda.h>
#include <cuda_runtime.h>
#include <cuda_runtime_api.h>
#include <stdio.h>
#include <stdlib.h>

// CUDA initialization and device management
static inline CUresult cuda_init() {
    return cuInit(0);
}

static inline CUresult cuda_device_get_count(int *count) {
    return cuDeviceGetCount(count);
}

static inline CUresult cuda_device_get(CUdevice *device, int ordinal) {
    return cuDeviceGet(device, ordinal);
}

static inline CUresult cuda_device_get_name(char *name, int len, CUdevice dev) {
    return cuDeviceGetName(name, len, dev);
}

static inline CUresult cuda_device_get_attribute(int *pi, CUdevice_attribute attrib, CUdevice dev) {
    return cuDeviceGetAttribute(pi, attrib, dev);
}

static inline CUresult cuda_device_total_mem(size_t *bytes, CUdevice dev) {
    return cuDeviceTotalMem_v2(bytes, dev);
}

// CUDA context management
static inline CUresult cuda_ctx_create(CUcontext *pctx, unsigned int flags, CUdevice dev) {
    return cuCtxCreate(pctx, flags, dev);
}

static inline CUresult cuda_ctx_destroy(CUcontext ctx) {
    return cuCtxDestroy(ctx);
}

static inline CUresult cuda_ctx_set_current(CUcontext ctx) {
    return cuCtxSetCurrent(ctx);
}

// CUDA memory management
static inline cudaError_t cuda_malloc(void **devPtr, size_t size) {
    return cudaMalloc(devPtr, size);
}

static inline cudaError_t cuda_free(void *devPtr) {
    return cudaFree(devPtr);
}

static inline cudaError_t cuda_memcpy_h2d(void *dst, const void *src, size_t count) {
    return cudaMemcpy(dst, src, count, cudaMemcpyHostToDevice);
}

static inline cudaError_t cuda_memcpy_d2h(void *dst, const void *src, size_t count) {
    return cudaMemcpy(dst, src, count, cudaMemcpyDeviceToHost);
}

static inline cudaError_t cuda_memcpy_d2d(void *dst, const void *src, size_t count) {
    return cudaMemcpy(dst, src, count, cudaMemcpyDeviceToDevice);
}

// CUDA stream management
static inline cudaError_t cuda_stream_create(cudaStream_t *pStream) {
    return cudaStreamCreate(pStream);
}

static inline cudaError_t cuda_stream_destroy(cudaStream_t stream) {
    return cudaStreamDestroy(stream);
}

static inline cudaError_t cuda_stream_synchronize(cudaStream_t stream) {
    return cudaStreamSynchronize(stream);
}

static inline cudaError_t cuda_stream_query(cudaStream_t stream) {
    return cudaStreamQuery(stream);
}

// CUDA event management
static inline cudaError_t cuda_event_create(cudaEvent_t *event) {
    return cudaEventCreate(event);
}

static inline cudaError_t cuda_event_destroy(cudaEvent_t event) {
    return cudaEventDestroy(event);
}

static inline cudaError_t cuda_event_record(cudaEvent_t event, cudaStream_t stream) {
    return cudaEventRecord(event, stream);
}

static inline cudaError_t cuda_event_synchronize(cudaEvent_t event) {
    return cudaEventSynchronize(event);
}

static inline cudaError_t cuda_event_query(cudaEvent_t event) {
    return cudaEventQuery(event);
}

static inline cudaError_t cuda_event_elapsed_time(float *ms, cudaEvent_t start, cudaEvent_t end) {
    return cudaEventElapsedTime(ms, start, end);
}

// CUDA kernel compilation and execution
static inline CUresult cuda_module_load_data_ex(CUmodule *module, const void *image, unsigned int numOptions, CUjit_option *options, void **optionValues) {
    return cuModuleLoadDataEx(module, image, numOptions, options, optionValues);
}

static inline CUresult cuda_module_get_function(CUfunction *hfunc, CUmodule hmod, const char *name) {
    return cuModuleGetFunction(hfunc, hmod, name);
}

static inline CUresult cuda_module_unload(CUmodule hmod) {
    return cuModuleUnload(hmod);
}

static inline CUresult cuda_launch_kernel(CUfunction f, unsigned int gridDimX, unsigned int gridDimY, unsigned int gridDimZ, unsigned int blockDimX, unsigned int blockDimY, unsigned int blockDimZ, unsigned int sharedMemBytes, CUstream hStream, void **kernelParams, void **extra) {
    return cuLaunchKernel(f, gridDimX, gridDimY, gridDimZ, blockDimX, blockDimY, blockDimZ, sharedMemBytes, hStream, kernelParams, extra);
}

// CUDA error handling
static inline const char* cuda_get_error_string(cudaError_t error) {
    return cudaGetErrorString(error);
}

static inline const char* cuda_get_cu_error_string(CUresult error) {
    const char* str;
    cuGetErrorString(error, &str);
    return str;
}

// CUDA device properties
static inline cudaError_t cuda_get_device_properties(struct cudaDeviceProp *prop, int device) {
    return cudaGetDeviceProperties(prop, device);
}

static inline cudaError_t cuda_device_synchronize() {
    return cudaDeviceSynchronize();
}

#endif // CUDA_SHARED_H 