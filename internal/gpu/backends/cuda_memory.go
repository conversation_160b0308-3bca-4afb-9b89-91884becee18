//go:build (linux || windows) && cuda

package backends

/*
#cgo CFLAGS: -I/usr/local/cuda/include
#cgo LDFLAGS: -L/usr/local/cuda/lib64 -lcuda -lcudart
#include "cuda_shared.h"
*/
import "C"

import (
	"fmt"
	"neuralmetergo/internal/gpu/types"
	"sync"
	"time"
	"unsafe"
)



// NewCUDAMemoryManager creates a new CUDA memory manager
func NewCUDAMemoryManager(ctx *CUDAContext) (types.GPUMemoryManager, error) {
	return &CUDAMemoryManager{
		context:        ctx,
		allocations:    make(map[uintptr]*CUDAMemory),
		totalAllocated: 0,
		peakAllocated:  0,
	}, nil
}

// NewCUDAExecutor creates a new CUDA executor
func NewCUDAExecutor(ctx *CUDAContext) (types.GPUExecutor, error) {
	return &CUDAExecutor{
		context: ctx,
		streams: make(map[string]*CUDAStream),
		kernels: make(map[string]*CUDAKernel),
	}, nil
}

// CUDAMemoryManager implements GPUMemoryManager for CUDA
type CUDAMemoryManager struct {
	context        *CUDAContext
	allocations    map[uintptr]*CUDAMemory
	totalAllocated uint64
	peakAllocated  uint64
	mutex          sync.RWMutex
}

func (m *CUDAMemoryManager) Allocate(size uint64) (types.GPUMemory, error) {
	return m.AllocateType(size, types.MemoryTypeDiscrete)
}

func (m *CUDAMemoryManager) AllocateType(size uint64, memType types.MemoryType) (types.GPUMemory, error) {
	if !m.context.IsValid() {
		return nil, types.NewGPUError(types.ErrorTypeMemory, 2201, "Context is invalid", m.context.deviceIndex)
	}

	if size == 0 {
		return nil, types.NewGPUError(types.ErrorTypeMemory, 2202, "Cannot allocate zero bytes", m.context.deviceIndex)
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(m.context.cudaContext); result != C.CUDA_SUCCESS {
		return nil, types.NewGPUError(types.ErrorTypeMemory, 2203,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), m.context.deviceIndex)
	}

	// Allocate CUDA memory
	var devicePtr unsafe.Pointer
	if err := C.cuda_malloc(&devicePtr, C.size_t(size)); err != C.cudaSuccess {
		return nil, types.NewGPUError(types.ErrorTypeMemory, 2204,
			fmt.Sprintf("CUDA memory allocation failed: %s", C.GoString(C.cuda_get_error_string(err))), m.context.deviceIndex)
	}

	ptr := uintptr(devicePtr)
	memory := &CUDAMemory{
		ptr:       ptr,
		size:      size,
		typ:       memType,
		manager:   m,
		devicePtr: devicePtr,
	}

	m.allocations[ptr] = memory
	m.totalAllocated += size
	if m.totalAllocated > m.peakAllocated {
		m.peakAllocated = m.totalAllocated
	}

	return memory, nil
}

func (m *CUDAMemoryManager) GetStats() types.GPUMemoryStats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return types.GPUMemoryStats{
		TotalAllocated: m.totalAllocated,
		PeakAllocated:  m.peakAllocated,
		CurrentUsed:    m.totalAllocated,
		FreeMemory:     m.context.device.Memory.Free - m.totalAllocated,
		Fragmentation:  0.0, // CUDA handles fragmentation internally
		LastUpdate:     time.Now(),
	}
}

func (m *CUDAMemoryManager) Cleanup() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(m.context.cudaContext); result != C.CUDA_SUCCESS {
		return types.NewGPUError("cleanup", 2205,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), m.context.deviceIndex)
	}

	// Free all allocations
	for ptr, mem := range m.allocations {
		if mem.devicePtr != nil {
			C.cuda_free(mem.devicePtr)
		}
		mem.freed = true
		delete(m.allocations, ptr)
	}
	m.totalAllocated = 0
	return nil
}

// CUDAMemory implements GPUMemory for CUDA
type CUDAMemory struct {
	ptr       uintptr
	size      uint64
	typ       types.MemoryType
	manager   *CUDAMemoryManager
	freed     bool
	devicePtr unsafe.Pointer
}

func (m *CUDAMemory) Ptr() uintptr           { return m.ptr }
func (m *CUDAMemory) Size() uint64           { return m.size }
func (m *CUDAMemory) Type() types.MemoryType { return m.typ }

func (m *CUDAMemory) Free() error {
	if m.freed {
		return types.NewGPUError(types.ErrorTypeMemory, 2206, "Memory already freed", -1)
	}

	m.manager.mutex.Lock()
	defer m.manager.mutex.Unlock()

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(m.manager.context.cudaContext); result != C.CUDA_SUCCESS {
		return types.NewGPUError(types.ErrorTypeMemory, 2207,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), -1)
	}

	if m.devicePtr != nil {
		if err := C.cuda_free(m.devicePtr); err != C.cudaSuccess {
			return types.NewGPUError(types.ErrorTypeMemory, 2208,
				fmt.Sprintf("Failed to free CUDA memory: %s", C.GoString(C.cuda_get_error_string(err))), -1)
		}
		m.devicePtr = nil
	}

	delete(m.manager.allocations, m.ptr)
	m.manager.totalAllocated -= m.size
	m.freed = true
	return nil
}

func (m *CUDAMemory) CopyFrom(src []byte) error {
	if m.freed {
		return types.NewGPUError(types.ErrorTypeMemory, 2209, "Cannot copy to freed memory", -1)
	}

	if uint64(len(src)) > m.size {
		return types.NewGPUError(types.ErrorTypeMemory, 2210, "Source data too large for buffer", -1)
	}

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(m.manager.context.cudaContext); result != C.CUDA_SUCCESS {
		return types.NewGPUError(types.ErrorTypeMemory, 2211,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), -1)
	}

	// Copy from host to device
	if err := C.cuda_memcpy_h2d(m.devicePtr, unsafe.Pointer(&src[0]), C.size_t(len(src))); err != C.cudaSuccess {
		return types.NewGPUError(types.ErrorTypeMemory, 2212,
			fmt.Sprintf("CUDA host to device copy failed: %s", C.GoString(C.cuda_get_error_string(err))), -1)
	}

	return nil
}

func (m *CUDAMemory) CopyTo(dst []byte) error {
	if m.freed {
		return types.NewGPUError(types.ErrorTypeMemory, 2213, "Cannot copy from freed memory", -1)
	}

	if uint64(len(dst)) > m.size {
		return types.NewGPUError(types.ErrorTypeMemory, 2214, "Destination buffer too small", -1)
	}

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(m.manager.context.cudaContext); result != C.CUDA_SUCCESS {
		return types.NewGPUError(types.ErrorTypeMemory, 2215,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), -1)
	}

	// Copy from device to host
	if err := C.cuda_memcpy_d2h(unsafe.Pointer(&dst[0]), m.devicePtr, C.size_t(len(dst))); err != C.cudaSuccess {
		return types.NewGPUError(types.ErrorTypeMemory, 2216,
			fmt.Sprintf("CUDA device to host copy failed: %s", C.GoString(C.cuda_get_error_string(err))), -1)
	}

	return nil
}

func (m *CUDAMemory) CopyFromGPU(src types.GPUMemory) error {
	if m.freed {
		return types.NewGPUError(types.ErrorTypeMemory, 2217, "Cannot copy to freed memory", -1)
	}

	srcCUDA, ok := src.(*CUDAMemory)
	if !ok {
		return types.NewGPUError(types.ErrorTypeMemory, 2218, "Source is not CUDA memory", -1)
	}

	if srcCUDA.size > m.size {
		return types.NewGPUError(types.ErrorTypeMemory, 2219, "Source memory too large", -1)
	}

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(m.manager.context.cudaContext); result != C.CUDA_SUCCESS {
		return types.NewGPUError(types.ErrorTypeMemory, 2220,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), -1)
	}

	// Copy from device to device
	if err := C.cuda_memcpy_d2d(m.devicePtr, srcCUDA.devicePtr, C.size_t(srcCUDA.size)); err != C.cudaSuccess {
		return types.NewGPUError(types.ErrorTypeMemory, 2221,
			fmt.Sprintf("CUDA device to device copy failed: %s", C.GoString(C.cuda_get_error_string(err))), -1)
	}

	return nil
}

// CUDAExecutor implements GPUExecutor for CUDA
type CUDAExecutor struct {
	context *CUDAContext
	streams map[string]*CUDAStream
	kernels map[string]*CUDAKernel
	mutex   sync.RWMutex
}

func (e *CUDAExecutor) CreateKernel(source string, entryPoint string) (types.GPUKernel, error) {
	if !e.context.IsValid() {
		return nil, types.NewGPUError("compute", 2301, "Context is invalid", e.context.deviceIndex)
	}

	if source == "" {
		return nil, types.NewGPUError("compute", 2302, "Kernel source cannot be empty", e.context.deviceIndex)
	}

	if entryPoint == "" {
		return nil, types.NewGPUError("compute", 2303, "Entry point cannot be empty", e.context.deviceIndex)
	}

	e.mutex.Lock()
	defer e.mutex.Unlock()

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(e.context.cudaContext); result != C.CUDA_SUCCESS {
		return nil, types.NewGPUError("compute", 2304,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), e.context.deviceIndex)
	}

	// In a real implementation, we would compile the source to PTX
	// For now, we'll assume the source is already compiled PTX code
	sourcePtr := C.CString(source)
	defer C.free(unsafe.Pointer(sourcePtr))

	var module C.CUmodule
	if result := C.cuda_module_load_data_ex(&module, unsafe.Pointer(sourcePtr), 0, nil, nil); result != C.CUDA_SUCCESS {
		return nil, types.NewGPUError("compute", 2305,
			fmt.Sprintf("Failed to load CUDA module: %s", C.GoString(C.cuda_get_cu_error_string(result))), e.context.deviceIndex)
	}

	// Get function from module
	entryPointPtr := C.CString(entryPoint)
	defer C.free(unsafe.Pointer(entryPointPtr))

	var function C.CUfunction
	if result := C.cuda_module_get_function(&function, module, entryPointPtr); result != C.CUDA_SUCCESS {
		C.cuda_module_unload(module)
		return nil, types.NewGPUError("compute", 2306,
			fmt.Sprintf("Function '%s' not found in CUDA module: %s", entryPoint, C.GoString(C.cuda_get_cu_error_string(result))), e.context.deviceIndex)
	}

	kernelID := fmt.Sprintf("%s_%d", entryPoint, len(e.kernels))
	kernel := &CUDAKernel{
		id:         kernelID,
		name:       entryPoint,
		source:     source,
		context:    e.context,
		compiled:   true,
		module:     module,
		function:   function,
		attributes: make(map[string]interface{}),
	}

	kernel.attributes["name"] = entryPoint
	kernel.attributes["compiled"] = true
	kernel.attributes["source_length"] = len(source)
	kernel.attributes["compute_capability"] = fmt.Sprintf("%d.%d",
		e.context.device.Compute.Units, e.context.device.Compute.ClockSpeed/1000)

	e.kernels[kernelID] = kernel
	return kernel, nil
}

func (e *CUDAExecutor) CreateStream() (types.GPUStream, error) {
	if !e.context.IsValid() {
		return nil, types.NewGPUError("compute", 2307, "Context is invalid", e.context.deviceIndex)
	}

	e.mutex.Lock()
	defer e.mutex.Unlock()

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(e.context.cudaContext); result != C.CUDA_SUCCESS {
		return nil, types.NewGPUError("compute", 2308,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), e.context.deviceIndex)
	}

	// Create CUDA stream
	var cudaStream C.cudaStream_t
	if err := C.cuda_stream_create(&cudaStream); err != C.cudaSuccess {
		return nil, types.NewGPUError("compute", 2309,
			fmt.Sprintf("Failed to create CUDA stream: %s", C.GoString(C.cuda_get_error_string(err))), e.context.deviceIndex)
	}

	streamID := fmt.Sprintf("stream_%d", len(e.streams))
	stream := &CUDAStream{
		id:         streamID,
		context:    e.context,
		state:      types.StreamStateReady,
		cudaStream: cudaStream,
	}

	e.streams[streamID] = stream
	return stream, nil
}

func (e *CUDAExecutor) CreateEvent() (types.GPUEvent, error) {
	if !e.context.IsValid() {
		return nil, types.NewGPUError("compute", 2310, "Context is invalid", e.context.deviceIndex)
	}

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(e.context.cudaContext); result != C.CUDA_SUCCESS {
		return nil, types.NewGPUError("compute", 2311,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), e.context.deviceIndex)
	}

	// Create CUDA event
	var cudaEvent C.cudaEvent_t
	if err := C.cuda_event_create(&cudaEvent); err != C.cudaSuccess {
		return nil, types.NewGPUError("compute", 2312,
			fmt.Sprintf("Failed to create CUDA event: %s", C.GoString(C.cuda_get_error_string(err))), e.context.deviceIndex)
	}

	eventID := fmt.Sprintf("event_%d", time.Now().UnixNano())
	event := &CUDAEvent{
		id:        eventID,
		context:   e.context,
		state:     types.EventStateReady,
		created:   time.Now(),
		cudaEvent: cudaEvent,
	}

	return event, nil
}

func (e *CUDAExecutor) Synchronize() error {
	if !e.context.IsValid() {
		return types.ErrContextInvalid
	}

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(e.context.cudaContext); result != C.CUDA_SUCCESS {
		return types.NewGPUError("compute", 2313,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), e.context.deviceIndex)
	}

	// Synchronize device
	if err := C.cuda_device_synchronize(); err != C.cudaSuccess {
		return types.NewGPUError("compute", 2314,
			fmt.Sprintf("CUDA device synchronization failed: %s", C.GoString(C.cuda_get_error_string(err))), e.context.deviceIndex)
	}

	return nil
}

func (e *CUDAExecutor) GetStreams() []types.GPUStream {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	streams := make([]types.GPUStream, 0, len(e.streams))
	for _, stream := range e.streams {
		streams = append(streams, stream)
	}
	return streams
}

type CUDAKernel struct {
	id         string
	name       string
	source     string
	context    *CUDAContext
	compiled   bool
	module     C.CUmodule
	function   C.CUfunction
	attributes map[string]interface{}
}

func (k *CUDAKernel) GetName() string {
	return k.name
}

func (k *CUDAKernel) Launch(grid types.GridDimension, block types.GridDimension, args []interface{}, stream types.GPUStream) error {
	if !k.compiled {
		return types.NewGPUError("compute", 2401, "Kernel not compiled", k.context.deviceIndex)
	}

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(k.context.cudaContext); result != C.CUDA_SUCCESS {
		return types.NewGPUError("compute", 2402,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), k.context.deviceIndex)
	}

	// Prepare kernel arguments
	kernelParams := make([]unsafe.Pointer, len(args))
	for i, arg := range args {
		if cudaMem, ok := arg.(*CUDAMemory); ok {
			kernelParams[i] = unsafe.Pointer(&cudaMem.devicePtr)
		} else {
			// For scalar arguments, create a pointer to the value
			kernelParams[i] = unsafe.Pointer(&arg)
		}
	}

	var cudaStream C.CUstream
	if cudaStreamObj, ok := stream.(*CUDAStream); ok {
		cudaStream = C.CUstream(cudaStreamObj.cudaStream)
	}

	// Launch kernel
	var kernelParamsPtr *unsafe.Pointer
	if len(kernelParams) > 0 {
		kernelParamsPtr = &kernelParams[0]
	}

	if result := C.cuda_launch_kernel(k.function,
		C.uint(grid.X), C.uint(grid.Y), C.uint(grid.Z),
		C.uint(block.X), C.uint(block.Y), C.uint(block.Z),
		0, // shared memory
		cudaStream,
		kernelParamsPtr,
		nil); result != C.CUDA_SUCCESS {
		return types.NewGPUError("compute", 2403,
			fmt.Sprintf("CUDA kernel launch failed: %s", C.GoString(C.cuda_get_cu_error_string(result))), k.context.deviceIndex)
	}

	return nil
}

func (k *CUDAKernel) GetAttributes() map[string]interface{} {
	return k.attributes
}

func (k *CUDAKernel) Destroy() error {
	if k.module != nil {
		C.cuda_module_unload(k.module)
		k.module = nil
		k.function = nil
	}
	k.compiled = false
	return nil
}

type CUDAStream struct {
	id         string
	context    *CUDAContext
	state      types.StreamState
	cudaStream C.cudaStream_t
	mutex      sync.Mutex
}

func (s *CUDAStream) ID() string {
	return s.id
}

func (s *CUDAStream) Submit(kernel types.GPUKernel, grid types.GridDimension, block types.GridDimension, args []interface{}) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.state = types.StreamStateRunning
	err := kernel.Launch(grid, block, args, s)
	if err != nil {
		s.state = types.StreamStateError
		return err
	}
	s.state = types.StreamStateCompleted
	return nil
}

func (s *CUDAStream) Synchronize() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(s.context.cudaContext); result != C.CUDA_SUCCESS {
		return types.NewGPUError("compute", 2501,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), s.context.deviceIndex)
	}

	// Synchronize CUDA stream
	if err := C.cuda_stream_synchronize(s.cudaStream); err != C.cudaSuccess {
		s.state = types.StreamStateError
		return types.NewGPUError("compute", 2502,
			fmt.Sprintf("CUDA stream synchronization failed: %s", C.GoString(C.cuda_get_error_string(err))), s.context.deviceIndex)
	}

	s.state = types.StreamStateReady
	return nil
}

func (s *CUDAStream) Query() types.StreamState {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(s.context.cudaContext); result != C.CUDA_SUCCESS {
		return types.StreamStateError
	}

	// Query CUDA stream
	if err := C.cuda_stream_query(s.cudaStream); err == C.cudaSuccess {
		s.state = types.StreamStateReady
	} else if err == C.cudaErrorNotReady {
		s.state = types.StreamStateRunning
	} else {
		s.state = types.StreamStateError
	}

	return s.state
}

func (s *CUDAStream) Destroy() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(s.context.cudaContext); result != C.CUDA_SUCCESS {
		return types.NewGPUError("compute", 2503,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), s.context.deviceIndex)
	}

	if err := C.cuda_stream_destroy(s.cudaStream); err != C.cudaSuccess {
		return types.NewGPUError("compute", 2504,
			fmt.Sprintf("Failed to destroy CUDA stream: %s", C.GoString(C.cuda_get_error_string(err))), s.context.deviceIndex)
	}

	s.state = types.StreamStateDestroyed
	return nil
}

type CUDAEvent struct {
	id        string
	context   *CUDAContext
	state     types.EventState
	created   time.Time
	recorded  time.Time
	cudaEvent C.cudaEvent_t
	mutex     sync.Mutex
}

func (e *CUDAEvent) ID() string {
	return e.id
}

func (e *CUDAEvent) Record(stream types.GPUStream) error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(e.context.cudaContext); result != C.CUDA_SUCCESS {
		return types.NewGPUError("compute", 2601,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), e.context.deviceIndex)
	}

	var cudaStream C.cudaStream_t
	if cudaStreamObj, ok := stream.(*CUDAStream); ok {
		cudaStream = cudaStreamObj.cudaStream
	}

	// Record CUDA event
	if err := C.cuda_event_record(e.cudaEvent, cudaStream); err != C.cudaSuccess {
		return types.NewGPUError("compute", 2602,
			fmt.Sprintf("Failed to record CUDA event: %s", C.GoString(C.cuda_get_error_string(err))), e.context.deviceIndex)
	}

	e.recorded = time.Now()
	e.state = types.EventStateRecorded
	return nil
}

func (e *CUDAEvent) Wait() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(e.context.cudaContext); result != C.CUDA_SUCCESS {
		return types.NewGPUError("compute", 2603,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), e.context.deviceIndex)
	}

	// Wait for CUDA event
	if err := C.cuda_event_synchronize(e.cudaEvent); err != C.cudaSuccess {
		return types.NewGPUError("compute", 2604,
			fmt.Sprintf("Failed to wait for CUDA event: %s", C.GoString(C.cuda_get_error_string(err))), e.context.deviceIndex)
	}

	e.state = types.EventStateComplete
	return nil
}

func (e *CUDAEvent) Query() types.EventState {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(e.context.cudaContext); result != C.CUDA_SUCCESS {
		return types.EventStateError
	}

	// Query CUDA event
	if err := C.cuda_event_query(e.cudaEvent); err == C.cudaSuccess {
		e.state = types.EventStateComplete
	} else if err == C.cudaErrorNotReady {
		e.state = types.EventStateRecorded
	} else {
		e.state = types.EventStateError
	}

	return e.state
}

func (e *CUDAEvent) ElapsedTime(start types.GPUEvent) (time.Duration, error) {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	startEvent, ok := start.(*CUDAEvent)
	if !ok {
		return 0, types.NewGPUError("compute", 2605, "Invalid start event", -1)
	}

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(e.context.cudaContext); result != C.CUDA_SUCCESS {
		return 0, types.NewGPUError("compute", 2606,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), e.context.deviceIndex)
	}

	// Get elapsed time from CUDA
	var ms C.float
	if err := C.cuda_event_elapsed_time(&ms, startEvent.cudaEvent, e.cudaEvent); err != C.cudaSuccess {
		return 0, types.NewGPUError("compute", 2607,
			fmt.Sprintf("Failed to get CUDA event elapsed time: %s", C.GoString(C.cuda_get_error_string(err))), e.context.deviceIndex)
	}

	return time.Duration(float64(ms) * float64(time.Millisecond)), nil
}

func (e *CUDAEvent) Destroy() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	// Set current CUDA context
	if result := C.cuda_ctx_set_current(e.context.cudaContext); result != C.CUDA_SUCCESS {
		return types.NewGPUError("compute", 2608,
			fmt.Sprintf("Failed to set CUDA context: %s", C.GoString(C.cuda_get_cu_error_string(result))), e.context.deviceIndex)
	}

	if err := C.cuda_event_destroy(e.cudaEvent); err != C.cudaSuccess {
		return types.NewGPUError("compute", 2609,
			fmt.Sprintf("Failed to destroy CUDA event: %s", C.GoString(C.cuda_get_error_string(err))), e.context.deviceIndex)
	}

	e.state = types.EventStateDestroyed
	return nil
}
