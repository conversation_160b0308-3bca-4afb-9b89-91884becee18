//go:build (linux || windows) && cuda

package backends

/*
#cgo CFLAGS: -I/usr/local/cuda-12.9/targets/x86_64-linux/include -I/usr/local/cuda/include -I/usr/include
#cgo LDFLAGS: -L/usr/local/cuda-12.9/targets/x86_64-linux/lib -L/usr/local/cuda/lib64 -lcuda -lcudart
#include "cuda_shared.h"
*/
import "C"

import (
	"context"
	"fmt"
	"runtime"
	"strconv"
	"strings"

	"neuralmetergo/internal/gpu/types"
)



// CUDABackend implements the unified GPUBackend interface for NVIDIA CUDA
type CUDABackend struct {
	initialized bool
	devices     []types.GPUDevice
}

// NewCUDABackend creates a new CUDA backend
func NewCUDABackend() *CUDABackend {
	return &CUDABackend{
		initialized: false,
	}
}

// Backend identification
func (c *CUDABackend) Name() string     { return "CUDA" }
func (c *CUDABackend) Version() string  { return "12.0" }
func (c *CUDABackend) Platform() string { return runtime.GOOS }

// EnumerateDevices discovers and returns all CUDA-capable devices
func (c *CUDABackend) EnumerateDevices(ctx context.Context) ([]types.GPUDevice, error) {
	if c.initialized && c.devices != nil {
		return c.devices, nil
	}

	// Initialize CUDA runtime
	if err := c.initializeCUDA(); err != nil {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 2001,
			fmt.Sprintf("Failed to initialize CUDA: %v", err), -1)
	}

	// Get device count
	deviceCount, err := c.getDeviceCount()
	if err != nil {
		return nil, types.NewGPUError(types.ErrorTypeDetection, 2002,
			fmt.Sprintf("Failed to get CUDA device count: %v", err), -1)
	}

	if deviceCount == 0 {
		return []types.GPUDevice{}, nil
	}

	// Enumerate each device
	devices := make([]types.GPUDevice, 0, deviceCount)
	for i := 0; i < deviceCount; i++ {
		device, err := c.createDeviceInfo(i)
		if err != nil {
			// Log error but continue with other devices
			fmt.Printf("Warning: Failed to get info for CUDA device %d: %v\n", i, err)
			continue
		}
		devices = append(devices, device)
	}

	c.devices = devices
	c.initialized = true
	return devices, nil
}

// GetDevice returns information about a specific CUDA device
func (c *CUDABackend) GetDevice(deviceID string) (*types.GPUDevice, error) {
	devices, err := c.EnumerateDevices(context.Background())
	if err != nil {
		return nil, err
	}

	for i := range devices {
		if devices[i].ID == deviceID {
			return &devices[i], nil
		}
	}

	return nil, types.NewGPUError(types.ErrorTypeDetection, 2003,
		fmt.Sprintf("CUDA device %s not found", deviceID), -1)
}

// CreateContext creates a CUDA context for the specified device
func (c *CUDABackend) CreateContext(device *types.GPUDevice) (types.GPUContext, error) {
	if device == nil || device.Backend != "CUDA" {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 2004,
			"Invalid device for CUDA context creation", -1)
	}

	// Extract device index from ID (format: "cuda-0", "cuda-1", etc.)
	parts := strings.Split(device.ID, "-")
	if len(parts) != 2 {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 2005,
			fmt.Sprintf("Invalid CUDA device ID format: %s", device.ID), -1)
	}

	deviceIndex, err := strconv.Atoi(parts[1])
	if err != nil {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 2006,
			fmt.Sprintf("Invalid device index in ID %s: %v", device.ID, err), -1)
	}

	return NewCUDAContext(device, deviceIndex)
}

// GetCapabilities returns the capabilities of a CUDA device
func (c *CUDABackend) GetCapabilities(device *types.GPUDevice) (*types.GPUCapability, error) {
	if device == nil || device.Backend != "CUDA" {
		return nil, types.NewGPUError(types.ErrorTypeDetection, 2007,
			"Invalid device for capability query", -1)
	}

	// Extract CUDA-specific information from device capabilities
	if caps, ok := device.Capabilities["cuda_capability"]; ok {
		if cudaCap, ok := caps.(CUDADeviceInfo); ok {
			return c.mapCUDACapabilities(cudaCap), nil
		}
	}

	// Fallback: create basic capabilities
	return c.createBasicCapabilities(device), nil
}

// SupportsFeature checks if a CUDA device supports a specific feature
func (c *CUDABackend) SupportsFeature(device *types.GPUDevice, feature types.GPUFeature) bool {
	if device == nil || device.Backend != "CUDA" {
		return false
	}

	caps, err := c.GetCapabilities(device)
	if err != nil {
		return false
	}

	if supported, exists := caps.Features[feature]; exists {
		return supported
	}

	return false
}

// CreateMemoryManager creates a CUDA memory manager
func (c *CUDABackend) CreateMemoryManager(ctx types.GPUContext) (types.GPUMemoryManager, error) {
	if ctx == nil || !ctx.IsValid() {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 2008,
			"Invalid context for memory manager creation", -1)
	}

	cudaCtx, ok := ctx.(*CUDAContext)
	if !ok {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 2009,
			"Context is not a CUDA context", -1)
	}

	return NewCUDAMemoryManager(cudaCtx)
}

// CreateExecutor creates a CUDA executor
func (c *CUDABackend) CreateExecutor(ctx types.GPUContext) (types.GPUExecutor, error) {
	if ctx == nil || !ctx.IsValid() {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 2010,
			"Invalid context for executor creation", -1)
	}

	cudaCtx, ok := ctx.(*CUDAContext)
	if !ok {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 2011,
			"Context is not a CUDA context", -1)
	}

	return NewCUDAExecutor(cudaCtx)
}

// Private helper methods

func (c *CUDABackend) initializeCUDA() error {
	// Initialize CUDA driver API
	if result := C.cuda_init(); result != C.CUDA_SUCCESS {
		return fmt.Errorf("CUDA initialization failed: %s", C.GoString(C.cuda_get_cu_error_string(result)))
	}
	return nil
}

func (c *CUDABackend) getDeviceCount() (int, error) {
	var count C.int
	if result := C.cuda_device_get_count(&count); result != C.CUDA_SUCCESS {
		return 0, fmt.Errorf("failed to get CUDA device count: %s", C.GoString(C.cuda_get_cu_error_string(result)))
	}
	return int(count), nil
}

func (c *CUDABackend) createDeviceInfo(deviceIndex int) (types.GPUDevice, error) {
	var deviceProp C.struct_cudaDeviceProp
	if err := C.cuda_get_device_properties(&deviceProp, C.int(deviceIndex)); err != C.cudaSuccess {
		return types.GPUDevice{}, fmt.Errorf("failed to get CUDA device properties for device %d: %s", deviceIndex, C.GoString(C.cuda_get_error_string(err)))
	}

	deviceName := C.GoString(&deviceProp.name[0])

	// Correctly populate the nested structs
	return types.GPUDevice{
		ID:         fmt.Sprintf("cuda-%d", deviceIndex),
		Name:       deviceName,
		Vendor:     "NVIDIA",
		Type:       types.DeviceTypeDiscrete,
		Backend:    "CUDA",
		Index:      deviceIndex,
		Status:     types.DeviceStatusAvailable,
		ClockSpeed: int(deviceProp.clockRate / 1000), // Convert MHz to kHz
		Memory: types.MemoryInfo{
			Total: uint64(deviceProp.totalGlobalMem),
			Free:  uint64(deviceProp.totalGlobalMem), // Placeholder, free memory needs a separate call
		},
		Compute: types.ComputeInfo{
			Capability: types.ComputeCapability{
				Major: int(deviceProp.major),
				Minor: int(deviceProp.minor),
			},
		},
		ComputeUnits: int(deviceProp.multiProcessorCount),
	}, nil
}

func (c *CUDABackend) getCoresPerMP(major, minor int) int {
	switch major {
	case 1:
		return 8 // Tesla
	case 2:
		if minor == 0 {
			return 32 // Fermi GF100
		}
		return 48 // Fermi GF10x
	case 3:
		return 192 // Kepler
	case 5:
		return 128 // Maxwell
	case 6:
		if minor == 0 {
			return 64 // Pascal GP100
		}
		return 128 // Pascal GP10x
	case 7:
		if minor == 0 {
			return 64 // Volta GV100
		}
		return 64 // Turing TU10x
	case 8:
		if minor == 0 {
			return 64 // Ampere GA100
		}
		return 128 // Ampere GA10x
	case 9:
		return 128 // Hopper/Ada Lovelace
	default:
		return 64 // Default assumption
	}
}

func (c *CUDABackend) mapCUDACapabilities(deviceInfo CUDADeviceInfo) *types.GPUCapability {
	capabilities := &types.GPUCapability{
		MaxTextureSize:     [3]uint32{65536, 65536, 4096}, // Common CUDA limits
		MaxBufferSize:      deviceInfo.GlobalMemory,
		MaxWorkGroupSize:   uint32(deviceInfo.MaxThreadsPerBlock),
		MaxWorkGroupDim:    [3]uint32{uint32(deviceInfo.MaxBlockDim[0]), uint32(deviceInfo.MaxBlockDim[1]), uint32(deviceInfo.MaxBlockDim[2])},
		MaxComputeUnits:    uint32(deviceInfo.MultiProcessorCount),
		MaxMemoryBandwidth: deviceInfo.MemoryBandwidth * 1000 * 1000 * 1000, // Convert GB/s to B/s
		Features: map[types.GPUFeature]bool{
			types.FeatureCompute:          true,
			types.FeatureGraphics:         false, // CUDA is compute-only
			types.FeatureUnifiedMemory:    deviceInfo.ComputeCapability.Major >= 6,
			types.FeatureAtomicOperations: true,
			types.FeatureSharedMemory:     true,
			types.FeatureHalfPrecision:    deviceInfo.ComputeCapability.Major >= 5,
			types.FeatureDoublePrecision:  deviceInfo.ComputeCapability.Major >= 1,
			types.FeatureTensorOperations: deviceInfo.ComputeCapability.Major >= 7,
		},
		Extensions: []string{
			"CUDA",
			fmt.Sprintf("compute_%d%d", deviceInfo.ComputeCapability.Major, deviceInfo.ComputeCapability.Minor),
		},
	}

	return capabilities
}

func (c *CUDABackend) createBasicCapabilities(device *types.GPUDevice) *types.GPUCapability {
	return &types.GPUCapability{
		MaxTextureSize:     [3]uint32{16384, 16384, 2048},
		MaxBufferSize:      device.Memory.Total,
		MaxWorkGroupSize:   1024,
		MaxWorkGroupDim:    [3]uint32{1024, 1024, 64},
		MaxComputeUnits:    uint32(device.Compute.Units / 32), // Estimate SMs
		MaxMemoryBandwidth: device.Memory.Bandwidth,
		Features: map[types.GPUFeature]bool{
			types.FeatureCompute:          true,
			types.FeatureGraphics:         false,
			types.FeatureUnifiedMemory:    false,
			types.FeatureAtomicOperations: true,
			types.FeatureSharedMemory:     true,
			types.FeatureHalfPrecision:    true,
			types.FeatureDoublePrecision:  true,
			types.FeatureTensorOperations: false,
		},
		Extensions: []string{"CUDA"},
	}
}

func (c *CUDABackend) estimateGFLOPS(deviceInfo CUDADeviceInfo) float64 {
	// Estimate GFLOPS based on cores, clock speed, and architecture
	totalCores := float64(deviceInfo.MultiProcessorCount * deviceInfo.CoresPerMP)
	clockGHz := float64(deviceInfo.ClockRate) / 1000000.0 // Convert kHz to GHz

	// Base calculation: cores * clock * operations per clock
	baseGFLOPS := totalCores * clockGHz * 2.0 // 2 ops per clock (FMA)

	// Architecture-specific multipliers
	switch deviceInfo.ComputeCapability.Major {
	case 7, 8, 9: // Volta, Ampere, Hopper
		return baseGFLOPS * 1.5 // Higher efficiency
	case 6: // Pascal
		return baseGFLOPS * 1.2
	case 5: // Maxwell
		return baseGFLOPS * 1.1
	default:
		return baseGFLOPS
	}
}

type CUDADeviceInfo struct {
	Name                string
	ComputeCapability   types.ComputeCapability
	Architecture        string
	MultiProcessorCount int
	CoresPerMP          int
	WarpSize            int
	GlobalMemory        uint64
	SharedMemPerBlock   int
	RegsPerBlock        int
	TotalConstMem       int
	MaxThreadsPerBlock  int
	MaxBlockDim         [3]int
	MaxGridDim          [3]int
	ClockRate           int    // kHz
	MemoryClockRate     int    // kHz
	MemoryBusWidth      int    // bits
	MemoryBandwidth     uint64 // GB/s
	L2CacheSize         int    // bytes
	PCIBusID            string
	PCIDeviceID         int
}

func (c *CUDABackend) getArchitectureName(major, minor int) string {
	switch major {
	case 1:
		return "Tesla"
	case 2:
		return "Fermi"
	case 3:
		return "Kepler"
	case 5:
		return "Maxwell"
	case 6:
		return "Pascal"
	case 7:
		if minor == 0 {
			return "Volta"
		}
		return "Turing"
	case 8:
		return "Ampere"
	case 9:
		return "Hopper"
	default:
		return fmt.Sprintf("Unknown_%d.%d", major, minor)
	}
}
