#ifndef METAL_WRAPPER_H
#define METAL_WRAPPER_H

#ifdef __cplusplus
extern "C" {
#endif

// Metal device functions
void* metal_create_system_default_device(void);
void* metal_copy_all_devices(void);
void metal_release_device(void* device);
int metal_device_supports_feature_set(void* device, unsigned long featureSet);
char* metal_device_name(void* device);
unsigned long metal_device_max_threads_per_threadgroup(void* device);
unsigned long metal_device_recommended_max_working_set_size(void* device);

// Metal buffer functions  
void* metal_device_new_buffer_with_length(void* device, unsigned long length, unsigned long options);
void* metal_device_new_buffer_with_bytes(void* device, const void* pointer, unsigned long length, unsigned long options);
void metal_release_buffer(void* buffer);
void* metal_buffer_contents(void* buffer);
unsigned long metal_buffer_length(void* buffer);

// Metal library and function functions
void* metal_device_new_library_with_source(void* device, const char* source, void* options, void** error);
void* metal_device_new_default_library(void* device);
void metal_release_library(void* library);
void* metal_library_new_function_with_name(void* library, const char* functionName);
void metal_release_function(void* function);

// Metal compute pipeline functions
void* metal_device_new_compute_pipeline_state_with_function(void* device, void* function, void** error);
void metal_release_compute_pipeline_state(void* computePipelineState);

// Metal command queue functions
void* metal_device_new_command_queue(void* device);
void metal_release_command_queue(void* commandQueue);
void* metal_command_queue_command_buffer(void* commandQueue);

// Metal command buffer functions
void metal_release_command_buffer(void* commandBuffer);
void metal_command_buffer_commit(void* commandBuffer);
void metal_command_buffer_wait_until_completed(void* commandBuffer);
unsigned long metal_command_buffer_status(void* commandBuffer);
void* metal_command_buffer_compute_command_encoder(void* commandBuffer);

// Metal compute command encoder functions
void metal_release_compute_command_encoder(void* computeCommandEncoder);
void metal_compute_command_encoder_set_compute_pipeline_state(void* computeCommandEncoder, void* computePipelineState);
void metal_compute_command_encoder_set_buffer(void* computeCommandEncoder, void* buffer, unsigned long offset, unsigned long index);
void metal_compute_command_encoder_dispatch_threadgroups(void* computeCommandEncoder, unsigned long threadgroupsPerGrid[3], unsigned long threadsPerThreadgroup[3]);
void metal_compute_command_encoder_end_encoding(void* computeCommandEncoder);

#ifdef __cplusplus
}
#endif

#endif // METAL_WRAPPER_H 