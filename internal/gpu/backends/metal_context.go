//go:build darwin && metal

package backends

/*
#cgo CFLAGS: -x objective-c
#cgo LDFLAGS: -framework Metal -framework Foundation
#include "metal_wrapper.h"
*/
import "C"

import (
	"fmt"
	"neuralmetergo/internal/gpu/types"
	"sync"
	"unsafe"
)

// MetalContext implements the unified GPUContext interface for Metal
type MetalContext struct {
	device       *types.GPUDevice
	deviceIndex  int
	valid        bool
	nativeHandle uintptr
	metalDevice  unsafe.Pointer
	commandQueue unsafe.Pointer
}

// NewMetalContext creates a new Metal context
func NewMetalContext(device *types.GPUDevice, deviceIndex int) (*MetalContext, error) {
	ctx := &MetalContext{
		device:      device,
		deviceIndex: deviceIndex,
		valid:       false,
	}

	// Initialize the Metal context
	if err := ctx.initialize(); err != nil {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 2100,
			fmt.Sprintf("Failed to initialize Metal context for device %s: %v", device.ID, err), -1)
	}

	return ctx, nil
}

// GetDevice returns the GPU device associated with this context
func (ctx *MetalContext) GetDevice() *types.GPUDevice {
	return ctx.device
}

// IsValid returns whether the context is valid
func (ctx *MetalContext) IsValid() bool {
	return ctx.valid && ctx.metalDevice != nil && ctx.commandQueue != nil
}

// Synchronize waits for all operations in the context to complete
func (ctx *MetalContext) Synchronize() error {
	if !ctx.valid {
		return types.NewGPUError(types.ErrorTypeInitialization, 3101, "Context is invalid", ctx.deviceIndex)
	}

	// Create a command buffer and commit it to ensure synchronization
	commandBuffer := C.metal_command_queue_command_buffer(ctx.commandQueue)
	if commandBuffer == nil {
		return types.NewGPUError(types.ErrorTypeExecution, 3102,
			"Failed to create command buffer for synchronization", ctx.deviceIndex)
	}
	defer C.metal_release_command_buffer(commandBuffer)

	C.metal_command_buffer_commit(commandBuffer)
	C.metal_command_buffer_wait_until_completed(commandBuffer)

	status := C.metal_command_buffer_status(commandBuffer)
	if status != 4 { // MTLCommandBufferStatusCompleted
		return types.NewGPUError(types.ErrorTypeExecution, 3103,
			fmt.Sprintf("Command buffer failed with status %d", status), ctx.deviceIndex)
	}

	return nil
}

// Destroy releases the Metal context
func (ctx *MetalContext) Destroy() error {
	if !ctx.valid {
		return types.NewGPUError(types.ErrorTypeInitialization, 3104, "Context is invalid", ctx.deviceIndex)
	}

	if err := ctx.cleanup(); err != nil {
		return types.NewGPUError(types.ErrorTypeInitialization, 3105,
			"Failed to cleanup Metal context", ctx.deviceIndex)
	}

	ctx.valid = false
	ctx.nativeHandle = 0
	return nil
}

// GetDeviceID returns the device ID
func (ctx *MetalContext) GetDeviceID() string {
	return ctx.device.ID
}

// GetBackend returns the backend name
func (ctx *MetalContext) GetBackend() string {
	return "Metal"
}

// GetNativeHandle returns the native Metal device handle
func (ctx *MetalContext) GetNativeHandle() uintptr {
	return ctx.nativeHandle
}

// GetDeviceIndex returns the Metal device index
func (ctx *MetalContext) GetDeviceIndex() int {
	return ctx.deviceIndex
}

// Private methods

func (ctx *MetalContext) initialize() error {
	// Create Metal device
	if ctx.deviceIndex == 0 {
		// Use system default device
		ctx.metalDevice = C.metal_create_system_default_device()
	} else {
		// Get device by index from all devices
		allDevices := C.metal_copy_all_devices()
		if allDevices == nil {
			return fmt.Errorf("failed to get Metal devices")
		}
		// For simplicity, we'll use the default device for now
		// In a full implementation, we'd enumerate and select by index
		ctx.metalDevice = C.metal_create_system_default_device()
	}

	if ctx.metalDevice == nil {
		return fmt.Errorf("failed to create Metal device")
	}

	// Create command queue
	ctx.commandQueue = C.metal_new_command_queue(ctx.metalDevice)
	if ctx.commandQueue == nil {
		C.metal_release_device(ctx.metalDevice)
		return fmt.Errorf("failed to create Metal command queue")
	}

	ctx.nativeHandle = uintptr(ctx.metalDevice)
	ctx.valid = true
	return nil
}

func (ctx *MetalContext) cleanup() error {
	if ctx.commandQueue != nil {
		C.metal_release_command_queue(ctx.commandQueue)
		ctx.commandQueue = nil
	}

	if ctx.metalDevice != nil {
		C.metal_release_device(ctx.metalDevice)
		ctx.metalDevice = nil
	}

	return nil
}

// Factory functions for memory manager and executor

// NewMetalMemoryManager creates a new Metal memory manager
func NewMetalMemoryManager(ctx *MetalContext) (types.GPUMemoryManager, error) {
	if ctx == nil || !ctx.IsValid() {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 3200,
			"Invalid context for memory manager creation", -1)
	}

	return &MetalMemoryManager{
		context:     ctx,
		allocations: make(map[uintptr]*MetalBuffer),
	}, nil
}

// NewMetalExecutor creates a new Metal executor
func NewMetalExecutor(ctx *MetalContext) (types.GPUExecutor, error) {
	if ctx == nil || !ctx.IsValid() {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 3300,
			"Invalid context for executor creation", -1)
	}

	return &MetalExecutor{
		context: ctx,
		kernels: make(map[string]*MetalKernel),
	}, nil
}

// MetalMemoryManager implements the GPUMemoryManager interface for Metal
type MetalMemoryManager struct {
	context     *MetalContext
	allocations map[uintptr]*MetalBuffer
	mutex       sync.RWMutex
}

// AllocateBuffer allocates a Metal buffer
func (m *MetalMemoryManager) AllocateBuffer(size uint64) (types.GPUBuffer, error) {
	return m.AllocateBufferWithFlags(size, types.MemoryFlagDefault)
}

// AllocateBufferWithFlags allocates a Metal buffer with specific flags
func (m *MetalMemoryManager) AllocateBufferWithFlags(size uint64, flags types.MemoryFlags) (types.GPUBuffer, error) {
	if !m.context.IsValid() {
		return nil, types.NewGPUError(types.ErrorTypeMemory, 3201,
			"Invalid context for buffer allocation", m.context.deviceIndex)
	}

	// Convert flags to Metal resource options
	var options C.int = 0 // MTLResourceStorageModeShared
	if flags&types.MemoryFlagHostVisible != 0 {
		options = 0 // MTLResourceStorageModeShared
	}

	// Allocate Metal buffer
	metalBuffer := C.metal_new_buffer(m.context.metalDevice, C.uint64_t(size), options)
	if metalBuffer == nil {
		return nil, types.NewGPUError(types.ErrorTypeMemory, 3202,
			fmt.Sprintf("Failed to allocate Metal buffer of size %d", size), m.context.deviceIndex)
	}

	buffer := &MetalBuffer{
		size:        size,
		flags:       flags,
		metalBuffer: metalBuffer,
		manager:     m,
	}

	m.mutex.Lock()
	m.allocations[buffer.GetPointer()] = buffer
	m.mutex.Unlock()

	return buffer, nil
}

// FreeBuffer frees a Metal buffer
func (m *MetalMemoryManager) FreeBuffer(buffer types.GPUBuffer) error {
	metalBuffer, ok := buffer.(*MetalBuffer)
	if !ok {
		return types.NewGPUError(types.ErrorTypeMemory, 3203,
			"Buffer is not a Metal buffer", m.context.deviceIndex)
	}

	m.mutex.Lock()
	delete(m.allocations, metalBuffer.GetPointer())
	m.mutex.Unlock()

	if metalBuffer.metalBuffer != nil {
		C.metal_release_buffer(metalBuffer.metalBuffer)
		metalBuffer.metalBuffer = nil
	}

	return nil
}

// CopyToDevice copies data to a Metal buffer
func (m *MetalMemoryManager) CopyToDevice(data []byte, buffer types.GPUBuffer) error {
	metalBuffer, ok := buffer.(*MetalBuffer)
	if !ok {
		return types.NewGPUError(types.ErrorTypeMemory, 3204,
			"Buffer is not a Metal buffer", m.context.deviceIndex)
	}

	if uint64(len(data)) > metalBuffer.size {
		return types.NewGPUError(types.ErrorTypeMemory, 3205,
			"Data size exceeds buffer size", m.context.deviceIndex)
	}

	// Get buffer contents and copy data
	contents := C.metal_buffer_contents(metalBuffer.metalBuffer)
	if contents == nil {
		return types.NewGPUError(types.ErrorTypeMemory, 3206,
			"Failed to get buffer contents", m.context.deviceIndex)
	}

	// Copy data to Metal buffer
	C.memcpy(contents, unsafe.Pointer(&data[0]), C.size_t(len(data)))
	return nil
}

// CopyFromDevice copies data from a Metal buffer
func (m *MetalMemoryManager) CopyFromDevice(buffer types.GPUBuffer, size uint64) ([]byte, error) {
	metalBuffer, ok := buffer.(*MetalBuffer)
	if !ok {
		return nil, types.NewGPUError(types.ErrorTypeMemory, 3207,
			"Buffer is not a Metal buffer", m.context.deviceIndex)
	}

	if size > metalBuffer.size {
		size = metalBuffer.size
	}

	// Get buffer contents and copy data
	contents := C.metal_buffer_contents(metalBuffer.metalBuffer)
	if contents == nil {
		return nil, types.NewGPUError(types.ErrorTypeMemory, 3208,
			"Failed to get buffer contents", m.context.deviceIndex)
	}

	// Copy data from Metal buffer
	data := make([]byte, size)
	C.memcpy(unsafe.Pointer(&data[0]), contents, C.size_t(size))
	return data, nil
}

// GetMemoryInfo returns memory usage information
func (m *MetalMemoryManager) GetMemoryInfo() (*types.MemoryInfo, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var totalUsed uint64
	for _, buffer := range m.allocations {
		totalUsed += buffer.size
	}

	return &types.MemoryInfo{
		TotalMemory:     m.context.device.MemoryTotal,
		FreeMemory:      m.context.device.MemoryFree,
		UsedMemory:      totalUsed,
		AllocationCount: len(m.allocations),
	}, nil
}

// GetTotalMemory returns total GPU memory
func (m *MetalMemoryManager) GetTotalMemory() uint64 {
	return m.context.device.MemoryTotal
}

// GetFreeMemory returns free GPU memory
func (m *MetalMemoryManager) GetFreeMemory() uint64 {
	return m.context.device.MemoryFree
}

// MetalBuffer implements the GPUBuffer interface for Metal
type MetalBuffer struct {
	size        uint64
	flags       types.MemoryFlags
	metalBuffer unsafe.Pointer
	manager     *MetalMemoryManager
}

// GetSize returns the buffer size
func (b *MetalBuffer) GetSize() uint64 {
	return b.size
}

// GetPointer returns the buffer pointer
func (b *MetalBuffer) GetPointer() uintptr {
	return uintptr(b.metalBuffer)
}

// IsValid returns whether the buffer is valid
func (b *MetalBuffer) IsValid() bool {
	return b.metalBuffer != nil
}

// GetFlags returns the buffer flags
func (b *MetalBuffer) GetFlags() types.MemoryFlags {
	return b.flags
}

// MetalExecutor implements the GPUExecutor interface for Metal
type MetalExecutor struct {
	context *MetalContext
	kernels map[string]*MetalKernel
	mutex   sync.RWMutex
}

// LoadKernel loads a Metal kernel from source
func (e *MetalExecutor) LoadKernel(source string, entryPoint string) (types.GPUKernel, error) {
	if !e.context.IsValid() {
		return nil, types.NewGPUError(types.ErrorTypeExecution, 3301,
			"Invalid context for kernel loading", e.context.deviceIndex)
	}

	// Create Metal library from source
	cSource := C.CString(source)
	defer C.free(unsafe.Pointer(cSource))

	library := C.metal_new_library_with_source(e.context.metalDevice, cSource)
	if library == nil {
		return nil, types.NewGPUError(types.ErrorTypeExecution, 3302,
			"Failed to create Metal library from source", e.context.deviceIndex)
	}
	defer C.metal_release_library(library)

	// Get function from library
	cEntryPoint := C.CString(entryPoint)
	defer C.free(unsafe.Pointer(cEntryPoint))

	function := C.metal_library_new_function_with_name(library, cEntryPoint)
	if function == nil {
		return nil, types.NewGPUError(types.ErrorTypeExecution, 3303,
			fmt.Sprintf("Failed to find function '%s' in Metal library", entryPoint), e.context.deviceIndex)
	}
	defer C.metal_release_function(function)

	// Create compute pipeline state
	pipelineState := C.metal_new_compute_pipeline_state_with_function(e.context.metalDevice, function)
	if pipelineState == nil {
		return nil, types.NewGPUError(types.ErrorTypeExecution, 3304,
			"Failed to create Metal compute pipeline state", e.context.deviceIndex)
	}

	kernel := &MetalKernel{
		name:          entryPoint,
		source:        source,
		context:       e.context,
		pipelineState: pipelineState,
	}

	e.mutex.Lock()
	e.kernels[entryPoint] = kernel
	e.mutex.Unlock()

	return kernel, nil
}

// LoadKernelFromFile loads a Metal kernel from a file
func (e *MetalExecutor) LoadKernelFromFile(filePath string, entryPoint string) (types.GPUKernel, error) {
	// For simplicity, return an error - file loading would require reading the file
	return nil, types.NewGPUError(types.ErrorTypeNotSupported, 3305,
		"Loading kernels from file not implemented", e.context.deviceIndex)
}

// ExecuteKernel executes a Metal kernel
func (e *MetalExecutor) ExecuteKernel(kernel types.GPUKernel, globalSize, localSize [3]int, args ...interface{}) error {
	metalKernel, ok := kernel.(*MetalKernel)
	if !ok {
		return types.NewGPUError(types.ErrorTypeExecution, 3306,
			"Kernel is not a Metal kernel", e.context.deviceIndex)
	}

	// Create command buffer
	commandBuffer := C.metal_command_queue_command_buffer(e.context.commandQueue)
	if commandBuffer == nil {
		return types.NewGPUError(types.ErrorTypeExecution, 3307,
			"Failed to create command buffer", e.context.deviceIndex)
	}
	defer C.metal_release_command_buffer(commandBuffer)

	// Create compute command encoder
	encoder := C.metal_command_buffer_compute_command_encoder(commandBuffer)
	if encoder == nil {
		return types.NewGPUError(types.ErrorTypeExecution, 3308,
			"Failed to create compute command encoder", e.context.deviceIndex)
	}
	defer C.metal_release_compute_encoder(encoder)

	// Set compute pipeline state
	C.metal_compute_encoder_set_compute_pipeline_state(encoder, metalKernel.pipelineState)

	// Set kernel arguments (simplified - only supports buffers for now)
	for i, arg := range args {
		if buffer, ok := arg.(types.GPUBuffer); ok {
			metalBuffer := buffer.(*MetalBuffer)
			C.metal_compute_encoder_set_buffer(encoder, metalBuffer.metalBuffer, 0, C.int(i))
		}
	}

	// Dispatch threads
	C.metal_compute_encoder_dispatch_threads(encoder,
		C.uint64_t(globalSize[0]), C.uint64_t(globalSize[1]), C.uint64_t(globalSize[2]),
		C.uint64_t(localSize[0]), C.uint64_t(localSize[1]), C.uint64_t(localSize[2]))

	// End encoding
	C.metal_compute_encoder_end_encoding(encoder)

	// Commit and wait
	C.metal_command_buffer_commit(commandBuffer)
	C.metal_command_buffer_wait_until_completed(commandBuffer)

	return nil
}

// ExecuteKernelAsync executes a Metal kernel asynchronously
func (e *MetalExecutor) ExecuteKernelAsync(kernel types.GPUKernel, globalSize, localSize [3]int, args ...interface{}) (types.GPUEvent, error) {
	// For simplicity, execute synchronously and return a completed event
	err := e.ExecuteKernel(kernel, globalSize, localSize, args...)
	if err != nil {
		return nil, err
	}

	return &MetalEvent{
		name:      "async_execution",
		context:   e.context,
		completed: true,
	}, nil
}

// Synchronize waits for all operations to complete
func (e *MetalExecutor) Synchronize() error {
	return e.context.Synchronize()
}

// CreateEvent creates a new Metal event
func (e *MetalExecutor) CreateEvent() (types.GPUEvent, error) {
	return &MetalEvent{
		name:      "metal_event",
		context:   e.context,
		completed: false,
	}, nil
}

// MetalKernel implements the GPUKernel interface for Metal
type MetalKernel struct {
	name          string
	source        string
	context       *MetalContext
	pipelineState unsafe.Pointer
}

// GetName returns the kernel name
func (k *MetalKernel) GetName() string {
	return k.name
}

// IsValid returns whether the kernel is valid
func (k *MetalKernel) IsValid() bool {
	return k.pipelineState != nil
}

// Release releases the kernel resources
func (k *MetalKernel) Release() error {
	if k.pipelineState != nil {
		C.metal_release_pipeline_state(k.pipelineState)
		k.pipelineState = nil
	}
	return nil
}

// SetArg sets a kernel argument
func (k *MetalKernel) SetArg(index int, value interface{}) error {
	// Metal handles arguments differently - they're set during dispatch
	// This is a placeholder implementation
	return nil
}

// MetalEvent implements the GPUEvent interface for Metal
type MetalEvent struct {
	name      string
	context   *MetalContext
	completed bool
}

// Wait waits for the event to complete
func (e *MetalEvent) Wait() error {
	if !e.completed {
		return e.context.Synchronize()
	}
	return nil
}

// IsComplete returns whether the event is complete
func (e *MetalEvent) IsComplete() bool {
	return e.completed
}

// GetElapsedTime returns the elapsed time
func (e *MetalEvent) GetElapsedTime() (float64, error) {
	// Simplified implementation
	return 0.0, nil
}

// Release releases the event
func (e *MetalEvent) Release() error {
	// Nothing to release for simplified implementation
	return nil
}
