//go:build darwin && metal

#import <Metal/Metal.h>
#import <Foundation/Foundation.h>

// Global error storage
static NSString *lastError = nil;

// Helper function to set error
void set_metal_error(NSString *error) {
    if (lastError) {
        [lastError release];
    }
    lastError = [error retain];
}

// Metal device functions
void* metal_create_system_default_device() {
    @autoreleasepool {
        id<MTLDevice> device = MTLCreateSystemDefaultDevice();
        if (device) {
            return (__bridge_retained void*)device;
        }
        set_metal_error(@"Failed to create system default Metal device");
        return NULL;
    }
}

void* metal_copy_all_devices() {
    @autoreleasepool {
        NSArray<id<MTLDevice>> *devices = MTLCopyAllDevices();
        if (devices && [devices count] > 0) {
            return (__bridge_retained void*)devices;
        }
        set_metal_error(@"No Metal devices found");
        return NULL;
    }
}

void metal_release_device(void* device) {
    if (device) {
        CFRelease(device);
    }
}

const char* metal_device_name(void* device) {
    @autoreleasepool {
        if (!device) return NULL;
        
        id<MTLDevice> metalDevice = (__bridge id<MTLDevice>)device;
        NSString *name = metalDevice.name;
        return [name UTF8String];
    }
}

uint64_t metal_device_max_buffer_length(void* device) {
    @autoreleasepool {
        if (!device) return 0;
        
        id<MTLDevice> metalDevice = (__bridge id<MTLDevice>)device;
        return metalDevice.maxBufferLength;
    }
}

bool metal_device_supports_feature_set(void* device, int featureSet) {
    @autoreleasepool {
        if (!device) return false;
        
        id<MTLDevice> metalDevice = (__bridge id<MTLDevice>)device;
        return [metalDevice supportsFeatureSet:(MTLFeatureSet)featureSet];
    }
}

// Metal command queue functions
void* metal_new_command_queue(void* device) {
    @autoreleasepool {
        if (!device) {
            set_metal_error(@"Device is NULL");
            return NULL;
        }
        
        id<MTLDevice> metalDevice = (__bridge id<MTLDevice>)device;
        id<MTLCommandQueue> queue = [metalDevice newCommandQueue];
        if (queue) {
            return (__bridge_retained void*)queue;
        }
        set_metal_error(@"Failed to create command queue");
        return NULL;
    }
}

void metal_release_command_queue(void* queue) {
    if (queue) {
        CFRelease(queue);
    }
}

// Metal buffer functions
void* metal_new_buffer(void* device, uint64_t length, int options) {
    @autoreleasepool {
        if (!device) {
            set_metal_error(@"Device is NULL");
            return NULL;
        }
        
        id<MTLDevice> metalDevice = (__bridge id<MTLDevice>)device;
        id<MTLBuffer> buffer = [metalDevice newBufferWithLength:length 
                                                        options:(MTLResourceOptions)options];
        if (buffer) {
            return (__bridge_retained void*)buffer;
        }
        set_metal_error([NSString stringWithFormat:@"Failed to allocate buffer of size %llu", length]);
        return NULL;
    }
}

void* metal_new_buffer_with_bytes(void* device, void* bytes, uint64_t length, int options) {
    @autoreleasepool {
        if (!device || !bytes) {
            set_metal_error(@"Device or bytes is NULL");
            return NULL;
        }
        
        id<MTLDevice> metalDevice = (__bridge id<MTLDevice>)device;
        id<MTLBuffer> buffer = [metalDevice newBufferWithBytes:bytes 
                                                        length:length 
                                                       options:(MTLResourceOptions)options];
        if (buffer) {
            return (__bridge_retained void*)buffer;
        }
        set_metal_error([NSString stringWithFormat:@"Failed to create buffer with bytes of size %llu", length]);
        return NULL;
    }
}

void metal_release_buffer(void* buffer) {
    if (buffer) {
        CFRelease(buffer);
    }
}

void* metal_buffer_contents(void* buffer) {
    @autoreleasepool {
        if (!buffer) return NULL;
        
        id<MTLBuffer> metalBuffer = (__bridge id<MTLBuffer>)buffer;
        return metalBuffer.contents;
    }
}

uint64_t metal_buffer_length(void* buffer) {
    @autoreleasepool {
        if (!buffer) return 0;
        
        id<MTLBuffer> metalBuffer = (__bridge id<MTLBuffer>)buffer;
        return metalBuffer.length;
    }
}

// Metal command buffer functions
void* metal_command_queue_command_buffer(void* queue) {
    @autoreleasepool {
        if (!queue) {
            set_metal_error(@"Command queue is NULL");
            return NULL;
        }
        
        id<MTLCommandQueue> commandQueue = (__bridge id<MTLCommandQueue>)queue;
        id<MTLCommandBuffer> commandBuffer = [commandQueue commandBuffer];
        if (commandBuffer) {
            return (__bridge_retained void*)commandBuffer;
        }
        set_metal_error(@"Failed to create command buffer");
        return NULL;
    }
}

void metal_command_buffer_commit(void* buffer) {
    @autoreleasepool {
        if (!buffer) return;
        
        id<MTLCommandBuffer> commandBuffer = (__bridge id<MTLCommandBuffer>)buffer;
        [commandBuffer commit];
    }
}

void metal_command_buffer_wait_until_completed(void* buffer) {
    @autoreleasepool {
        if (!buffer) return;
        
        id<MTLCommandBuffer> commandBuffer = (__bridge id<MTLCommandBuffer>)buffer;
        [commandBuffer waitUntilCompleted];
    }
}

int metal_command_buffer_status(void* buffer) {
    @autoreleasepool {
        if (!buffer) return -1;
        
        id<MTLCommandBuffer> commandBuffer = (__bridge id<MTLCommandBuffer>)buffer;
        return (int)commandBuffer.status;
    }
}

void metal_release_command_buffer(void* buffer) {
    if (buffer) {
        CFRelease(buffer);
    }
}

// Metal compute command encoder functions
void* metal_command_buffer_compute_command_encoder(void* buffer) {
    @autoreleasepool {
        if (!buffer) {
            set_metal_error(@"Command buffer is NULL");
            return NULL;
        }
        
        id<MTLCommandBuffer> commandBuffer = (__bridge id<MTLCommandBuffer>)buffer;
        id<MTLComputeCommandEncoder> encoder = [commandBuffer computeCommandEncoder];
        if (encoder) {
            return (__bridge_retained void*)encoder;
        }
        set_metal_error(@"Failed to create compute command encoder");
        return NULL;
    }
}

void metal_compute_encoder_set_compute_pipeline_state(void* encoder, void* pipelineState) {
    @autoreleasepool {
        if (!encoder || !pipelineState) return;
        
        id<MTLComputeCommandEncoder> computeEncoder = (__bridge id<MTLComputeCommandEncoder>)encoder;
        id<MTLComputePipelineState> pipeline = (__bridge id<MTLComputePipelineState>)pipelineState;
        [computeEncoder setComputePipelineState:pipeline];
    }
}

void metal_compute_encoder_set_buffer(void* encoder, void* buffer, uint64_t offset, int index) {
    @autoreleasepool {
        if (!encoder || !buffer) return;
        
        id<MTLComputeCommandEncoder> computeEncoder = (__bridge id<MTLComputeCommandEncoder>)encoder;
        id<MTLBuffer> metalBuffer = (__bridge id<MTLBuffer>)buffer;
        [computeEncoder setBuffer:metalBuffer offset:offset atIndex:index];
    }
}

void metal_compute_encoder_dispatch_threads(void* encoder, uint64_t threadsX, uint64_t threadsY, uint64_t threadsZ, uint64_t threadsPerGroupX, uint64_t threadsPerGroupY, uint64_t threadsPerGroupZ) {
    @autoreleasepool {
        if (!encoder) return;
        
        id<MTLComputeCommandEncoder> computeEncoder = (__bridge id<MTLComputeCommandEncoder>)encoder;
        MTLSize threadsPerThreadgroup = MTLSizeMake(threadsPerGroupX, threadsPerGroupY, threadsPerGroupZ);
        MTLSize threads = MTLSizeMake(threadsX, threadsY, threadsZ);
        
        [computeEncoder dispatchThreads:threads threadsPerThreadgroup:threadsPerThreadgroup];
    }
}

void metal_compute_encoder_end_encoding(void* encoder) {
    @autoreleasepool {
        if (!encoder) return;
        
        id<MTLComputeCommandEncoder> computeEncoder = (__bridge id<MTLComputeCommandEncoder>)encoder;
        [computeEncoder endEncoding];
    }
}

void metal_release_compute_encoder(void* encoder) {
    if (encoder) {
        CFRelease(encoder);
    }
}

// Metal library and function functions
void* metal_new_library_with_source(void* device, const char* source) {
    @autoreleasepool {
        if (!device || !source) {
            set_metal_error(@"Device or source is NULL");
            return NULL;
        }
        
        id<MTLDevice> metalDevice = (__bridge id<MTLDevice>)device;
        NSString *sourceString = [NSString stringWithUTF8String:source];
        
        NSError *error = nil;
        id<MTLLibrary> library = [metalDevice newLibraryWithSource:sourceString 
                                                           options:nil 
                                                             error:&error];
        if (library) {
            return (__bridge_retained void*)library;
        }
        
        if (error) {
            set_metal_error([NSString stringWithFormat:@"Metal library compilation failed: %@", error.localizedDescription]);
        } else {
            set_metal_error(@"Unknown error creating Metal library");
        }
        return NULL;
    }
}

void* metal_library_new_function_with_name(void* library, const char* name) {
    @autoreleasepool {
        if (!library || !name) {
            set_metal_error(@"Library or function name is NULL");
            return NULL;
        }
        
        id<MTLLibrary> metalLibrary = (__bridge id<MTLLibrary>)library;
        NSString *functionName = [NSString stringWithUTF8String:name];
        
        id<MTLFunction> function = [metalLibrary newFunctionWithName:functionName];
        if (function) {
            return (__bridge_retained void*)function;
        }
        
        set_metal_error([NSString stringWithFormat:@"Function '%@' not found in library", functionName]);
        return NULL;
    }
}

void* metal_new_compute_pipeline_state_with_function(void* device, void* function) {
    @autoreleasepool {
        if (!device || !function) {
            set_metal_error(@"Device or function is NULL");
            return NULL;
        }
        
        id<MTLDevice> metalDevice = (__bridge id<MTLDevice>)device;
        id<MTLFunction> metalFunction = (__bridge id<MTLFunction>)function;
        
        NSError *error = nil;
        id<MTLComputePipelineState> pipelineState = [metalDevice newComputePipelineStateWithFunction:metalFunction 
                                                                                                error:&error];
        if (pipelineState) {
            return (__bridge_retained void*)pipelineState;
        }
        
        if (error) {
            set_metal_error([NSString stringWithFormat:@"Failed to create compute pipeline state: %@", error.localizedDescription]);
        } else {
            set_metal_error(@"Unknown error creating compute pipeline state");
        }
        return NULL;
    }
}

void metal_release_library(void* library) {
    if (library) {
        CFRelease(library);
    }
}

void metal_release_function(void* function) {
    if (function) {
        CFRelease(function);
    }
}

void metal_release_pipeline_state(void* pipelineState) {
    if (pipelineState) {
        CFRelease(pipelineState);
    }
}

// Error handling
const char* metal_get_last_error() {
    @autoreleasepool {
        if (lastError) {
            return [lastError UTF8String];
        }
        return "No error";
    }
} 