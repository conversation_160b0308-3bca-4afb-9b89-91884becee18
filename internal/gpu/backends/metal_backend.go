//go:build darwin

package backends

import (
	"context"
	"fmt"
	"os/exec"
	"runtime"
	"strings"

	"neuralmetergo/internal/gpu/types"
)

// MetalBackend implements the unified GPUBackend interface for Apple Metal
type MetalBackend struct {
	initialized bool
	devices     []types.GPUDevice
}

// NewMetalBackend creates a new Metal backend
func NewMetalBackend() *MetalBackend {
	return &MetalBackend{
		initialized: false,
	}
}

// Backend identification
func (m *MetalBackend) Name() string     { return "Metal" }
func (m *MetalBackend) Version() string  { return "3.0" }
func (m *MetalBackend) Platform() string { return runtime.GOOS }

// EnumerateDevices discovers available Metal GPUs through runtime detection
func (m *MetalBackend) EnumerateDevices(ctx context.Context) ([]types.GPUDevice, error) {
	if !m.isMetalAvailable() {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 1001, "Metal not available on this system", -1)
	}

	devices, err := m.detectMetalDevices()
	if err != nil {
		return nil, types.NewGPUError(types.ErrorTypeDetection, 1002, fmt.Sprintf("Failed to detect Metal devices: %v", err), -1)
	}

	m.devices = devices
	m.initialized = true
	return devices, nil
}

// GetDevice returns a specific device by ID
func (m *MetalBackend) GetDevice(deviceID string) (*types.GPUDevice, error) {
	for i := range m.devices {
		if m.devices[i].ID == deviceID {
			return &m.devices[i], nil
		}
	}
	return nil, types.NewGPUError(types.ErrorTypeInitialization, 1003, "Metal device not found", -1)
}

// GetCapabilities returns Metal-specific capabilities
func (m *MetalBackend) GetCapabilities(device *types.GPUDevice) (*types.GPUCapability, error) {
	return &types.GPUCapability{
		MaxTextureSize:     [3]uint32{16384, 16384, 2048},
		MaxBufferSize:      1024 * 1024 * 1024, // 1GB max buffer
		MaxWorkGroupSize:   1024,
		MaxWorkGroupDim:    [3]uint32{1024, 1024, 64},
		MaxComputeUnits:    32,
		MaxMemoryBandwidth: 400 * 1024 * 1024 * 1024, // 400 GB/s
		Features: map[types.GPUFeature]bool{
			types.FeatureCompute:       true,
			types.FeatureGraphics:      true,
			types.FeatureUnifiedMemory: true,
			types.FeatureFloat16:       true,
			types.FeatureAsyncCompute:  true,
			types.FeatureSharedMemory:  true,
			types.FeatureHalfPrecision: true,
		},
		Extensions: []string{"metal3.0", "unified_memory"},
		Limits: map[string]interface{}{
			"max_threads_per_group": 1024,
			"max_buffer_size":       1024 * 1024 * 1024,
		},
	}, nil
}

// SupportsFeature checks if a device supports a specific feature
func (m *MetalBackend) SupportsFeature(device *types.GPUDevice, feature types.GPUFeature) bool {
	capabilities, err := m.GetCapabilities(device)
	if err != nil {
		return false
	}
	return capabilities.Features[feature]
}

// isMetalAvailable checks if Metal is actually available at runtime
func (m *MetalBackend) isMetalAvailable() bool {
	// Only available on macOS
	if runtime.GOOS != "darwin" {
		return false
	}

	// Try to run system_profiler to check for GPU hardware
	cmd := exec.Command("system_profiler", "SPDisplaysDataType", "-json")
	output, err := cmd.Output()
	if err != nil {
		return false
	}

	// If we can get display data, Metal should be available
	return len(output) > 0 && strings.Contains(string(output), "spdisplays_")
}

// detectMetalDevices uses system_profiler to find Metal-capable GPUs
func (m *MetalBackend) detectMetalDevices() ([]types.GPUDevice, error) {
	cmd := exec.Command("system_profiler", "SPDisplaysDataType", "-json")
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to run system_profiler: %v", err)
	}

	// Parse the JSON output to extract GPU information
	// For now, create a basic Metal device based on system detection
	devices := []types.GPUDevice{
		{
			ID:           "metal-0",
			Name:         m.getGPUName(string(output)),
			Type:         types.DeviceTypeIntegrated, // Apple Silicon is integrated
			Backend:      "Metal",
			Vendor:       "Apple",
			Index:        0,
			BusInfo:      "integrated",
			MemoryTotal:  m.getGPUMemory(string(output)),
			MemoryFree:   m.getGPUMemory(string(output)), // Start with full memory available
			Utilization:  0.0,
			PowerUsage:   0.0,
			ClockSpeed:   1000, // MHz placeholder
			ComputeUnits: m.getComputeUnits(string(output)),
			IsActive:     true,
			Compute: types.ComputeInfo{
				Units:      m.getComputeUnits(string(output)),
				ClockSpeed: 1000,
				MaxThreads: 1024,
			},
			Memory: types.MemoryInfo{
				Total:     m.getGPUMemory(string(output)),
				Free:      m.getGPUMemory(string(output)),
				Used:      0,
				Bandwidth: 400 * 1024 * 1024 * 1024, // 400 GB/s
				Type:      types.MemoryTypeUnified,
			},
			Performance: types.PerformanceInfo{
				GFLOPS:      2000.0, // Placeholder
				MemoryGBps:  400.0,
				Utilization: 0.0,
			},
			Capabilities: map[string]interface{}{
				"metal_version":    "3.0",
				"unified_memory":   true,
				"max_texture_size": 16384,
			},
			Status: types.DeviceStatusAvailable,
		},
	}

	return devices, nil
}

// Helper methods to extract GPU information from system_profiler output
func (m *MetalBackend) getGPUName(output string) string {
	// Simple parsing - in real implementation would parse JSON properly
	if strings.Contains(output, "Apple M1") {
		return "Apple M1"
	} else if strings.Contains(output, "Apple M2") {
		return "Apple M2"
	} else if strings.Contains(output, "Apple M3") {
		return "Apple M3"
	}
	return "Apple GPU"
}

func (m *MetalBackend) getGPUMemory(output string) uint64 {
	// Extract memory info - simplified for now
	// In real implementation, would parse the JSON to get actual memory
	if strings.Contains(output, "M1") {
		return 8 * 1024 * 1024 * 1024 // 8GB for M1
	}
	return 8 * 1024 * 1024 * 1024 // Default 8GB
}

func (m *MetalBackend) getComputeUnits(output string) int {
	// Extract compute units - simplified
	if strings.Contains(output, "M1") {
		return 8 // M1 has 8 GPU cores
	}
	return 8 // Default
}

// CreateContext creates a Metal context for the specified device
func (m *MetalBackend) CreateContext(device *types.GPUDevice) (types.GPUContext, error) {
	if !m.initialized {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 1004, "Metal backend not initialized", -1)
	}

	// Create Metal context using the real Metal APIs
	return NewMetalContext(device)
}

// CreateMemoryManager creates a memory manager for the given context
func (m *MetalBackend) CreateMemoryManager(ctx types.GPUContext) (types.GPUMemoryManager, error) {
	if ctx == nil || !ctx.IsValid() {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 1005, "Invalid Metal context", -1)
	}

	return NewMetalMemoryManager(ctx)
}

// CreateExecutor creates an executor for the given context
func (m *MetalBackend) CreateExecutor(ctx types.GPUContext) (types.GPUExecutor, error) {
	if ctx == nil || !ctx.IsValid() {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 1006, "Invalid Metal context", -1)
	}

	return NewMetalExecutor(ctx)
}

// Placeholder constructors - these need real Metal implementation from metal_context.go

// NewMetalContext creates a Metal context (placeholder)
func NewMetalContext(device *types.GPUDevice) (types.GPUContext, error) {
	return nil, types.NewGPUError(types.ErrorTypeInitialization, 1007, "Metal context creation not yet implemented - needs real Metal API integration", -1)
}

// NewMetalMemoryManager creates a Metal memory manager (placeholder)
func NewMetalMemoryManager(ctx types.GPUContext) (types.GPUMemoryManager, error) {
	return nil, types.NewGPUError(types.ErrorTypeInitialization, 1008, "Metal memory manager creation not yet implemented - needs real Metal API integration", -1)
}

// NewMetalExecutor creates a Metal executor (placeholder)
func NewMetalExecutor(ctx types.GPUContext) (types.GPUExecutor, error) {
	return nil, types.NewGPUError(types.ErrorTypeInitialization, 1009, "Metal executor creation not yet implemented - needs real Metal API integration", -1)
}
