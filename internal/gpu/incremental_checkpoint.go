package gpu

import (
	"bytes"
	"compress/gzip"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"io"
	"sync"
	"time"
)

// IncrementalCheckpointManager manages delta-based checkpointing
type IncrementalCheckpointManager struct {
	serializer *GPUStateSerializer
	storage    CheckpointStorage
	config     IncrementalCheckpointConfig
	logger     Logger
	mu         sync.RWMutex

	// State tracking for incremental checkpoints
	lastSnapshots map[string]*GPUStateSnapshot     // taskID -> last snapshot
	deltaTrackers map[string]*DeltaTracker         // taskID -> delta tracker
	activeOps     map[string]*IncrementalOperation // taskID -> operation
}

// IncrementalCheckpointConfig configures incremental checkpointing behavior
type IncrementalCheckpointConfig struct {
	// Basic settings
	Enabled               bool  `json:"enabled"`
	MaxDeltaSize          int64 `json:"max_delta_size"`          // Max size before forcing full checkpoint
	DeltaCompressionLevel int   `json:"delta_compression_level"` // Compression level for deltas

	// Timing settings
	FullCheckpointInterval time.Duration `json:"full_checkpoint_interval"` // Force full checkpoint after interval
	MaxIncrem<PERSON><PERSON>hain    int           `json:"max_incremental_chain"`    // Max incremental checkpoints before full

	// Performance settings
	AsyncDeltaCalculation bool  `json:"async_delta_calculation"` // Calculate deltas asynchronously
	ParallelDeltaWorkers  int   `json:"parallel_delta_workers"`  // Number of parallel delta workers
	MemoryBufferSize      int64 `json:"memory_buffer_size"`      // Buffer size for delta operations

	// Optimization settings
	TensorDiffThreshold  float64 `json:"tensor_diff_threshold"`  // Minimum change to include in delta
	SkipUnchangedTensors bool    `json:"skip_unchanged_tensors"` // Skip tensors with no changes
	UseBlockLevelDelta   bool    `json:"use_block_level_delta"`  // Use block-level instead of full tensor delta
	BlockSize            int     `json:"block_size"`             // Block size for block-level deltas
}

// DeltaTracker tracks changes between snapshots for incremental checkpointing
type DeltaTracker struct {
	TaskID           string            `json:"task_id"`
	LastFullSnapshot *GPUStateSnapshot `json:"last_full_snapshot"`
	TensorHashes     map[string]string `json:"tensor_hashes"` // tensorID -> hash
	MemoryHashes     map[string]string `json:"memory_hashes"` // poolID -> hash
	StreamHashes     map[string]string `json:"stream_hashes"` // streamID -> hash
	ChangeLog        []DeltaChange     `json:"change_log"`
	LastUpdate       time.Time         `json:"last_update"`
	IncrementalCount int               `json:"incremental_count"` // Number of incremental checkpoints since last full
	TotalCheckpoints int               `json:"total_checkpoints"` // Total number of checkpoints created
}

// DeltaChange represents a single change in the GPU state
type DeltaChange struct {
	Type      DeltaChangeType        `json:"type"`
	TensorID  string                 `json:"tensor_id,omitempty"`
	PoolID    string                 `json:"pool_id,omitempty"`
	StreamID  string                 `json:"stream_id,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	OldHash   string                 `json:"old_hash"`
	NewHash   string                 `json:"new_hash"`
	Size      int64                  `json:"size"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// DeltaChangeType represents the type of change in a delta
type DeltaChangeType string

const (
	DeltaChangeTensorModified DeltaChangeType = "tensor_modified"
	DeltaChangeTensorAdded    DeltaChangeType = "tensor_added"
	DeltaChangeTensorRemoved  DeltaChangeType = "tensor_removed"
	DeltaChangeMemoryModified DeltaChangeType = "memory_modified"
	DeltaChangeStreamModified DeltaChangeType = "stream_modified"
)

// IncrementalCheckpoint represents a delta-based checkpoint
type IncrementalCheckpoint struct {
	// Base checkpoint fields
	TaskID    string    `json:"task_id"`
	Version   int       `json:"version"`
	Timestamp time.Time `json:"timestamp"`
	NodeID    string    `json:"node_id"`

	// Incremental-specific fields
	IsIncremental bool          `json:"is_incremental"`
	BaseVersion   int           `json:"base_version"` // Version this delta is based on
	DeltaData     []byte        `json:"delta_data"`   // Compressed delta data
	Changes       []DeltaChange `json:"changes"`      // List of changes

	// Metadata
	DeltaSize      int64  `json:"delta_size"`
	CompressedSize int64  `json:"compressed_size"`
	Hash           string `json:"hash"`
	ChainLength    int    `json:"chain_length"` // Length of incremental chain
}

// IncrementalOperation tracks an ongoing incremental checkpoint operation
type IncrementalOperation struct {
	TaskID    string                 `json:"task_id"`
	StartTime time.Time              `json:"start_time"`
	Phase     IncrementalPhase       `json:"phase"`
	Progress  float64                `json:"progress"`
	Error     error                  `json:"error,omitempty"`
	WorkerID  int                    `json:"worker_id,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// IncrementalPhase represents the current phase of incremental checkpointing
type IncrementalPhase string

const (
	IncrementalPhaseCapturing   IncrementalPhase = "capturing"
	IncrementalPhaseCalculating IncrementalPhase = "calculating"
	IncrementalPhaseCompressing IncrementalPhase = "compressing"
	IncrementalPhaseStoring     IncrementalPhase = "storing"
	IncrementalPhaseCompleted   IncrementalPhase = "completed"
	IncrementalPhaseFailed      IncrementalPhase = "failed"
)

// DefaultIncrementalCheckpointConfig returns default configuration
func DefaultIncrementalCheckpointConfig() IncrementalCheckpointConfig {
	return IncrementalCheckpointConfig{
		Enabled:                true,
		MaxDeltaSize:           50 * 1024 * 1024, // 50MB
		DeltaCompressionLevel:  6,
		FullCheckpointInterval: 30 * time.Minute,
		MaxIncrementalChain:    10,
		AsyncDeltaCalculation:  true,
		ParallelDeltaWorkers:   4,
		MemoryBufferSize:       10 * 1024 * 1024, // 10MB
		TensorDiffThreshold:    0.001,            // 0.1% change threshold
		SkipUnchangedTensors:   true,
		UseBlockLevelDelta:     true,
		BlockSize:              4096, // 4KB blocks
	}
}

// NewIncrementalCheckpointManager creates a new incremental checkpoint manager
func NewIncrementalCheckpointManager(
	serializer *GPUStateSerializer,
	storage CheckpointStorage,
	config IncrementalCheckpointConfig,
	logger Logger,
) *IncrementalCheckpointManager {
	return &IncrementalCheckpointManager{
		serializer:    serializer,
		storage:       storage,
		config:        config,
		logger:        logger,
		lastSnapshots: make(map[string]*GPUStateSnapshot),
		deltaTrackers: make(map[string]*DeltaTracker),
		activeOps:     make(map[string]*IncrementalOperation),
	}
}

// CreateIncrementalCheckpoint creates an incremental checkpoint for a task
func (icm *IncrementalCheckpointManager) CreateIncrementalCheckpoint(
	taskID string,
	deviceID int,
	apiType string,
) (*IncrementalCheckpoint, error) {
	if !icm.config.Enabled {
		return nil, fmt.Errorf("incremental checkpointing is disabled")
	}

	icm.mu.Lock()
	defer icm.mu.Unlock()

	// Start operation tracking
	operation := &IncrementalOperation{
		TaskID:    taskID,
		StartTime: time.Now(),
		Phase:     IncrementalPhaseCapturing,
		Progress:  0.0,
	}
	icm.activeOps[taskID] = operation

	// Capture current GPU state
	operation.Phase = IncrementalPhaseCapturing
	operation.Progress = 0.1
	currentSnapshot, err := icm.serializer.CaptureGPUState(deviceID, apiType)
	if err != nil {
		operation.Error = err
		operation.Phase = IncrementalPhaseFailed
		return nil, fmt.Errorf("failed to capture GPU state: %w", err)
	}

	// Get or create delta tracker
	tracker, exists := icm.deltaTrackers[taskID]
	if !exists {
		tracker = &DeltaTracker{
			TaskID:           taskID,
			TensorHashes:     make(map[string]string),
			MemoryHashes:     make(map[string]string),
			StreamHashes:     make(map[string]string),
			ChangeLog:        []DeltaChange{},
			LastUpdate:       time.Now(),
			IncrementalCount: 0,
			TotalCheckpoints: 0,
		}
		icm.deltaTrackers[taskID] = tracker
	}

	// Check if we need a full checkpoint
	needsFullCheckpoint := icm.shouldCreateFullCheckpoint(tracker, currentSnapshot)

	if needsFullCheckpoint {
		return icm.createFullCheckpoint(taskID, currentSnapshot, tracker, operation)
	}

	return icm.createDeltaCheckpoint(taskID, currentSnapshot, tracker, operation)
}

// shouldCreateFullCheckpoint determines if a full checkpoint is needed
func (icm *IncrementalCheckpointManager) shouldCreateFullCheckpoint(
	tracker *DeltaTracker,
	currentSnapshot *GPUStateSnapshot,
) bool {
	// No previous snapshot - need full checkpoint
	if tracker.LastFullSnapshot == nil {
		return true
	}

	// Exceeded incremental chain length
	if tracker.IncrementalCount >= icm.config.MaxIncrementalChain {
		return true
	}

	// Exceeded time interval
	if time.Since(tracker.LastUpdate) > icm.config.FullCheckpointInterval {
		return true
	}

	return false
}

// createFullCheckpoint creates a full checkpoint and resets the delta tracker
func (icm *IncrementalCheckpointManager) createFullCheckpoint(
	taskID string,
	snapshot *GPUStateSnapshot,
	tracker *DeltaTracker,
	operation *IncrementalOperation,
) (*IncrementalCheckpoint, error) {
	operation.Phase = IncrementalPhaseCompressing
	operation.Progress = 0.5

	// Serialize the full snapshot
	data, err := icm.serializer.SerializeSnapshot(snapshot)
	if err != nil {
		operation.Error = err
		operation.Phase = IncrementalPhaseFailed
		return nil, fmt.Errorf("failed to serialize snapshot: %w", err)
	}

	// Create checkpoint
	checkpoint := &IncrementalCheckpoint{
		TaskID:         taskID,
		Version:        tracker.TotalCheckpoints + 1,
		Timestamp:      time.Now(),
		NodeID:         "local", // TODO: Get actual node ID
		IsIncremental:  false,
		BaseVersion:    0,
		DeltaData:      data,
		Changes:        []DeltaChange{},
		DeltaSize:      int64(len(data)),
		CompressedSize: int64(len(data)),
		ChainLength:    0,
	}

	// Calculate hash
	checkpoint.Hash = icm.calculateCheckpointHash(checkpoint)

	// Update tracker
	tracker.LastFullSnapshot = snapshot
	tracker.IncrementalCount = 0
	tracker.TotalCheckpoints++
	tracker.LastUpdate = time.Now()
	tracker.ChangeLog = []DeltaChange{}
	icm.updateTrackerHashes(tracker, snapshot)

	// Store checkpoint
	operation.Phase = IncrementalPhaseStoring
	operation.Progress = 0.9
	if err := icm.storeIncrementalCheckpoint(checkpoint); err != nil {
		operation.Error = err
		operation.Phase = IncrementalPhaseFailed
		return nil, fmt.Errorf("failed to store checkpoint: %w", err)
	}

	operation.Phase = IncrementalPhaseCompleted
	operation.Progress = 1.0

	icm.logger.Printf("Created full checkpoint for task %s, version %d, size %d bytes",
		taskID, checkpoint.Version, checkpoint.DeltaSize)

	return checkpoint, nil
}

// createDeltaCheckpoint creates an incremental delta checkpoint
func (icm *IncrementalCheckpointManager) createDeltaCheckpoint(
	taskID string,
	currentSnapshot *GPUStateSnapshot,
	tracker *DeltaTracker,
	operation *IncrementalOperation,
) (*IncrementalCheckpoint, error) {
	operation.Phase = IncrementalPhaseCalculating
	operation.Progress = 0.3

	// Calculate delta between current and last snapshot
	delta, changes, err := icm.calculateDelta(tracker.LastFullSnapshot, currentSnapshot, tracker)
	if err != nil {
		operation.Error = err
		operation.Phase = IncrementalPhaseFailed
		return nil, fmt.Errorf("failed to calculate delta: %w", err)
	}

	// Check if delta is too large
	if int64(len(delta)) > icm.config.MaxDeltaSize {
		icm.logger.Printf("Delta too large (%d bytes), creating full checkpoint instead", len(delta))
		return icm.createFullCheckpoint(taskID, currentSnapshot, tracker, operation)
	}

	operation.Phase = IncrementalPhaseCompressing
	operation.Progress = 0.6

	// Compress delta data
	compressor := NewDefaultCompressor(icm.config.DeltaCompressionLevel)
	compressedDelta, err := compressor.Compress(delta)
	if err != nil {
		operation.Error = err
		operation.Phase = IncrementalPhaseFailed
		return nil, fmt.Errorf("failed to compress delta: %w", err)
	}

	// Create incremental checkpoint
	checkpoint := &IncrementalCheckpoint{
		TaskID:         taskID,
		Version:        tracker.TotalCheckpoints + 1,
		Timestamp:      time.Now(),
		NodeID:         "local", // TODO: Get actual node ID
		IsIncremental:  true,
		BaseVersion:    0, // TODO: Track base version properly
		DeltaData:      compressedDelta,
		Changes:        changes,
		DeltaSize:      int64(len(delta)),
		CompressedSize: int64(len(compressedDelta)),
		ChainLength:    tracker.IncrementalCount + 1,
	}

	// Calculate hash
	checkpoint.Hash = icm.calculateCheckpointHash(checkpoint)

	// Update tracker
	tracker.IncrementalCount++
	tracker.TotalCheckpoints++
	tracker.LastUpdate = time.Now()
	tracker.ChangeLog = append(tracker.ChangeLog, changes...)
	icm.updateTrackerHashes(tracker, currentSnapshot)

	// Store checkpoint
	operation.Phase = IncrementalPhaseStoring
	operation.Progress = 0.9
	if err := icm.storeIncrementalCheckpoint(checkpoint); err != nil {
		operation.Error = err
		operation.Phase = IncrementalPhaseFailed
		return nil, fmt.Errorf("failed to store checkpoint: %w", err)
	}

	operation.Phase = IncrementalPhaseCompleted
	operation.Progress = 1.0

	icm.logger.Printf("Created incremental checkpoint for task %s, version %d, delta size %d bytes (compressed to %d)",
		taskID, checkpoint.Version, checkpoint.DeltaSize, checkpoint.CompressedSize)

	return checkpoint, nil
}

// calculateDelta calculates the delta between two snapshots
func (icm *IncrementalCheckpointManager) calculateDelta(
	baseSnapshot, currentSnapshot *GPUStateSnapshot,
	tracker *DeltaTracker,
) ([]byte, []DeltaChange, error) {
	deltaData := make(map[string]interface{})
	changes := []DeltaChange{}

	// Calculate tensor deltas
	tensorDeltas, tensorChanges := icm.calculateTensorDeltas(baseSnapshot, currentSnapshot, tracker)
	if len(tensorDeltas) > 0 {
		deltaData["tensors"] = tensorDeltas
		changes = append(changes, tensorChanges...)
	}

	// Calculate memory pool deltas
	memoryDeltas, memoryChanges := icm.calculateMemoryDeltas(baseSnapshot, currentSnapshot, tracker)
	if len(memoryDeltas) > 0 {
		deltaData["memory"] = memoryDeltas
		changes = append(changes, memoryChanges...)
	}

	// Calculate stream deltas
	streamDeltas, streamChanges := icm.calculateStreamDeltas(baseSnapshot, currentSnapshot, tracker)
	if len(streamDeltas) > 0 {
		deltaData["streams"] = streamDeltas
		changes = append(changes, streamChanges...)
	}

	// Serialize delta data
	data, err := json.Marshal(deltaData)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to serialize delta data: %w", err)
	}

	return data, changes, nil
}

// calculateTensorDeltas calculates deltas for tensor states
func (icm *IncrementalCheckpointManager) calculateTensorDeltas(
	baseSnapshot, currentSnapshot *GPUStateSnapshot,
	tracker *DeltaTracker,
) (map[string]interface{}, []DeltaChange) {
	deltas := make(map[string]interface{})
	changes := []DeltaChange{}

	// Create maps for efficient lookup
	baseTensors := make(map[string]TensorState)
	for _, tensor := range baseSnapshot.TensorStates {
		baseTensors[tensor.ID] = tensor
	}

	currentTensors := make(map[string]TensorState)
	for _, tensor := range currentSnapshot.TensorStates {
		currentTensors[tensor.ID] = tensor
	}

	// Check for modified and added tensors
	for id, currentTensor := range currentTensors {
		baseTensor, exists := baseTensors[id]

		if !exists {
			// New tensor
			deltas[id] = map[string]interface{}{
				"operation": "add",
				"data":      currentTensor,
			}
			changes = append(changes, DeltaChange{
				Type:      DeltaChangeTensorAdded,
				TensorID:  id,
				Timestamp: time.Now(),
				NewHash:   currentTensor.Checksum,
				Size:      currentTensor.Size,
			})
		} else if baseTensor.Checksum != currentTensor.Checksum {
			// Modified tensor
			if icm.config.SkipUnchangedTensors {
				// Calculate difference ratio
				diffRatio := icm.calculateTensorDifference(baseTensor, currentTensor)
				if diffRatio < icm.config.TensorDiffThreshold {
					continue // Skip if change is below threshold
				}
			}

			deltaOp := map[string]interface{}{
				"operation": "modify",
			}

			if icm.config.UseBlockLevelDelta {
				// Use block-level delta
				blockDeltas := icm.calculateBlockLevelDelta(baseTensor.Data, currentTensor.Data)
				deltaOp["block_deltas"] = blockDeltas
			} else {
				// Use full tensor data
				deltaOp["data"] = currentTensor
			}

			deltas[id] = deltaOp
			changes = append(changes, DeltaChange{
				Type:      DeltaChangeTensorModified,
				TensorID:  id,
				Timestamp: time.Now(),
				OldHash:   baseTensor.Checksum,
				NewHash:   currentTensor.Checksum,
				Size:      currentTensor.Size,
			})
		}
	}

	// Check for removed tensors
	for id, baseTensor := range baseTensors {
		if _, exists := currentTensors[id]; !exists {
			deltas[id] = map[string]interface{}{
				"operation": "remove",
			}
			changes = append(changes, DeltaChange{
				Type:      DeltaChangeTensorRemoved,
				TensorID:  id,
				Timestamp: time.Now(),
				OldHash:   baseTensor.Checksum,
				Size:      baseTensor.Size,
			})
		}
	}

	return deltas, changes
}

// calculateMemoryDeltas calculates deltas for memory pool states
func (icm *IncrementalCheckpointManager) calculateMemoryDeltas(
	baseSnapshot, currentSnapshot *GPUStateSnapshot,
	tracker *DeltaTracker,
) (map[string]interface{}, []DeltaChange) {
	deltas := make(map[string]interface{})
	changes := []DeltaChange{}

	// Compare memory states by serializing and comparing
	baseMemoryData, _ := json.Marshal(baseSnapshot.MemoryState)
	currentMemoryData, _ := json.Marshal(currentSnapshot.MemoryState)

	if !bytes.Equal(baseMemoryData, currentMemoryData) {
		deltas["memory_state"] = currentSnapshot.MemoryState
		changes = append(changes, DeltaChange{
			Type:      DeltaChangeMemoryModified,
			PoolID:    fmt.Sprintf("device_%d", currentSnapshot.MemoryState.DeviceID),
			Timestamp: time.Now(),
			Size:      int64(len(currentMemoryData)),
		})
	}

	return deltas, changes
}

// calculateStreamDeltas calculates deltas for stream manager states
func (icm *IncrementalCheckpointManager) calculateStreamDeltas(
	baseSnapshot, currentSnapshot *GPUStateSnapshot,
	tracker *DeltaTracker,
) (map[string]interface{}, []DeltaChange) {
	deltas := make(map[string]interface{})
	changes := []DeltaChange{}

	// Compare stream states by serializing and comparing
	baseStreamData, _ := json.Marshal(baseSnapshot.StreamState)
	currentStreamData, _ := json.Marshal(currentSnapshot.StreamState)

	if !bytes.Equal(baseStreamData, currentStreamData) {
		deltas["stream_state"] = currentSnapshot.StreamState
		changes = append(changes, DeltaChange{
			Type:      DeltaChangeStreamModified,
			StreamID:  fmt.Sprintf("%s_device_%d", currentSnapshot.StreamState.APIType, currentSnapshot.StreamState.DeviceID),
			Timestamp: time.Now(),
			Size:      int64(len(currentStreamData)),
		})
	}

	return deltas, changes
}

// calculateTensorDifference calculates the difference ratio between two tensors
func (icm *IncrementalCheckpointManager) calculateTensorDifference(base, current TensorState) float64 {
	// Simple implementation - compare checksums
	if base.Checksum == current.Checksum {
		return 0.0
	}

	// For now, assume any checksum difference means significant change
	// TODO: Implement more sophisticated difference calculation
	return 1.0
}

// calculateBlockLevelDelta calculates block-level differences between tensor data
func (icm *IncrementalCheckpointManager) calculateBlockLevelDelta(baseData, currentData []byte) []map[string]interface{} {
	blockDeltas := []map[string]interface{}{}
	blockSize := icm.config.BlockSize

	maxLen := len(baseData)
	if len(currentData) > maxLen {
		maxLen = len(currentData)
	}

	for i := 0; i < maxLen; i += blockSize {
		end := i + blockSize
		if end > maxLen {
			end = maxLen
		}

		baseBlock := []byte{}
		currentBlock := []byte{}

		if i < len(baseData) {
			blockEnd := end
			if blockEnd > len(baseData) {
				blockEnd = len(baseData)
			}
			baseBlock = baseData[i:blockEnd]
		}

		if i < len(currentData) {
			blockEnd := end
			if blockEnd > len(currentData) {
				blockEnd = len(currentData)
			}
			currentBlock = currentData[i:blockEnd]
		}

		// Compare blocks
		if !bytes.Equal(baseBlock, currentBlock) {
			blockDeltas = append(blockDeltas, map[string]interface{}{
				"offset": i,
				"data":   currentBlock,
			})
		}
	}

	return blockDeltas
}

// updateTrackerHashes updates the hash tracking in the delta tracker
func (icm *IncrementalCheckpointManager) updateTrackerHashes(tracker *DeltaTracker, snapshot *GPUStateSnapshot) {
	// Update tensor hashes
	for _, tensor := range snapshot.TensorStates {
		tracker.TensorHashes[tensor.ID] = tensor.Checksum
	}

	// Update memory hash by serializing the memory state
	memoryData, _ := json.Marshal(snapshot.MemoryState)
	memoryHash := icm.calculateHash(memoryData)
	tracker.MemoryHashes[fmt.Sprintf("device_%d", snapshot.MemoryState.DeviceID)] = memoryHash

	// Update stream hash by serializing the stream state
	streamData, _ := json.Marshal(snapshot.StreamState)
	streamHash := icm.calculateHash(streamData)
	tracker.StreamHashes[fmt.Sprintf("%s_device_%d", snapshot.StreamState.APIType, snapshot.StreamState.DeviceID)] = streamHash
}

// calculateCheckpointHash calculates a hash for the checkpoint
func (icm *IncrementalCheckpointManager) calculateCheckpointHash(checkpoint *IncrementalCheckpoint) string {
	hasher := sha256.New()
	hasher.Write([]byte(checkpoint.TaskID))
	hasher.Write([]byte(fmt.Sprintf("%d", checkpoint.Version)))
	hasher.Write(checkpoint.DeltaData)
	return fmt.Sprintf("%x", hasher.Sum(nil))
}

// calculateHash calculates SHA256 hash of data
func (icm *IncrementalCheckpointManager) calculateHash(data []byte) string {
	hasher := sha256.New()
	hasher.Write(data)
	return fmt.Sprintf("%x", hasher.Sum(nil))
}

// storeIncrementalCheckpoint stores an incremental checkpoint
func (icm *IncrementalCheckpointManager) storeIncrementalCheckpoint(checkpoint *IncrementalCheckpoint) error {
	// Convert to standard Checkpoint format for storage
	stdCheckpoint := &Checkpoint{
		TaskID:         checkpoint.TaskID,
		Version:        checkpoint.Version,
		Timestamp:      checkpoint.Timestamp,
		NodeID:         checkpoint.NodeID,
		Data:           checkpoint.DeltaData,
		Incremental:    checkpoint.IsIncremental,
		Hash:           checkpoint.Hash,
		CompressedSize: checkpoint.CompressedSize,
		OriginalSize:   checkpoint.DeltaSize,
	}

	return icm.storage.StoreCheckpoint(stdCheckpoint)
}

// GetOperationStatus returns the status of an ongoing operation
func (icm *IncrementalCheckpointManager) GetOperationStatus(taskID string) (*IncrementalOperation, bool) {
	icm.mu.RLock()
	defer icm.mu.RUnlock()

	op, exists := icm.activeOps[taskID]
	return op, exists
}

// ClearCompletedOperations removes completed operations from tracking
func (icm *IncrementalCheckpointManager) ClearCompletedOperations() {
	icm.mu.Lock()
	defer icm.mu.Unlock()

	for taskID, op := range icm.activeOps {
		if op.Phase == IncrementalPhaseCompleted || op.Phase == IncrementalPhaseFailed {
			delete(icm.activeOps, taskID)
		}
	}
}

// GetDeltaTracker returns the delta tracker for a task
func (icm *IncrementalCheckpointManager) GetDeltaTracker(taskID string) (*DeltaTracker, bool) {
	icm.mu.RLock()
	defer icm.mu.RUnlock()

	tracker, exists := icm.deltaTrackers[taskID]
	return tracker, exists
}

// ResetDeltaTracker resets the delta tracker for a task
func (icm *IncrementalCheckpointManager) ResetDeltaTracker(taskID string) {
	icm.mu.Lock()
	defer icm.mu.Unlock()

	delete(icm.deltaTrackers, taskID)
	delete(icm.lastSnapshots, taskID)
}

// RestoreFromIncrementalCheckpoint restores state from an incremental checkpoint
func (icm *IncrementalCheckpointManager) RestoreFromIncrementalCheckpoint(
	checkpoint *IncrementalCheckpoint,
) error {
	if !checkpoint.IsIncremental {
		return fmt.Errorf("checkpoint is not incremental")
	}

	icm.mu.Lock()
	defer icm.mu.Unlock()

	// Get base snapshot for this checkpoint chain
	tracker, exists := icm.deltaTrackers[checkpoint.TaskID]
	if !exists || tracker.LastFullSnapshot == nil {
		return fmt.Errorf("base snapshot not found for incremental restore of task %s", checkpoint.TaskID)
	}

	baseSnapshot := tracker.LastFullSnapshot

	// Decompress delta data if compressed
	deltaData := checkpoint.DeltaData
	if len(deltaData) > 0 {
		// Assume delta data is compressed, decompress it
		decompressed, err := icm.decompressDelta(deltaData)
		if err != nil {
			return fmt.Errorf("failed to decompress delta data: %w", err)
		}
		deltaData = decompressed
	}

	// Apply delta changes to base snapshot
	for _, change := range checkpoint.Changes {
		if err := icm.applyDeltaChange(baseSnapshot, change, deltaData); err != nil {
			return fmt.Errorf("failed to apply delta change %s: %w", change.Type, err)
		}
	}

	// Restore the modified snapshot using the serializer
	if err := icm.serializer.RestoreGPUState(baseSnapshot); err != nil {
		return fmt.Errorf("failed to restore modified snapshot: %w", err)
	}

	// Update tracker state
	tracker.LastUpdate = time.Now()
	tracker.IncrementalCount++

	icm.logger.Printf("Successfully restored from incremental checkpoint: task=%s, version=%d, changes=%d",
		checkpoint.TaskID, checkpoint.Version, len(checkpoint.Changes))

	return nil
}

// RestoreFromIncrementalChain restores state from a chain of incremental checkpoints
func (icm *IncrementalCheckpointManager) RestoreFromIncrementalChain(
	taskID string,
	checkpoints []*IncrementalCheckpoint,
) error {
	if len(checkpoints) == 0 {
		return fmt.Errorf("no checkpoints provided for chain restore")
	}

	icm.mu.Lock()
	defer icm.mu.Unlock()

	// Find base checkpoint (first non-incremental or oldest)
	var baseSnapshot *GPUStateSnapshot
	var incrementalCheckpoints []*IncrementalCheckpoint

	for _, checkpoint := range checkpoints {
		if !checkpoint.IsIncremental {
			// This is a full checkpoint, use as base
			if err := icm.deserializeFullCheckpoint(checkpoint, &baseSnapshot); err != nil {
				return fmt.Errorf("failed to deserialize base checkpoint: %w", err)
			}
		} else {
			incrementalCheckpoints = append(incrementalCheckpoints, checkpoint)
		}
	}

	if baseSnapshot == nil {
		return fmt.Errorf("no base checkpoint found in chain for task %s", taskID)
	}

	// Apply incremental checkpoints in order
	for _, checkpoint := range incrementalCheckpoints {
		// Decompress delta data
		deltaData, err := icm.decompressDelta(checkpoint.DeltaData)
		if err != nil {
			return fmt.Errorf("failed to decompress delta for version %d: %w", checkpoint.Version, err)
		}

		// Apply changes
		for _, change := range checkpoint.Changes {
			if err := icm.applyDeltaChange(baseSnapshot, change, deltaData); err != nil {
				return fmt.Errorf("failed to apply change in version %d: %w", checkpoint.Version, err)
			}
		}
	}

	// Restore final state
	if err := icm.serializer.RestoreGPUState(baseSnapshot); err != nil {
		return fmt.Errorf("failed to restore final state: %w", err)
	}

	icm.logger.Printf("Successfully restored from incremental chain: task=%s, checkpoints=%d",
		taskID, len(checkpoints))

	return nil
}

// FastRecoveryFromLatest performs fast recovery from the latest available checkpoint
func (icm *IncrementalCheckpointManager) FastRecoveryFromLatest(taskID string) error {
	icm.mu.RLock()
	tracker, exists := icm.deltaTrackers[taskID]
	icm.mu.RUnlock()

	if !exists {
		return fmt.Errorf("no delta tracker found for task %s", taskID)
	}

	// Use the last full snapshot if available
	if tracker.LastFullSnapshot != nil {
		icm.logger.Printf("Performing fast recovery from last full snapshot for task %s", taskID)
		return icm.serializer.RestoreGPUState(tracker.LastFullSnapshot)
	}

	// Try to load latest checkpoint from storage
	checkpoint, err := icm.loadLatestCheckpoint(taskID)
	if err != nil {
		return fmt.Errorf("failed to load latest checkpoint: %w", err)
	}

	if checkpoint.IsIncremental {
		return icm.RestoreFromIncrementalCheckpoint(checkpoint)
	} else {
		// Full checkpoint restore
		var snapshot *GPUStateSnapshot
		if err := icm.deserializeFullCheckpoint(checkpoint, &snapshot); err != nil {
			return fmt.Errorf("failed to deserialize full checkpoint: %w", err)
		}
		return icm.serializer.RestoreGPUState(snapshot)
	}
}

// applyDeltaChange applies a single delta change to a snapshot
func (icm *IncrementalCheckpointManager) applyDeltaChange(
	snapshot *GPUStateSnapshot,
	change DeltaChange,
	deltaData []byte,
) error {
	switch change.Type {
	case DeltaChangeTensorModified:
		return icm.applyTensorDelta(snapshot, change, deltaData)
	case DeltaChangeTensorAdded:
		return icm.addTensorFromDelta(snapshot, change, deltaData)
	case DeltaChangeTensorRemoved:
		return icm.removeTensorFromSnapshot(snapshot, change)
	case DeltaChangeMemoryModified:
		return icm.applyMemoryDelta(snapshot, change, deltaData)
	case DeltaChangeStreamModified:
		return icm.applyStreamDelta(snapshot, change, deltaData)
	default:
		return fmt.Errorf("unknown delta change type: %s", change.Type)
	}
}

// applyTensorDelta applies tensor modifications from delta
func (icm *IncrementalCheckpointManager) applyTensorDelta(
	snapshot *GPUStateSnapshot,
	change DeltaChange,
	deltaData []byte,
) error {
	// Find the tensor in the snapshot
	for i, tensor := range snapshot.TensorStates {
		if tensor.ID == change.TensorID {
			// Parse delta data for this tensor
			tensorDelta, err := icm.parseTensorDelta(deltaData, change.TensorID)
			if err != nil {
				return fmt.Errorf("failed to parse tensor delta: %w", err)
			}

			// Apply delta to tensor data
			if err := icm.applyTensorDataDelta(&snapshot.TensorStates[i], tensorDelta); err != nil {
				return fmt.Errorf("failed to apply tensor data delta: %w", err)
			}

			// Update metadata
			snapshot.TensorStates[i].Checksum = change.NewHash
			return nil
		}
	}
	return fmt.Errorf("tensor %s not found in snapshot", change.TensorID)
}

// addTensorFromDelta adds a new tensor from delta data
func (icm *IncrementalCheckpointManager) addTensorFromDelta(
	snapshot *GPUStateSnapshot,
	change DeltaChange,
	deltaData []byte,
) error {
	// Parse new tensor data from delta
	newTensor, err := icm.parseNewTensorFromDelta(deltaData, change.TensorID)
	if err != nil {
		return fmt.Errorf("failed to parse new tensor from delta: %w", err)
	}

	// Add to snapshot
	snapshot.TensorStates = append(snapshot.TensorStates, *newTensor)
	return nil
}

// removeTensorFromSnapshot removes a tensor from the snapshot
func (icm *IncrementalCheckpointManager) removeTensorFromSnapshot(
	snapshot *GPUStateSnapshot,
	change DeltaChange,
) error {
	for i, tensor := range snapshot.TensorStates {
		if tensor.ID == change.TensorID {
			// Remove tensor from slice
			snapshot.TensorStates = append(snapshot.TensorStates[:i], snapshot.TensorStates[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("tensor %s not found for removal", change.TensorID)
}

// applyMemoryDelta applies memory pool modifications from delta
func (icm *IncrementalCheckpointManager) applyMemoryDelta(
	snapshot *GPUStateSnapshot,
	change DeltaChange,
	deltaData []byte,
) error {
	// Parse memory delta and apply to memory state
	memoryDelta, err := icm.parseMemoryDelta(deltaData, change.PoolID)
	if err != nil {
		return fmt.Errorf("failed to parse memory delta: %w", err)
	}

	// Apply delta to memory state
	return icm.applyMemoryStateDelta(&snapshot.MemoryState, memoryDelta)
}

// applyStreamDelta applies stream manager modifications from delta
func (icm *IncrementalCheckpointManager) applyStreamDelta(
	snapshot *GPUStateSnapshot,
	change DeltaChange,
	deltaData []byte,
) error {
	// Parse stream delta and apply to stream state
	streamDelta, err := icm.parseStreamDelta(deltaData, change.StreamID)
	if err != nil {
		return fmt.Errorf("failed to parse stream delta: %w", err)
	}

	// Apply delta to stream state
	return icm.applyStreamStateDelta(&snapshot.StreamState, streamDelta)
}

// Helper methods for delta parsing and application

func (icm *IncrementalCheckpointManager) decompressDelta(compressedData []byte) ([]byte, error) {
	// Simple decompression using gzip (can be enhanced with other algorithms)
	reader, err := gzip.NewReader(bytes.NewReader(compressedData))
	if err != nil {
		return nil, fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer reader.Close()

	var buf bytes.Buffer
	if _, err := io.Copy(&buf, reader); err != nil {
		return nil, fmt.Errorf("failed to decompress data: %w", err)
	}

	return buf.Bytes(), nil
}

func (icm *IncrementalCheckpointManager) deserializeFullCheckpoint(
	checkpoint *IncrementalCheckpoint,
	snapshot **GPUStateSnapshot,
) error {
	// Deserialize full checkpoint data
	// This is a simplified implementation - actual implementation would
	// deserialize the checkpoint data into a GPUStateSnapshot
	icm.logger.Printf("Deserializing full checkpoint for task %s, version %d",
		checkpoint.TaskID, checkpoint.Version)

	// For now, return an error indicating this needs implementation
	return fmt.Errorf("full checkpoint deserialization not yet implemented")
}

func (icm *IncrementalCheckpointManager) loadLatestCheckpoint(taskID string) (*IncrementalCheckpoint, error) {
	// Load latest checkpoint from storage
	// This is a placeholder - actual implementation would query the storage system
	icm.logger.Printf("Loading latest checkpoint for task %s", taskID)
	return nil, fmt.Errorf("checkpoint loading from storage not yet implemented")
}

func (icm *IncrementalCheckpointManager) parseTensorDelta(deltaData []byte, tensorID string) (map[string]interface{}, error) {
	// Parse tensor-specific delta data
	// This would contain block-level changes, data modifications, etc.
	var delta map[string]interface{}
	if err := json.Unmarshal(deltaData, &delta); err != nil {
		return nil, fmt.Errorf("failed to unmarshal tensor delta: %w", err)
	}
	return delta, nil
}

func (icm *IncrementalCheckpointManager) applyTensorDataDelta(tensor *TensorState, delta map[string]interface{}) error {
	// Apply delta changes to tensor data
	// This would handle block-level updates, data patches, etc.
	icm.logger.Printf("Applying tensor data delta for tensor %s", tensor.ID)

	// Simplified implementation - actual implementation would apply block-level changes
	if newData, exists := delta["data"]; exists {
		if dataBytes, ok := newData.([]byte); ok {
			tensor.Data = dataBytes
		}
	}

	return nil
}

func (icm *IncrementalCheckpointManager) parseNewTensorFromDelta(deltaData []byte, tensorID string) (*TensorState, error) {
	// Parse new tensor data from delta
	var tensorData map[string]interface{}
	if err := json.Unmarshal(deltaData, &tensorData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal new tensor data: %w", err)
	}

	// Create new tensor state from parsed data
	// This is a simplified implementation
	newTensor := &TensorState{
		ID:   tensorID,
		Name: fmt.Sprintf("tensor_%s", tensorID),
		// Other fields would be populated from deltaData
	}

	return newTensor, nil
}

func (icm *IncrementalCheckpointManager) parseMemoryDelta(deltaData []byte, poolID string) (map[string]interface{}, error) {
	// Parse memory pool delta data
	var delta map[string]interface{}
	if err := json.Unmarshal(deltaData, &delta); err != nil {
		return nil, fmt.Errorf("failed to unmarshal memory delta: %w", err)
	}
	return delta, nil
}

func (icm *IncrementalCheckpointManager) applyMemoryStateDelta(memoryState *MemoryPoolState, delta map[string]interface{}) error {
	// Apply delta changes to memory pool state
	icm.logger.Printf("Applying memory state delta for device %d", memoryState.DeviceID)

	// Simplified implementation - actual implementation would update allocations, statistics, etc.
	if newUsedSize, exists := delta["used_size"]; exists {
		if usedSize, ok := newUsedSize.(int64); ok {
			memoryState.UsedSize = usedSize
		}
	}

	return nil
}

func (icm *IncrementalCheckpointManager) parseStreamDelta(deltaData []byte, streamID string) (map[string]interface{}, error) {
	// Parse stream manager delta data
	var delta map[string]interface{}
	if err := json.Unmarshal(deltaData, &delta); err != nil {
		return nil, fmt.Errorf("failed to unmarshal stream delta: %w", err)
	}
	return delta, nil
}

func (icm *IncrementalCheckpointManager) applyStreamStateDelta(streamState *StreamManagerState, delta map[string]interface{}) error {
	// Apply delta changes to stream manager state
	icm.logger.Printf("Applying stream state delta for device %d", streamState.DeviceID)

	// Simplified implementation - actual implementation would update stream states, queues, etc.
	return nil
}

// GetRecoveryCapabilities returns information about recovery capabilities
func (icm *IncrementalCheckpointManager) GetRecoveryCapabilities(taskID string) map[string]interface{} {
	icm.mu.RLock()
	defer icm.mu.RUnlock()

	tracker, exists := icm.deltaTrackers[taskID]
	if !exists {
		return map[string]interface{}{
			"available": false,
			"reason":    "no delta tracker found",
		}
	}

	return map[string]interface{}{
		"available":             true,
		"has_full_snapshot":     tracker.LastFullSnapshot != nil,
		"incremental_count":     tracker.IncrementalCount,
		"total_checkpoints":     tracker.TotalCheckpoints,
		"last_update":           tracker.LastUpdate,
		"recovery_chain_length": len(tracker.ChangeLog),
	}
}
