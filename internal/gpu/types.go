package gpu

import (
	"fmt"
	"neuralmetergo/internal/gpu/types"
	"time"
	"unsafe"
)

// GPUType represents the type of GPU acceleration available
type GPUType string

const (
	GPUTypeCUDA   GPUType = "CUDA"
	GPUTypeMetal  GPUType = "Metal" // Apple Metal backend
	GPUTypeOpenCL GPUType = "OpenCL"
	GPUTypeROCm   GPUType = "ROCm" // AMD ROCm
	GPUTypeCPU    GPUType = "CPU"  // Fallback
)

// ComputeCapability represents CUDA compute capability
type ComputeCapability struct {
	Major int `json:"major"`
	Minor int `json:"minor"`
}

// String returns a string representation of compute capability
func (cc ComputeCapability) String() string {
	return fmt.Sprintf("%d.%d", cc.Major, cc.Minor)
}

// GPUInfo represents detailed information about a GPU device
type GPUInfo struct {
	ID                      int               `json:"id"`
	Name                    string            `json:"name"`
	Type                    GPUType           `json:"type"`
	Platform                string            `json:"platform,omitempty"`     // e.g., "Windows", "Linux", "macOS"
	Vendor                  string            `json:"vendor,omitempty"`       // e.g., "NVIDIA", "AMD", "Apple"
	Architecture            string            `json:"architecture,omitempty"` // e.g., "Turing", "Apple Silicon"
	TotalMemory             int64             `json:"total_memory"`
	FreeMemory              int64             `json:"free_memory"`
	ComputeCapability       ComputeCapability `json:"compute_capability,omitempty"`
	MultiProcessorCount     int               `json:"multiprocessor_count"`
	ClockRate               int               `json:"clock_rate"`        // kHz
	MemoryClockRate         int               `json:"memory_clock_rate"` // kHz
	MemoryBusWidth          int               `json:"memory_bus_width"`  // bits
	MaxThreadsPerBlock      int               `json:"max_threads_per_block"`
	MaxBlockDimensions      [3]int            `json:"max_block_dimensions"`
	MaxGridDimensions       [3]int            `json:"max_grid_dimensions"`
	WarpSize                int               `json:"warp_size"`
	MaxSharedMemoryPerBlock int64             `json:"max_shared_memory_per_block"`
	Available               bool              `json:"available"`

	PowerUsage  float64 `json:"power_usage,omitempty"` // Watts
	Utilization float64 `json:"utilization,omitempty"` // Percentage
}

// IsCompatible checks if the GPU meets minimum requirements
func (g *GPUInfo) IsCompatible(minMemory int64, minComputeCapability ComputeCapability) bool {
	if !g.Available {
		return false
	}

	if g.TotalMemory < minMemory {
		return false
	}

	if g.Type == GPUTypeCUDA {
		if g.ComputeCapability.Major < minComputeCapability.Major {
			return false
		}
		if g.ComputeCapability.Major == minComputeCapability.Major &&
			g.ComputeCapability.Minor < minComputeCapability.Minor {
			return false
		}
	}

	return true
}

// MemoryUtilization calculates memory utilization percentage
func (g *GPUInfo) MemoryUtilization() float64 {
	if g.TotalMemory == 0 {
		return 0
	}
	used := g.TotalMemory - g.FreeMemory
	return float64(used) / float64(g.TotalMemory) * 100.0
}

// GPUMetrics represents real-time GPU performance metrics
type GPUMetrics struct {
	DeviceID          int       `json:"device_id"`
	Timestamp         time.Time `json:"timestamp"`
	GPUUtilization    float64   `json:"gpu_utilization"`    // Percentage
	MemoryUtilization float64   `json:"memory_utilization"` // Percentage
	PowerConsumption  float64   `json:"power_consumption"`  // Watts
	ClockSpeed        int       `json:"clock_speed"`        // MHz
	MemoryClockSpeed  int       `json:"memory_clock_speed"` // MHz
	FanSpeed          int       `json:"fan_speed"`          // RPM
}

// GPUConfig represents configuration options for GPU usage
type GPUConfig struct {
	Enabled              bool              `json:"enabled" yaml:"enabled"`
	PreferCUDA           bool              `json:"prefer_cuda" yaml:"prefer_cuda"`
	MinMemoryGB          float64           `json:"min_memory_gb" yaml:"min_memory_gb"`
	MinComputeCapability ComputeCapability `json:"min_compute_capability" yaml:"min_compute_capability"`
	MaxMemoryUtilization float64           `json:"max_memory_utilization" yaml:"max_memory_utilization"`
	DeviceID             int               `json:"device_id" yaml:"device_id"` // -1 for auto-select
	MonitoringInterval   time.Duration     `json:"monitoring_interval" yaml:"monitoring_interval"`
}

// DefaultGPUConfig returns default GPU configuration
func DefaultGPUConfig() GPUConfig {
	return GPUConfig{
		Enabled:              true,
		PreferCUDA:           true,
		MinMemoryGB:          2.0,
		MinComputeCapability: ComputeCapability{Major: 3, Minor: 5},
		MaxMemoryUtilization: 90.0,
		DeviceID:             -1, // Auto-select
		MonitoringInterval:   time.Second * 5,
	}
}

// Validate checks if the GPU configuration is valid
func (c *GPUConfig) Validate() error {
	if c.MinMemoryGB < 0 {
		return fmt.Errorf("min_memory_gb must be positive")
	}

	if c.MaxMemoryUtilization < 0 || c.MaxMemoryUtilization > 100 {
		return fmt.Errorf("max_memory_utilization must be between 0 and 100")
	}

	if c.MonitoringInterval < time.Second {
		return fmt.Errorf("monitoring_interval must be at least 1 second")
	}

	return nil
}

// GPUDetector interface for different GPU detection implementations
type GPUDetector interface {
	// Detect enumerates all available GPUs
	Detect() ([]*GPUInfo, error)

	// GetInfo retrieves detailed information about a specific GPU
	GetInfo(deviceID int) (*GPUInfo, error)

	// GetMetrics retrieves real-time metrics for a specific GPU
	GetMetrics(deviceID int) (*GPUMetrics, error)

	// IsSupported checks if this detector type is supported on the system
	IsSupported() bool

	// Initialize performs any necessary initialization
	Initialize() error

	// Cleanup performs cleanup operations
	Cleanup() error
}

// GPUManager interface for managing GPU resources
type GPUManager interface {
	// GetAvailableGPUs returns all available GPUs
	GetAvailableGPUs() ([]*GPUInfo, error)

	// SelectBestGPU selects the best GPU based on configuration
	SelectBestGPU(config GPUConfig) (*GPUInfo, error)

	// StartMonitoring begins monitoring GPU metrics
	StartMonitoring(deviceID int, interval time.Duration) error

	// StopMonitoring stops monitoring GPU metrics
	StopMonitoring(deviceID int) error

	// GetCurrentMetrics returns current metrics for a GPU
	GetCurrentMetrics(deviceID int) (*GPUMetrics, error)

	// IsGPUAvailable checks if a specific GPU is available
	IsGPUAvailable(deviceID int) bool

	// GetGPUTypes returns supported GPU types on this system
	GetGPUTypes() []GPUType
}

// Error types for GPU operations
type GPUError struct {
	Type     string
	Code     int
	Message  string
	DeviceID int
}

func (e *GPUError) Error() string {
	if e.DeviceID >= 0 {
		return fmt.Sprintf("GPU error on device %d: %s (code: %d)", e.DeviceID, e.Message, e.Code)
	}
	return fmt.Sprintf("GPU error: %s (code: %d)", e.Message, e.Code)
}

// NewGPUError creates a new GPU error
func NewGPUError(errorType string, code int, message string, deviceID int) *GPUError {
	return &GPUError{
		Type:     errorType,
		Code:     code,
		Message:  message,
		DeviceID: deviceID,
	}
}

// Common GPU error types
const (
	ErrorTypeInitialization = "initialization"
	ErrorTypeDetection      = "detection"
	ErrorTypeMemory         = "memory"
	ErrorTypeCompute        = "compute"
	ErrorTypeMonitoring     = "monitoring"
	ErrorTypeConfiguration  = "configuration"
)

// ONNX Runtime types and interfaces
type ONNXProvider string

const (
	ONNXProviderCUDA     ONNXProvider = "CUDAExecutionProvider"
	ONNXProviderOpenVINO ONNXProvider = "OpenVINOExecutionProvider"
	ONNXProviderTensorRT ONNXProvider = "TensorrtExecutionProvider"
	ONNXProviderCPU      ONNXProvider = "CPUExecutionProvider"
)

// ONNXSessionConfig represents ONNX Runtime session configuration
type ONNXSessionConfig struct {
	ModelPath         string            `json:"model_path"`
	Providers         []ONNXProvider    `json:"providers"`
	DeviceID          int               `json:"device_id"`
	GraphOptLevel     int               `json:"graph_optimization_level"` // 0=DISABLE, 1=BASIC, 2=EXTENDED, 3=ALL
	InterOpNumThreads int               `json:"inter_op_num_threads"`
	IntraOpNumThreads int               `json:"intra_op_num_threads"`
	LogSeverityLevel  int               `json:"log_severity_level"` // 0=VERBOSE, 1=INFO, 2=WARNING, 3=ERROR, 4=FATAL
	LogVerbosityLevel int               `json:"log_verbosity_level"`
	EnableCPUMemArena bool              `json:"enable_cpu_mem_arena"`
	EnableMemPattern  bool              `json:"enable_mem_pattern"`
	EnableProfiling   bool              `json:"enable_profiling"`
	ProfileFilePrefix string            `json:"profile_file_prefix"`
	ProviderOptions   map[string]string `json:"provider_options"`
}

// DefaultONNXConfig returns default ONNX Runtime configuration
func DefaultONNXConfig() ONNXSessionConfig {
	return ONNXSessionConfig{
		Providers:         []ONNXProvider{ONNXProviderCUDA, ONNXProviderCPU},
		DeviceID:          0,
		GraphOptLevel:     1, // BASIC
		InterOpNumThreads: 1,
		IntraOpNumThreads: 0, // Use default
		LogSeverityLevel:  2, // WARNING
		LogVerbosityLevel: 0,
		EnableCPUMemArena: true,
		EnableMemPattern:  true,
		EnableProfiling:   false,
		ProviderOptions: map[string]string{
			"device_id":                 "0",
			"gpu_mem_limit":             "**********", // 2GB
			"arena_extend_strategy":     "kNextPowerOfTwo",
			"do_copy_in_default_stream": "1",
			"cudnn_conv_algo_search":    "EXHAUSTIVE",
			"has_user_compute_stream":   "0",
		},
	}
}

// ONNXSession represents an ONNX Runtime inference session
type ONNXSession interface {
	// Initialize creates and configures the ONNX session
	Initialize(config ONNXSessionConfig) error

	// GetInputCount returns the number of input tensors
	GetInputCount() int

	// GetOutputCount returns the number of output tensors
	GetOutputCount() int

	// GetInputName returns the name of the input at the given index
	GetInputName(index int) (string, error)

	// GetOutputName returns the name of the output at the given index
	GetOutputName(index int) (string, error)

	// GetInputShape returns the shape of the input at the given index
	GetInputShape(index int) ([]int64, error)

	// GetOutputShape returns the shape of the output at the given index
	GetOutputShape(index int) ([]int64, error)

	// Run performs inference with the given inputs
	Run(inputs map[string]interface{}) (map[string]interface{}, error)

	// RunWithNames performs inference with named inputs and outputs
	RunWithNames(inputNames []string, inputs []interface{}, outputNames []string) ([]interface{}, error)

	// GetProfilingInfo returns profiling information if enabled
	GetProfilingInfo() (string, error)

	// Cleanup releases resources
	Cleanup() error
}

// CUDAMemoryPool represents a CUDA memory pool for efficient allocation
type CUDAMemoryPool interface {
	// Allocate allocates memory of the given size
	Allocate(size int64) (CUDAMemoryPtr, error)

	// Free releases the allocated memory
	Free(ptr CUDAMemoryPtr) error

	// GetTotalAllocated returns total allocated memory
	GetTotalAllocated() int64

	// GetPeakAllocated returns peak allocated memory
	GetPeakAllocated() int64

	// GetFreeMemory returns available free memory
	GetFreeMemory() int64

	// Reset resets the memory pool
	Reset() error

	// GetStats returns memory pool statistics
	GetStats() CUDAMemoryStats
}

// CUDAMemoryPtr represents a CUDA memory pointer
type CUDAMemoryPtr unsafe.Pointer

// CUDAMemoryStats represents CUDA memory pool statistics
type CUDAMemoryStats struct {
	TotalAllocated    int64   `json:"total_allocated"`
	PeakAllocated     int64   `json:"peak_allocated"`
	CurrentAllocated  int64   `json:"current_allocated"`
	FreeMemory        int64   `json:"free_memory"`
	FragmentationPct  float64 `json:"fragmentation_percent"`
	AllocationCount   int64   `json:"allocation_count"`
	DeallocationCount int64   `json:"deallocation_count"`
}

// Enhanced GPU interface for CUDA/ONNX operations
type GPUAccelerator interface {
	GPUDetector

	// ONNX Runtime operations
	CreateONNXSession(config ONNXSessionConfig) (ONNXSession, error)
	GetSupportedONNXProviders() []ONNXProvider

	// CUDA Memory Management
	CreateMemoryPool(initialSize int64) (CUDAMemoryPool, error)
	AllocateMemory(size int64) (CUDAMemoryPtr, error)
	FreeMemory(ptr CUDAMemoryPtr) error
	CopyHostToDevice(hostPtr unsafe.Pointer, devicePtr CUDAMemoryPtr, size int64) error
	CopyDeviceToHost(devicePtr CUDAMemoryPtr, hostPtr unsafe.Pointer, size int64) error
	CopyDeviceToDevice(srcPtr, dstPtr CUDAMemoryPtr, size int64) error

	// Stream Management
	CreateStream(flags int) (types.GPUStream, error)
	GetDefaultStream() types.GPUStream

	// Event Management
	CreateEvent(flags int) (types.GPUEvent, error)

	// Performance Monitoring
	StartProfiling(deviceID int) error
	StopProfiling(deviceID int) (string, error)
	GetMemoryBandwidth(deviceID int) (float64, error)
	GetComputeThroughput(deviceID int) (float64, error)
}
