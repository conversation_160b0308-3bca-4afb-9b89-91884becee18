package types

import (
	"errors"
	"fmt"
)

// Common GPU errors
var (
	ErrContextInvalid = errors.New("GPU context is invalid")
)

// ErrorType represents different categories of GPU errors
type ErrorType string

const (
	ErrorTypeContext        ErrorType = "context"
	ErrorTypeMemory         ErrorType = "memory"
	ErrorTypeExecution      ErrorType = "execution"
	ErrorTypeDevice         ErrorType = "device"
	ErrorTypeBackend        ErrorType = "backend"
	ErrorTypeInitialization ErrorType = "initialization"
	ErrorTypeDetection      ErrorType = "detection"
	ErrorTypeConfiguration  ErrorType = "configuration"
	ErrorTypeValidation     ErrorType = "validation"
	ErrorTypeNotSupported   ErrorType = "not_supported"
	ErrorTypeTimeout        ErrorType = "timeout"
	ErrorTypeCleanup        ErrorType = "cleanup"
	ErrorTypeInternal       ErrorType = "internal"
)

// GPUError represents a GPU-specific error
type GPUError struct {
	Type      ErrorType
	Code      int
	Message   string
	DeviceID  int
	Timestamp string
}

// Error implements the error interface
func (e *GPUError) Error() string {
	if e.DeviceID >= 0 {
		return fmt.Sprintf("GPU Error [%s:%d] Device %d: %s", e.Type, e.Code, e.DeviceID, e.Message)
	}
	return fmt.Sprintf("GPU Error [%s:%d]: %s", e.Type, e.Code, e.Message)
}

// NewGPUError creates a new GPU error
func NewGPUError(errorType ErrorType, code int, message string, deviceID int) *GPUError {
	return &GPUError{
		Type:     errorType,
		Code:     code,
		Message:  message,
		DeviceID: deviceID,
	}
}
