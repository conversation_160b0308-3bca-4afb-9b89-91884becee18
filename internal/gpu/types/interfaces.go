package types

import (
	"context"
	"fmt"
	"time"
)

// GPUType represents the type of GPU backend
type GPUType string

const (
	GPUTypeCUDA   GPUType = "CUDA"
	GPUTypeMetal  GPUType = "Metal"
	GPUTypeOpenCL GPUType = "OpenCL"
	GPUTypeROCm   GPUType = "ROCm"
	GPUTypeOneAPI GPUType = "OneAPI"
)

// DeviceType represents the type of device
type DeviceType string

const (
	DeviceTypeIntegrated DeviceType = "integrated"
	DeviceTypeDiscrete   DeviceType = "discrete"
)

// MemoryType represents the type of memory
type MemoryType string

const (
	MemoryTypeDiscrete MemoryType = "discrete"
	MemoryTypeUnified  MemoryType = "unified"
	MemoryTypeShared   MemoryType = "shared"
)

// DeviceStatus represents the status of a device
type DeviceStatus string

const (
	DeviceStatusAvailable   DeviceStatus = "available"
	DeviceStatusBusy        DeviceStatus = "busy"
	DeviceStatusUnavailable DeviceStatus = "unavailable"
	DeviceStatusError       DeviceStatus = "error"
)

// ComputeCapability represents CUDA compute capability
type ComputeCapability struct {
	Major int `json:"major"`
	Minor int `json:"minor"`
}

// String returns a string representation of compute capability
func (cc ComputeCapability) String() string {
	return fmt.Sprintf("%d.%d", cc.Major, cc.Minor)
}

// ComputeInfo provides information about GPU compute capabilities
type ComputeInfo struct {
	Units         int               `json:"units"`
	ClockSpeed    int               `json:"clock_speed"` // MHz
	Capability    ComputeCapability `json:"capability"`
	MaxThreads    int               `json:"max_threads"`
	WarpSize      int               `json:"warp_size"`
	MaxWarpsPerSM int               `json:"max_warps_per_sm"`
}

// PerformanceInfo provides performance metrics
type PerformanceInfo struct {
	GFLOPS      float64 `json:"gflops"`
	MemoryGBps  float64 `json:"memory_gbps"`
	Utilization float64 `json:"utilization"`
}

// GPUDevice represents information about a GPU device
type GPUDevice struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Type         DeviceType             `json:"type"`
	Backend      string                 `json:"backend"`
	Vendor       string                 `json:"vendor"`
	Index        int                    `json:"index"`
	BusInfo      string                 `json:"bus_info"`
	MemoryTotal  uint64                 `json:"memory_total"`
	MemoryFree   uint64                 `json:"memory_free"`
	Utilization  float64                `json:"utilization"`
	PowerUsage   float64                `json:"power_usage"`
	ClockSpeed   int                    `json:"clock_speed"`
	ComputeUnits int                    `json:"compute_units"`
	IsActive     bool                   `json:"is_active"`
	Compute      ComputeInfo            `json:"compute"`
	Memory       MemoryInfo             `json:"memory"`
	Performance  PerformanceInfo        `json:"performance"`
	Capabilities map[string]interface{} `json:"capabilities"`
	Status       DeviceStatus           `json:"status"`
}

// GPUFeature represents a specific GPU feature
type GPUFeature string

const (
	FeatureCompute          GPUFeature = "compute"
	FeatureGraphics         GPUFeature = "graphics"
	FeatureRayTracing       GPUFeature = "raytracing"
	FeatureTensorOps        GPUFeature = "tensor_ops"
	FeatureFloat16          GPUFeature = "float16"
	FeatureInt8             GPUFeature = "int8"
	FeatureAsyncCompute     GPUFeature = "async_compute"
	FeatureUnifiedMemory    GPUFeature = "unified_memory"
	FeatureMemoryCoherence  GPUFeature = "memory_coherence"
	FeatureAtomicOperations GPUFeature = "atomic_operations"
	FeatureSharedMemory     GPUFeature = "shared_memory"
	FeatureHalfPrecision    GPUFeature = "half_precision"
	FeatureDoublePrecision  GPUFeature = "double_precision"
	FeatureTensorOperations GPUFeature = "tensor_operations"
)

// GPUCapability represents the capabilities of a GPU device
type GPUCapability struct {
	MaxTextureSize     [3]uint32              `json:"max_texture_size"`
	MaxBufferSize      uint64                 `json:"max_buffer_size"`
	MaxWorkGroupSize   uint32                 `json:"max_work_group_size"`
	MaxWorkGroupDim    [3]uint32              `json:"max_work_group_dim"`
	MaxComputeUnits    uint32                 `json:"max_compute_units"`
	MaxMemoryBandwidth uint64                 `json:"max_memory_bandwidth"`
	Features           map[GPUFeature]bool    `json:"features"`
	Extensions         []string               `json:"extensions"`
	Limits             map[string]interface{} `json:"limits"`
}

// GPUContext represents a GPU execution context
type GPUContext interface {
	GetDevice() *GPUDevice
	IsValid() bool
	Synchronize() error
	GetDeviceID() string
	GetBackend() string
	Destroy() error
}

// MemoryFlags represents memory allocation flags
type MemoryFlags uint32

const (
	MemoryFlagDefault  MemoryFlags = 0
	MemoryFlagReadOnly MemoryFlags = 1 << iota
	MemoryFlagWriteOnly
	MemoryFlagReadWrite
	MemoryFlagHostVisible
	MemoryFlagCoherent
	MemoryFlagCached
)

// MemoryInfo provides information about GPU memory usage
type MemoryInfo struct {
	Total     uint64     `json:"total"`
	Free      uint64     `json:"free"`
	Used      uint64     `json:"used"`
	Bandwidth uint64     `json:"bandwidth"`
	Type      MemoryType `json:"type"`
}

// GPUBuffer represents a GPU memory buffer
type GPUBuffer interface {
	GetSize() uint64
	GetFlags() MemoryFlags
	IsValid() bool
	Map() ([]byte, error)
	Unmap() error
	CopyFrom(src []byte) error
	CopyTo(dst []byte) error
	Release() error
}

// GPUMemoryManager handles GPU memory allocation
type GPUMemoryManager interface {
	Allocate(size uint64) (GPUMemory, error)
	AllocateType(size uint64, memType MemoryType) (GPUMemory, error)
	GetStats() GPUMemoryStats
	Cleanup() error
}

// GPUExecutor handles GPU kernel execution
type GPUExecutor interface {
	CreateKernel(source string, entryPoint string) (GPUKernel, error)
	CreateStream() (GPUStream, error)
	CreateEvent() (GPUEvent, error)
	Synchronize() error
	GetStreams() []GPUStream
}

// GPUKernel represents a compiled GPU kernel
type GPUKernel interface {
	GetName() string
	Launch(grid GridDimension, block GridDimension, args []interface{}, stream GPUStream) error
	GetAttributes() map[string]interface{}
	Destroy() error
}

// GPUEvent represents an asynchronous GPU operation
type GPUEvent interface {
	ID() string
	Record(stream GPUStream) error
	Wait() error
	Query() EventState
	ElapsedTime(start GPUEvent) (time.Duration, error)
	Destroy() error
}

// GPUBackend interface defines the contract for GPU backend implementations
type GPUBackend interface {
	// Backend identification
	Name() string
	Version() string
	Platform() string

	// Device management
	EnumerateDevices(ctx context.Context) ([]GPUDevice, error)
	GetDevice(deviceID string) (*GPUDevice, error)
	GetCapabilities(device *GPUDevice) (*GPUCapability, error)
	SupportsFeature(device *GPUDevice, feature GPUFeature) bool

	// Context and resource management
	CreateContext(device *GPUDevice) (GPUContext, error)
	CreateMemoryManager(ctx GPUContext) (GPUMemoryManager, error)
	CreateExecutor(ctx GPUContext) (GPUExecutor, error)
}

// StreamState represents the state of a GPU stream
type StreamState int

const (
	StreamStateUnknown StreamState = iota
	StreamStateIdle
	StreamStateReady
	StreamStateRunning
	StreamStateCompleted
	StreamStateComplete
	StreamStateError
	StreamStateDestroyed
)

// EventState represents the state of a GPU event
type EventState int

const (
	EventStateUnknown EventState = iota
	EventStatePending
	EventStateReady
	EventStateRecorded
	EventStateComplete
	EventStateError
	EventStateDestroyed
)

// GridDimension represents the dimensions for GPU kernel execution
type GridDimension struct {
	X, Y, Z uint32
}

// GPUMemoryStats provides statistics about GPU memory usage
type GPUMemoryStats struct {
	TotalAllocated uint64
	PeakAllocated  uint64
	CurrentUsed    uint64
	FreeMemory     uint64
	Fragmentation  float64
	LastUpdate     time.Time
}

// GPUMemory represents a GPU memory allocation
type GPUMemory interface {
	Ptr() uintptr
	Size() uint64
	Type() MemoryType
	Free() error
	CopyFrom(src []byte) error
	CopyTo(dst []byte) error
	CopyFromGPU(src GPUMemory) error
}

// GPUStream represents a GPU execution stream
type GPUStream interface {
	ID() string
	Submit(kernel GPUKernel, grid GridDimension, block GridDimension, args []interface{}) error
	Synchronize() error
	Query() StreamState
	Destroy() error
}
