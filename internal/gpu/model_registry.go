package gpu

import (
	"sort"
	"sync"
	"time"
)

// ModelRegistry manages model versions and performance tracking
type ModelRegistry struct {
	versions []ModelVersion
	config   ModelRegistryConfig
	mu       sync.RWMutex
}

// ModelRegistryConfig configures model registry behavior
type ModelRegistryConfig struct {
	MaxVersions          int           `json:"max_versions"`
	EvaluationPeriod     time.Duration `json:"evaluation_period"`
	PerformanceThreshold float64       `json:"performance_threshold"`
}

// ModelVersion represents a saved model version
type ModelVersion struct {
	ID          string                 `json:"id"`
	Timestamp   time.Time              `json:"timestamp"`
	Accuracy    float64                `json:"accuracy"`
	ModelType   string                 `json:"model_type"`
	Parameters  map[string]interface{} `json:"parameters"`
	Performance ModelPerformance       `json:"performance"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// Note: ModelPerformance is defined in advanced_ensemble.go

// NewModelRegistry creates a new model registry
func NewModelRegistry(config ModelRegistryConfig) *ModelRegistry {
	return &ModelRegistry{
		versions: make([]ModelVersion, 0),
		config:   config,
	}
}

// SaveVersion saves a new model version
func (mr *ModelRegistry) SaveVersion(version ModelVersion) {
	mr.mu.Lock()
	defer mr.mu.Unlock()

	// Add the new version
	mr.versions = append(mr.versions, version)

	// Sort by timestamp (newest first)
	sort.Slice(mr.versions, func(i, j int) bool {
		return mr.versions[i].Timestamp.After(mr.versions[j].Timestamp)
	})

	// Remove old versions if we exceed the limit
	if len(mr.versions) > mr.config.MaxVersions {
		mr.versions = mr.versions[:mr.config.MaxVersions]
	}
}

// GetBestVersion returns the version with the highest accuracy
func (mr *ModelRegistry) GetBestVersion() *ModelVersion {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	if len(mr.versions) == 0 {
		return nil
	}

	best := &mr.versions[0]
	for i := 1; i < len(mr.versions); i++ {
		if mr.versions[i].Accuracy > best.Accuracy {
			best = &mr.versions[i]
		}
	}

	return best
}

// GetLatestVersion returns the most recent version
func (mr *ModelRegistry) GetLatestVersion() *ModelVersion {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	if len(mr.versions) == 0 {
		return nil
	}

	return &mr.versions[0] // Already sorted by timestamp
}

// GetVersionByID returns a specific version by ID
func (mr *ModelRegistry) GetVersionByID(id string) *ModelVersion {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	for i := range mr.versions {
		if mr.versions[i].ID == id {
			return &mr.versions[i]
		}
	}

	return nil
}

// GetVersions returns all versions (sorted by timestamp, newest first)
func (mr *ModelRegistry) GetVersions() []ModelVersion {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	// Return a copy to prevent external modification
	versions := make([]ModelVersion, len(mr.versions))
	copy(versions, mr.versions)
	return versions
}

// GetVersionCount returns the number of stored versions
func (mr *ModelRegistry) GetVersionCount() int {
	mr.mu.RLock()
	defer mr.mu.RUnlock()
	return len(mr.versions)
}

// GetPerformanceHistory returns performance metrics over time
func (mr *ModelRegistry) GetPerformanceHistory() []PerformancePoint {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	points := make([]PerformancePoint, len(mr.versions))
	for i, version := range mr.versions {
		points[i] = PerformancePoint{
			Timestamp: version.Timestamp,
			Accuracy:  version.Accuracy,
			RMSE:      version.Performance.RMSE,
			MAE:       version.Performance.MAE,
			ModelType: version.ModelType,
		}
	}

	// Sort by timestamp (oldest first for time series)
	sort.Slice(points, func(i, j int) bool {
		return points[i].Timestamp.Before(points[j].Timestamp)
	})

	return points
}

// PerformancePoint represents a performance measurement at a point in time
type PerformancePoint struct {
	Timestamp time.Time `json:"timestamp"`
	Accuracy  float64   `json:"accuracy"`
	RMSE      float64   `json:"rmse"`
	MAE       float64   `json:"mae"`
	ModelType string    `json:"model_type"`
}

// GetPerformanceTrend analyzes the performance trend
func (mr *ModelRegistry) GetPerformanceTrend() PerformanceTrend {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	if len(mr.versions) < 2 {
		return PerformanceTrend{
			Direction:  TrendDirectionStable,
			Confidence: 0.0,
			Change:     0.0,
		}
	}

	// Calculate trend based on accuracy over recent versions
	recentCount := minInt(5, len(mr.versions))
	// Take the most recent versions (versions are stored newest first)
	recent := make([]ModelVersion, recentCount)
	copy(recent, mr.versions[:recentCount])

	// Sort recent versions by timestamp (oldest first) for proper trend calculation
	sort.Slice(recent, func(i, j int) bool {
		return recent[i].Timestamp.Before(recent[j].Timestamp)
	})

	// Calculate linear trend
	var sumX, sumY, sumXY, sumX2 float64
	for i, version := range recent {
		x := float64(i)
		y := version.Accuracy
		sumX += x
		sumY += y
		sumXY += x * y
		sumX2 += x * x
	}

	n := float64(len(recent))
	slope := (n*sumXY - sumX*sumY) / (n*sumX2 - sumX*sumX)

	var direction TrendDirection
	if slope > 0.01 {
		direction = TrendDirectionIncreasing
	} else if slope < -0.01 {
		direction = TrendDirectionDecreasing
	} else {
		direction = TrendDirectionStable
	}

	// Calculate confidence based on consistency of trend
	confidence := calculateTrendConfidence(recent, slope)

	return PerformanceTrend{
		Direction:      direction,
		Confidence:     confidence,
		Change:         slope,
		RecentVersions: recentCount,
	}
}

// Note: TrendDirection is defined in performance_monitor.go

// PerformanceTrend represents the overall performance trend
type PerformanceTrend struct {
	Direction      TrendDirection `json:"direction"`
	Confidence     float64        `json:"confidence"`
	Change         float64        `json:"change"`
	RecentVersions int            `json:"recent_versions"`
}

// calculateTrendConfidence calculates confidence in the trend based on consistency
func calculateTrendConfidence(versions []ModelVersion, slope float64) float64 {
	if len(versions) < 2 {
		return 0.0
	}

	// Calculate how well the trend fits the data
	var sumSquaredErrors float64
	for i, version := range versions {
		expected := versions[0].Accuracy + slope*float64(i)
		error := version.Accuracy - expected
		sumSquaredErrors += error * error
	}

	// Convert to confidence (lower error = higher confidence)
	avgError := sumSquaredErrors / float64(len(versions))
	confidence := 1.0 / (1.0 + avgError*10.0) // Scaled confidence

	return minFloat(1.0, maxFloat(0.0, confidence))
}

// Cleanup removes old versions beyond the configured limit
func (mr *ModelRegistry) Cleanup() {
	mr.mu.Lock()
	defer mr.mu.Unlock()

	if len(mr.versions) > mr.config.MaxVersions {
		mr.versions = mr.versions[:mr.config.MaxVersions]
	}
}

// GetModelComparison compares performance between different model types
func (mr *ModelRegistry) GetModelComparison() map[string]ModelTypeStats {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	stats := make(map[string]ModelTypeStats)

	for _, version := range mr.versions {
		if existing, exists := stats[version.ModelType]; exists {
			existing.Count++
			existing.TotalAccuracy += version.Accuracy
			existing.TotalRMSE += version.Performance.RMSE
			existing.TotalMAE += version.Performance.MAE

			if version.Accuracy > existing.BestAccuracy {
				existing.BestAccuracy = version.Accuracy
			}
			if version.Accuracy < existing.WorstAccuracy {
				existing.WorstAccuracy = version.Accuracy
			}

			stats[version.ModelType] = existing
		} else {
			stats[version.ModelType] = ModelTypeStats{
				Count:         1,
				TotalAccuracy: version.Accuracy,
				TotalRMSE:     version.Performance.RMSE,
				TotalMAE:      version.Performance.MAE,
				BestAccuracy:  version.Accuracy,
				WorstAccuracy: version.Accuracy,
			}
		}
	}

	// Calculate averages
	for modelType, stat := range stats {
		stat.AvgAccuracy = stat.TotalAccuracy / float64(stat.Count)
		stat.AvgRMSE = stat.TotalRMSE / float64(stat.Count)
		stat.AvgMAE = stat.TotalMAE / float64(stat.Count)
		stats[modelType] = stat
	}

	return stats
}

// ModelTypeStats contains statistics for a specific model type
type ModelTypeStats struct {
	Count         int     `json:"count"`
	AvgAccuracy   float64 `json:"avg_accuracy"`
	BestAccuracy  float64 `json:"best_accuracy"`
	WorstAccuracy float64 `json:"worst_accuracy"`
	AvgRMSE       float64 `json:"avg_rmse"`
	AvgMAE        float64 `json:"avg_mae"`
	TotalAccuracy float64 `json:"-"` // Internal use
	TotalRMSE     float64 `json:"-"` // Internal use
	TotalMAE      float64 `json:"-"` // Internal use
}

// Utility functions (minInt is defined in cluster_workload_distributor.go)
func maxFloat(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

func minFloat(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}
