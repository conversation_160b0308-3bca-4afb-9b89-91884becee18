package gpu

import (
	"context"
	"fmt"
	"log"
	"math"
	"sort"
	"sync"
	"time"
)

// ClusterWorkloadDistributor handles distribution of tasks across a GPU cluster
type ClusterWorkloadDistributor struct {
	resourceDiscovery *GPUResourceDiscovery
	localDistributor  *WorkloadDistributor
	strategy          ClusterDistributionStrategy
	logger            *log.Logger
	mu                sync.RWMutex
	config            ClusterDistributorConfig
	scheduler         *ClusterScheduler
	loadBalancer      *ClusterLoadBalancer
	faultTolerance    *ClusterFaultTolerance
}

// ClusterDistributionStrategy defines strategies for cluster-wide task distribution
type ClusterDistributionStrategy int

const (
	ClusterStrategyRoundRobin ClusterDistributionStrategy = iota
	ClusterStrategyResourceBased
	ClusterStrategyLatencyOptimized
	ClusterStrategyDataLocality
	ClusterStrategyHybrid
	ClusterStrategyAdaptive
)

func (s ClusterDistributionStrategy) String() string {
	switch s {
	case ClusterStrategyRoundRobin:
		return "cluster_round_robin"
	case ClusterStrategyResourceBased:
		return "cluster_resource_based"
	case ClusterStrategyLatencyOptimized:
		return "cluster_latency_optimized"
	case ClusterStrategyDataLocality:
		return "cluster_data_locality"
	case ClusterStrategyHybrid:
		return "cluster_hybrid"
	case ClusterStrategyAdaptive:
		return "cluster_adaptive"
	default:
		return "unknown"
	}
}

// ClusterDistributorConfig contains configuration for cluster workload distribution
type ClusterDistributorConfig struct {
	// Resource weighting factors
	CPUMemoryWeight       float64 `json:"cpu_memory_weight"`
	GPUMemoryWeight       float64 `json:"gpu_memory_weight"`
	ComputeCapacityWeight float64 `json:"compute_capacity_weight"`
	NetworkLatencyWeight  float64 `json:"network_latency_weight"`
	BandwidthWeight       float64 `json:"bandwidth_weight"`

	// Performance factors
	HistoricalPerformanceWeight float64 `json:"historical_performance_weight"`
	CurrentLoadWeight           float64 `json:"current_load_weight"`
	QueueLengthWeight           float64 `json:"queue_length_weight"`

	// Data locality factors
	DataLocalityWeight float64 `json:"data_locality_weight"`
	TransferCostWeight float64 `json:"transfer_cost_weight"`

	// Fault tolerance
	NodeReliabilityWeight float64 `json:"node_reliability_weight"`
	FailoverEnabled       bool    `json:"failover_enabled"`
	MaxFailureRetries     int     `json:"max_failure_retries"`

	// Scheduling parameters
	MaxTasksPerNode      int           `json:"max_tasks_per_node"`
	MinResourceThreshold float64       `json:"min_resource_threshold"`
	RebalancingInterval  time.Duration `json:"rebalancing_interval"`
	HealthCheckInterval  time.Duration `json:"health_check_interval"`

	// Adaptive parameters
	LearningRate          float64       `json:"learning_rate"`
	AdaptationInterval    time.Duration `json:"adaptation_interval"`
	PerformanceWindowSize int           `json:"performance_window_size"`
}

// ClusterTask represents a task to be distributed across the cluster
type ClusterTask struct {
	WorkloadTask

	// Cluster-specific fields
	DataLocation        []string             `json:"data_location"`       // Node IDs where data is available
	DataSize            int64                `json:"data_size"`           // Size of data to transfer
	AffinityNodes       []string             `json:"affinity_nodes"`      // Preferred node IDs
	AntiAffinityNodes   []string             `json:"anti_affinity_nodes"` // Nodes to avoid
	NetworkRequirements *NetworkRequirements `json:"network_requirements"`
	ReplicationFactor   int                  `json:"replication_factor"` // For fault tolerance
	MaxRetries          int                  `json:"max_retries"`
	TimeoutDuration     time.Duration        `json:"timeout_duration"`
}

// NetworkRequirements specifies network requirements for a task
type NetworkRequirements struct {
	MinBandwidth   float64 `json:"min_bandwidth_mbps"`
	MaxLatency     float64 `json:"max_latency_ms"`
	MaxPacketLoss  float64 `json:"max_packet_loss_percent"`
	RequiresSecure bool    `json:"requires_secure"`
}

// ClusterTaskAssignment represents the assignment of a task to cluster resources
type ClusterTaskAssignment struct {
	Task            *ClusterTask      `json:"task"`
	PrimaryNode     *ClusterNode      `json:"primary_node"`
	PrimaryGPU      *ClusterGPUDevice `json:"primary_gpu"`
	BackupNodes     []*ClusterNode    `json:"backup_nodes,omitempty"`
	AssignmentScore float64           `json:"assignment_score"`
	Strategy        string            `json:"strategy"`
	AssignedAt      time.Time         `json:"assigned_at"`
	EstimatedStart  time.Time         `json:"estimated_start"`
	EstimatedEnd    time.Time         `json:"estimated_end"`
	DataTransfers   []DataTransfer    `json:"data_transfers,omitempty"`
}

// DataTransfer represents a required data transfer for task execution
type DataTransfer struct {
	SourceNode    string        `json:"source_node"`
	TargetNode    string        `json:"target_node"`
	DataSize      int64         `json:"data_size"`
	EstimatedTime time.Duration `json:"estimated_time"`
	Priority      int           `json:"priority"`
}

// NodeScore represents a node's suitability for a task
type NodeScore struct {
	Node               *ClusterNode      `json:"node"`
	TotalScore         float64           `json:"total_score"`
	ResourceScore      float64           `json:"resource_score"`
	PerformanceScore   float64           `json:"performance_score"`
	NetworkScore       float64           `json:"network_score"`
	DataLocalityScore  float64           `json:"data_locality_score"`
	ReliabilityScore   float64           `json:"reliability_score"`
	Available          bool              `json:"available"`
	SelectedGPU        *ClusterGPUDevice `json:"selected_gpu,omitempty"`
	EstimatedQueueTime time.Duration     `json:"estimated_queue_time"`
	DataTransferCost   float64           `json:"data_transfer_cost"`
	Reason             string            `json:"reason,omitempty"`
}

// DefaultClusterDistributorConfig returns default configuration
func DefaultClusterDistributorConfig() ClusterDistributorConfig {
	return ClusterDistributorConfig{
		CPUMemoryWeight:             0.15,
		GPUMemoryWeight:             0.25,
		ComputeCapacityWeight:       0.20,
		NetworkLatencyWeight:        0.10,
		BandwidthWeight:             0.10,
		HistoricalPerformanceWeight: 0.10,
		CurrentLoadWeight:           0.15,
		QueueLengthWeight:           0.10,
		DataLocalityWeight:          0.20,
		TransferCostWeight:          0.15,
		NodeReliabilityWeight:       0.10,
		FailoverEnabled:             true,
		MaxFailureRetries:           3,
		MaxTasksPerNode:             10,
		MinResourceThreshold:        0.10,
		RebalancingInterval:         time.Minute * 5,
		HealthCheckInterval:         time.Second * 30,
		LearningRate:                0.01,
		AdaptationInterval:          time.Hour,
		PerformanceWindowSize:       100,
	}
}

// NewClusterWorkloadDistributor creates a new cluster workload distributor
func NewClusterWorkloadDistributor(
	resourceDiscovery *GPUResourceDiscovery,
	localDistributor *WorkloadDistributor,
	strategy ClusterDistributionStrategy,
	logger *log.Logger,
) (*ClusterWorkloadDistributor, error) {
	if logger == nil {
		logger = log.Default()
	}

	config := DefaultClusterDistributorConfig()

	cwd := &ClusterWorkloadDistributor{
		resourceDiscovery: resourceDiscovery,
		localDistributor:  localDistributor,
		strategy:          strategy,
		logger:            logger,
		config:            config,
	}

	// Initialize cluster components
	scheduler, err := NewClusterScheduler(cwd, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create cluster scheduler: %w", err)
	}
	cwd.scheduler = scheduler

	loadBalancer, err := NewClusterLoadBalancer(cwd, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create cluster load balancer: %w", err)
	}
	cwd.loadBalancer = loadBalancer

	faultTolerance, err := NewClusterFaultTolerance(cwd, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create cluster fault tolerance: %w", err)
	}
	cwd.faultTolerance = faultTolerance

	return cwd, nil
}

// AssignClusterTask assigns a task to the most suitable cluster resources
func (cwd *ClusterWorkloadDistributor) AssignClusterTask(ctx context.Context, task *ClusterTask) (*ClusterTaskAssignment, error) {
	cwd.mu.Lock()
	defer cwd.mu.Unlock()

	// Get available cluster nodes
	nodes := cwd.resourceDiscovery.GetActiveNodes()
	if len(nodes) == 0 {
		return nil, fmt.Errorf("no active cluster nodes available")
	}

	// Score all nodes for this task
	nodeScores, err := cwd.scoreNodes(ctx, task, nodes)
	if err != nil {
		return nil, fmt.Errorf("failed to score nodes: %w", err)
	}

	// Filter available nodes
	availableScores := make([]NodeScore, 0)
	for _, score := range nodeScores {
		if score.Available {
			availableScores = append(availableScores, score)
		}
	}

	if len(availableScores) == 0 {
		return nil, fmt.Errorf("no suitable nodes available for task %s", task.ID)
	}

	// Sort by score (highest first)
	sort.Slice(availableScores, func(i, j int) bool {
		return availableScores[i].TotalScore > availableScores[j].TotalScore
	})

	// Select primary node and backup nodes
	primaryScore := availableScores[0]
	var backupNodes []*ClusterNode

	if cwd.config.FailoverEnabled && len(availableScores) > 1 {
		// Select backup nodes (up to 2)
		maxBackups := minInt(2, len(availableScores)-1)
		for i := 1; i <= maxBackups; i++ {
			backupNodes = append(backupNodes, availableScores[i].Node)
		}
	}

	// Calculate data transfers if needed
	dataTransfers := cwd.calculateDataTransfers(task, primaryScore.Node)

	// Create assignment
	assignment := &ClusterTaskAssignment{
		Task:            task,
		PrimaryNode:     primaryScore.Node,
		PrimaryGPU:      primaryScore.SelectedGPU,
		BackupNodes:     backupNodes,
		AssignmentScore: primaryScore.TotalScore,
		Strategy:        cwd.strategy.String(),
		AssignedAt:      time.Now(),
		EstimatedStart:  time.Now().Add(primaryScore.EstimatedQueueTime),
		EstimatedEnd:    time.Now().Add(primaryScore.EstimatedQueueTime + task.EstimatedDuration),
		DataTransfers:   dataTransfers,
	}

	cwd.logger.Printf("Assigned cluster task %s to node %s (GPU: %s) with score %.3f",
		task.ID, primaryScore.Node.ID, primaryScore.SelectedGPU.DeviceID, primaryScore.TotalScore)

	return assignment, nil
}

// scoreNodes calculates suitability scores for all nodes
func (cwd *ClusterWorkloadDistributor) scoreNodes(ctx context.Context, task *ClusterTask, nodes []*ClusterNode) ([]NodeScore, error) {
	scores := make([]NodeScore, 0, len(nodes))

	for _, node := range nodes {
		score := cwd.scoreNode(ctx, task, node)
		scores = append(scores, score)
	}

	return scores, nil
}

// scoreNode calculates a comprehensive score for a node's suitability for a task
func (cwd *ClusterWorkloadDistributor) scoreNode(ctx context.Context, task *ClusterTask, node *ClusterNode) NodeScore {
	score := NodeScore{
		Node:      node,
		Available: false,
	}

	// Check basic availability
	if node.Status != NodeStatusActive {
		score.Reason = fmt.Sprintf("node status: %s", node.Status)
		return score
	}

	// Find best GPU on this node
	bestGPU, _ := cwd.findBestGPU(task, node.GPUDevices)
	if bestGPU == nil {
		score.Reason = "no suitable GPU available"
		return score
	}
	score.SelectedGPU = bestGPU

	// Calculate component scores
	score.ResourceScore = cwd.calculateResourceScore(task, node, bestGPU)
	score.PerformanceScore = cwd.calculatePerformanceScore(task, node)
	score.NetworkScore = cwd.calculateNetworkScore(task, node)
	score.DataLocalityScore = cwd.calculateDataLocalityScore(task, node)
	score.ReliabilityScore = cwd.calculateReliabilityScore(task, node)
	score.EstimatedQueueTime = cwd.estimateQueueTime(node)
	score.DataTransferCost = cwd.calculateDataTransferCost(task, node)

	// Apply strategy-specific weighting
	score.TotalScore = cwd.calculateWeightedScore(score)

	// Check minimum thresholds
	if score.ResourceScore < cwd.config.MinResourceThreshold {
		score.Available = false
		score.Reason = "insufficient resources"
		return score
	}

	// Check network requirements
	if task.NetworkRequirements != nil {
		if !cwd.meetsNetworkRequirements(task.NetworkRequirements, node) {
			score.Available = false
			score.Reason = "network requirements not met"
			return score
		}
	}

	// Check affinity/anti-affinity
	if cwd.violatesAffinity(task, node) {
		score.Available = false
		score.Reason = "affinity constraints violated"
		return score
	}

	score.Available = true
	return score
}

// findBestGPU finds the most suitable GPU on a node for the task
func (cwd *ClusterWorkloadDistributor) findBestGPU(task *ClusterTask, gpus []*ClusterGPUDevice) (*ClusterGPUDevice, float64) {
	var bestGPU *ClusterGPUDevice
	var bestScore float64 = -1

	for _, gpu := range gpus {
		if gpu.Status != GPUStatusAvailable {
			continue
		}

		// Check memory requirements
		if task.MemoryRequirement > gpu.AvailableMemory {
			continue
		}

		// Calculate GPU score
		memoryScore := float64(gpu.AvailableMemory) / float64(gpu.TotalMemory)
		utilizationScore := 1.0 - gpu.Utilization.ComputePercent/100.0
		capabilityScore := cwd.calculateGPUCapabilityScore(task, gpu)

		score := (memoryScore + utilizationScore + capabilityScore) / 3.0

		if score > bestScore {
			bestScore = score
			bestGPU = gpu
		}
	}

	return bestGPU, bestScore
}

// calculateResourceScore evaluates resource availability
func (cwd *ClusterWorkloadDistributor) calculateResourceScore(task *ClusterTask, node *ClusterNode, gpu *ClusterGPUDevice) float64 {
	// GPU memory score
	gpuMemoryScore := float64(gpu.AvailableMemory) / float64(gpu.TotalMemory)

	// System memory score
	systemMemoryScore := float64(node.SystemInfo.AvailableMemory) / float64(node.SystemInfo.TotalMemory)

	// CPU availability score (inverse of load average)
	cpuScore := math.Max(0, 1.0-node.SystemInfo.LoadAverage/float64(node.SystemInfo.CPUCount))

	// Weighted combination
	return cwd.config.GPUMemoryWeight*gpuMemoryScore +
		cwd.config.CPUMemoryWeight*systemMemoryScore +
		0.2*cpuScore // CPU weight is implicit
}

// calculatePerformanceScore evaluates historical and current performance
func (cwd *ClusterWorkloadDistributor) calculatePerformanceScore(task *ClusterTask, node *ClusterNode) float64 {
	if node.WorkloadMetrics == nil {
		return 0.5 // Neutral score for unknown performance
	}

	// Throughput score
	throughputScore := math.Min(1.0, node.WorkloadMetrics.ThroughputTasksPerSec/10.0) // Normalize to 10 tasks/sec max

	// Queue length score (inverse)
	queueScore := 1.0 / (1.0 + float64(node.WorkloadMetrics.QueueLength))

	// Success rate score
	totalTasks := node.WorkloadMetrics.CompletedTasks + node.WorkloadMetrics.FailedTasks
	successRate := 1.0
	if totalTasks > 0 {
		successRate = float64(node.WorkloadMetrics.CompletedTasks) / float64(totalTasks)
	}

	return cwd.config.HistoricalPerformanceWeight*throughputScore +
		cwd.config.QueueLengthWeight*queueScore +
		0.3*successRate // Success rate weight is implicit
}

// calculateNetworkScore evaluates network performance
func (cwd *ClusterWorkloadDistributor) calculateNetworkScore(task *ClusterTask, node *ClusterNode) float64 {
	if node.NetworkInfo == nil {
		return 0.5 // Neutral score for unknown network
	}

	// Bandwidth score (normalize to 1Gbps)
	bandwidthScore := math.Min(1.0, node.NetworkInfo.Bandwidth/1000.0)

	// Latency score (inverse, normalize to 100ms max)
	latencyScore := math.Max(0, 1.0-node.NetworkInfo.Latency/100.0)

	// Packet loss score (inverse)
	packetLossScore := math.Max(0, 1.0-node.NetworkInfo.PacketLoss/5.0) // 5% max acceptable

	return cwd.config.BandwidthWeight*bandwidthScore +
		cwd.config.NetworkLatencyWeight*latencyScore +
		0.1*packetLossScore // Packet loss weight is implicit
}

// calculateDataLocalityScore evaluates data locality benefits
func (cwd *ClusterWorkloadDistributor) calculateDataLocalityScore(task *ClusterTask, node *ClusterNode) float64 {
	if len(task.DataLocation) == 0 {
		return 1.0 // No data locality requirements
	}

	// Check if data is local to this node
	for _, dataNode := range task.DataLocation {
		if dataNode == node.ID {
			return 1.0 // Perfect locality
		}
	}

	// Calculate transfer cost penalty
	transferPenalty := float64(task.DataSize) / (1024 * 1024 * 1024) // GB
	return math.Max(0, 1.0-transferPenalty*0.1)                      // 10% penalty per GB
}

// calculateReliabilityScore evaluates node reliability
func (cwd *ClusterWorkloadDistributor) calculateReliabilityScore(task *ClusterTask, node *ClusterNode) float64 {
	if node.WorkloadMetrics == nil {
		return 0.8 // Conservative score for unknown reliability
	}

	// Uptime score (based on system uptime)
	uptimeHours := float64(node.SystemInfo.Uptime) / 3600.0
	uptimeScore := math.Min(1.0, uptimeHours/(24.0*30.0)) // Normalize to 30 days

	// Failure rate score
	totalTasks := node.WorkloadMetrics.CompletedTasks + node.WorkloadMetrics.FailedTasks
	failureRate := 0.0
	if totalTasks > 0 {
		failureRate = float64(node.WorkloadMetrics.FailedTasks) / float64(totalTasks)
	}
	failureScore := 1.0 - failureRate

	return 0.6*uptimeScore + 0.4*failureScore
}

// Additional helper functions would continue here...
// For brevity, I'll add key remaining functions

func (cwd *ClusterWorkloadDistributor) calculateGPUCapabilityScore(task *ClusterTask, gpu *ClusterGPUDevice) float64 {
	// Simplified capability scoring based on compute intensity
	switch task.ComputeIntensity {
	case ComputeLight:
		return 1.0 // Any GPU can handle light compute
	case ComputeModerate:
		return math.Min(1.0, float64(gpu.CoreCount)/1000.0) // Normalize to 1000 cores
	case ComputeHeavy:
		return math.Min(1.0, float64(gpu.CoreCount)/2000.0) // Normalize to 2000 cores
	case ComputeIntensive:
		return math.Min(1.0, float64(gpu.CoreCount)/4000.0) // Normalize to 4000 cores
	default:
		return 0.5
	}
}

func (cwd *ClusterWorkloadDistributor) calculateWeightedScore(score NodeScore) float64 {
	switch cwd.strategy {
	case ClusterStrategyResourceBased:
		return score.ResourceScore
	case ClusterStrategyLatencyOptimized:
		return score.NetworkScore*0.6 + score.PerformanceScore*0.4
	case ClusterStrategyDataLocality:
		return score.DataLocalityScore*0.7 + score.ResourceScore*0.3
	case ClusterStrategyHybrid:
		return (score.ResourceScore + score.PerformanceScore + score.NetworkScore +
			score.DataLocalityScore + score.ReliabilityScore) / 5.0
	default: // ClusterStrategyAdaptive
		return cwd.adaptiveWeighting(score)
	}
}

func (cwd *ClusterWorkloadDistributor) adaptiveWeighting(score NodeScore) float64 {
	// Simplified adaptive weighting - in practice this would use ML
	weights := []float64{0.25, 0.20, 0.15, 0.25, 0.15} // Resource, Perf, Network, Locality, Reliability
	scores := []float64{score.ResourceScore, score.PerformanceScore, score.NetworkScore,
		score.DataLocalityScore, score.ReliabilityScore}

	total := 0.0
	for i, weight := range weights {
		total += weight * scores[i]
	}
	return total
}

func (cwd *ClusterWorkloadDistributor) estimateQueueTime(node *ClusterNode) time.Duration {
	if node.WorkloadMetrics == nil {
		return 0
	}

	// Simple estimation: queue length * average task time
	avgTime := time.Duration(node.WorkloadMetrics.AverageTaskTime * float64(time.Second))
	return time.Duration(node.WorkloadMetrics.QueueLength) * avgTime
}

func (cwd *ClusterWorkloadDistributor) calculateDataTransfers(task *ClusterTask, targetNode *ClusterNode) []DataTransfer {
	transfers := make([]DataTransfer, 0)

	for _, sourceNodeID := range task.DataLocation {
		if sourceNodeID != targetNode.ID {
			transfer := DataTransfer{
				SourceNode:    sourceNodeID,
				TargetNode:    targetNode.ID,
				DataSize:      task.DataSize,
				EstimatedTime: cwd.estimateTransferTime(task.DataSize, sourceNodeID, targetNode.ID),
				Priority:      1,
			}
			transfers = append(transfers, transfer)
		}
	}

	return transfers
}

func (cwd *ClusterWorkloadDistributor) estimateTransferTime(dataSize int64, sourceNodeID, targetNodeID string) time.Duration {
	// Simplified estimation assuming 1Gbps network
	bandwidthBps := float64(1000 * 1024 * 1024 / 8) // 1Gbps in bytes/sec
	transferSeconds := float64(dataSize) / bandwidthBps
	return time.Duration(transferSeconds * float64(time.Second))
}

func (cwd *ClusterWorkloadDistributor) calculateDataTransferCost(task *ClusterTask, node *ClusterNode) float64 {
	if len(task.DataLocation) == 0 {
		return 0.0
	}

	// Check if data is already on this node
	for _, dataNode := range task.DataLocation {
		if dataNode == node.ID {
			return 0.0 // No transfer cost
		}
	}

	// Calculate transfer cost (simplified)
	return float64(task.DataSize) / (1024 * 1024 * 1024) // Cost per GB
}

func (cwd *ClusterWorkloadDistributor) meetsNetworkRequirements(req *NetworkRequirements, node *ClusterNode) bool {
	if node.NetworkInfo == nil {
		return false
	}

	return node.NetworkInfo.Bandwidth >= req.MinBandwidth &&
		node.NetworkInfo.Latency <= req.MaxLatency &&
		node.NetworkInfo.PacketLoss <= req.MaxPacketLoss
}

func (cwd *ClusterWorkloadDistributor) violatesAffinity(task *ClusterTask, node *ClusterNode) bool {
	// Check anti-affinity
	for _, antiAffinityNode := range task.AntiAffinityNodes {
		if antiAffinityNode == node.ID {
			return true
		}
	}

	// Check affinity (if specified, node must be in the list)
	if len(task.AffinityNodes) > 0 {
		for _, affinityNode := range task.AffinityNodes {
			if affinityNode == node.ID {
				return false // Found in affinity list
			}
		}
		return true // Not in affinity list
	}

	return false
}

// Utility function
func minInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// GetStrategy returns the current distribution strategy
func (cwd *ClusterWorkloadDistributor) GetStrategy() ClusterDistributionStrategy {
	cwd.mu.RLock()
	defer cwd.mu.RUnlock()
	return cwd.strategy
}

// SetStrategy updates the distribution strategy
func (cwd *ClusterWorkloadDistributor) SetStrategy(strategy ClusterDistributionStrategy) {
	cwd.mu.Lock()
	defer cwd.mu.Unlock()
	cwd.strategy = strategy
	cwd.logger.Printf("Updated cluster distribution strategy to: %s", strategy.String())
}

// GetConfig returns the current configuration
func (cwd *ClusterWorkloadDistributor) GetConfig() ClusterDistributorConfig {
	cwd.mu.RLock()
	defer cwd.mu.RUnlock()
	return cwd.config
}

// SetConfig updates the configuration
func (cwd *ClusterWorkloadDistributor) SetConfig(config ClusterDistributorConfig) error {
	if err := validateClusterDistributorConfig(config); err != nil {
		return fmt.Errorf("invalid configuration: %w", err)
	}

	cwd.mu.Lock()
	defer cwd.mu.Unlock()
	cwd.config = config
	cwd.logger.Printf("Updated cluster distributor configuration")

	return nil
}

func validateClusterDistributorConfig(config ClusterDistributorConfig) error {
	if config.MaxTasksPerNode <= 0 {
		return fmt.Errorf("max_tasks_per_node must be positive")
	}
	if config.MinResourceThreshold < 0 || config.MinResourceThreshold > 1 {
		return fmt.Errorf("min_resource_threshold must be between 0 and 1")
	}
	if config.LearningRate < 0 || config.LearningRate > 1 {
		return fmt.Errorf("learning_rate must be between 0 and 1")
	}
	return nil
}
