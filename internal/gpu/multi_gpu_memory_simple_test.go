//go:build legacytest && never
// +build legacytest,never

package gpu

import (
	"context"
	"fmt"
	"log"
	"sync"
	"testing"
	"time"
)

func TestMultiGPUMemoryManager_BasicCreation(t *testing.T) {
	// Create mock devices
	devices := createMockManagedDevicesForMultiGPU(2)

	config := MultiGPUMemoryConfig{
		PartitioningStrategy:    PartitionRoundRobin,
		EnableCaching:           false,
		EnableMigration:         false,
		EnablePressureBalancing: false,
		MaxConcurrentTransfers:  2,
		TransferOptimization:    false,
		LocalityOptimization:    false,
		ReplicationLevel:        1,
		MonitoringInterval:      0, // Disable monitoring for test
	}

	logger := log.Default()
	mgr, err := NewMultiGPUMemoryManager(devices, config, logger)
	if err != nil {
		t.Fatalf("Failed to create multi-GPU memory manager: %v", err)
	}

	if mgr == nil {
		t.Fatal("Memory manager is nil")
	}

	if len(mgr.devices) != 2 {
		t.<PERSON><PERSON><PERSON>("Expected 2 devices, got %d", len(mgr.devices))
	}

	if mgr.partitioningStrategy != PartitionRoundRobin {
		t.Errorf("Expected strategy %v, got %v", PartitionRoundRobin, mgr.partitioningStrategy)
	}
}

func TestMultiGPUMemoryManager_Initialize(t *testing.T) {
	devices := createMockManagedDevicesForMultiGPU(2)

	config := MultiGPUMemoryConfig{
		PartitioningStrategy:    PartitionRoundRobin,
		EnableCaching:           false,
		EnableMigration:         false,
		EnablePressureBalancing: false,
		MonitoringInterval:      0,
	}

	logger := log.Default()
	mgr, err := NewMultiGPUMemoryManager(devices, config, logger)
	if err != nil {
		t.Fatalf("Failed to create multi-GPU memory manager: %v", err)
	}

	// Initialize the manager
	ctx := context.Background()
	err = mgr.Initialize(ctx)
	if err != nil {
		t.Fatalf("Failed to initialize multi-GPU memory manager: %v", err)
	}

	if !mgr.isInitialized {
		t.Error("Manager should be initialized")
	}

	// Test double initialization should fail
	err = mgr.Initialize(ctx)
	if err == nil {
		t.Error("Expected error on double initialization")
	}

	// Clean up
	err = mgr.Shutdown()
	if err != nil {
		t.Errorf("Failed to shutdown manager: %v", err)
	}
}

func TestMultiGPUMemoryManager_SegmentCalculation(t *testing.T) {
	devices := createMockManagedDevicesForMultiGPU(3)

	config := MultiGPUMemoryConfig{
		PartitioningStrategy: PartitionRoundRobin,
		MonitoringInterval:   0,
	}

	logger := log.Default()
	mgr, err := NewMultiGPUMemoryManager(devices, config, logger)
	if err != nil {
		t.Fatalf("Failed to create multi-GPU memory manager: %v", err)
	}

	ctx := context.Background()
	err = mgr.Initialize(ctx)
	if err != nil {
		t.Fatalf("Failed to initialize multi-GPU memory manager: %v", err)
	}
	defer mgr.Shutdown()

	// Test round-robin segment calculation
	size := int64(1024 * 1024 * 15) // 15MB for 3 devices
	segments, err := mgr.calculateSegments(size, PartitionRoundRobin)
	if err != nil {
		t.Fatalf("Failed to calculate segments: %v", err)
	}

	if len(segments) != 3 {
		t.Errorf("Expected 3 segments, got %d", len(segments))
	}

	totalSize := int64(0)
	for _, segment := range segments {
		totalSize += segment.Size
		if segment.Size <= 0 {
			t.Errorf("Invalid segment size: %d", segment.Size)
		}
	}

	if totalSize != size {
		t.Errorf("Total segment size %d doesn't match requested size %d", totalSize, size)
	}
}

func TestDataPartitioningStrategy_String(t *testing.T) {
	strategies := []struct {
		strategy DataPartitioningStrategy
		expected string
	}{
		{PartitionRoundRobin, "round_robin"},
		{PartitionBySize, "by_size"},
		{PartitionByWorkload, "by_workload"},
		{PartitionAdaptive, "adaptive"},
	}

	for _, test := range strategies {
		result := test.strategy.String()
		if result != test.expected {
			t.Errorf("Expected %s, got %s", test.expected, result)
		}
	}
}

func TestMultiGPUMemoryManager_Stats(t *testing.T) {
	devices := createMockManagedDevicesForMultiGPU(2)

	config := MultiGPUMemoryConfig{
		PartitioningStrategy: PartitionRoundRobin,
		MonitoringInterval:   0,
	}

	logger := log.Default()
	mgr, err := NewMultiGPUMemoryManager(devices, config, logger)
	if err != nil {
		t.Fatalf("Failed to create multi-GPU memory manager: %v", err)
	}

	ctx := context.Background()
	err = mgr.Initialize(ctx)
	if err != nil {
		t.Fatalf("Failed to initialize multi-GPU memory manager: %v", err)
	}
	defer mgr.Shutdown()

	// Get initial stats
	stats := mgr.GetStats()
	if stats.ActiveAllocations != 0 {
		t.Errorf("Expected 0 active allocations, got %d", stats.ActiveAllocations)
	}

	if stats.TotalAllocatedMemory != 0 {
		t.Errorf("Expected 0 total allocated memory, got %d", stats.TotalAllocatedMemory)
	}

	if len(stats.DeviceMemoryUtilization) != len(devices) {
		t.Errorf("Expected utilization for %d devices, got %d", len(devices), len(stats.DeviceMemoryUtilization))
	}
}

func TestMultiGPUMemoryManager_ErrorHandling(t *testing.T) {
	// Test with no devices
	config := MultiGPUMemoryConfig{}
	logger := log.Default()

	_, err := NewMultiGPUMemoryManager([]*ManagedDevice{}, config, logger)
	if err == nil {
		t.Error("Expected error when creating manager with no devices")
	}

	// Test allocation before initialization
	devices := createMockManagedDevicesForMultiGPU(1)
	mgr, _ := NewMultiGPUMemoryManager(devices, config, logger)

	_, err = mgr.AllocateDistributed(1024, PartitionRoundRobin)
	if err == nil {
		t.Error("Expected error when allocating before initialization")
	}
}

// Helper function to create mock managed devices for multi-GPU memory testing
func createMockManagedDevicesForMultiGPU(count int) []*ManagedDevice {
	devices := make([]*ManagedDevice, count)

	for i := 0; i < count; i++ {
		// Use GPUDevice from abstraction.go
		device := &GPUDevice{
			ID:           fmt.Sprintf("mock-device-%d", i),
			Name:         fmt.Sprintf("Mock GPU %d", i),
			Vendor:       "MockVendor",
			Architecture: "MockArch",
			Memory: GPUMemoryInfo{
				Total:     1024 * 1024 * 1024, // 1GB
				Free:      512 * 1024 * 1024,  // 512MB free
				Used:      512 * 1024 * 1024,  // 512MB used
				Type:      MemoryTypeUnified,
				Bandwidth: 500,
				BusWidth:  256,
			},
			Compute: GPUComputeInfo{
				Units:       2048,
				ClockSpeed:  1500,
				BoostClock:  1700,
				Performance: 5000.0,
				Precision:   "FP32",
			},
			Backend:   "mock",
			Available: true,
		}

		// Mock memory manager
		memoryMgr := &MockGPUMemoryManager{
			deviceID:    i,
			allocations: make(map[uintptr]*MockGPUMemory),
			nextPtr:     uintptr(1000 * (i + 1)), // Unique ptr space per device
			stats: GPUMemoryStats{
				TotalAllocated: 0,
				PeakAllocated:  0,
				CurrentUsed:    512 * 1024 * 1024, // 512MB used
				FreeMemory:     512 * 1024 * 1024, // 512MB free
				Fragmentation:  0.0,
				LastUpdate:     time.Now(),
			},
		}

		// Mock context
		context := &MockMultiGPUContext{
			device: device,
			valid:  true,
		}

		// Mock executor
		executor := &MockMultiGPUExecutor{
			device: device,
		}

		// Mock metrics
		metrics := &DeviceMetrics{
			ID:                 device.ID,
			MemoryUtilization:  0.5,
			ComputeUtilization: 0.3,

			PowerConsumption: 150.0,
			TasksCompleted:   100,
			AverageTaskTime:  time.Millisecond * 50,
			ErrorCount:       0,
			LastUpdate:       time.Now(),
			ThroughputMBps:   250.0,
		}

		managedDevice := &ManagedDevice{
			Device:    device,
			Context:   context,
			MemoryMgr: memoryMgr,
			Executor:  executor,
			Metrics:   metrics,
			LoadLevel: 0.3,
			LastUsed:  time.Now(),
			IsActive:  true,
		}

		devices[i] = managedDevice
	}

	return devices
}

// Mock implementations for testing

type MockGPUMemoryManager struct {
	deviceID    int
	allocations map[uintptr]*MockGPUMemory
	nextPtr     uintptr
	stats       GPUMemoryStats
	mu          sync.RWMutex
}

func (m *MockGPUMemoryManager) Allocate(size uint64) (GPUMemory, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if size > m.stats.FreeMemory {
		return nil, fmt.Errorf("insufficient memory: requested %d, available %d", size, m.stats.FreeMemory)
	}

	m.nextPtr++
	ptr := m.nextPtr

	gpuMem := &MockGPUMemory{
		ptr:     ptr,
		size:    size,
		memType: MemoryTypeUnified,
	}

	m.allocations[ptr] = gpuMem
	m.stats.CurrentUsed += size
	m.stats.FreeMemory -= size
	m.stats.TotalAllocated += size
	if m.stats.CurrentUsed > m.stats.PeakAllocated {
		m.stats.PeakAllocated = m.stats.CurrentUsed
	}

	return gpuMem, nil
}

func (m *MockGPUMemoryManager) AllocateType(size uint64, memType MemoryType) (GPUMemory, error) {
	return m.Allocate(size)
}

func (m *MockGPUMemoryManager) GetStats() GPUMemoryStats {
	m.mu.RLock()
	defer m.mu.RUnlock()

	m.stats.LastUpdate = time.Now()
	return m.stats
}

func (m *MockGPUMemoryManager) Cleanup() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// Free all allocations
	for ptr := range m.allocations {
		delete(m.allocations, ptr)
	}

	m.stats.CurrentUsed = 0
	m.stats.FreeMemory = 1024 * 1024 * 1024 // Reset to 1GB

	return nil
}

type MockGPUMemory struct {
	ptr     uintptr
	size    uint64
	memType MemoryType
	freed   bool
}

func (m *MockGPUMemory) Ptr() uintptr {
	return m.ptr
}

func (m *MockGPUMemory) Size() uint64 {
	return m.size
}

func (m *MockGPUMemory) Type() MemoryType {
	return m.memType
}

func (m *MockGPUMemory) Free() error {
	if m.freed {
		return fmt.Errorf("memory already freed")
	}
	m.freed = true
	return nil
}

func (m *MockGPUMemory) CopyFrom(src []byte) error {
	if m.freed {
		return fmt.Errorf("memory already freed")
	}
	return nil
}

func (m *MockGPUMemory) CopyTo(dst []byte) error {
	if m.freed {
		return fmt.Errorf("memory already freed")
	}
	return nil
}

func (m *MockGPUMemory) CopyFromGPU(src GPUMemory) error {
	if m.freed {
		return fmt.Errorf("memory already freed")
	}
	return nil
}

type MockMultiGPUContext struct {
	device *GPUDevice
	valid  bool
}

func (c *MockMultiGPUContext) Device() *GPUDevice {
	return c.device
}

func (c *MockMultiGPUContext) IsValid() bool {
	return c.valid
}

func (c *MockMultiGPUContext) Synchronize() error {
	return nil
}

func (c *MockMultiGPUContext) Destroy() error {
	c.valid = false
	return nil
}

type MockMultiGPUExecutor struct {
	device *GPUDevice
}

func (e *MockMultiGPUExecutor) CreateKernel(source string, entryPoint string) (GPUKernel, error) {
	return nil, fmt.Errorf("mock executor does not support kernel creation")
}

func (e *MockMultiGPUExecutor) CreateStream() (GPUStream, error) {
	return nil, fmt.Errorf("mock executor does not support stream creation")
}

func (e *MockMultiGPUExecutor) CreateEvent() (GPUEvent, error) {
	return nil, fmt.Errorf("mock executor does not support event creation")
}

func (e *MockMultiGPUExecutor) Synchronize() error {
	return nil
}

func (e *MockMultiGPUExecutor) GetStreams() []GPUStream {
	return []GPUStream{}
}
