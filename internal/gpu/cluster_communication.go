//go:build !windows

package gpu

import (
	"context"
	"crypto/tls"
	"fmt"
	"log"
	"net"
	"sync"
	"time"

	"github.com/pebbe/zmq4"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/keepalive"

	clusterpb "neuralmetergo/internal/gpu/proto"
)

// CommunicationConfig holds configuration for the communication system
type CommunicationConfig struct {
	// gRPC Configuration
	GRPCPort              int           `json:"grpc_port"`
	GRPCMaxMessageSize    int           `json:"grpc_max_message_size"`
	GRPCKeepaliveTime     time.Duration `json:"grpc_keepalive_time"`
	GRPCKeepaliveTimeout  time.Duration `json:"grpc_keepalive_timeout"`
	GRPCConnectionTimeout time.Duration `json:"grpc_connection_timeout"`

	// ZeroMQ Configuration
	ZMQPubPort   int    `json:"zmq_pub_port"`
	ZMQSubPort   int    `json:"zmq_sub_port"`
	ZMQReqPort   int    `json:"zmq_req_port"`
	ZMQRepPort   int    `json:"zmq_rep_port"`
	ZMQTransport string `json:"zmq_transport"` // tcp, inproc, ipc

	// Security Configuration
	TLSEnabled bool   `json:"tls_enabled"`
	CertFile   string `json:"cert_file"`
	KeyFile    string `json:"key_file"`
	CAFile     string `json:"ca_file"`
	ServerName string `json:"server_name"`

	// Performance Configuration
	MaxConcurrentStreams uint32        `json:"max_concurrent_streams"`
	BufferSize           int           `json:"buffer_size"`
	RetryAttempts        int           `json:"retry_attempts"`
	RetryDelay           time.Duration `json:"retry_delay"`

	// Node Information
	NodeID   string `json:"node_id"`
	NodeAddr string `json:"node_addr"`
}

// DefaultCommunicationConfig returns a default configuration
func DefaultCommunicationConfig() *CommunicationConfig {
	return &CommunicationConfig{
		GRPCPort:              50051,
		GRPCMaxMessageSize:    1024 * 1024 * 100, // 100MB
		GRPCKeepaliveTime:     30 * time.Second,
		GRPCKeepaliveTimeout:  5 * time.Second,
		GRPCConnectionTimeout: 10 * time.Second,

		ZMQPubPort:   5555,
		ZMQSubPort:   5556,
		ZMQReqPort:   5557,
		ZMQRepPort:   5558,
		ZMQTransport: "tcp",

		TLSEnabled: false,

		MaxConcurrentStreams: 1000,
		BufferSize:           8192,
		RetryAttempts:        3,
		RetryDelay:           time.Second,

		NodeID:   "node-" + fmt.Sprintf("%d", time.Now().Unix()),
		NodeAddr: "localhost",
	}
}

// ClusterCommunicationManager manages inter-node communication
type ClusterCommunicationManager struct {
	config *CommunicationConfig
	logger *log.Logger

	// gRPC components
	grpcServer   *grpc.Server
	grpcClients  map[string]clusterpb.GPUClusterServiceClient
	clientsMutex sync.RWMutex

	// ZeroMQ components
	zmqContext *zmq4.Context
	publisher  *zmq4.Socket
	subscriber *zmq4.Socket
	reqSocket  *zmq4.Socket
	repSocket  *zmq4.Socket

	// Communication channels
	messageHandlers map[string]MessageHandler
	handlersMutex   sync.RWMutex

	// State management
	isRunning    bool
	shutdownChan chan struct{}
	wg           sync.WaitGroup

	// Service implementation
	serviceImpl *ClusterServiceImpl
}

// MessageHandler defines the interface for handling different message types
type MessageHandler interface {
	HandleMessage(ctx context.Context, message *ZMQMessage) error
}

// ZMQMessage represents a ZeroMQ message structure
type ZMQMessage struct {
	Type      string                 `json:"type"`
	Source    string                 `json:"source"`
	Target    string                 `json:"target"`
	Timestamp time.Time              `json:"timestamp"`
	Data      map[string]interface{} `json:"data"`
}

// NewClusterCommunicationManager creates a new communication manager
func NewClusterCommunicationManager(config *CommunicationConfig, logger *log.Logger) (*ClusterCommunicationManager, error) {
	if config == nil {
		config = DefaultCommunicationConfig()
	}

	if logger == nil {
		logger = log.Default()
	}

	// Create ZeroMQ context
	zmqContext, err := zmq4.NewContext()
	if err != nil {
		return nil, fmt.Errorf("failed to create ZeroMQ context: %w", err)
	}

	ccm := &ClusterCommunicationManager{
		config:          config,
		logger:          logger,
		grpcClients:     make(map[string]clusterpb.GPUClusterServiceClient),
		zmqContext:      zmqContext,
		messageHandlers: make(map[string]MessageHandler),
		shutdownChan:    make(chan struct{}),
	}

	// Create service implementation
	ccm.serviceImpl = NewClusterServiceImpl(ccm, logger)

	return ccm, nil
}

// Start initializes and starts the communication manager
func (ccm *ClusterCommunicationManager) Start(ctx context.Context) error {
	ccm.logger.Printf("Starting Cluster Communication Manager on node %s", ccm.config.NodeID)

	// Start gRPC server
	if err := ccm.startGRPCServer(); err != nil {
		return fmt.Errorf("failed to start gRPC server: %w", err)
	}

	// Start ZeroMQ components
	if err := ccm.startZeroMQ(); err != nil {
		return fmt.Errorf("failed to start ZeroMQ: %w", err)
	}

	ccm.isRunning = true
	ccm.logger.Printf("Cluster Communication Manager started successfully")

	return nil
}

// Stop gracefully shuts down the communication manager
func (ccm *ClusterCommunicationManager) Stop() error {
	ccm.logger.Printf("Stopping Cluster Communication Manager")

	ccm.isRunning = false
	close(ccm.shutdownChan)

	// Stop gRPC server
	if ccm.grpcServer != nil {
		ccm.grpcServer.GracefulStop()
	}

	// Close gRPC clients
	ccm.clientsMutex.Lock()
	for nodeID, client := range ccm.grpcClients {
		if conn, ok := client.(interface{ GetConnection() *grpc.ClientConn }); ok {
			conn.GetConnection().Close()
		}
		delete(ccm.grpcClients, nodeID)
	}
	ccm.clientsMutex.Unlock()

	// Close ZeroMQ sockets
	ccm.closeZeroMQSockets()

	// Wait for all goroutines to finish
	ccm.wg.Wait()

	ccm.logger.Printf("Cluster Communication Manager stopped")
	return nil
}

// startGRPCServer initializes and starts the gRPC server
func (ccm *ClusterCommunicationManager) startGRPCServer() error {
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", ccm.config.GRPCPort))
	if err != nil {
		return fmt.Errorf("failed to listen on port %d: %w", ccm.config.GRPCPort, err)
	}

	// Configure gRPC server options
	opts := []grpc.ServerOption{
		grpc.MaxRecvMsgSize(ccm.config.GRPCMaxMessageSize),
		grpc.MaxSendMsgSize(ccm.config.GRPCMaxMessageSize),
		grpc.MaxConcurrentStreams(ccm.config.MaxConcurrentStreams),
		grpc.KeepaliveParams(keepalive.ServerParameters{
			Time:    ccm.config.GRPCKeepaliveTime,
			Timeout: ccm.config.GRPCKeepaliveTimeout,
		}),
	}

	// Add TLS if enabled
	if ccm.config.TLSEnabled {
		creds, err := ccm.createTLSCredentials()
		if err != nil {
			return fmt.Errorf("failed to create TLS credentials: %w", err)
		}
		opts = append(opts, grpc.Creds(creds))
	}

	ccm.grpcServer = grpc.NewServer(opts...)

	// Register service
	clusterpb.RegisterGPUClusterServiceServer(ccm.grpcServer, ccm.serviceImpl)

	// Start server in a goroutine
	ccm.wg.Add(1)
	go func() {
		defer ccm.wg.Done()
		ccm.logger.Printf("gRPC server listening on port %d", ccm.config.GRPCPort)
		if err := ccm.grpcServer.Serve(lis); err != nil {
			ccm.logger.Printf("gRPC server error: %v", err)
		}
	}()

	return nil
}

// startZeroMQ initializes ZeroMQ sockets and starts message processing
func (ccm *ClusterCommunicationManager) startZeroMQ() error {
	var err error

	// Create publisher socket
	ccm.publisher, err = ccm.zmqContext.NewSocket(zmq4.PUB)
	if err != nil {
		return fmt.Errorf("failed to create publisher socket: %w", err)
	}

	pubAddr := fmt.Sprintf("%s://*:%d", ccm.config.ZMQTransport, ccm.config.ZMQPubPort)
	if err := ccm.publisher.Bind(pubAddr); err != nil {
		return fmt.Errorf("failed to bind publisher to %s: %w", pubAddr, err)
	}

	// Create subscriber socket
	ccm.subscriber, err = ccm.zmqContext.NewSocket(zmq4.SUB)
	if err != nil {
		return fmt.Errorf("failed to create subscriber socket: %w", err)
	}

	// Subscribe to all messages initially
	if err := ccm.subscriber.SetSubscribe(""); err != nil {
		return fmt.Errorf("failed to set subscription: %w", err)
	}

	// Create request socket
	ccm.reqSocket, err = ccm.zmqContext.NewSocket(zmq4.REQ)
	if err != nil {
		return fmt.Errorf("failed to create request socket: %w", err)
	}

	// Create reply socket
	ccm.repSocket, err = ccm.zmqContext.NewSocket(zmq4.REP)
	if err != nil {
		return fmt.Errorf("failed to create reply socket: %w", err)
	}

	repAddr := fmt.Sprintf("%s://*:%d", ccm.config.ZMQTransport, ccm.config.ZMQRepPort)
	if err := ccm.repSocket.Bind(repAddr); err != nil {
		return fmt.Errorf("failed to bind reply socket to %s: %w", repAddr, err)
	}

	// Start message processing goroutines
	ccm.wg.Add(2)
	go ccm.processSubscriberMessages()
	go ccm.processReplyMessages()

	ccm.logger.Printf("ZeroMQ components started - Pub: %d, Rep: %d",
		ccm.config.ZMQPubPort, ccm.config.ZMQRepPort)

	return nil
}

// createTLSCredentials creates TLS credentials for gRPC
func (ccm *ClusterCommunicationManager) createTLSCredentials() (credentials.TransportCredentials, error) {
	if ccm.config.CertFile == "" || ccm.config.KeyFile == "" {
		// Use insecure credentials for development
		return credentials.NewTLS(&tls.Config{InsecureSkipVerify: true}), nil
	}

	cert, err := tls.LoadX509KeyPair(ccm.config.CertFile, ccm.config.KeyFile)
	if err != nil {
		return nil, fmt.Errorf("failed to load key pair: %w", err)
	}

	tlsConfig := &tls.Config{
		Certificates: []tls.Certificate{cert},
		ServerName:   ccm.config.ServerName,
	}

	return credentials.NewTLS(tlsConfig), nil
}

// GetGRPCClient returns or creates a gRPC client for the specified node
func (ccm *ClusterCommunicationManager) GetGRPCClient(nodeAddr string) (clusterpb.GPUClusterServiceClient, error) {
	ccm.clientsMutex.RLock()
	if client, exists := ccm.grpcClients[nodeAddr]; exists {
		ccm.clientsMutex.RUnlock()
		return client, nil
	}
	ccm.clientsMutex.RUnlock()

	ccm.clientsMutex.Lock()
	defer ccm.clientsMutex.Unlock()

	// Double-check after acquiring write lock
	if client, exists := ccm.grpcClients[nodeAddr]; exists {
		return client, nil
	}

	// Create new client connection
	opts := []grpc.DialOption{
		grpc.WithBlock(),
		grpc.WithTimeout(ccm.config.GRPCConnectionTimeout),
		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:                ccm.config.GRPCKeepaliveTime,
			Timeout:             ccm.config.GRPCKeepaliveTimeout,
			PermitWithoutStream: true,
		}),
	}

	if ccm.config.TLSEnabled {
		creds, err := ccm.createTLSCredentials()
		if err != nil {
			return nil, fmt.Errorf("failed to create TLS credentials: %w", err)
		}
		opts = append(opts, grpc.WithTransportCredentials(creds))
	} else {
		opts = append(opts, grpc.WithInsecure())
	}

	conn, err := grpc.Dial(nodeAddr, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %w", nodeAddr, err)
	}

	client := clusterpb.NewGPUClusterServiceClient(conn)
	ccm.grpcClients[nodeAddr] = client

	ccm.logger.Printf("Created gRPC client for node %s", nodeAddr)
	return client, nil
}

// PublishMessage publishes a message via ZeroMQ
func (ccm *ClusterCommunicationManager) PublishMessage(topic string, message *ZMQMessage) error {
	if ccm.publisher == nil {
		return fmt.Errorf("publisher not initialized")
	}

	message.Source = ccm.config.NodeID
	message.Timestamp = time.Now()

	// Serialize message (simplified - in production, use proper serialization)
	data := fmt.Sprintf("%s %s %s %v", topic, message.Type, message.Source, message.Data)

	_, err := ccm.publisher.Send(data, 0)
	if err != nil {
		return fmt.Errorf("failed to publish message: %w", err)
	}

	return nil
}

// SubscribeToNode connects to a node's publisher
func (ccm *ClusterCommunicationManager) SubscribeToNode(nodeAddr string) error {
	if ccm.subscriber == nil {
		return fmt.Errorf("subscriber not initialized")
	}

	subAddr := fmt.Sprintf("%s://%s:%d", ccm.config.ZMQTransport, nodeAddr, ccm.config.ZMQSubPort)
	if err := ccm.subscriber.Connect(subAddr); err != nil {
		return fmt.Errorf("failed to connect subscriber to %s: %w", subAddr, err)
	}

	ccm.logger.Printf("Subscribed to node %s", nodeAddr)
	return nil
}

// RegisterMessageHandler registers a handler for a specific message type
func (ccm *ClusterCommunicationManager) RegisterMessageHandler(messageType string, handler MessageHandler) {
	ccm.handlersMutex.Lock()
	defer ccm.handlersMutex.Unlock()

	ccm.messageHandlers[messageType] = handler
	ccm.logger.Printf("Registered message handler for type: %s", messageType)
}

// processSubscriberMessages processes incoming ZeroMQ messages
func (ccm *ClusterCommunicationManager) processSubscriberMessages() {
	defer ccm.wg.Done()

	for {
		select {
		case <-ccm.shutdownChan:
			return
		default:
			if ccm.subscriber == nil {
				time.Sleep(100 * time.Millisecond)
				continue
			}

			// Set receive timeout
			ccm.subscriber.SetRcvtimeo(100 * time.Millisecond)

			msg, err := ccm.subscriber.Recv(0)
			if err != nil {
				if err.Error() == "resource temporarily unavailable" {
					continue // Timeout, continue loop
				}
				ccm.logger.Printf("Error receiving ZMQ message: %v", err)
				continue
			}

			// Process message
			ccm.handleZMQMessage(msg)
		}
	}
}

// processReplyMessages processes incoming request-reply messages
func (ccm *ClusterCommunicationManager) processReplyMessages() {
	defer ccm.wg.Done()

	for {
		select {
		case <-ccm.shutdownChan:
			return
		default:
			if ccm.repSocket == nil {
				time.Sleep(100 * time.Millisecond)
				continue
			}

			// Set receive timeout
			ccm.repSocket.SetRcvtimeo(100 * time.Millisecond)

			msg, err := ccm.repSocket.Recv(0)
			if err != nil {
				if err.Error() == "resource temporarily unavailable" {
					continue // Timeout, continue loop
				}
				ccm.logger.Printf("Error receiving ZMQ request: %v", err)
				continue
			}

			// Process request and send reply
			reply := ccm.handleZMQRequest(msg)
			ccm.repSocket.Send(reply, 0)
		}
	}
}

// handleZMQMessage handles incoming ZeroMQ messages
func (ccm *ClusterCommunicationManager) handleZMQMessage(msg string) {
	// Parse message (simplified parsing)
	parts := []string{msg} // In production, implement proper parsing

	if len(parts) < 3 {
		ccm.logger.Printf("Invalid message format: %s", msg)
		return
	}

	messageType := parts[1]

	ccm.handlersMutex.RLock()
	handler, exists := ccm.messageHandlers[messageType]
	ccm.handlersMutex.RUnlock()

	if !exists {
		ccm.logger.Printf("No handler for message type: %s", messageType)
		return
	}

	// Create ZMQMessage structure
	zmqMsg := &ZMQMessage{
		Type:      messageType,
		Source:    parts[2],
		Timestamp: time.Now(),
		Data:      make(map[string]interface{}),
	}

	// Handle message
	ctx := context.Background()
	if err := handler.HandleMessage(ctx, zmqMsg); err != nil {
		ccm.logger.Printf("Error handling message: %v", err)
	}
}

// handleZMQRequest handles incoming ZeroMQ requests
func (ccm *ClusterCommunicationManager) handleZMQRequest(msg string) string {
	// Process request and return reply
	// This is a simplified implementation
	return fmt.Sprintf("ACK: %s", msg)
}

// closeZeroMQSockets closes all ZeroMQ sockets
func (ccm *ClusterCommunicationManager) closeZeroMQSockets() {
	if ccm.publisher != nil {
		ccm.publisher.Close()
	}
	if ccm.subscriber != nil {
		ccm.subscriber.Close()
	}
	if ccm.reqSocket != nil {
		ccm.reqSocket.Close()
	}
	if ccm.repSocket != nil {
		ccm.repSocket.Close()
	}
	if ccm.zmqContext != nil {
		ccm.zmqContext.Term()
	}
}

// GetConfig returns the current configuration
func (ccm *ClusterCommunicationManager) GetConfig() *CommunicationConfig {
	return ccm.config
}

// IsRunning returns whether the communication manager is running
func (ccm *ClusterCommunicationManager) IsRunning() bool {
	return ccm.isRunning
}

// GetNodeID returns the current node ID
func (ccm *ClusterCommunicationManager) GetNodeID() string {
	return ccm.config.NodeID
}
