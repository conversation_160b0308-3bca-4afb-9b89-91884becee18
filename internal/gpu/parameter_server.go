package gpu

import (
	"sync"
	"time"
)

// ParameterServer manages model parameters for distributed online learning
type ParameterServer struct {
	parameters  map[string]interface{}
	versions    map[string]int64
	lastUpdated map[string]time.Time
	subscribers map[string][]ParameterSubscriber
	updateCount int64
	mu          sync.RWMutex
}

// ParameterSubscriber interface for parameter update notifications
type ParameterSubscriber interface {
	OnParameterUpdate(key string, value interface{}, version int64) error
}

// NewParameterServer creates a new parameter server
func NewParameterServer() *ParameterServer {
	return &ParameterServer{
		parameters:  make(map[string]interface{}),
		versions:    make(map[string]int64),
		lastUpdated: make(map[string]time.Time),
		subscribers: make(map[string][]ParameterSubscriber),
	}
}

// Update updates model parameters
func (ps *ParameterServer) Update(params map[string]interface{}) {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	for key, value := range params {
		ps.parameters[key] = value
		ps.versions[key]++
		ps.lastUpdated[key] = time.Now()
		ps.updateCount++

		// Notify subscribers
		if subscribers, exists := ps.subscribers[key]; exists {
			for _, subscriber := range subscribers {
				go func(sub ParameterSubscriber, k string, v interface{}, ver int64) {
					sub.OnParameterUpdate(k, v, ver)
				}(subscriber, key, value, ps.versions[key])
			}
		}
	}
}

// Get retrieves a specific parameter
func (ps *ParameterServer) Get(key string) (interface{}, bool) {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	value, exists := ps.parameters[key]
	return value, exists
}

// GetAll retrieves all parameters
func (ps *ParameterServer) GetAll() map[string]interface{} {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	result := make(map[string]interface{})
	for key, value := range ps.parameters {
		result[key] = value
	}
	return result
}

// GetVersion retrieves the version of a specific parameter
func (ps *ParameterServer) GetVersion(key string) (int64, bool) {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	version, exists := ps.versions[key]
	return version, exists
}

// GetVersions retrieves all parameter versions
func (ps *ParameterServer) GetVersions() map[string]int64 {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	result := make(map[string]int64)
	for key, version := range ps.versions {
		result[key] = version
	}
	return result
}

// Subscribe subscribes to parameter updates
func (ps *ParameterServer) Subscribe(key string, subscriber ParameterSubscriber) {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	if ps.subscribers[key] == nil {
		ps.subscribers[key] = make([]ParameterSubscriber, 0)
	}
	ps.subscribers[key] = append(ps.subscribers[key], subscriber)
}

// Unsubscribe removes a subscriber from parameter updates
func (ps *ParameterServer) Unsubscribe(key string, subscriber ParameterSubscriber) {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	if subscribers, exists := ps.subscribers[key]; exists {
		for i, sub := range subscribers {
			if sub == subscriber {
				ps.subscribers[key] = append(subscribers[:i], subscribers[i+1:]...)
				break
			}
		}
	}
}

// GetStatistics returns parameter server statistics
func (ps *ParameterServer) GetStatistics() ParameterServerStats {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	var totalSubscribers int
	for _, subs := range ps.subscribers {
		totalSubscribers += len(subs)
	}

	return ParameterServerStats{
		ParameterCount:   len(ps.parameters),
		TotalUpdates:     ps.updateCount,
		SubscriberCount:  totalSubscribers,
		SubscriptionKeys: len(ps.subscribers),
	}
}

// ParameterServerStats contains statistics about the parameter server
type ParameterServerStats struct {
	ParameterCount   int   `json:"parameter_count"`
	TotalUpdates     int64 `json:"total_updates"`
	SubscriberCount  int   `json:"subscriber_count"`
	SubscriptionKeys int   `json:"subscription_keys"`
}

// GetParameterInfo returns detailed information about a parameter
func (ps *ParameterServer) GetParameterInfo(key string) *ParameterInfo {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	value, valueExists := ps.parameters[key]
	version, versionExists := ps.versions[key]
	lastUpdated, timeExists := ps.lastUpdated[key]

	if !valueExists {
		return nil
	}

	subscriberCount := 0
	if subscribers, exists := ps.subscribers[key]; exists {
		subscriberCount = len(subscribers)
	}

	info := &ParameterInfo{
		Key:             key,
		Value:           value,
		SubscriberCount: subscriberCount,
	}

	if versionExists {
		info.Version = version
	}

	if timeExists {
		info.LastUpdated = lastUpdated
	}

	return info
}

// ParameterInfo contains detailed information about a parameter
type ParameterInfo struct {
	Key             string      `json:"key"`
	Value           interface{} `json:"value"`
	Version         int64       `json:"version"`
	LastUpdated     time.Time   `json:"last_updated"`
	SubscriberCount int         `json:"subscriber_count"`
}

// Clear removes all parameters
func (ps *ParameterServer) Clear() {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	ps.parameters = make(map[string]interface{})
	ps.versions = make(map[string]int64)
	ps.lastUpdated = make(map[string]time.Time)
	ps.updateCount = 0
}

// SetParameter sets a single parameter
func (ps *ParameterServer) SetParameter(key string, value interface{}) {
	ps.Update(map[string]interface{}{key: value})
}

// DeleteParameter removes a parameter
func (ps *ParameterServer) DeleteParameter(key string) {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	delete(ps.parameters, key)
	delete(ps.versions, key)
	delete(ps.lastUpdated, key)
	delete(ps.subscribers, key)
}

// HasParameter checks if a parameter exists
func (ps *ParameterServer) HasParameter(key string) bool {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	_, exists := ps.parameters[key]
	return exists
}

// GetParameterKeys returns all parameter keys
func (ps *ParameterServer) GetParameterKeys() []string {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	keys := make([]string, 0, len(ps.parameters))
	for key := range ps.parameters {
		keys = append(keys, key)
	}
	return keys
}
