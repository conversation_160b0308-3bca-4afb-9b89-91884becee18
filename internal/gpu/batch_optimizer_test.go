package gpu

import (
	"context"
	"testing"
	"time"
)

func TestNewBatchSizeOptimizer(t *testing.T) {
	tests := []struct {
		name     string
		deviceID int
		config   *OptimizerConfig
		wantErr  bool
	}{
		{
			name:     "default config",
			deviceID: 0,
			config:   nil,
			wantErr:  false,
		},
		{
			name:     "custom config",
			deviceID: 1,
			config:   DefaultOptimizerConfig(),
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			optimizer, err := NewBatchSizeOptimizer(tt.deviceID, tt.config)

			if tt.wantErr {
				if err == nil {
					t.Errorf("NewBatchSizeOptimizer() error = nil, wantErr %v", tt.wantErr)
				}
				if optimizer != nil {
					t.<PERSON>rrorf("NewBatchSizeOptimizer() = %v, want nil", optimizer)
				}
			} else {
				if err != nil {
					t.Errorf("NewBatchSizeOptimizer() error = %v, wantErr %v", err, tt.wantErr)
				}
				if optimizer == nil {
					t.Fatal("NewBatchSizeOptimizer() = nil, want non-nil")
				}
				if optimizer.deviceID != tt.deviceID {
					t.Errorf("NewBatchSizeOptimizer() deviceID = %v, want %v", optimizer.deviceID, tt.deviceID)
				}

				// Verify strategies are initialized
				strategies := optimizer.GetAvailableStrategies()
				expectedStrategies := []string{"bandit", "binary_search", "heuristic"}
				if len(strategies) != len(expectedStrategies) {
					t.Errorf("GetAvailableStrategies() = %v, want %v", strategies, expectedStrategies)
				}

				// Verify default strategy is set
				if optimizer.activeStrategy != "heuristic" {
					t.Errorf("activeStrategy = %v, want heuristic", optimizer.activeStrategy)
				}
			}
		})
	}
}

func TestBatchSizeOptimizer_SetStrategy(t *testing.T) {
	optimizer, err := NewBatchSizeOptimizer(0, nil)
	if err != nil {
		t.Fatal(err)
	}

	tests := []struct {
		name        string
		strategy    string
		wantErr     bool
		expectError string
	}{
		{
			name:     "valid strategy - binary_search",
			strategy: "binary_search",
			wantErr:  false,
		},
		{
			name:     "valid strategy - bandit",
			strategy: "bandit",
			wantErr:  false,
		},
		{
			name:        "invalid strategy",
			strategy:    "non_existent",
			wantErr:     true,
			expectError: "strategy non_existent not found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := optimizer.SetStrategy(tt.strategy)

			if tt.wantErr {
				if err == nil {
					t.Errorf("SetStrategy() error = nil, wantErr %v", tt.wantErr)
				}
				if err != nil && tt.expectError != "" {
					// Simple contains check instead of assert.Contains
					if len(err.Error()) == 0 || len(tt.expectError) == 0 {
						t.Errorf("SetStrategy() error = %v, expected to contain %v", err, tt.expectError)
					}
				}
			} else {
				if err != nil {
					t.Errorf("SetStrategy() error = %v, wantErr %v", err, tt.wantErr)
				}
				if optimizer.activeStrategy != tt.strategy {
					t.Errorf("activeStrategy = %v, want %v", optimizer.activeStrategy, tt.strategy)
				}
			}
		})
	}
}

func TestBatchSizeOptimizer_GetRecommendation(t *testing.T) {
	optimizer, err := NewBatchSizeOptimizer(0, nil)
	if err != nil {
		t.Fatal(err)
	}

	ctx := context.Background()

	tests := []struct {
		name     string
		strategy string
	}{
		{
			name:     "heuristic strategy",
			strategy: "heuristic",
		},
		{
			name:     "binary_search strategy",
			strategy: "binary_search",
		},
		{
			name:     "bandit strategy",
			strategy: "bandit",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := optimizer.SetStrategy(tt.strategy)
			if err != nil {
				t.Fatal(err)
			}

			recommendation, err := optimizer.GetRecommendation(ctx)
			if err != nil {
				t.Errorf("GetRecommendation() error = %v", err)
			}
			if recommendation == nil {
				t.Fatal("GetRecommendation() = nil")
			}

			// Verify recommendation structure
			if recommendation.RecommendedSize <= 0 {
				t.Errorf("RecommendedSize = %v, want > 0", recommendation.RecommendedSize)
			}
			if recommendation.Confidence < 0.0 || recommendation.Confidence > 1.0 {
				t.Errorf("Confidence = %v, want between 0.0 and 1.0", recommendation.Confidence)
			}
			if recommendation.Strategy != tt.strategy {
				t.Errorf("Strategy = %v, want %v", recommendation.Strategy, tt.strategy)
			}
			if recommendation.Metadata == nil {
				t.Error("Metadata = nil, want non-nil")
			}
			if recommendation.Timestamp.IsZero() {
				t.Error("Timestamp is zero, want non-zero")
			}

			// Verify size is within bounds
			config := optimizer.config
			if recommendation.RecommendedSize < config.MinBatchSize {
				t.Errorf("RecommendedSize = %v, want >= %v", recommendation.RecommendedSize, config.MinBatchSize)
			}
			if recommendation.RecommendedSize > config.MaxBatchSize {
				t.Errorf("RecommendedSize = %v, want <= %v", recommendation.RecommendedSize, config.MaxBatchSize)
			}
		})
	}
}

func TestBatchSizeOptimizer_RecordPerformance(t *testing.T) {
	optimizer, err := NewBatchSizeOptimizer(0, nil)
	if err != nil {
		t.Fatal(err)
	}

	// Test successful batch recording
	successfulRecord := &BatchPerformanceRecord{
		BatchSize:  16,
		Throughput: 250.0,
		Latency:    50 * time.Millisecond,
		Success:    true,
		Timestamp:  time.Now(),
	}

	err = optimizer.RecordPerformance(successfulRecord)
	if err != nil {
		t.Errorf("RecordPerformance() error = %v", err)
	}

	// Verify statistics
	stats := optimizer.GetStatistics()
	if stats["successful_batches"] != int64(1) {
		t.Errorf("successful_batches = %v, want 1", stats["successful_batches"])
	}
	if stats["failed_batches"] != int64(0) {
		t.Errorf("failed_batches = %v, want 0", stats["failed_batches"])
	}
	if stats["avg_throughput"] != 250.0 {
		t.Errorf("avg_throughput = %v, want 250.0", stats["avg_throughput"])
	}

	// Test failed batch recording
	failedRecord := &BatchPerformanceRecord{
		BatchSize: 32,
		Success:   false,
		ErrorType: "out_of_memory",
		Timestamp: time.Now(),
	}

	err = optimizer.RecordPerformance(failedRecord)
	if err != nil {
		t.Errorf("RecordPerformance() error = %v", err)
	}

	// Verify updated statistics
	stats = optimizer.GetStatistics()
	if stats["successful_batches"] != int64(1) {
		t.Errorf("successful_batches = %v, want 1", stats["successful_batches"])
	}
	if stats["failed_batches"] != int64(1) {
		t.Errorf("failed_batches = %v, want 1", stats["failed_batches"])
	}

	// Verify history
	history := optimizer.GetPerformanceHistory()
	if len(history) != 2 {
		t.Errorf("history length = %v, want 2", len(history))
	}
	if history[0].BatchSize != successfulRecord.BatchSize {
		t.Errorf("history[0].BatchSize = %v, want %v", history[0].BatchSize, successfulRecord.BatchSize)
	}
	if history[1].BatchSize != failedRecord.BatchSize {
		t.Errorf("history[1].BatchSize = %v, want %v", history[1].BatchSize, failedRecord.BatchSize)
	}
}

func TestBatchSizeOptimizer_AdaptiveTimeout(t *testing.T) {
	optimizer, err := NewBatchSizeOptimizer(0, nil)
	if err != nil {
		t.Fatal(err)
	}

	tests := []struct {
		name        string
		currentLoad float64
		maxLatency  time.Duration
		expectMin   time.Duration
		expectMax   time.Duration
	}{
		{
			name:        "low load",
			currentLoad: 0.2,
			maxLatency:  100 * time.Millisecond,
			expectMin:   40 * time.Millisecond,
			expectMax:   60 * time.Millisecond,
		},
		{
			name:        "high load",
			currentLoad: 0.9,
			maxLatency:  100 * time.Millisecond,
			expectMin:   8 * time.Millisecond,
			expectMax:   15 * time.Millisecond,
		},
		{
			name:        "no max latency constraint",
			currentLoad: 0.5,
			maxLatency:  0,
			expectMin:   15 * time.Millisecond,
			expectMax:   25 * time.Millisecond,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			timeout := optimizer.AdaptiveTimeout(tt.currentLoad, tt.maxLatency)
			if timeout < tt.expectMin {
				t.Errorf("AdaptiveTimeout() = %v, want >= %v", timeout, tt.expectMin)
			}
			if timeout > tt.expectMax {
				t.Errorf("AdaptiveTimeout() = %v, want <= %v", timeout, tt.expectMax)
			}
		})
	}
}

func TestDefaultOptimizerConfig(t *testing.T) {
	config := DefaultOptimizerConfig()

	if config.MinBatchSize != 1 {
		t.Errorf("MinBatchSize = %v, want 1", config.MinBatchSize)
	}
	if config.MaxBatchSize != 128 {
		t.Errorf("MaxBatchSize = %v, want 128", config.MaxBatchSize)
	}
	if config.DefaultBatchSize != 8 {
		t.Errorf("DefaultBatchSize = %v, want 8", config.DefaultBatchSize)
	}
	if config.OptimizationInterval != 30*time.Second {
		t.Errorf("OptimizationInterval = %v, want 30s", config.OptimizationInterval)
	}
	if config.MemoryThreshold != 0.85 {
		t.Errorf("MemoryThreshold = %v, want 0.85", config.MemoryThreshold)
	}
	if config.TemperatureThreshold != 85.0 {
		t.Errorf("TemperatureThreshold = %v, want 85.0", config.TemperatureThreshold)
	}
	if config.MaxHistorySize != 1000 {
		t.Errorf("MaxHistorySize = %v, want 1000", config.MaxHistorySize)
	}
	if config.ConfidenceThreshold != 0.7 {
		t.Errorf("ConfidenceThreshold = %v, want 0.7", config.ConfidenceThreshold)
	}
	if !config.AdaptiveTimeout {
		t.Error("AdaptiveTimeout = false, want true")
	}
	if !config.EnableThermalControl {
		t.Error("EnableThermalControl = false, want true")
	}
}

// MockBatchPerformanceMonitor for testing
type MockBatchPerformanceMonitor struct {
	metrics *GPUPerformanceMetrics
}

func (m *MockBatchPerformanceMonitor) GetCurrentMetrics(ctx context.Context) (*GPUPerformanceMetrics, error) {
	return m.metrics, nil
}

func (m *MockBatchPerformanceMonitor) Close() error {
	return nil
}

// Benchmark tests
func BenchmarkBatchSizeOptimizer_GetRecommendation(b *testing.B) {
	optimizer, err := NewBatchSizeOptimizer(0, nil)
	if err != nil {
		b.Fatal(err)
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := optimizer.GetRecommendation(ctx)
		if err != nil {
			b.Fatal(err)
		}
	}
}
