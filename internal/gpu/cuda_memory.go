//go:build cuda
// +build cuda

package gpu

/*
#cgo CFLAGS: -I/usr/local/cuda/include
#cgo LDFLAGS: -L/usr/local/cuda/lib64 -lcuda -lcudart
#include <cuda_runtime.h>
#include <stdlib.h>

static cudaError_t cuda_get_device_memory_info(size_t* free, size_t* total) {
    return cudaMemGetInfo(free, total);
}
*/
import "C"
import (
	"fmt"
	"log"
	"sync"
	"time"
	"unsafe"
)

// allocation represents a single memory allocation
type allocation struct {
	ptr       CUDAMemoryPtr
	size      int64
	timestamp time.Time
}

// CUDAMemoryPoolImpl implements CUDAMemoryPool interface
type CUDAMemoryPoolImpl struct {
	deviceID        int
	initialSize     int64
	allocations     map[CUDAMemoryPtr]*allocation
	totalAllocated  int64
	peakAllocated   int64
	allocationCount int64
	deallocCount    int64
	mutex           sync.RWMutex
	logger          *log.Logger
}

// initialize sets up the memory pool
func (pool *CUDAMemoryPoolImpl) initialize() error {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	// Set the device context
	result := C.cudaSetDevice(C.int(pool.deviceID))
	if result != C.cudaSuccess {
		return fmt.Errorf("failed to set CUDA device %d: %d", pool.deviceID, result)
	}

	pool.logger.Printf("Initialized CUDA memory pool for device %d", pool.deviceID)
	return nil
}

// Allocate allocates memory of the given size
func (pool *CUDAMemoryPoolImpl) Allocate(size int64) (CUDAMemoryPtr, error) {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	// Set device context
	result := C.cudaSetDevice(C.int(pool.deviceID))
	if result != C.cudaSuccess {
		return nil, fmt.Errorf("failed to set CUDA device %d: %d", pool.deviceID, result)
	}

	// Allocate device memory
	var devPtr unsafe.Pointer
	result = C.cudaMalloc(&devPtr, C.size_t(size))
	if result != C.cudaSuccess {
		return nil, fmt.Errorf("CUDA memory allocation failed: %d", result)
	}

	ptr := CUDAMemoryPtr(devPtr)

	// Track allocation
	alloc := &allocation{
		ptr:       ptr,
		size:      size,
		timestamp: time.Now(),
	}

	pool.allocations[ptr] = alloc
	pool.totalAllocated += size
	pool.allocationCount++

	if pool.totalAllocated > pool.peakAllocated {
		pool.peakAllocated = pool.totalAllocated
	}

	pool.logger.Printf("Allocated %d bytes on device %d, ptr: %v", size, pool.deviceID, ptr)
	return ptr, nil
}

// Free releases the allocated memory
func (pool *CUDAMemoryPoolImpl) Free(ptr CUDAMemoryPtr) error {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	// Find allocation
	alloc, exists := pool.allocations[ptr]
	if !exists {
		return fmt.Errorf("memory pointer not found in pool: %v", ptr)
	}

	// Free device memory
	result := C.cudaFree(unsafe.Pointer(ptr))
	if result != C.cudaSuccess {
		return fmt.Errorf("CUDA memory free failed: %d", result)
	}

	// Update tracking
	delete(pool.allocations, ptr)
	pool.totalAllocated -= alloc.size
	pool.deallocCount++

	pool.logger.Printf("Freed %d bytes on device %d, ptr: %v", alloc.size, pool.deviceID, ptr)
	return nil
}

// GetTotalAllocated returns total allocated memory
func (pool *CUDAMemoryPoolImpl) GetTotalAllocated() int64 {
	pool.mutex.RLock()
	defer pool.mutex.RUnlock()
	return pool.totalAllocated
}

// GetPeakAllocated returns peak allocated memory
func (pool *CUDAMemoryPoolImpl) GetPeakAllocated() int64 {
	pool.mutex.RLock()
	defer pool.mutex.RUnlock()
	return pool.peakAllocated
}

// GetFreeMemory returns available free memory
func (pool *CUDAMemoryPoolImpl) GetFreeMemory() int64 {
	pool.mutex.RLock()
	defer pool.mutex.RUnlock()

	// Set device context
	result := C.cudaSetDevice(C.int(pool.deviceID))
	if result != C.cudaSuccess {
		pool.logger.Printf("Failed to set device %d for memory query: %d", pool.deviceID, result)
		return 0
	}

	var free, total C.size_t
	result = C.cuda_get_device_memory_info(&free, &total)
	if result != C.cudaSuccess {
		pool.logger.Printf("Failed to get memory info for device %d: %d", pool.deviceID, result)
		return 0
	}

	return int64(free)
}

// Reset resets the memory pool
func (pool *CUDAMemoryPoolImpl) Reset() error {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	// Free all allocations
	for ptr, alloc := range pool.allocations {
		result := C.cudaFree(unsafe.Pointer(alloc.ptr))
		if result != C.cudaSuccess {
			pool.logger.Printf("Failed to free memory during reset: ptr=%v, error=%d", alloc.ptr, result)
		}
		delete(pool.allocations, ptr)
	}

	// Reset statistics
	pool.totalAllocated = 0
	pool.allocationCount = 0
	pool.deallocCount = 0
	// Note: we don't reset peakAllocated as it's a historical metric

	pool.logger.Printf("Reset memory pool for device %d", pool.deviceID)
	return nil
}

// GetStats returns memory pool statistics
func (pool *CUDAMemoryPoolImpl) GetStats() CUDAMemoryStats {
	pool.mutex.RLock()
	defer pool.mutex.RUnlock()

	// Get current device memory info
	var free, total C.size_t
	result := C.cudaSetDevice(C.int(pool.deviceID))
	if result == C.cudaSuccess {
		C.cuda_get_device_memory_info(&free, &total)
	}

	// Calculate fragmentation (simplified)
	var fragmentation float64
	if pool.totalAllocated > 0 && len(pool.allocations) > 0 {
		// Simple fragmentation estimate based on allocation count vs total size
		avgAllocSize := float64(pool.totalAllocated) / float64(len(pool.allocations))
		idealAllocations := float64(pool.totalAllocated) / avgAllocSize
		actualAllocations := float64(len(pool.allocations))
		fragmentation = (actualAllocations - idealAllocations) / idealAllocations * 100.0
		if fragmentation < 0 {
			fragmentation = 0
		}
	}

	return CUDAMemoryStats{
		TotalAllocated:    pool.totalAllocated,
		PeakAllocated:     pool.peakAllocated,
		CurrentAllocated:  pool.totalAllocated,
		FreeMemory:        int64(free),
		FragmentationPct:  fragmentation,
		AllocationCount:   pool.allocationCount,
		DeallocationCount: pool.deallocCount,
	}
}

// cleanup performs cleanup operations
func (pool *CUDAMemoryPoolImpl) cleanup() error {
	return pool.Reset()
}
