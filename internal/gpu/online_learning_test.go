//go:build legacytest
// +build legacytest

package gpu

import (
	"context"
	"fmt"
	"log"
	"os"
	"testing"
	"time"
)

func TestOnlineLearningManager(t *testing.T) {
	// Create a test predictor
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    5,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)

	// Create online learning manager
	olConfig := DefaultOnlineLearningConfig()
	olConfig.UpdateInterval = time.Millisecond * 100 // Fast updates for testing
	olConfig.ModelEvaluationPeriod = time.Millisecond * 500

	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	olm, err := NewOnlineLearningManager(olConfig, predictor, logger)
	if err != nil {
		t.Fatalf("Failed to create online learning manager: %v", err)
	}

	// Test start and stop
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*2)
	defer cancel()

	err = olm.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start online learning manager: %v", err)
	}

	// Test processing data points
	testData := []WorkloadDataPoint{
		{
			Timestamp:      time.Now(),
			QueueLength:    10,
			ActiveTasks:    5,
			AvgUtilization: 0.5,
			NodesActive:    2,
		},
		{
			Timestamp:      time.Now().Add(time.Minute),
			QueueLength:    15,
			ActiveTasks:    8,
			AvgUtilization: 0.7,
			NodesActive:    3,
		},
		{
			Timestamp:      time.Now().Add(time.Minute * 2),
			QueueLength:    20,
			ActiveTasks:    12,
			AvgUtilization: 0.8,
			NodesActive:    4,
		},
	}

	for _, dataPoint := range testData {
		err = olm.ProcessDataPoint(dataPoint)
		if err != nil {
			t.Errorf("Failed to process data point: %v", err)
		}
	}

	// Wait for processing
	time.Sleep(time.Millisecond * 200)

	// Check status
	status := olm.GetStatus()
	if !status.IsRunning {
		t.Error("Expected online learning manager to be running")
	}

	if status.ExperienceBufferSize == 0 {
		t.Error("Expected experience buffer to have data")
	}

	// Stop the manager
	err = olm.Stop()
	if err != nil {
		t.Errorf("Failed to stop online learning manager: %v", err)
	}

	// Check status after stop
	status = olm.GetStatus()
	if status.IsRunning {
		t.Error("Expected online learning manager to be stopped")
	}
}

func TestDriftDetection(t *testing.T) {
	tests := []struct {
		name   string
		method string
	}{
		{"ADWIN", "adwin"},
		{"PageHinkley", "page_hinkley"},
		{"SPC", "spc"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := DriftDetectorConfig{
				WindowSize:      50,
				Threshold:       0.1,
				ConfidenceLevel: 0.95,
			}

			detector, err := NewDriftDetector(tt.method, config)
			if err != nil {
				t.Fatalf("Failed to create drift detector: %v", err)
			}

			// Add stable values
			for i := 0; i < 30; i++ {
				drift := detector.AddValue(1.0 + float64(i)*0.01)
				if drift {
					t.Errorf("Unexpected drift detected at stable value %d", i)
				}
			}

			// Add values with sudden change
			for i := 0; i < 20; i++ {
				value := 5.0 + float64(i)*0.1 // Significant change
				drift := detector.AddValue(value)
				if drift && tt.method != "spc" { // SPC needs more samples
					t.Logf("Drift detected at value %d (expected)", i+30)
					break
				}
			}

			// Test reset
			detector.Reset()
			if detector.GetWindowSize() != 0 {
				t.Error("Expected window size to be 0 after reset")
			}
		})
	}
}

func TestModelRegistry(t *testing.T) {
	config := ModelRegistryConfig{
		MaxVersions:          5,
		EvaluationPeriod:     time.Hour,
		PerformanceThreshold: 0.8,
	}

	registry := NewModelRegistry(config)

	// Test saving versions
	for i := 0; i < 7; i++ {
		version := ModelVersion{
			ID:        fmt.Sprintf("v%d", i),
			Timestamp: time.Now().Add(time.Duration(i) * time.Minute),
			Accuracy:  0.5 + float64(i)*0.1,
			ModelType: "test_model",
			Parameters: map[string]interface{}{
				"learning_rate": 0.01,
				"epochs":        100,
			},
			Performance: ModelPerformance{
				Accuracy: 0.5 + float64(i)*0.1,
				RMSE:     1.0 - float64(i)*0.1,
				MAE:      0.8 - float64(i)*0.05,
			},
		}
		registry.SaveVersion(version)
	}

	// Check version count (should be limited to maxVersions)
	if registry.GetVersionCount() != config.MaxVersions {
		t.Errorf("Expected %d versions, got %d", config.MaxVersions, registry.GetVersionCount())
	}

	// Test getting best version
	best := registry.GetBestVersion()
	if best == nil {
		t.Fatal("Expected to get best version")
	}

	if best.ID != "v6" { // Latest version should have highest accuracy
		t.Errorf("Expected best version to be v6, got %s", best.ID)
	}

	// Test getting latest version
	latest := registry.GetLatestVersion()
	if latest == nil {
		t.Fatal("Expected to get latest version")
	}

	// Test performance trend
	trend := registry.GetPerformanceTrend()
	if trend.Direction != TrendDirectionIncreasing {
		t.Errorf("Expected increasing trend, got %s", trend.Direction)
	}

	// Test model comparison
	comparison := registry.GetModelComparison()
	if len(comparison) != 1 {
		t.Errorf("Expected 1 model type, got %d", len(comparison))
	}

	stats, exists := comparison["test_model"]
	if !exists {
		t.Error("Expected test_model in comparison")
	}

	if stats.Count != config.MaxVersions {
		t.Errorf("Expected %d model instances, got %d", config.MaxVersions, stats.Count)
	}
}

func TestExperienceBuffer(t *testing.T) {
	buffer := NewExperienceBuffer(10)

	// Test adding experiences
	for i := 0; i < 15; i++ {
		dataPoint := WorkloadDataPoint{
			Timestamp:      time.Now().Add(time.Duration(i) * time.Minute),
			QueueLength:    i + 1,
			ActiveTasks:    i,
			AvgUtilization: float64(i) / 15.0,
			NodesActive:    (i % 5) + 1,
		}
		buffer.Add(dataPoint)
	}

	// Check size (should be limited to capacity)
	if buffer.Size() != 10 {
		t.Errorf("Expected buffer size 10, got %d", buffer.Size())
	}

	// Test sampling
	sample := buffer.Sample(5)
	if len(sample) != 5 {
		t.Errorf("Expected sample size 5, got %d", len(sample))
	}

	// Test getting recent experiences
	recent := buffer.GetRecent(3)
	if len(recent) != 3 {
		t.Errorf("Expected 3 recent experiences, got %d", len(recent))
	}

	// Check that recent experiences are actually the most recent
	if recent[2].QueueLength != 15 { // Last added should have QueueLength 15
		t.Errorf("Expected most recent QueueLength to be 15, got %d", recent[2].QueueLength)
	}

	// Test statistics
	stats := buffer.GetStatistics()
	if stats.Size != 10 {
		t.Errorf("Expected stats size 10, got %d", stats.Size)
	}

	if stats.Capacity != 10 {
		t.Errorf("Expected stats capacity 10, got %d", stats.Capacity)
	}

	// Test sampling by age
	since := time.Now().Add(-time.Minute * 5)
	ageSample := buffer.SampleByAge(since, 3)
	if len(ageSample) == 0 {
		t.Error("Expected to get age-based sample")
	}

	// Test clear
	buffer.Clear()
	if buffer.Size() != 0 {
		t.Errorf("Expected buffer size 0 after clear, got %d", buffer.Size())
	}
}

func TestStreamProcessor(t *testing.T) {
	config := StreamProcessorConfig{
		BufferSize:            10,
		ProcessingDelay:       time.Millisecond * 100,
		BackpressureThreshold: 8,
	}

	processor := NewStreamProcessor(config)

	// Test adding data points
	for i := 0; i < 5; i++ {
		dataPoint := WorkloadDataPoint{
			Timestamp:      time.Now().Add(time.Duration(i) * time.Second),
			QueueLength:    i + 1,
			ActiveTasks:    i,
			AvgUtilization: float64(i) / 5.0,
			NodesActive:    1,
		}
		err := processor.AddDataPoint(dataPoint)
		if err != nil {
			t.Errorf("Failed to add data point: %v", err)
		}
	}

	// Check buffer size
	if processor.GetBufferSize() != 5 {
		t.Errorf("Expected buffer size 5, got %d", processor.GetBufferSize())
	}

	// Test processing batch
	batch := processor.ProcessBatch()
	if len(batch) != 5 {
		t.Errorf("Expected batch size 5, got %d", len(batch))
	}

	// Buffer should be empty after processing
	if processor.GetBufferSize() != 0 {
		t.Errorf("Expected buffer size 0 after processing, got %d", processor.GetBufferSize())
	}

	// Test backpressure
	for i := 0; i < 12; i++ { // Add more than buffer size
		dataPoint := WorkloadDataPoint{
			Timestamp:   time.Now().Add(time.Duration(i) * time.Second),
			QueueLength: i + 1,
		}
		processor.AddDataPoint(dataPoint)
	}

	// Should trigger backpressure
	if !processor.IsBackpressureActive() {
		t.Error("Expected backpressure to be active")
	}

	// Check statistics
	stats := processor.GetStatistics()
	if stats.BufferCapacity != config.BufferSize {
		t.Errorf("Expected buffer capacity %d, got %d", config.BufferSize, stats.BufferCapacity)
	}

	if stats.DroppedCount == 0 {
		t.Error("Expected some dropped data points due to buffer overflow")
	}
}

func TestParameterServer(t *testing.T) {
	server := NewParameterServer()

	// Test setting parameters
	params := map[string]interface{}{
		"learning_rate": 0.01,
		"batch_size":    32,
		"epochs":        100,
	}

	server.Update(params)

	// Test getting parameters
	lr, exists := server.Get("learning_rate")
	if !exists {
		t.Error("Expected learning_rate parameter to exist")
	}

	if lr != 0.01 {
		t.Errorf("Expected learning_rate 0.01, got %v", lr)
	}

	// Test getting all parameters
	allParams := server.GetAll()
	if len(allParams) != 3 {
		t.Errorf("Expected 3 parameters, got %d", len(allParams))
	}

	// Test versions
	version, exists := server.GetVersion("learning_rate")
	if !exists {
		t.Error("Expected learning_rate version to exist")
	}

	if version != 1 {
		t.Errorf("Expected version 1, got %d", version)
	}

	// Test updating existing parameter
	server.SetParameter("learning_rate", 0.02)

	newVersion, _ := server.GetVersion("learning_rate")
	if newVersion != 2 {
		t.Errorf("Expected version 2 after update, got %d", newVersion)
	}

	// Test parameter info
	info := server.GetParameterInfo("learning_rate")
	if info == nil {
		t.Fatal("Expected parameter info")
	}

	if info.Key != "learning_rate" {
		t.Errorf("Expected key learning_rate, got %s", info.Key)
	}

	if info.Version != 2 {
		t.Errorf("Expected version 2, got %d", info.Version)
	}

	// Test statistics
	stats := server.GetStatistics()
	if stats.ParameterCount != 3 {
		t.Errorf("Expected 3 parameters, got %d", stats.ParameterCount)
	}

	if stats.TotalUpdates != 4 { // 3 initial + 1 update
		t.Errorf("Expected 4 total updates, got %d", stats.TotalUpdates)
	}

	// Test deletion
	server.DeleteParameter("epochs")
	if server.HasParameter("epochs") {
		t.Error("Expected epochs parameter to be deleted")
	}

	// Test keys
	keys := server.GetParameterKeys()
	if len(keys) != 2 {
		t.Errorf("Expected 2 parameter keys, got %d", len(keys))
	}
}

func BenchmarkOnlineLearningDataProcessing(b *testing.B) {
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    5,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)

	olConfig := DefaultOnlineLearningConfig()
	olConfig.EnableStreaming = false // Disable streaming for direct processing

	logger := log.New(os.Stdout, "BENCH: ", log.LstdFlags)
	olm, _ := NewOnlineLearningManager(olConfig, predictor, logger)

	dataPoint := WorkloadDataPoint{
		Timestamp:      time.Now(),
		QueueLength:    10,
		ActiveTasks:    5,
		AvgUtilization: 0.5,
		NodesActive:    2,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		dataPoint.Timestamp = time.Now().Add(time.Duration(i) * time.Second)
		olm.ProcessDataPoint(dataPoint)
	}
}

func BenchmarkDriftDetection(b *testing.B) {
	config := DriftDetectorConfig{
		WindowSize:      100,
		Threshold:       0.1,
		ConfidenceLevel: 0.95,
	}

	detector, _ := NewDriftDetector("adwin", config)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		value := 1.0 + float64(i%100)*0.01
		detector.AddValue(value)
	}
}

func BenchmarkExperienceBufferSampling(b *testing.B) {
	buffer := NewExperienceBuffer(1000)

	// Fill buffer
	for i := 0; i < 1000; i++ {
		dataPoint := WorkloadDataPoint{
			Timestamp:      time.Now().Add(time.Duration(i) * time.Second),
			QueueLength:    i,
			ActiveTasks:    i,
			AvgUtilization: float64(i) / 1000.0,
			NodesActive:    (i % 10) + 1,
		}
		buffer.Add(dataPoint)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		buffer.Sample(50)
	}
}
