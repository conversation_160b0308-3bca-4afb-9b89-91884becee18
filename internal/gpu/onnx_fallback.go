//go:build !onnx
// +build !onnx

package gpu

import (
	"fmt"
	"log"
)

// MockONNXSession provides a fallback implementation when ONNX is not available
type MockONNXSession struct {
	logger *log.Logger
}

// NewONNXSession creates a new mock ONNX session when ONNX is not available
func NewONNXSession(logger *log.Logger) ONNXSession {
	return &MockONNXSession{
		logger: logger,
	}
}

// Initialize returns an error indicating ONNX is not available
func (s *MockONNXSession) Initialize(config ONNXSessionConfig) error {
	return fmt.Errorf("ONNX Runtime not available - build with 'onnx' tag to enable ONNX support")
}

// GetInputCount returns 0 for mock session
func (s *MockONNXSession) GetInputCount() int {
	return 0
}

// GetOutputCount returns 0 for mock session
func (s *MockONNXSession) GetOutputCount() int {
	return 0
}

// GetInputName returns error for mock session
func (s *<PERSON>ckONNXSession) GetInputName(index int) (string, error) {
	return "", fmt.Errorf("ONNX Runtime not available")
}

// GetOutputName returns error for mock session
func (s *MockONNXSession) GetOutputName(index int) (string, error) {
	return "", fmt.Errorf("ONNX Runtime not available")
}

// GetInputShape returns error for mock session
func (s *MockONNXSession) GetInputShape(index int) ([]int64, error) {
	return nil, fmt.Errorf("ONNX Runtime not available")
}

// GetOutputShape returns error for mock session
func (s *MockONNXSession) GetOutputShape(index int) ([]int64, error) {
	return nil, fmt.Errorf("ONNX Runtime not available")
}

// Run returns error for mock session
func (s *MockONNXSession) Run(inputs map[string]interface{}) (map[string]interface{}, error) {
	return nil, fmt.Errorf("ONNX Runtime not available - build with 'onnx' tag to enable ONNX support")
}

// RunWithNames returns error for mock session
func (s *MockONNXSession) RunWithNames(inputNames []string, inputs []interface{}, outputNames []string) ([]interface{}, error) {
	return nil, fmt.Errorf("ONNX Runtime not available")
}

// GetProfilingInfo returns error for mock session
func (s *MockONNXSession) GetProfilingInfo() (string, error) {
	return "", fmt.Errorf("ONNX Runtime not available")
}

// Cleanup does nothing for mock session
func (s *MockONNXSession) Cleanup() error {
	return nil
}
