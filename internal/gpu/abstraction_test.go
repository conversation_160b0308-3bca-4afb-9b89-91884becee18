//go:build legacytest
// +build legacytest

package gpu

import (
	"context"
	"log"
	"os"
	"runtime"
	"testing"
)

func TestNewAbstractionManager(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	if manager == nil {
		t.Fatal("NewAbstractionManager returned nil")
	}

	if manager.logger != logger {
		t.Error("Logger not set correctly")
	}

	if manager.registry == nil {
		t.Error("Registry not initialized")
	}
}

func TestListBackends(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	backends := manager.ListBackends()

	// Should always have CPU backend
	found := false
	for _, backend := range backends {
		if backend == "CPU" {
			found = true
			break
		}
	}

	if !found {
		t.Error("CPU backend not found in available backends")
	}
}

func TestGetBestBackend(t *testing.T) {
	logger := log.New(os.Stdo<PERSON>, "TEST: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	backend, err := manager.GetBestBackend(context.Background())
	if err != nil {
		t.Fatalf("GetBestBackend failed: %v", err)
	}

	if backend == nil {
		t.Fatal("GetBestBackend returned nil")
	}

	// Should return a valid backend
	if backend.Name() == "" {
		t.Error("Best backend has empty name")
	}
}

func TestGetBackend(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	// Test CPU backend (should always be available)
	cpuBackend, err := manager.GetBackend("CPU")
	if err != nil {
		t.Fatalf("Failed to get CPU backend: %v", err)
	}
	if cpuBackend == nil {
		t.Error("CPU backend not found")
	}

	// Test non-existent backend
	_, err = manager.GetBackend("NonExistent")
	if err == nil {
		t.Error("Non-existent backend should return error")
	}
}

func TestEnumerateDevices(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	devices, err := manager.EnumerateDevices(context.Background())
	if err != nil {
		t.Fatalf("Failed to enumerate devices: %v", err)
	}

	// Should have at least CPU device
	if len(devices) == 0 {
		t.Error("No devices found")
	}

	// Verify device properties
	foundCPU := false
	for _, device := range devices {
		if device.Backend == "CPU" {
			foundCPU = true
		}

		// Validate device structure
		if device.ID == "" {
			t.Error("Device ID is empty")
		}
		if device.Name == "" {
			t.Error("Device name is empty")
		}
		if device.Backend == "" {
			t.Error("Device backend is empty")
		}
		if device.Memory.Total == 0 {
			t.Error("Device memory total is zero")
		}
	}

	if !foundCPU {
		t.Error("CPU device not found")
	}
}

func TestGetDevice(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	devices, err := manager.EnumerateDevices(context.Background())
	if err != nil {
		t.Fatalf("Failed to enumerate devices: %v", err)
	}

	if len(devices) == 0 {
		t.Skip("No devices available for testing")
	}

	// Test getting first device
	device, err := manager.GetDevice(context.Background(), devices[0].ID)
	if err != nil {
		t.Fatalf("Failed to get device: %v", err)
	}

	if device.ID != devices[0].ID {
		t.Error("Retrieved device ID doesn't match")
	}

	// Test non-existent device
	_, err = manager.GetDevice(context.Background(), "non-existent-device")
	if err == nil {
		t.Error("Getting non-existent device should return error")
	}
}

func TestSelectBestDevice(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	device, err := manager.SelectBestDevice(context.Background())
	if err != nil {
		t.Fatalf("Failed to select best device: %v", err)
	}

	if device == nil {
		t.Fatal("SelectBestDevice returned nil")
	}

	if device.ID == "" {
		t.Error("Best device has empty ID")
	}
}

func TestCreateContext(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	devices, err := manager.EnumerateDevices(context.Background())
	if err != nil {
		t.Fatalf("Failed to enumerate devices: %v", err)
	}

	if len(devices) == 0 {
		t.Skip("No devices available for testing")
	}

	// Test creating context for first device
	ctx, err := manager.CreateContext(context.Background(), &devices[0])
	if err != nil {
		t.Fatalf("Failed to create context: %v", err)
	}

	if ctx == nil {
		t.Fatal("Context is nil")
	}

	if !ctx.IsValid() {
		t.Error("Context is not valid")
	}

	// Test cleanup
	err = ctx.Destroy()
	if err != nil {
		t.Errorf("Failed to cleanup context: %v", err)
	}

	if ctx.IsValid() {
		t.Error("Context should be invalid after cleanup")
	}
}

func TestGetCapabilities(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	devices, err := manager.EnumerateDevices(context.Background())
	if err != nil {
		t.Fatalf("Failed to enumerate devices: %v", err)
	}

	if len(devices) == 0 {
		t.Skip("No devices available for testing")
	}

	// Test getting capabilities for first device
	caps, err := manager.GetCapabilities(&devices[0])
	if err != nil {
		t.Fatalf("Failed to get capabilities: %v", err)
	}

	if caps == nil {
		t.Fatal("Capabilities is nil")
	}

	if caps.Features == nil {
		t.Error("Features map is nil")
	}
}

func TestSupportsFeature(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	devices, err := manager.EnumerateDevices(context.Background())
	if err != nil {
		t.Fatalf("Failed to enumerate devices: %v", err)
	}

	if len(devices) == 0 {
		t.Skip("No devices available for testing")
	}

	// Test feature support
	supported := manager.SupportsFeature(&devices[0], FeatureAsyncCopy)
	t.Logf("Device %s supports async copy: %v", devices[0].Name, supported)
}

func TestPlatformSpecificBackends(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	backends := manager.ListBackends()

	// Check platform-specific expectations
	switch runtime.GOOS {
	case "darwin":
		// On macOS, we might have Metal backend (if build tags are present)
		t.Logf("macOS detected, checking for Metal backend availability")
	case "linux":
		// On Linux, we might have CUDA, ROCm, oneAPI backends
		t.Logf("Linux detected, checking for CUDA/ROCm/oneAPI backend availability")
	case "windows":
		// On Windows, we might have CUDA, DirectML, oneAPI backends
		t.Logf("Windows detected, checking for CUDA/DirectML/oneAPI backend availability")
	}

	// Log available backends for debugging
	for _, backendName := range backends {
		backend, err := manager.GetBackend(backendName)
		if err != nil {
			t.Errorf("Failed to get backend %s: %v", backendName, err)
			continue
		}
		t.Logf("Available backend: %s v%s on %s", backend.Name(), backend.Version(), backend.Platform())
	}
}

func TestBackendRegistry(t *testing.T) {
	registry := NewBackendRegistry()

	// Test registering CPU backend
	cpuBackend := NewCPUBackend()
	registry.Register("CPU", cpuBackend, 500)

	// Test getting registered backend
	retrieved, exists := registry.GetBackend("CPU")
	if !exists {
		t.Error("Failed to retrieve registered backend")
	}
	if retrieved == nil {
		t.Error("Retrieved backend is nil")
	}
	if retrieved.Name() != "CPU" {
		t.Error("Retrieved backend name doesn't match")
	}

	// Test getting ordered backends
	ordered := registry.GetOrderedBackends()
	if len(ordered) != 1 {
		t.Errorf("Expected 1 backend, got %d", len(ordered))
	}

	// Test getting best backend
	best, err := registry.GetBestBackend(context.Background())
	if err != nil {
		t.Errorf("GetBestBackend failed: %v", err)
	}
	if best == nil {
		t.Error("GetBestBackend returned nil")
	}
	if best.Name() != "CPU" {
		t.Error("Best backend should be CPU")
	}
}

func TestSupportDetection(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	// Test platform detection
	tests := []struct {
		name     string
		testFunc func() bool
	}{
		{"Metal", manager.isMetalSupported},
		{"CUDA", manager.isCUDASupported},
		{"OpenCL", manager.isOpenCLSupported},
		{"ROCm", manager.isROCmSupported},
		{"OneAPI", manager.isOneAPISupported},
		{"DirectML", manager.isDirectMLSupported},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			supported := test.testFunc()
			t.Logf("%s support: %v", test.name, supported)
		})
	}
}

func TestCleanup(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	err := manager.Cleanup()
	if err != nil {
		t.Errorf("Failed to cleanup: %v", err)
	}
}

func TestInvalidateCache(t *testing.T) {
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	// This should not fail
	manager.InvalidateCache()
}

// Benchmark tests
func BenchmarkEnumerateDevices(b *testing.B) {
	logger := log.New(os.Stdout, "BENCH: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := manager.EnumerateDevices(context.Background())
		if err != nil {
			b.Fatalf("Failed to enumerate devices: %v", err)
		}
	}
}

func BenchmarkGetBestBackend(b *testing.B) {
	logger := log.New(os.Stdout, "BENCH: ", log.LstdFlags)
	manager := NewAbstractionManager(logger)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		backend, err := manager.GetBestBackend(context.Background())
		if err != nil {
			b.Fatalf("GetBestBackend failed: %v", err)
		}
		if backend == nil {
			b.Fatal("GetBestBackend returned nil")
		}
	}
}
