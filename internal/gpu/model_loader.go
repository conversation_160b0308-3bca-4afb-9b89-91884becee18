package gpu

import (
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"log"
	"os"
	"sync"
	"time"
)

// ModelFormat represents supported model formats
type ModelFormat string

const (
	ModelFormatONNX       ModelFormat = "ONNX"
	ModelFormatTensorFlow ModelFormat = "TensorFlow"
	ModelFormatPyTorch    ModelFormat = "PyTorch"
	ModelFormatCoreML     ModelFormat = "CoreML"
	ModelFormatTensorRT   ModelFormat = "TensorRT"
)

// ModelValidationResult represents the result of model validation
type ModelValidationResult struct {
	Valid          bool          `json:"valid"`
	Format         ModelFormat   `json:"format"`
	Version        string        `json:"version"`
	Checksum       string        `json:"checksum"`
	Size           int64         `json:"size"`
	ValidationTime time.Duration `json:"validation_time"`
	Errors         []string      `json:"errors,omitempty"`
	Warnings       []string      `json:"warnings,omitempty"`
}

// ModelMemoryConfig represents memory optimization configuration
type ModelMemoryConfig struct {
	EnableQuantization       bool    `json:"enable_quantization"`
	QuantizationPrecision    string  `json:"quantization_precision"` // "FP16", "INT8", "INT4"
	EnableMemoryPooling      bool    `json:"enable_memory_pooling"`
	PoolInitialSize          int64   `json:"pool_initial_size"`
	PoolMaxSize              int64   `json:"pool_max_size"`
	EnableGradientCheckpoint bool    `json:"enable_gradient_checkpoint"`
	MaxMemoryUsagePercent    float64 `json:"max_memory_usage_percent"`
}

// BatchProcessingConfig represents batch processing configuration
type BatchProcessingConfig struct {
	EnableDynamicBatching    bool          `json:"enable_dynamic_batching"`
	MaxBatchSize             int           `json:"max_batch_size"`
	MinBatchSize             int           `json:"min_batch_size"`
	BatchTimeout             time.Duration `json:"batch_timeout"`
	EnableAdaptiveSizing     bool          `json:"enable_adaptive_sizing"`
	EnablePriorityScheduling bool          `json:"enable_priority_scheduling"`
	EnableAsyncProcessing    bool          `json:"enable_async_processing"`
}

// TensorOptimizationConfig represents tensor operation optimization settings
type TensorOptimizationConfig struct {
	EnableTensorFusion      bool     `json:"enable_tensor_fusion"`
	EnableOperationCache    bool     `json:"enable_operation_cache"`
	CacheSize               int64    `json:"cache_size"`
	EnableGraphOptimization bool     `json:"enable_graph_optimization"`
	OptimizationLevel       int      `json:"optimization_level"` // 0-3
	EnableKernelTuning      bool     `json:"enable_kernel_tuning"`
	PreferredLibraries      []string `json:"preferred_libraries"` // cuDNN, cuBLAS, etc.
}

// ModelLoadConfig combines all model loading configuration options
type ModelLoadConfig struct {
	ModelPath          string                   `json:"model_path"`
	Format             ModelFormat              `json:"format"`
	DeviceID           int                      `json:"device_id"`
	Providers          []ONNXProvider           `json:"providers"`
	ValidateChecksum   bool                     `json:"validate_checksum"`
	ExpectedChecksum   string                   `json:"expected_checksum,omitempty"`
	Memory             ModelMemoryConfig        `json:"memory"`
	BatchProcessing    BatchProcessingConfig    `json:"batch_processing"`
	TensorOptimization TensorOptimizationConfig `json:"tensor_optimization"`
	PerformanceMode    string                   `json:"performance_mode"` // "latency", "throughput", "balanced"
	EnableProfiling    bool                     `json:"enable_profiling"`
	LogLevel           int                      `json:"log_level"`
}

// DefaultModelLoadConfig returns default model loading configuration
func DefaultModelLoadConfig() ModelLoadConfig {
	return ModelLoadConfig{
		Format:           ModelFormatONNX,
		DeviceID:         -1, // Auto-select
		Providers:        []ONNXProvider{ONNXProviderCUDA, ONNXProviderCPU},
		ValidateChecksum: true,
		Memory: ModelMemoryConfig{
			EnableQuantization:       false,
			QuantizationPrecision:    "FP32",
			EnableMemoryPooling:      true,
			PoolInitialSize:          1024 * 1024 * 100,  // 100MB
			PoolMaxSize:              1024 * 1024 * 1024, // 1GB
			EnableGradientCheckpoint: false,
			MaxMemoryUsagePercent:    80.0,
		},
		BatchProcessing: BatchProcessingConfig{
			EnableDynamicBatching:    true,
			MaxBatchSize:             32,
			MinBatchSize:             1,
			BatchTimeout:             time.Millisecond * 100,
			EnableAdaptiveSizing:     true,
			EnablePriorityScheduling: false,
			EnableAsyncProcessing:    true,
		},
		TensorOptimization: TensorOptimizationConfig{
			EnableTensorFusion:      true,
			EnableOperationCache:    true,
			CacheSize:               1024 * 1024 * 50, // 50MB
			EnableGraphOptimization: true,
			OptimizationLevel:       2,
			EnableKernelTuning:      false,
			PreferredLibraries:      []string{"cuDNN", "cuBLAS"},
		},
		PerformanceMode: "balanced",
		EnableProfiling: false,
		LogLevel:        2, // WARNING
	}
}

// ModelValidator interface for model validation implementations
type ModelValidator interface {
	ValidateFormat(filePath string) (ModelFormat, error)
	ValidateChecksum(filePath string, expectedChecksum string) error
	ValidateCompatibility(filePath string, deviceInfo *GPUInfo) error
	GetModelInfo(filePath string) (*ModelInfo, error)
}

// ModelInfo represents detailed information about a model
type ModelInfo struct {
	Format       ModelFormat            `json:"format"`
	Version      string                 `json:"version"`
	Size         int64                  `json:"size"`
	Checksum     string                 `json:"checksum"`
	InputCount   int                    `json:"input_count"`
	OutputCount  int                    `json:"output_count"`
	InputShapes  [][]int64              `json:"input_shapes"`
	OutputShapes [][]int64              `json:"output_shapes"`
	InputNames   []string               `json:"input_names"`
	OutputNames  []string               `json:"output_names"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// LoadedModel represents a loaded model with its session and metadata
type LoadedModel struct {
	ID               string          `json:"id"`
	Config           ModelLoadConfig `json:"config"`
	Info             *ModelInfo      `json:"info"`
	Session          ONNXSession     `json:"-"`
	LoadTime         time.Time       `json:"load_time"`
	LastUsed         time.Time       `json:"last_used"`
	UseCount         int64           `json:"use_count"`
	MemoryUsage      int64           `json:"memory_usage"`
	AvgInferenceTime time.Duration   `json:"avg_inference_time"`
	TotalInferences  int64           `json:"total_inferences"`
	mutex            sync.RWMutex
}

// ModelLoader interface for model loading and management
type ModelLoader interface {
	// LoadModel loads a model with the given configuration
	LoadModel(config ModelLoadConfig) (*LoadedModel, error)

	// UnloadModel unloads a previously loaded model
	UnloadModel(modelID string) error

	// GetLoadedModel retrieves a loaded model by ID
	GetLoadedModel(modelID string) (*LoadedModel, error)

	// ListLoadedModels returns all currently loaded models
	ListLoadedModels() []*LoadedModel

	// ValidateModel validates a model file without loading it
	ValidateModel(filePath string, config ModelLoadConfig) (*ModelValidationResult, error)

	// GetMemoryUsage returns current memory usage statistics
	GetMemoryUsage() (*ModelMemoryStats, error)

	// OptimizeMemory performs memory optimization operations
	OptimizeMemory() error

	// GetPerformanceStats returns performance statistics
	GetPerformanceStats() (*ModelPerformanceStats, error)
}

// ModelMemoryStats represents memory usage statistics for loaded models
type ModelMemoryStats struct {
	TotalAllocated   int64   `json:"total_allocated"`
	TotalUsed        int64   `json:"total_used"`
	TotalAvailable   int64   `json:"total_available"`
	FragmentationPct float64 `json:"fragmentation_percent"`
	ModelsLoaded     int     `json:"models_loaded"`
	PoolUtilization  float64 `json:"pool_utilization"`
	GarbageCollected int64   `json:"garbage_collected"`
}

// ModelPerformanceStats represents performance statistics for model operations
type ModelPerformanceStats struct {
	TotalInferences     int64         `json:"total_inferences"`
	AvgInferenceTime    time.Duration `json:"avg_inference_time"`
	MinInferenceTime    time.Duration `json:"min_inference_time"`
	MaxInferenceTime    time.Duration `json:"max_inference_time"`
	ThroughputPerSecond float64       `json:"throughput_per_second"`
	BatchUtilization    float64       `json:"batch_utilization"`
	CacheHitRate        float64       `json:"cache_hit_rate"`
	ErrorRate           float64       `json:"error_rate"`
	LastUpdated         time.Time     `json:"last_updated"`
}

// GPUModelLoader implements the ModelLoader interface
type GPUModelLoader struct {
	accelerator  GPUAccelerator
	validator    ModelValidator
	loadedModels map[string]*LoadedModel
	memoryPool   CUDAMemoryPool
	config       ModelLoadConfig
	mutex        sync.RWMutex
	logger       *log.Logger
	stats        *ModelPerformanceStats
	memoryStats  *ModelMemoryStats
}

// NewGPUModelLoader creates a new GPU model loader
func NewGPUModelLoader(accelerator GPUAccelerator, logger *log.Logger) *GPUModelLoader {
	return &GPUModelLoader{
		accelerator:  accelerator,
		validator:    NewModelValidator(logger),
		loadedModels: make(map[string]*LoadedModel),
		logger:       logger,
		stats: &ModelPerformanceStats{
			LastUpdated: time.Now(),
		},
		memoryStats: &ModelMemoryStats{},
	}
}

// Initialize initializes the model loader with configuration
func (ml *GPUModelLoader) Initialize(config ModelLoadConfig) error {
	ml.mutex.Lock()
	defer ml.mutex.Unlock()

	ml.config = config

	// Initialize memory pool if enabled
	if config.Memory.EnableMemoryPooling {
		pool, err := ml.accelerator.CreateMemoryPool(config.Memory.PoolInitialSize)
		if err != nil {
			return fmt.Errorf("failed to create memory pool: %w", err)
		}
		ml.memoryPool = pool
	}

	ml.logger.Printf("GPU Model Loader initialized with config: %+v", config)
	return nil
}

// LoadModel loads a model with the given configuration
func (ml *GPUModelLoader) LoadModel(config ModelLoadConfig) (*LoadedModel, error) {
	startTime := time.Now()

	// Validate model first
	validation, err := ml.ValidateModel(config.ModelPath, config)
	if err != nil {
		return nil, fmt.Errorf("model validation failed: %w", err)
	}

	if !validation.Valid {
		return nil, fmt.Errorf("model validation failed: %v", validation.Errors)
	}

	// Generate model ID
	modelID := ml.generateModelID(config.ModelPath, config)

	// Check if model is already loaded
	ml.mutex.RLock()
	if existingModel, exists := ml.loadedModels[modelID]; exists {
		ml.mutex.RUnlock()
		existingModel.mutex.Lock()
		existingModel.LastUsed = time.Now()
		existingModel.UseCount++
		existingModel.mutex.Unlock()
		ml.logger.Printf("Reusing already loaded model: %s", modelID)
		return existingModel, nil
	}
	ml.mutex.RUnlock()

	// Create ONNX session configuration
	onnxConfig := ml.createONNXConfig(config)

	// Create and initialize ONNX session
	session := NewONNXSession(ml.logger)
	if err := session.Initialize(onnxConfig); err != nil {
		return nil, fmt.Errorf("failed to initialize ONNX session: %w", err)
	}

	// Get model info from session
	modelInfo, err := ml.extractModelInfo(session, config.ModelPath)
	if err != nil {
		session.Cleanup()
		return nil, fmt.Errorf("failed to extract model info: %w", err)
	}

	// Calculate memory usage (estimate)
	memoryUsage := ml.estimateMemoryUsage(modelInfo)

	// Create loaded model
	loadedModel := &LoadedModel{
		ID:               modelID,
		Config:           config,
		Info:             modelInfo,
		Session:          session,
		LoadTime:         startTime,
		LastUsed:         time.Now(),
		UseCount:         1,
		MemoryUsage:      memoryUsage,
		AvgInferenceTime: 0,
		TotalInferences:  0,
	}

	// Store loaded model
	ml.mutex.Lock()
	ml.loadedModels[modelID] = loadedModel
	ml.mutex.Unlock()

	loadDuration := time.Since(startTime)
	ml.logger.Printf("Successfully loaded model '%s' in %v (memory: %d bytes)",
		modelID, loadDuration, memoryUsage)

	return loadedModel, nil
}

// generateModelID generates a unique ID for a model based on path and config
func (ml *GPUModelLoader) generateModelID(modelPath string, config ModelLoadConfig) string {
	hasher := md5.New()
	hasher.Write([]byte(modelPath))
	hasher.Write([]byte(fmt.Sprintf("%+v", config)))
	return hex.EncodeToString(hasher.Sum(nil))[:16]
}

// createONNXConfig converts ModelLoadConfig to ONNXSessionConfig
func (ml *GPUModelLoader) createONNXConfig(config ModelLoadConfig) ONNXSessionConfig {
	onnxConfig := DefaultONNXConfig()
	onnxConfig.ModelPath = config.ModelPath
	onnxConfig.Providers = config.Providers
	onnxConfig.DeviceID = config.DeviceID
	onnxConfig.LogSeverityLevel = config.LogLevel
	onnxConfig.EnableProfiling = config.EnableProfiling

	// Apply optimization settings
	if config.TensorOptimization.EnableGraphOptimization {
		onnxConfig.GraphOptLevel = config.TensorOptimization.OptimizationLevel
	}

	// Apply memory settings
	onnxConfig.EnableCPUMemArena = config.Memory.EnableMemoryPooling
	onnxConfig.EnableMemPattern = config.Memory.EnableMemoryPooling

	return onnxConfig
}

// extractModelInfo extracts information from a loaded ONNX session
func (ml *GPUModelLoader) extractModelInfo(session ONNXSession, modelPath string) (*ModelInfo, error) {
	// Calculate file checksum
	checksum, err := ml.calculateChecksum(modelPath)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate checksum: %w", err)
	}

	// Get file size
	fileInfo, err := os.Stat(modelPath)
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	// Extract input/output information
	inputCount := session.GetInputCount()
	outputCount := session.GetOutputCount()

	inputNames := make([]string, inputCount)
	inputShapes := make([][]int64, inputCount)
	for i := 0; i < inputCount; i++ {
		name, err := session.GetInputName(i)
		if err != nil {
			return nil, fmt.Errorf("failed to get input name %d: %w", i, err)
		}
		inputNames[i] = name

		shape, err := session.GetInputShape(i)
		if err != nil {
			return nil, fmt.Errorf("failed to get input shape %d: %w", i, err)
		}
		inputShapes[i] = shape
	}

	outputNames := make([]string, outputCount)
	outputShapes := make([][]int64, outputCount)
	for i := 0; i < outputCount; i++ {
		name, err := session.GetOutputName(i)
		if err != nil {
			return nil, fmt.Errorf("failed to get output name %d: %w", i, err)
		}
		outputNames[i] = name

		shape, err := session.GetOutputShape(i)
		if err != nil {
			return nil, fmt.Errorf("failed to get output shape %d: %w", i, err)
		}
		outputShapes[i] = shape
	}

	return &ModelInfo{
		Format:       ModelFormatONNX,
		Size:         fileInfo.Size(),
		Checksum:     checksum,
		InputCount:   inputCount,
		OutputCount:  outputCount,
		InputShapes:  inputShapes,
		OutputShapes: outputShapes,
		InputNames:   inputNames,
		OutputNames:  outputNames,
		Metadata:     make(map[string]interface{}),
	}, nil
}

// calculateChecksum calculates SHA256 checksum of a file
func (ml *GPUModelLoader) calculateChecksum(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hasher := sha256.New()
	if _, err := io.Copy(hasher, file); err != nil {
		return "", err
	}

	return hex.EncodeToString(hasher.Sum(nil)), nil
}

// estimateMemoryUsage estimates memory usage for a model
func (ml *GPUModelLoader) estimateMemoryUsage(info *ModelInfo) int64 {
	// Basic estimation based on model size and tensor shapes
	baseMemory := info.Size

	// Add memory for input/output tensors (rough estimate)
	var tensorMemory int64
	for _, shape := range info.InputShapes {
		elements := int64(1)
		for _, dim := range shape {
			if dim > 0 {
				elements *= dim
			}
		}
		tensorMemory += elements * 4 // Assume FP32
	}

	for _, shape := range info.OutputShapes {
		elements := int64(1)
		for _, dim := range shape {
			if dim > 0 {
				elements *= dim
			}
		}
		tensorMemory += elements * 4 // Assume FP32
	}

	// Add 20% overhead for internal structures
	return baseMemory + tensorMemory + int64(float64(baseMemory)*0.2)
}

// UnloadModel unloads a previously loaded model
func (ml *GPUModelLoader) UnloadModel(modelID string) error {
	ml.mutex.Lock()
	defer ml.mutex.Unlock()

	model, exists := ml.loadedModels[modelID]
	if !exists {
		return fmt.Errorf("model not found: %s", modelID)
	}

	// Cleanup ONNX session
	if err := model.Session.Cleanup(); err != nil {
		ml.logger.Printf("Warning: failed to cleanup session for model %s: %v", modelID, err)
	}

	// Remove from loaded models
	delete(ml.loadedModels, modelID)

	ml.logger.Printf("Successfully unloaded model: %s", modelID)
	return nil
}

// GetLoadedModel retrieves a loaded model by ID
func (ml *GPUModelLoader) GetLoadedModel(modelID string) (*LoadedModel, error) {
	ml.mutex.RLock()
	defer ml.mutex.RUnlock()

	model, exists := ml.loadedModels[modelID]
	if !exists {
		return nil, fmt.Errorf("model not found: %s", modelID)
	}

	return model, nil
}

// ListLoadedModels returns all currently loaded models
func (ml *GPUModelLoader) ListLoadedModels() []*LoadedModel {
	ml.mutex.RLock()
	defer ml.mutex.RUnlock()

	models := make([]*LoadedModel, 0, len(ml.loadedModels))
	for _, model := range ml.loadedModels {
		models = append(models, model)
	}

	return models
}

// ValidateModel validates a model file without loading it
func (ml *GPUModelLoader) ValidateModel(filePath string, config ModelLoadConfig) (*ModelValidationResult, error) {
	startTime := time.Now()

	result := &ModelValidationResult{
		Valid:          true,
		ValidationTime: 0,
		Errors:         []string{},
		Warnings:       []string{},
	}

	// Check if file exists
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		result.Valid = false
		result.Errors = append(result.Errors, fmt.Sprintf("file not found: %v", err))
		return result, nil
	}

	result.Size = fileInfo.Size()

	// Validate file format
	format, err := ml.validator.ValidateFormat(filePath)
	if err != nil {
		result.Valid = false
		result.Errors = append(result.Errors, fmt.Sprintf("invalid format: %v", err))
		return result, nil
	}

	result.Format = format

	// Validate checksum if provided
	if config.ValidateChecksum && config.ExpectedChecksum != "" {
		if err := ml.validator.ValidateChecksum(filePath, config.ExpectedChecksum); err != nil {
			result.Valid = false
			result.Errors = append(result.Errors, fmt.Sprintf("checksum validation failed: %v", err))
		}
	}

	// Calculate actual checksum
	checksum, err := ml.calculateChecksum(filePath)
	if err != nil {
		result.Warnings = append(result.Warnings, fmt.Sprintf("failed to calculate checksum: %v", err))
	} else {
		result.Checksum = checksum
	}

	// Check file size limits
	maxSize := int64(10 * 1024 * 1024 * 1024) // 10GB limit
	if result.Size > maxSize {
		result.Warnings = append(result.Warnings, fmt.Sprintf("large model file: %d bytes", result.Size))
	}

	result.ValidationTime = time.Since(startTime)

	return result, nil
}

// GetMemoryUsage returns current memory usage statistics
func (ml *GPUModelLoader) GetMemoryUsage() (*ModelMemoryStats, error) {
	ml.mutex.RLock()
	defer ml.mutex.RUnlock()

	stats := &ModelMemoryStats{
		ModelsLoaded: len(ml.loadedModels),
	}

	// Calculate total memory usage from loaded models
	for _, model := range ml.loadedModels {
		model.mutex.RLock()
		stats.TotalUsed += model.MemoryUsage
		model.mutex.RUnlock()
	}

	// Get memory pool statistics if available
	if ml.memoryPool != nil {
		poolStats := ml.memoryPool.GetStats()
		stats.TotalAllocated = poolStats.TotalAllocated
		stats.TotalAvailable = poolStats.FreeMemory
		stats.FragmentationPct = poolStats.FragmentationPct
		stats.GarbageCollected = poolStats.DeallocationCount

		if poolStats.TotalAllocated > 0 {
			stats.PoolUtilization = float64(poolStats.CurrentAllocated) / float64(poolStats.TotalAllocated) * 100.0
		}
	}

	return stats, nil
}

// OptimizeMemory performs memory optimization operations
func (ml *GPUModelLoader) OptimizeMemory() error {
	ml.mutex.Lock()
	defer ml.mutex.Unlock()

	// Remove unused models (not accessed for more than 1 hour)
	cutoffTime := time.Now().Add(-1 * time.Hour)
	var toRemove []string

	for modelID, model := range ml.loadedModels {
		model.mutex.RLock()
		if model.LastUsed.Before(cutoffTime) && model.UseCount == 1 {
			toRemove = append(toRemove, modelID)
		}
		model.mutex.RUnlock()
	}

	// Remove identified models
	for _, modelID := range toRemove {
		if err := ml.unloadModelUnsafe(modelID); err != nil {
			ml.logger.Printf("Warning: failed to unload unused model %s: %v", modelID, err)
		} else {
			ml.logger.Printf("Unloaded unused model: %s", modelID)
		}
	}

	// Reset memory pool if available
	if ml.memoryPool != nil {
		if err := ml.memoryPool.Reset(); err != nil {
			return fmt.Errorf("failed to reset memory pool: %w", err)
		}
	}

	ml.logger.Printf("Memory optimization completed, removed %d unused models", len(toRemove))
	return nil
}

// unloadModelUnsafe unloads a model without acquiring mutex (internal use)
func (ml *GPUModelLoader) unloadModelUnsafe(modelID string) error {
	model, exists := ml.loadedModels[modelID]
	if !exists {
		return fmt.Errorf("model not found: %s", modelID)
	}

	// Cleanup ONNX session
	if err := model.Session.Cleanup(); err != nil {
		return fmt.Errorf("failed to cleanup session: %w", err)
	}

	// Remove from loaded models
	delete(ml.loadedModels, modelID)

	return nil
}

// GetPerformanceStats returns performance statistics
func (ml *GPUModelLoader) GetPerformanceStats() (*ModelPerformanceStats, error) {
	ml.mutex.RLock()
	defer ml.mutex.RUnlock()

	// Aggregate stats from all loaded models
	var totalInferences int64
	var totalInferenceTime time.Duration
	var minTime, maxTime time.Duration
	var errorCount int64

	minTime = time.Duration(^uint64(0) >> 1) // Max duration

	for _, model := range ml.loadedModels {
		model.mutex.RLock()
		totalInferences += model.TotalInferences
		if model.TotalInferences > 0 {
			modelTotalTime := time.Duration(model.TotalInferences) * model.AvgInferenceTime
			totalInferenceTime += modelTotalTime

			if model.AvgInferenceTime < minTime {
				minTime = model.AvgInferenceTime
			}
			if model.AvgInferenceTime > maxTime {
				maxTime = model.AvgInferenceTime
			}
		}
		model.mutex.RUnlock()
	}

	stats := &ModelPerformanceStats{
		TotalInferences:  totalInferences,
		MinInferenceTime: minTime,
		MaxInferenceTime: maxTime,
		BatchUtilization: 85.0, // TODO: Calculate actual batch utilization
		CacheHitRate:     92.0, // TODO: Calculate actual cache hit rate
		ErrorRate:        float64(errorCount) / float64(totalInferences) * 100.0,
		LastUpdated:      time.Now(),
	}

	if totalInferences > 0 {
		stats.AvgInferenceTime = totalInferenceTime / time.Duration(totalInferences)

		// Calculate throughput (inferences per second)
		// This is a rough estimate based on average inference time
		if stats.AvgInferenceTime > 0 {
			stats.ThroughputPerSecond = float64(time.Second) / float64(stats.AvgInferenceTime)
		}
	}

	if minTime == time.Duration(^uint64(0)>>1) {
		stats.MinInferenceTime = 0
	}

	return stats, nil
}

// RunInference performs inference on a loaded model
func (ml *GPUModelLoader) RunInference(modelID string, inputs map[string]interface{}) (map[string]interface{}, error) {
	// Get loaded model
	model, err := ml.GetLoadedModel(modelID)
	if err != nil {
		return nil, err
	}

	// Track inference timing
	startTime := time.Now()

	// Run inference
	outputs, err := model.Session.Run(inputs)
	if err != nil {
		return nil, fmt.Errorf("inference failed: %w", err)
	}

	inferenceTime := time.Since(startTime)

	// Update model statistics
	model.mutex.Lock()
	model.LastUsed = time.Now()
	model.UseCount++
	model.TotalInferences++

	// Update average inference time (exponential moving average)
	if model.AvgInferenceTime == 0 {
		model.AvgInferenceTime = inferenceTime
	} else {
		alpha := 0.1 // Smoothing factor
		model.AvgInferenceTime = time.Duration(float64(model.AvgInferenceTime)*(1-alpha) + float64(inferenceTime)*alpha)
	}
	model.mutex.Unlock()

	return outputs, nil
}

// BatchInference performs batch inference on a loaded model
func (ml *GPUModelLoader) BatchInference(modelID string, batchInputs []map[string]interface{}) ([]map[string]interface{}, error) {
	// Get loaded model
	model, err := ml.GetLoadedModel(modelID)
	if err != nil {
		return nil, err
	}

	// Check batch size limits
	if len(batchInputs) > model.Config.BatchProcessing.MaxBatchSize {
		return nil, fmt.Errorf("batch size %d exceeds maximum %d", len(batchInputs), model.Config.BatchProcessing.MaxBatchSize)
	}

	results := make([]map[string]interface{}, len(batchInputs))

	// Process batch (currently sequential, TODO: implement true batching)
	for i, inputs := range batchInputs {
		outputs, err := ml.RunInference(modelID, inputs)
		if err != nil {
			return nil, fmt.Errorf("batch inference failed at index %d: %w", i, err)
		}
		results[i] = outputs
	}

	return results, nil
}

// Cleanup performs cleanup of all resources
func (ml *GPUModelLoader) Cleanup() error {
	ml.mutex.Lock()
	defer ml.mutex.Unlock()

	// Unload all models
	for modelID := range ml.loadedModels {
		if err := ml.unloadModelUnsafe(modelID); err != nil {
			ml.logger.Printf("Warning: failed to unload model %s during cleanup: %v", modelID, err)
		}
	}

	// Cleanup memory pool
	if ml.memoryPool != nil {
		if err := ml.memoryPool.Reset(); err != nil {
			ml.logger.Printf("Warning: failed to reset memory pool during cleanup: %v", err)
		}
	}

	ml.logger.Printf("GPU Model Loader cleanup completed")
	return nil
}
