package gpu

import (
	"testing"
)

func TestMixedPrecisionEngine_NewMixedPrecisionEngine(t *testing.T) {
	config := DefaultMixedPrecisionConfig()
	hardwareInfo := &GPUInfo{
		ID:                  0,
		Name:                "Test GPU",
		TotalMemory:         8192 * 1024 * 1024, // Convert MB to bytes
		MultiProcessorCount: 32,
		Available:           true,
	}

	engine := NewMixedPrecisionEngine(config, hardwareInfo)

	if engine == nil {
		t.Fatal("NewMixedPrecisionEngine returned nil")
	}

	if engine.config.EnableMixedPrecision != config.EnableMixedPrecision {
		t.<PERSON><PERSON>rf("Expected EnableMixedPrecision %v, got %v", config.EnableMixedPrecision, engine.config.EnableMixedPrecision)
	}

	if len(engine.layers) != 0 {
		t.Errorf("Expected 0 layers initially, got %d", len(engine.layers))
	}

	if engine.quantizationEngine == nil {
		t.Error("Expected quantization engine to be initialized")
	}
}

func TestMixedPrecisionEngine_AddLayer(t *testing.T) {
	config := DefaultMixedPrecisionConfig()
	engine := NewMixedPrecisionEngine(config, nil)

	layer1 := NewLayer("layer1", "conv1", LayerTypeConvolution)
	layer1.WeightShape = []int64{64, 3, 3, 3}

	layer2 := NewLayer("layer2", "linear1", LayerTypeLinear)
	layer2.WeightShape = []int64{1000, 512}

	engine.AddLayer(layer1)
	engine.AddLayer(layer2)

	layers := engine.GetLayers()
	if len(layers) != 2 {
		t.Errorf("Expected 2 layers, got %d", len(layers))
	}

	if layers[0].ID != "layer1" {
		t.Errorf("Expected first layer ID 'layer1', got %s", layers[0].ID)
	}

	if layers[1].Type != LayerTypeLinear {
		t.Errorf("Expected second layer type %s, got %s", LayerTypeLinear, layers[1].Type)
	}
}

func TestLayer_PrecisionManagement(t *testing.T) {
	layer := NewLayer("test", "test_layer", LayerTypeConvolution)

	// Test default precision
	if layer.GetPrecision() != PrecisionFP32 {
		t.Errorf("Expected default precision %s, got %s", PrecisionFP32, layer.GetPrecision())
	}

	// Test setting precision
	layer.SetPrecision(PrecisionINT8)
	if layer.GetPrecision() != PrecisionINT8 {
		t.Errorf("Expected precision %s after setting, got %s", PrecisionINT8, layer.GetPrecision())
	}

	// Test memory footprint calculation
	layer.WeightShape = []int64{32, 3, 3, 3}
	layer.BiasShape = []int64{32}

	// FP32 footprint
	layer.SetPrecision(PrecisionFP32)
	fp32Footprint := layer.EstimateMemoryFootprint()
	expectedFP32 := (32*3*3*3 + 32) * 4 // 4 bytes per FP32
	if fp32Footprint != int64(expectedFP32) {
		t.Errorf("Expected FP32 footprint %d, got %d", expectedFP32, fp32Footprint)
	}

	// INT8 footprint
	layer.SetPrecision(PrecisionINT8)
	int8Footprint := layer.EstimateMemoryFootprint()
	expectedINT8 := (32*3*3*3 + 32) * 1 // 1 byte per INT8
	if int8Footprint != int64(expectedINT8) {
		t.Errorf("Expected INT8 footprint %d, got %d", expectedINT8, int8Footprint)
	}

	// Verify INT8 uses less memory than FP32
	if int8Footprint >= fp32Footprint {
		t.Errorf("Expected INT8 footprint (%d) to be less than FP32 footprint (%d)", int8Footprint, fp32Footprint)
	}
}

func TestMixedPrecisionEngine_SensitivityAnalysis(t *testing.T) {
	config := DefaultMixedPrecisionConfig()
	engine := NewMixedPrecisionEngine(config, nil)

	// Add test layers
	layer1 := NewLayer("conv1", "convolution1", LayerTypeConvolution)
	layer1.WeightShape = []int64{32, 3, 3, 3}
	layer2 := NewLayer("linear1", "linear1", LayerTypeLinear)
	layer2.WeightShape = []int64{1000, 512}

	engine.AddLayer(layer1)
	engine.AddLayer(layer2)

	// Create calibration data
	calibrationData := []*Tensor{
		createTestTensor([]int64{1, 3, 32, 32}, TensorFloat32),
		createTestTensor([]int64{1, 3, 32, 32}, TensorFloat32),
	}

	// Run sensitivity analysis
	err := engine.AnalyzeSensitivity(calibrationData)
	if err != nil {
		t.Fatalf("Sensitivity analysis failed: %v", err)
	}

	// Verify sensitivity metrics were computed
	layers := engine.GetLayers()
	for _, layer := range layers {
		sensitivity := layer.GetSensitivity()
		if sensitivity == nil {
			t.Errorf("Expected sensitivity metrics for layer %s", layer.ID)
			continue
		}

		if sensitivity.OverallSensitivity < 0 || sensitivity.OverallSensitivity > 1 {
			t.Errorf("Expected overall sensitivity in [0,1], got %f for layer %s",
				sensitivity.OverallSensitivity, layer.ID)
		}
	}
}

func TestMixedPrecisionEngine_PrecisionAssignment(t *testing.T) {
	config := DefaultMixedPrecisionConfig()
	config.MemoryBudgetMB = 100 // Small budget to test memory constraints

	engine := NewMixedPrecisionEngine(config, nil)

	// Add layers with different characteristics
	sensitiveLayer := NewLayer("sensitive", "important_conv", LayerTypeConvolution)
	sensitiveLayer.WeightShape = []int64{128, 64, 3, 3}

	regularLayer := NewLayer("regular", "regular_conv", LayerTypeConvolution)
	regularLayer.WeightShape = []int64{64, 32, 3, 3}

	activationLayer := NewLayer("activation", "relu", LayerTypeActivation)

	engine.AddLayer(sensitiveLayer)
	engine.AddLayer(regularLayer)
	engine.AddLayer(activationLayer)

	// Create mock sensitivity data
	sensitiveSensitivity := &SensitivityMetrics{
		GradientMagnitude:  0.9,
		HessianTrace:       0.8,
		ActivationVariance: 0.7,
		WeightVariance:     0.6,
		OverallSensitivity: 0.85, // High sensitivity
	}

	regularSensitivity := &SensitivityMetrics{
		GradientMagnitude:  0.3,
		HessianTrace:       0.2,
		ActivationVariance: 0.4,
		WeightVariance:     0.3,
		OverallSensitivity: 0.3, // Low sensitivity
	}

	sensitiveLayer.UpdateSensitivity(sensitiveSensitivity)
	regularLayer.UpdateSensitivity(regularSensitivity)

	// Run precision assignment
	err := engine.AssignPrecisions()
	if err != nil {
		t.Fatalf("Precision assignment failed: %v", err)
	}

	// Verify precision assignments
	// Sensitive layer should get higher precision
	if sensitiveLayer.GetPrecision() == PrecisionINT4 {
		t.Error("Sensitive layer should not be assigned lowest precision")
	}

	// Regular layer with low sensitivity might get lower precision
	regularPrecision := regularLayer.GetPrecision()
	if regularPrecision != PrecisionINT8 && regularPrecision != PrecisionINT4 {
		t.Errorf("Regular layer with low sensitivity should get quantized precision, got %s", regularPrecision)
	}

	// Activation layer should get appropriate precision based on type defaults
	activationPrecision := activationLayer.GetPrecision()
	expectedActivationPrecision := config.LayerTypePrecisionMap[LayerTypeActivation]
	if activationPrecision != expectedActivationPrecision {
		t.Errorf("Activation layer should get type default precision %s, got %s",
			expectedActivationPrecision, activationPrecision)
	}
}

func TestMixedPrecisionEngine_QuantizeLayer(t *testing.T) {
	config := DefaultMixedPrecisionConfig()
	engine := NewMixedPrecisionEngine(config, nil)

	layer := NewLayer("test", "test_conv", LayerTypeConvolution)
	layer.WeightShape = []int64{16, 8, 3, 3}
	layer.SetPrecision(PrecisionINT8)

	// Create test weights tensor
	weights := createTestTensorWithData([]int64{16, 8, 3, 3}, TensorFloat32,
		[]float32{0.1, 0.2, 0.3, 0.4, 0.5, -0.1, -0.2, -0.3})

	// Quantize the layer
	quantizedWeights, err := engine.QuantizeLayer(layer, weights)
	if err != nil {
		t.Fatalf("Layer quantization failed: %v", err)
	}

	if quantizedWeights == nil {
		t.Fatal("Quantized weights should not be nil")
	}

	if quantizedWeights.DataType() != TensorInt8 {
		t.Errorf("Expected quantized weights to be INT8, got %s", quantizedWeights.DataType())
	}

	if !layer.IsQuantized {
		t.Error("Layer should be marked as quantized")
	}

	if layer.QuantizationParams["weights"] == nil {
		t.Error("Layer should have quantization parameters stored")
	}
}

func TestMixedPrecisionEngine_GetSummary(t *testing.T) {
	config := DefaultMixedPrecisionConfig()
	engine := NewMixedPrecisionEngine(config, nil)

	// Add layers with different precisions
	layer1 := NewLayer("l1", "conv1", LayerTypeConvolution)
	layer1.WeightShape = []int64{32, 16, 3, 3}
	layer1.SetPrecision(PrecisionFP32)

	layer2 := NewLayer("l2", "conv2", LayerTypeConvolution)
	layer2.WeightShape = []int64{64, 32, 3, 3}
	layer2.SetPrecision(PrecisionINT8)

	layer3 := NewLayer("l3", "linear", LayerTypeLinear)
	layer3.WeightShape = []int64{1000, 512}
	layer3.SetPrecision(PrecisionINT4)

	engine.AddLayer(layer1)
	engine.AddLayer(layer2)
	engine.AddLayer(layer3)

	// Update memory footprints and verify they're calculated
	fp1 := layer1.EstimateMemoryFootprint()
	fp2 := layer2.EstimateMemoryFootprint()
	fp3 := layer3.EstimateMemoryFootprint()

	if fp1 <= 0 || fp2 <= 0 || fp3 <= 0 {
		t.Fatalf("Memory footprints should be positive: fp1=%d, fp2=%d, fp3=%d", fp1, fp2, fp3)
	}

	summary := engine.GetMixedPrecisionSummary()

	// Verify summary structure
	if summary["total_layers"] != 3 {
		t.Errorf("Expected 3 total layers, got %v", summary["total_layers"])
	}

	precisionDist, ok := summary["precision_distribution"].(map[PrecisionMode]int)
	if !ok {
		t.Fatal("Precision distribution should be a map")
	}

	if precisionDist[PrecisionFP32] != 1 {
		t.Errorf("Expected 1 FP32 layer, got %d", precisionDist[PrecisionFP32])
	}

	if precisionDist[PrecisionINT8] != 1 {
		t.Errorf("Expected 1 INT8 layer, got %d", precisionDist[PrecisionINT8])
	}

	if precisionDist[PrecisionINT4] != 1 {
		t.Errorf("Expected 1 INT4 layer, got %d", precisionDist[PrecisionINT4])
	}

	// Verify memory calculations
	totalMemoryMB, ok := summary["total_memory_mb"].(float64)
	if !ok {
		t.Fatal("Total memory should be a float64")
	}

	// Verify memory is calculated correctly (should be ~0.52 MB for our test layers)

	if totalMemoryMB <= 0 {
		t.Error("Total memory should be positive")
	}

	// Verify mixed precision is enabled
	if summary["mixed_precision_enabled"] != true {
		t.Error("Mixed precision should be enabled")
	}
}

func TestDefaultMixedPrecisionConfig(t *testing.T) {
	config := DefaultMixedPrecisionConfig()

	if !config.EnableMixedPrecision {
		t.Error("Mixed precision should be enabled by default")
	}

	if !config.AutoPrecisionAssignment {
		t.Error("Auto precision assignment should be enabled by default")
	}

	if config.MemoryBudgetMB <= 0 {
		t.Errorf("Memory budget should be positive, got %d", config.MemoryBudgetMB)
	}

	if len(config.SensitivityThresholds) == 0 {
		t.Error("Sensitivity thresholds should be defined")
	}

	if len(config.LayerTypePrecisionMap) == 0 {
		t.Error("Layer type precision map should be defined")
	}

	// Verify specific layer type defaults
	if config.LayerTypePrecisionMap[LayerTypeConvolution] != PrecisionINT8 {
		t.Errorf("Expected convolution layers to default to INT8, got %s",
			config.LayerTypePrecisionMap[LayerTypeConvolution])
	}

	if config.LayerTypePrecisionMap[LayerTypeNormalization] != PrecisionFP32 {
		t.Errorf("Expected normalization layers to default to FP32, got %s",
			config.LayerTypePrecisionMap[LayerTypeNormalization])
	}
}

func TestHardwareConstraints(t *testing.T) {
	config := DefaultMixedPrecisionConfig()

	// Test with hardware constraints
	constraints := &HardwareConstraints{
		SupportedPrecisions: []PrecisionMode{PrecisionFP32, PrecisionINT8},
		PreferredPrecisions: []PrecisionMode{PrecisionINT8},
		MemoryBandwidthGBps: 500.0,
		MaxMemoryMB:         8192,
	}

	config.HardwareConstraints = constraints

	engine := NewMixedPrecisionEngine(config, nil)

	// Add a layer and test precision assignment with constraints
	layer := NewLayer("test", "test_layer", LayerTypeConvolution)
	layer.WeightShape = []int64{32, 16, 3, 3}
	engine.AddLayer(layer)

	// Mock low sensitivity to trigger INT4 assignment
	lowSensitivity := &SensitivityMetrics{
		OverallSensitivity: 0.1,
	}
	layer.UpdateSensitivity(lowSensitivity)

	err := engine.AssignPrecisions()
	if err != nil {
		t.Fatalf("Precision assignment with constraints failed: %v", err)
	}

	// Since INT4 is not supported, should fall back to INT8
	if layer.GetPrecision() != PrecisionINT8 {
		t.Errorf("Expected layer to use supported precision INT8, got %s", layer.GetPrecision())
	}
}

// Helper functions for testing

func createTestTensor(shape []int64, dtype TensorDataType) *Tensor {
	tensor, _ := NewTensor(shape, dtype, DeviceCPU, 0)
	tensor.Fill(float32(0.5))
	return tensor
}

func createTestTensorWithData(shape []int64, dtype TensorDataType, data []float32) *Tensor {
	tensor, _ := NewTensor(shape, dtype, DeviceCPU, 0)

	// Fill with provided data, cycling if necessary
	size := tensor.Size()
	for i := int64(0); i < size; i++ {
		value := data[i%int64(len(data))]
		tensor.SetFloat32(value, i)
	}

	return tensor
}
