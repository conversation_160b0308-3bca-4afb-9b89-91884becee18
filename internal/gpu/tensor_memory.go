package gpu

import (
	"fmt"
	"math"
)

/*
#include <string.h>
*/
import "C"

// MemoryLayoutOptimizer provides automatic layout optimization for tensor operations
type MemoryLayoutOptimizer struct {
	// Cache for optimal layouts based on operation patterns
	layoutCache map[string]TensorMemoryLayout

	// Performance thresholds for layout decisions
	rowMajorThreshold int64 // Elements threshold for preferring row-major
	colMajorThreshold int64 // Elements threshold for preferring col-major
}

// NewMemoryLayoutOptimizer creates a new layout optimizer
func NewMemoryLayoutOptimizer() *MemoryLayoutOptimizer {
	return &MemoryLayoutOptimizer{
		layoutCache:       make(map[string]TensorMemoryLayout),
		rowMajorThreshold: 1000000, // 1M elements
		colMajorThreshold: 100000,  // 100K elements
	}
}

// OptimizeLayoutForOperation suggests the best memory layout for a given operation
func (opt *MemoryLayoutOptimizer) OptimizeLayoutForOperation(operation string, shapes ...TensorShape) TensorMemoryLayout {
	// Create cache key
	cacheKey := fmt.Sprintf("%s_%v", operation, shapes)

	if layout, exists := opt.layoutCache[cacheKey]; exists {
		return layout
	}

	var optimalLayout TensorMemoryLayout

	switch operation {
	case "matmul":
		// Matrix multiplication benefits from row-major for most cases
		optimalLayout = RowMajor

	case "conv2d":
		// Convolution typically benefits from row-major (NCHW format)
		optimalLayout = RowMajor

	case "transpose":
		// Transpose operation might benefit from column-major
		if len(shapes) > 0 && shapes[0].NumElements() < opt.colMajorThreshold {
			optimalLayout = ColMajor
		} else {
			optimalLayout = RowMajor
		}

	case "elementwise":
		// Element-wise operations are generally layout-agnostic, prefer row-major
		optimalLayout = RowMajor

	default:
		// Default to row-major for unknown operations
		optimalLayout = RowMajor
	}

	// Cache the decision
	opt.layoutCache[cacheKey] = optimalLayout
	return optimalLayout
}

// In-place Operations

// AddInPlace performs in-place element-wise addition
func (t *Tensor) AddInPlace(other *Tensor) error {
	if err := t.validateBinaryOp(other); err != nil {
		return err
	}

	if t.device != DeviceCPU {
		return fmt.Errorf("GPU in-place operations not yet implemented")
	}

	return t.performInPlaceElementWiseOp(other, func(a, b float64) float64 { return a + b })
}

// SubtractInPlace performs in-place element-wise subtraction
func (t *Tensor) SubtractInPlace(other *Tensor) error {
	if err := t.validateBinaryOp(other); err != nil {
		return err
	}

	if t.device != DeviceCPU {
		return fmt.Errorf("GPU in-place operations not yet implemented")
	}

	return t.performInPlaceElementWiseOp(other, func(a, b float64) float64 { return a - b })
}

// MultiplyInPlace performs in-place element-wise multiplication
func (t *Tensor) MultiplyInPlace(other *Tensor) error {
	if err := t.validateBinaryOp(other); err != nil {
		return err
	}

	if t.device != DeviceCPU {
		return fmt.Errorf("GPU in-place operations not yet implemented")
	}

	return t.performInPlaceElementWiseOp(other, func(a, b float64) float64 { return a * b })
}

// DivideInPlace performs in-place element-wise division
func (t *Tensor) DivideInPlace(other *Tensor) error {
	if err := t.validateBinaryOp(other); err != nil {
		return err
	}

	if t.device != DeviceCPU {
		return fmt.Errorf("GPU in-place operations not yet implemented")
	}

	return t.performInPlaceElementWiseOp(other, func(a, b float64) float64 {
		if b == 0 {
			return math.Inf(1) // Return positive infinity for division by zero
		}
		return a / b
	})
}

// ScaleInPlace performs in-place scalar multiplication
func (t *Tensor) ScaleInPlace(scalar float64) error {
	if t.device != DeviceCPU {
		return fmt.Errorf("GPU in-place operations not yet implemented")
	}

	numElements := t.NumElements()
	for i := int64(0); i < numElements; i++ {
		val, err := t.getElementAsFloat64(i)
		if err != nil {
			return err
		}

		err = t.setElementFromFloat64(i, val*scalar)
		if err != nil {
			return err
		}
	}

	return nil
}

// ClampInPlace performs in-place value clamping
func (t *Tensor) ClampInPlace(minVal, maxVal float64) error {
	if t.device != DeviceCPU {
		return fmt.Errorf("GPU in-place operations not yet implemented")
	}

	if minVal > maxVal {
		return fmt.Errorf("minimum value (%f) cannot be greater than maximum value (%f)", minVal, maxVal)
	}

	numElements := t.NumElements()
	for i := int64(0); i < numElements; i++ {
		val, err := t.getElementAsFloat64(i)
		if err != nil {
			return err
		}

		// Clamp value
		if val < minVal {
			val = minVal
		} else if val > maxVal {
			val = maxVal
		}

		err = t.setElementFromFloat64(i, val)
		if err != nil {
			return err
		}
	}

	return nil
}

// Advanced Memory Management

// Compact creates a new contiguous tensor from a potentially non-contiguous one
func (t *Tensor) Compact() (*Tensor, error) {
	if t.IsContiguous() {
		// Already contiguous, return a clone
		return t.Clone()
	}

	// Create new contiguous tensor
	newTensor, err := NewTensor(t.shape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	newTensor.SetMemoryPool(t.memPool)

	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU compact operation not yet implemented")
	}

	// Copy data in contiguous order
	numElements := t.NumElements()
	for i := int64(0); i < numElements; i++ {
		val, err := t.getElementAsFloat64(i)
		if err != nil {
			return nil, err
		}

		err = newTensor.setElementFromFloat64(i, val)
		if err != nil {
			return nil, err
		}
	}

	return newTensor, nil
}

// Reshape creates a new tensor with different shape (requires contiguous memory)
func (t *Tensor) Reshape(newShape TensorShape) (*Tensor, error) {
	if !newShape.IsValid() {
		return nil, fmt.Errorf("invalid new shape: %v", newShape)
	}

	if newShape.NumElements() != t.shape.NumElements() {
		return nil, fmt.Errorf("new shape %v has different number of elements than original %v",
			newShape, t.shape)
	}

	if !t.IsContiguous() {
		return nil, fmt.Errorf("cannot reshape non-contiguous tensor, use Compact() first")
	}

	// Create view with new shape
	return t.View(newShape)
}

// Permute rearranges tensor dimensions according to the given permutation
func (t *Tensor) Permute(dims []int) (*Tensor, error) {
	if len(dims) != t.Rank() {
		return nil, fmt.Errorf("permutation length (%d) must match tensor rank (%d)", len(dims), t.Rank())
	}

	// Validate permutation
	used := make([]bool, t.Rank())
	for _, dim := range dims {
		if dim < 0 || dim >= t.Rank() {
			return nil, fmt.Errorf("invalid dimension %d for tensor rank %d", dim, t.Rank())
		}
		if used[dim] {
			return nil, fmt.Errorf("dimension %d appears multiple times in permutation", dim)
		}
		used[dim] = true
	}

	// Create new shape and strides
	newShape := make(TensorShape, len(dims))
	newStrides := make(TensorStrides, len(dims))

	for i, dim := range dims {
		newShape[i] = t.shape[dim]
		newStrides[i] = t.strides[dim]
	}

	permuted := &Tensor{
		shape:      newShape,
		strides:    newStrides,
		layout:     t.layout,
		dtype:      t.dtype,
		data:       t.data,
		devicePtr:  t.devicePtr,
		device:     t.device,
		deviceID:   t.deviceID,
		ownsMemory: false, // Permutation doesn't own memory
		size:       t.size,
		offset:     t.offset,
		refCount:   t.refCount,
		memPool:    t.memPool,
	}

	// Increment reference count
	*t.refCount++

	return permuted, nil
}

// Squeeze removes dimensions of size 1
func (t *Tensor) Squeeze(dims ...int) (*Tensor, error) {
	var targetDims []int

	if len(dims) == 0 {
		// Remove all dimensions of size 1
		for i, size := range t.shape {
			if size == 1 {
				targetDims = append(targetDims, i)
			}
		}
	} else {
		// Remove specified dimensions
		for _, dim := range dims {
			if dim < 0 || dim >= t.Rank() {
				return nil, fmt.Errorf("invalid dimension %d for tensor rank %d", dim, t.Rank())
			}
			if t.shape[dim] != 1 {
				return nil, fmt.Errorf("cannot squeeze dimension %d with size %d", dim, t.shape[dim])
			}
			targetDims = append(targetDims, dim)
		}
	}

	// Create new shape excluding squeezed dimensions
	newShape := make(TensorShape, 0, t.Rank()-len(targetDims))
	newStrides := make(TensorStrides, 0, t.Rank()-len(targetDims))

	squeezed := make(map[int]bool)
	for _, dim := range targetDims {
		squeezed[dim] = true
	}

	for i := 0; i < t.Rank(); i++ {
		if !squeezed[i] {
			newShape = append(newShape, t.shape[i])
			newStrides = append(newStrides, t.strides[i])
		}
	}

	// Handle case where all dimensions are squeezed (scalar)
	if len(newShape) == 0 {
		newShape = TensorShape{1}
		newStrides = TensorStrides{1}
	}

	result := &Tensor{
		shape:      newShape,
		strides:    newStrides,
		layout:     t.layout,
		dtype:      t.dtype,
		data:       t.data,
		devicePtr:  t.devicePtr,
		device:     t.device,
		deviceID:   t.deviceID,
		ownsMemory: false, // Squeeze doesn't own memory
		size:       t.size,
		offset:     t.offset,
		refCount:   t.refCount,
		memPool:    t.memPool,
	}

	// Increment reference count
	*t.refCount++

	return result, nil
}

// Unsqueeze adds dimensions of size 1
func (t *Tensor) Unsqueeze(dims ...int) (*Tensor, error) {
	if len(dims) == 0 {
		return nil, fmt.Errorf("must specify at least one dimension to unsqueeze")
	}

	newRank := t.Rank() + len(dims)

	// Validate dimensions
	for _, dim := range dims {
		if dim < 0 || dim >= newRank {
			return nil, fmt.Errorf("invalid dimension %d for new tensor rank %d", dim, newRank)
		}
	}

	// Create new shape and strides
	newShape := make(TensorShape, newRank)
	newStrides := make(TensorStrides, newRank)

	// Mark unsqueezed dimensions
	unsqueezed := make(map[int]bool)
	for _, dim := range dims {
		unsqueezed[dim] = true
	}

	oldIdx := 0
	for i := 0; i < newRank; i++ {
		if unsqueezed[i] {
			newShape[i] = 1
			// Set stride to be compatible with existing layout
			if i == newRank-1 {
				newStrides[i] = 1
			} else {
				// Use the stride of the next non-unsqueezed dimension
				nextIdx := oldIdx
				for j := i + 1; j < newRank && unsqueezed[j]; j++ {
					// Skip other unsqueezed dimensions
				}
				if nextIdx < t.Rank() {
					newStrides[i] = t.strides[nextIdx]
				} else {
					newStrides[i] = 1
				}
			}
		} else {
			newShape[i] = t.shape[oldIdx]
			newStrides[i] = t.strides[oldIdx]
			oldIdx++
		}
	}

	result := &Tensor{
		shape:      newShape,
		strides:    newStrides,
		layout:     t.layout,
		dtype:      t.dtype,
		data:       t.data,
		devicePtr:  t.devicePtr,
		device:     t.device,
		deviceID:   t.deviceID,
		ownsMemory: false, // Unsqueeze doesn't own memory
		size:       t.size,
		offset:     t.offset,
		refCount:   t.refCount,
		memPool:    t.memPool,
	}

	// Increment reference count
	*t.refCount++

	return result, nil
}

// Memory Layout Conversion

// ToRowMajor converts tensor to row-major layout
func (t *Tensor) ToRowMajor() (*Tensor, error) {
	if t.layout == RowMajor && t.IsContiguous() {
		return t.Clone()
	}

	// Create new tensor with row-major layout
	newTensor, err := NewTensor(t.shape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	newTensor.SetMemoryPool(t.memPool)
	newTensor.layout = RowMajor

	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU layout conversion not yet implemented")
	}

	// Copy data in row-major order
	return t.copyWithLayout(newTensor, RowMajor)
}

// ToColMajor converts tensor to column-major layout
func (t *Tensor) ToColMajor() (*Tensor, error) {
	if t.layout == ColMajor && t.IsContiguous() {
		return t.Clone()
	}

	// Create new tensor with column-major layout
	newTensor, err := NewTensor(t.shape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	newTensor.SetMemoryPool(t.memPool)
	newTensor.layout = ColMajor

	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU layout conversion not yet implemented")
	}

	// Copy data in column-major order
	return t.copyWithLayout(newTensor, ColMajor)
}

// Helper Methods

// performInPlaceElementWiseOp performs in-place element-wise operations
func (t *Tensor) performInPlaceElementWiseOp(other *Tensor, op func(float64, float64) float64) error {
	numElements := t.NumElements()

	for i := int64(0); i < numElements; i++ {
		var a, b float64
		var err error

		// Get values from both tensors
		a, err = t.getElementAsFloat64(i)
		if err != nil {
			return err
		}

		b, err = other.getElementAsFloat64(i)
		if err != nil {
			return err
		}

		// Perform operation and set result
		resultValue := op(a, b)
		err = t.setElementFromFloat64(i, resultValue)
		if err != nil {
			return err
		}
	}

	return nil
}

// copyWithLayout copies tensor data with specified layout
func (t *Tensor) copyWithLayout(dest *Tensor, layout TensorMemoryLayout) (*Tensor, error) {
	// For now, implement simple copy - layout optimization can be added later
	numElements := t.NumElements()

	for i := int64(0); i < numElements; i++ {
		val, err := t.getElementAsFloat64(i)
		if err != nil {
			return nil, err
		}

		err = dest.setElementFromFloat64(i, val)
		if err != nil {
			return nil, err
		}
	}

	dest.layout = layout
	return dest, nil
}

// Memory Statistics and Analysis

// MemoryStats represents memory usage statistics for a tensor
type MemoryStats struct {
	TotalSize         int64   // Total memory size in bytes
	ElementSize       int     // Size per element in bytes
	NumElements       int64   // Total number of elements
	IsContiguous      bool    // Whether memory is contiguous
	Layout            string  // Memory layout (row_major/col_major)
	Device            string  // Device location (cpu/gpu)
	MemoryUtilization float64 // Percentage of memory actually used
}

// GetMemoryStats returns detailed memory statistics for the tensor
func (t *Tensor) GetMemoryStats() MemoryStats {
	utilization := 100.0
	if t.size > 0 {
		actualSize := t.NumElements() * int64(t.dtype.Size())
		utilization = (float64(actualSize) / float64(t.size)) * 100.0
	}

	return MemoryStats{
		TotalSize:         t.size,
		ElementSize:       t.dtype.Size(),
		NumElements:       t.NumElements(),
		IsContiguous:      t.IsContiguous(),
		Layout:            t.layout.String(),
		Device:            t.device.String(),
		MemoryUtilization: utilization,
	}
}

// EstimateMemoryFootprint estimates memory footprint for a given operation
func EstimateMemoryFootprint(operation string, inputShapes []TensorShape, dtype TensorDataType) int64 {
	var totalMemory int64
	elementSize := int64(dtype.Size())

	switch operation {
	case "conv2d":
		// Estimate memory for convolution: input + kernel + output + workspace
		if len(inputShapes) >= 2 {
			inputSize := inputShapes[0].NumElements() * elementSize
			kernelSize := inputShapes[1].NumElements() * elementSize
			// Rough output size estimation (will vary based on parameters)
			outputSize := inputSize        // Conservative estimate
			workspaceSize := inputSize / 4 // Workspace for im2col or similar
			totalMemory = inputSize + kernelSize + outputSize + workspaceSize
		}

	case "matmul":
		// Estimate memory for matrix multiplication: A + B + C
		if len(inputShapes) >= 2 {
			aSize := inputShapes[0].NumElements() * elementSize
			bSize := inputShapes[1].NumElements() * elementSize
			// Output size for A[m,k] * B[k,n] = C[m,n]
			if len(inputShapes[0]) >= 2 && len(inputShapes[1]) >= 2 {
				m := inputShapes[0][0]
				n := inputShapes[1][1]
				cSize := m * n * elementSize
				totalMemory = aSize + bSize + cSize
			} else {
				totalMemory = aSize + bSize + aSize // Conservative fallback
			}
		}

	case "elementwise":
		// Estimate memory for element-wise operations: input1 + input2 + output
		for _, shape := range inputShapes {
			totalMemory += shape.NumElements() * elementSize
		}
		if len(inputShapes) > 0 {
			totalMemory += inputShapes[0].NumElements() * elementSize // Output
		}

	default:
		// Conservative estimate: sum of all inputs plus largest input for output
		var maxSize int64
		for _, shape := range inputShapes {
			size := shape.NumElements() * elementSize
			totalMemory += size
			if size > maxSize {
				maxSize = size
			}
		}
		totalMemory += maxSize // Output
	}

	return totalMemory
}
