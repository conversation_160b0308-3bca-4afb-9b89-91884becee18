package gpu

import (
	"container/heap"
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// ClusterScheduler manages task scheduling across the GPU cluster
type ClusterScheduler struct {
	distributor    *ClusterWorkloadDistributor
	taskQueue      *PriorityTaskQueue
	runningTasks   map[string]*ScheduledTask
	completedTasks map[string]*ScheduledTask
	logger         *log.Logger
	mu             sync.RWMutex

	// Scheduling state
	isRunning bool
	stopChan  chan struct{}
	wg        sync.WaitGroup

	// Configuration
	config SchedulerConfig

	// Metrics
	metrics *SchedulerMetrics
}

// SchedulerConfig contains configuration for the cluster scheduler
type SchedulerConfig struct {
	MaxConcurrentTasks     int           `json:"max_concurrent_tasks"`
	SchedulingInterval     time.Duration `json:"scheduling_interval"`
	TaskTimeoutDuration    time.Duration `json:"task_timeout_duration"`
	RetryDelayBase         time.Duration `json:"retry_delay_base"`
	RetryDelayMax          time.Duration `json:"retry_delay_max"`
	MaxRetryAttempts       int           `json:"max_retry_attempts"`
	DeadlineGracePeriod    time.Duration `json:"deadline_grace_period"`
	ResourceReservationTTL time.Duration `json:"resource_reservation_ttl"`
	EnablePreemption       bool          `json:"enable_preemption"`
	PreemptionThreshold    float64       `json:"preemption_threshold"`
}

// ScheduledTask represents a task in the scheduler
type ScheduledTask struct {
	Task          *ClusterTask           `json:"task"`
	Assignment    *ClusterTaskAssignment `json:"assignment"`
	Status        TaskScheduleStatus     `json:"status"`
	SubmittedAt   time.Time              `json:"submitted_at"`
	ScheduledAt   time.Time              `json:"scheduled_at"`
	StartedAt     time.Time              `json:"started_at"`
	CompletedAt   time.Time              `json:"completed_at"`
	Deadline      time.Time              `json:"deadline,omitempty"`
	RetryCount    int                    `json:"retry_count"`
	LastError     string                 `json:"last_error,omitempty"`
	ResourceLocks []string               `json:"resource_locks"`
	Dependencies  []*ScheduledTask       `json:"dependencies,omitempty"`
	Dependents    []*ScheduledTask       `json:"dependents,omitempty"`
}

// TaskScheduleStatus represents the status of a scheduled task
type TaskScheduleStatus string

const (
	TaskStatusQueued    TaskScheduleStatus = "queued"
	TaskStatusScheduled TaskScheduleStatus = "scheduled"
	TaskStatusRunning   TaskScheduleStatus = "running"
	TaskStatusCompleted TaskScheduleStatus = "completed"
	TaskStatusFailed    TaskScheduleStatus = "failed"
	TaskStatusCancelled TaskScheduleStatus = "cancelled"
	TaskStatusTimeout   TaskScheduleStatus = "timeout"
	TaskStatusPreempted TaskScheduleStatus = "preempted"
)

// SchedulerMetrics tracks scheduler performance
type SchedulerMetrics struct {
	TasksSubmitted      int64         `json:"tasks_submitted"`
	TasksCompleted      int64         `json:"tasks_completed"`
	TasksFailed         int64         `json:"tasks_failed"`
	TasksCancelled      int64         `json:"tasks_cancelled"`
	TasksTimeout        int64         `json:"tasks_timeout"`
	TasksPreempted      int64         `json:"tasks_preempted"`
	AverageQueueTime    time.Duration `json:"average_queue_time"`
	AverageRunTime      time.Duration `json:"average_run_time"`
	ThroughputPerHour   float64       `json:"throughput_per_hour"`
	ResourceUtilization float64       `json:"resource_utilization"`
	LastUpdated         time.Time     `json:"last_updated"`
}

// PriorityTaskQueue implements a priority queue for tasks
type PriorityTaskQueue struct {
	tasks []*ScheduledTask
	mu    sync.RWMutex
}

// NewClusterScheduler creates a new cluster scheduler
func NewClusterScheduler(distributor *ClusterWorkloadDistributor, logger *log.Logger) (*ClusterScheduler, error) {
	if logger == nil {
		logger = log.Default()
	}

	scheduler := &ClusterScheduler{
		distributor:    distributor,
		taskQueue:      NewPriorityTaskQueue(),
		runningTasks:   make(map[string]*ScheduledTask),
		completedTasks: make(map[string]*ScheduledTask),
		logger:         logger,
		config:         DefaultSchedulerConfig(),
		metrics:        &SchedulerMetrics{},
	}

	return scheduler, nil
}

// DefaultSchedulerConfig returns default scheduler configuration
func DefaultSchedulerConfig() SchedulerConfig {
	return SchedulerConfig{
		MaxConcurrentTasks:     100,
		SchedulingInterval:     time.Second * 5,
		TaskTimeoutDuration:    time.Hour * 2,
		RetryDelayBase:         time.Second * 30,
		RetryDelayMax:          time.Minute * 10,
		MaxRetryAttempts:       3,
		DeadlineGracePeriod:    time.Minute * 5,
		ResourceReservationTTL: time.Minute * 15,
		EnablePreemption:       true,
		PreemptionThreshold:    0.8,
	}
}

// Start begins the scheduler operation
func (cs *ClusterScheduler) Start(ctx context.Context) error {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	if cs.isRunning {
		return fmt.Errorf("scheduler already running")
	}

	cs.isRunning = true
	cs.stopChan = make(chan struct{})

	// Start scheduling loop
	cs.wg.Add(1)
	go cs.schedulingLoop(ctx)

	// Start metrics update loop
	cs.wg.Add(1)
	go cs.metricsLoop(ctx)

	cs.logger.Printf("Cluster scheduler started")
	return nil
}

// Stop stops the scheduler
func (cs *ClusterScheduler) Stop() error {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	if !cs.isRunning {
		return fmt.Errorf("scheduler not running")
	}

	close(cs.stopChan)
	cs.wg.Wait()
	cs.isRunning = false

	cs.logger.Printf("Cluster scheduler stopped")
	return nil
}

// SubmitTask submits a task for scheduling
func (cs *ClusterScheduler) SubmitTask(task *ClusterTask) (*ScheduledTask, error) {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	scheduledTask := &ScheduledTask{
		Task:        task,
		Status:      TaskStatusQueued,
		SubmittedAt: time.Now(),
		RetryCount:  0,
	}

	// Set deadline if specified
	if task.TimeoutDuration > 0 {
		scheduledTask.Deadline = time.Now().Add(task.TimeoutDuration)
	}

	// Add to queue
	cs.taskQueue.Push(scheduledTask)
	cs.metrics.TasksSubmitted++

	cs.logger.Printf("Submitted task %s to scheduler queue", task.ID)
	return scheduledTask, nil
}

// CancelTask cancels a queued or running task
func (cs *ClusterScheduler) CancelTask(taskID string) error {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	// Check if task is running
	if runningTask, exists := cs.runningTasks[taskID]; exists {
		runningTask.Status = TaskStatusCancelled
		runningTask.CompletedAt = time.Now()
		delete(cs.runningTasks, taskID)
		cs.completedTasks[taskID] = runningTask
		cs.metrics.TasksCancelled++
		cs.logger.Printf("Cancelled running task %s", taskID)
		return nil
	}

	// Check if task is in queue
	if cs.taskQueue.Remove(taskID) {
		cs.metrics.TasksCancelled++
		cs.logger.Printf("Cancelled queued task %s", taskID)
		return nil
	}

	return fmt.Errorf("task %s not found", taskID)
}

// GetTaskStatus returns the status of a task
func (cs *ClusterScheduler) GetTaskStatus(taskID string) (*ScheduledTask, error) {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	// Check running tasks
	if task, exists := cs.runningTasks[taskID]; exists {
		return task, nil
	}

	// Check completed tasks
	if task, exists := cs.completedTasks[taskID]; exists {
		return task, nil
	}

	// Check queued tasks
	if task := cs.taskQueue.Find(taskID); task != nil {
		return task, nil
	}

	return nil, fmt.Errorf("task %s not found", taskID)
}

// schedulingLoop is the main scheduling loop
func (cs *ClusterScheduler) schedulingLoop(ctx context.Context) {
	defer cs.wg.Done()

	ticker := time.NewTicker(cs.config.SchedulingInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-cs.stopChan:
			return
		case <-ticker.C:
			cs.processSchedulingCycle(ctx)
		}
	}
}

// processSchedulingCycle processes one scheduling cycle
func (cs *ClusterScheduler) processSchedulingCycle(ctx context.Context) {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	// Check for timed out tasks
	cs.checkTimeouts()

	// Check for completed tasks
	cs.checkCompletedTasks()

	// Schedule new tasks if capacity allows
	if len(cs.runningTasks) < cs.config.MaxConcurrentTasks {
		cs.scheduleNextTasks(ctx)
	}

	// Handle preemption if enabled
	if cs.config.EnablePreemption {
		cs.handlePreemption(ctx)
	}
}

// scheduleNextTasks attempts to schedule the next available tasks
func (cs *ClusterScheduler) scheduleNextTasks(ctx context.Context) {
	for len(cs.runningTasks) < cs.config.MaxConcurrentTasks && !cs.taskQueue.IsEmpty() {
		task := cs.taskQueue.PopTask()
		if task == nil {
			break
		}

		// Check if task dependencies are satisfied
		if !cs.areDependenciesSatisfied(task) {
			// Put task back in queue
			cs.taskQueue.Push(task)
			break
		}

		// Try to assign resources
		assignment, err := cs.distributor.AssignClusterTask(ctx, task.Task)
		if err != nil {
			cs.logger.Printf("Failed to assign task %s: %v", task.Task.ID, err)
			cs.handleTaskFailure(task, err.Error())
			continue
		}

		// Start task execution
		task.Assignment = assignment
		task.Status = TaskStatusRunning
		task.ScheduledAt = time.Now()
		task.StartedAt = time.Now()
		cs.runningTasks[task.Task.ID] = task

		cs.logger.Printf("Started execution of task %s on node %s",
			task.Task.ID, assignment.PrimaryNode.ID)
	}
}

// checkTimeouts checks for timed out tasks
func (cs *ClusterScheduler) checkTimeouts() {
	now := time.Now()

	for taskID, task := range cs.runningTasks {
		if !task.Deadline.IsZero() && now.After(task.Deadline) {
			cs.logger.Printf("Task %s timed out", taskID)
			task.Status = TaskStatusTimeout
			task.CompletedAt = now
			delete(cs.runningTasks, taskID)
			cs.completedTasks[taskID] = task
			cs.metrics.TasksTimeout++
		}
	}
}

// checkCompletedTasks checks for tasks that have completed
func (cs *ClusterScheduler) checkCompletedTasks() {
	// This would integrate with actual task execution monitoring
	// For now, this is a placeholder for the completion detection logic
}

// handlePreemption handles task preemption for higher priority tasks
func (cs *ClusterScheduler) handlePreemption(ctx context.Context) {
	if cs.taskQueue.IsEmpty() {
		return
	}

	// Get highest priority queued task
	nextTask := cs.taskQueue.Peek()
	if nextTask == nil {
		return
	}

	// Find lowest priority running task that could be preempted
	var candidateForPreemption *ScheduledTask
	lowestPriority := nextTask.Task.Priority

	for _, runningTask := range cs.runningTasks {
		if runningTask.Task.Priority < lowestPriority {
			candidateForPreemption = runningTask
			lowestPriority = runningTask.Task.Priority
		}
	}

	if candidateForPreemption != nil {
		cs.logger.Printf("Preempting task %s for higher priority task %s",
			candidateForPreemption.Task.ID, nextTask.Task.ID)

		// Mark as preempted and move back to queue
		candidateForPreemption.Status = TaskStatusPreempted
		delete(cs.runningTasks, candidateForPreemption.Task.ID)
		cs.taskQueue.Push(candidateForPreemption)
		cs.metrics.TasksPreempted++
	}
}

// areDependenciesSatisfied checks if all task dependencies are completed
func (cs *ClusterScheduler) areDependenciesSatisfied(task *ScheduledTask) bool {
	for _, depID := range task.Task.Dependencies {
		if completedTask, exists := cs.completedTasks[depID]; !exists ||
			completedTask.Status != TaskStatusCompleted {
			return false
		}
	}
	return true
}

// handleTaskFailure handles task failure and retry logic
func (cs *ClusterScheduler) handleTaskFailure(task *ScheduledTask, errorMsg string) {
	task.LastError = errorMsg
	task.RetryCount++

	if task.RetryCount <= cs.config.MaxRetryAttempts {
		// Calculate retry delay with exponential backoff
		delay := cs.config.RetryDelayBase * time.Duration(1<<(task.RetryCount-1))
		if delay > cs.config.RetryDelayMax {
			delay = cs.config.RetryDelayMax
		}

		// Schedule retry
		go func() {
			time.Sleep(delay)
			cs.mu.Lock()
			defer cs.mu.Unlock()
			task.Status = TaskStatusQueued
			cs.taskQueue.Push(task)
			cs.logger.Printf("Retrying task %s (attempt %d)", task.Task.ID, task.RetryCount)
		}()
	} else {
		// Max retries exceeded
		task.Status = TaskStatusFailed
		task.CompletedAt = time.Now()
		cs.completedTasks[task.Task.ID] = task
		cs.metrics.TasksFailed++
		cs.logger.Printf("Task %s failed after %d attempts", task.Task.ID, task.RetryCount)
	}
}

// metricsLoop updates scheduler metrics periodically
func (cs *ClusterScheduler) metricsLoop(ctx context.Context) {
	defer cs.wg.Done()

	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-cs.stopChan:
			return
		case <-ticker.C:
			cs.updateMetrics()
		}
	}
}

// updateMetrics calculates and updates scheduler metrics
func (cs *ClusterScheduler) updateMetrics() {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	now := time.Now()

	// Calculate average queue time and run time
	var totalQueueTime, totalRunTime time.Duration
	completedCount := int64(len(cs.completedTasks))

	for _, task := range cs.completedTasks {
		if !task.ScheduledAt.IsZero() {
			totalQueueTime += task.ScheduledAt.Sub(task.SubmittedAt)
		}
		if !task.CompletedAt.IsZero() && !task.StartedAt.IsZero() {
			totalRunTime += task.CompletedAt.Sub(task.StartedAt)
		}
	}

	if completedCount > 0 {
		cs.metrics.AverageQueueTime = totalQueueTime / time.Duration(completedCount)
		cs.metrics.AverageRunTime = totalRunTime / time.Duration(completedCount)
	}

	// Calculate throughput (tasks per hour)
	if completedCount > 0 {
		oldestTask := time.Now()
		for _, task := range cs.completedTasks {
			if task.SubmittedAt.Before(oldestTask) {
				oldestTask = task.SubmittedAt
			}
		}

		duration := now.Sub(oldestTask)
		if duration > 0 {
			cs.metrics.ThroughputPerHour = float64(completedCount) / duration.Hours()
		}
	}

	// Calculate resource utilization
	totalCapacity := float64(cs.config.MaxConcurrentTasks)
	currentUtilization := float64(len(cs.runningTasks))
	cs.metrics.ResourceUtilization = currentUtilization / totalCapacity

	cs.metrics.LastUpdated = now
}

// GetMetrics returns current scheduler metrics
func (cs *ClusterScheduler) GetMetrics() SchedulerMetrics {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	return *cs.metrics
}

// NewPriorityTaskQueue creates a new priority task queue
func NewPriorityTaskQueue() *PriorityTaskQueue {
	return &PriorityTaskQueue{
		tasks: make([]*ScheduledTask, 0),
	}
}

// Priority queue implementation
func (pq *PriorityTaskQueue) Len() int {
	pq.mu.RLock()
	defer pq.mu.RUnlock()
	return len(pq.tasks)
}

func (pq *PriorityTaskQueue) Less(i, j int) bool {
	// Higher priority first, then earlier submission time
	if pq.tasks[i].Task.Priority != pq.tasks[j].Task.Priority {
		return pq.tasks[i].Task.Priority > pq.tasks[j].Task.Priority
	}
	return pq.tasks[i].SubmittedAt.Before(pq.tasks[j].SubmittedAt)
}

func (pq *PriorityTaskQueue) Swap(i, j int) {
	pq.tasks[i], pq.tasks[j] = pq.tasks[j], pq.tasks[i]
}

func (pq *PriorityTaskQueue) Push(x interface{}) {
	pq.mu.Lock()
	defer pq.mu.Unlock()
	pq.tasks = append(pq.tasks, x.(*ScheduledTask))
	heap.Fix(pq, len(pq.tasks)-1)
}

func (pq *PriorityTaskQueue) Pop() any {
	old := pq.tasks
	n := len(old)
	if n == 0 {
		return nil
	}
	task := old[n-1]
	pq.tasks = old[0 : n-1]
	return task
}

func (pq *PriorityTaskQueue) PopTask() *ScheduledTask {
	pq.mu.Lock()
	defer pq.mu.Unlock()
	if len(pq.tasks) == 0 {
		return nil
	}
	task := heap.Pop(pq).(*ScheduledTask)
	return task
}

func (pq *PriorityTaskQueue) Peek() *ScheduledTask {
	pq.mu.RLock()
	defer pq.mu.RUnlock()
	if len(pq.tasks) == 0 {
		return nil
	}
	return pq.tasks[0]
}

func (pq *PriorityTaskQueue) IsEmpty() bool {
	pq.mu.RLock()
	defer pq.mu.RUnlock()
	return len(pq.tasks) == 0
}

func (pq *PriorityTaskQueue) Find(taskID string) *ScheduledTask {
	pq.mu.RLock()
	defer pq.mu.RUnlock()
	for _, task := range pq.tasks {
		if task.Task.ID == taskID {
			return task
		}
	}
	return nil
}

func (pq *PriorityTaskQueue) Remove(taskID string) bool {
	pq.mu.Lock()
	defer pq.mu.Unlock()
	for i, task := range pq.tasks {
		if task.Task.ID == taskID {
			heap.Remove(pq, i)
			return true
		}
	}
	return false
}
