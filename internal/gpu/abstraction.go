package gpu

import (
	"context"
	"fmt"
	"runtime"
	"sort"
	"sync"

	"neuralmetergo/internal/gpu/types"
)

// Type aliases for backward compatibility
type GPUDevice = types.GPUDevice
type GPUContext = types.GPUContext
type GPUBackend = types.GPUBackend
type GPUCapability = types.GPUCapability
type GPUFeature = types.GPUFeature
type GPUMemoryManager = types.GPUMemoryManager
type GPUBuffer = types.GPUBuffer
type GPUExecutor = types.GPUExecutor
type GPUKernel = types.GPUKernel
type GPUEvent = types.GPUEvent
type MemoryInfo = types.MemoryInfo
type MemoryFlags = types.MemoryFlags

// Constants for backward compatibility
const (
	FeatureCompute         = types.FeatureCompute
	FeatureGraphics        = types.FeatureGraphics
	FeatureFloat16         = types.FeatureFloat16
	FeatureInt8            = types.FeatureInt8
	FeatureAsyncCompute    = types.FeatureAsyncCompute
	FeatureUnifiedMemory   = types.FeatureUnifiedMemory
	FeatureMemoryCoherence = types.FeatureMemoryCoherence
	FeatureRayTracing      = types.FeatureRayTracing
	FeatureTensorOps       = types.FeatureTensorOps
)

const (
	MemoryFlagDefault     = types.MemoryFlagDefault
	MemoryFlagReadOnly    = types.MemoryFlagReadOnly
	MemoryFlagWriteOnly   = types.MemoryFlagWriteOnly
	MemoryFlagReadWrite   = types.MemoryFlagReadWrite
	MemoryFlagHostVisible = types.MemoryFlagHostVisible
	MemoryFlagCoherent    = types.MemoryFlagCoherent
	MemoryFlagCached      = types.MemoryFlagCached
)

// BackendRegistry manages available GPU backends
type BackendRegistry struct {
	backends map[string]types.GPUBackend
	priority map[string]int
	mutex    sync.RWMutex
}

// NewBackendRegistry creates a new backend registry
func NewBackendRegistry() *BackendRegistry {
	return &BackendRegistry{
		backends: make(map[string]types.GPUBackend),
		priority: make(map[string]int),
	}
}

// Register adds a backend to the registry
func (r *BackendRegistry) Register(name string, backend types.GPUBackend, priority int) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.backends[name] = backend
	r.priority[name] = priority
}

// GetBackend retrieves a backend by name
func (r *BackendRegistry) GetBackend(name string) (types.GPUBackend, bool) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	backend, exists := r.backends[name]
	return backend, exists
}

// GetOrderedBackends returns backends ordered by priority
func (r *BackendRegistry) GetOrderedBackends() []types.GPUBackend {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	type backendPriority struct {
		backend  types.GPUBackend
		priority int
	}

	var backends []backendPriority
	for name, backend := range r.backends {
		backends = append(backends, backendPriority{
			backend:  backend,
			priority: r.priority[name],
		})
	}

	// Sort by priority (higher values first)
	sort.Slice(backends, func(i, j int) bool {
		return backends[i].priority > backends[j].priority
	})

	result := make([]types.GPUBackend, len(backends))
	for i, bp := range backends {
		result[i] = bp.backend
	}

	return result
}

// ListBackends returns the names of all registered backends
func (r *BackendRegistry) ListBackends() []string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	names := make([]string, 0, len(r.backends))
	for name := range r.backends {
		names = append(names, name)
	}

	sort.Strings(names)
	return names
}

// GetBestBackend returns the highest priority backend that has available devices
func (r *BackendRegistry) GetBestBackend(ctx context.Context) (types.GPUBackend, error) {
	orderedBackends := r.GetOrderedBackends()

	for _, backend := range orderedBackends {
		devices, err := backend.EnumerateDevices(ctx)
		if err == nil && len(devices) > 0 {
			return backend, nil
		}
	}

	return nil, fmt.Errorf("no available GPU backends found")
}

// Platform detection utilities
func GetCurrentPlatform() string {
	return runtime.GOOS
}

func GetCurrentArchitecture() string {
	return runtime.GOARCH
}

func IsAppleSilicon() bool {
	return runtime.GOOS == "darwin" && runtime.GOARCH == "arm64"
}

func IsMacOS() bool {
	return runtime.GOOS == "darwin"
}

func IsWindows() bool {
	return runtime.GOOS == "windows"
}

func IsLinux() bool {
	return runtime.GOOS == "linux"
}
