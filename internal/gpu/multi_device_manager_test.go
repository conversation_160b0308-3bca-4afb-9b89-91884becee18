package gpu

import (
	"log"
	"testing"
)

func TestMultiDeviceManagerCreation(t *testing.T) {
	config := DefaultMultiDeviceConfig()
	config.MonitoringInterval = 0 // Disable monitoring for test

	logger := log.Default()

	mgr, err := NewMultiDeviceManager(config, logger)
	if err != nil {
		t.Fatalf("Failed to create multi-device manager: %v", err)
	}

	if mgr == nil {
		t.<PERSON><PERSON>("Manager is nil")
	}

	if mgr.IsInitialized() {
		t.<PERSON>rror("Manager should not be initialized yet")
	}

	if len(mgr.GetDevices()) != 0 {
		t.Error("Should have no devices before initialization")
	}
}

func TestLoadBalancingStrategyString(t *testing.T) {
	tests := []struct {
		strategy LoadBalancingStrategy
		expected string
	}{
		{LoadBalanceRoundRobin, "round_robin"},
		{LoadBalanceMemoryBased, "memory_based"},
		{LoadBalanceComputeBased, "compute_based"},
		{LoadBalanceDynamic, "dynamic"},
		{LoadBalanceWeighted, "weighted"},
	}

	for _, test := range tests {
		if test.strategy.String() != test.expected {
			t.Errorf("Strategy %d should return %s, got %s",
				test.strategy, test.expected, test.strategy.String())
		}
	}
}

func TestSynchronizationManagerCreation(t *testing.T) {
	logger := log.Default()
	syncMgr := NewSynchronizationManager(logger)

	if syncMgr == nil {
		t.Fatal("Synchronization manager is nil")
	}

	if syncMgr.IsInitialized() {
		t.Error("Synchronization manager should not be initialized yet")
	}

	// Test cleanup on uninitialized manager
	err := syncMgr.Cleanup()
	if err != nil {
		t.Errorf("Cleanup on uninitialized manager should not fail: %v", err)
	}
}

func TestMultiDeviceConfigValidation(t *testing.T) {
	tests := []struct {
		name       string
		config     MultiDeviceConfig
		shouldFail bool
	}{
		{
			name:       "Valid config",
			config:     DefaultMultiDeviceConfig(),
			shouldFail: false,
		},
		{
			name: "Invalid max devices",
			config: MultiDeviceConfig{
				MaxDevices: 0,
				MinDevices: 1,
			},
			shouldFail: true,
		},
		{
			name: "Invalid min devices",
			config: MultiDeviceConfig{
				MaxDevices: 2,
				MinDevices: 0,
			},
			shouldFail: true,
		},
		{
			name: "Min > Max devices",
			config: MultiDeviceConfig{
				MaxDevices: 1,
				MinDevices: 2,
			},
			shouldFail: true,
		},
	}

	logger := log.Default()

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			_, err := NewMultiDeviceManager(test.config, logger)
			if test.shouldFail && err == nil {
				t.Errorf("Expected configuration validation to fail for %s", test.name)
			}
			if !test.shouldFail && err != nil {
				t.Errorf("Expected configuration validation to succeed for %s: %v", test.name, err)
			}
		})
	}
}
