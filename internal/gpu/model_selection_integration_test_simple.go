package gpu

import (
	"log"
	"os"
	"testing"
	"time"
)

func TestModelSelectionIntegrationBasic(t *testing.T) {
	// Create predictor
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    10,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)

	// Create logger
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	// Create integration
	integration := NewModelSelectionIntegration(predictor, logger)

	// Validate integration
	if integration == nil {
		t.Fatal("Expected integration instance, got nil")
	}

	if integration.predictor != predictor {
		t.Error("Expected predictor to be set")
	}

	if integration.optimizer == nil {
		t.Error("Expected optimizer to be initialized")
	}

	if integration.featureEngineer == nil {
		t.<PERSON>r("Expected feature engineer to be initialized")
	}

	// Test configuration
	status := integration.GetOptimizationStatus()
	if status.OptimizationInterval != time.Hour*24 {
		t.<PERSON>rf("Expected optimization interval of 24h, got %v", status.OptimizationInterval)
	}

	if !status.IsOptimizationDue {
		t.Error("Expected optimization to be due initially")
	}
}

func TestModelSelectionOptimizerBasic(t *testing.T) {
	// Create predictor and feature engineer first
	config := PredictionConfig{
		HistoryWindow:    time.Hour * 24,
		MinDataPoints:    10,
		ModelType:        "linear_regression",
		UpdateInterval:   time.Minute,
		SeasonalPatterns: false,
	}
	predictor := NewWorkloadPredictor(config)
	featureEngineer := NewFeatureEngineer()
	logger := log.New(os.Stdout, "TEST: ", log.LstdFlags)

	// Test basic optimizer functionality
	optimizerConfig := DefaultModelSelectionConfig()
	optimizer := NewModelSelectionOptimizer(optimizerConfig, predictor, featureEngineer, logger)

	if optimizer == nil {
		t.Fatal("Expected optimizer instance, got nil")
	}

	t.Logf("Optimizer created successfully")
}

func TestFeatureEngineerBasic(t *testing.T) {
	// Test basic feature engineering functionality
	engineer := NewFeatureEngineer()
	if engineer == nil {
		t.Fatal("Expected feature engineer instance, got nil")
	}

	// Create test data point
	baseTime := time.Now()
	point := WorkloadDataPoint{
		Timestamp:      baseTime,
		QueueLength:    10,
		ActiveTasks:    5,
		AvgUtilization: 0.75,
		NodesActive:    3,
	}

	// Test feature extraction
	enhanced := engineer.ExtractAllFeatures(point, []WorkloadDataPoint{})

	// Verify basic features are preserved
	if enhanced.Timestamp != point.Timestamp {
		t.Error("Timestamp should be preserved")
	}
	if enhanced.QueueLength != point.QueueLength {
		t.Error("QueueLength should be preserved")
	}
	if enhanced.ActiveTasks != point.ActiveTasks {
		t.Error("ActiveTasks should be preserved")
	}

	// Verify temporal features are computed
	if enhanced.HourOfDay < 0 || enhanced.HourOfDay > 23 {
		t.Errorf("HourOfDay should be 0-23, got %d", enhanced.HourOfDay)
	}
	if enhanced.DayOfWeek < 0 || enhanced.DayOfWeek > 6 {
		t.Errorf("DayOfWeek should be 0-6, got %d", enhanced.DayOfWeek)
	}

	// Test feature vector extraction
	featureVector := engineer.ExtractFeatureVector(enhanced)
	if len(featureVector) == 0 {
		t.Error("Feature vector should not be empty")
	}

	t.Logf("Feature extraction completed successfully")
	t.Logf("  Hour of day: %d", enhanced.HourOfDay)
	t.Logf("  Day of week: %d", enhanced.DayOfWeek)
	t.Logf("  Hour sin/cos: %.3f/%.3f", enhanced.HourSin, enhanced.HourCos)
	t.Logf("  Feature vector length: %d", len(featureVector))
}
