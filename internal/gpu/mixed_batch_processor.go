package gpu

import (
	"context"
	"fmt"
	"log"
	"math"
	"sort"
	"sync"
	"time"
)

// PaddingStrategy defines how to pad inputs to make them compatible
type PaddingStrategy int

const (
	PaddingZero PaddingStrategy = iota
	PaddingEdge
	PaddingReflection
	PaddingMirror
)

// String returns the string representation of padding strategy
func (p PaddingStrategy) String() string {
	switch p {
	case PaddingZero:
		return "zero"
	case PaddingEdge:
		return "edge"
	case PaddingReflection:
		return "reflection"
	case PaddingMirror:
		return "mirror"
	default:
		return "unknown"
	}
}

// BatchCompatibilityLevel defines how compatible requests are for batching
type BatchCompatibilityLevel int

const (
	CompatibilityExact        BatchCompatibilityLevel = iota // Exact same shape and type
	CompatibilityHigh                                        // Same model, similar shapes (can be efficiently padded)
	CompatibilityMedium                                      // Same model, different shapes (requires padding)
	CompatibilityLow                                         // Different models but compatible types
	CompatibilityIncompatible                                // Cannot be batched together
)

// String returns the string representation of compatibility level
func (c BatchCompatibilityLevel) String() string {
	switch c {
	case CompatibilityExact:
		return "exact"
	case CompatibilityHigh:
		return "high"
	case CompatibilityMedium:
		return "medium"
	case CompatibilityLow:
		return "low"
	case CompatibilityIncompatible:
		return "incompatible"
	default:
		return "unknown"
	}
}

// RequestGroupKey represents a key for grouping compatible requests
type RequestGroupKey struct {
	ModelID    string
	DataType   TensorDataType
	Rank       int    // Number of dimensions
	ShapeClass string // Classification of shape for batching
}

// String returns string representation of the group key
func (key RequestGroupKey) String() string {
	return fmt.Sprintf("%s_%s_%d_%s", key.ModelID, key.DataType, key.Rank, key.ShapeClass)
}

// BatchNormalizationResult contains the result of batch normalization
type BatchNormalizationResult struct {
	NormalizedShape      []int                  `json:"normalized_shape"`
	PaddingInfo          map[string][]int       `json:"padding_info"`          // requestID -> padding applied
	MemoryFootprint      uint64                 `json:"memory_footprint"`      // Total memory required
	PaddingOverhead      float64                `json:"padding_overhead"`      // Ratio of padded vs actual data
	ProcessingEfficiency float64                `json:"processing_efficiency"` // Efficiency score (0-1)
	Timestamp            time.Time              `json:"timestamp"`
	Metadata             map[string]interface{} `json:"metadata"`
}

// BatchGroup represents a group of compatible requests
type BatchGroup struct {
	GroupKey           RequestGroupKey         `json:"group_key"`
	Requests           []*InferenceRequest     `json:"requests"`
	TargetShape        []int                   `json:"target_shape"` // Normalized shape for the group
	CompatibilityLevel BatchCompatibilityLevel `json:"compatibility_level"`
	EstimatedMemory    uint64                  `json:"estimated_memory"`
	PaddingOverhead    float64                 `json:"padding_overhead"`
	Created            time.Time               `json:"created"`
}

// MixedBatchConfig contains configuration for mixed batch processing
type MixedBatchConfig struct {
	MaxPaddingOverhead          float64         `json:"max_padding_overhead"` // Maximum allowed padding ratio
	MaxShapeVariance            float64         `json:"max_shape_variance"`   // Maximum shape variance for grouping
	PreferredPaddingStrategy    PaddingStrategy `json:"preferred_padding_strategy"`
	EnableShapeRounding         bool            `json:"enable_shape_rounding"`   // Round shapes to nearest power of 2
	MaxBatchVariance            int             `json:"max_batch_variance"`      // Max difference in input sizes
	MinBatchEfficiency          float64         `json:"min_batch_efficiency"`    // Minimum processing efficiency
	EnableDynamicGrouping       bool            `json:"enable_dynamic_grouping"` // Allow dynamic regrouping
	ShapeCompatibilityThreshold float64         `json:"shape_compatibility_threshold"`
	MemoryEfficiencyTarget      float64         `json:"memory_efficiency_target"`
}

// DefaultMixedBatchConfig returns sensible default configuration
func DefaultMixedBatchConfig() *MixedBatchConfig {
	return &MixedBatchConfig{
		MaxPaddingOverhead:          0.30, // 30% maximum padding overhead
		MaxShapeVariance:            0.25, // 25% maximum shape variance
		PreferredPaddingStrategy:    PaddingZero,
		EnableShapeRounding:         true,
		MaxBatchVariance:            100,  // Max 100 unit difference per dimension
		MinBatchEfficiency:          0.70, // 70% minimum efficiency
		EnableDynamicGrouping:       true,
		ShapeCompatibilityThreshold: 0.80, // 80% shape compatibility
		MemoryEfficiencyTarget:      0.85, // 85% memory efficiency target
	}
}

// BatchNormalizer handles normalization of variable-sized inputs
type BatchNormalizer struct {
	mu                   sync.RWMutex
	config               *MixedBatchConfig
	paddingStrategy      PaddingStrategy
	shapeCache           map[string][]int // Cached normalized shapes
	normalizationHistory []BatchNormalizationResult
	maxHistorySize       int
	logger               *log.Logger
}

// NewBatchNormalizer creates a new batch normalizer
func NewBatchNormalizer(config *MixedBatchConfig, logger *log.Logger) *BatchNormalizer {
	if config == nil {
		config = DefaultMixedBatchConfig()
	}
	if logger == nil {
		logger = log.Default()
	}

	return &BatchNormalizer{
		config:               config,
		paddingStrategy:      config.PreferredPaddingStrategy,
		shapeCache:           make(map[string][]int),
		normalizationHistory: make([]BatchNormalizationResult, 0, 100),
		maxHistorySize:       100,
		logger:               logger,
	}
}

// NormalizeBatchShapes normalizes a batch of requests to have compatible shapes
func (bn *BatchNormalizer) NormalizeBatchShapes(requests []*InferenceRequest) (*BatchNormalizationResult, error) {
	bn.mu.Lock()
	defer bn.mu.Unlock()

	if len(requests) == 0 {
		return nil, fmt.Errorf("cannot normalize empty batch")
	}

	// Group requests by basic compatibility
	groups := bn.groupRequestsByShape(requests)

	// Find the optimal target shape
	targetShape, err := bn.calculateOptimalTargetShape(groups)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate target shape: %w", err)
	}

	// Calculate padding requirements
	paddingInfo := make(map[string][]int)
	totalMemory := uint64(0)
	actualDataSize := uint64(0)

	for _, request := range requests {
		padding := bn.calculatePadding(request.InputShape, targetShape)
		paddingInfo[request.ID] = padding

		// Calculate memory requirements
		actualSize := bn.calculateTensorSize(request.InputShape, TensorFloat32) // Assume float32 for now
		paddedSize := bn.calculateTensorSize(targetShape, TensorFloat32)

		totalMemory += paddedSize
		actualDataSize += actualSize
	}

	// Calculate efficiency metrics
	paddingOverhead := float64(totalMemory-actualDataSize) / float64(actualDataSize)
	processingEfficiency := bn.calculateProcessingEfficiency(requests, targetShape)

	result := &BatchNormalizationResult{
		NormalizedShape:      targetShape,
		PaddingInfo:          paddingInfo,
		MemoryFootprint:      totalMemory,
		PaddingOverhead:      paddingOverhead,
		ProcessingEfficiency: processingEfficiency,
		Timestamp:            time.Now(),
		Metadata: map[string]interface{}{
			"num_requests":     len(requests),
			"padding_strategy": bn.paddingStrategy.String(),
			"target_shape":     targetShape,
		},
	}

	// Validate efficiency requirements
	if paddingOverhead > bn.config.MaxPaddingOverhead {
		return nil, fmt.Errorf("padding overhead %.2f exceeds maximum %.2f",
			paddingOverhead, bn.config.MaxPaddingOverhead)
	}

	if processingEfficiency < bn.config.MinBatchEfficiency {
		return nil, fmt.Errorf("processing efficiency %.2f below minimum %.2f",
			processingEfficiency, bn.config.MinBatchEfficiency)
	}

	// Add to history
	bn.addNormalizationToHistory(result)

	bn.logger.Printf("Normalized batch of %d requests to shape %v (overhead: %.2f%%, efficiency: %.2f%%)",
		len(requests), targetShape, paddingOverhead*100, processingEfficiency*100)

	return result, nil
}

// groupRequestsByShape groups requests based on shape compatibility
func (bn *BatchNormalizer) groupRequestsByShape(requests []*InferenceRequest) map[string][]*InferenceRequest {
	groups := make(map[string][]*InferenceRequest)

	for _, request := range requests {
		key := bn.generateShapeGroupKey(request.InputShape)
		groups[key] = append(groups[key], request)
	}

	return groups
}

// generateShapeGroupKey generates a key for grouping shapes
func (bn *BatchNormalizer) generateShapeGroupKey(shape []int) string {
	if bn.config.EnableShapeRounding {
		// Round to nearest power of 2 for better grouping
		roundedShape := make([]int, len(shape))
		for i, dim := range shape {
			roundedShape[i] = bn.roundToPowerOf2(dim)
		}
		return fmt.Sprintf("%v", roundedShape)
	}
	return fmt.Sprintf("%v", shape)
}

// roundToPowerOf2 rounds a dimension to the nearest power of 2
func (bn *BatchNormalizer) roundToPowerOf2(dim int) int {
	if dim <= 1 {
		return 1
	}

	power := int(math.Ceil(math.Log2(float64(dim))))
	return int(math.Pow(2, float64(power)))
}

// calculateOptimalTargetShape calculates the optimal target shape for batching
func (bn *BatchNormalizer) calculateOptimalTargetShape(groups map[string][]*InferenceRequest) ([]int, error) {
	var allShapes [][]int

	// Collect all shapes
	for _, group := range groups {
		for _, request := range group {
			allShapes = append(allShapes, request.InputShape)
		}
	}

	if len(allShapes) == 0 {
		return nil, fmt.Errorf("no shapes to analyze")
	}

	// Find the maximum dimension for each axis
	maxShape := make([]int, len(allShapes[0]))
	copy(maxShape, allShapes[0])

	for _, shape := range allShapes[1:] {
		if len(shape) != len(maxShape) {
			return nil, fmt.Errorf("incompatible shapes: different ranks")
		}

		for i, dim := range shape {
			if dim > maxShape[i] {
				maxShape[i] = dim
			}
		}
	}

	// Optionally round to powers of 2 for GPU efficiency
	if bn.config.EnableShapeRounding {
		for i, dim := range maxShape {
			maxShape[i] = bn.roundToPowerOf2(dim)
		}
	}

	return maxShape, nil
}

// calculatePadding calculates padding needed to transform from source to target shape
func (bn *BatchNormalizer) calculatePadding(sourceShape, targetShape []int) []int {
	if len(sourceShape) != len(targetShape) {
		return nil // Incompatible shapes
	}

	padding := make([]int, len(sourceShape)*2) // [top, bottom, left, right, ...]

	for i := 0; i < len(sourceShape); i++ {
		totalPadding := targetShape[i] - sourceShape[i]
		if totalPadding < 0 {
			// Would need cropping, not supported in this implementation
			return nil
		}

		// Distribute padding evenly (with preference for right/bottom)
		leftPadding := totalPadding / 2
		rightPadding := totalPadding - leftPadding

		padding[i*2] = leftPadding
		padding[i*2+1] = rightPadding
	}

	return padding
}

// calculateTensorSize calculates the memory size for a tensor with given shape and type
func (bn *BatchNormalizer) calculateTensorSize(shape []int, dtype TensorDataType) uint64 {
	numElements := int64(1)
	for _, dim := range shape {
		numElements *= int64(dim)
	}
	return uint64(numElements) * uint64(dtype.Size())
}

// calculateProcessingEfficiency estimates processing efficiency for a batch
func (bn *BatchNormalizer) calculateProcessingEfficiency(requests []*InferenceRequest, targetShape []int) float64 {
	if len(requests) == 0 {
		return 0.0
	}

	totalActualElements := int64(0)
	totalPaddedElements := int64(0)

	targetElements := int64(1)
	for _, dim := range targetShape {
		targetElements *= int64(dim)
	}

	for _, request := range requests {
		actualElements := int64(1)
		for _, dim := range request.InputShape {
			actualElements *= int64(dim)
		}

		totalActualElements += actualElements
		totalPaddedElements += targetElements
	}

	if totalPaddedElements == 0 {
		return 0.0
	}

	return float64(totalActualElements) / float64(totalPaddedElements)
}

// addNormalizationToHistory adds a normalization result to history
func (bn *BatchNormalizer) addNormalizationToHistory(result *BatchNormalizationResult) {
	if len(bn.normalizationHistory) >= bn.maxHistorySize {
		// Remove oldest entry
		bn.normalizationHistory = bn.normalizationHistory[1:]
	}
	bn.normalizationHistory = append(bn.normalizationHistory, *result)
}

// GetNormalizationStatistics returns statistics about batch normalization
func (bn *BatchNormalizer) GetNormalizationStatistics() map[string]interface{} {
	bn.mu.RLock()
	defer bn.mu.RUnlock()

	if len(bn.normalizationHistory) == 0 {
		return map[string]interface{}{
			"total_normalizations": 0,
		}
	}

	totalOverhead := 0.0
	totalEfficiency := 0.0
	for _, result := range bn.normalizationHistory {
		totalOverhead += result.PaddingOverhead
		totalEfficiency += result.ProcessingEfficiency
	}

	count := float64(len(bn.normalizationHistory))

	return map[string]interface{}{
		"total_normalizations":      len(bn.normalizationHistory),
		"avg_padding_overhead":      totalOverhead / count,
		"avg_processing_efficiency": totalEfficiency / count,
		"padding_strategy":          bn.paddingStrategy.String(),
	}
}

// MixedBatchProcessor handles processing of mixed batches
type MixedBatchProcessor struct {
	mu              sync.RWMutex
	config          *MixedBatchConfig
	normalizer      *BatchNormalizer
	memoryPredictor *MemoryPredictor

	// Group management
	activeGroups    map[string]*BatchGroup
	groupHistory    []*BatchGroup
	maxGroupHistory int

	// Statistics
	stats *MixedBatchStats

	logger *log.Logger
}

// MixedBatchStats tracks statistics for mixed batch processing
type MixedBatchStats struct {
	TotalBatches            int64     `json:"total_batches"`
	MixedBatches            int64     `json:"mixed_batches"`       // Batches with different input sizes
	HomogeneousBatches      int64     `json:"homogeneous_batches"` // Batches with same input sizes
	AvgPaddingOverhead      float64   `json:"avg_padding_overhead"`
	AvgProcessingEfficiency float64   `json:"avg_processing_efficiency"`
	GroupsCreated           int64     `json:"groups_created"`
	GroupsMerged            int64     `json:"groups_merged"`
	LastUpdated             time.Time `json:"last_updated"`
}

// NewMixedBatchProcessor creates a new mixed batch processor
func NewMixedBatchProcessor(config *MixedBatchConfig, memoryPredictor *MemoryPredictor, logger *log.Logger) *MixedBatchProcessor {
	if config == nil {
		config = DefaultMixedBatchConfig()
	}
	if logger == nil {
		logger = log.Default()
	}

	return &MixedBatchProcessor{
		config:          config,
		normalizer:      NewBatchNormalizer(config, logger),
		memoryPredictor: memoryPredictor,
		activeGroups:    make(map[string]*BatchGroup),
		groupHistory:    make([]*BatchGroup, 0, 1000),
		maxGroupHistory: 1000,
		stats: &MixedBatchStats{
			LastUpdated: time.Now(),
		},
		logger: logger,
	}
}

// ProcessMixedBatch processes a batch with variable input sizes and types
func (mbp *MixedBatchProcessor) ProcessMixedBatch(ctx context.Context, requests []*InferenceRequest) ([]*BatchGroup, error) {
	mbp.mu.Lock()
	defer mbp.mu.Unlock()

	if len(requests) == 0 {
		return nil, fmt.Errorf("cannot process empty batch")
	}

	// Analyze batch composition
	batchType := mbp.analyzeBatchComposition(requests)

	var groups []*BatchGroup
	var err error

	switch batchType {
	case "homogeneous":
		groups, err = mbp.processHomogeneousBatch(requests)
		mbp.stats.HomogeneousBatches++
	case "mixed":
		groups, err = mbp.processMixedSizeBatch(requests)
		mbp.stats.MixedBatches++
	default:
		return nil, fmt.Errorf("unknown batch type: %s", batchType)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to process batch: %w", err)
	}

	// Update statistics
	mbp.stats.TotalBatches++
	mbp.updateBatchStatistics(groups)

	mbp.logger.Printf("Processed %s batch with %d requests into %d groups",
		batchType, len(requests), len(groups))

	return groups, nil
}

// analyzeBatchComposition determines if a batch is homogeneous or mixed
func (mbp *MixedBatchProcessor) analyzeBatchComposition(requests []*InferenceRequest) string {
	if len(requests) <= 1 {
		return "homogeneous"
	}

	firstShape := requests[0].InputShape
	firstModel := requests[0].ModelID

	for _, request := range requests[1:] {
		if request.ModelID != firstModel {
			return "mixed"
		}

		if len(request.InputShape) != len(firstShape) {
			return "mixed"
		}

		for i, dim := range request.InputShape {
			if dim != firstShape[i] {
				return "mixed"
			}
		}
	}

	return "homogeneous"
}

// processHomogeneousBatch processes a batch where all inputs have the same shape
func (mbp *MixedBatchProcessor) processHomogeneousBatch(requests []*InferenceRequest) ([]*BatchGroup, error) {
	if len(requests) == 0 {
		return nil, fmt.Errorf("cannot process empty homogeneous batch")
	}

	// Create a single group since all requests are identical
	groupKey := RequestGroupKey{
		ModelID:    requests[0].ModelID,
		DataType:   TensorFloat32, // Assume float32 for now
		Rank:       len(requests[0].InputShape),
		ShapeClass: fmt.Sprintf("exact_%v", requests[0].InputShape),
	}

	// Estimate memory for the group
	estimatedMemory := mbp.estimateGroupMemory(requests, requests[0].InputShape)

	group := &BatchGroup{
		GroupKey:           groupKey,
		Requests:           requests,
		TargetShape:        requests[0].InputShape,
		CompatibilityLevel: CompatibilityExact,
		EstimatedMemory:    estimatedMemory,
		PaddingOverhead:    0.0, // No padding needed for homogeneous batch
		Created:            time.Now(),
	}

	return []*BatchGroup{group}, nil
}

// processMixedSizeBatch processes a batch with different input sizes
func (mbp *MixedBatchProcessor) processMixedSizeBatch(requests []*InferenceRequest) ([]*BatchGroup, error) {
	// Group requests by compatibility
	compatibilityGroups := mbp.groupByCompatibility(requests)

	var resultGroups []*BatchGroup

	for _, group := range compatibilityGroups {
		// Try to normalize shapes within each compatibility group
		normResult, err := mbp.normalizer.NormalizeBatchShapes(group)
		if err != nil {
			// If normalization fails, split into smaller groups
			subGroups, splitErr := mbp.splitIncompatibleGroup(group)
			if splitErr != nil {
				mbp.logger.Printf("Failed to normalize and split group: %v, %v", err, splitErr)
				continue
			}
			resultGroups = append(resultGroups, subGroups...)
		} else {
			// Create batch group with normalized shape
			batchGroup := mbp.createBatchGroup(group, normResult)
			resultGroups = append(resultGroups, batchGroup)
		}
	}

	if len(resultGroups) == 0 {
		return nil, fmt.Errorf("no compatible groups could be created")
	}

	return resultGroups, nil
}

// groupByCompatibility groups requests based on their compatibility level
func (mbp *MixedBatchProcessor) groupByCompatibility(requests []*InferenceRequest) [][]*InferenceRequest {
	// Group by model first
	modelGroups := make(map[string][]*InferenceRequest)
	for _, request := range requests {
		modelGroups[request.ModelID] = append(modelGroups[request.ModelID], request)
	}

	var compatibilityGroups [][]*InferenceRequest

	// Within each model group, create compatibility subgroups
	for _, modelRequests := range modelGroups {
		subGroups := mbp.createShapeCompatibilityGroups(modelRequests)
		compatibilityGroups = append(compatibilityGroups, subGroups...)
	}

	return compatibilityGroups
}

// createShapeCompatibilityGroups creates groups based on shape compatibility
func (mbp *MixedBatchProcessor) createShapeCompatibilityGroups(requests []*InferenceRequest) [][]*InferenceRequest {
	if len(requests) <= 1 {
		return [][]*InferenceRequest{requests}
	}

	// Sort requests by total size for better grouping
	sort.Slice(requests, func(i, j int) bool {
		sizeI := mbp.calculateShapeSize(requests[i].InputShape)
		sizeJ := mbp.calculateShapeSize(requests[j].InputShape)
		return sizeI < sizeJ
	})

	var groups [][]*InferenceRequest
	currentGroup := []*InferenceRequest{requests[0]}

	for i := 1; i < len(requests); i++ {
		compatibility := mbp.calculateShapeCompatibility(currentGroup[0].InputShape, requests[i].InputShape)

		if compatibility >= mbp.config.ShapeCompatibilityThreshold {
			currentGroup = append(currentGroup, requests[i])
		} else {
			// Start new group
			groups = append(groups, currentGroup)
			currentGroup = []*InferenceRequest{requests[i]}
		}
	}

	// Add the last group
	if len(currentGroup) > 0 {
		groups = append(groups, currentGroup)
	}

	return groups
}

// calculateShapeCompatibility calculates compatibility score between two shapes
func (mbp *MixedBatchProcessor) calculateShapeCompatibility(shape1, shape2 []int) float64 {
	if len(shape1) != len(shape2) {
		return 0.0 // Different ranks are incompatible
	}

	if len(shape1) == 0 {
		return 1.0 // Both empty shapes are fully compatible
	}

	totalVariance := 0.0
	for i := 0; i < len(shape1); i++ {
		dim1, dim2 := float64(shape1[i]), float64(shape2[i])
		variance := math.Abs(dim1-dim2) / math.Max(dim1, dim2)
		totalVariance += variance
	}

	avgVariance := totalVariance / float64(len(shape1))
	return math.Max(0.0, 1.0-avgVariance)
}

// calculateShapeSize calculates the total size (number of elements) of a shape
func (mbp *MixedBatchProcessor) calculateShapeSize(shape []int) int {
	size := 1
	for _, dim := range shape {
		size *= dim
	}
	return size
}

// splitIncompatibleGroup splits a group that couldn't be normalized
func (mbp *MixedBatchProcessor) splitIncompatibleGroup(requests []*InferenceRequest) ([]*BatchGroup, error) {
	var groups []*BatchGroup

	// Process each request individually as a fallback
	for _, request := range requests {
		groupKey := RequestGroupKey{
			ModelID:    request.ModelID,
			DataType:   TensorFloat32,
			Rank:       len(request.InputShape),
			ShapeClass: fmt.Sprintf("individual_%v", request.InputShape),
		}

		estimatedMemory := mbp.estimateGroupMemory([]*InferenceRequest{request}, request.InputShape)

		group := &BatchGroup{
			GroupKey:           groupKey,
			Requests:           []*InferenceRequest{request},
			TargetShape:        request.InputShape,
			CompatibilityLevel: CompatibilityIncompatible,
			EstimatedMemory:    estimatedMemory,
			PaddingOverhead:    0.0,
			Created:            time.Now(),
		}

		groups = append(groups, group)
	}

	return groups, nil
}

// createBatchGroup creates a batch group from normalized requests
func (mbp *MixedBatchProcessor) createBatchGroup(requests []*InferenceRequest, normResult *BatchNormalizationResult) *BatchGroup {
	if len(requests) == 0 {
		return nil
	}

	// Determine compatibility level
	var compatibilityLevel BatchCompatibilityLevel
	if normResult.PaddingOverhead == 0.0 {
		compatibilityLevel = CompatibilityExact
	} else if normResult.PaddingOverhead <= 0.1 {
		compatibilityLevel = CompatibilityHigh
	} else if normResult.PaddingOverhead <= 0.3 {
		compatibilityLevel = CompatibilityMedium
	} else {
		compatibilityLevel = CompatibilityLow
	}

	groupKey := RequestGroupKey{
		ModelID:    requests[0].ModelID,
		DataType:   TensorFloat32,
		Rank:       len(normResult.NormalizedShape),
		ShapeClass: fmt.Sprintf("normalized_%v", normResult.NormalizedShape),
	}

	return &BatchGroup{
		GroupKey:           groupKey,
		Requests:           requests,
		TargetShape:        normResult.NormalizedShape,
		CompatibilityLevel: compatibilityLevel,
		EstimatedMemory:    normResult.MemoryFootprint,
		PaddingOverhead:    normResult.PaddingOverhead,
		Created:            time.Now(),
	}
}

// estimateGroupMemory estimates memory requirements for a group
func (mbp *MixedBatchProcessor) estimateGroupMemory(requests []*InferenceRequest, targetShape []int) uint64 {
	if mbp.memoryPredictor != nil {
		// Use the memory predictor if available
		prediction, err := mbp.memoryPredictor.PredictBatchMemory(requests)
		if err == nil {
			return prediction.PredictedUsage
		}
	}

	// Fallback to simple calculation
	elementSize := uint64(TensorFloat32.Size())
	shapeSize := uint64(1)
	for _, dim := range targetShape {
		shapeSize *= uint64(dim)
	}

	return shapeSize * elementSize * uint64(len(requests))
}

// updateBatchStatistics updates statistics after processing a batch
func (mbp *MixedBatchProcessor) updateBatchStatistics(groups []*BatchGroup) {
	totalOverhead := 0.0
	totalEfficiency := 0.0
	count := 0

	for _, group := range groups {
		if group.PaddingOverhead > 0 {
			totalOverhead += group.PaddingOverhead
			count++
		}
		// Calculate efficiency based on compatibility level
		switch group.CompatibilityLevel {
		case CompatibilityExact:
			totalEfficiency += 1.0
		case CompatibilityHigh:
			totalEfficiency += 0.9
		case CompatibilityMedium:
			totalEfficiency += 0.7
		case CompatibilityLow:
			totalEfficiency += 0.5
		default:
			totalEfficiency += 0.3
		}
	}

	if count > 0 {
		mbp.stats.AvgPaddingOverhead = totalOverhead / float64(count)
	}
	if len(groups) > 0 {
		mbp.stats.AvgProcessingEfficiency = totalEfficiency / float64(len(groups))
	}
	mbp.stats.GroupsCreated += int64(len(groups))
	mbp.stats.LastUpdated = time.Now()
}

// GetStatistics returns mixed batch processing statistics
func (mbp *MixedBatchProcessor) GetStatistics() *MixedBatchStats {
	mbp.mu.RLock()
	defer mbp.mu.RUnlock()

	// Return a copy to avoid race conditions
	statsCopy := *mbp.stats
	return &statsCopy
}

// GetActiveGroups returns currently active batch groups
func (mbp *MixedBatchProcessor) GetActiveGroups() map[string]*BatchGroup {
	mbp.mu.RLock()
	defer mbp.mu.RUnlock()

	// Return a copy to avoid race conditions
	groupsCopy := make(map[string]*BatchGroup)
	for key, group := range mbp.activeGroups {
		groupCopy := *group
		groupsCopy[key] = &groupCopy
	}
	return groupsCopy
}

// ClearCompletedGroups removes completed groups from active tracking
func (mbp *MixedBatchProcessor) ClearCompletedGroups(completedGroupKeys []string) {
	mbp.mu.Lock()
	defer mbp.mu.Unlock()

	for _, key := range completedGroupKeys {
		if group, exists := mbp.activeGroups[key]; exists {
			// Move to history
			if len(mbp.groupHistory) >= mbp.maxGroupHistory {
				mbp.groupHistory = mbp.groupHistory[1:]
			}
			mbp.groupHistory = append(mbp.groupHistory, group)

			// Remove from active
			delete(mbp.activeGroups, key)
		}
	}

	mbp.logger.Printf("Cleared %d completed groups from active tracking", len(completedGroupKeys))
}

// BatchCreationResult represents the result of creating a variable input batch
type BatchCreationResult struct {
	BatchID           string                 `json:"batch_id"`
	Requests          []*InferenceRequest    `json:"requests"`
	MemoryRequirement uint64                 `json:"memory_requirement"`
	BatchSize         int                    `json:"batch_size"`
	CreatedAt         time.Time              `json:"created_at"`
	Metadata          map[string]interface{} `json:"metadata"`
}
