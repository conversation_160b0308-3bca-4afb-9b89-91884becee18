//go:build onnx
// +build onnx

package gpu

/*
#cgo CFLAGS: -I/usr/local/include/onnxruntime
#cgo LDFLAGS: -L/usr/local/lib -lonnxruntime
#include <onnxruntime/onnxruntime_c_api.h>
#include <stdlib.h>
#include <string.h>

// Helper functions for ONNX Runtime C API
static const OrtApi* g_ort = NULL;

// Initialize ONNX Runtime API
static int initialize_ort_api() {
    g_ort = OrtGetApiBase()->GetApi(ORT_API_VERSION);
    if (!g_ort) {
        return -1;
    }
    return 0;
}

// Create session options
static OrtSessionOptions* create_session_options() {
    OrtSessionOptions* session_options;
    OrtStatus* status = g_ort->CreateSessionOptions(&session_options);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return NULL;
    }
    return session_options;
}

// Set provider options
static int set_cuda_provider_options(OrtSessionOptions* session_options, int device_id,
                                    const char* gpu_mem_limit, const char* arena_extend_strategy) {
    const char* keys[] = {"device_id", "gpu_mem_limit", "arena_extend_strategy"};

    char device_id_str[16];
    snprintf(device_id_str, sizeof(device_id_str), "%d", device_id);

    const char* values[] = {device_id_str, gpu_mem_limit, arena_extend_strategy};

    OrtCUDAProviderOptions cuda_options = {0};
    OrtStatus* status = g_ort->CreateCUDAProviderOptions(&cuda_options);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return -1;
    }

    status = g_ort->UpdateCUDAProviderOptions(&cuda_options, keys, values, 3);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        g_ort->ReleaseCUDAProviderOptions(&cuda_options);
        return -1;
    }

    status = g_ort->SessionOptionsAppendExecutionProvider_CUDA(session_options, &cuda_options);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        g_ort->ReleaseCUDAProviderOptions(&cuda_options);
        return -1;
    }

    g_ort->ReleaseCUDAProviderOptions(&cuda_options);
    return 0;
}

// Create session
static OrtSession* create_session(OrtEnv* env, const char* model_path, OrtSessionOptions* session_options) {
    OrtSession* session;
    OrtStatus* status = g_ort->CreateSession(env, model_path, session_options, &session);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return NULL;
    }
    return session;
}

// Get input/output counts
static size_t get_input_count(OrtSession* session) {
    size_t count;
    OrtStatus* status = g_ort->SessionGetInputCount(session, &count);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return 0;
    }
    return count;
}

static size_t get_output_count(OrtSession* session) {
    size_t count;
    OrtStatus* status = g_ort->SessionGetOutputCount(session, &count);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return 0;
    }
    return count;
}

// Get input/output names
static char* get_input_name(OrtSession* session, size_t index, OrtAllocator* allocator) {
    char* name;
    OrtStatus* status = g_ort->SessionGetInputName(session, index, allocator, &name);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return NULL;
    }
    return name;
}

static char* get_output_name(OrtSession* session, size_t index, OrtAllocator* allocator) {
    char* name;
    OrtStatus* status = g_ort->SessionGetOutputName(session, index, allocator, &name);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return NULL;
    }
    return name;
}

// Get type information
static OrtTypeInfo* get_input_type_info(OrtSession* session, size_t index) {
    OrtTypeInfo* type_info;
    OrtStatus* status = g_ort->SessionGetInputTypeInfo(session, index, &type_info);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return NULL;
    }
    return type_info;
}

static OrtTypeInfo* get_output_type_info(OrtSession* session, size_t index) {
    OrtTypeInfo* type_info;
    OrtStatus* status = g_ort->SessionGetOutputTypeInfo(session, index, &type_info);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return NULL;
    }
    return type_info;
}

// Create environment
static OrtEnv* create_environment(const char* log_id, int log_level) {
    OrtEnv* env;
    OrtStatus* status = g_ort->CreateEnv(log_level, log_id, &env);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return NULL;
    }
    return env;
}

// Get default allocator
static OrtAllocator* get_default_allocator() {
    OrtAllocator* allocator;
    OrtStatus* status = g_ort->GetAllocatorWithDefaultOptions(&allocator);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return NULL;
    }
    return allocator;
}

// Helper to get tensor shape
static int get_tensor_shape(OrtTypeInfo* type_info, int64_t* dims, size_t* num_dims) {
    const OrtTensorTypeAndShapeInfo* tensor_info;
    OrtStatus* status = g_ort->CastTypeInfoToTensorInfo(type_info, &tensor_info);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return -1;
    }

    status = g_ort->GetDimensionsCount(tensor_info, num_dims);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return -1;
    }

    status = g_ort->GetDimensions(tensor_info, dims, *num_dims);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return -1;
    }

    return 0;
}

// Memory management helpers
static OrtMemoryInfo* create_cpu_memory_info() {
    OrtMemoryInfo* memory_info;
    OrtStatus* status = g_ort->CreateCpuMemoryInfo(OrtArenaAllocator, OrtMemTypeDefault, &memory_info);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return NULL;
    }
    return memory_info;
}

static OrtMemoryInfo* create_cuda_memory_info(int device_id) {
    OrtMemoryInfo* memory_info;
    OrtStatus* status = g_ort->CreateMemoryInfo("Cuda", OrtArenaAllocator, device_id, OrtMemTypeDefault, &memory_info);
    if (status != NULL) {
        g_ort->ReleaseStatus(status);
        return NULL;
    }
    return memory_info;
}
*/
import "C"
import (
	"fmt"
	"log"
	"sync"
	"unsafe"
)

// ONNXRuntimeSession implements the ONNXSession interface
type ONNXRuntimeSession struct {
	env            *C.OrtEnv
	session        *C.OrtSession
	sessionOptions *C.OrtSessionOptions
	allocator      *C.OrtAllocator
	memoryInfo     *C.OrtMemoryInfo
	config         ONNXSessionConfig
	inputNames     []string
	outputNames    []string
	inputShapes    [][]int64
	outputShapes   [][]int64
	initialized    bool
	mutex          sync.RWMutex
	logger         *log.Logger
}

// Global ONNX Runtime initialization
var (
	ortInitialized bool
	ortInitMutex   sync.Mutex
)

// NewONNXSession creates a new ONNX Runtime session
func NewONNXSession(logger *log.Logger) ONNXSession {
	if logger == nil {
		logger = log.Default()
	}

	return &ONNXRuntimeSession{
		logger: logger,
	}
}

// Initialize creates and configures the ONNX session
func (s *ONNXRuntimeSession) Initialize(config ONNXSessionConfig) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.initialized {
		return fmt.Errorf("session already initialized")
	}

	// Initialize ONNX Runtime API if not already done
	ortInitMutex.Lock()
	if !ortInitialized {
		if result := C.initialize_ort_api(); result != 0 {
			ortInitMutex.Unlock()
			return fmt.Errorf("failed to initialize ONNX Runtime API")
		}
		ortInitialized = true
	}
	ortInitMutex.Unlock()

	s.config = config

	// Create environment
	logID := C.CString("NeuralMeterGo")
	defer C.free(unsafe.Pointer(logID))

	s.env = C.create_environment(logID, C.int(config.LogSeverityLevel))
	if s.env == nil {
		return fmt.Errorf("failed to create ONNX Runtime environment")
	}

	// Create session options
	s.sessionOptions = C.create_session_options()
	if s.sessionOptions == nil {
		return fmt.Errorf("failed to create session options")
	}

	// Configure session options
	if err := s.configureSessionOptions(); err != nil {
		return fmt.Errorf("failed to configure session options: %v", err)
	}

	// Create session
	modelPath := C.CString(config.ModelPath)
	defer C.free(unsafe.Pointer(modelPath))

	s.session = C.create_session(s.env, modelPath, s.sessionOptions)
	if s.session == nil {
		return fmt.Errorf("failed to create ONNX Runtime session from model: %s", config.ModelPath)
	}

	// Get default allocator
	s.allocator = C.get_default_allocator()
	if s.allocator == nil {
		return fmt.Errorf("failed to get default allocator")
	}

	// Create memory info
	if s.hasCUDAProvider() {
		s.memoryInfo = C.create_cuda_memory_info(C.int(config.DeviceID))
	} else {
		s.memoryInfo = C.create_cpu_memory_info()
	}

	if s.memoryInfo == nil {
		return fmt.Errorf("failed to create memory info")
	}

	// Cache input/output metadata
	if err := s.cacheMetadata(); err != nil {
		return fmt.Errorf("failed to cache metadata: %v", err)
	}

	s.initialized = true
	s.logger.Printf("ONNX Runtime session initialized with model: %s", config.ModelPath)

	return nil
}

// configureSessionOptions configures the session options based on config
func (s *ONNXRuntimeSession) configureSessionOptions() error {
	// Set graph optimization level
	C.g_ort.SetSessionGraphOptimizationLevel(s.sessionOptions, C.uint32_t(s.config.GraphOptLevel))

	// Set thread counts
	if s.config.InterOpNumThreads > 0 {
		C.g_ort.SetInterOpNumThreads(s.sessionOptions, C.int(s.config.InterOpNumThreads))
	}
	if s.config.IntraOpNumThreads > 0 {
		C.g_ort.SetIntraOpNumThreads(s.sessionOptions, C.int(s.config.IntraOpNumThreads))
	}

	// Set memory arena options
	if s.config.EnableCPUMemArena {
		C.g_ort.EnableCpuMemArena(s.sessionOptions)
	} else {
		C.g_ort.DisableCpuMemArena(s.sessionOptions)
	}

	if s.config.EnableMemPattern {
		C.g_ort.EnableMemPattern(s.sessionOptions)
	} else {
		C.g_ort.DisableMemPattern(s.sessionOptions)
	}

	// Set profiling
	if s.config.EnableProfiling {
		profilePrefix := C.CString(s.config.ProfileFilePrefix)
		defer C.free(unsafe.Pointer(profilePrefix))
		C.g_ort.EnableProfiling(s.sessionOptions, profilePrefix)
	}

	// Configure providers
	for _, provider := range s.config.Providers {
		if err := s.addProvider(provider); err != nil {
			s.logger.Printf("Failed to add provider %s: %v", provider, err)
		}
	}

	return nil
}

// addProvider adds an execution provider to the session
func (s *ONNXRuntimeSession) addProvider(provider ONNXProvider) error {
	switch provider {
	case ONNXProviderCUDA:
		gpuMemLimit := s.config.ProviderOptions["gpu_mem_limit"]
		if gpuMemLimit == "" {
			gpuMemLimit = "**********" // 2GB default
		}

		arenaStrategy := s.config.ProviderOptions["arena_extend_strategy"]
		if arenaStrategy == "" {
			arenaStrategy = "kNextPowerOfTwo"
		}

		cGpuMemLimit := C.CString(gpuMemLimit)
		cArenaStrategy := C.CString(arenaStrategy)
		defer C.free(unsafe.Pointer(cGpuMemLimit))
		defer C.free(unsafe.Pointer(cArenaStrategy))

		if result := C.set_cuda_provider_options(s.sessionOptions, C.int(s.config.DeviceID),
			cGpuMemLimit, cArenaStrategy); result != 0 {
			return fmt.Errorf("failed to set CUDA provider options")
		}

	case ONNXProviderCPU:
		// CPU provider is always available and added by default

	default:
		return fmt.Errorf("unsupported provider: %s", provider)
	}

	return nil
}

// hasCUDAProvider checks if CUDA provider is enabled
func (s *ONNXRuntimeSession) hasCUDAProvider() bool {
	for _, provider := range s.config.Providers {
		if provider == ONNXProviderCUDA {
			return true
		}
	}
	return false
}

// cacheMetadata caches input/output metadata for efficient access
func (s *ONNXRuntimeSession) cacheMetadata() error {
	// Cache input metadata
	inputCount := int(C.get_input_count(s.session))
	s.inputNames = make([]string, inputCount)
	s.inputShapes = make([][]int64, inputCount)

	for i := 0; i < inputCount; i++ {
		// Get input name
		name := C.get_input_name(s.session, C.size_t(i), s.allocator)
		if name == nil {
			return fmt.Errorf("failed to get input name for index %d", i)
		}
		s.inputNames[i] = C.GoString(name)
		C.g_ort.AllocatorFree(s.allocator, unsafe.Pointer(name))

		// Get input shape
		typeInfo := C.get_input_type_info(s.session, C.size_t(i))
		if typeInfo == nil {
			return fmt.Errorf("failed to get input type info for index %d", i)
		}

		var dims [8]C.int64_t // Support up to 8 dimensions
		var numDims C.size_t

		if result := C.get_tensor_shape(typeInfo, &dims[0], &numDims); result != 0 {
			C.g_ort.ReleaseTypeInfo(typeInfo)
			return fmt.Errorf("failed to get input shape for index %d", i)
		}

		shape := make([]int64, int(numDims))
		for j := 0; j < int(numDims); j++ {
			shape[j] = int64(dims[j])
		}
		s.inputShapes[i] = shape

		C.g_ort.ReleaseTypeInfo(typeInfo)
	}

	// Cache output metadata
	outputCount := int(C.get_output_count(s.session))
	s.outputNames = make([]string, outputCount)
	s.outputShapes = make([][]int64, outputCount)

	for i := 0; i < outputCount; i++ {
		// Get output name
		name := C.get_output_name(s.session, C.size_t(i), s.allocator)
		if name == nil {
			return fmt.Errorf("failed to get output name for index %d", i)
		}
		s.outputNames[i] = C.GoString(name)
		C.g_ort.AllocatorFree(s.allocator, unsafe.Pointer(name))

		// Get output shape
		typeInfo := C.get_output_type_info(s.session, C.size_t(i))
		if typeInfo == nil {
			return fmt.Errorf("failed to get output type info for index %d", i)
		}

		var dims [8]C.int64_t // Support up to 8 dimensions
		var numDims C.size_t

		if result := C.get_tensor_shape(typeInfo, &dims[0], &numDims); result != 0 {
			C.g_ort.ReleaseTypeInfo(typeInfo)
			return fmt.Errorf("failed to get output shape for index %d", i)
		}

		shape := make([]int64, int(numDims))
		for j := 0; j < int(numDims); j++ {
			shape[j] = int64(dims[j])
		}
		s.outputShapes[i] = shape

		C.g_ort.ReleaseTypeInfo(typeInfo)
	}

	return nil
}

// GetInputCount returns the number of input tensors
func (s *ONNXRuntimeSession) GetInputCount() int {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return len(s.inputNames)
}

// GetOutputCount returns the number of output tensors
func (s *ONNXRuntimeSession) GetOutputCount() int {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return len(s.outputNames)
}

// GetInputName returns the name of the input at the given index
func (s *ONNXRuntimeSession) GetInputName(index int) (string, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if index < 0 || index >= len(s.inputNames) {
		return "", fmt.Errorf("input index %d out of range [0, %d)", index, len(s.inputNames))
	}

	return s.inputNames[index], nil
}

// GetOutputName returns the name of the output at the given index
func (s *ONNXRuntimeSession) GetOutputName(index int) (string, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if index < 0 || index >= len(s.outputNames) {
		return "", fmt.Errorf("output index %d out of range [0, %d)", index, len(s.outputNames))
	}

	return s.outputNames[index], nil
}

// GetInputShape returns the shape of the input at the given index
func (s *ONNXRuntimeSession) GetInputShape(index int) ([]int64, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if index < 0 || index >= len(s.inputShapes) {
		return nil, fmt.Errorf("input index %d out of range [0, %d)", index, len(s.inputShapes))
	}

	// Return a copy to prevent external modification
	shape := make([]int64, len(s.inputShapes[index]))
	copy(shape, s.inputShapes[index])
	return shape, nil
}

// GetOutputShape returns the shape of the output at the given index
func (s *ONNXRuntimeSession) GetOutputShape(index int) ([]int64, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if index < 0 || index >= len(s.outputShapes) {
		return nil, fmt.Errorf("output index %d out of range [0, %d)", index, len(s.outputShapes))
	}

	// Return a copy to prevent external modification
	shape := make([]int64, len(s.outputShapes[index]))
	copy(shape, s.outputShapes[index])
	return shape, nil
}

// Run performs inference with the given inputs
func (s *ONNXRuntimeSession) Run(inputs map[string]interface{}) (map[string]interface{}, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.initialized {
		return nil, fmt.Errorf("session not initialized")
	}

	// Convert inputs to arrays for RunWithNames
	inputNames := make([]string, 0, len(inputs))
	inputValues := make([]interface{}, 0, len(inputs))

	for name, value := range inputs {
		inputNames = append(inputNames, name)
		inputValues = append(inputValues, value)
	}

	// Run inference
	outputValues, err := s.RunWithNames(inputNames, inputValues, s.outputNames)
	if err != nil {
		return nil, err
	}

	// Convert outputs back to map
	outputs := make(map[string]interface{})
	for i, name := range s.outputNames {
		if i < len(outputValues) {
			outputs[name] = outputValues[i]
		}
	}

	return outputs, nil
}

// RunWithNames performs inference with named inputs and outputs
func (s *ONNXRuntimeSession) RunWithNames(inputNames []string, inputs []interface{}, outputNames []string) ([]interface{}, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.initialized {
		return nil, fmt.Errorf("session not initialized")
	}

	// For now, return a placeholder implementation
	return nil, fmt.Errorf("inference not yet implemented - requires tensor conversion")
}

// GetProfilingInfo returns profiling information if enabled
func (s *ONNXRuntimeSession) GetProfilingInfo() (string, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.initialized {
		return "", fmt.Errorf("session not initialized")
	}

	if !s.config.EnableProfiling {
		return "", fmt.Errorf("profiling not enabled")
	}

	// Implementation would call g_ort->EndProfiling and return profiling data
	return "profiling info not yet implemented", nil
}

// Cleanup releases resources
func (s *ONNXRuntimeSession) Cleanup() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.initialized {
		return nil
	}

	if s.memoryInfo != nil {
		C.g_ort.ReleaseMemoryInfo(s.memoryInfo)
		s.memoryInfo = nil
	}

	if s.session != nil {
		C.g_ort.ReleaseSession(s.session)
		s.session = nil
	}

	if s.sessionOptions != nil {
		C.g_ort.ReleaseSessionOptions(s.sessionOptions)
		s.sessionOptions = nil
	}

	if s.env != nil {
		C.g_ort.ReleaseEnv(s.env)
		s.env = nil
	}

	s.initialized = false
	s.logger.Println("ONNX Runtime session cleaned up")

	return nil
}
