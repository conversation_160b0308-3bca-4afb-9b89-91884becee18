package gpu

import (
	"log"
	"os"
	"testing"
	"time"
)

// TestFragmentationPrevention tests the enhanced fragmentation calculation and prevention
func TestFragmentationPrevention(t *testing.T) {
	logger := log.New(os.Stdout, "FRAG_TEST: ", log.LstdFlags)

	config := DefaultPoolConfig(0)
	config.InitialSize = 0            // Start with no pre-allocation
	config.MaxSize = 16 * 1024 * 1024 // 16MB
	config.BlockSize = 1024 * 1024    // 1MB
	config.Strategy = StrategyDynamic
	config.EnableDefragmentation = true
	config.DefragmentationInterval = 50 * time.Millisecond
	config.FragmentationLimit = 30.0 // 30%

	pool, err := NewAdvancedMemoryPool(config, logger)
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}
	defer pool.Shutdown()

	if err := pool.Initialize(); err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}

	// Test 1: Create fragmentation with varied block sizes
	var allocations []CUDAMemoryPtr
	sizes := []int64{512 * 1024, 256 * 1024, 1024 * 1024, 128 * 1024, 2048 * 1024}

	for i, size := range sizes {
		ptr, err := pool.Allocate(size)
		if err != nil {
			t.Fatalf("Failed to allocate %d bytes: %v", size, err)
		}
		allocations = append(allocations, ptr)
		t.Logf("Allocated block %d: %d bytes", i+1, size)
	}

	// Free every other block to create fragmentation
	for i := 0; i < len(allocations); i += 2 {
		if err := pool.Free(allocations[i]); err != nil {
			t.Errorf("Failed to free allocation %d: %v", i, err)
		}
	}

	stats := pool.GetStatistics()
	t.Logf("After creating fragmentation: %.1f%% fragmentation, %d free blocks",
		stats.FragmentationPercent, stats.FreeBlockCount)

	// Test 2: Verify fragmentation calculation is reasonable
	if stats.FragmentationPercent < 0 || stats.FragmentationPercent > 100 {
		t.Errorf("Invalid fragmentation percentage: %.1f%%", stats.FragmentationPercent)
	}

	// Test 3: Wait for defragmentation to trigger
	time.Sleep(100 * time.Millisecond)

	stats = pool.GetStatistics()
	t.Logf("After defragmentation: %.1f%% fragmentation, %d defrag operations",
		stats.FragmentationPercent, stats.DefragmentationCount)

	// Test 4: Free remaining allocations
	for i := 1; i < len(allocations); i += 2 {
		if err := pool.Free(allocations[i]); err != nil {
			t.Errorf("Failed to free allocation %d: %v", i, err)
		}
	}

	finalStats := pool.GetStatistics()
	t.Logf("Final stats: %.1f%% fragmentation, %d total blocks, %d defrag operations",
		finalStats.FragmentationPercent, finalStats.BlockCount, finalStats.DefragmentationCount)
}

// TestMemoryReuse tests the enhanced memory reuse strategies
func TestMemoryReuse(t *testing.T) {
	logger := log.New(os.Stdout, "REUSE_TEST: ", log.LstdFlags)

	config := DefaultPoolConfig(0)
	config.InitialSize = 0
	config.MaxSize = 8 * 1024 * 1024
	config.BlockSize = 1024 * 1024
	config.Strategy = StrategyDynamic
	config.EnableDefragmentation = false // Disable for this test

	pool, err := NewAdvancedMemoryPool(config, logger)
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}
	defer pool.Shutdown()

	if err := pool.Initialize(); err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}

	// Test 1: Exact match reuse
	size1 := int64(512 * 1024)
	ptr1, err := pool.Allocate(size1)
	if err != nil {
		t.Fatalf("Failed to allocate: %v", err)
	}

	if err := pool.Free(ptr1); err != nil {
		t.Fatalf("Failed to free: %v", err)
	}

	// Allocate same size - should reuse exact block
	ptr2, err := pool.Allocate(size1)
	if err != nil {
		t.Fatalf("Failed to reallocate: %v", err)
	}

	stats := pool.GetStatistics()
	t.Logf("Exact match reuse: %d total blocks, %d used blocks", stats.BlockCount, stats.UsedBlockCount)

	// Test 2: Best-fit reuse with minimal waste
	size2 := int64(400 * 1024) // Should fit in 512KB block with <25% waste
	if err := pool.Free(ptr2); err != nil {
		t.Fatalf("Failed to free: %v", err)
	}

	ptr3, err := pool.Allocate(size2)
	if err != nil {
		t.Fatalf("Failed to allocate smaller size: %v", err)
	}

	stats = pool.GetStatistics()
	t.Logf("Best-fit reuse: %d total blocks, %d used blocks", stats.BlockCount, stats.UsedBlockCount)

	// Test 3: Allocation efficiency - should prefer reuse over new allocation
	if err := pool.Free(ptr3); err != nil {
		t.Fatalf("Failed to free: %v", err)
	}

	// Allocate multiple blocks of similar sizes
	var ptrs []CUDAMemoryPtr
	for i := 0; i < 5; i++ {
		ptr, err := pool.Allocate(int64(300 * 1024)) // Should reuse existing blocks
		if err != nil {
			t.Fatalf("Failed to allocate iteration %d: %v", i, err)
		}
		ptrs = append(ptrs, ptr)
	}

	stats = pool.GetStatistics()
	t.Logf("Multiple allocations: %d total blocks, %d used blocks, %d allocations",
		stats.BlockCount, stats.UsedBlockCount, stats.AllocationCount)

	// Should have reasonable block reuse
	if stats.BlockCount > 10 {
		t.Errorf("Too many blocks created: %d (expected reuse to keep count low)", stats.BlockCount)
	}

	// Clean up
	for _, ptr := range ptrs {
		if err := pool.Free(ptr); err != nil {
			t.Errorf("Failed to free pointer: %v", err)
		}
	}
}

// TestCoalescingAndFragmentRemoval tests the defragmentation coalescing logic
func TestCoalescingAndFragmentRemoval(t *testing.T) {
	logger := log.New(os.Stdout, "COALESCE_TEST: ", log.LstdFlags)

	config := DefaultPoolConfig(0)
	config.InitialSize = 0
	config.MaxSize = 16 * 1024 * 1024
	config.BlockSize = 1024 * 1024
	config.Strategy = StrategyDynamic
	config.EnableDefragmentation = true
	config.DefragmentationInterval = 25 * time.Millisecond
	config.FragmentationLimit = 20.0 // Low threshold to trigger often

	pool, err := NewAdvancedMemoryPool(config, logger)
	if err != nil {
		t.Fatalf("Failed to create memory pool: %v", err)
	}
	defer pool.Shutdown()

	if err := pool.Initialize(); err != nil {
		t.Fatalf("Failed to initialize memory pool: %v", err)
	}

	// Create many small allocations
	var allocations []CUDAMemoryPtr
	for i := 0; i < 20; i++ {
		size := int64(32 * 1024) // 32KB each
		ptr, err := pool.Allocate(size)
		if err != nil {
			t.Fatalf("Failed to allocate small block %d: %v", i, err)
		}
		allocations = append(allocations, ptr)
	}

	// Free most of them to create many small fragments
	for i := 0; i < 15; i++ {
		if err := pool.Free(allocations[i]); err != nil {
			t.Errorf("Failed to free small block %d: %v", i, err)
		}
	}

	stats := pool.GetStatistics()
	initialBlocks := stats.BlockCount
	initialFreeBlocks := stats.FreeBlockCount
	t.Logf("Before coalescing: %d total blocks, %d free blocks, %.1f%% fragmentation",
		initialBlocks, initialFreeBlocks, stats.FragmentationPercent)

	// Wait for defragmentation to run
	time.Sleep(150 * time.Millisecond)

	stats = pool.GetStatistics()
	t.Logf("After coalescing: %d total blocks, %d free blocks, %.1f%% fragmentation, %d defrag ops",
		stats.BlockCount, stats.FreeBlockCount, stats.FragmentationPercent, stats.DefragmentationCount)

	// Should have performed some defragmentation
	if stats.DefragmentationCount == 0 {
		t.Errorf("Expected defragmentation to run, but count is 0")
	}

	// Free remaining allocations
	for i := 15; i < len(allocations); i++ {
		if err := pool.Free(allocations[i]); err != nil {
			t.Errorf("Failed to free remaining block %d: %v", i, err)
		}
	}

	finalStats := pool.GetStatistics()
	t.Logf("Final state: %d total blocks, %d free blocks, %.1f%% fragmentation",
		finalStats.BlockCount, finalStats.FreeBlockCount, finalStats.FragmentationPercent)
}
