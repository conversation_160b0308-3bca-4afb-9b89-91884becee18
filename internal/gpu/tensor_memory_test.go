package gpu

import (
	"math"
	"testing"
)

func TestMemoryLayoutOptimizer(t *testing.T) {
	optimizer := NewMemoryLayoutOptimizer()

	// Test different operations
	shape1 := TensorShape{100, 100}
	shape2 := TensorShape{100, 100}

	// Test matmul optimization
	layout := optimizer.OptimizeLayoutForOperation("matmul", shape1, shape2)
	if layout != RowMajor {
		t.<PERSON>("Expected RowMajor for matmul, got %v", layout)
	}

	// Test conv2d optimization
	layout = optimizer.OptimizeLayoutForOperation("conv2d", shape1)
	if layout != RowMajor {
		t.<PERSON>rf("Expected RowMajor for conv2d, got %v", layout)
	}

	// Test transpose optimization with small tensor
	smallShape := TensorShape{10, 10}
	layout = optimizer.OptimizeLayoutForOperation("transpose", smallShape)
	if layout != ColMajor {
		t.<PERSON>("Expected ColMajor for small transpose, got %v", layout)
	}

	// Test caching
	layout1 := optimizer.OptimizeLayoutForOperation("matmul", shape1, shape2)
	layout2 := optimizer.OptimizeLayoutForOperation("matmul", shape1, shape2)
	if layout1 != layout2 {
		t.Errorf("Cache not working: got different layouts %v vs %v", layout1, layout2)
	}
}

func TestTensorInPlaceOperations(t *testing.T) {
	// Create test tensors
	shape := TensorShape{2, 3}
	tensor1, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor1: %v", err)
	}
	defer tensor1.Free()

	tensor2, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor2: %v", err)
	}
	defer tensor2.Free()

	// Fill tensors with test data
	for i := int64(0); i < 6; i++ {
		tensor1.setElementFromFloat64(i, float64(i+1)) // [1, 2, 3, 4, 5, 6]
		tensor2.setElementFromFloat64(i, float64(i+2)) // [2, 3, 4, 5, 6, 7]
	}

	// Test AddInPlace
	err = tensor1.AddInPlace(tensor2)
	if err != nil {
		t.Fatalf("AddInPlace failed: %v", err)
	}

	// Check results: [3, 5, 7, 9, 11, 13]
	expected := []float64{3, 5, 7, 9, 11, 13}
	for i := int64(0); i < 6; i++ {
		val, err := tensor1.getElementAsFloat64(i)
		if err != nil {
			t.Fatalf("Failed to get element %d: %v", i, err)
		}
		if math.Abs(val-expected[i]) > 1e-6 {
			t.Errorf("AddInPlace result[%d]: expected %f, got %f", i, expected[i], val)
		}
	}
}

func TestTensorSubtractInPlace(t *testing.T) {
	shape := TensorShape{2, 2}
	tensor1, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor1: %v", err)
	}
	defer tensor1.Free()

	tensor2, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor2: %v", err)
	}
	defer tensor2.Free()

	// Fill tensors: tensor1 = [5, 6, 7, 8], tensor2 = [1, 2, 3, 4]
	for i := int64(0); i < 4; i++ {
		tensor1.setElementFromFloat64(i, float64(i+5))
		tensor2.setElementFromFloat64(i, float64(i+1))
	}

	err = tensor1.SubtractInPlace(tensor2)
	if err != nil {
		t.Fatalf("SubtractInPlace failed: %v", err)
	}

	// Check results: [4, 4, 4, 4]
	for i := int64(0); i < 4; i++ {
		val, err := tensor1.getElementAsFloat64(i)
		if err != nil {
			t.Fatalf("Failed to get element %d: %v", i, err)
		}
		if math.Abs(val-4.0) > 1e-6 {
			t.Errorf("SubtractInPlace result[%d]: expected 4.0, got %f", i, val)
		}
	}
}

func TestTensorScaleInPlace(t *testing.T) {
	shape := TensorShape{3}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill tensor: [1, 2, 3]
	for i := int64(0); i < 3; i++ {
		tensor.setElementFromFloat64(i, float64(i+1))
	}

	// Scale by 2.5
	err = tensor.ScaleInPlace(2.5)
	if err != nil {
		t.Fatalf("ScaleInPlace failed: %v", err)
	}

	// Check results: [2.5, 5.0, 7.5]
	expected := []float64{2.5, 5.0, 7.5}
	for i := int64(0); i < 3; i++ {
		val, err := tensor.getElementAsFloat64(i)
		if err != nil {
			t.Fatalf("Failed to get element %d: %v", i, err)
		}
		if math.Abs(val-expected[i]) > 1e-6 {
			t.Errorf("ScaleInPlace result[%d]: expected %f, got %f", i, expected[i], val)
		}
	}
}

func TestTensorClampInPlace(t *testing.T) {
	shape := TensorShape{5}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill tensor: [-2, -1, 0, 1, 2]
	for i := int64(0); i < 5; i++ {
		tensor.setElementFromFloat64(i, float64(i-2))
	}

	// Clamp between -1 and 1
	err = tensor.ClampInPlace(-1.0, 1.0)
	if err != nil {
		t.Fatalf("ClampInPlace failed: %v", err)
	}

	// Check results: [-1, -1, 0, 1, 1]
	expected := []float64{-1, -1, 0, 1, 1}
	for i := int64(0); i < 5; i++ {
		val, err := tensor.getElementAsFloat64(i)
		if err != nil {
			t.Fatalf("Failed to get element %d: %v", i, err)
		}
		if math.Abs(val-expected[i]) > 1e-6 {
			t.Errorf("ClampInPlace result[%d]: expected %f, got %f", i, expected[i], val)
		}
	}
}

func TestTensorCompact(t *testing.T) {
	// Create original tensor
	shape := TensorShape{2, 3}
	original, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create original tensor: %v", err)
	}
	defer original.Free()

	// Fill with test data
	for i := int64(0); i < 6; i++ {
		original.setElementFromFloat64(i, float64(i+1))
	}

	// Create a slice (non-contiguous)
	slice, err := original.Slice([]int64{0, 1}, []int64{2, 3})
	if err != nil {
		t.Fatalf("Failed to create slice: %v", err)
	}
	defer slice.Free()

	// Compact the slice
	compacted, err := slice.Compact()
	if err != nil {
		t.Fatalf("Failed to compact tensor: %v", err)
	}
	defer compacted.Free()

	// Check that compacted tensor is contiguous
	if !compacted.IsContiguous() {
		t.Errorf("Compacted tensor should be contiguous")
	}

	// Check data integrity
	if compacted.NumElements() != slice.NumElements() {
		t.Errorf("Element count mismatch: expected %d, got %d",
			slice.NumElements(), compacted.NumElements())
	}
}

func TestTensorReshape(t *testing.T) {
	// Create tensor [2, 3]
	shape := TensorShape{2, 3}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Reshape to [3, 2]
	reshaped, err := tensor.Reshape(TensorShape{3, 2})
	if err != nil {
		t.Fatalf("Failed to reshape tensor: %v", err)
	}
	defer reshaped.Free()

	// Check new shape
	newShape := reshaped.Shape()
	if len(newShape) != 2 || newShape[0] != 3 || newShape[1] != 2 {
		t.Errorf("Reshape failed: expected [3, 2], got %v", newShape)
	}

	// Check that it's still a view (same data pointer)
	if reshaped.ownsMemory {
		t.Errorf("Reshaped tensor should not own memory (should be a view)")
	}
}

func TestTensorPermute(t *testing.T) {
	// Create tensor [2, 3, 4]
	shape := TensorShape{2, 3, 4}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Permute dimensions: [0, 1, 2] -> [2, 0, 1]
	permuted, err := tensor.Permute([]int{2, 0, 1})
	if err != nil {
		t.Fatalf("Failed to permute tensor: %v", err)
	}
	defer permuted.Free()

	// Check new shape: should be [4, 2, 3]
	newShape := permuted.Shape()
	expected := TensorShape{4, 2, 3}
	if len(newShape) != len(expected) {
		t.Fatalf("Permute shape length mismatch: expected %d, got %d", len(expected), len(newShape))
	}

	for i, dim := range expected {
		if newShape[i] != dim {
			t.Errorf("Permute shape[%d]: expected %d, got %d", i, dim, newShape[i])
		}
	}
}

func TestTensorSqueeze(t *testing.T) {
	// Create tensor [1, 3, 1, 4]
	shape := TensorShape{1, 3, 1, 4}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Squeeze all dimensions of size 1
	squeezed, err := tensor.Squeeze()
	if err != nil {
		t.Fatalf("Failed to squeeze tensor: %v", err)
	}
	defer squeezed.Free()

	// Check new shape: should be [3, 4]
	newShape := squeezed.Shape()
	expected := TensorShape{3, 4}
	if len(newShape) != len(expected) {
		t.Fatalf("Squeeze shape length mismatch: expected %d, got %d", len(expected), len(newShape))
	}

	for i, dim := range expected {
		if newShape[i] != dim {
			t.Errorf("Squeeze shape[%d]: expected %d, got %d", i, dim, newShape[i])
		}
	}

	// Test squeezing specific dimensions
	squeezed2, err := tensor.Squeeze(0, 2)
	if err != nil {
		t.Fatalf("Failed to squeeze specific dimensions: %v", err)
	}
	defer squeezed2.Free()

	// Check new shape: should be [3, 4]
	newShape2 := squeezed2.Shape()
	for i, dim := range expected {
		if newShape2[i] != dim {
			t.Errorf("Specific squeeze shape[%d]: expected %d, got %d", i, dim, newShape2[i])
		}
	}
}

func TestTensorUnsqueeze(t *testing.T) {
	// Create tensor [3, 4]
	shape := TensorShape{3, 4}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Unsqueeze at dimensions 0 and 2
	unsqueezed, err := tensor.Unsqueeze(0, 2)
	if err != nil {
		t.Fatalf("Failed to unsqueeze tensor: %v", err)
	}
	defer unsqueezed.Free()

	// Check new shape: should be [1, 3, 1, 4]
	newShape := unsqueezed.Shape()
	expected := TensorShape{1, 3, 1, 4}
	if len(newShape) != len(expected) {
		t.Fatalf("Unsqueeze shape length mismatch: expected %d, got %d", len(expected), len(newShape))
	}

	for i, dim := range expected {
		if newShape[i] != dim {
			t.Errorf("Unsqueeze shape[%d]: expected %d, got %d", i, dim, newShape[i])
		}
	}
}

func TestTensorLayoutConversion(t *testing.T) {
	// Create tensor with row-major layout
	shape := TensorShape{2, 3}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Fill with test data
	for i := int64(0); i < 6; i++ {
		tensor.setElementFromFloat64(i, float64(i+1))
	}

	// Convert to column-major
	colMajor, err := tensor.ToColMajor()
	if err != nil {
		t.Fatalf("Failed to convert to column-major: %v", err)
	}
	defer colMajor.Free()

	// Check layout
	if colMajor.layout != ColMajor {
		t.Errorf("Expected ColMajor layout, got %v", colMajor.layout)
	}

	// Convert back to row-major
	rowMajor, err := colMajor.ToRowMajor()
	if err != nil {
		t.Fatalf("Failed to convert to row-major: %v", err)
	}
	defer rowMajor.Free()

	// Check layout
	if rowMajor.layout != RowMajor {
		t.Errorf("Expected RowMajor layout, got %v", rowMajor.layout)
	}
}

func TestTensorMemoryStats(t *testing.T) {
	// Create tensor
	shape := TensorShape{10, 20}
	tensor, err := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	if err != nil {
		t.Fatalf("Failed to create tensor: %v", err)
	}
	defer tensor.Free()

	// Get memory stats
	stats := tensor.GetMemoryStats()

	// Check stats
	expectedElements := int64(200)
	expectedElementSize := 4 // float32
	expectedTotalSize := expectedElements * int64(expectedElementSize)

	if stats.NumElements != expectedElements {
		t.Errorf("NumElements: expected %d, got %d", expectedElements, stats.NumElements)
	}

	if stats.ElementSize != expectedElementSize {
		t.Errorf("ElementSize: expected %d, got %d", expectedElementSize, stats.ElementSize)
	}

	if stats.TotalSize != expectedTotalSize {
		t.Errorf("TotalSize: expected %d, got %d", expectedTotalSize, stats.TotalSize)
	}

	if !stats.IsContiguous {
		t.Errorf("Expected tensor to be contiguous")
	}

	if stats.Layout != "row_major" {
		t.Errorf("Expected row_major layout, got %s", stats.Layout)
	}

	if stats.Device != "cpu" {
		t.Errorf("Expected cpu device, got %s", stats.Device)
	}

	if math.Abs(stats.MemoryUtilization-100.0) > 1e-6 {
		t.Errorf("Expected 100%% memory utilization, got %f", stats.MemoryUtilization)
	}
}

func TestEstimateMemoryFootprint(t *testing.T) {
	// Test convolution memory estimation
	inputShape := TensorShape{1, 32, 224, 224} // NCHW format
	kernelShape := TensorShape{64, 32, 3, 3}   // Output channels, input channels, height, width

	footprint := EstimateMemoryFootprint("conv2d", []TensorShape{inputShape, kernelShape}, TensorFloat32)

	// Should be > 0 and reasonable
	if footprint <= 0 {
		t.Errorf("Expected positive memory footprint, got %d", footprint)
	}

	// Test matrix multiplication memory estimation
	aShape := TensorShape{100, 200}
	bShape := TensorShape{200, 150}

	footprint = EstimateMemoryFootprint("matmul", []TensorShape{aShape, bShape}, TensorFloat32)

	expectedMinimum := (aShape.NumElements() + bShape.NumElements() + 100*150) * 4 // float32 size
	if footprint < expectedMinimum {
		t.Errorf("Matrix multiplication footprint too small: expected >= %d, got %d", expectedMinimum, footprint)
	}
}

// Benchmark tests for memory operations
func BenchmarkTensorAddInPlace(b *testing.B) {
	shape := TensorShape{1000, 1000}
	tensor1, _ := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	tensor2, _ := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	defer tensor1.Free()
	defer tensor2.Free()

	// Fill with test data
	for i := int64(0); i < tensor1.NumElements(); i++ {
		tensor1.setElementFromFloat64(i, 1.0)
		tensor2.setElementFromFloat64(i, 2.0)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		tensor1.AddInPlace(tensor2)
	}
}

func BenchmarkTensorPermute(b *testing.B) {
	shape := TensorShape{100, 200, 50}
	tensor, _ := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	defer tensor.Free()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		permuted, _ := tensor.Permute([]int{2, 0, 1})
		permuted.Free()
	}
}

func BenchmarkTensorCompact(b *testing.B) {
	shape := TensorShape{100, 100}
	tensor, _ := NewTensor(shape, TensorFloat32, DeviceCPU, 0)
	defer tensor.Free()

	// Create a slice to make it non-contiguous
	slice, _ := tensor.Slice([]int64{10, 10}, []int64{90, 90})
	defer slice.Free()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		compacted, _ := slice.Compact()
		compacted.Free()
	}
}
