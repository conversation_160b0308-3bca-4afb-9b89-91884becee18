package gpu

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"
)

// RequestPriority defines the priority levels for inference requests
type RequestPriority int

const (
	RequestPriorityLow RequestPriority = iota
	RequestPriorityNormal
	RequestPriorityHigh
	RequestPriorityCritical
)

// String returns the string representation of the priority
func (p RequestPriority) String() string {
	switch p {
	case RequestPriorityLow:
		return "low"
	case RequestPriorityNormal:
		return "normal"
	case RequestPriorityHigh:
		return "high"
	case RequestPriorityCritical:
		return "critical"
	default:
		return "unknown"
	}
}

// InferenceRequest represents a single inference request in the queue
type InferenceRequest struct {
	ID              string                 `json:"id"`
	Priority        RequestPriority        `json:"priority"`
	Data            []byte                 `json:"-"`            // Raw input data
	InputShape      []int                  `json:"input_shape"`  // Shape of input tensor
	ModelID         string                 `json:"model_id"`     // Target model identifier
	ClientID        string                 `json:"client_id"`    // Client identifier for SLA tracking
	SLADeadline     time.Time              `json:"sla_deadline"` // SLA deadline for request
	Metadata        map[string]interface{} `json:"metadata"`     // Additional request metadata
	SubmittedAt     time.Time              `json:"submitted_at"`
	ProcessingStart time.Time              `json:"processing_start,omitempty"`
	QueueTime       time.Duration          `json:"queue_time,omitempty"`

	// Callback for result delivery
	ResultCallback func(result *InferenceResult, err error) `json:"-"`

	// Context for cancellation
	Context context.Context `json:"-"`
}

// InferenceResult represents the result of an inference request
type InferenceResult struct {
	RequestID    string                 `json:"request_id"`
	Data         []byte                 `json:"-"`            // Raw output data
	OutputShape  []int                  `json:"output_shape"` // Shape of output tensor
	Metadata     map[string]interface{} `json:"metadata"`
	ProcessedAt  time.Time              `json:"processed_at"`
	ProcessTime  time.Duration          `json:"process_time"`
	QueueTime    time.Duration          `json:"queue_time"`
	TotalLatency time.Duration          `json:"total_latency"`
}

// ClientSLA defines SLA parameters for different clients
type ClientSLA struct {
	ClientID         string        `json:"client_id"`
	MaxLatency       time.Duration `json:"max_latency"`       // Maximum allowed latency
	ThroughputQuota  int           `json:"throughput_quota"`  // Requests per second quota
	PriorityWeight   float64       `json:"priority_weight"`   // Weight multiplier for priority calculation
	IsGuaranteed     bool          `json:"is_guaranteed"`     // Whether SLA is guaranteed
	FailureThreshold float64       `json:"failure_threshold"` // SLA violation threshold (0-1)
}

// QueueMetrics tracks queue performance and SLA compliance
type QueueMetrics struct {
	TotalRequests      int64         `json:"total_requests"`
	ProcessedRequests  int64         `json:"processed_requests"`
	FailedRequests     int64         `json:"failed_requests"`
	CurrentQueueSize   int           `json:"current_queue_size"`
	AverageQueueTime   time.Duration `json:"average_queue_time"`
	AverageProcessTime time.Duration `json:"average_process_time"`
	SLAViolations      int64         `json:"sla_violations"`
	ThroughputCurrent  float64       `json:"throughput_current"` // requests/second
	ThroughputPeak     float64       `json:"throughput_peak"`    // peak requests/second
	LastUpdated        time.Time     `json:"last_updated"`

	// Per-priority metrics
	PriorityMetrics map[RequestPriority]*PriorityMetrics `json:"priority_metrics"`
}

// PriorityMetrics tracks metrics per priority level
type PriorityMetrics struct {
	TotalRequests      int64         `json:"total_requests"`
	ProcessedRequests  int64         `json:"processed_requests"`
	FailedRequests     int64         `json:"failed_requests"`
	AverageQueueTime   time.Duration `json:"average_queue_time"`
	AverageProcessTime time.Duration `json:"average_process_time"`
	CurrentQueueSize   int           `json:"current_queue_size"`
}

// QueueConfig contains configuration for the inference queue
type QueueConfig struct {
	MaxQueueSize         int           `json:"max_queue_size"`
	MaxBatchWaitTime     time.Duration `json:"max_batch_wait_time"`
	SLACheckInterval     time.Duration `json:"sla_check_interval"`
	PriorityBoostFactor  float64       `json:"priority_boost_factor"`  // Factor to boost priority based on wait time
	LoadBalanceThreshold float64       `json:"load_balance_threshold"` // Queue size threshold for load balancing
	EnableSLAMonitoring  bool          `json:"enable_sla_monitoring"`
	EnablePriorityBoost  bool          `json:"enable_priority_boost"`
	DropPolicy           string        `json:"drop_policy"` // "oldest", "lowest_priority", "sla_violating"
	BackpressureEnabled  bool          `json:"backpressure_enabled"`
}

// DefaultQueueConfig returns a sensible default configuration
func DefaultQueueConfig() *QueueConfig {
	return &QueueConfig{
		MaxQueueSize:         1000,
		MaxBatchWaitTime:     50 * time.Millisecond,
		SLACheckInterval:     1 * time.Second,
		PriorityBoostFactor:  1.5,
		LoadBalanceThreshold: 0.8,
		EnableSLAMonitoring:  true,
		EnablePriorityBoost:  true,
		DropPolicy:           "lowest_priority",
		BackpressureEnabled:  true,
	}
}

// InferenceQueue manages a priority-based queue for inference requests
type InferenceQueue struct {
	mu     sync.RWMutex
	config *QueueConfig

	// Priority queues for different request priorities
	queues map[RequestPriority][]*InferenceRequest

	// Client SLA tracking
	clientSLAs map[string]*ClientSLA

	// Metrics and monitoring
	metrics *QueueMetrics

	// Batch optimizer integration
	batchOptimizer *BatchSizeOptimizer

	// Channels for queue management
	submitChannel  chan *InferenceRequest
	processingDone chan *InferenceRequest
	shutdown       chan bool

	// Background goroutines management
	backgroundCtx    context.Context
	backgroundCancel context.CancelFunc
	wg               sync.WaitGroup
}

// NewInferenceQueue creates a new intelligent inference queue
func NewInferenceQueue(config *QueueConfig, batchOptimizer *BatchSizeOptimizer) (*InferenceQueue, error) {
	if config == nil {
		config = DefaultQueueConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	queue := &InferenceQueue{
		config:           config,
		queues:           make(map[RequestPriority][]*InferenceRequest),
		clientSLAs:       make(map[string]*ClientSLA),
		batchOptimizer:   batchOptimizer,
		submitChannel:    make(chan *InferenceRequest, config.MaxQueueSize),
		processingDone:   make(chan *InferenceRequest, config.MaxQueueSize),
		shutdown:         make(chan bool, 1),
		backgroundCtx:    ctx,
		backgroundCancel: cancel,
		metrics: &QueueMetrics{
			PriorityMetrics: make(map[RequestPriority]*PriorityMetrics),
			LastUpdated:     time.Now(),
		},
	}

	// Initialize priority queues
	for priority := RequestPriorityLow; priority <= RequestPriorityCritical; priority++ {
		queue.queues[priority] = make([]*InferenceRequest, 0)
		queue.metrics.PriorityMetrics[priority] = &PriorityMetrics{}
	}

	// Start background workers
	queue.startBackgroundWorkers()

	return queue, nil
}

// AddClientSLA adds or updates SLA configuration for a client
func (q *InferenceQueue) AddClientSLA(sla *ClientSLA) {
	q.mu.Lock()
	defer q.mu.Unlock()

	q.clientSLAs[sla.ClientID] = sla
}

// RemoveClientSLA removes SLA configuration for a client
func (q *InferenceQueue) RemoveClientSLA(clientID string) {
	q.mu.Lock()
	defer q.mu.Unlock()

	delete(q.clientSLAs, clientID)
}

// SubmitRequest submits a new inference request to the queue
func (q *InferenceQueue) SubmitRequest(request *InferenceRequest) error {
	if request == nil {
		return errors.New("request cannot be nil")
	}

	// Set submission timestamp
	request.SubmittedAt = time.Now()

	// Generate ID if not provided
	if request.ID == "" {
		request.ID = fmt.Sprintf("req_%d_%s", time.Now().UnixNano(), request.ClientID)
	}

	// Set default context if not provided
	if request.Context == nil {
		request.Context = context.Background()
	}

	// Check queue capacity and apply backpressure
	if q.config.BackpressureEnabled && q.getCurrentQueueSize() >= q.config.MaxQueueSize {
		if err := q.handleBackpressure(request); err != nil {
			return fmt.Errorf("queue backpressure: %w", err)
		}
	}

	select {
	case q.submitChannel <- request:
		q.updateSubmissionMetrics(request)
		return nil
	case <-q.backgroundCtx.Done():
		return errors.New("queue is shutting down")
	default:
		return errors.New("queue is full")
	}
}

// GetNextBatch returns the next batch of requests for processing
func (q *InferenceQueue) GetNextBatch(ctx context.Context) ([]*InferenceRequest, error) {
	// Get recommended batch size from optimizer
	recommendation, err := q.batchOptimizer.GetRecommendation(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get batch size recommendation: %w", err)
	}

	batchSize := recommendation.RecommendedSize
	batch := make([]*InferenceRequest, 0, batchSize)

	q.mu.Lock()
	defer q.mu.Unlock()

	// Select requests based on priority and SLA constraints
	for len(batch) < batchSize {
		request := q.selectNextRequest()
		if request == nil {
			break // No more requests available
		}

		// Mark processing start time
		request.ProcessingStart = time.Now()
		request.QueueTime = request.ProcessingStart.Sub(request.SubmittedAt)

		batch = append(batch, request)
	}

	// Update queue metrics
	q.updateBatchMetrics(batch)

	return batch, nil
}

// CompleteRequest marks a request as completed and updates metrics
func (q *InferenceQueue) CompleteRequest(request *InferenceRequest, result *InferenceResult, err error) {
	q.mu.Lock()
	defer q.mu.Unlock()

	// Update metrics
	if err != nil {
		q.metrics.FailedRequests++
		q.metrics.PriorityMetrics[request.Priority].FailedRequests++
	} else {
		q.metrics.ProcessedRequests++
		q.metrics.PriorityMetrics[request.Priority].ProcessedRequests++
	}

	// Check SLA compliance
	if q.config.EnableSLAMonitoring {
		q.checkSLACompliance(request, result, err)
	}

	// Call result callback if provided
	if request.ResultCallback != nil {
		go request.ResultCallback(result, err)
	}

	// Signal processing completion
	select {
	case q.processingDone <- request:
	case <-q.backgroundCtx.Done():
	}
}

// GetMetrics returns current queue metrics
func (q *InferenceQueue) GetMetrics() *QueueMetrics {
	q.mu.RLock()
	defer q.mu.RUnlock()

	// Create a copy of metrics to avoid race conditions
	metricsCopy := *q.metrics
	metricsCopy.CurrentQueueSize = q.getCurrentQueueSize()
	metricsCopy.LastUpdated = time.Now()

	// Copy priority metrics
	metricsCopy.PriorityMetrics = make(map[RequestPriority]*PriorityMetrics)
	for priority, metrics := range q.metrics.PriorityMetrics {
		metricsCopy.PriorityMetrics[priority] = &PriorityMetrics{
			TotalRequests:      metrics.TotalRequests,
			ProcessedRequests:  metrics.ProcessedRequests,
			FailedRequests:     metrics.FailedRequests,
			AverageQueueTime:   metrics.AverageQueueTime,
			AverageProcessTime: metrics.AverageProcessTime,
			CurrentQueueSize:   len(q.queues[priority]),
		}
	}

	return &metricsCopy
}

// GetQueueStatus returns detailed queue status
func (q *InferenceQueue) GetQueueStatus() map[string]interface{} {
	q.mu.RLock()
	defer q.mu.RUnlock()

	status := map[string]interface{}{
		"total_queue_size":   q.getCurrentQueueSize(),
		"priority_breakdown": make(map[string]int),
		"oldest_request_age": time.Duration(0),
		"sla_violations":     q.metrics.SLAViolations,
		"throughput":         q.metrics.ThroughputCurrent,
	}

	// Calculate priority breakdown and oldest request
	var oldestTime time.Time
	for priority, requests := range q.queues {
		status["priority_breakdown"].(map[string]int)[priority.String()] = len(requests)

		if len(requests) > 0 {
			if oldestTime.IsZero() || requests[0].SubmittedAt.Before(oldestTime) {
				oldestTime = requests[0].SubmittedAt
			}
		}
	}

	if !oldestTime.IsZero() {
		status["oldest_request_age"] = time.Since(oldestTime)
	}

	return status
}

// Close gracefully shuts down the queue
func (q *InferenceQueue) Close() error {
	// Signal shutdown
	q.backgroundCancel()

	// Signal shutdown to background workers
	select {
	case q.shutdown <- true:
	default:
	}

	// Wait for background workers to finish
	q.wg.Wait()

	// Close channels
	close(q.submitChannel)
	close(q.processingDone)
	close(q.shutdown)

	return nil
}

// startBackgroundWorkers starts background goroutines for queue management
func (q *InferenceQueue) startBackgroundWorkers() {
	// Start request processing worker
	q.wg.Add(1)
	go q.requestProcessingWorker()

	// Start SLA monitoring worker if enabled
	if q.config.EnableSLAMonitoring {
		q.wg.Add(1)
		go q.slaMonitoringWorker()
	}

	// Start metrics update worker
	q.wg.Add(1)
	go q.metricsUpdateWorker()
}

// getCurrentQueueSize returns the total number of requests in all priority queues
func (q *InferenceQueue) getCurrentQueueSize() int {
	q.mu.RLock()
	defer q.mu.RUnlock()

	total := 0
	for _, requests := range q.queues {
		total += len(requests)
	}
	return total
}

// handleBackpressure implements backpressure policy when queue is full
func (q *InferenceQueue) handleBackpressure(newRequest *InferenceRequest) error {
	switch q.config.DropPolicy {
	case "oldest":
		return q.dropOldestRequest()
	case "lowest_priority":
		return q.dropLowestPriorityRequest()
	case "sla_violating":
		return q.dropSLAViolatingRequest()
	default:
		return errors.New("queue is full and no drop policy configured")
	}
}

// dropOldestRequest removes the oldest request from any priority queue
func (q *InferenceQueue) dropOldestRequest() error {
	var oldestRequest *InferenceRequest
	var oldestPriority RequestPriority
	var oldestIndex int

	for priority, requests := range q.queues {
		if len(requests) > 0 {
			if oldestRequest == nil || requests[0].SubmittedAt.Before(oldestRequest.SubmittedAt) {
				oldestRequest = requests[0]
				oldestPriority = priority
				oldestIndex = 0
			}
		}
	}

	if oldestRequest != nil {
		q.queues[oldestPriority] = append(q.queues[oldestPriority][:oldestIndex], q.queues[oldestPriority][oldestIndex+1:]...)
		return nil
	}

	return errors.New("no requests to drop")
}

// dropLowestPriorityRequest removes a request from the lowest priority queue
func (q *InferenceQueue) dropLowestPriorityRequest() error {
	for priority := RequestPriorityLow; priority <= RequestPriorityCritical; priority++ {
		if len(q.queues[priority]) > 0 {
			q.queues[priority] = q.queues[priority][1:]
			return nil
		}
	}
	return errors.New("no requests to drop")
}

// dropSLAViolatingRequest removes requests that are already violating SLA
func (q *InferenceQueue) dropSLAViolatingRequest() error {
	now := time.Now()

	for priority, requests := range q.queues {
		for i, request := range requests {
			if sla, exists := q.clientSLAs[request.ClientID]; exists {
				waitTime := now.Sub(request.SubmittedAt)
				if waitTime > sla.MaxLatency {
					q.queues[priority] = append(requests[:i], requests[i+1:]...)
					return nil
				}
			}
		}
	}

	// If no SLA violating requests, fall back to oldest
	return q.dropOldestRequest()
}

// updateSubmissionMetrics updates metrics when a request is submitted
func (q *InferenceQueue) updateSubmissionMetrics(request *InferenceRequest) {
	q.mu.Lock()
	defer q.mu.Unlock()

	q.metrics.TotalRequests++
	q.metrics.PriorityMetrics[request.Priority].TotalRequests++
}

// selectNextRequest selects the next request to process based on priority and SLA
func (q *InferenceQueue) selectNextRequest() *InferenceRequest {
	// Start with highest priority and work down
	for priority := RequestPriorityCritical; priority >= RequestPriorityLow; priority-- {
		if len(q.queues[priority]) > 0 {
			request := q.queues[priority][0]
			q.queues[priority] = q.queues[priority][1:]
			return request
		}
	}
	return nil
}

// updateBatchMetrics updates metrics when a batch is created
func (q *InferenceQueue) updateBatchMetrics(batch []*InferenceRequest) {
	if len(batch) == 0 {
		return
	}

	// Update queue time averages
	totalQueueTime := time.Duration(0)
	for _, request := range batch {
		totalQueueTime += request.QueueTime

		// Update priority-specific metrics
		priorityMetrics := q.metrics.PriorityMetrics[request.Priority]
		priorityMetrics.AverageQueueTime = (priorityMetrics.AverageQueueTime + request.QueueTime) / 2
	}

	avgQueueTime := totalQueueTime / time.Duration(len(batch))
	q.metrics.AverageQueueTime = (q.metrics.AverageQueueTime + avgQueueTime) / 2
}

// checkSLACompliance checks if a request violated SLA requirements
func (q *InferenceQueue) checkSLACompliance(request *InferenceRequest, result *InferenceResult, err error) {
	sla, exists := q.clientSLAs[request.ClientID]
	if !exists {
		return
	}

	totalLatency := time.Since(request.SubmittedAt)
	if result != nil {
		result.TotalLatency = totalLatency
	}

	// Check if SLA was violated
	if totalLatency > sla.MaxLatency || err != nil {
		q.metrics.SLAViolations++
	}
}

// requestProcessingWorker processes incoming requests and adds them to queues
func (q *InferenceQueue) requestProcessingWorker() {
	defer q.wg.Done()

	for {
		select {
		case request := <-q.submitChannel:
			q.mu.Lock()

			// Apply priority boost if enabled
			if q.config.EnablePriorityBoost {
				q.applyPriorityBoost(request)
			}

			// Add to appropriate priority queue
			q.queues[request.Priority] = append(q.queues[request.Priority], request)

			q.mu.Unlock()

		case <-q.shutdown:
			return
		case <-q.backgroundCtx.Done():
			return
		}
	}
}

// applyPriorityBoost applies priority boost based on wait time
func (q *InferenceQueue) applyPriorityBoost(request *InferenceRequest) {
	waitTime := time.Since(request.SubmittedAt)

	// Calculate boost based on wait time and configuration
	boostThreshold := q.config.MaxBatchWaitTime * 2
	if waitTime > boostThreshold && request.Priority < RequestPriorityCritical {
		// Boost priority by one level
		request.Priority++
	}
}

// slaMonitoringWorker monitors SLA compliance and adjusts priorities
func (q *InferenceQueue) slaMonitoringWorker() {
	defer q.wg.Done()

	ticker := time.NewTicker(q.config.SLACheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			q.performSLACheck()
		case <-q.shutdown:
			return
		case <-q.backgroundCtx.Done():
			return
		}
	}
}

// performSLACheck checks all queued requests for SLA violations
func (q *InferenceQueue) performSLACheck() {
	q.mu.Lock()
	defer q.mu.Unlock()

	now := time.Now()

	for priority, requests := range q.queues {
		for _, request := range requests {
			if sla, exists := q.clientSLAs[request.ClientID]; exists {
				waitTime := now.Sub(request.SubmittedAt)

				// If request is approaching SLA deadline, boost priority
				if waitTime > sla.MaxLatency/2 && request.Priority < RequestPriorityCritical {
					// Remove from current queue
					for i, r := range requests {
						if r.ID == request.ID {
							q.queues[priority] = append(requests[:i], requests[i+1:]...)
							break
						}
					}

					// Add to higher priority queue
					newPriority := request.Priority + 1
					request.Priority = newPriority
					q.queues[newPriority] = append(q.queues[newPriority], request)
					break
				}
			}
		}
	}
}

// metricsUpdateWorker periodically updates throughput and other metrics
func (q *InferenceQueue) metricsUpdateWorker() {
	defer q.wg.Done()

	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	var lastProcessedCount int64
	lastUpdate := time.Now()

	for {
		select {
		case <-ticker.C:
			q.mu.Lock()

			// Calculate throughput
			now := time.Now()
			duration := now.Sub(lastUpdate).Seconds()
			if duration > 0 {
				currentProcessed := q.metrics.ProcessedRequests
				throughput := float64(currentProcessed-lastProcessedCount) / duration
				q.metrics.ThroughputCurrent = throughput

				if throughput > q.metrics.ThroughputPeak {
					q.metrics.ThroughputPeak = throughput
				}

				lastProcessedCount = currentProcessed
				lastUpdate = now
			}

			q.mu.Unlock()

		case <-q.shutdown:
			return
		case <-q.backgroundCtx.Done():
			return
		}
	}
}
