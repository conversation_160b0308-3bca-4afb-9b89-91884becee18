//go:build legacytest
// +build legacytest

package gpu

import (
	"context"
	"fmt"
	"log"
	"os"
	"testing"
	"time"
)

func TestInterGPUSynchronizer_BasicFunctionality(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	// Create mock managed devices directly
	devices := createMockManagedDevices(2)

	// Create a simple device manager configuration
	config := DefaultMultiDeviceConfig()
	config.MaxDevices = 2
	config.MinDevices = 1

	deviceManager, err := NewMultiDeviceManager(config, logger)
	if err != nil {
		t.Fatalf("Failed to create device manager: %v", err)
	}

	// Mock initialization by setting internal state
	deviceManager.devices = devices
	deviceManager.deviceMap = make(map[string]*ManagedDevice)
	for _, device := range devices {
		deviceManager.deviceMap[device.Device.ID] = device
	}
	deviceManager.isInitialized = true

	syncManager := NewSynchronizationManager(logger)
	err = syncManager.Initialize(devices)
	if err != nil {
		t.Fatalf("Failed to initialize sync manager: %v", err)
	}

	// Create inter-GPU synchronizer
	interSync := NewInterGPUSynchronizer(deviceManager, syncManager, logger)

	ctx := context.Background()
	err = interSync.Initialize(ctx)
	if err != nil {
		t.Fatalf("Failed to initialize inter-GPU synchronizer: %v", err)
	}

	if !interSync.IsInitialized() {
		t.Error("Inter-GPU synchronizer should be initialized")
	}

	// Test basic communication
	device1 := devices[0].Device.ID
	device2 := devices[1].Device.ID

	// Test message sending
	err = interSync.SendMessage(device1, device2, InterGPUMessageTypeSync, "test message")
	if err != nil {
		t.Errorf("Failed to send message: %v", err)
	}

	// Test memory transfer scheduling
	_, err = interSync.TransferMemory(device1, device2, 0x1000, 0x2000, 1024, TransferPriorityNormal)
	if err != nil {
		t.Errorf("Failed to schedule memory transfer: %v", err)
	}

	// Test device health
	health, err := interSync.GetDeviceHealth(device1)
	if err != nil {
		t.Errorf("Failed to get device health: %v", err)
	}

	if health == nil {
		t.Error("Expected device health information")
	} else {
		if health.Status != DeviceStatusHealthy {
			t.Errorf("Expected healthy status, got %v", health.Status)
		}
	}

	// Cleanup
	defer interSync.Cleanup()
}

func TestInterGPUCommunicator_BasicMessaging(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	devices := createMockManagedDevices(2)

	comm := NewInterGPUCommunicator(logger)
	err := comm.Initialize(devices)
	if err != nil {
		t.Fatalf("Failed to initialize communicator: %v", err)
	}
	defer comm.Cleanup()

	sourceDevice := devices[0].Device.ID
	targetDevice := devices[1].Device.ID

	// Test sending a message
	err = comm.SendMessage(sourceDevice, targetDevice, InterGPUMessageTypeSync, "test payload")
	if err != nil {
		t.Errorf("Failed to send message: %v", err)
	}

	// Test receiving a message
	message, err := comm.ReceiveMessage(targetDevice, 1*time.Second)
	if err != nil {
		t.Errorf("Failed to receive message: %v", err)
	}

	if message != nil {
		if message.Source != sourceDevice {
			t.Errorf("Expected source %s, got %s", sourceDevice, message.Source)
		}

		if message.Type != InterGPUMessageTypeSync {
			t.Errorf("Expected message type %s, got %s", InterGPUMessageTypeSync, message.Type)
		}

		if message.Payload != "test payload" {
			t.Errorf("Expected payload 'test payload', got %v", message.Payload)
		}
	}
}

func TestCollectiveOperations_SimpleBarrier(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	devices := createMockManagedDevices(2)

	config := DefaultMultiDeviceConfig()
	deviceManager, err := NewMultiDeviceManager(config, logger)
	if err != nil {
		t.Fatalf("Failed to create device manager: %v", err)
	}

	comm := NewInterGPUCommunicator(logger)
	err = comm.Initialize(devices)
	if err != nil {
		t.Fatalf("Failed to initialize communicator: %v", err)
	}
	defer comm.Cleanup()

	collOps := NewCollectiveOperations(deviceManager, comm, logger)
	err = collOps.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize collective operations: %v", err)
	}
	defer collOps.Cleanup()

	ctx := context.Background()
	barrierID := "test_barrier"
	participants := []string{devices[0].Device.ID, devices[1].Device.ID}

	// Create barrier
	err = collOps.CreateBarrier(ctx, barrierID, participants, 5*time.Second)
	if err != nil {
		t.Errorf("Failed to create barrier: %v", err)
	}

	// Test devices arriving at barrier
	for i, device := range devices {
		err = collOps.WaitAtBarrier(ctx, barrierID, device.Device.ID)
		if err != nil {
			t.Errorf("Device %d failed to wait at barrier: %v", i, err)
		}
	}
}

func TestFaultToleranceManager_BasicOperations(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	devices := createMockManagedDevices(2)

	ftm := NewFaultToleranceManager(logger)
	err := ftm.Initialize(devices)
	if err != nil {
		t.Fatalf("Failed to initialize fault tolerance manager: %v", err)
	}
	defer ftm.Cleanup()

	deviceID := devices[0].Device.ID

	// Test getting device state
	state, err := ftm.GetDeviceState(deviceID)
	if err != nil {
		t.Errorf("Failed to get device state: %v", err)
	}

	if state != nil {
		if state.DeviceID != deviceID {
			t.Errorf("Expected device ID %s, got %s", deviceID, state.DeviceID)
		}

		if state.Status != DeviceStatusHealthy {
			t.Errorf("Expected status healthy, got %v", state.Status)
		}
	}

	// Test recording a fault
	err = ftm.RecordFault(deviceID, FaultTypeMemory, "Test memory fault", FaultSeverityHigh)
	if err != nil {
		t.Errorf("Failed to record fault: %v", err)
	}

	// Verify fault was recorded
	stateAfterFault, err := ftm.GetDeviceState(deviceID)
	if err != nil {
		t.Errorf("Failed to get device state after fault: %v", err)
	}

	if stateAfterFault != nil {
		if stateAfterFault.ErrorCount != 1 {
			t.Errorf("Expected error count 1, got %d", stateAfterFault.ErrorCount)
		}

		if stateAfterFault.Status != DeviceStatusDegraded {
			t.Errorf("Expected status degraded, got %v", stateAfterFault.Status)
		}
	}
}

// Helper function to create mock managed devices for testing
func createMockManagedDevices(count int) []*ManagedDevice {
	devices := make([]*ManagedDevice, count)
	for i := 0; i < count; i++ {
		device := &GPUDevice{
			ID:           fmt.Sprintf("device_%d", i),
			Name:         fmt.Sprintf("Mock GPU Device %d", i),
			Vendor:       "MockVendor",
			Architecture: "MockArch",
			Backend:      "CUDA",
			Available:    true,
			Memory: GPUMemoryInfo{
				Total:      8 * 1024 * 1024 * 1024, // 8GB
				Free:       6 * 1024 * 1024 * 1024, // 6GB
				Used:       2 * 1024 * 1024 * 1024, // 2GB
				Type:       MemoryTypeDiscrete,
				Bandwidth:  500, // 500 GB/s
				BusWidth:   256,
				ClockSpeed: 1500,
			},
			Compute: GPUComputeInfo{
				Units:       2048,
				ClockSpeed:  1400,
				BoostClock:  1600,
				Performance: 10000.0, // 10 TFLOPS
				Precision:   "FP32",
			},
			Capabilities: make(map[string]interface{}),
		}

		managedDevice := &ManagedDevice{
			Device:    device,
			Context:   nil, // Mock context
			MemoryMgr: nil, // Mock memory manager
			Executor:  nil, // Mock executor
			Metrics: &DeviceMetrics{
				ID:                 device.ID,
				MemoryUtilization:  0.25, // 25% used
				ComputeUtilization: 0.0,

				PowerConsumption: 150.0,
				ErrorCount:       0,
				LastUpdate:       time.Now(),
			},
			LoadLevel: 0.0,
			LastUsed:  time.Now(),
			IsActive:  true,
		}

		devices[i] = managedDevice
	}
	return devices
}
