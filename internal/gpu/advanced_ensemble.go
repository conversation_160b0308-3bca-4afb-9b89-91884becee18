package gpu

import (
	"fmt"
	"log"
	"math"
	"sync"
	"time"
)

// EnsembleMethod defines the type of ensemble technique
type EnsembleMethod int

const (
	WeightedAveraging EnsembleMethod = iota
	Stacking
	BayesianModelAveraging
	VotingEnsemble
	AdaptiveWeighting
)

// AdvancedEnsemble combines multiple prediction models with sophisticated ensemble techniques
type AdvancedEnsemble struct {
	// Traditional time series models
	timeSeriesModels  []PredictionModel
	timeSeriesWeights []float64

	// Deep learning models
	deepLearningModels  []PredictionModel
	deepLearningWeights []float64

	// Meta-learner for stacking
	metaLearner PredictionModel

	// Ensemble configuration
	method               EnsembleMethod
	adaptiveLearningRate float64
	crossValidationFolds int

	// Performance tracking
	accuracy          float64
	modelPerformances []ModelPerformance
	lastTrained       time.Time

	// Thread safety
	mu sync.RWMutex
}

// ModelPerformance tracks individual model performance metrics
type ModelPerformance struct {
	ModelName   string
	Accuracy    float64
	RMSE        float64
	MAE         float64
	Predictions []float64
	Errors      []float64
	LastUpdated time.Time
}

// EnsembleConfig defines configuration for advanced ensemble
type EnsembleConfig struct {
	Method               EnsembleMethod
	IncludeTimeSeries    bool
	IncludeDeepLearning  bool
	AdaptiveLearningRate float64
	CrossValidationFolds int
	MetaLearnerType      string
}

// NewAdvancedEnsemble creates a new advanced ensemble model
func NewAdvancedEnsemble(config EnsembleConfig) *AdvancedEnsemble {
	ensemble := &AdvancedEnsemble{
		method:               config.Method,
		adaptiveLearningRate: config.AdaptiveLearningRate,
		crossValidationFolds: config.CrossValidationFolds,
		modelPerformances:    make([]ModelPerformance, 0),
	}

	// Initialize time series models if requested
	if config.IncludeTimeSeries {
		ensemble.timeSeriesModels = []PredictionModel{
			NewLinearRegressionModel(),
			NewMovingAverageModel(),
			NewExponentialSmoothingModel(),
			NewARIMAModel(),
			NewHoltWintersModel(),
		}
		ensemble.timeSeriesWeights = make([]float64, len(ensemble.timeSeriesModels))
		// Initialize with equal weights
		for i := range ensemble.timeSeriesWeights {
			ensemble.timeSeriesWeights[i] = 1.0 / float64(len(ensemble.timeSeriesModels))
		}
	}

	// Initialize deep learning models if requested
	if config.IncludeDeepLearning {
		ensemble.deepLearningModels = []PredictionModel{
			NewLSTMPredictionModel(),
			NewGRUPredictionModel(),
		}
		ensemble.deepLearningWeights = make([]float64, len(ensemble.deepLearningModels))
		// Initialize with equal weights
		for i := range ensemble.deepLearningWeights {
			ensemble.deepLearningWeights[i] = 1.0 / float64(len(ensemble.deepLearningModels))
		}
	}

	// Initialize meta-learner for stacking
	if config.Method == Stacking {
		switch config.MetaLearnerType {
		case "lstm":
			ensemble.metaLearner = NewLSTMPredictionModel()
		default:
			ensemble.metaLearner = NewLinearRegressionModel()
		}
	}

	return ensemble
}

// Train trains all models in the ensemble
func (ae *AdvancedEnsemble) Train(data []WorkloadDataPoint) error {
	ae.mu.Lock()
	defer ae.mu.Unlock()

	if len(data) < 20 {
		return fmt.Errorf("need at least 20 data points for advanced ensemble")
	}

	log.Printf("Training advanced ensemble with %d data points using method %d", len(data), ae.method)

	// Split data for cross-validation if using stacking
	var trainData, validationData []WorkloadDataPoint
	if ae.method == Stacking {
		splitIndex := int(0.8 * float64(len(data)))
		trainData = data[:splitIndex]
		validationData = data[splitIndex:]
	} else {
		trainData = data
	}

	// Train time series models
	ae.trainTimeSeriesModels(trainData)

	// Train deep learning models
	ae.trainDeepLearningModels(trainData)

	// Train meta-learner for stacking
	if ae.method == Stacking && len(validationData) > 0 {
		ae.trainMetaLearner(validationData)
	}

	// Update ensemble weights based on method
	ae.updateEnsembleWeights(trainData)

	// Calculate overall ensemble accuracy
	ae.calculateEnsembleAccuracy(trainData)

	ae.lastTrained = time.Now()
	log.Printf("Advanced ensemble training completed. Accuracy: %.2f%%", ae.accuracy*100)

	return nil
}

// trainTimeSeriesModels trains all time series models
func (ae *AdvancedEnsemble) trainTimeSeriesModels(data []WorkloadDataPoint) {
	var wg sync.WaitGroup
	performanceChan := make(chan ModelPerformance, len(ae.timeSeriesModels))

	for i, model := range ae.timeSeriesModels {
		wg.Add(1)
		go func(idx int, m PredictionModel) {
			defer wg.Done()

			modelName := fmt.Sprintf("TimeSeries_%d", idx)
			performance := ModelPerformance{
				ModelName:   modelName,
				LastUpdated: time.Now(),
			}

			if err := m.Train(data); err == nil {
				performance.Accuracy = m.GetAccuracy()
				performance.RMSE, performance.MAE = ae.calculateModelMetrics(m, data)
			} else {
				log.Printf("Failed to train time series model %d: %v", idx, err)
				performance.Accuracy = 0.0
			}

			performanceChan <- performance
		}(i, model)
	}

	wg.Wait()
	close(performanceChan)

	// Collect performance metrics
	for performance := range performanceChan {
		ae.modelPerformances = append(ae.modelPerformances, performance)
	}
}

// trainDeepLearningModels trains all deep learning models
func (ae *AdvancedEnsemble) trainDeepLearningModels(data []WorkloadDataPoint) {
	var wg sync.WaitGroup
	performanceChan := make(chan ModelPerformance, len(ae.deepLearningModels))

	for i, model := range ae.deepLearningModels {
		wg.Add(1)
		go func(idx int, m PredictionModel) {
			defer wg.Done()

			modelName := fmt.Sprintf("DeepLearning_%d", idx)
			performance := ModelPerformance{
				ModelName:   modelName,
				LastUpdated: time.Now(),
			}

			if err := m.Train(data); err == nil {
				performance.Accuracy = m.GetAccuracy()
				performance.RMSE, performance.MAE = ae.calculateModelMetrics(m, data)
			} else {
				log.Printf("Failed to train deep learning model %d: %v", idx, err)
				performance.Accuracy = 0.0
			}

			performanceChan <- performance
		}(i, model)
	}

	wg.Wait()
	close(performanceChan)

	// Collect performance metrics
	for performance := range performanceChan {
		ae.modelPerformances = append(ae.modelPerformances, performance)
	}
}

// trainMetaLearner trains the meta-learner for stacking
func (ae *AdvancedEnsemble) trainMetaLearner(validationData []WorkloadDataPoint) error {
	if ae.metaLearner == nil {
		return fmt.Errorf("meta-learner not initialized")
	}

	// Generate meta-features from base model predictions
	metaFeatures := ae.generateMetaFeatures(validationData)

	// Train meta-learner on meta-features
	return ae.metaLearner.Train(metaFeatures)
}

// generateMetaFeatures creates meta-features from base model predictions
func (ae *AdvancedEnsemble) generateMetaFeatures(data []WorkloadDataPoint) []WorkloadDataPoint {
	metaFeatures := make([]WorkloadDataPoint, 0, len(data))

	for _, dataPoint := range data {
		// Create a single data point to get prediction from each model
		_ = dataPoint // Use dataPoint for predictions

		// Collect predictions from all models
		var predictions []float64

		// Time series model predictions
		for _, model := range ae.timeSeriesModels {
			if pred, err := model.Predict(time.Hour); err == nil {
				predictions = append(predictions, float64(pred.PredictedQueue))
			}
		}

		// Deep learning model predictions
		for _, model := range ae.deepLearningModels {
			if pred, err := model.Predict(time.Hour); err == nil {
				predictions = append(predictions, float64(pred.PredictedQueue))
			}
		}

		// Create meta-feature data point
		if len(predictions) > 0 {
			// Use average of predictions as meta-feature
			avgPrediction := 0.0
			for _, p := range predictions {
				avgPrediction += p
			}
			avgPrediction /= float64(len(predictions))

			metaFeature := WorkloadDataPoint{
				Timestamp:      dataPoint.Timestamp,
				QueueLength:    int(avgPrediction),
				ActiveTasks:    dataPoint.ActiveTasks,
				AvgUtilization: dataPoint.AvgUtilization,
				NodesActive:    dataPoint.NodesActive,
			}
			metaFeatures = append(metaFeatures, metaFeature)
		}
	}

	return metaFeatures
}

// updateEnsembleWeights updates model weights based on the ensemble method
func (ae *AdvancedEnsemble) updateEnsembleWeights(data []WorkloadDataPoint) {
	switch ae.method {
	case WeightedAveraging:
		ae.updateWeightedAveragingWeights()
	case BayesianModelAveraging:
		ae.updateBayesianWeights()
	case VotingEnsemble:
		ae.updateVotingWeights()
	case AdaptiveWeighting:
		ae.updateAdaptiveWeights(data)
	default:
		ae.updateWeightedAveragingWeights()
	}
}

// updateWeightedAveragingWeights updates weights based on model accuracy
func (ae *AdvancedEnsemble) updateWeightedAveragingWeights() {
	// Update time series weights
	if len(ae.timeSeriesModels) > 0 {
		totalAccuracy := 0.0
		accuracies := make([]float64, len(ae.timeSeriesModels))

		for i, model := range ae.timeSeriesModels {
			accuracies[i] = model.GetAccuracy()
			totalAccuracy += accuracies[i]
		}

		if totalAccuracy > 0 {
			for i := range ae.timeSeriesWeights {
				ae.timeSeriesWeights[i] = accuracies[i] / totalAccuracy
			}
		}
	}

	// Update deep learning weights
	if len(ae.deepLearningModels) > 0 {
		totalAccuracy := 0.0
		accuracies := make([]float64, len(ae.deepLearningModels))

		for i, model := range ae.deepLearningModels {
			accuracies[i] = model.GetAccuracy()
			totalAccuracy += accuracies[i]
		}

		if totalAccuracy > 0 {
			for i := range ae.deepLearningWeights {
				ae.deepLearningWeights[i] = accuracies[i] / totalAccuracy
			}
		}
	}
}

// updateBayesianWeights updates weights using Bayesian model averaging
func (ae *AdvancedEnsemble) updateBayesianWeights() {
	// Simplified Bayesian approach using model likelihood

	// Time series models
	if len(ae.timeSeriesModels) > 0 {
		likelihoods := make([]float64, len(ae.timeSeriesModels))
		totalLikelihood := 0.0

		for i, model := range ae.timeSeriesModels {
			accuracy := model.GetAccuracy()
			// Convert accuracy to likelihood (higher accuracy = higher likelihood)
			likelihoods[i] = math.Exp(accuracy * 10) // Scale for numerical stability
			totalLikelihood += likelihoods[i]
		}

		if totalLikelihood > 0 {
			for i := range ae.timeSeriesWeights {
				ae.timeSeriesWeights[i] = likelihoods[i] / totalLikelihood
			}
		}
	}

	// Deep learning models
	if len(ae.deepLearningModels) > 0 {
		likelihoods := make([]float64, len(ae.deepLearningModels))
		totalLikelihood := 0.0

		for i, model := range ae.deepLearningModels {
			accuracy := model.GetAccuracy()
			likelihoods[i] = math.Exp(accuracy * 10)
			totalLikelihood += likelihoods[i]
		}

		if totalLikelihood > 0 {
			for i := range ae.deepLearningWeights {
				ae.deepLearningWeights[i] = likelihoods[i] / totalLikelihood
			}
		}
	}
}

// updateVotingWeights sets equal weights for voting ensemble
func (ae *AdvancedEnsemble) updateVotingWeights() {
	// Equal weights for voting
	if len(ae.timeSeriesModels) > 0 {
		weight := 1.0 / float64(len(ae.timeSeriesModels))
		for i := range ae.timeSeriesWeights {
			ae.timeSeriesWeights[i] = weight
		}
	}

	if len(ae.deepLearningModels) > 0 {
		weight := 1.0 / float64(len(ae.deepLearningModels))
		for i := range ae.deepLearningWeights {
			ae.deepLearningWeights[i] = weight
		}
	}
}

// updateAdaptiveWeights updates weights adaptively based on recent performance
func (ae *AdvancedEnsemble) updateAdaptiveWeights(data []WorkloadDataPoint) {
	// Use exponential moving average of recent performance
	recentData := data
	if len(data) > 50 {
		recentData = data[len(data)-50:] // Use last 50 data points
	}

	// Calculate recent performance for time series models
	if len(ae.timeSeriesModels) > 0 {
		recentPerformances := make([]float64, len(ae.timeSeriesModels))
		totalPerformance := 0.0

		for i, model := range ae.timeSeriesModels {
			rmse, _ := ae.calculateModelMetrics(model, recentData)
			// Convert RMSE to performance (lower RMSE = higher performance)
			recentPerformances[i] = 1.0 / (1.0 + rmse)
			totalPerformance += recentPerformances[i]
		}

		// Update weights with learning rate
		if totalPerformance > 0 {
			for i := range ae.timeSeriesWeights {
				newWeight := recentPerformances[i] / totalPerformance
				ae.timeSeriesWeights[i] = (1-ae.adaptiveLearningRate)*ae.timeSeriesWeights[i] +
					ae.adaptiveLearningRate*newWeight
			}
		}
	}

	// Similar for deep learning models
	if len(ae.deepLearningModels) > 0 {
		recentPerformances := make([]float64, len(ae.deepLearningModels))
		totalPerformance := 0.0

		for i, model := range ae.deepLearningModels {
			rmse, _ := ae.calculateModelMetrics(model, recentData)
			recentPerformances[i] = 1.0 / (1.0 + rmse)
			totalPerformance += recentPerformances[i]
		}

		if totalPerformance > 0 {
			for i := range ae.deepLearningWeights {
				newWeight := recentPerformances[i] / totalPerformance
				ae.deepLearningWeights[i] = (1-ae.adaptiveLearningRate)*ae.deepLearningWeights[i] +
					ae.adaptiveLearningRate*newWeight
			}
		}
	}
}

// calculateModelMetrics calculates RMSE and MAE for a model
func (ae *AdvancedEnsemble) calculateModelMetrics(model PredictionModel, data []WorkloadDataPoint) (rmse, mae float64) {
	if len(data) < 2 {
		return 0.0, 0.0
	}

	var errors []float64
	for i := 1; i < len(data); i++ {
		// Use previous data point to predict current
		if pred, err := model.Predict(time.Hour); err == nil {
			actual := float64(data[i].QueueLength)
			predicted := float64(pred.PredictedQueue)
			error := actual - predicted
			errors = append(errors, error)
		}
	}

	if len(errors) == 0 {
		return 0.0, 0.0
	}

	// Calculate RMSE
	sumSquaredErrors := 0.0
	sumAbsErrors := 0.0
	for _, error := range errors {
		sumSquaredErrors += error * error
		sumAbsErrors += math.Abs(error)
	}

	rmse = math.Sqrt(sumSquaredErrors / float64(len(errors)))
	mae = sumAbsErrors / float64(len(errors))

	return rmse, mae
}

// calculateEnsembleAccuracy calculates overall ensemble accuracy
func (ae *AdvancedEnsemble) calculateEnsembleAccuracy(data []WorkloadDataPoint) {
	if len(data) < 2 {
		ae.accuracy = 0.5
		return
	}

	var predictions []float64
	var actuals []float64

	// Use internal prediction method to avoid locking issues
	for i := 1; i < len(data); i++ {
		if pred, err := ae.predictInternal(time.Hour); err == nil {
			predictions = append(predictions, float64(pred.PredictedQueue))
			actuals = append(actuals, float64(data[i].QueueLength))
		}
	}

	if len(predictions) == 0 {
		ae.accuracy = 0.5
		return
	}

	// Calculate MAPE (Mean Absolute Percentage Error)
	totalAPE := 0.0
	validPoints := 0

	for i := range predictions {
		if actuals[i] != 0 {
			ape := math.Abs((actuals[i] - predictions[i]) / actuals[i])
			totalAPE += ape
			validPoints++
		}
	}

	if validPoints > 0 {
		mape := totalAPE / float64(validPoints)
		ae.accuracy = math.Max(0.0, 1.0-mape) // Convert MAPE to accuracy
	} else {
		ae.accuracy = 0.5
	}
}

// Predict generates an ensemble prediction
func (ae *AdvancedEnsemble) Predict(horizon time.Duration) (WorkloadPrediction, error) {
	ae.mu.RLock()
	defer ae.mu.RUnlock()

	if ae.lastTrained.IsZero() {
		return WorkloadPrediction{}, fmt.Errorf("advanced ensemble not trained")
	}

	return ae.predictInternal(horizon)
}

// predictInternal generates prediction without acquiring locks (for internal use)
func (ae *AdvancedEnsemble) predictInternal(horizon time.Duration) (WorkloadPrediction, error) {
	if ae.lastTrained.IsZero() {
		return WorkloadPrediction{}, fmt.Errorf("advanced ensemble not trained")
	}

	switch ae.method {
	case Stacking:
		return ae.predictWithStacking(horizon)
	default:
		return ae.predictWithWeightedAveraging(horizon)
	}
}

// predictWithWeightedAveraging generates prediction using weighted averaging
func (ae *AdvancedEnsemble) predictWithWeightedAveraging(horizon time.Duration) (WorkloadPrediction, error) {
	var allPredictions []WorkloadPrediction
	var allWeights []float64

	// Get predictions from time series models
	for i, model := range ae.timeSeriesModels {
		if pred, err := model.Predict(horizon); err == nil {
			allPredictions = append(allPredictions, pred)
			allWeights = append(allWeights, ae.timeSeriesWeights[i])
		}
	}

	// Get predictions from deep learning models
	for i, model := range ae.deepLearningModels {
		if pred, err := model.Predict(horizon); err == nil {
			allPredictions = append(allPredictions, pred)
			allWeights = append(allWeights, ae.deepLearningWeights[i])
		}
	}

	if len(allPredictions) == 0 {
		return WorkloadPrediction{}, fmt.Errorf("no models could generate predictions")
	}

	// Normalize weights
	totalWeight := 0.0
	for _, w := range allWeights {
		totalWeight += w
	}
	for i := range allWeights {
		allWeights[i] /= totalWeight
	}

	// Combine predictions
	return ae.combineWeightedPredictions(allPredictions, allWeights, horizon)
}

// predictWithStacking generates prediction using stacking
func (ae *AdvancedEnsemble) predictWithStacking(horizon time.Duration) (WorkloadPrediction, error) {
	if ae.metaLearner == nil {
		return WorkloadPrediction{}, fmt.Errorf("meta-learner not available for stacking")
	}

	// Get base model predictions
	var basePredictions []float64

	for _, model := range ae.timeSeriesModels {
		if pred, err := model.Predict(horizon); err == nil {
			basePredictions = append(basePredictions, float64(pred.PredictedQueue))
		}
	}

	for _, model := range ae.deepLearningModels {
		if pred, err := model.Predict(horizon); err == nil {
			basePredictions = append(basePredictions, float64(pred.PredictedQueue))
		}
	}

	if len(basePredictions) == 0 {
		return WorkloadPrediction{}, fmt.Errorf("no base predictions available for stacking")
	}

	// Use meta-learner to generate final prediction
	return ae.metaLearner.Predict(horizon)
}

// combineWeightedPredictions combines multiple predictions using weights
func (ae *AdvancedEnsemble) combineWeightedPredictions(predictions []WorkloadPrediction, weights []float64, horizon time.Duration) (WorkloadPrediction, error) {
	var weightedQueue, weightedTasks, weightedNodes float64
	var weightedConfidence float64
	actionCounts := make(map[ScalingAction]float64)

	for i, pred := range predictions {
		weight := weights[i]
		weightedQueue += float64(pred.PredictedQueue) * weight
		weightedTasks += float64(pred.PredictedTasks) * weight
		weightedNodes += float64(pred.PredictedNodes) * weight
		weightedConfidence += pred.Confidence * weight
		actionCounts[pred.RecommendedAction] += weight
	}

	// Determine most weighted action
	var bestAction ScalingAction
	maxWeight := 0.0
	for action, weight := range actionCounts {
		if weight > maxWeight {
			maxWeight = weight
			bestAction = action
		}
	}

	return WorkloadPrediction{
		Timestamp:         time.Now().Add(horizon),
		PredictedQueue:    int(math.Round(weightedQueue)),
		PredictedTasks:    int(math.Round(weightedTasks)),
		PredictedNodes:    int(math.Round(weightedNodes)),
		Confidence:        weightedConfidence,
		RecommendedAction: bestAction,
	}, nil
}

// GetAccuracy returns the ensemble accuracy
func (ae *AdvancedEnsemble) GetAccuracy() float64 {
	ae.mu.RLock()
	defer ae.mu.RUnlock()
	return ae.accuracy
}

// GetModelPerformances returns performance metrics for all models
func (ae *AdvancedEnsemble) GetModelPerformances() []ModelPerformance {
	ae.mu.RLock()
	defer ae.mu.RUnlock()
	return ae.modelPerformances
}

// GetEnsembleWeights returns current model weights
func (ae *AdvancedEnsemble) GetEnsembleWeights() ([]float64, []float64) {
	ae.mu.RLock()
	defer ae.mu.RUnlock()
	return ae.timeSeriesWeights, ae.deepLearningWeights
}

// CrossValidate performs cross-validation to evaluate ensemble performance
func (ae *AdvancedEnsemble) CrossValidate(data []WorkloadDataPoint, folds int) (float64, error) {
	if len(data) < folds {
		return 0.0, fmt.Errorf("insufficient data for %d-fold cross-validation", folds)
	}

	foldSize := len(data) / folds
	var accuracies []float64

	for i := 0; i < folds; i++ {
		// Split data into training and validation sets
		start := i * foldSize
		end := start + foldSize
		if i == folds-1 {
			end = len(data) // Include remaining data in last fold
		}

		validationData := data[start:end]
		trainData := append(data[:start], data[end:]...)

		// Create temporary ensemble for this fold
		config := EnsembleConfig{
			Method:               ae.method,
			IncludeTimeSeries:    len(ae.timeSeriesModels) > 0,
			IncludeDeepLearning:  len(ae.deepLearningModels) > 0,
			AdaptiveLearningRate: ae.adaptiveLearningRate,
			CrossValidationFolds: ae.crossValidationFolds,
		}
		tempEnsemble := NewAdvancedEnsemble(config)

		// Train on training data
		if err := tempEnsemble.Train(trainData); err != nil {
			continue
		}

		// Evaluate on validation data
		tempEnsemble.calculateEnsembleAccuracy(validationData)
		accuracies = append(accuracies, tempEnsemble.GetAccuracy())
	}

	if len(accuracies) == 0 {
		return 0.0, fmt.Errorf("cross-validation failed")
	}

	// Calculate average accuracy
	totalAccuracy := 0.0
	for _, acc := range accuracies {
		totalAccuracy += acc
	}

	return totalAccuracy / float64(len(accuracies)), nil
}

// AdvancedEnsemblePredictionModel adapts AdvancedEnsemble to PredictionModel interface
type AdvancedEnsemblePredictionModel struct {
	ensemble    *AdvancedEnsemble
	accuracy    float64
	lastTrained time.Time
	mu          sync.RWMutex
}

// NewAdvancedEnsemblePredictionModel creates a new advanced ensemble prediction model
func NewAdvancedEnsemblePredictionModel(config EnsembleConfig) *AdvancedEnsemblePredictionModel {
	return &AdvancedEnsemblePredictionModel{
		ensemble: NewAdvancedEnsemble(config),
	}
}

// Train trains the advanced ensemble model
func (aepm *AdvancedEnsemblePredictionModel) Train(data []WorkloadDataPoint) error {
	aepm.mu.Lock()
	defer aepm.mu.Unlock()

	err := aepm.ensemble.Train(data)
	if err != nil {
		return err
	}

	aepm.accuracy = aepm.ensemble.GetAccuracy()
	aepm.lastTrained = time.Now()

	return nil
}

// Predict generates a prediction using the advanced ensemble
func (aepm *AdvancedEnsemblePredictionModel) Predict(horizon time.Duration) (WorkloadPrediction, error) {
	aepm.mu.RLock()
	defer aepm.mu.RUnlock()

	return aepm.ensemble.Predict(horizon)
}

// GetAccuracy returns the ensemble accuracy
func (aepm *AdvancedEnsemblePredictionModel) GetAccuracy() float64 {
	aepm.mu.RLock()
	defer aepm.mu.RUnlock()
	return aepm.accuracy
}
