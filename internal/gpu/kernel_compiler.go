package gpu

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"
)

// KernelLanguage represents the kernel programming language
type KernelLanguage string

const (
	KernelCUDA   KernelLanguage = "CUDA"
	KernelOpenCL KernelLanguage = "OpenCL"
	KernelROCm   KernelLanguage = "ROCm"
	KernelMetal  KernelLanguage = "Metal"
)

// OptimizationLevel represents compilation optimization levels
type OptimizationLevel string

const (
	OptimizationNone   OptimizationLevel = "O0"
	OptimizationBasic  OptimizationLevel = "O1"
	OptimizationMedium OptimizationLevel = "O2"
	OptimizationFull   OptimizationLevel = "O3"
	OptimizationAuto   OptimizationLevel = "auto"
)

// KernelType represents different types of kernels
type KernelType string

const (
	KernelTypeCompute     KernelType = "compute"
	KernelTypeMemory      KernelType = "memory"
	KernelTypeReduction   KernelType = "reduction"
	KernelTypeConvolution KernelType = "convolution"
	KernelTypeMatmul      KernelType = "matmul"
	KernelTypeCustom      KernelType = "custom"
)

// CompilationTarget represents the target architecture for compilation
type CompilationTarget struct {
	Architecture       string            `json:"architecture"`
	ComputeCapability  ComputeCapability `json:"compute_capability"`
	MaxThreadsPerBlock int               `json:"max_threads_per_block"`
	SharedMemorySize   int64             `json:"shared_memory_size"`
	WarpSize           int               `json:"warp_size"`
	Extensions         []string          `json:"extensions"`
}

// KernelSource represents kernel source code with metadata
type KernelSource struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Language   KernelLanguage         `json:"language"`
	Type       KernelType             `json:"type"`
	Source     string                 `json:"source"`
	Headers    map[string]string      `json:"headers"`
	Macros     map[string]string      `json:"macros"`
	Parameters []KernelParameter      `json:"parameters"`
	Metadata   map[string]interface{} `json:"metadata"`
	CreatedAt  time.Time              `json:"created_at"`
	ModifiedAt time.Time              `json:"modified_at"`
}

// KernelParameter represents a kernel parameter definition
type KernelParameter struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Size        int    `json:"size"`
	IsPointer   bool   `json:"is_pointer"`
	IsConstant  bool   `json:"is_constant"`
	Description string `json:"description"`
}

// CompiledKernel represents a compiled kernel binary with metadata
type CompiledKernel struct {
	ID                string                 `json:"id"`
	SourceID          string                 `json:"source_id"`
	Target            CompilationTarget      `json:"target"`
	OptimizationLevel OptimizationLevel      `json:"optimization_level"`
	Binary            []byte                 `json:"binary"`
	PTX               string                 `json:"ptx,omitempty"`
	SPIRV             []byte                 `json:"spirv,omitempty"`
	CompilerVersion   string                 `json:"compiler_version"`
	CompilerFlags     []string               `json:"compiler_flags"`
	CompilationTime   time.Duration          `json:"compilation_time"`
	CompiledAt        time.Time              `json:"compiled_at"`
	Hash              string                 `json:"hash"`
	Size              int64                  `json:"size"`
	Metadata          map[string]interface{} `json:"metadata"`
	Performance       KernelPerformanceData  `json:"performance"`
}

// KernelPerformanceData represents performance characteristics of a kernel
type KernelPerformanceData struct {
	RegistersUsed       int               `json:"registers_used"`
	SharedMemoryUsed    int64             `json:"shared_memory_used"`
	LocalMemoryUsed     int64             `json:"local_memory_used"`
	MaxOccupancy        float64           `json:"max_occupancy"`
	OptimalBlockSize    int               `json:"optimal_block_size"`
	OptimalGridSize     int               `json:"optimal_grid_size"`
	EstimatedLatency    float64           `json:"estimated_latency_us"`
	EstimatedThroughput float64           `json:"estimated_throughput_gops"`
	BenchmarkResults    []BenchmarkResult `json:"benchmark_results"`
}

// CompilationOptions represents options for kernel compilation
type CompilationOptions struct {
	OptimizationLevel OptimizationLevel      `json:"optimization_level"`
	Target            CompilationTarget      `json:"target"`
	IncludePaths      []string               `json:"include_paths"`
	Defines           map[string]string      `json:"defines"`
	CompilerFlags     []string               `json:"compiler_flags"`
	DebugInfo         bool                   `json:"debug_info"`
	Verbose           bool                   `json:"verbose"`
	GeneratePTX       bool                   `json:"generate_ptx"`
	GenerateSPIRV     bool                   `json:"generate_spirv"`
	EnableProfiling   bool                   `json:"enable_profiling"`
	Timeout           time.Duration          `json:"timeout"`
	Metadata          map[string]interface{} `json:"metadata"`
}

// DefaultCompilationOptions returns default compilation options
func DefaultCompilationOptions() CompilationOptions {
	return CompilationOptions{
		OptimizationLevel: OptimizationMedium,
		IncludePaths:      []string{},
		Defines:           make(map[string]string),
		CompilerFlags:     []string{},
		DebugInfo:         false,
		Verbose:           false,
		GeneratePTX:       true,
		GenerateSPIRV:     false,
		EnableProfiling:   false,
		Timeout:           time.Minute * 5,
		Metadata:          make(map[string]interface{}),
	}
}

// CacheConfig represents configuration for kernel caching
type CacheConfig struct {
	Enabled          bool          `json:"enabled"`
	CacheDir         string        `json:"cache_dir"`
	MaxCacheSize     int64         `json:"max_cache_size_mb"`
	MaxAge           time.Duration `json:"max_age"`
	CompressionLevel int           `json:"compression_level"`
	EvictionPolicy   string        `json:"eviction_policy"`
	CleanupInterval  time.Duration `json:"cleanup_interval"`
	EnableMetrics    bool          `json:"enable_metrics"`
	EnableValidation bool          `json:"enable_validation"`
}

// DefaultCacheConfig returns default cache configuration
func DefaultCacheConfig() CacheConfig {
	return CacheConfig{
		Enabled:          true,
		CacheDir:         filepath.Join(os.TempDir(), "neuralmetergo", "kernels"),
		MaxCacheSize:     1024,               // 1GB
		MaxAge:           time.Hour * 24 * 7, // 1 week
		CompressionLevel: 6,
		EvictionPolicy:   "lru",
		CleanupInterval:  time.Hour,
		EnableMetrics:    true,
		EnableValidation: true,
	}
}

// KernelCache represents the kernel cache with metadata
type KernelCache struct {
	config   CacheConfig
	entries  map[string]*CacheEntry
	lruList  *LRUNode
	metrics  CacheMetrics
	mutex    sync.RWMutex
	stopChan chan struct{}
	wg       sync.WaitGroup
}

// CacheEntry represents a cached kernel entry
type CacheEntry struct {
	Key          string          `json:"key"`
	Kernel       *CompiledKernel `json:"kernel"`
	AccessCount  int64           `json:"access_count"`
	LastAccessed time.Time       `json:"last_accessed"`
	CreatedAt    time.Time       `json:"created_at"`
	Size         int64           `json:"size"`
	FilePath     string          `json:"file_path"`
	lruNode      *LRUNode
}

// LRUNode represents a node in the LRU linked list
type LRUNode struct {
	Key   string
	Entry *CacheEntry
	Prev  *LRUNode
	Next  *LRUNode
}

// CacheMetrics represents cache performance metrics
type CacheMetrics struct {
	Hits            int64     `json:"hits"`
	Misses          int64     `json:"misses"`
	Evictions       int64     `json:"evictions"`
	TotalSize       int64     `json:"total_size"`
	EntryCount      int64     `json:"entry_count"`
	LastCleanup     time.Time `json:"last_cleanup"`
	AverageLoadTime float64   `json:"average_load_time_ms"`
}

// KernelCompiler represents the main kernel compilation and caching system
type KernelCompiler struct {
	cache     *KernelCache
	compilers map[KernelLanguage]LanguageCompiler
	config    CompilerConfig
	metrics   CompilerMetrics
	mutex     sync.RWMutex
}

// CompilerConfig represents configuration for the kernel compiler
type CompilerConfig struct {
	CacheConfig        CacheConfig        `json:"cache_config"`
	DefaultOptions     CompilationOptions `json:"default_options"`
	CompilerPaths      map[string]string  `json:"compiler_paths"`
	MaxConcurrentJobs  int                `json:"max_concurrent_jobs"`
	EnableFallback     bool               `json:"enable_fallback"`
	FallbackLanguages  []KernelLanguage   `json:"fallback_languages"`
	AutoOptimization   bool               `json:"auto_optimization"`
	BenchmarkOnCompile bool               `json:"benchmark_on_compile"`
	Timeout            time.Duration      `json:"timeout"`
}

// DefaultCompilerConfig returns default compiler configuration
func DefaultCompilerConfig() CompilerConfig {
	return CompilerConfig{
		CacheConfig:        DefaultCacheConfig(),
		DefaultOptions:     DefaultCompilationOptions(),
		CompilerPaths:      make(map[string]string),
		MaxConcurrentJobs:  runtime.NumCPU(),
		EnableFallback:     true,
		FallbackLanguages:  []KernelLanguage{KernelOpenCL, KernelCUDA},
		AutoOptimization:   true,
		BenchmarkOnCompile: false,
		Timeout:            time.Minute * 10,
	}
}

// CompilerMetrics represents compiler performance metrics
type CompilerMetrics struct {
	TotalCompilations      int64         `json:"total_compilations"`
	SuccessfulCompilations int64         `json:"successful_compilations"`
	FailedCompilations     int64         `json:"failed_compilations"`
	CacheHits              int64         `json:"cache_hits"`
	CacheMisses            int64         `json:"cache_misses"`
	AverageCompileTime     time.Duration `json:"average_compile_time"`
	TotalCompileTime       time.Duration `json:"total_compile_time"`
	LastCompilation        time.Time     `json:"last_compilation"`
}

// LanguageCompiler interface for different kernel language compilers
type LanguageCompiler interface {
	Compile(source *KernelSource, options CompilationOptions) (*CompiledKernel, error)
	ValidateSource(source *KernelSource) error
	GetSupportedTargets() []CompilationTarget
	GetOptimalTarget(gpu *GPUInfo) (CompilationTarget, error)
	GetCompilerVersion() (string, error)
	IsAvailable() bool
}

// NewKernelCompiler creates a new kernel compiler instance
func NewKernelCompiler(config CompilerConfig) (*KernelCompiler, error) {
	cache, err := NewKernelCache(config.CacheConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create kernel cache: %w", err)
	}

	kc := &KernelCompiler{
		cache:     cache,
		compilers: make(map[KernelLanguage]LanguageCompiler),
		config:    config,
		metrics:   CompilerMetrics{},
	}

	if err := kc.initializeCompilers(); err != nil {
		return nil, fmt.Errorf("failed to initialize compilers: %w", err)
	}

	return kc, nil
}

// initializeCompilers initializes all available language compilers
func (kc *KernelCompiler) initializeCompilers() error {
	// Initialize CUDA compiler if available
	if cudaCompiler := NewCUDACompiler(kc.config.CompilerPaths); cudaCompiler.IsAvailable() {
		kc.compilers[KernelCUDA] = cudaCompiler
	}

	// Initialize OpenCL compiler if available
	if openclCompiler := NewOpenCLCompiler(kc.config.CompilerPaths); openclCompiler.IsAvailable() {
		kc.compilers[KernelOpenCL] = openclCompiler
	}

	// Initialize ROCm compiler if available
	if rocmCompiler := NewROCmCompiler(kc.config.CompilerPaths); rocmCompiler.IsAvailable() {
		kc.compilers[KernelROCm] = rocmCompiler
	}

	// Metal compiler removed - Linux only project

	if len(kc.compilers) == 0 {
		return fmt.Errorf("no kernel compilers available on this system")
	}

	return nil
}

// CompileKernel compiles a kernel with intelligent caching and optimization
func (kc *KernelCompiler) CompileKernel(source *KernelSource, options CompilationOptions) (*CompiledKernel, error) {
	startTime := time.Now()
	defer func() {
		kc.updateMetrics(time.Since(startTime))
	}()

	// Generate cache key
	cacheKey := kc.generateCacheKey(source, options)

	// Check cache first
	if kc.cache.config.Enabled {
		if cached, found := kc.cache.Get(cacheKey); found {
			kc.mutex.Lock()
			kc.metrics.CacheHits++
			kc.mutex.Unlock()
			return cached, nil
		}
		kc.mutex.Lock()
		kc.metrics.CacheMisses++
		kc.mutex.Unlock()
	}

	// Get appropriate compiler
	compiler, exists := kc.compilers[source.Language]
	if !exists {
		if kc.config.EnableFallback {
			return kc.compileWithFallback(source, options)
		}
		return nil, fmt.Errorf("compiler for language %s not available", source.Language)
	}

	// Validate source code
	if err := compiler.ValidateSource(source); err != nil {
		return nil, fmt.Errorf("source validation failed: %w", err)
	}

	// Auto-optimize options if enabled
	if kc.config.AutoOptimization {
		options = kc.optimizeCompilationOptions(options, source)
	}

	// Compile kernel
	kernel, err := compiler.Compile(source, options)
	if err != nil {
		kc.mutex.Lock()
		kc.metrics.FailedCompilations++
		kc.mutex.Unlock()

		if kc.config.EnableFallback {
			return kc.compileWithFallback(source, options)
		}
		return nil, fmt.Errorf("compilation failed: %w", err)
	}

	kc.mutex.Lock()
	kc.metrics.SuccessfulCompilations++
	kc.mutex.Unlock()

	// Cache the compiled kernel
	if kc.cache.config.Enabled {
		if err := kc.cache.Put(cacheKey, kernel); err != nil {
			fmt.Printf("Warning: failed to cache kernel: %v\n", err)
		}
	}

	return kernel, nil
}

// compileWithFallback attempts compilation with fallback languages
func (kc *KernelCompiler) compileWithFallback(source *KernelSource, options CompilationOptions) (*CompiledKernel, error) {
	var lastErr error

	for _, fallbackLang := range kc.config.FallbackLanguages {
		if fallbackLang == source.Language {
			continue
		}

		compiler, exists := kc.compilers[fallbackLang]
		if !exists {
			continue
		}

		convertedSource, err := kc.convertKernelSource(source, fallbackLang)
		if err != nil {
			lastErr = err
			continue
		}

		kernel, err := compiler.Compile(convertedSource, options)
		if err != nil {
			lastErr = err
			continue
		}

		return kernel, nil
	}

	return nil, fmt.Errorf("compilation failed with all fallback options, last error: %w", lastErr)
}

// convertKernelSource converts kernel source between languages
func (kc *KernelCompiler) convertKernelSource(source *KernelSource, targetLang KernelLanguage) (*KernelSource, error) {
	converted := &KernelSource{
		ID:         source.ID + "_converted_" + string(targetLang),
		Name:       source.Name + "_" + string(targetLang),
		Language:   targetLang,
		Type:       source.Type,
		Source:     source.Source,
		Headers:    make(map[string]string),
		Macros:     make(map[string]string),
		Parameters: source.Parameters,
		Metadata:   make(map[string]interface{}),
		CreatedAt:  time.Now(),
		ModifiedAt: time.Now(),
	}

	for k, v := range source.Metadata {
		converted.Metadata[k] = v
	}
	converted.Metadata["converted_from"] = string(source.Language)

	return converted, nil
}

// optimizeCompilationOptions automatically optimizes compilation options
func (kc *KernelCompiler) optimizeCompilationOptions(options CompilationOptions, source *KernelSource) CompilationOptions {
	optimized := options

	if options.OptimizationLevel == OptimizationAuto {
		switch source.Type {
		case KernelTypeCompute, KernelTypeMatmul:
			optimized.OptimizationLevel = OptimizationFull
		case KernelTypeMemory:
			optimized.OptimizationLevel = OptimizationMedium
		default:
			optimized.OptimizationLevel = OptimizationMedium
		}
	}

	switch source.Type {
	case KernelTypeMatmul:
		optimized.CompilerFlags = append(optimized.CompilerFlags, "--use-fast-math")
	case KernelTypeReduction:
		optimized.CompilerFlags = append(optimized.CompilerFlags, "--maxrregcount=32")
	}

	return optimized
}

// generateCacheKey generates a unique cache key for kernel and options
func (kc *KernelCompiler) generateCacheKey(source *KernelSource, options CompilationOptions) string {
	hasher := sha256.New()

	hasher.Write([]byte(source.Source))
	hasher.Write([]byte(source.Language))
	hasher.Write([]byte(source.Type))

	optionsJSON, _ := json.Marshal(options)
	hasher.Write(optionsJSON)

	for k, v := range source.Headers {
		hasher.Write([]byte(k + ":" + v))
	}
	for k, v := range source.Macros {
		hasher.Write([]byte(k + ":" + v))
	}

	return hex.EncodeToString(hasher.Sum(nil))
}

// updateMetrics updates compiler metrics
func (kc *KernelCompiler) updateMetrics(duration time.Duration) {
	kc.mutex.Lock()
	defer kc.mutex.Unlock()

	kc.metrics.TotalCompilations++
	kc.metrics.TotalCompileTime += duration
	kc.metrics.AverageCompileTime = kc.metrics.TotalCompileTime / time.Duration(kc.metrics.TotalCompilations)
	kc.metrics.LastCompilation = time.Now()
}

// GetSupportedLanguages returns list of supported kernel languages
func (kc *KernelCompiler) GetSupportedLanguages() []KernelLanguage {
	kc.mutex.RLock()
	defer kc.mutex.RUnlock()

	languages := make([]KernelLanguage, 0, len(kc.compilers))
	for lang := range kc.compilers {
		languages = append(languages, lang)
	}
	return languages
}

// GetCompilerInfo returns information about available compilers
func (kc *KernelCompiler) GetCompilerInfo() map[KernelLanguage]map[string]interface{} {
	kc.mutex.RLock()
	defer kc.mutex.RUnlock()

	info := make(map[KernelLanguage]map[string]interface{})
	for lang, compiler := range kc.compilers {
		compilerInfo := make(map[string]interface{})
		compilerInfo["available"] = compiler.IsAvailable()

		if version, err := compiler.GetCompilerVersion(); err == nil {
			compilerInfo["version"] = version
		}

		compilerInfo["supported_targets"] = compiler.GetSupportedTargets()
		info[lang] = compilerInfo
	}

	return info
}

// GetMetrics returns current compiler metrics
func (kc *KernelCompiler) GetMetrics() CompilerMetrics {
	kc.mutex.RLock()
	defer kc.mutex.RUnlock()
	return kc.metrics
}

// GetCacheMetrics returns current cache metrics
func (kc *KernelCompiler) GetCacheMetrics() CacheMetrics {
	return kc.cache.GetMetrics()
}

// ClearCache clears the kernel cache
func (kc *KernelCompiler) ClearCache() error {
	return kc.cache.Clear()
}

// Cleanup performs cleanup operations
func (kc *KernelCompiler) Cleanup() error {
	if kc.cache != nil {
		return kc.cache.Cleanup()
	}
	return nil
}
