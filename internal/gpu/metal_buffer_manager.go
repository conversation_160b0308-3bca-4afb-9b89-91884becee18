package gpu

import (
	"fmt"
	"log"
	"sync"
	"sync/atomic"
	"time"
)

// BufferPriority defines the priority levels for Metal command buffers
type BufferPriority int

const (
	BufferPriorityLow    BufferPriority = 0
	BufferPriorityNormal BufferPriority = 1
	BufferPriorityHigh   BufferPriority = 2
)

// BufferOptions defines Metal command buffer creation options
const (
	BufferOptionNone                 = 0x00
	BufferOptionUnretainedReferences = 0x01
	BufferOptionErrorLogLevel        = 0x02
)

// BufferState represents the current state of a command buffer
type BufferState int

const (
	BufferStateIdle BufferState = iota
	BufferStateBusy
	BufferStateCommitted
	BufferStateCompleted
	BufferStateError
)

// MetalCommandBuffer interface represents the native Metal command buffer
type MetalCommandBuffer interface {
	Create(device uintptr, queue uintptr, options int) error
	Commit() error
	WaitUntilCompleted() error
	WaitUntilScheduled() error
	GetStatus() int
	GetHandle() uintptr
	Release() error
}

// ManagedMetalBuffer extends MetalCommandBuffer with management features
type ManagedMetalBuffer struct {
	MetalCommandBuffer
	id          string
	deviceID    int
	queueID     string
	priority    BufferPriority
	options     int
	state       BufferState
	inUse       bool
	lastUsed    time.Time
	createdAt   time.Time
	completedAt *time.Time

	// Usage statistics
	usageCount    int64
	totalTime     time.Duration
	errorCount    int64
	commitCount   int64
	completeCount int64

	// Thread safety
	mu sync.RWMutex
}

// NewManagedMetalBuffer creates a new managed Metal command buffer
func NewManagedMetalBuffer(id string, deviceID int, queueID string, priority BufferPriority, options int, logger *log.Logger) (*ManagedMetalBuffer, error) {
	buffer := &MockMetalCommandBuffer{} // In real implementation, use actual Metal buffer
	if err := buffer.Create(uintptr(deviceID), 0, options); err != nil {
		return nil, fmt.Errorf("failed to create Metal command buffer: %w", err)
	}

	managed := &ManagedMetalBuffer{
		MetalCommandBuffer: buffer,
		id:                 id,
		deviceID:           deviceID,
		queueID:            queueID,
		priority:           priority,
		options:            options,
		state:              BufferStateIdle,
		lastUsed:           time.Now(),
		createdAt:          time.Now(),
	}

	return managed, nil
}

// Management methods
func (b *ManagedMetalBuffer) GetID() string {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return b.id
}

func (b *ManagedMetalBuffer) GetDeviceID() int {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return b.deviceID
}

func (b *ManagedMetalBuffer) GetQueueID() string {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return b.queueID
}

func (b *ManagedMetalBuffer) GetPriority() BufferPriority {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return b.priority
}

func (b *ManagedMetalBuffer) GetOptions() int {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return b.options
}

func (b *ManagedMetalBuffer) GetState() BufferState {
	b.mu.RLock()
	defer b.mu.RUnlock()

	if b.errorCount > 0 {
		return BufferStateError
	}
	if b.completedAt != nil {
		return BufferStateCompleted
	}
	if b.commitCount > 0 {
		return BufferStateCommitted
	}
	if b.inUse {
		return BufferStateBusy
	}
	return BufferStateIdle
}

func (b *ManagedMetalBuffer) IsInUse() bool {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return b.inUse
}

func (b *ManagedMetalBuffer) SetInUse(inUse bool) {
	b.mu.Lock()
	defer b.mu.Unlock()

	if !b.inUse && inUse {
		atomic.AddInt64(&b.usageCount, 1)
	}
	b.inUse = inUse
	b.lastUsed = time.Now()
}

func (b *ManagedMetalBuffer) CommitBuffer() error {
	b.mu.Lock()
	defer b.mu.Unlock()

	err := b.MetalCommandBuffer.Commit()
	if err == nil {
		atomic.AddInt64(&b.commitCount, 1)
	} else {
		atomic.AddInt64(&b.errorCount, 1)
	}
	return err
}

func (b *ManagedMetalBuffer) WaitForCompletion() error {
	err := b.MetalCommandBuffer.WaitUntilCompleted()
	if err == nil {
		b.mu.Lock()
		now := time.Now()
		b.completedAt = &now
		atomic.AddInt64(&b.completeCount, 1)
		b.mu.Unlock()
	} else {
		atomic.AddInt64(&b.errorCount, 1)
	}
	return err
}

func (b *ManagedMetalBuffer) GetUsageStats() (int64, time.Duration, int64, int64, int64) {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return b.usageCount, b.totalTime, b.errorCount, b.commitCount, b.completeCount
}

func (b *ManagedMetalBuffer) UpdateTotalTime(duration time.Duration) {
	b.mu.Lock()
	defer b.mu.Unlock()
	b.totalTime += duration
}

func (b *ManagedMetalBuffer) IncrementErrorCount() {
	atomic.AddInt64(&b.errorCount, 1)
}

func (b *ManagedMetalBuffer) GetLastUsed() time.Time {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return b.lastUsed
}

func (b *ManagedMetalBuffer) GetCompletedAt() *time.Time {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return b.completedAt
}

func (b *ManagedMetalBuffer) Destroy() error {
	b.mu.Lock()
	defer b.mu.Unlock()

	if b.MetalCommandBuffer != nil {
		return b.MetalCommandBuffer.Release()
	}
	return nil
}

// BufferPool manages a pool of Metal command buffers for a single device/queue
type BufferPool struct {
	deviceID   int
	queueID    string
	minBuffers int
	maxBuffers int
	logger     *log.Logger

	// Available buffers organized by priority
	availableBuffers map[BufferPriority][]*ManagedMetalBuffer
	allBuffers       []*ManagedMetalBuffer

	// Thread safety
	mu sync.Mutex
}

// NewBufferPool creates a new buffer pool for a device/queue
func NewBufferPool(deviceID int, queueID string, minBuffers, maxBuffers int, logger *log.Logger) *BufferPool {
	pool := &BufferPool{
		deviceID:         deviceID,
		queueID:          queueID,
		minBuffers:       minBuffers,
		maxBuffers:       maxBuffers,
		logger:           logger,
		availableBuffers: make(map[BufferPriority][]*ManagedMetalBuffer),
		allBuffers:       make([]*ManagedMetalBuffer, 0),
	}

	// Initialize priority queues
	pool.availableBuffers[BufferPriorityLow] = make([]*ManagedMetalBuffer, 0)
	pool.availableBuffers[BufferPriorityNormal] = make([]*ManagedMetalBuffer, 0)
	pool.availableBuffers[BufferPriorityHigh] = make([]*ManagedMetalBuffer, 0)

	// Create initial buffers
	for i := 0; i < minBuffers; i++ {
		buffer, err := pool.createBuffer(BufferPriorityNormal)
		if err != nil {
			logger.Printf("Warning: Failed to create initial Metal buffer %d: %v", i, err)
			continue
		}
		pool.allBuffers = append(pool.allBuffers, buffer)
		pool.availableBuffers[BufferPriorityNormal] = append(pool.availableBuffers[BufferPriorityNormal], buffer)
	}

	logger.Printf("Created Metal buffer pool for device %d with %d initial buffers", deviceID, len(pool.allBuffers))
	return pool
}

// AcquireBuffer acquires a buffer from the pool
func (p *BufferPool) AcquireBuffer(priority BufferPriority) (*ManagedMetalBuffer, error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	// Try to get buffer from requested priority
	if len(p.availableBuffers[priority]) > 0 {
		buffer := p.availableBuffers[priority][0]
		p.availableBuffers[priority] = p.availableBuffers[priority][1:]
		buffer.SetInUse(true)
		p.logger.Printf("Acquired buffer %s (priority %d) for device %d", buffer.GetID(), priority, p.deviceID)
		return buffer, nil
	}

	// Try lower priorities if high priority requested
	if priority == BufferPriorityHigh {
		if len(p.availableBuffers[BufferPriorityNormal]) > 0 {
			buffer := p.availableBuffers[BufferPriorityNormal][0]
			p.availableBuffers[BufferPriorityNormal] = p.availableBuffers[BufferPriorityNormal][1:]
			buffer.SetInUse(true)
			p.logger.Printf("Acquired buffer %s (priority %d, requested %d) for device %d", buffer.GetID(), buffer.GetPriority(), priority, p.deviceID)
			return buffer, nil
		}
		if len(p.availableBuffers[BufferPriorityLow]) > 0 {
			buffer := p.availableBuffers[BufferPriorityLow][0]
			p.availableBuffers[BufferPriorityLow] = p.availableBuffers[BufferPriorityLow][1:]
			buffer.SetInUse(true)
			p.logger.Printf("Acquired buffer %s (priority %d, requested %d) for device %d", buffer.GetID(), buffer.GetPriority(), priority, p.deviceID)
			return buffer, nil
		}
	}

	// Try normal priority if high wasn't available
	if priority == BufferPriorityNormal {
		if len(p.availableBuffers[BufferPriorityLow]) > 0 {
			buffer := p.availableBuffers[BufferPriorityLow][0]
			p.availableBuffers[BufferPriorityLow] = p.availableBuffers[BufferPriorityLow][1:]
			buffer.SetInUse(true)
			p.logger.Printf("Acquired buffer %s (priority %d, requested %d) for device %d", buffer.GetID(), buffer.GetPriority(), priority, p.deviceID)
			return buffer, nil
		}
	}

	// No available buffers, try to create new one if under capacity
	if len(p.allBuffers) < p.maxBuffers {
		buffer, err := p.createBuffer(priority)
		if err != nil {
			return nil, fmt.Errorf("failed to create new buffer: %w", err)
		}
		p.allBuffers = append(p.allBuffers, buffer)
		buffer.SetInUse(true)
		p.logger.Printf("Created and acquired new buffer %s (priority %d) for device %d", buffer.GetID(), priority, p.deviceID)
		return buffer, nil
	}

	// Pool at capacity, try LRU eviction
	lruBuffer := p.findLRUBuffer()
	if lruBuffer != nil {
		lruBuffer.SetInUse(true)
		p.logger.Printf("Acquired LRU buffer %s (priority %d, requested %d) for device %d", lruBuffer.GetID(), lruBuffer.GetPriority(), priority, p.deviceID)
		return lruBuffer, nil
	}

	return nil, fmt.Errorf("no buffers available and pool at capacity")
}

// ReleaseBuffer releases a buffer back to the pool
func (p *BufferPool) ReleaseBuffer(buffer *ManagedMetalBuffer) error {
	if buffer == nil {
		return fmt.Errorf("cannot release nil buffer")
	}

	p.mu.Lock()
	defer p.mu.Unlock()

	if buffer.GetDeviceID() != p.deviceID {
		return fmt.Errorf("buffer belongs to different device")
	}

	buffer.SetInUse(false)
	priority := buffer.GetPriority()
	p.availableBuffers[priority] = append(p.availableBuffers[priority], buffer)

	p.logger.Printf("Released buffer %s (priority %d) for device %d", buffer.GetID(), priority, p.deviceID)
	return nil
}

// GetPoolStats returns statistics about the buffer pool
func (p *BufferPool) GetPoolStats() map[string]interface{} {
	p.mu.Lock()
	defer p.mu.Unlock()

	totalAvailable := len(p.availableBuffers[BufferPriorityLow]) +
		len(p.availableBuffers[BufferPriorityNormal]) +
		len(p.availableBuffers[BufferPriorityHigh])

	return map[string]interface{}{
		"device_id":       p.deviceID,
		"queue_id":        p.queueID,
		"min_buffers":     p.minBuffers,
		"max_buffers":     p.maxBuffers,
		"total":           len(p.allBuffers),
		"available":       totalAvailable,
		"in_use":          len(p.allBuffers) - totalAvailable,
		"low_priority":    len(p.availableBuffers[BufferPriorityLow]),
		"normal_priority": len(p.availableBuffers[BufferPriorityNormal]),
		"high_priority":   len(p.availableBuffers[BufferPriorityHigh]),
	}
}

// Cleanup destroys all buffers in the pool
func (p *BufferPool) Cleanup() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	var errors []error
	for _, buffer := range p.allBuffers {
		if err := buffer.Destroy(); err != nil {
			errors = append(errors, err)
		}
	}

	// Clear all references
	p.allBuffers = nil
	for priority := range p.availableBuffers {
		p.availableBuffers[priority] = nil
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors during cleanup: %v", errors)
	}

	p.logger.Printf("Cleaned up Metal buffer pool for device %d", p.deviceID)
	return nil
}

// createBuffer creates a new managed Metal buffer
func (p *BufferPool) createBuffer(priority BufferPriority) (*ManagedMetalBuffer, error) {
	id := fmt.Sprintf("metal-buffer-dev%d-queue%s-%d", p.deviceID, p.queueID, time.Now().UnixNano())
	return NewManagedMetalBuffer(id, p.deviceID, p.queueID, priority, BufferOptionNone, p.logger)
}

// findLRUBuffer finds the least recently used available buffer
func (p *BufferPool) findLRUBuffer() *ManagedMetalBuffer {
	var lruBuffer *ManagedMetalBuffer
	var oldestTime time.Time

	for _, priorityBuffers := range p.availableBuffers {
		for _, buffer := range priorityBuffers {
			if !buffer.IsInUse() {
				if lruBuffer == nil || buffer.GetLastUsed().Before(oldestTime) {
					lruBuffer = buffer
					oldestTime = buffer.GetLastUsed()
				}
			}
		}
	}

	if lruBuffer != nil {
		// Remove from available list
		priority := lruBuffer.GetPriority()
		for i, buffer := range p.availableBuffers[priority] {
			if buffer == lruBuffer {
				p.availableBuffers[priority] = append(p.availableBuffers[priority][:i], p.availableBuffers[priority][i+1:]...)
				break
			}
		}
	}

	return lruBuffer
}

// MetalBufferManager coordinates multiple device/queue buffer pools
type MetalBufferManager struct {
	pools         map[string]*BufferPool // key: deviceID-queueID
	defaultDevice int
	started       bool
	logger        *log.Logger

	// Performance metrics
	totalAcquires  int64
	totalReleases  int64
	totalCreations int64
	totalErrors    int64

	// Thread safety
	mu sync.RWMutex
}

// NewMetalBufferManager creates a new Metal buffer manager
func NewMetalBufferManager(logger *log.Logger) *MetalBufferManager {
	return &MetalBufferManager{
		pools:  make(map[string]*BufferPool),
		logger: logger,
	}
}

// Initialize initializes the buffer manager for the specified devices and queues
func (bm *MetalBufferManager) Initialize(devices []int, queues map[int]string, minBuffers, maxBuffers int) error {
	bm.mu.Lock()
	defer bm.mu.Unlock()

	if bm.started {
		return fmt.Errorf("buffer manager already initialized")
	}

	if len(devices) == 0 {
		return fmt.Errorf("no devices specified")
	}

	// Create pools for each device-queue combination
	for _, deviceID := range devices {
		queueID, exists := queues[deviceID]
		if !exists {
			queueID = fmt.Sprintf("default-queue-%d", deviceID)
		}

		poolKey := fmt.Sprintf("%d-%s", deviceID, queueID)
		pool := NewBufferPool(deviceID, queueID, minBuffers, maxBuffers, bm.logger)
		bm.pools[poolKey] = pool
	}

	bm.defaultDevice = devices[0]
	bm.started = true

	bm.logger.Printf("Metal buffer manager initialized with %d device pools", len(bm.pools))
	return nil
}

// AcquireBuffer acquires a command buffer for the specified device and queue
func (bm *MetalBufferManager) AcquireBuffer(deviceID int, queueID string, priority BufferPriority) (*ManagedMetalBuffer, error) {
	bm.mu.RLock()
	defer bm.mu.RUnlock()

	if !bm.started {
		return nil, fmt.Errorf("buffer manager not initialized")
	}

	poolKey := fmt.Sprintf("%d-%s", deviceID, queueID)
	pool, exists := bm.pools[poolKey]
	if !exists {
		return nil, fmt.Errorf("no pool for device %d queue %s", deviceID, queueID)
	}

	buffer, err := pool.AcquireBuffer(priority)
	if err != nil {
		atomic.AddInt64(&bm.totalErrors, 1)
		return nil, err
	}

	atomic.AddInt64(&bm.totalAcquires, 1)
	return buffer, nil
}

// AcquireDefaultBuffer acquires a command buffer for the default device
func (bm *MetalBufferManager) AcquireDefaultBuffer(priority BufferPriority) (*ManagedMetalBuffer, error) {
	bm.mu.RLock()
	defer bm.mu.RUnlock()

	// Find the default device's queue
	var queueID string
	for _, pool := range bm.pools {
		if pool.deviceID == bm.defaultDevice {
			queueID = pool.queueID
			break
		}
	}

	if queueID == "" {
		return nil, fmt.Errorf("no queue found for default device %d", bm.defaultDevice)
	}

	return bm.AcquireBuffer(bm.defaultDevice, queueID, priority)
}

// ReleaseBuffer releases a command buffer back to its pool
func (bm *MetalBufferManager) ReleaseBuffer(buffer *ManagedMetalBuffer) error {
	if buffer == nil {
		return fmt.Errorf("cannot release nil buffer")
	}

	bm.mu.RLock()
	defer bm.mu.RUnlock()

	if !bm.started {
		return fmt.Errorf("buffer manager not initialized")
	}

	poolKey := fmt.Sprintf("%d-%s", buffer.GetDeviceID(), buffer.GetQueueID())
	pool, exists := bm.pools[poolKey]
	if !exists {
		return fmt.Errorf("no pool for device %d queue %s", buffer.GetDeviceID(), buffer.GetQueueID())
	}

	err := pool.ReleaseBuffer(buffer)
	if err == nil {
		atomic.AddInt64(&bm.totalReleases, 1)
	} else {
		atomic.AddInt64(&bm.totalErrors, 1)
	}

	return err
}

// GetManagerStats returns statistics about the buffer manager
func (bm *MetalBufferManager) GetManagerStats() map[string]interface{} {
	bm.mu.RLock()
	defer bm.mu.RUnlock()

	pools := make(map[string]interface{})
	for _, pool := range bm.pools {
		pools[fmt.Sprintf("device_%d_queue_%s", pool.deviceID, pool.queueID)] = pool.GetPoolStats()
	}

	return map[string]interface{}{
		"started":         bm.started,
		"total_pools":     len(bm.pools),
		"default_device":  bm.defaultDevice,
		"total_acquires":  bm.totalAcquires,
		"total_releases":  bm.totalReleases,
		"total_creations": bm.totalCreations,
		"total_errors":    bm.totalErrors,
		"pools":           pools,
	}
}

// Cleanup shuts down the buffer manager and all pools
func (bm *MetalBufferManager) Cleanup() error {
	bm.mu.Lock()
	defer bm.mu.Unlock()

	if !bm.started {
		return nil
	}

	var errors []error
	for poolKey, pool := range bm.pools {
		if err := pool.Cleanup(); err != nil {
			errors = append(errors, fmt.Errorf("pool %s: %w", poolKey, err))
		}
	}

	bm.pools = make(map[string]*BufferPool)
	bm.started = false

	if len(errors) > 0 {
		return fmt.Errorf("errors during cleanup: %v", errors)
	}

	bm.logger.Printf("Metal buffer manager cleaned up")
	return nil
}

// MockMetalCommandBuffer implements MetalCommandBuffer for testing
type MockMetalCommandBuffer struct {
	handle    uintptr
	created   bool
	committed bool
	completed bool
	released  bool
	status    int
}

func (m *MockMetalCommandBuffer) Create(device uintptr, queue uintptr, options int) error {
	m.created = true
	m.handle = uintptr(time.Now().UnixNano()) // Use timestamp as mock handle
	m.status = 0                              // MTLCommandBufferStatusNotEnqueued
	return nil
}

func (m *MockMetalCommandBuffer) Commit() error {
	if !m.created || m.released {
		return fmt.Errorf("buffer not available")
	}
	m.committed = true
	m.status = 1 // MTLCommandBufferStatusEnqueued
	return nil
}

func (m *MockMetalCommandBuffer) WaitUntilCompleted() error {
	if !m.created || m.released {
		return fmt.Errorf("buffer not available")
	}
	if !m.committed {
		return fmt.Errorf("buffer not committed")
	}
	m.completed = true
	m.status = 4 // MTLCommandBufferStatusCompleted
	return nil
}

func (m *MockMetalCommandBuffer) WaitUntilScheduled() error {
	if !m.created || m.released {
		return fmt.Errorf("buffer not available")
	}
	if !m.committed {
		return fmt.Errorf("buffer not committed")
	}
	m.status = 2 // MTLCommandBufferStatusCommitted
	return nil
}

func (m *MockMetalCommandBuffer) GetStatus() int {
	return m.status
}

func (m *MockMetalCommandBuffer) GetHandle() uintptr {
	return m.handle
}

func (m *MockMetalCommandBuffer) Release() error {
	m.released = true
	m.created = false
	return nil
}
