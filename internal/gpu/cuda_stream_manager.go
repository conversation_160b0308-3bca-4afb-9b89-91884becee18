//go:build cuda
// +build cuda

package gpu

import (
	"fmt"
	"log"
	"sort"
	"sync"
	"sync/atomic"
	"time"
)

// StreamPriority defines the priority levels for CUDA streams
type StreamPriority int

const (
	StreamPriorityLow    StreamPriority = 0
	StreamPriorityNormal StreamPriority = 1
	StreamPriorityHigh   StreamPriority = 2
)

// StreamFlags defines CUDA stream creation flags
const (
	StreamFlagDefault     = 0x00
	StreamFlagNonBlocking = 0x01
)

// StreamState represents the current state of a stream
type CUDAStreamState int

const (
	CUDAStreamStateIdle CUDAStreamState = iota
	CUDAStreamStateBusy
	CUDAStreamStateError
)

// ManagedCUDAStream extends CUDAStream with management metadata
type ManagedCUDAStream struct {
	CUDAStream
	id         string
	deviceID   int
	priority   StreamPriority
	state      CUDAStreamState
	lastUsed   time.Time
	usageCount int64
	inUse      bool
	createdAt  time.Time
	totalTime  time.Duration
	errorCount int64
	mutex      sync.RWMutex
}

// NewManagedCUDAStream creates a new managed CUDA stream
func NewManagedCUDAStream(id string, deviceID int, priority StreamPriority, flags int, logger *log.Logger) (*ManagedCUDAStream, error) {
	stream := &CUDAStreamImpl{
		logger: logger,
	}

	if err := stream.Create(flags); err != nil {
		return nil, fmt.Errorf("failed to create CUDA stream: %v", err)
	}

	managed := &ManagedCUDAStream{
		CUDAStream: stream,
		id:         id,
		deviceID:   deviceID,
		priority:   priority,
		state:      CUDAStreamStateIdle,
		lastUsed:   time.Now(),
		createdAt:  time.Now(),
	}

	return managed, nil
}

// GetID returns the stream identifier
func (s *ManagedCUDAStream) GetID() string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.id
}

// GetDeviceID returns the device ID
func (s *ManagedCUDAStream) GetDeviceID() int {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.deviceID
}

// GetPriority returns the stream priority
func (s *ManagedCUDAStream) GetPriority() StreamPriority {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.priority
}

// GetState returns the current stream state
func (s *ManagedCUDAStream) GetState() CUDAStreamState {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.state
}

// SetInUse marks the stream as in use
func (s *ManagedCUDAStream) SetInUse(inUse bool) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.inUse = inUse
	if inUse {
		s.state = CUDAStreamStateBusy
		s.lastUsed = time.Now()
		atomic.AddInt64(&s.usageCount, 1)
	} else {
		s.state = CUDAStreamStateIdle
	}
}

// IsInUse returns whether the stream is currently in use
func (s *ManagedCUDAStream) IsInUse() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.inUse
}

// GetUsageStats returns usage statistics
func (s *ManagedCUDAStream) GetUsageStats() (int64, time.Duration, int64) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.usageCount, s.totalTime, s.errorCount
}

// UpdateTotalTime adds to the total execution time
func (s *ManagedCUDAStream) UpdateTotalTime(duration time.Duration) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.totalTime += duration
}

// IncrementErrorCount increments the error counter
func (s *ManagedCUDAStream) IncrementErrorCount() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.errorCount++
	s.state = CUDAStreamStateError
}

// StreamPool manages a pool of CUDA streams for a specific device
type StreamPool struct {
	deviceID       int
	streams        map[string]*ManagedCUDAStream
	availableQueue []*ManagedCUDAStream
	maxStreams     int
	minStreams     int
	defaultFlags   int
	logger         *log.Logger
	mutex          sync.RWMutex
	streamCounter  int64
}

// NewStreamPool creates a new stream pool for a device
func NewStreamPool(deviceID int, minStreams, maxStreams int, logger *log.Logger) *StreamPool {
	if logger == nil {
		logger = log.Default()
	}

	pool := &StreamPool{
		deviceID:     deviceID,
		streams:      make(map[string]*ManagedCUDAStream),
		maxStreams:   maxStreams,
		minStreams:   minStreams,
		defaultFlags: StreamFlagNonBlocking,
		logger:       logger,
	}

	// Pre-create minimum streams
	for i := 0; i < minStreams; i++ {
		stream, err := pool.createNewStream(StreamPriorityNormal)
		if err != nil {
			logger.Printf("Failed to create initial stream %d: %v", i, err)
			continue
		}
		pool.availableQueue = append(pool.availableQueue, stream)
	}

	return pool
}

// createNewStream creates a new managed stream
func (p *StreamPool) createNewStream(priority StreamPriority) (*ManagedCUDAStream, error) {
	counter := atomic.AddInt64(&p.streamCounter, 1)
	id := fmt.Sprintf("stream_%d_%d", p.deviceID, counter)

	stream, err := NewManagedCUDAStream(id, p.deviceID, priority, p.defaultFlags, p.logger)
	if err != nil {
		return nil, err
	}

	p.streams[id] = stream
	p.logger.Printf("Created new CUDA stream %s for device %d", id, p.deviceID)
	return stream, nil
}

// AcquireStream acquires a stream from the pool
func (p *StreamPool) AcquireStream(priority StreamPriority) (*ManagedCUDAStream, error) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// Try to find an available stream with matching or higher priority
	for i, stream := range p.availableQueue {
		if stream.GetPriority() >= priority && !stream.IsInUse() {
			// Remove from available queue
			p.availableQueue = append(p.availableQueue[:i], p.availableQueue[i+1:]...)
			stream.SetInUse(true)
			return stream, nil
		}
	}

	// No available stream found, try to create a new one
	if len(p.streams) < p.maxStreams {
		stream, err := p.createNewStream(priority)
		if err != nil {
			return nil, err
		}
		stream.SetInUse(true)
		return stream, nil
	}

	// Pool is at capacity, wait for the least recently used stream
	return p.waitForAvailableStream(priority)
}

// waitForAvailableStream waits for an available stream
func (p *StreamPool) waitForAvailableStream(priority StreamPriority) (*ManagedCUDAStream, error) {
	// Sort streams by last used time (LRU)
	var candidates []*ManagedCUDAStream
	for _, stream := range p.streams {
		if !stream.IsInUse() {
			candidates = append(candidates, stream)
		}
	}

	if len(candidates) == 0 {
		return nil, fmt.Errorf("no available streams in pool")
	}

	sort.Slice(candidates, func(i, j int) bool {
		return candidates[i].lastUsed.Before(candidates[j].lastUsed)
	})

	// Return the least recently used stream
	stream := candidates[0]
	stream.SetInUse(true)
	return stream, nil
}

// ReleaseStream returns a stream to the pool
func (p *StreamPool) ReleaseStream(stream *ManagedCUDAStream) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if stream == nil {
		return fmt.Errorf("cannot release nil stream")
	}

	// Synchronize the stream before releasing
	if err := stream.Synchronize(); err != nil {
		stream.IncrementErrorCount()
		p.logger.Printf("Error synchronizing stream %s: %v", stream.GetID(), err)
	}

	stream.SetInUse(false)

	// Add back to available queue if under minimum or prioritized
	if len(p.availableQueue) < p.minStreams || stream.GetPriority() == StreamPriorityHigh {
		p.availableQueue = append(p.availableQueue, stream)
	}

	return nil
}

// GetPoolStats returns pool statistics
func (p *StreamPool) GetPoolStats() map[string]interface{} {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	stats := map[string]interface{}{
		"device_id":       p.deviceID,
		"total_streams":   len(p.streams),
		"available":       len(p.availableQueue),
		"max_streams":     p.maxStreams,
		"min_streams":     p.minStreams,
		"streams_created": p.streamCounter,
	}

	// Count streams by state
	stateCount := map[CUDAStreamState]int{}
	priorityCount := map[StreamPriority]int{}

	for _, stream := range p.streams {
		stateCount[stream.GetState()]++
		priorityCount[stream.GetPriority()]++
	}

	stats["states"] = stateCount
	stats["priorities"] = priorityCount

	return stats
}

// Cleanup destroys all streams in the pool
func (p *StreamPool) Cleanup() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	var errors []error
	for id, stream := range p.streams {
		if err := stream.Destroy(); err != nil {
			errors = append(errors, fmt.Errorf("failed to destroy stream %s: %v", id, err))
		}
	}

	p.streams = make(map[string]*ManagedCUDAStream)
	p.availableQueue = nil

	if len(errors) > 0 {
		return fmt.Errorf("cleanup errors: %v", errors)
	}

	p.logger.Printf("Cleaned up stream pool for device %d", p.deviceID)
	return nil
}

// CUDAStreamManager manages streams across multiple devices
type CUDAStreamManager struct {
	pools         map[int]*StreamPool
	defaultDevice int
	logger        *log.Logger
	mutex         sync.RWMutex
	started       bool
}

// NewCUDAStreamManager creates a new CUDA stream manager
func NewCUDAStreamManager(logger *log.Logger) *CUDAStreamManager {
	if logger == nil {
		logger = log.Default()
	}

	return &CUDAStreamManager{
		pools:  make(map[int]*StreamPool),
		logger: logger,
	}
}

// Initialize sets up stream pools for available devices
func (m *CUDAStreamManager) Initialize(devices []int, minStreams, maxStreams int) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.started {
		return fmt.Errorf("stream manager already initialized")
	}

	for _, deviceID := range devices {
		pool := NewStreamPool(deviceID, minStreams, maxStreams, m.logger)
		m.pools[deviceID] = pool
		m.logger.Printf("Initialized stream pool for device %d (min: %d, max: %d)", deviceID, minStreams, maxStreams)
	}

	if len(devices) > 0 {
		m.defaultDevice = devices[0]
	}

	m.started = true
	return nil
}

// AcquireStream acquires a stream for the specified device
func (m *CUDAStreamManager) AcquireStream(deviceID int, priority StreamPriority) (*ManagedCUDAStream, error) {
	m.mutex.RLock()
	pool, exists := m.pools[deviceID]
	m.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("no stream pool for device %d", deviceID)
	}

	return pool.AcquireStream(priority)
}

// AcquireDefaultStream acquires a stream for the default device
func (m *CUDAStreamManager) AcquireDefaultStream(priority StreamPriority) (*ManagedCUDAStream, error) {
	return m.AcquireStream(m.defaultDevice, priority)
}

// ReleaseStream releases a stream back to its pool
func (m *CUDAStreamManager) ReleaseStream(stream *ManagedCUDAStream) error {
	if stream == nil {
		return fmt.Errorf("cannot release nil stream")
	}

	deviceID := stream.GetDeviceID()
	m.mutex.RLock()
	pool, exists := m.pools[deviceID]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("no stream pool for device %d", deviceID)
	}

	return pool.ReleaseStream(stream)
}

// GetManagerStats returns statistics for all pools
func (m *CUDAStreamManager) GetManagerStats() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	stats := map[string]interface{}{
		"default_device": m.defaultDevice,
		"total_pools":    len(m.pools),
		"started":        m.started,
	}

	pools := make(map[string]interface{})
	for deviceID, pool := range m.pools {
		pools[fmt.Sprintf("device_%d", deviceID)] = pool.GetPoolStats()
	}
	stats["pools"] = pools

	return stats
}

// Cleanup destroys all stream pools
func (m *CUDAStreamManager) Cleanup() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	var errors []error
	for deviceID, pool := range m.pools {
		if err := pool.Cleanup(); err != nil {
			errors = append(errors, fmt.Errorf("device %d: %v", deviceID, err))
		}
	}

	m.pools = make(map[int]*StreamPool)
	m.started = false

	if len(errors) > 0 {
		return fmt.Errorf("cleanup errors: %v", errors)
	}

	m.logger.Printf("Cleaned up CUDA stream manager")
	return nil
}
