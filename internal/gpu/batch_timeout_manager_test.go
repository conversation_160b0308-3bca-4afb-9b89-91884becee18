package gpu

import (
	"sync"
	"testing"
	"time"
)

func TestDefaultTimeoutConfig(t *testing.T) {
	config := DefaultTimeoutConfig()

	if config.BaseTimeout != 50*time.Millisecond {
		t.<PERSON><PERSON>rf("Expected BaseTimeout 50ms, got %v", config.BaseTimeout)
	}

	if config.Strategy != TimeoutStrategyAdaptive {
		t.<PERSON>("Expected strategy adaptive, got %v", config.Strategy)
	}

	if !config.EnablePriorityAdjustment {
		t.<PERSON>r("Expected priority adjustment to be enabled")
	}
}

func TestNewBatchTimeoutManager(t *testing.T) {
	manager := NewBatchTimeoutManager(nil)
	defer manager.Close()

	if manager.config == nil {
		t.<PERSON>rror("Expected config to be set")
	}

	if len(manager.metrics.PriorityMetrics) != 4 {
		t.Errorf("Expected 4 priority metrics, got %d", len(manager.metrics.PriorityMetrics))
	}
}

func TestCreateTimeout(t *testing.T) {
	config := &TimeoutConfig{
		BaseTimeout:              50 * time.Millisecond,
		MinTimeout:               10 * time.Millisecond,
		MaxTimeout:               200 * time.Millisecond,
		Strategy:                 TimeoutStrategyFixed,
		EnablePriorityAdjustment: false,
	}

	manager := NewBatchTimeoutManager(config)
	defer manager.Close()

	timeout := manager.CreateTimeout(RequestPriorityNormal, 5)

	if timeout == nil {
		t.Fatal("Expected timeout to be created")
	}

	if timeout.Priority != RequestPriorityNormal {
		t.Errorf("Expected priority normal, got %v", timeout.Priority)
	}

	if timeout.Duration != 50*time.Millisecond {
		t.Errorf("Expected duration 50ms, got %v", timeout.Duration)
	}
}

func TestTimeoutTrigger(t *testing.T) {
	config := &TimeoutConfig{
		BaseTimeout:              50 * time.Millisecond,
		MinTimeout:               10 * time.Millisecond,
		MaxTimeout:               200 * time.Millisecond,
		Strategy:                 TimeoutStrategyFixed,
		EnablePriorityAdjustment: false,
	}

	manager := NewBatchTimeoutManager(config)
	defer manager.Close()

	timeout := manager.CreateTimeout(RequestPriorityNormal, 5)

	select {
	case <-timeout.Done:
		if !timeout.Triggered {
			t.Error("Expected timeout to be triggered")
		}
	case <-time.After(100 * time.Millisecond):
		t.Error("Timeout did not trigger within expected time")
	}

	metrics := manager.GetMetrics()
	if metrics.TimeoutsTriggered != 1 {
		t.Errorf("Expected 1 timeout triggered, got %d", metrics.TimeoutsTriggered)
	}
}

func TestTimeoutCancellation(t *testing.T) {
	config := &TimeoutConfig{
		BaseTimeout:              200 * time.Millisecond,
		MinTimeout:               10 * time.Millisecond,
		MaxTimeout:               300 * time.Millisecond,
		Strategy:                 TimeoutStrategyFixed,
		EnablePriorityAdjustment: false,
	}

	manager := NewBatchTimeoutManager(config)
	defer manager.Close()

	timeout := manager.CreateTimeout(RequestPriorityNormal, 5)

	manager.CancelTimeout(timeout.ID)

	select {
	case <-timeout.Done:
		if timeout.Triggered {
			t.Error("Expected timeout not to be triggered after cancellation")
		}
	case <-time.After(100 * time.Millisecond):
		t.Error("Timeout did not complete after cancellation")
	}
}

func TestConcurrentTimeouts(t *testing.T) {
	manager := NewBatchTimeoutManager(nil)
	defer manager.Close()

	var wg sync.WaitGroup
	numGoroutines := 10
	timeoutsPerGoroutine := 5

	wg.Add(numGoroutines)
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < timeoutsPerGoroutine; j++ {
				priority := RequestPriority(j % 4)
				timeout := manager.CreateTimeout(priority, j+1)
				if timeout == nil {
					t.Errorf("Failed to create timeout")
				}
			}
		}()
	}

	wg.Wait()

	expectedTimeouts := int64(numGoroutines * timeoutsPerGoroutine)
	metrics := manager.GetMetrics()
	if metrics.TimeoutsCreated != expectedTimeouts {
		t.Errorf("Expected %d timeouts created, got %d", expectedTimeouts, metrics.TimeoutsCreated)
	}
}
