package gpu

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"neuralmetergo/internal/gpu/types"
)

// OpenCLDetector implements GPUDetector for OpenCL devices with runtime detection
type OpenCLDetector struct {
	logger      *log.<PERSON>gger
	initialized bool
	deviceCount int
	available   bool
}

// NewOpenCLDetector creates a new OpenCL detector with runtime detection
func NewOpenCLDetector(logger *log.Logger) GPUDetector {
	if logger == nil {
		logger = log.Default()
	}

	detector := &OpenCLDetector{
		logger: logger,
	}

	// Check if OpenCL is available at runtime
	detector.available = detector.checkOpenCLAvailability()
	
	return detector
}

// checkOpenCLAvailability checks if OpenCL is available on the system
func (o *OpenCLDetector) checkOpenCLAvailability() bool {
	// Check for clinfo command
	if _, err := exec.LookPath("clinfo"); err != nil {
		o.logger.Println("clinfo not found in PATH, checking for OpenCL libraries")
	} else {
		// Test clinfo command
		cmd := exec.Command("clinfo", "--list")
		if err := cmd.Run(); err == nil {
			o.logger.Println("OpenCL availability confirmed via clinfo")
			return true
		}
	}

	// Check for OpenCL runtime libraries
	openclPaths := []string{
		"/usr/lib/x86_64-linux-gnu/libOpenCL.so",
		"/usr/lib/libOpenCL.so",
		"/usr/local/lib/libOpenCL.so",
		"/opt/intel/opencl/lib64/libOpenCL.so",
		"/usr/lib64/libOpenCL.so",
	}

	for _, path := range openclPaths {
		if _, err := os.Stat(path); err == nil {
			o.logger.Printf("Found OpenCL runtime at: %s", path)
			return true
		}
	}

	// Check for Intel OpenCL runtime
	if _, err := os.Stat("/opt/intel/opencl"); err == nil {
		o.logger.Println("Intel OpenCL runtime detected")
		return true
	}

	o.logger.Println("OpenCL not available on system")
	return false
}

// Detect enumerates all available OpenCL devices
func (o *OpenCLDetector) Detect() ([]*GPUInfo, error) {
	if !o.available {
		return []*GPUInfo{}, nil
	}

	if !o.initialized {
		if err := o.Initialize(); err != nil {
			return []*GPUInfo{}, nil
		}
	}

	if o.deviceCount == 0 {
	return []*GPUInfo{}, nil
}

	var gpus []*GPUInfo
	for i := 0; i < o.deviceCount; i++ {
		gpu, err := o.GetInfo(i)
		if err != nil {
			o.logger.Printf("Failed to get info for OpenCL device %d: %v", i, err)
			continue
		}
		gpus = append(gpus, gpu)
	}

	return gpus, nil
}

// GetInfo retrieves detailed information about a specific OpenCL device using clinfo
func (o *OpenCLDetector) GetInfo(deviceID int) (*GPUInfo, error) {
	if !o.available {
		return nil, NewGPUError(ErrorTypeDetection, -1, "OpenCL not available on system", deviceID)
	}

	if !o.initialized {
		return nil, fmt.Errorf("OpenCL detector not initialized")
	}

	if deviceID < 0 || deviceID >= o.deviceCount {
		return nil, fmt.Errorf("invalid device ID %d", deviceID)
	}

	// Use clinfo to get device information
	cmd := exec.Command("clinfo", "--raw")
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to query OpenCL devices: %v", err)
	}

	// Parse clinfo output to get device info
	gpu, err := o.parseClInfoOutput(deviceID, string(output))
	if err != nil {
		return nil, fmt.Errorf("failed to parse OpenCL device info: %v", err)
	}

	return gpu, nil
}

// parseClInfoOutput parses clinfo output into GPUInfo
func (o *OpenCLDetector) parseClInfoOutput(deviceID int, output string) (*GPUInfo, error) {
	lines := strings.Split(output, "\n")
	deviceCount := 0
	var currentDevice *GPUInfo

	for _, line := range lines {
		line = strings.TrimSpace(line)
		
		if strings.Contains(line, "Device Type") && strings.Contains(line, "GPU") {
			if deviceCount == deviceID {
				currentDevice = &GPUInfo{
					ID:                      deviceID,
					Type:                    GPUTypeOpenCL,
					Available:               true,
					MaxThreadsPerBlock:      256, // Common OpenCL default
					MaxBlockDimensions:      [3]int{256, 256, 256},
					MaxGridDimensions:       [3]int{65535, 65535, 65535},
					WarpSize:                32, // Common default
					MaxSharedMemoryPerBlock: 32 * 1024, // Common default
				}
			}
			deviceCount++
		}

		if currentDevice != nil && deviceCount-1 == deviceID {
			if strings.Contains(line, "Device Name") {
				parts := strings.Split(line, ":")
				if len(parts) > 1 {
					currentDevice.Name = strings.TrimSpace(parts[1])
				}
			} else if strings.Contains(line, "Global Memory Size") {
				parts := strings.Split(line, ":")
				if len(parts) > 1 {
					sizeStr := strings.TrimSpace(parts[1])
					if size, err := strconv.ParseInt(sizeStr, 10, 64); err == nil {
						currentDevice.TotalMemory = size
						currentDevice.FreeMemory = size // Assume all free initially
					}
				}
			} else if strings.Contains(line, "Max Compute Units") {
				parts := strings.Split(line, ":")
				if len(parts) > 1 {
					countStr := strings.TrimSpace(parts[1])
					if count, err := strconv.Atoi(countStr); err == nil {
						currentDevice.MultiProcessorCount = count
					}
				}
			} else if strings.Contains(line, "Max Clock Frequency") {
				parts := strings.Split(line, ":")
				if len(parts) > 1 {
					clockStr := strings.TrimSpace(parts[1])
					if clock, err := strconv.Atoi(clockStr); err == nil {
						currentDevice.ClockRate = clock
					}
				}
			}
		}
	}

	if currentDevice == nil {
		return nil, fmt.Errorf("device %d not found in clinfo output", deviceID)
	}

	return currentDevice, nil
}

// GetMetrics retrieves real-time metrics for an OpenCL device
func (o *OpenCLDetector) GetMetrics(deviceID int) (*GPUMetrics, error) {
	if !o.available {
		return nil, NewGPUError(ErrorTypeMonitoring, -1, "OpenCL not available on system", deviceID)
	}

	if !o.initialized {
		return nil, fmt.Errorf("OpenCL detector not initialized")
	}

	if deviceID < 0 || deviceID >= o.deviceCount {
		return nil, fmt.Errorf("invalid device ID %d", deviceID)
	}

	// OpenCL doesn't provide direct metrics, so we'll return basic info
	metrics := &GPUMetrics{
		DeviceID:          deviceID,
		Timestamp:         time.Now(),
		GPUUtilization:    0.0, // Not available via OpenCL
		MemoryUtilization: 0.0, // Not available via OpenCL
		PowerConsumption:  0.0, // Not available via OpenCL
		ClockSpeed:        0,   // Not available via OpenCL
		MemoryClockSpeed:  0,   // Not available via OpenCL
		FanSpeed:          0,   // Not available via OpenCL
	}

	return metrics, nil
}

// IsSupported returns true if OpenCL is available on the system
func (o *OpenCLDetector) IsSupported() bool {
	return o.available
}

// Initialize initializes the OpenCL detector using clinfo
func (o *OpenCLDetector) Initialize() error {
	if !o.available {
		o.logger.Println("OpenCL detector not available on system")
		return NewGPUError(ErrorTypeInitialization, -1, "OpenCL not available on system", -1)
	}

	// Count devices using clinfo
	cmd := exec.Command("clinfo", "--raw")
	output, err := cmd.Output()
	if err != nil {
		o.logger.Printf("Failed to get OpenCL device count: %v", err)
		return NewGPUError(ErrorTypeInitialization, -1, fmt.Sprintf("Failed to query OpenCL devices: %v", err), -1)
	}

	// Count GPU devices in output
	deviceCount := 0
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, "Device Type") && strings.Contains(line, "GPU") {
			deviceCount++
		}
	}

	o.deviceCount = deviceCount
	o.initialized = true

	o.logger.Printf("OpenCL detector initialized with %d devices", o.deviceCount)
	return nil
}

// Cleanup cleans up OpenCL resources
func (o *OpenCLDetector) Cleanup() error {
	o.initialized = false
	o.deviceCount = 0
	return nil
}

// OpenCLBackendWrapper wraps OpenCLDetector to implement types.GPUBackend interface
type OpenCLBackendWrapper struct {
	detector GPUDetector
}

// Name returns the backend name
func (o *OpenCLBackendWrapper) Name() string {
	return "OpenCL"
}

// Version returns the backend version
func (o *OpenCLBackendWrapper) Version() string {
	return "1.0"
}

// Platform returns the platform name
func (o *OpenCLBackendWrapper) Platform() string {
	return "Cross-platform"
}

// EnumerateDevices lists all OpenCL devices
func (o *OpenCLBackendWrapper) EnumerateDevices(ctx context.Context) ([]types.GPUDevice, error) {
	gpuInfos, err := o.detector.Detect()
	if err != nil {
		return nil, err
	}

	var devices []types.GPUDevice
	for _, info := range gpuInfos {
		device := types.GPUDevice{
			ID:      fmt.Sprintf("opencl:%d", info.ID),
			Name:    info.Name,
			Backend: "OpenCL",
		}
		devices = append(devices, device)
	}

	return devices, nil
}

// GetDevice returns a specific device by ID
func (o *OpenCLBackendWrapper) GetDevice(deviceID string) (*types.GPUDevice, error) {
	var id int
	fmt.Sscanf(deviceID, "opencl:%d", &id)
	
	info, err := o.detector.GetInfo(id)
	if err != nil {
		return nil, err
	}

	return &types.GPUDevice{
		ID:      deviceID,
		Name:    info.Name,
		Backend: "OpenCL",
	}, nil
}

// GetCapabilities returns device capabilities
func (o *OpenCLBackendWrapper) GetCapabilities(device *types.GPUDevice) (*types.GPUCapability, error) {
	return &types.GPUCapability{
		Features: map[types.GPUFeature]bool{
			types.FeatureCompute: true,
		},
	}, nil
}

// SupportsFeature checks if device supports a feature
func (o *OpenCLBackendWrapper) SupportsFeature(device *types.GPUDevice, feature types.GPUFeature) bool {
	return feature == types.FeatureCompute
}

// CreateContext creates a GPU context for the device
func (o *OpenCLBackendWrapper) CreateContext(device *types.GPUDevice) (types.GPUContext, error) {
	var deviceID int
	fmt.Sscanf(device.ID, "opencl:%d", &deviceID)
	
	return &OpenCLContext{
		deviceID: deviceID,
		detector: o.detector,
	}, nil
}

// CreateMemoryManager creates a memory manager
func (o *OpenCLBackendWrapper) CreateMemoryManager(ctx types.GPUContext) (types.GPUMemoryManager, error) {
	return &OpenCLMemoryManager{ctx: ctx}, nil
}

// CreateExecutor creates an executor
func (o *OpenCLBackendWrapper) CreateExecutor(ctx types.GPUContext) (types.GPUExecutor, error) {
	return &OpenCLExecutor{ctx: ctx}, nil
}

// OpenCLContext implements types.GPUContext
type OpenCLContext struct {
	deviceID int
	detector GPUDetector
}

func (o *OpenCLContext) GetDevice() *types.GPUDevice {
	return &types.GPUDevice{
		ID:      fmt.Sprintf("opencl:%d", o.deviceID),
		Backend: "OpenCL",
	}
}

func (o *OpenCLContext) IsValid() bool {
	return true
}

func (o *OpenCLContext) Synchronize() error {
	return nil
}

func (o *OpenCLContext) GetDeviceID() string {
	return fmt.Sprintf("opencl:%d", o.deviceID)
}

func (o *OpenCLContext) GetBackend() string {
	return "OpenCL"
}

func (o *OpenCLContext) Destroy() error {
	return nil
}

// OpenCLMemoryManager implements types.GPUMemoryManager
type OpenCLMemoryManager struct {
	ctx types.GPUContext
}

func (o *OpenCLMemoryManager) Allocate(size uint64) (types.GPUMemory, error) {
	return &OpenCLMemory{size: size}, nil
}

func (o *OpenCLMemoryManager) AllocateType(size uint64, memType types.MemoryType) (types.GPUMemory, error) {
	return &OpenCLMemory{size: size}, nil
}

func (o *OpenCLMemoryManager) GetStats() types.GPUMemoryStats {
	return types.GPUMemoryStats{}
}

func (o *OpenCLMemoryManager) Cleanup() error {
	return nil
}

// OpenCLMemory implements types.GPUMemory
type OpenCLMemory struct {
	size uint64
}

func (o *OpenCLMemory) Ptr() uintptr {
	return 0
}

func (o *OpenCLMemory) Size() uint64 {
	return o.size
}

func (o *OpenCLMemory) Type() types.MemoryType {
	return types.MemoryTypeDiscrete
}

func (o *OpenCLMemory) Free() error {
	return nil
}

func (o *OpenCLMemory) CopyFrom(src []byte) error {
	return nil
}

func (o *OpenCLMemory) CopyTo(dst []byte) error {
	return nil
}

func (o *OpenCLMemory) CopyFromGPU(src types.GPUMemory) error {
	return nil
}

// OpenCLExecutor implements types.GPUExecutor
type OpenCLExecutor struct {
	ctx types.GPUContext
}

func (o *OpenCLExecutor) CreateKernel(source string, entryPoint string) (types.GPUKernel, error) {
	return &OpenCLKernel{name: entryPoint}, nil
}

func (o *OpenCLExecutor) CreateStream() (types.GPUStream, error) {
	return &OpenCLStream{id: "opencl_stream"}, nil
}

func (o *OpenCLExecutor) CreateEvent() (types.GPUEvent, error) {
	return &OpenCLEvent{}, nil
}

func (o *OpenCLExecutor) Synchronize() error {
	return nil
}

func (o *OpenCLExecutor) GetStreams() []types.GPUStream {
	return []types.GPUStream{}
}

// OpenCLKernel implements types.GPUKernel
type OpenCLKernel struct {
	name string
}

func (o *OpenCLKernel) GetName() string {
	return o.name
}

func (o *OpenCLKernel) Launch(grid types.GridDimension, block types.GridDimension, args []interface{}, stream types.GPUStream) error {
	return nil
}

func (o *OpenCLKernel) GetAttributes() map[string]interface{} {
	return map[string]interface{}{}
}

func (o *OpenCLKernel) Destroy() error {
	return nil
}

// OpenCLStream implements types.GPUStream
type OpenCLStream struct {
	id string
}

func (o *OpenCLStream) ID() string {
	return o.id
}

func (o *OpenCLStream) Submit(kernel types.GPUKernel, grid types.GridDimension, block types.GridDimension, args []interface{}) error {
	return nil
}

func (o *OpenCLStream) Synchronize() error {
	return nil
}

func (o *OpenCLStream) Query() types.StreamState {
	return 0
}

func (o *OpenCLStream) Destroy() error {
	return nil
}

// OpenCLEvent implements types.GPUEvent
type OpenCLEvent struct{}

func (o *OpenCLEvent) ID() string {
	return "opencl_event"
}

func (o *OpenCLEvent) Record(stream types.GPUStream) error {
	return nil
}

func (o *OpenCLEvent) Wait() error {
	return nil
}

func (o *OpenCLEvent) Query() types.EventState {
	return 0
}

func (o *OpenCLEvent) ElapsedTime(start types.GPUEvent) (time.Duration, error) {
	return 0, nil
}

func (o *OpenCLEvent) Destroy() error {
	return nil
}
