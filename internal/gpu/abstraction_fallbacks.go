//go:build cuda

package gpu

import "neuralmetergo/internal/gpu/types"

// Fallback implementations for backends not available with current build tags
// These ensure the abstraction manager can compile regardless of which backends are built

// createMetalBackendIfAvailable - fallback for non-Darwin builds
func createMetalBackendIfAvailable() types.GPUBackend {
	return nil // Metal not available on Linux
}

// createOpenCLBackendIfAvailable - fallback when OpenCL not built
func createOpenCLBackendIfAvailable() types.GPUBackend {
	return nil // OpenCL not built with cuda-only build
}

// createROCmBackendIfAvailable - fallback when ROCm not built  
func createROCmBackendIfAvailable() types.GPUBackend {
	return nil // ROCm not built with cuda-only build
}

// createOneAPIBackendIfAvailable - fallback when OneAPI not built
func createOneAPIBackendIfAvailable() types.GPUBackend {
	return nil // OneAPI not built with cuda-only build
}

// createDirectMLBackendIfAvailable - fallback for non-Windows builds
func createDirectMLBackendIfAvailable() types.GPUBackend {
	return nil // DirectML not available on Linux
} 