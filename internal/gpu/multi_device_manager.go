package gpu

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// MultiDeviceManager manages multiple GPU devices with load balancing and coordination
// Implements MultiDeviceManagerInterface for performance monitoring integration
type MultiDeviceManager struct {
	devices        []*ManagedDevice
	deviceMap      map[string]*ManagedDevice
	strategy       LoadBalancingStrategy
	abstractionMgr *AbstractionManager
	logger         *log.Logger
	mu             sync.RWMutex
	config         MultiDeviceConfig
	isInitialized  bool
	syncManager    *SynchronizationManager
}

// ManagedDevice represents a GPU device under management
type ManagedDevice struct {
	Device    *GPUDevice
	Context   GPUContext
	MemoryMgr GPUMemoryManager
	Executor  GPUExecutor
	Metrics   *DeviceMetrics
	LoadLevel float64 // Current load (0.0 - 1.0)
	LastUsed  time.Time
	IsActive  bool
	mu        sync.RWMutex
}

// DeviceMetrics tracks performance metrics for a managed device
type DeviceMetrics struct {
	ID                 string        `json:"id"`
	MemoryUtilization  float64       `json:"memory_utilization"`  // 0.0 - 1.0
	ComputeUtilization float64       `json:"compute_utilization"` // 0.0 - 1.0
	Temperature        int           `json:"temperature"`         // Celsius
	PowerConsumption   float64       `json:"power_consumption"`   // Watts
	TasksCompleted     int64         `json:"tasks_completed"`
	AverageTaskTime    time.Duration `json:"average_task_time"`
	ErrorCount         int64         `json:"error_count"`
	LastUpdate         time.Time     `json:"last_update"`
	ThroughputMBps     float64       `json:"throughput_mbps"`
}

// LoadBalancingStrategy defines different strategies for distributing work
type LoadBalancingStrategy int

const (
	LoadBalanceRoundRobin LoadBalancingStrategy = iota
	LoadBalanceMemoryBased
	LoadBalanceComputeBased
	LoadBalanceDynamic
	LoadBalanceWeighted
)

func (s LoadBalancingStrategy) String() string {
	switch s {
	case LoadBalanceRoundRobin:
		return "round_robin"
	case LoadBalanceMemoryBased:
		return "memory_based"
	case LoadBalanceComputeBased:
		return "compute_based"
	case LoadBalanceDynamic:
		return "dynamic"
	case LoadBalanceWeighted:
		return "weighted"
	default:
		return "unknown"
	}
}

// MultiDeviceConfig contains configuration for multi-device operations
type MultiDeviceConfig struct {
	MaxDevices            int                   `json:"max_devices"`
	MinDevices            int                   `json:"min_devices"`
	Strategy              LoadBalancingStrategy `json:"strategy"`
	MemoryThreshold       float64               `json:"memory_threshold"`      // 0.0 - 1.0
	ComputeThreshold      float64               `json:"compute_threshold"`     // 0.0 - 1.0
	TemperatureThreshold  int                   `json:"temperature_threshold"` // Celsius
	MonitoringInterval    time.Duration         `json:"monitoring_interval"`
	FailoverEnabled       bool                  `json:"failover_enabled"`
	DeviceWeights         map[string]float64    `json:"device_weights"`
	ExcludedDevices       []string              `json:"excluded_devices"`
	PreferredDevices      []string              `json:"preferred_devices"`
	EnablePeerToPeer      bool                  `json:"enable_peer_to_peer"`
	DataLocalityOptimized bool                  `json:"data_locality_optimized"`
}

// DefaultMultiDeviceConfig returns default configuration for multi-device operations
func DefaultMultiDeviceConfig() MultiDeviceConfig {
	return MultiDeviceConfig{
		MaxDevices:            8, // Reasonable limit for most systems
		MinDevices:            1, // Fallback to single device
		Strategy:              LoadBalanceDynamic,
		MemoryThreshold:       0.85, // 85% memory utilization threshold
		ComputeThreshold:      0.90, // 90% compute utilization threshold
		TemperatureThreshold:  85,   // 85°C temperature threshold
		MonitoringInterval:    time.Second * 2,
		FailoverEnabled:       true,
		DeviceWeights:         make(map[string]float64),
		ExcludedDevices:       []string{},
		PreferredDevices:      []string{},
		EnablePeerToPeer:      true,
		DataLocalityOptimized: true,
	}
}

// NewMultiDeviceManager creates a new multi-device manager
func NewMultiDeviceManager(config MultiDeviceConfig, logger *log.Logger) (*MultiDeviceManager, error) {
	if logger == nil {
		logger = log.Default()
	}

	// Validate configuration
	if err := validateMultiDeviceConfig(config); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	abstractionMgr := NewAbstractionManager(logger)
	syncMgr := NewSynchronizationManager(logger)

	mgr := &MultiDeviceManager{
		devices:        make([]*ManagedDevice, 0),
		deviceMap:      make(map[string]*ManagedDevice),
		strategy:       config.Strategy,
		abstractionMgr: abstractionMgr,
		logger:         logger,
		config:         config,
		isInitialized:  false,
		syncManager:    syncMgr,
	}

	return mgr, nil
}

// Initialize discovers and initializes available GPU devices
func (mdm *MultiDeviceManager) Initialize(ctx context.Context) error {
	mdm.mu.Lock()
	defer mdm.mu.Unlock()

	if mdm.isInitialized {
		return fmt.Errorf("multi-device manager already initialized")
	}

	// Discover available devices
	devices, err := mdm.abstractionMgr.EnumerateDevices(ctx)
	if err != nil {
		return fmt.Errorf("failed to enumerate devices: %w", err)
	}

	mdm.logger.Printf("Discovered %d total devices", len(devices))

	// Filter and select devices based on configuration
	selectedDevices, err := mdm.selectDevices(devices)
	if err != nil {
		return fmt.Errorf("failed to select devices: %w", err)
	}

	if len(selectedDevices) < mdm.config.MinDevices {
		return fmt.Errorf("insufficient devices: found %d, minimum required %d",
			len(selectedDevices), mdm.config.MinDevices)
	}

	// Initialize selected devices
	for _, device := range selectedDevices {
		managedDevice, err := mdm.initializeDevice(ctx, device)
		if err != nil {
			mdm.logger.Printf("Failed to initialize device %s: %v", device.ID, err)
			continue
		}

		mdm.devices = append(mdm.devices, managedDevice)
		mdm.deviceMap[device.ID] = managedDevice
		mdm.logger.Printf("Successfully initialized device %s (%s)", device.ID, device.Name)
	}

	if len(mdm.devices) < mdm.config.MinDevices {
		return fmt.Errorf("failed to initialize minimum required devices: got %d, need %d",
			len(mdm.devices), mdm.config.MinDevices)
	}

	// Initialize synchronization manager with devices
	if err := mdm.syncManager.Initialize(mdm.devices); err != nil {
		return fmt.Errorf("failed to initialize synchronization manager: %w", err)
	}

	// Start monitoring if enabled
	if mdm.config.MonitoringInterval > 0 {
		go mdm.startMonitoring()
	}

	mdm.isInitialized = true
	mdm.logger.Printf("Multi-device manager initialized with %d devices using %s strategy",
		len(mdm.devices), mdm.strategy)

	return nil
}

// selectDevices filters and selects devices based on configuration
func (mdm *MultiDeviceManager) selectDevices(allDevices []GPUDevice) ([]*GPUDevice, error) {
	var candidates []*GPUDevice

	// Apply exclusion filters
	for i := range allDevices {
		device := &allDevices[i]

		// Skip excluded devices
		if mdm.isDeviceExcluded(device.ID) {
			mdm.logger.Printf("Excluding device %s (in exclusion list)", device.ID)
			continue
		}

		// Skip unavailable devices
		if !device.IsActive {
			mdm.logger.Printf("Excluding device %s (not available)", device.ID)
			continue
		}

		candidates = append(candidates, device)
	}

	// Apply preference ordering
	var selected []*GPUDevice

	// First, add preferred devices if they exist
	for _, preferredID := range mdm.config.PreferredDevices {
		for _, candidate := range candidates {
			if candidate.ID == preferredID {
				selected = append(selected, candidate)
				break
			}
		}
	}

	// Then add remaining candidates up to max limit
	for _, candidate := range candidates {
		if len(selected) >= mdm.config.MaxDevices {
			break
		}

		// Skip if already in preferred list
		alreadySelected := false
		for _, s := range selected {
			if s.ID == candidate.ID {
				alreadySelected = true
				break
			}
		}

		if !alreadySelected {
			selected = append(selected, candidate)
		}
	}

	return selected, nil
}

// initializeDevice initializes a single device for management
func (mdm *MultiDeviceManager) initializeDevice(ctx context.Context, device *GPUDevice) (*ManagedDevice, error) {
	// Create GPU context
	gpuCtx, err := mdm.abstractionMgr.CreateContext(ctx, device)
	if err != nil {
		return nil, fmt.Errorf("failed to create context: %w", err)
	}

	// Create memory manager
	memoryMgr, err := mdm.abstractionMgr.CreateMemoryManager(gpuCtx)
	if err != nil {
		gpuCtx.Destroy()
		return nil, fmt.Errorf("failed to create memory manager: %w", err)
	}

	// Create executor
	executor, err := mdm.abstractionMgr.CreateExecutor(gpuCtx)
	if err != nil {
		gpuCtx.Destroy()
		return nil, fmt.Errorf("failed to create executor: %w", err)
	}

	// Initialize metrics
	metrics := &DeviceMetrics{
		ID:                 device.ID,
		MemoryUtilization:  0.0,
		ComputeUtilization: 0.0,
		Temperature:        0,
		PowerConsumption:   0.0,
		TasksCompleted:     0,
		AverageTaskTime:    0,
		ErrorCount:         0,
		LastUpdate:         time.Now(),
		ThroughputMBps:     0.0,
	}

	managedDevice := &ManagedDevice{
		Device:    device,
		Context:   gpuCtx,
		MemoryMgr: memoryMgr,
		Executor:  executor,
		Metrics:   metrics,
		LoadLevel: 0.0,
		LastUsed:  time.Now(),
		IsActive:  true,
	}

	return managedDevice, nil
}

// isDeviceExcluded checks if a device is in the exclusion list
func (mdm *MultiDeviceManager) isDeviceExcluded(deviceID string) bool {
	for _, excludedID := range mdm.config.ExcludedDevices {
		if excludedID == deviceID {
			return true
		}
	}
	return false
}

// validateMultiDeviceConfig validates the configuration
func validateMultiDeviceConfig(config MultiDeviceConfig) error {
	if config.MaxDevices < 1 {
		return fmt.Errorf("max_devices must be at least 1")
	}

	if config.MinDevices < 1 {
		return fmt.Errorf("min_devices must be at least 1")
	}

	if config.MinDevices > config.MaxDevices {
		return fmt.Errorf("min_devices cannot be greater than max_devices")
	}

	if config.MemoryThreshold < 0.0 || config.MemoryThreshold > 1.0 {
		return fmt.Errorf("memory_threshold must be between 0.0 and 1.0")
	}

	if config.ComputeThreshold < 0.0 || config.ComputeThreshold > 1.0 {
		return fmt.Errorf("compute_threshold must be between 0.0 and 1.0")
	}

	if config.TemperatureThreshold < 0 || config.TemperatureThreshold > 150 {
		return fmt.Errorf("temperature_threshold must be between 0 and 150")
	}

	if config.MonitoringInterval < 0 {
		return fmt.Errorf("monitoring_interval cannot be negative")
	}

	return nil
}

// GetDevices returns all managed devices
func (mdm *MultiDeviceManager) GetDevices() []*ManagedDevice {
	mdm.mu.RLock()
	defer mdm.mu.RUnlock()

	devices := make([]*ManagedDevice, len(mdm.devices))
	copy(devices, mdm.devices)
	return devices
}

// GetDevice returns a specific managed device by ID
func (mdm *MultiDeviceManager) GetDevice(deviceID string) (*ManagedDevice, error) {
	mdm.mu.RLock()
	defer mdm.mu.RUnlock()

	device, exists := mdm.deviceMap[deviceID]
	if !exists {
		return nil, fmt.Errorf("device %s not found", deviceID)
	}

	return device, nil
}

// GetActiveDevices returns all currently active devices
func (mdm *MultiDeviceManager) GetActiveDevices() []*ManagedDevice {
	mdm.mu.RLock()
	defer mdm.mu.RUnlock()

	var activeDevices []*ManagedDevice
	for _, device := range mdm.devices {
		device.mu.RLock()
		if device.IsActive {
			activeDevices = append(activeDevices, device)
		}
		device.mu.RUnlock()
	}

	return activeDevices
}

// IsInitialized returns whether the manager is initialized
func (mdm *MultiDeviceManager) IsInitialized() bool {
	mdm.mu.RLock()
	defer mdm.mu.RUnlock()
	return mdm.isInitialized
}

// GetConfiguration returns the current configuration
func (mdm *MultiDeviceManager) GetConfiguration() MultiDeviceConfig {
	mdm.mu.RLock()
	defer mdm.mu.RUnlock()
	return mdm.config
}

// startMonitoring starts the device monitoring loop
func (mdm *MultiDeviceManager) startMonitoring() {
	ticker := time.NewTicker(mdm.config.MonitoringInterval)
	defer ticker.Stop()

	for range ticker.C {
		mdm.updateDeviceMetrics()
	}
}

// updateDeviceMetrics updates metrics for all devices
func (mdm *MultiDeviceManager) updateDeviceMetrics() {
	mdm.mu.RLock()
	devices := make([]*ManagedDevice, len(mdm.devices))
	copy(devices, mdm.devices)
	mdm.mu.RUnlock()

	for _, device := range devices {
		device.mu.Lock()
		// Update device metrics - this would integrate with actual GPU monitoring
		// For now, we'll simulate metric updates
		device.Metrics.LastUpdate = time.Now()
		device.mu.Unlock()
	}
}

// Cleanup shuts down the multi-device manager and cleans up resources
func (mdm *MultiDeviceManager) Cleanup() error {
	mdm.mu.Lock()
	defer mdm.mu.Unlock()

	if !mdm.isInitialized {
		return nil
	}

	var errors []error

	// Cleanup synchronization manager
	if err := mdm.syncManager.Cleanup(); err != nil {
		errors = append(errors, fmt.Errorf("sync manager cleanup failed: %w", err))
	}

	// Cleanup all managed devices
	for _, device := range mdm.devices {
		if err := mdm.cleanupDevice(device); err != nil {
			errors = append(errors, fmt.Errorf("device %s cleanup failed: %w", device.Device.ID, err))
		}
	}

	// Cleanup abstraction manager
	if err := mdm.abstractionMgr.Cleanup(); err != nil {
		errors = append(errors, fmt.Errorf("abstraction manager cleanup failed: %w", err))
	}

	mdm.devices = nil
	mdm.deviceMap = nil
	mdm.isInitialized = false

	if len(errors) > 0 {
		return fmt.Errorf("cleanup completed with errors: %v", errors)
	}

	mdm.logger.Println("Multi-device manager cleanup completed successfully")
	return nil
}

// cleanupDevice cleans up resources for a single device
func (mdm *MultiDeviceManager) cleanupDevice(device *ManagedDevice) error {
	device.mu.Lock()
	defer device.mu.Unlock()

	var errors []error

	// Memory managers don't need explicit cleanup - they're cleaned up when context is destroyed

	if device.Context != nil {
		if err := device.Context.Destroy(); err != nil {
			errors = append(errors, fmt.Errorf("context destroy failed: %w", err))
		}
	}

	device.IsActive = false

	if len(errors) > 0 {
		return fmt.Errorf("device cleanup failed: %v", errors)
	}

	return nil
}
