package gpu

import "testing"

func TestAccessControl_CheckAccess(t *testing.T) {
	mgr := NewAccessControlManager(SecurityConfig{EnableAccessControl: true})
	if !mgr.CheckAccess("user1", "gpu0") {
		t.<PERSON><PERSON>r("Expected access to be granted by default")
	}
}

func TestAccessControl_GrantRevoke(t *testing.T) {
	mgr := NewAccessControlManager(SecurityConfig{EnableAccessControl: true})
	err := mgr.GrantAccess("user2", "gpu1")
	if err != nil {
		t.<PERSON><PERSON>rf("GrantAccess error: %v", err)
	}
	err = mgr.RevokeAccess("user2", "gpu1")
	if err != nil {
		t.<PERSON><PERSON>("RevokeAccess error: %v", err)
	}
}
