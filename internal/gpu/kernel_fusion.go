package gpu

import (
	"fmt"
	"strings"
	"sync"
	"time"
)

// FusionOpType represents different types of operations that can be fused
type FusionOpType string

const (
	FusionElementwise FusionOpType = "elementwise"
	FusionActivation  FusionOpType = "activation"
	FusionReduction   FusionOpType = "reduction"
	FusionConvolution FusionOpType = "convolution"
	FusionMatMul      FusionOpType = "matmul"
	FusionPooling     FusionOpType = "pooling"
	FusionTranspose   FusionOpType = "transpose"
	FusionBroadcast   FusionOpType = "broadcast"
)

// FusionOperation represents a single operation in a fusion sequence
type FusionOperation struct {
	ID          string                 `json:"id"`
	Type        FusionOpType           `json:"type"`
	Name        string                 `json:"name"`
	Parameters  map[string]interface{} `json:"parameters"`
	InputShapes []TensorShape          `json:"input_shapes"`
	OutputShape TensorShape            `json:"output_shape"`
	DataType    TensorDataType         `json:"data_type"`
	Cost        float64                `json:"cost"` // Estimated computational cost
}

// FusionPattern represents a sequence of operations that can be fused
type FusionPattern struct {
	ID           string             `json:"id"`
	Name         string             `json:"name"`
	Operations   []*FusionOperation `json:"operations"`
	Inputs       []string           `json:"inputs"`
	Outputs      []string           `json:"outputs"`
	FusionCost   float64            `json:"fusion_cost"`
	SeparateCost float64            `json:"separate_cost"`
	Speedup      float64            `json:"speedup"`
	MemorySaved  int64              `json:"memory_saved"`
}

// KernelFusionConfig represents configuration for kernel fusion
type KernelFusionConfig struct {
	Enabled               bool             `json:"enabled"`
	MaxFusionDepth        int              `json:"max_fusion_depth"`
	MinSpeedupThreshold   float64          `json:"min_speedup_threshold"`
	MaxKernelComplexity   int              `json:"max_kernel_complexity"`
	EnableAutoTuning      bool             `json:"enable_auto_tuning"`
	TuningIterations      int              `json:"tuning_iterations"`
	CacheOptimizedKernels bool             `json:"cache_optimized_kernels"`
	EnablePatternMatching bool             `json:"enable_pattern_matching"`
	PreferredFusionTypes  []FusionOpType   `json:"preferred_fusion_types"`
	BlockSizeHints        map[string]int   `json:"block_size_hints"`
	GridSizeHints         map[string]int   `json:"grid_size_hints"`
	SharedMemoryLimits    map[string]int64 `json:"shared_memory_limits"`
	RegisterLimits        map[string]int   `json:"register_limits"`
}

// DefaultKernelFusionConfig returns default fusion configuration
func DefaultKernelFusionConfig() KernelFusionConfig {
	return KernelFusionConfig{
		Enabled:               true,
		MaxFusionDepth:        5,
		MinSpeedupThreshold:   1.2,
		MaxKernelComplexity:   10,
		EnableAutoTuning:      true,
		TuningIterations:      10,
		CacheOptimizedKernels: true,
		EnablePatternMatching: true,
		PreferredFusionTypes:  []FusionOpType{FusionElementwise, FusionActivation, FusionReduction},
		BlockSizeHints:        map[string]int{"elementwise": 256, "reduction": 128, "matmul": 16},
		GridSizeHints:         map[string]int{"elementwise": 65535, "reduction": 1024, "matmul": 65535},
		SharedMemoryLimits:    map[string]int64{"default": 48 * 1024}, // 48KB
		RegisterLimits:        map[string]int{"default": 255},
	}
}

// KernelFusionEngine manages kernel fusion and optimization
type KernelFusionEngine struct {
	config         KernelFusionConfig
	compiler       *KernelCompiler
	patterns       map[string]*FusionPattern
	optimizedCache map[string]*CompiledKernel
	metrics        FusionMetrics
	mutex          sync.RWMutex
}

// FusionMetrics tracks kernel fusion performance statistics
type FusionMetrics struct {
	TotalFusions        int64         `json:"total_fusions"`
	SuccessfulFusions   int64         `json:"successful_fusions"`
	FailedFusions       int64         `json:"failed_fusions"`
	AverageSpeedup      float64       `json:"average_speedup"`
	TotalMemorySaved    int64         `json:"total_memory_saved"`
	CacheHits           int64         `json:"cache_hits"`
	CacheMisses         int64         `json:"cache_misses"`
	TuningTime          time.Duration `json:"tuning_time"`
	CompilationTime     time.Duration `json:"compilation_time"`
	PatternMatchingTime time.Duration `json:"pattern_matching_time"`
}

// NewKernelFusionEngine creates a new kernel fusion engine
func NewKernelFusionEngine(config KernelFusionConfig, compiler *KernelCompiler) *KernelFusionEngine {
	engine := &KernelFusionEngine{
		config:         config,
		compiler:       compiler,
		patterns:       make(map[string]*FusionPattern),
		optimizedCache: make(map[string]*CompiledKernel),
		metrics:        FusionMetrics{},
	}

	// Initialize built-in fusion patterns
	engine.initializeBuiltinPatterns()

	return engine
}

// FuseOperations attempts to fuse a sequence of operations into a single kernel
func (kfe *KernelFusionEngine) FuseOperations(operations []*FusionOperation) (*FusionPattern, error) {
	if !kfe.config.Enabled {
		return nil, fmt.Errorf("kernel fusion is disabled")
	}

	startTime := time.Now()
	defer func() {
		kfe.metrics.PatternMatchingTime += time.Since(startTime)
	}()

	kfe.mutex.Lock()
	defer kfe.mutex.Unlock()

	// Validate operations for fusion
	if err := kfe.validateFusionOperations(operations); err != nil {
		kfe.metrics.FailedFusions++
		return nil, fmt.Errorf("fusion validation failed: %w", err)
	}

	// Create fusion pattern
	pattern := &FusionPattern{
		ID:         kfe.generatePatternID(operations),
		Name:       kfe.generatePatternName(operations),
		Operations: operations,
		Inputs:     kfe.extractInputs(operations),
		Outputs:    kfe.extractOutputs(operations),
	}

	// Calculate costs and potential speedup
	pattern.SeparateCost = kfe.calculateSeparateCost(operations)
	pattern.FusionCost = kfe.calculateFusionCost(operations)
	pattern.Speedup = pattern.SeparateCost / pattern.FusionCost
	pattern.MemorySaved = kfe.calculateMemorySavings(operations)

	// Check if fusion is beneficial
	if pattern.Speedup < kfe.config.MinSpeedupThreshold {
		kfe.metrics.FailedFusions++
		return nil, fmt.Errorf("fusion not beneficial: speedup %.2f < threshold %.2f",
			pattern.Speedup, kfe.config.MinSpeedupThreshold)
	}

	// Store pattern
	kfe.patterns[pattern.ID] = pattern
	kfe.metrics.SuccessfulFusions++
	kfe.metrics.AverageSpeedup = (kfe.metrics.AverageSpeedup*float64(kfe.metrics.SuccessfulFusions-1) + pattern.Speedup) / float64(kfe.metrics.SuccessfulFusions)
	kfe.metrics.TotalMemorySaved += pattern.MemorySaved

	return pattern, nil
}

// GenerateFusedKernel creates an optimized kernel for a fusion pattern
func (kfe *KernelFusionEngine) GenerateFusedKernel(pattern *FusionPattern, target CompilationTarget) (*CompiledKernel, error) {
	startTime := time.Now()
	defer func() {
		kfe.metrics.CompilationTime += time.Since(startTime)
	}()

	// Check cache first
	cacheKey := fmt.Sprintf("%s_%s", pattern.ID, target.Architecture)
	if kernel, exists := kfe.optimizedCache[cacheKey]; exists {
		kfe.metrics.CacheHits++
		return kernel, nil
	}
	kfe.metrics.CacheMisses++

	// Generate kernel source
	source, err := kfe.generateKernelSource(pattern, target)
	if err != nil {
		return nil, fmt.Errorf("failed to generate kernel source: %w", err)
	}

	// Compile with optimization
	options := DefaultCompilationOptions()
	options.OptimizationLevel = OptimizationFull
	options.Target = target

	// Apply auto-tuning if enabled
	if kfe.config.EnableAutoTuning {
		options = kfe.autoTuneCompilationOptions(options, pattern, target)
	}

	kernel, err := kfe.compiler.CompileKernel(source, options)
	if err != nil {
		return nil, fmt.Errorf("failed to compile fused kernel: %w", err)
	}

	// Cache the compiled kernel
	if kfe.config.CacheOptimizedKernels {
		kfe.optimizedCache[cacheKey] = kernel
	}

	kfe.metrics.TotalFusions++
	return kernel, nil
}

// FindFusionOpportunities analyzes a sequence of operations to find fusion opportunities
func (kfe *KernelFusionEngine) FindFusionOpportunities(operations []*FusionOperation) ([]*FusionPattern, error) {
	if !kfe.config.EnablePatternMatching {
		return nil, nil
	}

	var opportunities []*FusionPattern

	// Look for common fusion patterns
	for i := 0; i < len(operations); i++ {
		// Try different fusion depths
		for depth := 2; depth <= kfe.config.MaxFusionDepth && i+depth <= len(operations); depth++ {
			subOps := operations[i : i+depth]

			// Check if this subsequence can be fused
			if kfe.canFuseOperations(subOps) {
				pattern, err := kfe.FuseOperations(subOps)
				if err == nil && pattern.Speedup >= kfe.config.MinSpeedupThreshold {
					opportunities = append(opportunities, pattern)
				}
			}
		}
	}

	return opportunities, nil
}

// OptimizeKernelParameters performs automatic parameter tuning for a kernel
func (kfe *KernelFusionEngine) OptimizeKernelParameters(kernel *CompiledKernel, pattern *FusionPattern, target CompilationTarget) (*KernelLaunchParams, error) {
	if !kfe.config.EnableAutoTuning {
		return kfe.getDefaultLaunchParams(pattern, target), nil
	}

	startTime := time.Now()
	defer func() {
		kfe.metrics.TuningTime += time.Since(startTime)
	}()

	bestParams := kfe.getDefaultLaunchParams(pattern, target)
	bestTime := float64(1e9) // Large initial value

	// Try different block sizes
	blockSizes := []int{32, 64, 128, 256, 512, 1024}
	for _, blockSize := range blockSizes {
		// Skip if block size exceeds target limits
		if blockSize > target.MaxThreadsPerBlock {
			continue
		}

		params := &KernelLaunchParams{
			BlockSize: blockSize,
			GridSize:  kfe.calculateOptimalGridSize(pattern, blockSize, target),
			SharedMem: kfe.calculateSharedMemoryUsage(pattern, blockSize),
			StreamID:  0,
		}

		// Benchmark this configuration
		execTime, err := kfe.benchmarkKernelConfiguration(kernel, params, pattern)
		if err != nil {
			continue // Skip failed configurations
		}

		if execTime < bestTime {
			bestTime = execTime
			bestParams = params
		}
	}

	return bestParams, nil
}

// KernelLaunchParams represents optimized kernel launch parameters
type KernelLaunchParams struct {
	BlockSize int   `json:"block_size"`
	GridSize  int   `json:"grid_size"`
	SharedMem int64 `json:"shared_memory"`
	StreamID  int   `json:"stream_id"`
}

// GetMetrics returns current fusion engine metrics
func (kfe *KernelFusionEngine) GetMetrics() FusionMetrics {
	kfe.mutex.RLock()
	defer kfe.mutex.RUnlock()
	return kfe.metrics
}

// GetFusionPatterns returns all registered fusion patterns
func (kfe *KernelFusionEngine) GetFusionPatterns() map[string]*FusionPattern {
	kfe.mutex.RLock()
	defer kfe.mutex.RUnlock()

	patterns := make(map[string]*FusionPattern)
	for k, v := range kfe.patterns {
		patterns[k] = v
	}
	return patterns
}

// ClearCache clears the optimized kernel cache
func (kfe *KernelFusionEngine) ClearCache() {
	kfe.mutex.Lock()
	defer kfe.mutex.Unlock()
	kfe.optimizedCache = make(map[string]*CompiledKernel)
}

// Private helper methods

func (kfe *KernelFusionEngine) initializeBuiltinPatterns() {
	// Element-wise + Activation fusion pattern
	kfe.registerBuiltinPattern("elementwise_activation", []FusionOpType{FusionElementwise, FusionActivation})

	// Convolution + Activation fusion pattern
	kfe.registerBuiltinPattern("conv_activation", []FusionOpType{FusionConvolution, FusionActivation})

	// MatMul + Activation fusion pattern
	kfe.registerBuiltinPattern("matmul_activation", []FusionOpType{FusionMatMul, FusionActivation})

	// Element-wise chain fusion pattern
	kfe.registerBuiltinPattern("elementwise_chain", []FusionOpType{FusionElementwise, FusionElementwise, FusionElementwise})
}

func (kfe *KernelFusionEngine) registerBuiltinPattern(name string, opTypes []FusionOpType) {
	// This would register common fusion patterns for automatic detection
	// Implementation would include pattern matching logic
}

func (kfe *KernelFusionEngine) validateFusionOperations(operations []*FusionOperation) error {
	if len(operations) < 2 {
		return fmt.Errorf("need at least 2 operations for fusion")
	}

	if len(operations) > kfe.config.MaxFusionDepth {
		return fmt.Errorf("fusion depth %d exceeds maximum %d", len(operations), kfe.config.MaxFusionDepth)
	}

	// Check data type compatibility
	baseDataType := operations[0].DataType
	for i, op := range operations {
		if op.DataType != baseDataType {
			return fmt.Errorf("operation %d has incompatible data type %s, expected %s", i, op.DataType, baseDataType)
		}
	}

	// Check shape compatibility
	for i := 1; i < len(operations); i++ {
		prevOutput := operations[i-1].OutputShape
		currInput := operations[i].InputShapes[0]

		if !kfe.shapesCompatible(prevOutput, currInput) {
			return fmt.Errorf("operation %d input shape incompatible with operation %d output shape", i, i-1)
		}
	}

	return nil
}

func (kfe *KernelFusionEngine) canFuseOperations(operations []*FusionOperation) bool {
	// Check if operations can be fused based on type compatibility
	for i := 0; i < len(operations)-1; i++ {
		if !kfe.typesCanFuse(operations[i].Type, operations[i+1].Type) {
			return false
		}
	}
	return true
}

func (kfe *KernelFusionEngine) typesCanFuse(type1, type2 FusionOpType) bool {
	// Define fusion compatibility matrix
	fusionMatrix := map[FusionOpType][]FusionOpType{
		FusionElementwise: {FusionElementwise, FusionActivation, FusionReduction},
		FusionActivation:  {FusionElementwise, FusionActivation},
		FusionConvolution: {FusionActivation, FusionPooling},
		FusionMatMul:      {FusionActivation, FusionElementwise},
		FusionPooling:     {FusionActivation},
		FusionReduction:   {FusionActivation},
	}

	compatibleTypes, exists := fusionMatrix[type1]
	if !exists {
		return false
	}

	for _, compatibleType := range compatibleTypes {
		if compatibleType == type2 {
			return true
		}
	}
	return false
}

func (kfe *KernelFusionEngine) shapesCompatible(shape1, shape2 TensorShape) bool {
	if len(shape1) != len(shape2) {
		return false
	}
	for i, dim := range shape1 {
		if dim != shape2[i] {
			return false
		}
	}
	return true
}

func (kfe *KernelFusionEngine) generatePatternID(operations []*FusionOperation) string {
	var parts []string
	for _, op := range operations {
		parts = append(parts, string(op.Type))
	}
	return strings.Join(parts, "_")
}

func (kfe *KernelFusionEngine) generatePatternName(operations []*FusionOperation) string {
	var parts []string
	for _, op := range operations {
		parts = append(parts, op.Name)
	}
	return "Fused_" + strings.Join(parts, "_")
}

func (kfe *KernelFusionEngine) extractInputs(operations []*FusionOperation) []string {
	// Extract unique input identifiers from the first operation
	return []string{operations[0].ID + "_input"}
}

func (kfe *KernelFusionEngine) extractOutputs(operations []*FusionOperation) []string {
	// Extract output identifier from the last operation
	lastOp := operations[len(operations)-1]
	return []string{lastOp.ID + "_output"}
}

func (kfe *KernelFusionEngine) calculateSeparateCost(operations []*FusionOperation) float64 {
	totalCost := 0.0
	for _, op := range operations {
		totalCost += op.Cost
	}
	// Add memory transfer overhead between operations
	totalCost += float64(len(operations)-1) * 0.1 // Assume 0.1 cost per memory transfer
	return totalCost
}

func (kfe *KernelFusionEngine) calculateFusionCost(operations []*FusionOperation) float64 {
	// Fused operations have computational cost but reduced memory overhead
	totalCost := 0.0
	for _, op := range operations {
		totalCost += op.Cost * 0.9 // 10% reduction due to fusion optimizations
	}
	// Single memory transfer for fused operation
	totalCost += 0.05
	return totalCost
}

func (kfe *KernelFusionEngine) calculateMemorySavings(operations []*FusionOperation) int64 {
	// Calculate intermediate tensor memory that can be saved
	savings := int64(0)
	for i := 0; i < len(operations)-1; i++ {
		// Each intermediate result saves memory
		elements := operations[i].OutputShape.NumElements()
		elementSize := int64(operations[i].DataType.Size())
		savings += elements * elementSize
	}
	return savings
}

func (kfe *KernelFusionEngine) generateKernelSource(pattern *FusionPattern, target CompilationTarget) (*KernelSource, error) {
	// Generate optimized kernel source code for the fusion pattern
	sourceCode := kfe.generateCUDAKernelSource(pattern, target)

	source := &KernelSource{
		ID:         pattern.ID + "_kernel",
		Name:       pattern.Name + "_Kernel",
		Language:   KernelCUDA,
		Type:       KernelTypeCustom,
		Source:     sourceCode,
		Headers:    make(map[string]string),
		Macros:     make(map[string]string),
		Parameters: kfe.generateKernelParameters(pattern),
		Metadata: map[string]interface{}{
			"fusion_pattern": pattern.ID,
			"operations":     len(pattern.Operations),
			"speedup":        pattern.Speedup,
		},
		CreatedAt:  time.Now(),
		ModifiedAt: time.Now(),
	}

	return source, nil
}

func (kfe *KernelFusionEngine) generateCUDAKernelSource(pattern *FusionPattern, target CompilationTarget) string {
	var builder strings.Builder

	// Kernel header
	builder.WriteString("extern \"C\" __global__ void ")
	builder.WriteString(pattern.Name)
	builder.WriteString("_kernel(")

	// Generate parameter list
	params := kfe.generateKernelParameters(pattern)
	for i, param := range params {
		if i > 0 {
			builder.WriteString(", ")
		}
		builder.WriteString(fmt.Sprintf("%s %s", param.Type, param.Name))
	}
	builder.WriteString(") {\n")

	// Thread indexing
	builder.WriteString("    int idx = blockIdx.x * blockDim.x + threadIdx.x;\n")
	builder.WriteString("    int stride = blockDim.x * gridDim.x;\n")
	builder.WriteString(fmt.Sprintf("    int n = %d;\n", pattern.Operations[0].OutputShape.NumElements()))

	// Main computation loop
	builder.WriteString("    for (int i = idx; i < n; i += stride) {\n")

	// Generate fused operation code
	builder.WriteString("        float temp = input[i];\n")
	for _, op := range pattern.Operations {
		switch op.Type {
		case FusionElementwise:
			builder.WriteString(kfe.generateElementwiseCode(op))
		case FusionActivation:
			builder.WriteString(kfe.generateActivationCode(op))
		}
	}
	builder.WriteString("        output[i] = temp;\n")

	builder.WriteString("    }\n")
	builder.WriteString("}\n")

	return builder.String()
}

func (kfe *KernelFusionEngine) generateElementwiseCode(op *FusionOperation) string {
	switch op.Name {
	case "Add":
		return "        temp = temp + other[i];\n"
	case "Multiply":
		return "        temp = temp * other[i];\n"
	case "Scale":
		if scalar, ok := op.Parameters["scalar"].(float64); ok {
			return fmt.Sprintf("        temp = temp * %f;\n", scalar)
		}
		return "        temp = temp * scalar;\n"
	default:
		return "        // Unknown elementwise operation\n"
	}
}

func (kfe *KernelFusionEngine) generateActivationCode(op *FusionOperation) string {
	switch op.Name {
	case "ReLU":
		return "        temp = fmaxf(0.0f, temp);\n"
	case "Sigmoid":
		return "        temp = 1.0f / (1.0f + expf(-temp));\n"
	case "Tanh":
		return "        temp = tanhf(temp);\n"
	default:
		return "        // Unknown activation function\n"
	}
}

func (kfe *KernelFusionEngine) generateKernelParameters(pattern *FusionPattern) []KernelParameter {
	params := []KernelParameter{
		{
			Name:        "input",
			Type:        "float*",
			Size:        8,
			IsPointer:   true,
			IsConstant:  false,
			Description: "Input tensor data",
		},
		{
			Name:        "output",
			Type:        "float*",
			Size:        8,
			IsPointer:   true,
			IsConstant:  false,
			Description: "Output tensor data",
		},
	}

	// Add additional parameters based on operations
	for _, op := range pattern.Operations {
		if op.Type == FusionElementwise && op.Name != "Scale" {
			params = append(params, KernelParameter{
				Name:        "other",
				Type:        "float*",
				Size:        8,
				IsPointer:   true,
				IsConstant:  true,
				Description: "Second operand for elementwise operation",
			})
			break
		}
	}

	return params
}

func (kfe *KernelFusionEngine) autoTuneCompilationOptions(options CompilationOptions, pattern *FusionPattern, target CompilationTarget) CompilationOptions {
	// Auto-tune compilation options based on pattern characteristics
	tuned := options

	// Adjust optimization level based on pattern complexity
	if len(pattern.Operations) > 3 {
		tuned.OptimizationLevel = OptimizationFull
	}

	// Add pattern-specific compiler flags
	tuned.CompilerFlags = append(tuned.CompilerFlags, "--use_fast_math")

	if pattern.Operations[0].Type == FusionElementwise {
		tuned.CompilerFlags = append(tuned.CompilerFlags, "--ftz=true") // Flush denormals to zero
	}

	return tuned
}

func (kfe *KernelFusionEngine) getDefaultLaunchParams(pattern *FusionPattern, target CompilationTarget) *KernelLaunchParams {
	// Get default block size hint
	blockSize := 256 // Default
	if hint, exists := kfe.config.BlockSizeHints[string(pattern.Operations[0].Type)]; exists {
		blockSize = hint
	}

	// Ensure block size doesn't exceed target limits
	if blockSize > target.MaxThreadsPerBlock {
		blockSize = target.MaxThreadsPerBlock
	}

	return &KernelLaunchParams{
		BlockSize: blockSize,
		GridSize:  kfe.calculateOptimalGridSize(pattern, blockSize, target),
		SharedMem: kfe.calculateSharedMemoryUsage(pattern, blockSize),
		StreamID:  0,
	}
}

func (kfe *KernelFusionEngine) calculateOptimalGridSize(pattern *FusionPattern, blockSize int, target CompilationTarget) int {
	numElements := pattern.Operations[0].OutputShape.NumElements()
	gridSize := int((numElements + int64(blockSize) - 1) / int64(blockSize))

	// Limit grid size to target maximum
	maxGridSize := 65535 // Common CUDA limit
	if hint, exists := kfe.config.GridSizeHints[string(pattern.Operations[0].Type)]; exists {
		maxGridSize = hint
	}

	if gridSize > maxGridSize {
		gridSize = maxGridSize
	}

	return gridSize
}

func (kfe *KernelFusionEngine) calculateSharedMemoryUsage(pattern *FusionPattern, blockSize int) int64 {
	// Calculate shared memory requirements based on pattern
	baseUsage := int64(blockSize * 4) // 4 bytes per thread for basic operations

	// Add extra shared memory for complex operations
	for _, op := range pattern.Operations {
		switch op.Type {
		case FusionReduction:
			baseUsage += int64(blockSize * 4) // Extra space for reduction
		case FusionConvolution:
			baseUsage += int64(blockSize * 8) // Extra space for convolution tiles
		}
	}

	// Check against limits
	if limit, exists := kfe.config.SharedMemoryLimits["default"]; exists && baseUsage > limit {
		baseUsage = limit
	}

	return baseUsage
}

func (kfe *KernelFusionEngine) benchmarkKernelConfiguration(kernel *CompiledKernel, params *KernelLaunchParams, pattern *FusionPattern) (float64, error) {
	// This would perform actual kernel benchmarking
	// For now, return a simulated execution time based on parameters
	baseTime := 1000.0 // microseconds

	// Adjust based on block size efficiency
	efficiency := float64(params.BlockSize) / 256.0
	if efficiency > 1.0 {
		efficiency = 2.0 - efficiency // Penalty for oversized blocks
	}

	return baseTime / efficiency, nil
}
