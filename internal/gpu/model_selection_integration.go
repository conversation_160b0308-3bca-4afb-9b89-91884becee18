package gpu

import (
	"fmt"
	"log"
	"sync"
	"time"
)

// ModelSelectionIntegration integrates automated model selection with the workload prediction system
type ModelSelectionIntegration struct {
	optimizer            *ModelSelectionOptimizer
	predictor            *WorkloadPredictor
	featureEngineer      *FeatureEngineer
	logger               *log.Logger
	config               ModelSelectionConfig
	lastOptimization     time.Time
	optimizationInterval time.Duration
}

// NewModelSelectionIntegration creates a new integration instance
func NewModelSelectionIntegration(
	predictor *WorkloadPredictor,
	logger *log.Logger,
) *ModelSelectionIntegration {
	featureEngineer := NewFeatureEngineer()
	config := DefaultModelSelectionConfig()

	// Adjust config for production use
	config.CVFolds = 3
	config.MaxIterations = 20
	config.Timeout = time.Minute * 10
	config.EnableBayesianOpt = false      // Disable for stability
	config.EnableEnsemble = false         // Disable for simplicity
	config.EnableFeatureSelection = false // Disable for performance

	optimizer := NewModelSelectionOptimizer(config, predictor, featureEngineer, logger)

	return &ModelSelectionIntegration{
		optimizer:            optimizer,
		predictor:            predictor,
		featureEngineer:      featureEngineer,
		logger:               logger,
		config:               config,
		optimizationInterval: time.Hour * 24, // Daily optimization
	}
}

// OptimizeModelSelection performs automated model selection and hyperparameter tuning
func (msi *ModelSelectionIntegration) OptimizeModelSelection(data []WorkloadDataPoint) (*OptimizationResult, error) {
	if len(data) < msi.config.CVFolds*10 {
		return nil, fmt.Errorf("insufficient data for optimization: need at least %d points, got %d",
			msi.config.CVFolds*10, len(data))
	}

	msi.logger.Printf("Starting model selection optimization with %d data points", len(data))

	// Perform optimization
	result, err := msi.optimizer.OptimizeModels(data, "avg_utilization")
	if err != nil {
		return nil, fmt.Errorf("optimization failed: %w", err)
	}

	msi.lastOptimization = time.Now()

	// Log results
	msi.logger.Printf("Optimization completed successfully:")
	msi.logger.Printf("  Best model: %s (score: %.4f)", result.BestModel.ModelType, result.BestModel.MeanScore)
	msi.logger.Printf("  Total candidates evaluated: %d", len(result.AllCandidates))
	msi.logger.Printf("  Optimization time: %v", result.TotalTime)
	msi.logger.Printf("  Iterations: %d", result.Iterations)

	if result.ConvergedEarly {
		msi.logger.Printf("  Early convergence detected")
	}

	return result, nil
}

// ShouldRunOptimization checks if optimization should be run based on time and conditions
func (msi *ModelSelectionIntegration) ShouldRunOptimization() bool {
	return time.Since(msi.lastOptimization) >= msi.optimizationInterval
}

// GetOptimizationStatus returns the current optimization status
func (msi *ModelSelectionIntegration) GetOptimizationStatus() ModelSelectionStatus {
	return ModelSelectionStatus{
		LastOptimization:     msi.lastOptimization,
		NextOptimization:     msi.lastOptimization.Add(msi.optimizationInterval),
		OptimizationInterval: msi.optimizationInterval,
		IsOptimizationDue:    msi.ShouldRunOptimization(),
		Config:               msi.config,
	}
}

// UpdateConfig updates the optimization configuration
func (msi *ModelSelectionIntegration) UpdateConfig(config ModelSelectionConfig) {
	msi.config = config
	msi.optimizer = NewModelSelectionOptimizer(config, msi.predictor, msi.featureEngineer, msi.logger)
}

// ModelSelectionStatus represents the current status of model selection
type ModelSelectionStatus struct {
	LastOptimization     time.Time            `json:"last_optimization"`
	NextOptimization     time.Time            `json:"next_optimization"`
	OptimizationInterval time.Duration        `json:"optimization_interval"`
	IsOptimizationDue    bool                 `json:"is_optimization_due"`
	Config               ModelSelectionConfig `json:"config"`
}

// PerformPeriodicOptimization runs optimization if due and returns results
func (msi *ModelSelectionIntegration) PerformPeriodicOptimization() (*OptimizationResult, error) {
	if !msi.ShouldRunOptimization() {
		return nil, fmt.Errorf("optimization not due yet")
	}

	// Get historical data from predictor
	historicalData := msi.predictor.GetHistoricalData()
	if len(historicalData) == 0 {
		return nil, fmt.Errorf("no historical data available for optimization")
	}

	return msi.OptimizeModelSelection(historicalData)
}

// GetModelRecommendations provides recommendations based on optimization results
func (msi *ModelSelectionIntegration) GetModelRecommendations(result *OptimizationResult) []ModelRecommendation {
	var recommendations []ModelRecommendation

	if result == nil || result.BestModel == nil {
		return recommendations
	}

	// Primary recommendation: use the best model
	recommendations = append(recommendations, ModelRecommendation{
		Type:       "primary",
		ModelType:  result.BestModel.ModelType,
		Confidence: result.BestModel.MeanScore,
		Reason:     fmt.Sprintf("Best performing model with %.2f%% accuracy", result.BestModel.MeanScore*100),
		Parameters: result.BestModel.Hyperparameters,
		Priority:   1,
	})

	// Secondary recommendations from top models
	for i, model := range result.TopModels {
		if i >= 3 || model.ID == result.BestModel.ID {
			break // Limit to top 3 alternatives
		}

		recommendations = append(recommendations, ModelRecommendation{
			Type:       "alternative",
			ModelType:  model.ModelType,
			Confidence: model.MeanScore,
			Reason:     fmt.Sprintf("Alternative option with %.2f%% accuracy", model.MeanScore*100),
			Parameters: model.Hyperparameters,
			Priority:   i + 2,
		})
	}

	// Ensemble recommendation if available
	for _, model := range result.TopModels {
		if model.IsEnsemble {
			recommendations = append(recommendations, ModelRecommendation{
				Type:       "ensemble",
				ModelType:  model.ModelType,
				Confidence: model.MeanScore,
				Reason:     fmt.Sprintf("Ensemble model combining multiple approaches with %.2f%% accuracy", model.MeanScore*100),
				Parameters: model.Hyperparameters,
				Priority:   len(recommendations) + 1,
			})
			break
		}
	}

	return recommendations
}

// ModelRecommendation represents a model recommendation
type ModelRecommendation struct {
	Type       string                 `json:"type"` // "primary", "alternative", "ensemble"
	ModelType  string                 `json:"model_type"`
	Confidence float64                `json:"confidence"`
	Reason     string                 `json:"reason"`
	Parameters map[string]interface{} `json:"parameters"`
	Priority   int                    `json:"priority"`
}

// EvaluateCurrentModel evaluates the current model's performance
func (msi *ModelSelectionIntegration) EvaluateCurrentModel(recentData []WorkloadDataPoint) ModelEvaluation {
	if len(recentData) == 0 {
		return ModelEvaluation{
			IsValid: false,
			Error:   "no recent data available for evaluation",
		}
	}

	// Simple evaluation using recent predictions vs actuals
	var predictions []float64
	var actuals []float64

	for _, point := range recentData {
		// Get prediction from current predictor
		predictionResult, err := msi.predictor.GetPrediction(time.Minute * 5)
		if err != nil {
			continue
		}

		prediction := float64(predictionResult.PredictedQueue)
		actual := float64(point.QueueLength)

		predictions = append(predictions, prediction)
		actuals = append(actuals, actual)
	}

	if len(predictions) == 0 {
		return ModelEvaluation{
			IsValid: false,
			Error:   "no valid predictions available",
		}
	}

	// Calculate metrics
	accuracy := msi.calculateAccuracy(predictions, actuals)
	rmse := msi.calculateRMSE(predictions, actuals)
	mae := msi.calculateMAE(predictions, actuals)

	return ModelEvaluation{
		IsValid:    true,
		Accuracy:   accuracy,
		RMSE:       rmse,
		MAE:        mae,
		DataPoints: len(predictions),
		Timestamp:  time.Now(),
	}
}

// ModelEvaluation represents the evaluation of a model's performance
type ModelEvaluation struct {
	IsValid    bool      `json:"is_valid"`
	Accuracy   float64   `json:"accuracy"`
	RMSE       float64   `json:"rmse"`
	MAE        float64   `json:"mae"`
	DataPoints int       `json:"data_points"`
	Timestamp  time.Time `json:"timestamp"`
	Error      string    `json:"error,omitempty"`
}

// Helper methods for metric calculations (simplified versions)
func (msi *ModelSelectionIntegration) calculateAccuracy(predictions, actuals []float64) float64 {
	if len(predictions) == 0 {
		return 0.0
	}

	var totalError float64
	validCount := 0

	for i := range predictions {
		if actuals[i] != 0 {
			error := (predictions[i] - actuals[i]) / actuals[i]
			if error < 0 {
				error = -error
			}
			totalError += error
			validCount++
		}
	}

	if validCount == 0 {
		return 0.0
	}

	mape := totalError / float64(validCount)
	accuracy := 1.0 - mape
	if accuracy < 0 {
		accuracy = 0
	}
	if accuracy > 1 {
		accuracy = 1
	}

	return accuracy
}

func (msi *ModelSelectionIntegration) calculateRMSE(predictions, actuals []float64) float64 {
	if len(predictions) == 0 {
		return 0.0
	}

	var sumSquaredError float64
	for i := range predictions {
		error := predictions[i] - actuals[i]
		sumSquaredError += error * error
	}

	return sumSquaredError / float64(len(predictions)) // Simplified: not taking square root
}

func (msi *ModelSelectionIntegration) calculateMAE(predictions, actuals []float64) float64 {
	if len(predictions) == 0 {
		return 0.0
	}

	var sumAbsError float64
	for i := range predictions {
		error := predictions[i] - actuals[i]
		if error < 0 {
			error = -error
		}
		sumAbsError += error
	}

	return sumAbsError / float64(len(predictions))
}

// AutoOptimizationManager manages automatic model optimization
type AutoOptimizationManager struct {
	integration *ModelSelectionIntegration
	isRunning   bool
	stopChan    chan struct{}
	logger      *log.Logger
	mu          sync.Mutex // Add mutex for thread safety
}

// NewAutoOptimizationManager creates a new auto optimization manager
func NewAutoOptimizationManager(integration *ModelSelectionIntegration, logger *log.Logger) *AutoOptimizationManager {
	return &AutoOptimizationManager{
		integration: integration,
		logger:      logger,
		stopChan:    make(chan struct{}),
	}
}

// Start begins automatic optimization
func (aom *AutoOptimizationManager) Start() {
	aom.mu.Lock()
	defer aom.mu.Unlock()

	if aom.isRunning {
		return
	}

	aom.isRunning = true
	// Create new stop channel if needed
	if aom.stopChan == nil {
		aom.stopChan = make(chan struct{})
	}
	go aom.optimizationLoop()
	aom.logger.Println("Auto optimization manager started")
}

// Stop stops automatic optimization
func (aom *AutoOptimizationManager) Stop() {
	aom.mu.Lock()
	defer aom.mu.Unlock()

	if !aom.isRunning {
		return
	}

	aom.isRunning = false
	if aom.stopChan != nil {
		close(aom.stopChan)
		aom.stopChan = nil // Set to nil to prevent double close
	}
	aom.logger.Println("Auto optimization manager stopped")
}

// optimizationLoop runs the automatic optimization loop
func (aom *AutoOptimizationManager) optimizationLoop() {
	ticker := time.NewTicker(time.Hour) // Check every hour
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if aom.integration.ShouldRunOptimization() {
				aom.logger.Println("Running periodic model optimization...")

				result, err := aom.integration.PerformPeriodicOptimization()
				if err != nil {
					aom.logger.Printf("Periodic optimization failed: %v", err)
				} else {
					aom.logger.Printf("Periodic optimization completed successfully")

					// Get recommendations
					recommendations := aom.integration.GetModelRecommendations(result)
					for _, rec := range recommendations {
						aom.logger.Printf("  %s: %s (%.2f%% confidence)",
							rec.Type, rec.ModelType, rec.Confidence*100)
					}
				}
			}

		case <-aom.stopChan:
			return
		}
	}
}

// IsRunning returns whether the manager is running
func (aom *AutoOptimizationManager) IsRunning() bool {
	aom.mu.Lock()
	defer aom.mu.Unlock()
	return aom.isRunning
}
