//go:build darwin && metal

package metal

/*
#cgo LDFLAGS: -framework Metal -framework Foundation
#include <Metal/Metal.h>
#include <Foundation/Foundation.h>

// Helper functions for Metal command buffer management
static id<MTLCommandBuffer> createCommandBuffer(id<MTLCommandQueue> queue) {
    if (queue == nil) return nil;
    return [queue commandBuffer];
}

static void commitCommandBuffer(id<MTLCommandBuffer> buffer) {
    if (buffer != nil) {
        [buffer commit];
    }
}

static void waitUntilCompleted(id<MTLCommandBuffer> buffer) {
    if (buffer != nil) {
        [buffer waitUntilCompleted];
    }
}

static bool isCommandBufferCompleted(id<MTLCommandBuffer> buffer) {
    if (buffer == nil) return true;
    return [buffer status] == MTLCommandBufferStatusCompleted;
}

static void releaseCommandBuffer(id<MTLCommandBuffer> buffer) {
    if (buffer != nil) {
        [buffer release];
    }
}
*/
import "C"

import (
	"fmt"
	"sync"
	"sync/atomic"

	"neuralmetergo/internal/core"
)

// MetalStreamManager handles Metal command buffer streams
type MetalStreamManager struct {
	device       C.id
	commandQueue C.id
	streams      map[string]*MetalStream
	streamCount  int64
	mutex        sync.RWMutex
}

// NewMetalStreamManager creates a new Metal stream manager
func NewMetalStreamManager(device C.id, commandQueue C.id) *MetalStreamManager {
	return &MetalStreamManager{
		device:       device,
		commandQueue: commandQueue,
		streams:      make(map[string]*MetalStream),
		streamCount:  0,
	}
}

// CreateStream creates a new Metal command buffer stream
func (m *MetalStreamManager) CreateStream() (core.GPUStream, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.commandQueue == nil {
		return nil, core.NewGPUError(
			core.ErrorStreamCreationFailed,
			core.PlatformMetal,
			-1,
			"No Metal command queue available",
			nil,
		)
	}

	commandBuffer := C.createCommandBuffer(m.commandQueue)
	if commandBuffer == nil {
		return nil, core.NewGPUError(
			core.ErrorStreamCreationFailed,
			core.PlatformMetal,
			-1,
			"Failed to create Metal command buffer",
			nil,
		)
	}

	streamID := fmt.Sprintf("metal_stream_%d", atomic.AddInt64(&m.streamCount, 1))

	stream := &MetalStream{
		id:            streamID,
		commandBuffer: commandBuffer,
		active:        true,
		priority:      0, // Default priority
	}

	m.streams[streamID] = stream

	return stream, nil
}

// DestroyStream destroys a Metal stream
func (m *MetalStreamManager) DestroyStream(stream core.GPUStream) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	metalStream, ok := stream.(*MetalStream)
	if !ok {
		return core.NewGPUError(
			core.ErrorInvalidParameter,
			core.PlatformMetal,
			-1,
			"Invalid Metal stream object",
			nil,
		)
	}

	streamID := metalStream.GetID()
	if existingStream, exists := m.streams[streamID]; exists {
		// Wait for completion before destroying
		if existingStream.commandBuffer != nil {
			C.waitUntilCompleted(existingStream.commandBuffer)
			C.releaseCommandBuffer(existingStream.commandBuffer)
			existingStream.commandBuffer = nil
		}
		existingStream.active = false
		delete(m.streams, streamID)
	}

	return nil
}

// SynchronizeStream synchronizes a Metal stream
func (m *MetalStreamManager) SynchronizeStream(stream core.GPUStream) error {
	metalStream, ok := stream.(*MetalStream)
	if !ok {
		return core.NewGPUError(
			core.ErrorInvalidParameter,
			core.PlatformMetal,
			-1,
			"Invalid Metal stream object",
			nil,
		)
	}

	if !metalStream.IsActive() {
		return core.NewGPUError(
			core.ErrorStreamDestroyed,
			core.PlatformMetal,
			-1,
			"Cannot synchronize destroyed Metal stream",
			nil,
		)
	}

	if metalStream.commandBuffer != nil {
		C.waitUntilCompleted(metalStream.commandBuffer)
	}

	return nil
}

// Shutdown destroys all streams and cleans up resources
func (m *MetalStreamManager) Shutdown() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for _, stream := range m.streams {
		if stream.commandBuffer != nil {
			C.waitUntilCompleted(stream.commandBuffer)
			C.releaseCommandBuffer(stream.commandBuffer)
			stream.commandBuffer = nil
		}
		stream.active = false
	}

	m.streams = make(map[string]*MetalStream)
}

// MetalStream implements the core.GPUStream interface for Metal
type MetalStream struct {
	id            string
	commandBuffer C.id
	active        bool
	priority      int
	mutex         sync.RWMutex
}

func (s *MetalStream) GetID() string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.id
}

func (s *MetalStream) IsActive() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.active
}

func (s *MetalStream) GetPriority() int {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.priority
}

// GetCommandBuffer returns the underlying Metal command buffer for platform-specific operations
func (s *MetalStream) GetCommandBuffer() C.id {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.commandBuffer
}

// Commit commits the command buffer for execution
func (s *MetalStream) Commit() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.active {
		return core.NewGPUError(
			core.ErrorStreamDestroyed,
			core.PlatformMetal,
			-1,
			"Cannot commit destroyed Metal stream",
			nil,
		)
	}

	if s.commandBuffer != nil {
		C.commitCommandBuffer(s.commandBuffer)
	}

	return nil
}

// IsCompleted checks if the command buffer has completed execution
func (s *MetalStream) IsCompleted() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if s.commandBuffer == nil {
		return true
	}

	return bool(C.isCommandBufferCompleted(s.commandBuffer))
}
