//go:build darwin && metal

package metal

import (
	"context"
	"runtime"

	"neuralmetergo/internal/core"
)

// SimpleMetalDetector implements GPU detection for Metal on macOS (simplified version)
type SimpleMetalDetector struct {
	devices []core.GPUDevice
}

// NewSimpleMetalDetector creates a new simplified Metal GPU detector
func NewSimpleMetalDetector() *SimpleMetalDetector {
	return &SimpleMetalDetector{
		devices: make([]core.GPUDevice, 0),
	}
}

// DetectDevices discovers available Metal devices (simplified implementation)
func (d *SimpleMetalDetector) DetectDevices(ctx context.Context) ([]core.GPUDevice, error) {
	// Simplified detection - on macOS, assume we have at least one Metal device
	if runtime.GOOS != "darwin" {
		return nil, core.NewGPUError(
			core.ErrorPlatformNotSupported,
			core.PlatformMetal,
			-1,
			"Metal is only supported on macOS",
			nil,
		)
	}

	// Create a simulated Metal device for demonstration
	device := &SimpleMetalDevice{
		id:         0,
		name:       "Apple GPU (Simulated)",
		memorySize: 8 * 1024 * 1024 * 1024, // 8GB simulated
		capability: "Apple7",               // Simulated capability
		available:  true,
	}

	devices := []core.GPUDevice{device}
	d.devices = devices

	return devices, nil
}

// SimpleMetalDevice implements the GPUDevice interface for Metal (simplified)
type SimpleMetalDevice struct {
	id         int
	name       string
	memorySize int64
	capability string
	available  bool
}

func (d *SimpleMetalDevice) GetID() int {
	return d.id
}

func (d *SimpleMetalDevice) GetName() string {
	return d.name
}

func (d *SimpleMetalDevice) GetMemorySize() int64 {
	return d.memorySize
}

func (d *SimpleMetalDevice) GetComputeCapability() string {
	return d.capability
}

func (d *SimpleMetalDevice) GetPlatform() core.GPUPlatform {
	return core.PlatformMetal
}

func (d *SimpleMetalDevice) IsAvailable() bool {
	return d.available
}
