//go:build darwin && metal

package metal

/*
#cgo LDFLAGS: -framework Metal -framework Foundation
#include <Metal/Metal.h>
#include <Foundation/Foundation.h>

// Helper functions for Metal device detection
static int getDeviceCount() {
    NSArray<id<MTLDevice>> *devices = MTLCopyAllDevices();
    int count = (int)[devices count];
    [devices release];
    return count;
}

static id<MTLDevice> getDeviceAtIndex(int index) {
    NSArray<id<MTLDevice>> *devices = MTLCopyAllDevices();
    if (index >= [devices count]) {
        [devices release];
        return nil;
    }
    id<MTLDevice> device = [devices objectAtIndex:index];
    [device retain];
    [devices release];
    return device;
}

static const char* getDeviceName(id<MTLDevice> device) {
    if (device == nil) return "";
    NSString *name = [device name];
    return [name UTF8String];
}

static uint64_t getDeviceMemorySize(id<MTLDevice> device) {
    if (device == nil) return 0;
    if (@available(macOS 10.13, *)) {
        return [device recommendedMaxWorkingSetSize];
    }
    return 0; // Fallback for older macOS versions
}

static bool deviceSupportsFamily(id<MTLDevice> device, int family) {
    if (device == nil) return false;
    if (@available(macOS 10.15, *)) {
        return [device supportsFamily:(MTLGPUFamily)family];
    }
    return false;
}
*/
import "C"

import (
	"context"

	"neuralmetergo/internal/core"
)

// MetalDetector implements GPU detection for Metal on macOS
type MetalDetector struct {
	devices []core.GPUDevice
}

// NewMetalDetector creates a new Metal GPU detector
func NewMetalDetector() *MetalDetector {
	return &MetalDetector{
		devices: make([]core.GPUDevice, 0),
	}
}

// DetectDevices discovers all available Metal devices
func (d *MetalDetector) DetectDevices(ctx context.Context) ([]core.GPUDevice, error) {
	deviceCount := int(C.getDeviceCount())
	if deviceCount == 0 {
		return nil, core.NewGPUError(
			core.ErrorDeviceNotFound,
			core.PlatformMetal,
			-1,
			"No Metal devices found on this system",
			nil,
		)
	}

	devices := make([]core.GPUDevice, 0, deviceCount)

	for i := 0; i < deviceCount; i++ {
		metalDevice := C.getDeviceAtIndex(C.int(i))
		if metalDevice == nil {
			continue
		}

		device := &MetalDevice{
			id:          i,
			name:        C.GoString(C.getDeviceName(metalDevice)),
			memorySize:  int64(C.getDeviceMemorySize(metalDevice)),
			metalDevice: metalDevice,
		}

		// Check device capabilities
		device.capability = d.getDeviceCapability(metalDevice)

		devices = append(devices, device)
	}

	d.devices = devices
	return devices, nil
}

// getDeviceCapability determines the Metal GPU family support
func (d *MetalDetector) getDeviceCapability(device C.id) string {
	// Check for different Metal GPU families
	if bool(C.deviceSupportsFamily(device, 7)) { // MTLGPUFamilyApple7
		return "Apple7"
	} else if bool(C.deviceSupportsFamily(device, 6)) { // MTLGPUFamilyApple6
		return "Apple6"
	} else if bool(C.deviceSupportsFamily(device, 5)) { // MTLGPUFamilyApple5
		return "Apple5"
	} else if bool(C.deviceSupportsFamily(device, 4)) { // MTLGPUFamilyApple4
		return "Apple4"
	} else if bool(C.deviceSupportsFamily(device, 3)) { // MTLGPUFamilyApple3
		return "Apple3"
	} else if bool(C.deviceSupportsFamily(device, 2)) { // MTLGPUFamilyApple2
		return "Apple2"
	} else if bool(C.deviceSupportsFamily(device, 1)) { // MTLGPUFamilyApple1
		return "Apple1"
	}
	return "Unknown"
}

// MetalDevice implements the GPUDevice interface for Metal
type MetalDevice struct {
	id          int
	name        string
	memorySize  int64
	capability  string
	metalDevice C.id
}

func (d *MetalDevice) GetID() int {
	return d.id
}

func (d *MetalDevice) GetName() string {
	return d.name
}

func (d *MetalDevice) GetMemorySize() int64 {
	return d.memorySize
}

func (d *MetalDevice) GetComputeCapability() string {
	return d.capability
}

func (d *MetalDevice) GetPlatform() core.GPUPlatform {
	return core.PlatformMetal
}

func (d *MetalDevice) IsAvailable() bool {
	return d.metalDevice != nil
}

// GetMetalDevice returns the underlying Metal device for platform-specific operations
func (d *MetalDevice) GetMetalDevice() C.id {
	return d.metalDevice
}

// Cleanup releases the Metal device reference
func (d *MetalDevice) Cleanup() {
	if d.metalDevice != nil {
		// Release the Metal device reference
		// Note: In a real implementation, we'd need proper memory management
		d.metalDevice = nil
	}
}
