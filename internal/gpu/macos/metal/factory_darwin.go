//go:build darwin

package metal

import (
	"neuralmetergo/internal/core"
)

// Factory implements core.GPUProviderFactory for Metal on macOS
type Factory struct{}

// NewFactory creates a new Metal provider factory
func NewFactory() *Factory {
	return &Factory{}
}

// CreateProvider creates a Metal GPU provider
func (f *Factory) CreateProvider() (*Provider, error) {
	return NewProvider(), nil
}

// Provider implements core.GPUProvider for Metal
type Provider struct {
	devices []core.GPUDevice
}

// NewProvider creates a new Metal GPU provider
func NewProvider() *Provider {
	return &Provider{
		devices: make([]core.GPUDevice, 0),
	}
}

// Initialize initializes the Metal provider
func (p *Provider) Initialize() error {
	// Simulate Metal device detection for macOS CLI
	device := &Device{
		id:         0,
		name:       "Apple GPU",
		deviceType: "integrated",
		available:  true,
	}

	p.devices = []core.GPUDevice{device}
	return nil
}

// GetDevices returns available Metal devices
func (p *Provider) GetDevices() ([]core.GPUDevice, error) {
	return p.devices, nil
}

// Cleanup cleans up Metal resources
func (p *Provider) Cleanup() error {
	p.devices = nil
	return nil
}

// Device implements core.GPUDevice for Metal
type Device struct {
	id         int
	name       string
	deviceType string
	available  bool
}

// GetID returns the device ID
func (d *Device) GetID() int {
	return d.id
}

// GetName returns the device name
func (d *Device) GetName() string {
	return d.name
}

// GetType returns the device type
func (d *Device) GetType() string {
	return d.deviceType
}

// GetComputeCapability returns the compute capability
func (d *Device) GetComputeCapability() string {
	return "Apple7" // Simulated Metal compute capability
}

// GetMemorySize returns the device memory size
func (d *Device) GetMemorySize() int64 {
	return 8 * 1024 * 1024 * 1024 // 8GB
}

// GetPlatform returns the GPU platform
func (d *Device) GetPlatform() core.GPUPlatform {
	return core.PlatformMetal
}

// IsAvailable returns whether the device is available
func (d *Device) IsAvailable() bool {
	return d.available
}

// GetMemory returns device memory information
func (d *Device) GetMemory() (core.GPUMemory, error) {
	return &Memory{
		size:    8 * 1024 * 1024 * 1024, // 8GB
		address: 0x1000000,              // Simulated address
	}, nil
}

// GetMaxThreadsPerGroup returns max threads per group
func (d *Device) GetMaxThreadsPerGroup() int {
	return 1024
}

// GetMaxBufferLength returns max buffer length
func (d *Device) GetMaxBufferLength() int64 {
	return 1024 * 1024 * 1024 // 1GB
}

// SupportsNonUniformThreadGroups returns whether non-uniform thread groups are supported
func (d *Device) SupportsNonUniformThreadGroups() bool {
	return true
}

// Memory implements core.GPUMemory for Metal
type Memory struct {
	size    int64
	address uintptr
}

// GetSize returns memory size
func (m *Memory) GetSize() int64 {
	return m.size
}

// GetAddress returns memory address
func (m *Memory) GetAddress() uintptr {
	return m.address
}

// IsValid returns whether the memory is valid
func (m *Memory) IsValid() bool {
	return m.address != 0 && m.size > 0
}
