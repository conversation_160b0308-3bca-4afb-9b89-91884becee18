//go:build darwin && metal

package metal

import (
	"context"

	"neuralmetergo/internal/core"
)

// MetalProviderFactory implements the core.GPUProviderFactory interface for Metal
type MetalProviderFactory struct {
	*core.DefaultGPUProviderFactory
}

// NewMetalProviderFactory creates a new Metal-specific provider factory
func NewMetalProviderFactory() core.GPUProviderFactory {
	return &MetalProviderFactory{
		DefaultGPUProviderFactory: &core.DefaultGPUProviderFactory{},
	}
}

// CreateProvider creates a Metal GPU provider when the platform is Metal
func (f *MetalProviderFactory) CreateProvider(ctx context.Context, platform core.GPUPlatform) (core.GPUProvider, error) {
	if platform == core.PlatformMetal {
		return NewSimpleMetalProvider(), nil
	}

	// For other platforms, delegate to the base factory
	return f.DefaultGPUProviderFactory.CreateProvider(ctx, platform)
}

// DetectAvailablePlatforms detects Metal availability on macOS
func (f *MetalProviderFactory) DetectAvailablePlatforms(ctx context.Context) ([]core.GPUPlatform, error) {
	var platforms []core.GPUPlatform

	// On macOS with Metal build tag, Metal should be available
	detector := NewSimpleMetalDetector()
	devices, err := detector.DetectDevices(ctx)
	if err == nil && len(devices) > 0 {
		platforms = append(platforms, core.PlatformMetal)
	}

	if len(platforms) == 0 {
		return nil, core.NewGPUError(
			core.ErrorPlatformNotSupported,
			core.PlatformMetal,
			-1,
			"No Metal devices found on this macOS system",
			nil,
		)
	}

	return platforms, nil
}
