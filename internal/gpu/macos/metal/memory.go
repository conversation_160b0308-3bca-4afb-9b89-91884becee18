//go:build darwin && metal

package metal

/*
#cgo LDFLAGS: -framework Metal -framework Foundation
#include <Metal/Metal.h>
#include <Foundation/Foundation.h>

// Helper functions for Metal memory management
static id<MTLBuffer> createBuffer(id<MTLDevice> device, uint64_t size) {
    if (device == nil) return nil;
    return [device newBufferWithLength:size options:MTLResourceStorageModeShared];
}

static void releaseBuffer(id<MTLBuffer> buffer) {
    if (buffer != nil) {
        [buffer release];
    }
}

static uint64_t getBufferLength(id<MTLBuffer> buffer) {
    if (buffer == nil) return 0;
    return [buffer length];
}

static void* getBufferContents(id<MTLBuffer> buffer) {
    if (buffer == nil) return NULL;
    return [buffer contents];
}
*/
import "C"

import (
	"sync"

	"neuralmetergo/internal/core"
)

// MetalMemoryManager handles Metal GPU memory allocation and management
type MetalMemoryManager struct {
	device      C.id
	allocations map[uintptr]*MetalMemory
	totalUsed   int64
	mutex       sync.RWMutex
}

// NewMetalMemoryManager creates a new Metal memory manager
func NewMetalMemoryManager(device C.id) *MetalMemoryManager {
	return &MetalMemoryManager{
		device:      device,
		allocations: make(map[uintptr]*MetalMemory),
		totalUsed:   0,
	}
}

// Allocate allocates GPU memory using Metal
func (m *MetalMemoryManager) Allocate(size int64) (core.GPUMemory, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if size <= 0 {
		return nil, core.NewGPUError(
			core.ErrorInvalidParameter,
			core.PlatformMetal,
			-1,
			"Invalid memory size for allocation",
			nil,
		)
	}

	buffer := C.createBuffer(m.device, C.uint64_t(size))
	if buffer == nil {
		return nil, core.NewGPUError(
			core.ErrorMemoryAllocationFailed,
			core.PlatformMetal,
			-1,
			"Failed to allocate Metal buffer",
			nil,
		)
	}

	contents := C.getBufferContents(buffer)
	address := uintptr(contents)

	memory := &MetalMemory{
		size:    size,
		address: address,
		buffer:  buffer,
		valid:   true,
	}

	m.allocations[address] = memory
	m.totalUsed += size

	return memory, nil
}

// Free releases GPU memory
func (m *MetalMemoryManager) Free(memory core.GPUMemory) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	metalMemory, ok := memory.(*MetalMemory)
	if !ok {
		return core.NewGPUError(
			core.ErrorInvalidParameter,
			core.PlatformMetal,
			-1,
			"Invalid Metal memory object",
			nil,
		)
	}

	if !metalMemory.IsValid() {
		return core.NewGPUError(
			core.ErrorInvalidMemoryAccess,
			core.PlatformMetal,
			-1,
			"Attempting to free invalid Metal memory",
			nil,
		)
	}

	address := metalMemory.GetAddress()
	if allocation, exists := m.allocations[address]; exists {
		C.releaseBuffer(allocation.buffer)
		allocation.valid = false
		m.totalUsed -= allocation.size
		delete(m.allocations, address)
	}

	return nil
}

// GetUsedMemory returns the total amount of used memory
func (m *MetalMemoryManager) GetUsedMemory() int64 {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.totalUsed
}

// Shutdown releases all allocated memory
func (m *MetalMemoryManager) Shutdown() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for _, allocation := range m.allocations {
		if allocation.valid {
			C.releaseBuffer(allocation.buffer)
			allocation.valid = false
		}
	}

	m.allocations = make(map[uintptr]*MetalMemory)
	m.totalUsed = 0
}

// MetalMemory implements the core.GPUMemory interface for Metal
type MetalMemory struct {
	size    int64
	address uintptr
	buffer  C.id
	valid   bool
}

func (m *MetalMemory) GetSize() int64 {
	return m.size
}

func (m *MetalMemory) GetAddress() uintptr {
	return m.address
}

func (m *MetalMemory) IsValid() bool {
	return m.valid
}

// GetMetalBuffer returns the underlying Metal buffer for platform-specific operations
func (m *MetalMemory) GetMetalBuffer() C.id {
	return m.buffer
}
