package gpu

import (
	"testing"
	"time"
)

// MockGPUManager for testing
type MockGPUManager struct {
	gpus []*GPUInfo
}

func (m *MockGPUManager) GetAvailableGPUs() ([]*GPUInfo, error) {
	return m.gpus, nil
}

func (m *MockGPUManager) SelectBestGPU(config GPUConfig) (*GPUInfo, error) {
	if len(m.gpus) > 0 {
		return m.gpus[0], nil
	}
	return nil, nil
}

func (m *MockGPUManager) StartMonitoring(deviceID int, interval time.Duration) error {
	return nil
}

func (m *MockGPUManager) StopMonitoring(deviceID int) error {
	return nil
}

func (m *MockGPUManager) GetCurrentMetrics(deviceID int) (*GPUMetrics, error) {
	return &GPUMetrics{}, nil
}

func (m *MockGPUManager) IsGPUAvailable(deviceID int) bool {
	return deviceID < len(m.gpus) && m.gpus[deviceID].Available
}

func (m *MockGPUManager) GetGPUTypes() []GPUType {
	return []GPUType{GPUTypeCUDA, GPUTypeOpenCL}
}

func createMockGPUManager() *MockGPUManager {
	return &MockGPUManager{
		gpus: []*GPUInfo{
			{
				ID:                  0,
				Name:                "NVIDIA GeForce RTX 4090",
				Vendor:              "NVIDIA",
				Type:                GPUTypeCUDA,
				TotalMemory:         24 * 1024 * 1024 * 1024, // 24GB
				FreeMemory:          20 * 1024 * 1024 * 1024, // 20GB
				MultiProcessorCount: 128,
				ClockRate:           2520000, // 2.52 GHz
				ComputeCapability:   ComputeCapability{Major: 8, Minor: 9},
				Available:           true,

				Utilization: 25.5,
			},
		},
	}
}

func TestNewGPUConfigurationInterface(t *testing.T) {
	mockManager := createMockGPUManager()

	gci, err := NewGPUConfigurationInterface(mockManager)
	if err != nil {
		t.Fatalf("Failed to create GPU configuration interface: %v", err)
	}

	if gci == nil {
		t.Fatal("GPU configuration interface is nil")
	}

	if len(gci.detectedGPUs) != 1 {
		t.Errorf("Expected 1 detected GPU, got %d", len(gci.detectedGPUs))
	}

	if gci.activeProfile != ProfileBalanced {
		t.Errorf("Expected active profile to be balanced, got %s", gci.activeProfile)
	}
}

func TestDefaultGPUOptimizationConfig(t *testing.T) {
	config := DefaultGPUOptimizationConfig()

	if config == nil {
		t.Fatal("Default configuration is nil")
	}

	// Test device selection defaults
	if !config.DeviceSelection.AutoSelect {
		t.Error("Expected auto-select to be true by default")
	}

	if config.DeviceSelection.PreferredVendor != "nvidia" {
		t.Errorf("Expected preferred vendor to be nvidia, got %s", config.DeviceSelection.PreferredVendor)
	}

	// Test precision defaults
	if config.Precision.DefaultMode != PrecisionFP32 {
		t.Errorf("Expected default precision to be FP32, got %s", config.Precision.DefaultMode)
	}

	// Test memory defaults
	if config.Memory.Strategy != MemoryDynamic {
		t.Errorf("Expected memory strategy to be dynamic, got %s", config.Memory.Strategy)
	}

	if config.Memory.PoolInitialSizeMB != 512 {
		t.Errorf("Expected initial pool size to be 512MB, got %d", config.Memory.PoolInitialSizeMB)
	}

	// Test performance defaults
	if config.Performance.BatchSize != 32 {
		t.Errorf("Expected batch size to be 32, got %d", config.Performance.BatchSize)
	}

	if !config.Performance.DynamicBatching {
		t.Error("Expected dynamic batching to be enabled by default")
	}

	// Test presets defaults
	if config.Presets.ActiveProfile != ProfileBalanced {
		t.Errorf("Expected active profile to be balanced, got %s", config.Presets.ActiveProfile)
	}
}

func TestSetOptimizationProfile(t *testing.T) {
	mockManager := createMockGPUManager()
	gci, err := NewGPUConfigurationInterface(mockManager)
	if err != nil {
		t.Fatalf("Failed to create GPU configuration interface: %v", err)
	}

	testCases := []struct {
		profile  OptimizationProfile
		expected PrecisionMode
	}{
		{ProfileSpeedOptimized, PrecisionFP16},
		{ProfileMemoryOptimized, PrecisionINT8},
		{ProfileBalanced, PrecisionFP32},
		{ProfilePowerEfficient, PrecisionFP16},
	}

	for _, tc := range testCases {
		err := gci.SetOptimizationProfile(tc.profile)
		if err != nil {
			t.Errorf("Failed to set profile %s: %v", tc.profile, err)
			continue
		}

		if gci.activeProfile != tc.profile {
			t.Errorf("Expected active profile to be %s, got %s", tc.profile, gci.activeProfile)
		}

		if gci.config.Precision.DefaultMode != tc.expected {
			t.Errorf("For profile %s, expected precision %s, got %s",
				tc.profile, tc.expected, gci.config.Precision.DefaultMode)
		}
	}
}

func TestValidateConfiguration(t *testing.T) {
	mockManager := createMockGPUManager()
	gci, err := NewGPUConfigurationInterface(mockManager)
	if err != nil {
		t.Fatalf("Failed to create GPU configuration interface: %v", err)
	}

	// Test valid configuration
	validConfig := DefaultGPUOptimizationConfig()
	err = gci.ValidateConfiguration(validConfig)
	if err != nil {
		t.Errorf("Valid configuration failed validation: %v", err)
	}

	// Test invalid configuration - negative memory
	invalidConfig := DefaultGPUOptimizationConfig()
	invalidConfig.DeviceSelection.MinMemoryGB = -1.0
	err = gci.ValidateConfiguration(invalidConfig)
	if err == nil {
		t.Error("Expected validation to fail for negative memory GB")
	}

	// Test invalid configuration - zero pool size
	invalidConfig2 := DefaultGPUOptimizationConfig()
	invalidConfig2.Memory.PoolInitialSizeMB = 0
	err = gci.ValidateConfiguration(invalidConfig2)
	if err == nil {
		t.Error("Expected validation to fail for zero pool size")
	}
}

func TestCreateCustomProfile(t *testing.T) {
	mockManager := createMockGPUManager()
	gci, err := NewGPUConfigurationInterface(mockManager)
	if err != nil {
		t.Fatalf("Failed to create GPU configuration interface: %v", err)
	}

	profileName := "test-profile"
	profileDesc := "Test profile description"

	err = gci.CreateCustomProfile(profileName, profileDesc)
	if err != nil {
		t.Errorf("Failed to create custom profile: %v", err)
	}

	// Check if profile was created
	profile, exists := gci.config.Presets.CustomProfiles[profileName]
	if !exists {
		t.Error("Custom profile was not created")
	}

	if profile.Name != profileName {
		t.Errorf("Expected profile name %s, got %s", profileName, profile.Name)
	}

	if profile.Description != profileDesc {
		t.Errorf("Expected profile description %s, got %s", profileDesc, profile.Description)
	}

	if profile.CreatedAt.IsZero() {
		t.Error("Profile creation time was not set")
	}
}

func TestBenchmarkConfiguration(t *testing.T) {
	mockManager := createMockGPUManager()
	gci, err := NewGPUConfigurationInterface(mockManager)
	if err != nil {
		t.Fatalf("Failed to create GPU configuration interface: %v", err)
	}

	result, err := gci.BenchmarkConfiguration()
	if err != nil {
		t.Errorf("Benchmark failed: %v", err)
	}

	if result == nil {
		t.Error("Benchmark result is nil")
	}

	if result.ThroughputOpsPerSec <= 0 {
		t.Error("Expected positive throughput")
	}

	if result.LatencyMs <= 0 {
		t.Error("Expected positive latency")
	}

	if result.Timestamp.IsZero() {
		t.Error("Benchmark timestamp was not set")
	}

	// Check if result was cached
	profileKey := string(gci.activeProfile)
	cachedResult, exists := gci.benchmarkCache[profileKey]
	if !exists {
		t.Error("Benchmark result was not cached")
	}

	if cachedResult.ThroughputOpsPerSec != result.ThroughputOpsPerSec {
		t.Error("Cached result does not match returned result")
	}
}

func TestGetOptimizationSuggestions(t *testing.T) {
	mockManager := createMockGPUManager()
	gci, err := NewGPUConfigurationInterface(mockManager)
	if err != nil {
		t.Fatalf("Failed to create GPU configuration interface: %v", err)
	}

	suggestions := gci.GetOptimizationSuggestions()

	// Should have suggestions for high-end GPU with default config
	if len(suggestions) == 0 {
		t.Error("Expected optimization suggestions for default configuration")
	}

	// Test with no GPUs
	emptyManager := &MockGPUManager{gpus: []*GPUInfo{}}
	emptyGCI, err := NewGPUConfigurationInterface(emptyManager)
	if err != nil {
		t.Fatalf("Failed to create empty GPU configuration interface: %v", err)
	}

	emptySuggestions := emptyGCI.GetOptimizationSuggestions()
	if len(emptySuggestions) == 0 {
		t.Error("Expected suggestion about no GPUs detected")
	}
}

func TestGetConfigurationSummary(t *testing.T) {
	mockManager := createMockGPUManager()
	gci, err := NewGPUConfigurationInterface(mockManager)
	if err != nil {
		t.Fatalf("Failed to create GPU configuration interface: %v", err)
	}

	summary := gci.GetConfigurationSummary()

	if summary == "" {
		t.Error("Configuration summary is empty")
	}

	// Check if summary contains key information
	expectedStrings := []string{
		"GPU Configuration Summary",
		"Active Profile:",
		"Environment:",
		"Precision Mode:",
		"Memory Strategy:",
		"Batch Size:",
		"Detected GPUs:",
	}

	for _, expected := range expectedStrings {
		if !stringContains(summary, expected) {
			t.Errorf("Summary missing expected string: %s", expected)
		}
	}
}

// Helper function to check if a string contains a substring
func stringContains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			stringContains(s[1:], substr) ||
			(len(s) > 0 && s[:len(substr)] == substr))
}
