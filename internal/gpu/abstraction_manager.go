//go:build linux && cuda
// +build linux,cuda

package gpu

import (
	"context"
	"fmt"
	"log"
	"sync"

	"neuralmetergo/internal/gpu/types"
)

// AbstractionManager provides a unified interface for managing GPU backends
type AbstractionManager struct {
	registry *BackendRegistry
	logger   *log.Logger
	mu       sync.RWMutex

	// Cache for enumerated devices
	cachedDevices []types.GPUDevice
	cacheValid    bool
}

// NewAbstractionManager creates a new abstraction manager
func NewAbstractionManager(logger *log.Logger) *AbstractionManager {
	if logger == nil {
		logger = log.Default()
	}

	am := &AbstractionManager{
		registry: NewBackendRegistry(),
		logger:   logger,
	}

	// Register all available backends
	am.registerBackends()

	return am
}

// registerBackends registers all available backends for Linux
func (am *AbstractionManager) registerBackends() {
	am.logger.Printf("Registering GPU backends for Linux")

	// CUDA backend (highest priority on Linux)
	if am.isCUDASupported() {
		if backend := am.createCUDABackend(); backend != nil {
			am.registry.Register("CUDA", backend, 100)
			am.logger.Printf("Registered CUDA backend")
		}
	}

	// ROCm backend (AMD GPUs)
	if am.isROCmSupported() {
		if backend := am.createROCmBackend(); backend != nil {
			am.registry.Register("ROCm", backend, 95)
			am.logger.Printf("Registered ROCm backend")
		}
	}

	// OpenCL backend
	if am.isOpenCLSupported() {
		if backend := am.createOpenCLBackend(); backend != nil {
			am.registry.Register("OpenCL", backend, 80)
			am.logger.Printf("Registered OpenCL backend")
		}
	}

	am.logger.Printf("GPU backend registration complete")
}

// Platform-specific registration functions removed - Linux only project

// Backend availability checks for Linux GPU types
func (am *AbstractionManager) isCUDASupported() bool {
	detector := NewCUDADetector(am.logger)
	return detector.IsSupported()
}

func (am *AbstractionManager) isOpenCLSupported() bool {
	detector := NewOpenCLDetector(am.logger)
	return detector.IsSupported()
}

func (am *AbstractionManager) isROCmSupported() bool {
	detector := NewROCmDetector(am.logger)
	return detector.IsSupported()
}

// Device Management

// EnumerateDevices discovers and returns all available GPU devices
func (am *AbstractionManager) EnumerateDevices(ctx context.Context) ([]types.GPUDevice, error) {
	am.mu.Lock()
	defer am.mu.Unlock()

	if am.cacheValid && am.cachedDevices != nil {
		return am.cachedDevices, nil
	}

	am.logger.Printf("Enumerating GPU devices...")

	var allDevices []types.GPUDevice
	deviceIDOffset := 0

	// Get ordered backends (by priority)
	backends := am.registry.GetOrderedBackends()

	for _, backend := range backends {
		am.logger.Printf("Checking backend: %s", backend.Name())

		devices, err := backend.EnumerateDevices(ctx)
		if err != nil {
			am.logger.Printf("Backend %s enumeration failed: %v", backend.Name(), err)
			continue
		}

		// Update device IDs to be globally unique and set backend
		for i := range devices {
			devices[i].ID = fmt.Sprintf("%s-%d", backend.Name(), deviceIDOffset+i)
			devices[i].Backend = backend.Name()
			am.logger.Printf("Found device: %s (%s) via %s backend",
				devices[i].Name, devices[i].Vendor, devices[i].Backend)
		}

		allDevices = append(allDevices, devices...)
		deviceIDOffset += len(devices) + 100 // Leave gap between backends
	}

	if len(allDevices) == 0 {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 1001, "no GPU hardware detected - this Linux product requires compatible GPU drivers (CUDA, ROCm, or OpenCL)", -1)
	}

	am.cachedDevices = allDevices
	am.cacheValid = true

	return allDevices, nil
}

// GetDevice returns a specific device by ID
func (am *AbstractionManager) GetDevice(ctx context.Context, deviceID string) (*types.GPUDevice, error) {
	devices, err := am.EnumerateDevices(ctx)
	if err != nil {
		return nil, err
	}

	for i := range devices {
		if devices[i].ID == deviceID {
			return &devices[i], nil
		}
	}

	return nil, types.NewGPUError(types.ErrorTypeInitialization, 1002, "GPU device not found", -1)
}

// SelectBestDevice selects the highest priority device from the best available backend
func (am *AbstractionManager) SelectBestDevice(ctx context.Context) (*types.GPUDevice, error) {
	devices, err := am.EnumerateDevices(ctx)
	if err != nil {
		return nil, err
	}

	if len(devices) == 0 {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 1002, "GPU device not found", -1)
	}

	// Return the first device (highest priority backend, first device)
	return &devices[0], nil
}

// Context and Resource Management

// CreateContext creates a GPU context for the specified device
func (am *AbstractionManager) CreateContext(ctx context.Context, device *types.GPUDevice) (types.GPUContext, error) {
	if device == nil {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 1002, "GPU device not found", -1)
	}

	backend, exists := am.registry.GetBackend(device.Backend)
	if !exists {
		return nil, fmt.Errorf("backend %s not found for device %s", device.Backend, device.ID)
	}

	return backend.CreateContext(device)
}

// GetCapabilities gets unified capability information for a device
func (am *AbstractionManager) GetCapabilities(device *types.GPUDevice) (*types.GPUCapability, error) {
	if device == nil {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 1002, "GPU device not found", -1)
	}

	backend, exists := am.registry.GetBackend(device.Backend)
	if !exists {
		return nil, fmt.Errorf("backend %s not found for device %s", device.Backend, device.ID)
	}

	return backend.GetCapabilities(device)
}

// SupportsFeature checks if a device supports a specific feature
func (am *AbstractionManager) SupportsFeature(device *types.GPUDevice, feature types.GPUFeature) bool {
	if device == nil {
		return false
	}

	backend, exists := am.registry.GetBackend(device.Backend)
	if !exists {
		return false
	}

	return backend.SupportsFeature(device, feature)
}

// CreateMemoryManager creates a memory manager for the given context
func (am *AbstractionManager) CreateMemoryManager(ctx types.GPUContext) (types.GPUMemoryManager, error) {
	if ctx == nil || !ctx.IsValid() {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 1003, "GPU context is invalid", -1)
	}

	device := ctx.GetDevice()
	backend, exists := am.registry.GetBackend(device.Backend)
	if !exists {
		return nil, fmt.Errorf("backend %s not found for device %s", device.Backend, device.ID)
	}

	return backend.CreateMemoryManager(ctx)
}

// CreateExecutor creates an executor for the given context
func (am *AbstractionManager) CreateExecutor(ctx types.GPUContext) (types.GPUExecutor, error) {
	if ctx == nil || !ctx.IsValid() {
		return nil, types.NewGPUError(types.ErrorTypeInitialization, 1003, "GPU context is invalid", -1)
	}

	device := ctx.GetDevice()
	backend, exists := am.registry.GetBackend(device.Backend)
	if !exists {
		return nil, fmt.Errorf("backend %s not found for device %s", device.Backend, device.ID)
	}

	return backend.CreateExecutor(ctx)
}

// Backend Management

// ListBackends returns the names of all registered backends
func (am *AbstractionManager) ListBackends() []string {
	return am.registry.ListBackends()
}

// GetBackend returns a specific backend by name
func (am *AbstractionManager) GetBackend(name string) (types.GPUBackend, error) {
	backend, exists := am.registry.GetBackend(name)
	if !exists {
		return nil, fmt.Errorf("backend %s not found", name)
	}
	return backend, nil
}

// GetBestBackend returns the highest priority functional backend
func (am *AbstractionManager) GetBestBackend(ctx context.Context) (types.GPUBackend, error) {
	return am.registry.GetBestBackend(ctx)
}

// Invalidate cache when devices may have changed
func (am *AbstractionManager) InvalidateCache() {
	am.mu.Lock()
	defer am.mu.Unlock()

	am.cacheValid = false
	am.cachedDevices = nil
}

// Cleanup shuts down all backends and cleans up resources
func (am *AbstractionManager) Cleanup() error {
	am.mu.Lock()
	defer am.mu.Unlock()

	am.cacheValid = false
	am.cachedDevices = nil

	am.logger.Printf("GPU abstraction manager cleaned up successfully")
	return nil
}

// Backend creation functions - these implement the logic directly to avoid build tag issues
// Metal removed - Linux only project

func (am *AbstractionManager) createCUDABackend() types.GPUBackend {
	// Create CUDA backend with runtime detection
	detector := NewCUDADetector(am.logger)
	if detector.IsSupported() {
		return &CUDABackendWrapper{detector: detector}
	}
	return nil
}

func (am *AbstractionManager) createOpenCLBackend() types.GPUBackend {
	// OpenCL backend with runtime detection
	detector := NewOpenCLDetector(am.logger)
	if detector.IsSupported() {
		return &OpenCLBackendWrapper{detector: detector}
	}
	return nil
}

func (am *AbstractionManager) createROCmBackend() types.GPUBackend {
	// ROCm is primarily for Linux AMD GPUs
	if !IsLinux() {
		return nil
	}
	// ROCm backend with runtime detection
	detector := NewROCmDetector(am.logger)
	if detector.IsSupported() {
		return &ROCmBackendWrapper{detector: detector}
	}
	return nil
}

// OneAPI removed - Linux only project, focus on CUDA/OpenCL/ROCm

// DirectML removed - Linux only project

// Backend creation functions are implemented in separate files with build tags

// Helper functions for system memory detection
func getSystemMemory() uint64 {
	return 16 * 1024 * 1024 * 1024 // 16GB placeholder
}

func getAvailableMemory() uint64 {
	return 8 * 1024 * 1024 * 1024 // 8GB placeholder
}
