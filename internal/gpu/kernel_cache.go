package gpu

import (
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"
)

// NewKernelCache creates a new kernel cache instance
func NewKernelCache(config CacheConfig) (*KernelCache, error) {
	// Create cache directory if it doesn't exist
	if err := os.MkdirAll(config.CacheDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create cache directory: %w", err)
	}

	cache := &KernelCache{
		config:   config,
		entries:  make(map[string]*CacheEntry),
		metrics:  CacheMetrics{},
		stopChan: make(chan struct{}),
	}

	// Initialize LRU list
	cache.lruList = &LRUNode{}
	cache.lruList.Next = cache.lruList
	cache.lruList.Prev = cache.lruList

	// Load existing cache entries from disk
	if err := cache.loadFromDisk(); err != nil {
		return nil, fmt.Errorf("failed to load cache from disk: %w", err)
	}

	// Start cleanup goroutine if enabled
	if config.CleanupInterval > 0 {
		cache.wg.Add(1)
		go cache.cleanupLoop()
	}

	return cache, nil
}

// Get retrieves a kernel from the cache
func (kc *KernelCache) Get(key string) (*CompiledKernel, bool) {
	kc.mutex.RLock()
	defer kc.mutex.RUnlock()

	entry, exists := kc.entries[key]
	if !exists {
		kc.metrics.Misses++
		return nil, false
	}

	// Check if entry has expired
	if kc.config.MaxAge > 0 && time.Since(entry.CreatedAt) > kc.config.MaxAge {
		kc.mutex.RUnlock()
		kc.mutex.Lock()
		kc.evictEntry(key)
		kc.mutex.Unlock()
		kc.mutex.RLock()
		kc.metrics.Misses++
		return nil, false
	}

	// Update access statistics
	entry.AccessCount++
	entry.LastAccessed = time.Now()

	// Move to front of LRU list
	kc.moveToFront(entry)

	kc.metrics.Hits++
	return entry.Kernel, true
}

// Put stores a kernel in the cache
func (kc *KernelCache) Put(key string, kernel *CompiledKernel) error {
	kc.mutex.Lock()
	defer kc.mutex.Unlock()

	// Check if entry already exists
	if existingEntry, exists := kc.entries[key]; exists {
		// Update existing entry
		existingEntry.Kernel = kernel
		existingEntry.LastAccessed = time.Now()
		kc.moveToFront(existingEntry)
		return kc.saveToDisk(existingEntry)
	}

	// Create new entry
	entry := &CacheEntry{
		Key:          key,
		Kernel:       kernel,
		AccessCount:  1,
		LastAccessed: time.Now(),
		CreatedAt:    time.Now(),
		Size:         int64(len(kernel.Binary)),
	}

	// Generate file path for persistent storage
	entry.FilePath = filepath.Join(kc.config.CacheDir, key+".cache")

	// Save to disk
	if err := kc.saveToDisk(entry); err != nil {
		return fmt.Errorf("failed to save cache entry to disk: %w", err)
	}

	// Add to cache
	kc.entries[key] = entry
	kc.addToLRU(entry)

	// Update metrics
	kc.metrics.EntryCount++
	kc.metrics.TotalSize += entry.Size

	// Check if we need to evict entries
	kc.enforceEvictionPolicy()

	return nil
}

// Clear removes all entries from the cache
func (kc *KernelCache) Clear() error {
	kc.mutex.Lock()
	defer kc.mutex.Unlock()

	// Remove all files from disk
	for _, entry := range kc.entries {
		if err := os.Remove(entry.FilePath); err != nil && !os.IsNotExist(err) {
			return fmt.Errorf("failed to remove cache file %s: %w", entry.FilePath, err)
		}
	}

	// Clear in-memory cache
	kc.entries = make(map[string]*CacheEntry)
	kc.lruList.Next = kc.lruList
	kc.lruList.Prev = kc.lruList

	// Reset metrics
	kc.metrics.EntryCount = 0
	kc.metrics.TotalSize = 0
	kc.metrics.Evictions = 0

	return nil
}

// GetMetrics returns current cache metrics
func (kc *KernelCache) GetMetrics() CacheMetrics {
	kc.mutex.RLock()
	defer kc.mutex.RUnlock()
	return kc.metrics
}

// Cleanup performs cleanup operations and stops background goroutines
func (kc *KernelCache) Cleanup() error {
	// Stop cleanup goroutine
	close(kc.stopChan)
	kc.wg.Wait()

	// Save current state to disk
	kc.mutex.Lock()
	defer kc.mutex.Unlock()

	for _, entry := range kc.entries {
		if err := kc.saveToDisk(entry); err != nil {
			return fmt.Errorf("failed to save cache entry during cleanup: %w", err)
		}
	}

	return nil
}

// loadFromDisk loads cache entries from persistent storage
func (kc *KernelCache) loadFromDisk() error {
	if !kc.config.Enabled {
		return nil
	}

	// Read cache directory
	files, err := os.ReadDir(kc.config.CacheDir)
	if err != nil {
		if os.IsNotExist(err) {
			return nil // Directory doesn't exist yet, that's fine
		}
		return fmt.Errorf("failed to read cache directory: %w", err)
	}

	// Load each cache file
	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".cache" {
			if err := kc.loadCacheFile(filepath.Join(kc.config.CacheDir, file.Name())); err != nil {
				// Log error but continue loading other files
				fmt.Printf("Warning: failed to load cache file %s: %v\n", file.Name(), err)
			}
		}
	}

	return nil
}

// loadCacheFile loads a single cache file
func (kc *KernelCache) loadCacheFile(filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	var reader io.Reader = file

	// Handle compression if enabled
	if kc.config.CompressionLevel > 0 {
		gzReader, err := gzip.NewReader(file)
		if err != nil {
			return err
		}
		defer gzReader.Close()
		reader = gzReader
	}

	// Decode cache entry
	var entry CacheEntry
	if err := json.NewDecoder(reader).Decode(&entry); err != nil {
		return err
	}

	// Validate entry
	if kc.config.EnableValidation {
		if err := kc.validateCacheEntry(&entry); err != nil {
			return fmt.Errorf("cache entry validation failed: %w", err)
		}
	}

	// Check if entry has expired
	if kc.config.MaxAge > 0 && time.Since(entry.CreatedAt) > kc.config.MaxAge {
		// Remove expired file
		os.Remove(filePath)
		return nil
	}

	// Add to cache
	key := filepath.Base(filePath)
	key = key[:len(key)-len(filepath.Ext(key))] // Remove .cache extension
	entry.Key = key
	entry.FilePath = filePath

	kc.entries[key] = &entry
	kc.addToLRU(&entry)

	// Update metrics
	kc.metrics.EntryCount++
	kc.metrics.TotalSize += entry.Size

	return nil
}

// saveToDisk saves a cache entry to persistent storage
func (kc *KernelCache) saveToDisk(entry *CacheEntry) error {
	if !kc.config.Enabled {
		return nil
	}

	file, err := os.Create(entry.FilePath)
	if err != nil {
		return err
	}
	defer file.Close()

	var writer io.Writer = file

	// Handle compression if enabled
	if kc.config.CompressionLevel > 0 {
		gzWriter, err := gzip.NewWriterLevel(file, kc.config.CompressionLevel)
		if err != nil {
			return err
		}
		defer gzWriter.Close()
		writer = gzWriter
	}

	// Encode cache entry
	return json.NewEncoder(writer).Encode(entry)
}

// validateCacheEntry validates a cache entry
func (kc *KernelCache) validateCacheEntry(entry *CacheEntry) error {
	if entry.Kernel == nil {
		return fmt.Errorf("kernel is nil")
	}

	if len(entry.Kernel.Binary) == 0 {
		return fmt.Errorf("kernel binary is empty")
	}

	if entry.Key == "" {
		return fmt.Errorf("cache key is empty")
	}

	return nil
}

// enforceEvictionPolicy enforces the cache eviction policy
func (kc *KernelCache) enforceEvictionPolicy() {
	maxSizeBytes := kc.config.MaxCacheSize * 1024 * 1024 // Convert MB to bytes

	// Evict entries if cache is too large
	for kc.metrics.TotalSize > maxSizeBytes && kc.metrics.EntryCount > 0 {
		switch kc.config.EvictionPolicy {
		case "lru":
			kc.evictLRU()
		case "lfu":
			kc.evictLFU()
		case "ttl":
			kc.evictExpired()
		default:
			kc.evictLRU() // Default to LRU
		}
	}
}

// evictLRU evicts the least recently used entry
func (kc *KernelCache) evictLRU() {
	if kc.lruList.Prev == kc.lruList {
		return // List is empty
	}

	// Get least recently used entry (tail of list)
	lruNode := kc.lruList.Prev
	kc.evictEntry(lruNode.Key)
}

// evictLFU evicts the least frequently used entry
func (kc *KernelCache) evictLFU() {
	var lfuKey string
	var minAccessCount int64 = -1

	for key, entry := range kc.entries {
		if minAccessCount == -1 || entry.AccessCount < minAccessCount {
			minAccessCount = entry.AccessCount
			lfuKey = key
		}
	}

	if lfuKey != "" {
		kc.evictEntry(lfuKey)
	}
}

// evictExpired evicts expired entries
func (kc *KernelCache) evictExpired() {
	if kc.config.MaxAge <= 0 {
		return
	}

	expiredKeys := make([]string, 0)
	for key, entry := range kc.entries {
		if time.Since(entry.CreatedAt) > kc.config.MaxAge {
			expiredKeys = append(expiredKeys, key)
		}
	}

	for _, key := range expiredKeys {
		kc.evictEntry(key)
	}
}

// evictEntry removes an entry from the cache
func (kc *KernelCache) evictEntry(key string) {
	entry, exists := kc.entries[key]
	if !exists {
		return
	}

	// Remove from LRU list
	kc.removeFromLRU(entry)

	// Remove from map
	delete(kc.entries, key)

	// Remove file from disk
	if err := os.Remove(entry.FilePath); err != nil && !os.IsNotExist(err) {
		fmt.Printf("Warning: failed to remove cache file %s: %v\n", entry.FilePath, err)
	}

	// Update metrics
	kc.metrics.EntryCount--
	kc.metrics.TotalSize -= entry.Size
	kc.metrics.Evictions++
}

// addToLRU adds an entry to the front of the LRU list
func (kc *KernelCache) addToLRU(entry *CacheEntry) {
	node := &LRUNode{
		Key:   entry.Key,
		Entry: entry,
	}

	node.Next = kc.lruList.Next
	node.Prev = kc.lruList
	kc.lruList.Next.Prev = node
	kc.lruList.Next = node

	entry.lruNode = node
}

// removeFromLRU removes an entry from the LRU list
func (kc *KernelCache) removeFromLRU(entry *CacheEntry) {
	if entry.lruNode == nil {
		return
	}

	node := entry.lruNode
	node.Prev.Next = node.Next
	node.Next.Prev = node.Prev
	entry.lruNode = nil
}

// moveToFront moves an entry to the front of the LRU list
func (kc *KernelCache) moveToFront(entry *CacheEntry) {
	kc.removeFromLRU(entry)
	kc.addToLRU(entry)
}

// cleanupLoop runs periodic cleanup operations
func (kc *KernelCache) cleanupLoop() {
	defer kc.wg.Done()

	ticker := time.NewTicker(kc.config.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			kc.performCleanup()
		case <-kc.stopChan:
			return
		}
	}
}

// performCleanup performs periodic cleanup operations
func (kc *KernelCache) performCleanup() {
	kc.mutex.Lock()
	defer kc.mutex.Unlock()

	// Remove expired entries
	kc.evictExpired()

	// Enforce eviction policy
	kc.enforceEvictionPolicy()

	// Update cleanup timestamp
	kc.metrics.LastCleanup = time.Now()
}
