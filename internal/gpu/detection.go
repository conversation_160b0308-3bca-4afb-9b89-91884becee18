package gpu

import (
	"context"
	"fmt"
	"log"
	"neuralmetergo/internal/gpu/types"
	"sync"
	"time"
)

// Manager implements GPUManager interface
type Manager struct {
	detectors     []GPUDetector
	gpus          []*GPUInfo
	gpusOnce      sync.Once
	monitors      map[int]*GPUMonitor
	monitorsMutex sync.RWMutex
	config        GPUConfig
	logger        *log.Logger
}

// GPUMonitor handles monitoring for a specific GPU
type GPUMonitor struct {
	deviceID  int
	detector  GPUDetector
	interval  time.Duration
	stopChan  chan bool
	metrics   *GPUMetrics
	metricsMu sync.RWMutex
	running   bool
}

// NewManager creates a new GPU manager with the provided configuration
func NewManager(config GPUConfig, logger *log.Logger) *Manager {
	if logger == nil {
		logger = log.Default()
	}

	m := &Manager{
		detectors: make([]GPUDetector, 0),
		monitors:  make(map[int]*GPUMonitor),
		config:    config,
		logger:    logger,
	}

	// Register detectors based on build tags and availability
	m.registerDetectors()

	return m
}

// registerDetectors registers available GPU detectors
func (m *Manager) registerDetectors() {
	// CUDA detector (if available)
	if cudaDetector := NewCUDADetector(m.logger); cudaDetector.IsSupported() {
		if err := cudaDetector.Initialize(); err != nil {
			m.logger.Printf("Failed to initialize CUDA detector: %v", err)
		} else {
			m.detectors = append(m.detectors, cudaDetector)
			m.logger.Println("CUDA detector registered")
		}
	}

	// ROCm detector (AMD GPUs on Linux)
	if rocmDetector := NewROCmDetector(m.logger); rocmDetector.IsSupported() {
		if err := rocmDetector.Initialize(); err != nil {
			m.logger.Printf("Failed to initialize ROCm detector: %v", err)
		} else {
			m.detectors = append(m.detectors, rocmDetector)
			m.logger.Println("ROCm detector registered")
		}
	}

	// OpenCL detector (if available)
	if openclDetector := NewOpenCLDetector(m.logger); openclDetector.IsSupported() {
		if err := openclDetector.Initialize(); err != nil {
			m.logger.Printf("Failed to initialize OpenCL detector: %v", err)
		} else {
			m.detectors = append(m.detectors, openclDetector)
			m.logger.Println("OpenCL detector registered")
		}
	}

	// NEW: Try the unified abstraction system as well
	m.tryAbstractionSystem()

	m.logger.Printf("Registered %d GPU detectors", len(m.detectors))
}

// tryAbstractionSystem attempts to use the new unified GPU abstraction system
func (m *Manager) tryAbstractionSystem() {
	// If specialised detector(s) (CUDA / ROCm / OpenCL) are present we prefer them, because
	// they expose detailed fields (SM count, clocks, architecture) that the abstraction
	// layer still lacks.  Only fall back to the abstraction manager when **no** dedicated
	// detector registered successfully.
	if len(m.detectors) > 0 {
		m.logger.Printf("Skipping abstraction fallback because %d specialised detector(s) succeeded", len(m.detectors))
		return
	}

	abstractionManager := NewAbstractionManager(m.logger)
	ctx := context.Background()
	devices, err := abstractionManager.EnumerateDevices(ctx)
	if err != nil {
		m.logger.Printf("Abstraction system detection failed: %v", err)
		return
	}

	if len(devices) == 0 {
		return
	}

	m.logger.Printf("Abstraction system detected %d device(s)", len(devices))

	for _, device := range devices {
		gpuInfo, err := convertDeviceToGPUInfo(device)
		if err == nil && gpuInfo != nil {
			m.gpus = append(m.gpus, gpuInfo)
		}
	}
}

// convertDeviceToGPUInfo converts a new types.GPUDevice to old GPUInfo format
func convertDeviceToGPUInfo(device types.GPUDevice) (*GPUInfo, error) {
	var gpuType GPUType
	switch device.Backend {
	case "ROCm":
		gpuType = GPUTypeROCm
	case "CUDA":
		gpuType = GPUTypeCUDA
	case "OpenCL":
		gpuType = GPUTypeOpenCL
	default:
		// Attempt to convert from the backend string, fallback to a default
		gpuType = GPUType(device.Backend)
		// A more robust solution might be needed if backend strings diverge
	}

	return &GPUInfo{
		ID:                  device.Index,
		Name:                device.Name,
		Type:                gpuType,
		Platform:            device.Backend,
		Vendor:              device.Vendor,
		Architecture:        architectureFromCompute(ComputeCapability{Major: device.Compute.Capability.Major, Minor: device.Compute.Capability.Minor}),
		TotalMemory:         int64(device.Memory.Total),
		FreeMemory:          int64(device.Memory.Free),
		ComputeCapability:   ComputeCapability{Major: device.Compute.Capability.Major, Minor: device.Compute.Capability.Minor},
		MultiProcessorCount: device.ComputeUnits,
		ClockRate:           device.ClockSpeed,
	}, nil
}

// GetAvailableGPUs returns all available GPUs
func (m *Manager) GetAvailableGPUs() ([]*GPUInfo, error) {
	var err error
	m.gpusOnce.Do(func() {
		err = m.detectGPUs()
	})

	if err != nil {
		return nil, err
	}

	// Return a copy to prevent external modification
	result := make([]*GPUInfo, len(m.gpus))
	copy(result, m.gpus)
	return result, nil
}

// detectGPUs performs GPU detection using all available detectors
func (m *Manager) detectGPUs() error {
	var allGPUs []*GPUInfo
	var detectionErrors []error

	// Try each detector
	for _, detector := range m.detectors {
		gpus, err := detector.Detect()
		if err != nil {
			detectionErrors = append(detectionErrors, fmt.Errorf("GPU detection failed: %w", err))
			continue
		}

		if len(gpus) > 0 {
			allGPUs = append(allGPUs, gpus...)
			m.logger.Printf("Detected %d hardware GPU(s)", len(gpus))
		}
	}

	if len(allGPUs) == 0 {
		if len(detectionErrors) > 0 {
			return fmt.Errorf("no GPU hardware detected - this Linux product requires compatible GPU drivers (CUDA, ROCm, or OpenCL): %v", detectionErrors)
		}
		return fmt.Errorf("no GPU hardware detected - this Linux product requires compatible GPU drivers (CUDA, ROCm, or OpenCL)")
	}

	// Remove duplicates and sort by preference
	m.gpus = m.dedupAndSortGPUs(allGPUs)
	m.logger.Printf("Final GPU list contains %d unique devices", len(m.gpus))

	return nil
}

// dedupAndSortGPUs removes duplicates and sorts GPUs by preference
func (m *Manager) dedupAndSortGPUs(gpus []*GPUInfo) []*GPUInfo {
	seen := make(map[string]bool)
	var result []*GPUInfo

	// Preference order: CUDA > ROCm > OpenCL
	priorities := map[GPUType]int{
		GPUTypeCUDA:   3,
		GPUTypeROCm:   2,
		GPUTypeOpenCL: 1,
	}

	// First pass: collect all GPUs with priority
	type gpuWithPriority struct {
		gpu      *GPUInfo
		priority int
	}
	var gpusWithPriority []gpuWithPriority

	for _, gpu := range gpus {
		key := fmt.Sprintf("%s-%s-%d", gpu.Type, gpu.Name, gpu.ID)
		if seen[key] {
			continue
		}
		seen[key] = true

		priority := priorities[gpu.Type]
		gpusWithPriority = append(gpusWithPriority, gpuWithPriority{
			gpu:      gpu,
			priority: priority,
		})
	}

	// Sort by priority (higher first), then by memory (larger first)
	for i := 0; i < len(gpusWithPriority); i++ {
		for j := i + 1; j < len(gpusWithPriority); j++ {
			a, b := gpusWithPriority[i], gpusWithPriority[j]

			if a.priority < b.priority ||
				(a.priority == b.priority && a.gpu.TotalMemory < b.gpu.TotalMemory) {
				gpusWithPriority[i], gpusWithPriority[j] = b, a
			}
		}
	}

	// Extract sorted GPUs
	for _, item := range gpusWithPriority {
		result = append(result, item.gpu)
	}

	return result
}

// SelectBestGPU selects the best GPU based on configuration
func (m *Manager) SelectBestGPU(config GPUConfig) (*GPUInfo, error) {
	if !config.Enabled {
		return nil, fmt.Errorf("GPU is disabled in configuration")
	}

	gpus, err := m.GetAvailableGPUs()
	if err != nil {
		return nil, err
	}

	if len(gpus) == 0 {
		return nil, fmt.Errorf("no GPUs available")
	}

	// If specific device requested, check if it's available
	if config.DeviceID >= 0 {
		for _, gpu := range gpus {
			if gpu.ID == config.DeviceID {
				minMemory := int64(config.MinMemoryGB * 1024 * 1024 * 1024)
				if gpu.IsCompatible(minMemory, config.MinComputeCapability) {
					return gpu, nil
				}
				return nil, fmt.Errorf("GPU device %d does not meet requirements", config.DeviceID)
			}
		}
		return nil, fmt.Errorf("GPU device %d not found", config.DeviceID)
	}

	// Auto-select best GPU
	minMemory := int64(config.MinMemoryGB * 1024 * 1024 * 1024)

	for _, gpu := range gpus {
		if !gpu.IsCompatible(minMemory, config.MinComputeCapability) {
			continue
		}

		// Apply preferences
		if config.PreferCUDA && gpu.Type != GPUTypeCUDA {
			continue
		}

		// Check utilization constraints

		if gpu.MemoryUtilization() > config.MaxMemoryUtilization {
			continue
		}

		return gpu, nil
	}

	return nil, fmt.Errorf("no suitable GPU found meeting the requirements")
}

// StartMonitoring begins monitoring GPU metrics
func (m *Manager) StartMonitoring(deviceID int, interval time.Duration) error {
	m.monitorsMutex.Lock()
	defer m.monitorsMutex.Unlock()

	if _, exists := m.monitors[deviceID]; exists {
		return fmt.Errorf("monitoring already started for device %d", deviceID)
	}

	// Find appropriate detector for this device
	var detector GPUDetector
	for _, d := range m.detectors {
		if info, err := d.GetInfo(deviceID); err == nil && info != nil {
			detector = d
			break
		}
	}

	if detector == nil {
		return fmt.Errorf("no detector found for device %d", deviceID)
	}

	monitor := &GPUMonitor{
		deviceID: deviceID,
		detector: detector,
		interval: interval,
		stopChan: make(chan bool),
		running:  true,
	}

	m.monitors[deviceID] = monitor

	// Start monitoring goroutine
	go m.monitorGPU(monitor)

	m.logger.Printf("Started monitoring GPU device %d with interval %v", deviceID, interval)
	return nil
}

// StopMonitoring stops monitoring GPU metrics
func (m *Manager) StopMonitoring(deviceID int) error {
	m.monitorsMutex.Lock()
	defer m.monitorsMutex.Unlock()

	monitor, exists := m.monitors[deviceID]
	if !exists {
		return fmt.Errorf("no monitoring active for device %d", deviceID)
	}

	monitor.running = false
	close(monitor.stopChan)
	delete(m.monitors, deviceID)

	m.logger.Printf("Stopped monitoring GPU device %d", deviceID)
	return nil
}

// GetCurrentMetrics returns current metrics for a GPU
func (m *Manager) GetCurrentMetrics(deviceID int) (*GPUMetrics, error) {
	m.monitorsMutex.RLock()
	monitor, exists := m.monitors[deviceID]
	m.monitorsMutex.RUnlock()

	if exists {
		monitor.metricsMu.RLock()
		metrics := monitor.metrics
		monitor.metricsMu.RUnlock()

		if metrics != nil {
			// Return a copy
			metricsCopy := *metrics
			return &metricsCopy, nil
		}
	}

	// If not monitoring, try to get metrics directly
	for _, detector := range m.detectors {
		if metrics, err := detector.GetMetrics(deviceID); err == nil {
			return metrics, nil
		}
	}

	return nil, fmt.Errorf("unable to get metrics for device %d", deviceID)
}

// IsGPUAvailable checks if a specific GPU is available
func (m *Manager) IsGPUAvailable(deviceID int) bool {
	gpus, err := m.GetAvailableGPUs()
	if err != nil {
		return false
	}

	for _, gpu := range gpus {
		if gpu.ID == deviceID && gpu.Available {
			return true
		}
	}

	return false
}

// GetGPUTypes returns supported GPU types on this system
func (m *Manager) GetGPUTypes() []GPUType {
	var types []GPUType
	typeSet := make(map[GPUType]bool)

	gpus, err := m.GetAvailableGPUs()
	if err != nil {
		return types
	}

	for _, gpu := range gpus {
		if !typeSet[gpu.Type] {
			types = append(types, gpu.Type)
			typeSet[gpu.Type] = true
		}
	}

	return types
}

// monitorGPU runs the monitoring loop for a specific GPU
func (m *Manager) monitorGPU(monitor *GPUMonitor) {
	ticker := time.NewTicker(monitor.interval)
	defer ticker.Stop()

	for {
		select {
		case <-monitor.stopChan:
			return
		case <-ticker.C:
			if !monitor.running {
				return
			}

			metrics, err := monitor.detector.GetMetrics(monitor.deviceID)
			if err != nil {
				m.logger.Printf("Failed to get metrics for device %d: %v", monitor.deviceID, err)
				continue
			}

			monitor.metricsMu.Lock()
			monitor.metrics = metrics
			monitor.metricsMu.Unlock()
		}
	}
}

// Cleanup performs cleanup operations for all detectors
func (m *Manager) Cleanup() error {
	// Stop all monitoring
	m.monitorsMutex.Lock()
	for deviceID := range m.monitors {
		if monitor := m.monitors[deviceID]; monitor.running {
			monitor.running = false
			close(monitor.stopChan)
		}
	}
	m.monitors = make(map[int]*GPUMonitor)
	m.monitorsMutex.Unlock()

	// Cleanup all detectors
	var errors []error
	for _, detector := range m.detectors {
		if err := detector.Cleanup(); err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("cleanup errors: %v", errors)
	}

	m.logger.Println("GPU manager cleanup completed")
	return nil
}

// RefreshGPUs forces a refresh of the GPU list
func (m *Manager) RefreshGPUs() error {
	m.gpusOnce = sync.Once{}
	return m.detectGPUs()
}

// GetConfiguration returns the current GPU configuration
func (m *Manager) GetConfiguration() GPUConfig {
	return m.config
}

// UpdateConfiguration updates the GPU configuration
func (m *Manager) UpdateConfiguration(config GPUConfig) error {
	if err := config.Validate(); err != nil {
		return err
	}

	m.config = config
	return nil
}
