package gpu

import (
	"context"
	"fmt"
	"math"
	"sync"
	"sync/atomic"
	"time"
)

// TimeoutStrategy defines different approaches to batch timeout management
type TimeoutStrategy int

const (
	TimeoutStrategyFixed TimeoutStrategy = iota
	TimeoutStrategyAdaptive
	TimeoutStrategyLoadAware
	TimeoutStrategyLatencyOptimal
	TimeoutStrategyHybrid
)

// String returns the string representation of the timeout strategy
func (ts TimeoutStrategy) String() string {
	switch ts {
	case TimeoutStrategyFixed:
		return "fixed"
	case TimeoutStrategyAdaptive:
		return "adaptive"
	case TimeoutStrategyLoadAware:
		return "load_aware"
	case TimeoutStrategyLatencyOptimal:
		return "latency_optimal"
	case TimeoutStrategyHybrid:
		return "hybrid"
	default:
		return "unknown"
	}
}

// TimeoutConfig defines configuration for batch timeout management
type TimeoutConfig struct {
	// Base timeout for batch processing
	BaseTimeout time.Duration `json:"base_timeout"`

	// Minimum timeout (lower bound for adaptive strategies)
	MinTimeout time.Duration `json:"min_timeout"`

	// Maximum timeout (upper bound for adaptive strategies)
	MaxTimeout time.Duration `json:"max_timeout"`

	// Strategy for timeout adjustment
	Strategy TimeoutStrategy `json:"strategy"`

	// Adaptation speed for adaptive strategies (0.0-1.0)
	AdaptationSpeed float64 `json:"adaptation_speed"`

	// Load threshold for load-aware strategies
	LoadThreshold float64 `json:"load_threshold"`

	// Target latency for latency-optimal strategy
	TargetLatency time.Duration `json:"target_latency"`

	// Grace period before enforcing timeout
	GracePeriod time.Duration `json:"grace_period"`

	// Enable priority-based timeout adjustment
	EnablePriorityAdjustment bool `json:"enable_priority_adjustment"`

	// Priority multipliers for timeout adjustment
	PriorityMultipliers map[RequestPriority]float64 `json:"priority_multipliers"`

	// Performance window for adaptation calculations
	PerformanceWindow time.Duration `json:"performance_window"`
}

// DefaultTimeoutConfig returns a default timeout configuration
func DefaultTimeoutConfig() *TimeoutConfig {
	return &TimeoutConfig{
		BaseTimeout:              50 * time.Millisecond,
		MinTimeout:               10 * time.Millisecond,
		MaxTimeout:               200 * time.Millisecond,
		Strategy:                 TimeoutStrategyAdaptive,
		AdaptationSpeed:          0.1,
		LoadThreshold:            0.8,
		TargetLatency:            100 * time.Millisecond,
		GracePeriod:              5 * time.Millisecond,
		EnablePriorityAdjustment: true,
		PriorityMultipliers: map[RequestPriority]float64{
			RequestPriorityLow:      1.5,
			RequestPriorityNormal:   1.0,
			RequestPriorityHigh:     0.7,
			RequestPriorityCritical: 0.3,
		},
		PerformanceWindow: 30 * time.Second,
	}
}

// BatchTimeout represents a timeout event for batch processing
type BatchTimeout struct {
	// Unique timeout ID
	ID string

	// Priority level for this timeout
	Priority RequestPriority

	// Calculated timeout duration
	Duration time.Duration

	// Timestamp when timeout was created
	CreatedAt time.Time

	// Context for cancellation
	Context context.Context

	// Cancel function
	Cancel context.CancelFunc

	// Channel to signal timeout completion
	Done chan struct{}

	// Whether timeout was triggered
	Triggered bool

	// Reason for timeout
	Reason string
}

// TimeoutMetrics tracks performance metrics for timeout management
type TimeoutMetrics struct {
	// Total number of timeouts created
	TimeoutsCreated int64 `json:"timeouts_created"`

	// Number of timeouts that triggered
	TimeoutsTriggered int64 `json:"timeouts_triggered"`

	// Number of timeouts cancelled before triggering
	TimeoutsCancelled int64 `json:"timeouts_cancelled"`

	// Average timeout duration
	AverageTimeout time.Duration `json:"average_timeout"`

	// Current effective timeout
	CurrentTimeout time.Duration `json:"current_timeout"`

	// Timeout efficiency (successful triggers / total created)
	TimeoutEfficiency float64 `json:"timeout_efficiency"`

	// Recent performance metrics
	RecentLatency time.Duration `json:"recent_latency"`

	// Recent throughput (batches per second)
	RecentThroughput float64 `json:"recent_throughput"`

	// Load factor (0.0-1.0)
	LoadFactor float64 `json:"load_factor"`

	// Per-priority timeout metrics
	PriorityMetrics map[RequestPriority]*PriorityTimeoutMetrics `json:"priority_metrics"`
}

// PriorityTimeoutMetrics tracks timeout metrics per priority level
type PriorityTimeoutMetrics struct {
	TimeoutsCreated   int64         `json:"timeouts_created"`
	TimeoutsTriggered int64         `json:"timeouts_triggered"`
	AverageTimeout    time.Duration `json:"average_timeout"`
	EffectiveTimeout  time.Duration `json:"effective_timeout"`
}

// PerformanceWindow tracks recent performance for adaptive strategies
type PerformanceWindow struct {
	samples    []PerformanceSample
	mutex      sync.RWMutex
	maxSamples int
}

// PerformanceSample represents a single performance measurement
type PerformanceSample struct {
	Timestamp      time.Time
	BatchSize      int
	ProcessingTime time.Duration
	TotalLatency   time.Duration
	QueueSize      int
	Priority       RequestPriority
	TimeoutUsed    time.Duration
	WasTriggered   bool
}

// BatchTimeoutManager manages batch processing timeouts
type BatchTimeoutManager struct {
	config     *TimeoutConfig
	metrics    *TimeoutMetrics
	perfWindow *PerformanceWindow

	// Active timeouts by ID
	activeTimeouts map[string]*BatchTimeout

	// Timeout calculation state
	currentTimeout time.Duration
	lastAdaptation time.Time

	// Performance tracking
	performanceHist  []PerformanceSample
	performanceIndex int

	// Synchronization
	mu sync.RWMutex

	// Lifecycle management
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	// Background workers
	adaptationTicker *time.Ticker
	metricsTicker    *time.Ticker
}

// NewBatchTimeoutManager creates a new batch timeout manager
func NewBatchTimeoutManager(config *TimeoutConfig) *BatchTimeoutManager {
	if config == nil {
		config = DefaultTimeoutConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	manager := &BatchTimeoutManager{
		config: config,
		metrics: &TimeoutMetrics{
			PriorityMetrics: make(map[RequestPriority]*PriorityTimeoutMetrics),
		},
		perfWindow: &PerformanceWindow{
			maxSamples: 1000,
		},
		activeTimeouts:   make(map[string]*BatchTimeout),
		currentTimeout:   config.BaseTimeout,
		lastAdaptation:   time.Now(),
		performanceHist:  make([]PerformanceSample, 100),
		ctx:              ctx,
		cancel:           cancel,
		adaptationTicker: time.NewTicker(1 * time.Second),
		metricsTicker:    time.NewTicker(5 * time.Second),
	}

	// Initialize priority metrics
	priorities := []RequestPriority{
		RequestPriorityLow, RequestPriorityNormal,
		RequestPriorityHigh, RequestPriorityCritical,
	}
	for _, priority := range priorities {
		manager.metrics.PriorityMetrics[priority] = &PriorityTimeoutMetrics{
			EffectiveTimeout: config.BaseTimeout,
		}
	}

	manager.startBackgroundWorkers()
	return manager
}

// CreateTimeout creates a new timeout for batch processing
func (btm *BatchTimeoutManager) CreateTimeout(priority RequestPriority, queueSize int) *BatchTimeout {
	btm.mu.Lock()
	defer btm.mu.Unlock()

	// Calculate timeout duration based on strategy and priority
	duration := btm.calculateTimeoutDuration(priority, queueSize)

	// Create timeout context
	ctx, cancel := context.WithTimeout(btm.ctx, duration)

	timeout := &BatchTimeout{
		ID:        fmt.Sprintf("timeout_%d_%s", time.Now().UnixNano(), priority.String()),
		Priority:  priority,
		Duration:  duration,
		CreatedAt: time.Now(),
		Context:   ctx,
		Cancel:    cancel,
		Done:      make(chan struct{}),
		Reason:    "pending",
	}

	// Store active timeout
	btm.activeTimeouts[timeout.ID] = timeout

	// Update metrics
	atomic.AddInt64(&btm.metrics.TimeoutsCreated, 1)
	atomic.AddInt64(&btm.metrics.PriorityMetrics[priority].TimeoutsCreated, 1)

	// Start timeout goroutine
	btm.wg.Add(1)
	go btm.handleTimeout(timeout)

	return timeout
}

// calculateTimeoutDuration calculates timeout based on strategy and current conditions
func (btm *BatchTimeoutManager) calculateTimeoutDuration(priority RequestPriority, queueSize int) time.Duration {
	var baseDuration time.Duration

	switch btm.config.Strategy {
	case TimeoutStrategyFixed:
		baseDuration = btm.config.BaseTimeout

	case TimeoutStrategyAdaptive:
		baseDuration = btm.calculateAdaptiveTimeout()

	case TimeoutStrategyLoadAware:
		baseDuration = btm.calculateLoadAwareTimeout(queueSize)

	case TimeoutStrategyLatencyOptimal:
		baseDuration = btm.calculateLatencyOptimalTimeout()

	case TimeoutStrategyHybrid:
		baseDuration = btm.calculateHybridTimeout(queueSize)

	default:
		baseDuration = btm.config.BaseTimeout
	}

	// Apply priority adjustment
	if btm.config.EnablePriorityAdjustment {
		if multiplier, exists := btm.config.PriorityMultipliers[priority]; exists {
			baseDuration = time.Duration(float64(baseDuration) * multiplier)
		}
	}

	// Apply bounds
	if baseDuration < btm.config.MinTimeout {
		baseDuration = btm.config.MinTimeout
	}
	if baseDuration > btm.config.MaxTimeout {
		baseDuration = btm.config.MaxTimeout
	}

	return baseDuration
}

// calculateAdaptiveTimeout calculates timeout based on recent performance
func (btm *BatchTimeoutManager) calculateAdaptiveTimeout() time.Duration {
	btm.perfWindow.mutex.RLock()
	samples := btm.perfWindow.samples
	btm.perfWindow.mutex.RUnlock()

	if len(samples) < 5 {
		return btm.config.BaseTimeout
	}

	// Calculate average latency from recent samples
	var totalLatency time.Duration
	var timeoutTriggered int

	for i := len(samples) - 10; i < len(samples) && i >= 0; i++ {
		totalLatency += samples[i].TotalLatency
		if samples[i].WasTriggered {
			timeoutTriggered++
		}
	}

	avgLatency := totalLatency / time.Duration(min(10, len(samples)))
	triggerRate := float64(timeoutTriggered) / float64(min(10, len(samples)))

	// Adjust timeout based on performance
	adjustment := 1.0
	if triggerRate > 0.7 {
		// Too many timeouts triggering, increase timeout
		adjustment = 1.0 + (triggerRate-0.7)*btm.config.AdaptationSpeed
	} else if triggerRate < 0.3 {
		// Too few timeouts triggering, decrease timeout
		adjustment = 1.0 - (0.3-triggerRate)*btm.config.AdaptationSpeed
	}

	newTimeout := time.Duration(float64(avgLatency) * adjustment)

	// Smooth the change
	currentTimeout := btm.currentTimeout
	smoothedTimeout := time.Duration(
		float64(currentTimeout)*(1-btm.config.AdaptationSpeed) +
			float64(newTimeout)*btm.config.AdaptationSpeed,
	)

	return smoothedTimeout
}

// calculateLoadAwareTimeout adjusts timeout based on current system load
func (btm *BatchTimeoutManager) calculateLoadAwareTimeout(queueSize int) time.Duration {
	// Estimate load factor based on queue size and recent throughput
	recentThroughput := btm.metrics.RecentThroughput
	if recentThroughput <= 0 {
		recentThroughput = 1.0 // Avoid division by zero
	}

	// Calculate load factor
	expectedProcessingTime := float64(queueSize) / recentThroughput
	loadFactor := expectedProcessingTime / float64(btm.config.BaseTimeout.Milliseconds())

	// Adjust timeout based on load
	var adjustment float64
	if loadFactor > btm.config.LoadThreshold {
		// High load, increase timeout
		adjustment = 1.0 + (loadFactor-btm.config.LoadThreshold)*0.5
	} else {
		// Low load, decrease timeout
		adjustment = 0.5 + loadFactor/btm.config.LoadThreshold*0.5
	}

	return time.Duration(float64(btm.config.BaseTimeout) * adjustment)
}

// calculateLatencyOptimalTimeout optimizes for target latency
func (btm *BatchTimeoutManager) calculateLatencyOptimalTimeout() time.Duration {
	recentLatency := btm.metrics.RecentLatency
	targetLatency := btm.config.TargetLatency

	if recentLatency <= 0 {
		return btm.config.BaseTimeout
	}

	// Calculate adjustment to reach target latency
	latencyRatio := float64(recentLatency) / float64(targetLatency)

	var adjustment float64
	if latencyRatio > 1.1 {
		// Latency too high, decrease timeout
		adjustment = 1.0 - (latencyRatio-1.0)*0.3
	} else if latencyRatio < 0.9 {
		// Latency too low, increase timeout
		adjustment = 1.0 + (1.0-latencyRatio)*0.3
	} else {
		adjustment = 1.0
	}

	return time.Duration(float64(btm.currentTimeout) * adjustment)
}

// calculateHybridTimeout combines multiple strategies
func (btm *BatchTimeoutManager) calculateHybridTimeout(queueSize int) time.Duration {
	adaptive := btm.calculateAdaptiveTimeout()
	loadAware := btm.calculateLoadAwareTimeout(queueSize)
	latencyOptimal := btm.calculateLatencyOptimalTimeout()

	// Weight the different approaches
	weights := []float64{0.4, 0.3, 0.3} // adaptive, load-aware, latency-optimal
	timeouts := []time.Duration{adaptive, loadAware, latencyOptimal}

	var weightedSum float64
	for i, timeout := range timeouts {
		weightedSum += float64(timeout) * weights[i]
	}

	return time.Duration(weightedSum)
}

// handleTimeout manages the timeout lifecycle
func (btm *BatchTimeoutManager) handleTimeout(timeout *BatchTimeout) {
	defer btm.wg.Done()
	defer close(timeout.Done)
	defer func() {
		btm.mu.Lock()
		delete(btm.activeTimeouts, timeout.ID)
		btm.mu.Unlock()
	}()

	select {
	case <-timeout.Context.Done():
		if timeout.Context.Err() == context.DeadlineExceeded {
			timeout.Triggered = true
			timeout.Reason = "timeout_exceeded"
			atomic.AddInt64(&btm.metrics.TimeoutsTriggered, 1)
			atomic.AddInt64(&btm.metrics.PriorityMetrics[timeout.Priority].TimeoutsTriggered, 1)
		} else {
			timeout.Reason = "cancelled"
			atomic.AddInt64(&btm.metrics.TimeoutsCancelled, 1)
		}
	case <-btm.ctx.Done():
		timeout.Reason = "shutdown"
		return
	}
}

// CancelTimeout cancels an active timeout
func (btm *BatchTimeoutManager) CancelTimeout(timeoutID string) {
	btm.mu.RLock()
	timeout, exists := btm.activeTimeouts[timeoutID]
	btm.mu.RUnlock()

	if exists && timeout.Cancel != nil {
		timeout.Cancel()
	}
}

// RecordPerformance records performance data for timeout adaptation
func (btm *BatchTimeoutManager) RecordPerformance(sample PerformanceSample) {
	btm.perfWindow.mutex.Lock()
	defer btm.perfWindow.mutex.Unlock()

	// Add to circular buffer
	btm.perfWindow.samples = append(btm.perfWindow.samples, sample)
	if len(btm.perfWindow.samples) > btm.perfWindow.maxSamples {
		btm.perfWindow.samples = btm.perfWindow.samples[1:]
	}

	// Update current performance metrics
	btm.updatePerformanceMetrics(sample)
}

// updatePerformanceMetrics updates current performance metrics
func (btm *BatchTimeoutManager) updatePerformanceMetrics(sample PerformanceSample) {
	btm.mu.Lock()
	defer btm.mu.Unlock()

	// Update circular buffer for historical data
	btm.performanceHist[btm.performanceIndex] = sample
	btm.performanceIndex = (btm.performanceIndex + 1) % len(btm.performanceHist)

	// Update recent metrics
	btm.metrics.RecentLatency = sample.TotalLatency

	// Calculate throughput based on recent samples
	now := time.Now()
	recentSamples := 0
	for _, hist := range btm.performanceHist {
		if now.Sub(hist.Timestamp) < 10*time.Second {
			recentSamples++
		}
	}

	if recentSamples > 0 {
		btm.metrics.RecentThroughput = float64(recentSamples) / 10.0
	}
}

// GetEffectiveTimeout returns the current effective timeout for a priority
func (btm *BatchTimeoutManager) GetEffectiveTimeout(priority RequestPriority) time.Duration {
	btm.mu.RLock()
	defer btm.mu.RUnlock()

	return btm.metrics.PriorityMetrics[priority].EffectiveTimeout
}

// GetMetrics returns current timeout metrics
func (btm *BatchTimeoutManager) GetMetrics() *TimeoutMetrics {
	btm.mu.RLock()
	defer btm.mu.RUnlock()

	// Calculate efficiency
	created := atomic.LoadInt64(&btm.metrics.TimeoutsCreated)
	triggered := atomic.LoadInt64(&btm.metrics.TimeoutsTriggered)

	if created > 0 {
		btm.metrics.TimeoutEfficiency = float64(triggered) / float64(created)
	}

	btm.metrics.CurrentTimeout = btm.currentTimeout

	// Return a copy to avoid race conditions
	metricsCopy := *btm.metrics
	return &metricsCopy
}

// UpdateConfig updates the timeout configuration
func (btm *BatchTimeoutManager) UpdateConfig(config *TimeoutConfig) {
	btm.mu.Lock()
	defer btm.mu.Unlock()

	btm.config = config
}

// startBackgroundWorkers starts background maintenance tasks
func (btm *BatchTimeoutManager) startBackgroundWorkers() {
	// Adaptation worker
	btm.wg.Add(1)
	go func() {
		defer btm.wg.Done()
		for {
			select {
			case <-btm.adaptationTicker.C:
				btm.performAdaptation()
			case <-btm.ctx.Done():
				return
			}
		}
	}()

	// Metrics worker
	btm.wg.Add(1)
	go func() {
		defer btm.wg.Done()
		for {
			select {
			case <-btm.metricsTicker.C:
				btm.updateMetrics()
			case <-btm.ctx.Done():
				return
			}
		}
	}()
}

// performAdaptation performs periodic timeout adaptation
func (btm *BatchTimeoutManager) performAdaptation() {
	btm.mu.Lock()
	defer btm.mu.Unlock()

	if btm.config.Strategy == TimeoutStrategyFixed {
		return
	}

	// Update current timeout based on strategy
	newTimeout := btm.calculateTimeoutDuration(RequestPriorityNormal, 0)
	btm.currentTimeout = newTimeout
	btm.lastAdaptation = time.Now()

	// Update per-priority effective timeouts
	for priority := range btm.metrics.PriorityMetrics {
		effectiveTimeout := btm.calculateTimeoutDuration(priority, 0)
		btm.metrics.PriorityMetrics[priority].EffectiveTimeout = effectiveTimeout
	}
}

// updateMetrics updates periodic metrics
func (btm *BatchTimeoutManager) updateMetrics() {
	btm.mu.RLock()
	defer btm.mu.RUnlock()

	// Calculate average timeout duration
	var totalTimeout time.Duration
	var timeoutCount int

	for _, sample := range btm.performanceHist {
		if sample.Timestamp.IsZero() {
			continue
		}
		totalTimeout += sample.TimeoutUsed
		timeoutCount++
	}

	if timeoutCount > 0 {
		btm.metrics.AverageTimeout = totalTimeout / time.Duration(timeoutCount)
	}

	// Calculate load factor
	btm.metrics.LoadFactor = btm.calculateCurrentLoadFactor()
}

// calculateCurrentLoadFactor calculates current system load factor
func (btm *BatchTimeoutManager) calculateCurrentLoadFactor() float64 {
	// Simple load calculation based on active timeouts and recent performance
	activeCount := len(btm.activeTimeouts)

	if activeCount == 0 {
		return 0.0
	}

	// Load factor based on queue pressure and timeout trigger rate
	triggerRate := btm.metrics.TimeoutEfficiency
	return math.Min(1.0, float64(activeCount)/10.0+triggerRate)
}

// Close shuts down the timeout manager
func (btm *BatchTimeoutManager) Close() error {
	btm.cancel()

	// Cancel all active timeouts
	btm.mu.RLock()
	for _, timeout := range btm.activeTimeouts {
		if timeout.Cancel != nil {
			timeout.Cancel()
		}
	}
	btm.mu.RUnlock()

	// Stop tickers
	btm.adaptationTicker.Stop()
	btm.metricsTicker.Stop()

	// Wait for workers to finish
	btm.wg.Wait()

	return nil
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
