package gpu

import (
	"hash/fnv"
	"math"
	"time"
)

// FeatureEngineer provides utilities for computing enhanced features for workload prediction
type FeatureEngineer struct {
	historicalData []WorkloadDataPoint
	holidayDates   map[string]bool // Holiday dates in YYYY-MM-DD format
}

// NewFeatureEngineer creates a new feature engineering utility
func NewFeatureEngineer() *FeatureEngineer {
	fe := &FeatureEngineer{
		historicalData: make([]WorkloadDataPoint, 0),
		holidayDates:   make(map[string]bool),
	}

	// Initialize common holidays (can be extended)
	fe.initializeHolidays()
	return fe
}

// initializeHolidays sets up common holiday dates
func (fe *FeatureEngineer) initializeHolidays() {
	// Add common holidays (can be extended based on region/requirements)
	holidays := []string{
		"2024-01-01", "2024-07-04", "2024-12-25", // New Year, Independence Day, Christmas
		"2025-01-01", "2025-07-04", "2025-12-25",
	}

	for _, holiday := range holidays {
		fe.holidayDates[holiday] = true
	}
}

// AddHoliday adds a holiday date to the feature engineer
func (fe *FeatureEngineer) AddHoliday(date string) {
	fe.holidayDates[date] = true
}

// ComputeTemporalFeatures calculates time-based features with cyclical encoding
func (fe *FeatureEngineer) ComputeTemporalFeatures(timestamp time.Time) TemporalFeatures {
	hour := timestamp.Hour()
	dayOfWeek := int(timestamp.Weekday())
	dayOfMonth := timestamp.Day()
	monthOfYear := int(timestamp.Month())

	// Cyclical encoding for hour and day of week
	hourSin := math.Sin(2 * math.Pi * float64(hour) / 24.0)
	hourCos := math.Cos(2 * math.Pi * float64(hour) / 24.0)
	dayOfWeekSin := math.Sin(2 * math.Pi * float64(dayOfWeek) / 7.0)
	dayOfWeekCos := math.Cos(2 * math.Pi * float64(dayOfWeek) / 7.0)

	// Weekend and holiday detection
	isWeekend := dayOfWeek == 0 || dayOfWeek == 6 // Sunday or Saturday
	dateStr := timestamp.Format("2006-01-02")
	isHoliday := fe.holidayDates[dateStr]

	return TemporalFeatures{
		HourOfDay:    hour,
		DayOfWeek:    dayOfWeek,
		DayOfMonth:   dayOfMonth,
		MonthOfYear:  monthOfYear,
		IsWeekend:    isWeekend,
		IsHoliday:    isHoliday,
		HourSin:      hourSin,
		HourCos:      hourCos,
		DayOfWeekSin: dayOfWeekSin,
		DayOfWeekCos: dayOfWeekCos,
	}
}

// ComputeRollingStatistics calculates rolling statistics for specified windows
func (fe *FeatureEngineer) ComputeRollingStatistics(data []WorkloadDataPoint, currentTime time.Time) RollingStatistics {
	windows := []time.Duration{time.Hour, 6 * time.Hour, 24 * time.Hour}
	stats := RollingStatistics{}

	for i, window := range windows {
		windowStart := currentTime.Add(-window)

		var queueValues []float64
		var utilizationValues []float64

		for _, point := range data {
			if point.Timestamp.After(windowStart) && point.Timestamp.Before(currentTime) {
				queueValues = append(queueValues, float64(point.QueueLength))
				utilizationValues = append(utilizationValues, point.AvgUtilization)
			}
		}

		// Calculate moving averages
		queueMA := calculateMean(queueValues)
		utilizationMA := calculateMean(utilizationValues)
		utilizationStd := calculateStandardDeviation(utilizationValues)

		// Assign to appropriate window
		switch i {
		case 0: // 1h
			stats.QueueLengthMA1h = queueMA
			stats.UtilizationMA1h = utilizationMA
			stats.UtilizationStd1h = utilizationStd
		case 1: // 6h
			stats.QueueLengthMA6h = queueMA
			stats.UtilizationMA6h = utilizationMA
			stats.UtilizationStd6h = utilizationStd
		case 2: // 24h
			stats.QueueLengthMA24h = queueMA
			stats.UtilizationMA24h = utilizationMA
			stats.UtilizationStd24h = utilizationStd
		}
	}

	return stats
}

// ComputeLagFeatures calculates lag features for specified time offsets
func (fe *FeatureEngineer) ComputeLagFeatures(data []WorkloadDataPoint, currentTime time.Time) LagFeatures {
	lagOffsets := []time.Duration{time.Hour, 6 * time.Hour, 24 * time.Hour}
	features := LagFeatures{}

	for i, offset := range lagOffsets {
		targetTime := currentTime.Add(-offset)

		// Find the closest data point to the target time
		var closestPoint *WorkloadDataPoint
		minDiff := time.Hour * 24 // Initialize with large value

		for _, point := range data {
			diff := absDuration(point.Timestamp.Sub(targetTime))
			if diff < minDiff {
				minDiff = diff
				closestPoint = &point
			}
		}

		if closestPoint != nil {
			switch i {
			case 0: // 1h lag
				features.QueueLengthLag1h = closestPoint.QueueLength
				features.UtilizationLag1h = closestPoint.AvgUtilization
			case 1: // 6h lag
				features.QueueLengthLag6h = closestPoint.QueueLength
				features.UtilizationLag6h = closestPoint.AvgUtilization
			case 2: // 24h lag
				features.QueueLengthLag24h = closestPoint.QueueLength
				features.UtilizationLag24h = closestPoint.AvgUtilization
			}
		}
	}

	return features
}

// ComputeRateOfChange calculates rate of change for key metrics
func (fe *FeatureEngineer) ComputeRateOfChange(data []WorkloadDataPoint, currentTime time.Time) RateOfChangeFeatures {
	if len(data) < 2 {
		return RateOfChangeFeatures{}
	}

	// Find data points from 1 hour ago and current
	oneHourAgo := currentTime.Add(-time.Hour)

	var currentPoint, pastPoint *WorkloadDataPoint

	// Find closest points to current time and one hour ago
	for i := len(data) - 1; i >= 0; i-- {
		point := &data[i]
		if currentPoint == nil && point.Timestamp.Before(currentTime) {
			currentPoint = point
		}
		if pastPoint == nil && point.Timestamp.Before(oneHourAgo) {
			pastPoint = point
			break
		}
	}

	if currentPoint == nil || pastPoint == nil {
		return RateOfChangeFeatures{}
	}

	timeDiff := currentPoint.Timestamp.Sub(pastPoint.Timestamp).Hours()
	if timeDiff == 0 {
		return RateOfChangeFeatures{}
	}

	return RateOfChangeFeatures{
		QueueLengthRateOfChange: (float64(currentPoint.QueueLength) - float64(pastPoint.QueueLength)) / timeDiff,
		UtilizationRateOfChange: (currentPoint.AvgUtilization - pastPoint.AvgUtilization) / timeDiff,
		ActiveTasksRateOfChange: (float64(currentPoint.ActiveTasks) - float64(pastPoint.ActiveTasks)) / timeDiff,
	}
}

// ComputeFeatureInteractions calculates interaction features between variables
func (fe *FeatureEngineer) ComputeFeatureInteractions(queueLength int, utilization float64, activeTasks int, nodesActive int) FeatureInteractions {
	var tasksPerNodeRatio float64
	if nodesActive > 0 {
		tasksPerNodeRatio = float64(activeTasks) / float64(nodesActive)
	}

	// Memory pressure index (heuristic based on queue and utilization)
	memoryPressureIndex := float64(queueLength) * utilization / 100.0

	// Load variability index (based on queue vs active tasks ratio)
	var loadVariabilityIndex float64
	if activeTasks > 0 {
		loadVariabilityIndex = float64(queueLength) / float64(activeTasks)
	}

	return FeatureInteractions{
		QueueUtilizationProduct: float64(queueLength) * utilization,
		TasksPerNodeRatio:       tasksPerNodeRatio,
		MemoryPressureIndex:     memoryPressureIndex,
		LoadVariabilityIndex:    loadVariabilityIndex,
	}
}

// ComputePolynomialFeatures calculates polynomial features for key metrics
func (fe *FeatureEngineer) ComputePolynomialFeatures(utilization float64, queueLength int) PolynomialFeatures {
	queueFloat := float64(queueLength)

	return PolynomialFeatures{
		UtilizationSquared: utilization * utilization,
		QueueLengthSquared: queueFloat * queueFloat,
		QueueLengthCubed:   queueFloat * queueFloat * queueFloat,
	}
}

// ComputeTimeSeriesDecomposition performs basic time series decomposition
func (fe *FeatureEngineer) ComputeTimeSeriesDecomposition(data []WorkloadDataPoint, currentValue float64) TimeSeriesDecomposition {
	if len(data) < 24 { // Need at least 24 points for meaningful decomposition
		return TimeSeriesDecomposition{
			TrendComponent:    currentValue,
			SeasonalComponent: 0,
			ResidualComponent: 0,
		}
	}

	// Extract utilization values
	values := make([]float64, len(data))
	for i, point := range data {
		values[i] = point.AvgUtilization
	}

	// Simple trend calculation (linear trend over the period)
	trend := calculateLinearTrend(values)

	// Simple seasonal component (difference from hourly average)
	seasonal := calculateSeasonalComponent(data, currentValue)

	// Residual is what's left after removing trend and seasonal
	residual := currentValue - trend - seasonal

	return TimeSeriesDecomposition{
		TrendComponent:    trend,
		SeasonalComponent: seasonal,
		ResidualComponent: residual,
	}
}

// ExtractAllFeatures computes all enhanced features for a given data point
func (fe *FeatureEngineer) ExtractAllFeatures(basePoint WorkloadDataPoint, historicalData []WorkloadDataPoint) WorkloadDataPoint {
	// Start with the base point
	enhanced := basePoint

	// Compute temporal features
	temporal := fe.ComputeTemporalFeatures(basePoint.Timestamp)
	enhanced.HourOfDay = temporal.HourOfDay
	enhanced.DayOfWeek = temporal.DayOfWeek
	enhanced.DayOfMonth = temporal.DayOfMonth
	enhanced.MonthOfYear = temporal.MonthOfYear
	enhanced.IsWeekend = temporal.IsWeekend
	enhanced.IsHoliday = temporal.IsHoliday
	enhanced.HourSin = temporal.HourSin
	enhanced.HourCos = temporal.HourCos
	enhanced.DayOfWeekSin = temporal.DayOfWeekSin
	enhanced.DayOfWeekCos = temporal.DayOfWeekCos

	// Compute rolling statistics
	rolling := fe.ComputeRollingStatistics(historicalData, basePoint.Timestamp)
	enhanced.QueueLengthMA1h = rolling.QueueLengthMA1h
	enhanced.QueueLengthMA6h = rolling.QueueLengthMA6h
	enhanced.QueueLengthMA24h = rolling.QueueLengthMA24h
	enhanced.UtilizationMA1h = rolling.UtilizationMA1h
	enhanced.UtilizationMA6h = rolling.UtilizationMA6h
	enhanced.UtilizationMA24h = rolling.UtilizationMA24h
	enhanced.UtilizationStd1h = rolling.UtilizationStd1h
	enhanced.UtilizationStd6h = rolling.UtilizationStd6h
	enhanced.UtilizationStd24h = rolling.UtilizationStd24h

	// Compute lag features
	lag := fe.ComputeLagFeatures(historicalData, basePoint.Timestamp)
	enhanced.QueueLengthLag1h = lag.QueueLengthLag1h
	enhanced.QueueLengthLag6h = lag.QueueLengthLag6h
	enhanced.QueueLengthLag24h = lag.QueueLengthLag24h
	enhanced.UtilizationLag1h = lag.UtilizationLag1h
	enhanced.UtilizationLag6h = lag.UtilizationLag6h
	enhanced.UtilizationLag24h = lag.UtilizationLag24h

	// Compute rate of change features
	rateOfChange := fe.ComputeRateOfChange(historicalData, basePoint.Timestamp)
	enhanced.QueueLengthRateOfChange = rateOfChange.QueueLengthRateOfChange
	enhanced.UtilizationRateOfChange = rateOfChange.UtilizationRateOfChange
	enhanced.ActiveTasksRateOfChange = rateOfChange.ActiveTasksRateOfChange

	// Compute feature interactions
	interactions := fe.ComputeFeatureInteractions(basePoint.QueueLength, basePoint.AvgUtilization, basePoint.ActiveTasks, basePoint.NodesActive)
	enhanced.QueueUtilizationProduct = interactions.QueueUtilizationProduct
	enhanced.TasksPerNodeRatio = interactions.TasksPerNodeRatio
	enhanced.MemoryPressureIndex = interactions.MemoryPressureIndex
	enhanced.LoadVariabilityIndex = interactions.LoadVariabilityIndex

	// Compute polynomial features
	polynomial := fe.ComputePolynomialFeatures(basePoint.AvgUtilization, basePoint.QueueLength)
	enhanced.UtilizationSquared = polynomial.UtilizationSquared
	enhanced.QueueLengthSquared = polynomial.QueueLengthSquared
	enhanced.QueueLengthCubed = polynomial.QueueLengthCubed

	// Compute time series decomposition
	decomposition := fe.ComputeTimeSeriesDecomposition(historicalData, basePoint.AvgUtilization)
	enhanced.TrendComponent = decomposition.TrendComponent
	enhanced.SeasonalComponent = decomposition.SeasonalComponent
	enhanced.ResidualComponent = decomposition.ResidualComponent

	return enhanced
}

// NormalizeFeatures applies normalization to selected features
func (fe *FeatureEngineer) NormalizeFeatures(data []WorkloadDataPoint) []WorkloadDataPoint {
	if len(data) == 0 {
		return data
	}

	normalized := make([]WorkloadDataPoint, len(data))
	copy(normalized, data)

	// Extract values for normalization
	var utilizationValues, queueValues []float64
	for _, point := range data {
		utilizationValues = append(utilizationValues, point.AvgUtilization)
		queueValues = append(queueValues, float64(point.QueueLength))
	}

	// Calculate normalization parameters
	utilizationMean := calculateMean(utilizationValues)
	utilizationStd := calculateStandardDeviation(utilizationValues)
	queueMean := calculateMean(queueValues)
	queueStd := calculateStandardDeviation(queueValues)

	// Apply Z-score normalization
	for i := range normalized {
		if utilizationStd > 0 {
			normalized[i].AvgUtilization = (normalized[i].AvgUtilization - utilizationMean) / utilizationStd
		}
		if queueStd > 0 {
			queueNormalized := (float64(normalized[i].QueueLength) - queueMean) / queueStd
			normalized[i].QueueLength = int(queueNormalized)
		}
	}

	return normalized
}

// Feature structure definitions for intermediate calculations
type TemporalFeatures struct {
	HourOfDay    int
	DayOfWeek    int
	DayOfMonth   int
	MonthOfYear  int
	IsWeekend    bool
	IsHoliday    bool
	HourSin      float64
	HourCos      float64
	DayOfWeekSin float64
	DayOfWeekCos float64
}

type RollingStatistics struct {
	QueueLengthMA1h   float64
	QueueLengthMA6h   float64
	QueueLengthMA24h  float64
	UtilizationMA1h   float64
	UtilizationMA6h   float64
	UtilizationMA24h  float64
	UtilizationStd1h  float64
	UtilizationStd6h  float64
	UtilizationStd24h float64
}

type LagFeatures struct {
	QueueLengthLag1h  int
	QueueLengthLag6h  int
	QueueLengthLag24h int
	UtilizationLag1h  float64
	UtilizationLag6h  float64
	UtilizationLag24h float64
}

type RateOfChangeFeatures struct {
	QueueLengthRateOfChange float64
	UtilizationRateOfChange float64
	ActiveTasksRateOfChange float64
}

type FeatureInteractions struct {
	QueueUtilizationProduct float64
	TasksPerNodeRatio       float64
	MemoryPressureIndex     float64
	LoadVariabilityIndex    float64
}

type PolynomialFeatures struct {
	UtilizationSquared float64
	QueueLengthSquared float64
	QueueLengthCubed   float64
}

type TimeSeriesDecomposition struct {
	TrendComponent    float64
	SeasonalComponent float64
	ResidualComponent float64
}

// Utility functions for statistical calculations
func calculateMean(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}

	sum := 0.0
	for _, v := range values {
		sum += v
	}
	return sum / float64(len(values))
}

func calculateStandardDeviation(values []float64) float64 {
	if len(values) <= 1 {
		return 0
	}

	mean := calculateMean(values)
	sumSquaredDiffs := 0.0

	for _, v := range values {
		diff := v - mean
		sumSquaredDiffs += diff * diff
	}

	variance := sumSquaredDiffs / float64(len(values)-1)
	return math.Sqrt(variance)
}

func calculateLinearTrend(values []float64) float64 {
	if len(values) < 2 {
		return 0
	}

	n := float64(len(values))
	sumX := 0.0
	sumY := 0.0
	sumXY := 0.0
	sumX2 := 0.0

	for i, y := range values {
		x := float64(i)
		sumX += x
		sumY += y
		sumXY += x * y
		sumX2 += x * x
	}

	// Calculate slope of linear trend
	denominator := n*sumX2 - sumX*sumX
	if denominator == 0 {
		return 0
	}

	slope := (n*sumXY - sumX*sumY) / denominator

	// Return trend value at the last point
	lastX := float64(len(values) - 1)
	intercept := (sumY - slope*sumX) / n
	return slope*lastX + intercept
}

func calculateSeasonalComponent(data []WorkloadDataPoint, currentValue float64) float64 {
	if len(data) == 0 {
		return 0
	}

	// Get current hour
	currentHour := data[len(data)-1].Timestamp.Hour()

	// Calculate average utilization for this hour across historical data
	var hourlyValues []float64
	for _, point := range data {
		if point.Timestamp.Hour() == currentHour {
			hourlyValues = append(hourlyValues, point.AvgUtilization)
		}
	}

	if len(hourlyValues) == 0 {
		return 0
	}

	hourlyAverage := calculateMean(hourlyValues)
	overallAverage := calculateMean(extractUtilizationValues(data))

	// Seasonal component is the difference from overall average
	return hourlyAverage - overallAverage
}

func extractUtilizationValues(data []WorkloadDataPoint) []float64 {
	values := make([]float64, len(data))
	for i, point := range data {
		values[i] = point.AvgUtilization
	}
	return values
}

func absDuration(d time.Duration) time.Duration {
	if d < 0 {
		return -d
	}
	return d
}

// FeatureHash implements feature hashing for categorical variables
func FeatureHash(value string, numBuckets int) int {
	hash := fnv.New32a()
	hash.Write([]byte(value))
	return int(hash.Sum32()) % numBuckets
}

// NormalizeFeatureVector applies min-max normalization to feature vectors
func (fe *FeatureEngineer) NormalizeFeatureVector(features []float64, mins, maxs []float64) []float64 {
	normalized := make([]float64, len(features))

	for i, feature := range features {
		if i < len(mins) && i < len(maxs) && maxs[i] != mins[i] {
			normalized[i] = (feature - mins[i]) / (maxs[i] - mins[i])
		} else {
			normalized[i] = feature
		}
	}

	return normalized
}

// StandardizeFeatureVector applies z-score standardization to feature vectors
func (fe *FeatureEngineer) StandardizeFeatureVector(features []float64, means, stds []float64) []float64 {
	standardized := make([]float64, len(features))

	for i, feature := range features {
		if i < len(means) && i < len(stds) && stds[i] != 0 {
			standardized[i] = (feature - means[i]) / stds[i]
		} else {
			standardized[i] = feature
		}
	}

	return standardized
}

// ExtractFeatureVector converts a WorkloadDataPoint to a feature vector
func (fe *FeatureEngineer) ExtractFeatureVector(dataPoint WorkloadDataPoint) []float64 {
	return []float64{
		// Basic metrics
		float64(dataPoint.QueueLength),
		dataPoint.AvgUtilization,
		float64(dataPoint.ActiveTasks),
		float64(dataPoint.NodesActive),

		// Temporal features
		float64(dataPoint.HourOfDay),
		float64(dataPoint.DayOfWeek),
		boolToFloat(dataPoint.IsWeekend),
		boolToFloat(dataPoint.IsHoliday),
		dataPoint.HourSin,
		dataPoint.HourCos,
		dataPoint.DayOfWeekSin,
		dataPoint.DayOfWeekCos,

		// Rolling statistics
		dataPoint.QueueLengthMA1h,
		dataPoint.QueueLengthMA6h,
		dataPoint.QueueLengthMA24h,
		dataPoint.UtilizationMA1h,
		dataPoint.UtilizationMA6h,
		dataPoint.UtilizationMA24h,
		dataPoint.UtilizationStd1h,
		dataPoint.UtilizationStd6h,
		dataPoint.UtilizationStd24h,

		// Lag features
		float64(dataPoint.QueueLengthLag1h),
		float64(dataPoint.QueueLengthLag6h),
		float64(dataPoint.QueueLengthLag24h),
		dataPoint.UtilizationLag1h,
		dataPoint.UtilizationLag6h,
		dataPoint.UtilizationLag24h,

		// Rate of change
		dataPoint.QueueLengthRateOfChange,
		dataPoint.UtilizationRateOfChange,
		dataPoint.ActiveTasksRateOfChange,

		// Application-specific metrics
		dataPoint.AvgTaskDuration.Seconds(),
		float64(dataPoint.HighPriorityTasks),
		float64(dataPoint.MediumPriorityTasks),
		float64(dataPoint.LowPriorityTasks),
		dataPoint.AvgMemoryUsagePerTask,
		dataPoint.PeakMemoryUsagePerTask,
		dataPoint.AvgGPUUtilPerTask,
		dataPoint.PeakGPUUtilPerTask,

		// User/Project activity
		float64(dataPoint.ActiveUsers),
		float64(dataPoint.ActiveProjects),
		dataPoint.TaskSubmissionRate,

		// External factors - System health
		dataPoint.CPUUtilization,
		dataPoint.MemoryUtilization,
		dataPoint.NetworkBandwidthUsage,
		dataPoint.StorageIOPS,
		dataPoint.StorageThroughput,

		// External factors - Cluster state
		float64(dataPoint.NodesInMaintenance),
		float64(dataPoint.RecentScaleUpEvents),
		float64(dataPoint.RecentScaleDownEvents),
		dataPoint.TimeSinceLastScaling.Hours(),
		dataPoint.ClusterHealthScore,

		// External factors - Environmental
		dataPoint.DataCenterTemperature,
		dataPoint.PowerUsageEffectiveness,
		dataPoint.CoolingEfficiency,

		// Cost-related features
		dataPoint.CurrentSpotPrice,
		dataPoint.ElectricityCost,
		dataPoint.TimeOfUsePricing,

		// Feature interactions
		dataPoint.QueueUtilizationProduct,
		dataPoint.TasksPerNodeRatio,
		dataPoint.MemoryPressureIndex,
		dataPoint.LoadVariabilityIndex,

		// Polynomial features
		dataPoint.UtilizationSquared,
		dataPoint.QueueLengthSquared,
		dataPoint.QueueLengthCubed,

		// Time series decomposition
		dataPoint.TrendComponent,
		dataPoint.SeasonalComponent,
		dataPoint.ResidualComponent,
	}
}

// boolToFloat converts boolean to float64
func boolToFloat(b bool) float64 {
	if b {
		return 1.0
	}
	return 0.0
}

// GetFeatureNames returns the names of all features in the feature vector
func (fe *FeatureEngineer) GetFeatureNames() []string {
	return []string{
		"queue_length", "avg_utilization", "active_tasks", "nodes_active",
		"hour_of_day", "day_of_week", "is_weekend", "is_holiday",
		"hour_sin", "hour_cos", "day_of_week_sin", "day_of_week_cos",
		"queue_length_ma_1h", "queue_length_ma_6h", "queue_length_ma_24h",
		"utilization_ma_1h", "utilization_ma_6h", "utilization_ma_24h",
		"utilization_std_1h", "utilization_std_6h", "utilization_std_24h",
		"queue_length_lag_1h", "queue_length_lag_6h", "queue_length_lag_24h",
		"utilization_lag_1h", "utilization_lag_6h", "utilization_lag_24h",
		"queue_length_rate_of_change", "utilization_rate_of_change", "active_tasks_rate_of_change",
		"avg_task_duration", "high_priority_tasks", "medium_priority_tasks", "low_priority_tasks",
		"avg_memory_usage_per_task", "peak_memory_usage_per_task", "avg_gpu_util_per_task", "peak_gpu_util_per_task",
		"active_users", "active_projects", "task_submission_rate",
		"cpu_utilization", "memory_utilization", "network_bandwidth_usage", "storage_iops", "storage_throughput",
		"nodes_in_maintenance", "recent_scale_up_events", "recent_scale_down_events", "time_since_last_scaling", "cluster_health_score",
		"datacenter_temperature", "power_usage_effectiveness", "cooling_efficiency",
		"current_spot_price", "electricity_cost", "time_of_use_pricing",
		"queue_utilization_product", "tasks_per_node_ratio", "memory_pressure_index", "load_variability_index",
		"utilization_squared", "queue_length_squared", "queue_length_cubed",
		"trend_component", "seasonal_component", "residual_component",
	}
}
