package gpu

import (
	"fmt"
	"math"
	"sort"
	"sync"
	"time"
)

// NewWorkloadPredictor creates a new workload predictor
func NewWorkloadPredictor(config PredictionConfig) *WorkloadPredictor {
	predictor := &WorkloadPredictor{
		config:         config,
		historicalData: make([]WorkloadDataPoint, 0),
	}

	// Initialize prediction model based on config
	switch config.ModelType {
	case "linear_regression":
		predictor.model = NewLinearRegressionModel()
	case "moving_average":
		predictor.model = NewMovingAverageModel()
	case "exponential_smoothing":
		predictor.model = NewExponentialSmoothingModel()
	case "arima":
		predictor.model = NewARIMAModel()
	case "sarima":
		predictor.model = NewSARIMAModel()
	case "prophet":
		predictor.model = NewProphetModel()
	case "holt_winters":
		predictor.model = NewHoltWintersModel()
	case "ensemble":
		predictor.model = NewEnsembleModel()
	case "lstm":
		predictor.model = NewLSTMPredictionModel()
	case "gru":
		predictor.model = NewGRUPredictionModel()
	case "deep_learning_ensemble":
		predictor.model = NewDeepLearningEnsemblePredictionModel()
	case "advanced_ensemble":
		config := EnsembleConfig{
			Method:               WeightedAveraging,
			IncludeTimeSeries:    true,
			IncludeDeepLearning:  true,
			AdaptiveLearningRate: 0.1,
			MetaLearnerType:      "linear",
		}
		predictor.model = NewAdvancedEnsemblePredictionModel(config)
	case "stacking_ensemble":
		config := EnsembleConfig{
			Method:               Stacking,
			IncludeTimeSeries:    true,
			IncludeDeepLearning:  true,
			AdaptiveLearningRate: 0.1,
			MetaLearnerType:      "lstm",
		}
		predictor.model = NewAdvancedEnsemblePredictionModel(config)
	case "bayesian_ensemble":
		config := EnsembleConfig{
			Method:               BayesianModelAveraging,
			IncludeTimeSeries:    true,
			IncludeDeepLearning:  true,
			AdaptiveLearningRate: 0.1,
			MetaLearnerType:      "linear",
		}
		predictor.model = NewAdvancedEnsemblePredictionModel(config)
	default:
		predictor.model = NewLinearRegressionModel()
	}

	return predictor
}

// AddDataPoint adds a new workload data point to the historical data
func (wp *WorkloadPredictor) AddDataPoint(dataPoint WorkloadDataPoint) {
	wp.mu.Lock()
	defer wp.mu.Unlock()

	wp.historicalData = append(wp.historicalData, dataPoint)

	// Remove old data points outside the history window
	cutoff := time.Now().Add(-wp.config.HistoryWindow)
	var filteredData []WorkloadDataPoint
	for _, point := range wp.historicalData {
		if point.Timestamp.After(cutoff) {
			filteredData = append(filteredData, point)
		}
	}
	wp.historicalData = filteredData

	// Retrain model if we have enough data
	if len(wp.historicalData) >= wp.config.MinDataPoints {
		wp.model.Train(wp.historicalData)
	}
}

// GetPrediction generates a workload prediction for the specified horizon
func (wp *WorkloadPredictor) GetPrediction(horizon time.Duration) (WorkloadPrediction, error) {
	wp.mu.RLock()
	defer wp.mu.RUnlock()

	if len(wp.historicalData) < wp.config.MinDataPoints {
		return WorkloadPrediction{}, fmt.Errorf("insufficient data points: have %d, need %d",
			len(wp.historicalData), wp.config.MinDataPoints)
	}

	return wp.model.Predict(horizon)
}

// GetHistoricalData returns a copy of the historical data
func (wp *WorkloadPredictor) GetHistoricalData() []WorkloadDataPoint {
	wp.mu.RLock()
	defer wp.mu.RUnlock()

	data := make([]WorkloadDataPoint, len(wp.historicalData))
	copy(data, wp.historicalData)
	return data
}

// LinearRegressionModel implements linear regression prediction
type LinearRegressionModel struct {
	slope         float64
	intercept     float64
	accuracy      float64
	lastTrained   time.Time
	lastDataTime  time.Time
	lastDataValue float64
	mu            sync.RWMutex
}

// NewLinearRegressionModel creates a new linear regression model
func NewLinearRegressionModel() *LinearRegressionModel {
	return &LinearRegressionModel{}
}

// Train trains the linear regression model on the provided data
func (lrm *LinearRegressionModel) Train(data []WorkloadDataPoint) error {
	lrm.mu.Lock()
	defer lrm.mu.Unlock()

	if len(data) < 2 {
		return fmt.Errorf("need at least 2 data points for linear regression")
	}

	// Convert timestamps to numeric values (minutes since first data point)
	baseTime := data[0].Timestamp
	var x, y []float64

	for _, point := range data {
		minutes := point.Timestamp.Sub(baseTime).Minutes()
		x = append(x, minutes)
		y = append(y, float64(point.QueueLength))
	}

	// Calculate linear regression coefficients
	lrm.slope, lrm.intercept = calculateLinearRegression(x, y)
	lrm.accuracy = calculateAccuracy(x, y, lrm.slope, lrm.intercept)
	lrm.lastTrained = time.Now()

	// Store the last data point for prediction
	lastIndex := len(data) - 1
	lrm.lastDataTime = data[lastIndex].Timestamp
	lrm.lastDataValue = float64(data[lastIndex].QueueLength)

	return nil
}

// Predict generates a prediction using linear regression
func (lrm *LinearRegressionModel) Predict(horizon time.Duration) (WorkloadPrediction, error) {
	lrm.mu.RLock()
	defer lrm.mu.RUnlock()

	if lrm.lastTrained.IsZero() {
		return WorkloadPrediction{}, fmt.Errorf("model not trained")
	}

	// Predict queue length at the horizon
	// Predict from the last known data point plus the horizon
	minutesFromLastData := horizon.Minutes()
	predictedQueue := lrm.lastDataValue + lrm.slope*minutesFromLastData

	// Ensure non-negative prediction
	if predictedQueue < 0 {
		predictedQueue = 0
	}

	// Estimate nodes needed (simple heuristic)
	tasksPerNode := 10.0
	predictedNodes := int(math.Ceil(predictedQueue / tasksPerNode))

	// Determine recommended action
	var action ScalingAction
	if predictedQueue > 100 {
		action = ScalingActionScaleUp
	} else if predictedQueue < 20 {
		action = ScalingActionScaleDown
	} else {
		action = ScalingActionNone
	}

	return WorkloadPrediction{
		Timestamp:         time.Now().Add(horizon),
		PredictedQueue:    int(predictedQueue),
		PredictedTasks:    int(predictedQueue * 0.8), // Assume 80% of queue becomes active tasks
		PredictedNodes:    predictedNodes,
		Confidence:        lrm.accuracy,
		RecommendedAction: action,
	}, nil
}

// GetAccuracy returns the current model accuracy
func (lrm *LinearRegressionModel) GetAccuracy() float64 {
	lrm.mu.RLock()
	defer lrm.mu.RUnlock()
	return lrm.accuracy
}

// MovingAverageModel implements moving average prediction
type MovingAverageModel struct {
	windowSize  int
	accuracy    float64
	lastTrained time.Time
	mu          sync.RWMutex
}

// NewMovingAverageModel creates a new moving average model
func NewMovingAverageModel() *MovingAverageModel {
	return &MovingAverageModel{
		windowSize: 10, // Default window size
	}
}

// Train trains the moving average model
func (mam *MovingAverageModel) Train(data []WorkloadDataPoint) error {
	mam.mu.Lock()
	defer mam.mu.Unlock()

	if len(data) < mam.windowSize {
		return fmt.Errorf("need at least %d data points for moving average", mam.windowSize)
	}

	// Calculate accuracy based on how well the model would have predicted historical data
	var errors []float64
	for i := mam.windowSize; i < len(data); i++ {
		// Calculate moving average for the window before this point
		sum := 0.0
		for j := i - mam.windowSize; j < i; j++ {
			sum += float64(data[j].QueueLength)
		}
		predicted := sum / float64(mam.windowSize)
		actual := float64(data[i].QueueLength)

		// Calculate percentage error
		if actual > 0 {
			error := math.Abs(predicted-actual) / actual
			errors = append(errors, error)
		}
	}

	// Calculate average accuracy
	if len(errors) > 0 {
		avgError := 0.0
		for _, err := range errors {
			avgError += err
		}
		avgError /= float64(len(errors))
		mam.accuracy = math.Max(0, 1.0-avgError) // Convert error to accuracy
	} else {
		mam.accuracy = 0.5 // Default accuracy if we can't calculate
	}

	mam.lastTrained = time.Now()
	return nil
}

// Predict generates a prediction using moving average
func (mam *MovingAverageModel) Predict(horizon time.Duration) (WorkloadPrediction, error) {
	mam.mu.RLock()
	defer mam.mu.RUnlock()

	if mam.lastTrained.IsZero() {
		return WorkloadPrediction{}, fmt.Errorf("model not trained")
	}

	// For moving average, we assume the prediction is the same as the current average
	// In a real implementation, this would use the most recent data points
	predictedQueue := 50.0 // Placeholder - would calculate from recent data

	// Estimate nodes needed
	tasksPerNode := 10.0
	predictedNodes := int(math.Ceil(predictedQueue / tasksPerNode))

	// Determine recommended action
	var action ScalingAction
	if predictedQueue > 100 {
		action = ScalingActionScaleUp
	} else if predictedQueue < 20 {
		action = ScalingActionScaleDown
	} else {
		action = ScalingActionNone
	}

	return WorkloadPrediction{
		Timestamp:         time.Now().Add(horizon),
		PredictedQueue:    int(predictedQueue),
		PredictedTasks:    int(predictedQueue * 0.8),
		PredictedNodes:    predictedNodes,
		Confidence:        mam.accuracy,
		RecommendedAction: action,
	}, nil
}

// GetAccuracy returns the current model accuracy
func (mam *MovingAverageModel) GetAccuracy() float64 {
	mam.mu.RLock()
	defer mam.mu.RUnlock()
	return mam.accuracy
}

// ExponentialSmoothingModel implements exponential smoothing prediction
type ExponentialSmoothingModel struct {
	alpha       float64
	lastValue   float64
	accuracy    float64
	lastTrained time.Time
	mu          sync.RWMutex
}

// NewExponentialSmoothingModel creates a new exponential smoothing model
func NewExponentialSmoothingModel() *ExponentialSmoothingModel {
	return &ExponentialSmoothingModel{
		alpha: 0.3, // Default smoothing parameter
	}
}

// Train trains the exponential smoothing model
func (esm *ExponentialSmoothingModel) Train(data []WorkloadDataPoint) error {
	esm.mu.Lock()
	defer esm.mu.Unlock()

	if len(data) < 2 {
		return fmt.Errorf("need at least 2 data points for exponential smoothing")
	}

	// Sort data by timestamp
	sort.Slice(data, func(i, j int) bool {
		return data[i].Timestamp.Before(data[j].Timestamp)
	})

	// Initialize with first value
	smoothed := float64(data[0].QueueLength)
	var errors []float64

	// Apply exponential smoothing
	for i := 1; i < len(data); i++ {
		actual := float64(data[i].QueueLength)

		// Calculate error for accuracy measurement
		if actual > 0 {
			error := math.Abs(smoothed-actual) / actual
			errors = append(errors, error)
		}

		// Update smoothed value
		smoothed = esm.alpha*actual + (1-esm.alpha)*smoothed
	}

	esm.lastValue = smoothed

	// Calculate accuracy
	if len(errors) > 0 {
		avgError := 0.0
		for _, err := range errors {
			avgError += err
		}
		avgError /= float64(len(errors))
		esm.accuracy = math.Max(0, 1.0-avgError)
	} else {
		esm.accuracy = 0.5
	}

	esm.lastTrained = time.Now()
	return nil
}

// Predict generates a prediction using exponential smoothing
func (esm *ExponentialSmoothingModel) Predict(horizon time.Duration) (WorkloadPrediction, error) {
	esm.mu.RLock()
	defer esm.mu.RUnlock()

	if esm.lastTrained.IsZero() {
		return WorkloadPrediction{}, fmt.Errorf("model not trained")
	}

	// For exponential smoothing, the prediction is the last smoothed value
	predictedQueue := esm.lastValue

	// Ensure non-negative prediction
	if predictedQueue < 0 {
		predictedQueue = 0
	}

	// Estimate nodes needed
	tasksPerNode := 10.0
	predictedNodes := int(math.Ceil(predictedQueue / tasksPerNode))

	// Determine recommended action
	var action ScalingAction
	if predictedQueue > 100 {
		action = ScalingActionScaleUp
	} else if predictedQueue < 20 {
		action = ScalingActionScaleDown
	} else {
		action = ScalingActionNone
	}

	return WorkloadPrediction{
		Timestamp:         time.Now().Add(horizon),
		PredictedQueue:    int(predictedQueue),
		PredictedTasks:    int(predictedQueue * 0.8),
		PredictedNodes:    predictedNodes,
		Confidence:        esm.accuracy,
		RecommendedAction: action,
	}, nil
}

// GetAccuracy returns the current model accuracy
func (esm *ExponentialSmoothingModel) GetAccuracy() float64 {
	esm.mu.RLock()
	defer esm.mu.RUnlock()
	return esm.accuracy
}

// Helper functions for linear regression
func calculateLinearRegression(x, y []float64) (slope, intercept float64) {
	if len(x) != len(y) || len(x) < 2 {
		return 0, 0
	}

	n := float64(len(x))
	var sumX, sumY, sumXY, sumX2 float64

	for i := 0; i < len(x); i++ {
		sumX += x[i]
		sumY += y[i]
		sumXY += x[i] * y[i]
		sumX2 += x[i] * x[i]
	}

	// Calculate slope and intercept
	denominator := n*sumX2 - sumX*sumX
	if denominator == 0 {
		return 0, sumY / n // If no variance in x, return average y as intercept
	}

	slope = (n*sumXY - sumX*sumY) / denominator
	intercept = (sumY - slope*sumX) / n

	return slope, intercept
}

func calculateAccuracy(x, y []float64, slope, intercept float64) float64 {
	if len(x) != len(y) || len(x) == 0 {
		return 0.0
	}

	var sumSquaredErrors, sumSquaredTotal float64
	mean := 0.0
	for _, val := range y {
		mean += val
	}
	mean /= float64(len(y))

	for i := 0; i < len(x); i++ {
		predicted := slope*x[i] + intercept
		actual := y[i]
		sumSquaredErrors += (actual - predicted) * (actual - predicted)
		sumSquaredTotal += (actual - mean) * (actual - mean)
	}

	if sumSquaredTotal == 0 {
		return 1.0 // Perfect prediction if no variance
	}

	rSquared := 1.0 - (sumSquaredErrors / sumSquaredTotal)
	return math.Max(0, rSquared) // Ensure non-negative accuracy
}

// ARIMAModel implements ARIMA (AutoRegressive Integrated Moving Average) prediction
type ARIMAModel struct {
	p           int       // AR order
	d           int       // Differencing order
	q           int       // MA order
	arParams    []float64 // AR parameters
	maParams    []float64 // MA parameters
	residuals   []float64 // Model residuals
	accuracy    float64
	lastTrained time.Time
	lastValues  []float64 // Store recent values for prediction
	mu          sync.RWMutex
}

// NewARIMAModel creates a new ARIMA model with default parameters
func NewARIMAModel() *ARIMAModel {
	return &ARIMAModel{
		p: 2, // Default AR order
		d: 1, // Default differencing order
		q: 1, // Default MA order
	}
}

// Train trains the ARIMA model on the provided data
func (am *ARIMAModel) Train(data []WorkloadDataPoint) error {
	am.mu.Lock()
	defer am.mu.Unlock()

	if len(data) < am.p+am.d+am.q+5 {
		return fmt.Errorf("need at least %d data points for ARIMA(%d,%d,%d)",
			am.p+am.d+am.q+5, am.p, am.d, am.q)
	}

	// Extract time series values
	values := make([]float64, len(data))
	for i, point := range data {
		values[i] = float64(point.QueueLength)
	}

	// Apply differencing
	diffValues := am.difference(values, am.d)

	// Simplified ARIMA estimation using least squares
	am.arParams, am.maParams, am.residuals = am.estimateParameters(diffValues)

	// Calculate model accuracy
	am.accuracy = am.calculateARIMAAccuracy(diffValues)
	am.lastTrained = time.Now()

	// Store recent values for prediction
	recentCount := am.p + am.q + am.d
	if len(values) >= recentCount {
		am.lastValues = values[len(values)-recentCount:]
	} else {
		am.lastValues = values
	}

	return nil
}

// Predict generates a prediction using ARIMA
func (am *ARIMAModel) Predict(horizon time.Duration) (WorkloadPrediction, error) {
	am.mu.RLock()
	defer am.mu.RUnlock()

	if am.lastTrained.IsZero() {
		return WorkloadPrediction{}, fmt.Errorf("ARIMA model not trained")
	}

	// Simple ARIMA prediction (simplified implementation)
	predictedValue := am.forecastNextValue()

	// Ensure non-negative prediction
	if predictedValue < 0 {
		predictedValue = 0
	}

	// Estimate nodes needed
	tasksPerNode := 10.0
	predictedNodes := int(math.Ceil(predictedValue / tasksPerNode))

	// Determine recommended action
	var action ScalingAction
	if predictedValue > 100 {
		action = ScalingActionScaleUp
	} else if predictedValue < 20 {
		action = ScalingActionScaleDown
	} else {
		action = ScalingActionNone
	}

	return WorkloadPrediction{
		Timestamp:         time.Now().Add(horizon),
		PredictedQueue:    int(predictedValue),
		PredictedTasks:    int(predictedValue * 0.8),
		PredictedNodes:    predictedNodes,
		Confidence:        am.accuracy,
		RecommendedAction: action,
	}, nil
}

// GetAccuracy returns the current model accuracy
func (am *ARIMAModel) GetAccuracy() float64 {
	am.mu.RLock()
	defer am.mu.RUnlock()
	return am.accuracy
}

// difference applies differencing to the time series
func (am *ARIMAModel) difference(values []float64, order int) []float64 {
	result := values
	for i := 0; i < order; i++ {
		if len(result) <= 1 {
			break
		}
		diffed := make([]float64, len(result)-1)
		for j := 1; j < len(result); j++ {
			diffed[j-1] = result[j] - result[j-1]
		}
		result = diffed
	}
	return result
}

// estimateParameters estimates ARIMA parameters using simplified method
func (am *ARIMAModel) estimateParameters(diffValues []float64) ([]float64, []float64, []float64) {
	// Simplified parameter estimation - in production, use more sophisticated methods
	arParams := make([]float64, am.p)
	maParams := make([]float64, am.q)
	residuals := make([]float64, len(diffValues))

	// Initialize with simple values
	for i := range arParams {
		arParams[i] = 0.1 / float64(i+1)
	}
	for i := range maParams {
		maParams[i] = 0.1 / float64(i+1)
	}

	// Calculate residuals (simplified)
	for i := range diffValues {
		predicted := 0.0
		if i > 0 {
			predicted = diffValues[i-1] * 0.5 // Simplified prediction
		}
		residuals[i] = diffValues[i] - predicted
	}

	return arParams, maParams, residuals
}

// calculateARIMAAccuracy calculates model accuracy
func (am *ARIMAModel) calculateARIMAAccuracy(diffValues []float64) float64 {
	if len(diffValues) < 2 {
		return 0.5
	}

	// Calculate MAPE (Mean Absolute Percentage Error)
	var totalError float64
	validPoints := 0

	for i := 1; i < len(diffValues); i++ {
		actual := diffValues[i]
		predicted := diffValues[i-1] * 0.5 // Simplified prediction

		if math.Abs(actual) > 0.1 { // Avoid division by very small numbers
			error := math.Abs((actual - predicted) / actual)
			// Cap error at 1.0 to prevent extreme values
			if error > 1.0 {
				error = 1.0
			}
			totalError += error
			validPoints++
		}
	}

	if validPoints == 0 {
		return 0.5
	}

	mape := totalError / float64(validPoints)
	// Use a more conservative accuracy calculation
	accuracy := math.Max(0, 1.0-mape)
	if accuracy < 0.1 {
		accuracy = 0.1 // Minimum accuracy to avoid zero
	}
	return accuracy
}

// forecastNextValue forecasts the next value using ARIMA
func (am *ARIMAModel) forecastNextValue() float64 {
	if len(am.lastValues) == 0 {
		return 50.0 // Default value
	}

	// Simplified forecasting - in production, implement full ARIMA forecasting
	recent := am.lastValues[len(am.lastValues)-1]
	if len(am.lastValues) > 1 {
		trend := am.lastValues[len(am.lastValues)-1] - am.lastValues[len(am.lastValues)-2]
		return recent + trend*0.5 // Simple trend extrapolation
	}

	return recent
}

// SARIMAModel implements Seasonal ARIMA prediction
type SARIMAModel struct {
	*ARIMAModel
	seasonalP      int // Seasonal AR order
	seasonalD      int // Seasonal differencing order
	seasonalQ      int // Seasonal MA order
	seasonalPeriod int // Seasonal period (e.g., 24 for hourly data with daily seasonality)
	seasonalParams []float64
}

// NewSARIMAModel creates a new SARIMA model
func NewSARIMAModel() *SARIMAModel {
	return &SARIMAModel{
		ARIMAModel:     NewARIMAModel(),
		seasonalP:      1,
		seasonalD:      1,
		seasonalQ:      1,
		seasonalPeriod: 24, // Daily seasonality for hourly data
	}
}

// Train trains the SARIMA model
func (sm *SARIMAModel) Train(data []WorkloadDataPoint) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	minPoints := sm.p + sm.d + sm.q + sm.seasonalP + sm.seasonalD + sm.seasonalQ + sm.seasonalPeriod + 10
	if len(data) < minPoints {
		return fmt.Errorf("need at least %d data points for SARIMA", minPoints)
	}

	// Extract time series values
	values := make([]float64, len(data))
	for i, point := range data {
		values[i] = float64(point.QueueLength)
	}

	// Apply seasonal differencing first
	seasonalDiffValues := sm.seasonalDifference(values)

	// Apply regular differencing
	diffValues := sm.difference(seasonalDiffValues, sm.d)

	// Estimate parameters (simplified)
	sm.arParams, sm.maParams, sm.residuals = sm.estimateParameters(diffValues)
	sm.seasonalParams = sm.estimateSeasonalParameters(values)

	// Calculate accuracy
	sm.accuracy = sm.calculateSARIMAAccuracy(values)
	sm.lastTrained = time.Now()

	// Store recent values
	recentCount := sm.seasonalPeriod + sm.p + sm.q + sm.d
	if len(values) >= recentCount {
		sm.lastValues = values[len(values)-recentCount:]
	} else {
		sm.lastValues = values
	}

	return nil
}

// seasonalDifference applies seasonal differencing
func (sm *SARIMAModel) seasonalDifference(values []float64) []float64 {
	if len(values) <= sm.seasonalPeriod {
		return values
	}

	result := make([]float64, len(values)-sm.seasonalPeriod)
	for i := sm.seasonalPeriod; i < len(values); i++ {
		result[i-sm.seasonalPeriod] = values[i] - values[i-sm.seasonalPeriod]
	}
	return result
}

// estimateSeasonalParameters estimates seasonal parameters
func (sm *SARIMAModel) estimateSeasonalParameters(values []float64) []float64 {
	// Simplified seasonal parameter estimation
	params := make([]float64, sm.seasonalP+sm.seasonalQ)
	for i := range params {
		params[i] = 0.1 / float64(i+1)
	}
	return params
}

// calculateSARIMAAccuracy calculates SARIMA model accuracy
func (sm *SARIMAModel) calculateSARIMAAccuracy(values []float64) float64 {
	if len(values) < sm.seasonalPeriod+2 {
		return 0.5
	}

	// Calculate seasonal accuracy
	var totalError float64
	validPoints := 0

	for i := sm.seasonalPeriod; i < len(values); i++ {
		actual := values[i]
		// Simple seasonal prediction: use value from same position in previous season
		predicted := values[i-sm.seasonalPeriod]

		if actual > 0 {
			error := math.Abs((actual - predicted) / actual)
			totalError += error
			validPoints++
		}
	}

	if validPoints == 0 {
		return 0.5
	}

	mape := totalError / float64(validPoints)
	return math.Max(0, 1.0-mape)
}

// ProphetModel implements Facebook Prophet-like prediction
type ProphetModel struct {
	trend       TrendComponent
	seasonality SeasonalityComponent
	holidays    []Holiday
	accuracy    float64
	lastTrained time.Time
	lastValues  []float64
	timestamps  []time.Time
	mu          sync.RWMutex
}

// TrendComponent represents the trend component of Prophet
type TrendComponent struct {
	GrowthRate  float64
	ChangePoint time.Time
	Baseline    float64
}

// SeasonalityComponent represents seasonality in Prophet
type SeasonalityComponent struct {
	DailyAmplitude  float64
	WeeklyAmplitude float64
	YearlyAmplitude float64
	DailyPhase      float64
	WeeklyPhase     float64
	YearlyPhase     float64
}

// Holiday represents a holiday effect
type Holiday struct {
	Name   string
	Date   time.Time
	Effect float64
}

// NewProphetModel creates a new Prophet model
func NewProphetModel() *ProphetModel {
	return &ProphetModel{
		trend: TrendComponent{
			GrowthRate: 0.01,
			Baseline:   50.0,
		},
		seasonality: SeasonalityComponent{
			DailyAmplitude:  10.0,
			WeeklyAmplitude: 20.0,
			YearlyAmplitude: 5.0,
		},
	}
}

// Train trains the Prophet model
func (pm *ProphetModel) Train(data []WorkloadDataPoint) error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if len(data) < 14 { // Need at least 2 weeks of data
		return fmt.Errorf("need at least 14 data points for Prophet model")
	}

	// Extract values and timestamps
	values := make([]float64, len(data))
	timestamps := make([]time.Time, len(data))
	for i, point := range data {
		values[i] = float64(point.QueueLength)
		timestamps[i] = point.Timestamp
	}

	pm.lastValues = values
	pm.timestamps = timestamps

	// Fit trend component
	pm.trend = pm.fitTrend(values, timestamps)

	// Fit seasonality component
	pm.seasonality = pm.fitSeasonality(values, timestamps)

	// Calculate accuracy
	pm.accuracy = pm.calculateProphetAccuracy(values, timestamps)
	pm.lastTrained = time.Now()

	return nil
}

// Predict generates a prediction using Prophet
func (pm *ProphetModel) Predict(horizon time.Duration) (WorkloadPrediction, error) {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	if pm.lastTrained.IsZero() {
		return WorkloadPrediction{}, fmt.Errorf("Prophet model not trained")
	}

	futureTime := time.Now().Add(horizon)

	// Calculate trend component
	trendValue := pm.calculateTrend(futureTime)

	// Calculate seasonal component
	seasonalValue := pm.calculateSeasonality(futureTime)

	// Combine components
	predictedValue := trendValue + seasonalValue

	// Ensure non-negative prediction
	if predictedValue < 0 {
		predictedValue = 0
	}

	// Estimate nodes needed
	tasksPerNode := 10.0
	predictedNodes := int(math.Ceil(predictedValue / tasksPerNode))

	// Determine recommended action
	var action ScalingAction
	if predictedValue > 100 {
		action = ScalingActionScaleUp
	} else if predictedValue < 20 {
		action = ScalingActionScaleDown
	} else {
		action = ScalingActionNone
	}

	return WorkloadPrediction{
		Timestamp:         futureTime,
		PredictedQueue:    int(predictedValue),
		PredictedTasks:    int(predictedValue * 0.8),
		PredictedNodes:    predictedNodes,
		Confidence:        pm.accuracy,
		RecommendedAction: action,
	}, nil
}

// GetAccuracy returns the current model accuracy
func (pm *ProphetModel) GetAccuracy() float64 {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	return pm.accuracy
}

// fitTrend fits the trend component
func (pm *ProphetModel) fitTrend(values []float64, timestamps []time.Time) TrendComponent {
	if len(values) < 2 {
		return pm.trend
	}

	// Simple linear trend fitting
	n := float64(len(values))
	sumX, sumY, sumXY, sumX2 := 0.0, 0.0, 0.0, 0.0

	baseTime := timestamps[0]
	for i, value := range values {
		x := timestamps[i].Sub(baseTime).Hours()
		sumX += x
		sumY += value
		sumXY += x * value
		sumX2 += x * x
	}

	slope := (n*sumXY - sumX*sumY) / (n*sumX2 - sumX*sumX)
	intercept := (sumY - slope*sumX) / n

	return TrendComponent{
		GrowthRate:  slope,
		Baseline:    intercept,
		ChangePoint: baseTime,
	}
}

// fitSeasonality fits the seasonality component
func (pm *ProphetModel) fitSeasonality(values []float64, timestamps []time.Time) SeasonalityComponent {
	// Simplified seasonality fitting
	// In production, use Fourier series fitting

	dailyAmp := pm.calculateDailySeasonality(values, timestamps)
	weeklyAmp := pm.calculateWeeklySeasonality(values, timestamps)

	return SeasonalityComponent{
		DailyAmplitude:  dailyAmp,
		WeeklyAmplitude: weeklyAmp,
		YearlyAmplitude: pm.seasonality.YearlyAmplitude, // Keep existing
		DailyPhase:      0.0,
		WeeklyPhase:     0.0,
		YearlyPhase:     0.0,
	}
}

// calculateDailySeasonality calculates daily seasonality amplitude
func (pm *ProphetModel) calculateDailySeasonality(values []float64, timestamps []time.Time) float64 {
	if len(values) < 24 {
		return 10.0 // Default amplitude
	}

	// Group by hour of day
	hourlyValues := make(map[int][]float64)
	for i, ts := range timestamps {
		hour := ts.Hour()
		hourlyValues[hour] = append(hourlyValues[hour], values[i])
	}

	// Calculate variance across hours
	hourlyMeans := make([]float64, 0, 24)
	for hour := 0; hour < 24; hour++ {
		if vals, exists := hourlyValues[hour]; exists && len(vals) > 0 {
			sum := 0.0
			for _, val := range vals {
				sum += val
			}
			hourlyMeans = append(hourlyMeans, sum/float64(len(vals)))
		}
	}

	if len(hourlyMeans) < 2 {
		return 10.0
	}

	// Calculate standard deviation as amplitude
	mean := 0.0
	for _, val := range hourlyMeans {
		mean += val
	}
	mean /= float64(len(hourlyMeans))

	variance := 0.0
	for _, val := range hourlyMeans {
		variance += (val - mean) * (val - mean)
	}
	variance /= float64(len(hourlyMeans))

	return math.Sqrt(variance)
}

// calculateWeeklySeasonality calculates weekly seasonality amplitude
func (pm *ProphetModel) calculateWeeklySeasonality(values []float64, timestamps []time.Time) float64 {
	if len(values) < 7 {
		return 20.0 // Default amplitude
	}

	// Group by day of week
	dailyValues := make(map[time.Weekday][]float64)
	for i, ts := range timestamps {
		day := ts.Weekday()
		dailyValues[day] = append(dailyValues[day], values[i])
	}

	// Calculate variance across days
	dailyMeans := make([]float64, 0, 7)
	for day := time.Sunday; day <= time.Saturday; day++ {
		if vals, exists := dailyValues[day]; exists && len(vals) > 0 {
			sum := 0.0
			for _, val := range vals {
				sum += val
			}
			dailyMeans = append(dailyMeans, sum/float64(len(vals)))
		}
	}

	if len(dailyMeans) < 2 {
		return 20.0
	}

	// Calculate standard deviation as amplitude
	mean := 0.0
	for _, val := range dailyMeans {
		mean += val
	}
	mean /= float64(len(dailyMeans))

	variance := 0.0
	for _, val := range dailyMeans {
		variance += (val - mean) * (val - mean)
	}
	variance /= float64(len(dailyMeans))

	return math.Sqrt(variance)
}

// calculateTrend calculates trend value at given time
func (pm *ProphetModel) calculateTrend(t time.Time) float64 {
	hours := t.Sub(pm.trend.ChangePoint).Hours()
	return pm.trend.Baseline + pm.trend.GrowthRate*hours
}

// calculateSeasonality calculates seasonal value at given time
func (pm *ProphetModel) calculateSeasonality(t time.Time) float64 {
	// Daily seasonality
	hourOfDay := float64(t.Hour()) + float64(t.Minute())/60.0
	dailySeasonal := pm.seasonality.DailyAmplitude * math.Sin(2*math.Pi*hourOfDay/24.0+pm.seasonality.DailyPhase)

	// Weekly seasonality
	dayOfWeek := float64(t.Weekday())
	weeklySeasonal := pm.seasonality.WeeklyAmplitude * math.Sin(2*math.Pi*dayOfWeek/7.0+pm.seasonality.WeeklyPhase)

	return dailySeasonal + weeklySeasonal
}

// calculateProphetAccuracy calculates Prophet model accuracy
func (pm *ProphetModel) calculateProphetAccuracy(values []float64, timestamps []time.Time) float64 {
	if len(values) < 2 {
		return 0.5
	}

	var totalError float64
	validPoints := 0

	for i := 1; i < len(values); i++ {
		actual := values[i]

		// Calculate prediction using trend and seasonality
		trendValue := pm.calculateTrend(timestamps[i])
		seasonalValue := pm.calculateSeasonality(timestamps[i])
		predicted := trendValue + seasonalValue

		if actual > 0 {
			error := math.Abs((actual - predicted) / actual)
			totalError += error
			validPoints++
		}
	}

	if validPoints == 0 {
		return 0.5
	}

	mape := totalError / float64(validPoints)
	return math.Max(0, 1.0-mape)
}

// HoltWintersModel implements Holt-Winters exponential smoothing
type HoltWintersModel struct {
	alpha        float64   // Level smoothing parameter
	beta         float64   // Trend smoothing parameter
	gamma        float64   // Seasonal smoothing parameter
	level        float64   // Current level
	trend        float64   // Current trend
	seasonal     []float64 // Seasonal components
	seasonPeriod int       // Seasonal period
	accuracy     float64
	lastTrained  time.Time
	mu           sync.RWMutex
}

// NewHoltWintersModel creates a new Holt-Winters model
func NewHoltWintersModel() *HoltWintersModel {
	return &HoltWintersModel{
		alpha:        0.3,
		beta:         0.1,
		gamma:        0.1,
		seasonPeriod: 24, // Daily seasonality for hourly data
	}
}

// Train trains the Holt-Winters model
func (hw *HoltWintersModel) Train(data []WorkloadDataPoint) error {
	hw.mu.Lock()
	defer hw.mu.Unlock()

	if len(data) < hw.seasonPeriod*2 {
		return fmt.Errorf("need at least %d data points for Holt-Winters", hw.seasonPeriod*2)
	}

	// Extract values
	values := make([]float64, len(data))
	for i, point := range data {
		values[i] = float64(point.QueueLength)
	}

	// Initialize components
	hw.initializeComponents(values)

	// Apply Holt-Winters smoothing
	hw.smooth(values)

	// Calculate accuracy
	hw.accuracy = hw.calculateHoltWintersAccuracy(values)
	hw.lastTrained = time.Now()

	return nil
}

// Predict generates a prediction using Holt-Winters
func (hw *HoltWintersModel) Predict(horizon time.Duration) (WorkloadPrediction, error) {
	hw.mu.RLock()
	defer hw.mu.RUnlock()

	if hw.lastTrained.IsZero() {
		return WorkloadPrediction{}, fmt.Errorf("Holt-Winters model not trained")
	}

	// Forecast h steps ahead (simplified to 1 step)
	h := 1 // For simplicity, forecast 1 step ahead
	seasonalIndex := h % hw.seasonPeriod
	if seasonalIndex == 0 {
		seasonalIndex = hw.seasonPeriod
	}

	predictedValue := (hw.level + float64(h)*hw.trend) * hw.seasonal[seasonalIndex-1]

	// Ensure non-negative prediction
	if predictedValue < 0 {
		predictedValue = 0
	}

	// Estimate nodes needed
	tasksPerNode := 10.0
	predictedNodes := int(math.Ceil(predictedValue / tasksPerNode))

	// Determine recommended action
	var action ScalingAction
	if predictedValue > 100 {
		action = ScalingActionScaleUp
	} else if predictedValue < 20 {
		action = ScalingActionScaleDown
	} else {
		action = ScalingActionNone
	}

	return WorkloadPrediction{
		Timestamp:         time.Now().Add(horizon),
		PredictedQueue:    int(predictedValue),
		PredictedTasks:    int(predictedValue * 0.8),
		PredictedNodes:    predictedNodes,
		Confidence:        hw.accuracy,
		RecommendedAction: action,
	}, nil
}

// GetAccuracy returns the current model accuracy
func (hw *HoltWintersModel) GetAccuracy() float64 {
	hw.mu.RLock()
	defer hw.mu.RUnlock()
	return hw.accuracy
}

// initializeComponents initializes Holt-Winters components
func (hw *HoltWintersModel) initializeComponents(values []float64) {
	// Initialize level as average of first season
	sum := 0.0
	for i := 0; i < hw.seasonPeriod && i < len(values); i++ {
		sum += values[i]
	}
	hw.level = sum / float64(minimum(hw.seasonPeriod, len(values)))

	// Initialize trend
	if len(values) >= 2*hw.seasonPeriod {
		sum1, sum2 := 0.0, 0.0
		for i := 0; i < hw.seasonPeriod; i++ {
			sum1 += values[i]
			sum2 += values[i+hw.seasonPeriod]
		}
		hw.trend = (sum2 - sum1) / float64(hw.seasonPeriod*hw.seasonPeriod)
	} else {
		hw.trend = 0.0
	}

	// Initialize seasonal components
	hw.seasonal = make([]float64, hw.seasonPeriod)
	for i := 0; i < hw.seasonPeriod; i++ {
		if i < len(values) && hw.level > 0 {
			hw.seasonal[i] = values[i] / hw.level
		} else {
			hw.seasonal[i] = 1.0
		}
	}
}

// smooth applies Holt-Winters smoothing
func (hw *HoltWintersModel) smooth(values []float64) {
	for t := 0; t < len(values); t++ {
		seasonalIndex := t % hw.seasonPeriod

		// Update level
		oldLevel := hw.level
		if hw.seasonal[seasonalIndex] != 0 {
			hw.level = hw.alpha*(values[t]/hw.seasonal[seasonalIndex]) + (1-hw.alpha)*(hw.level+hw.trend)
		}

		// Update trend
		hw.trend = hw.beta*(hw.level-oldLevel) + (1-hw.beta)*hw.trend

		// Update seasonal component
		if hw.level != 0 {
			hw.seasonal[seasonalIndex] = hw.gamma*(values[t]/hw.level) + (1-hw.gamma)*hw.seasonal[seasonalIndex]
		}
	}
}

// calculateHoltWintersAccuracy calculates Holt-Winters model accuracy
func (hw *HoltWintersModel) calculateHoltWintersAccuracy(values []float64) float64 {
	if len(values) < hw.seasonPeriod+1 {
		return 0.5
	}

	var totalError float64
	validPoints := 0

	// Calculate accuracy for points after initial seasonal period
	for t := hw.seasonPeriod; t < len(values); t++ {
		actual := values[t]
		seasonalIndex := t % hw.seasonPeriod

		// Simple prediction using current components
		predicted := hw.level * hw.seasonal[seasonalIndex]

		if actual > 0 {
			error := math.Abs((actual - predicted) / actual)
			totalError += error
			validPoints++
		}
	}

	if validPoints == 0 {
		return 0.5
	}

	mape := totalError / float64(validPoints)
	return math.Max(0, 1.0-mape)
}

// EnsembleModel combines multiple prediction models
type EnsembleModel struct {
	models      []PredictionModel
	weights     []float64
	accuracy    float64
	lastTrained time.Time
	mu          sync.RWMutex
}

// NewEnsembleModel creates a new ensemble model
func NewEnsembleModel() *EnsembleModel {
	models := []PredictionModel{
		NewLinearRegressionModel(),
		NewMovingAverageModel(),
		NewExponentialSmoothingModel(),
		NewARIMAModel(),
		NewHoltWintersModel(),
	}

	// Equal weights initially
	weights := make([]float64, len(models))
	for i := range weights {
		weights[i] = 1.0 / float64(len(models))
	}

	return &EnsembleModel{
		models:  models,
		weights: weights,
	}
}

// Train trains all models in the ensemble
func (em *EnsembleModel) Train(data []WorkloadDataPoint) error {
	em.mu.Lock()
	defer em.mu.Unlock()

	if len(data) < 10 {
		return fmt.Errorf("need at least 10 data points for ensemble model")
	}

	// Train all models
	var trainedModels []PredictionModel
	var modelAccuracies []float64

	for _, model := range em.models {
		if err := model.Train(data); err == nil {
			trainedModels = append(trainedModels, model)
			modelAccuracies = append(modelAccuracies, model.GetAccuracy())
		}
	}

	if len(trainedModels) == 0 {
		return fmt.Errorf("no models could be trained successfully")
	}

	em.models = trainedModels

	// Update weights based on accuracy
	em.updateWeights(modelAccuracies)

	// Calculate ensemble accuracy
	em.accuracy = em.calculateEnsembleAccuracy(modelAccuracies)
	em.lastTrained = time.Now()

	return nil
}

// Predict generates an ensemble prediction
func (em *EnsembleModel) Predict(horizon time.Duration) (WorkloadPrediction, error) {
	em.mu.RLock()
	defer em.mu.RUnlock()

	if em.lastTrained.IsZero() {
		return WorkloadPrediction{}, fmt.Errorf("ensemble model not trained")
	}

	// Get predictions from all models
	var predictions []WorkloadPrediction
	var validWeights []float64

	for i, model := range em.models {
		if pred, err := model.Predict(horizon); err == nil {
			predictions = append(predictions, pred)
			validWeights = append(validWeights, em.weights[i])
		}
	}

	if len(predictions) == 0 {
		return WorkloadPrediction{}, fmt.Errorf("no models could generate predictions")
	}

	// Normalize weights
	totalWeight := 0.0
	for _, w := range validWeights {
		totalWeight += w
	}
	for i := range validWeights {
		validWeights[i] /= totalWeight
	}

	// Combine predictions using weighted average
	var weightedQueue, weightedTasks, weightedNodes float64
	var weightedConfidence float64
	actionCounts := make(map[ScalingAction]float64)

	for i, pred := range predictions {
		weight := validWeights[i]
		weightedQueue += float64(pred.PredictedQueue) * weight
		weightedTasks += float64(pred.PredictedTasks) * weight
		weightedNodes += float64(pred.PredictedNodes) * weight
		weightedConfidence += pred.Confidence * weight
		actionCounts[pred.RecommendedAction] += weight
	}

	// Determine most weighted action
	var bestAction ScalingAction
	maxWeight := 0.0
	for action, weight := range actionCounts {
		if weight > maxWeight {
			maxWeight = weight
			bestAction = action
		}
	}

	return WorkloadPrediction{
		Timestamp:         time.Now().Add(horizon),
		PredictedQueue:    int(math.Round(weightedQueue)),
		PredictedTasks:    int(math.Round(weightedTasks)),
		PredictedNodes:    int(math.Round(weightedNodes)),
		Confidence:        weightedConfidence,
		RecommendedAction: bestAction,
	}, nil
}

// GetAccuracy returns the ensemble model accuracy
func (em *EnsembleModel) GetAccuracy() float64 {
	em.mu.RLock()
	defer em.mu.RUnlock()
	return em.accuracy
}

// updateWeights updates model weights based on accuracy
func (em *EnsembleModel) updateWeights(accuracies []float64) {
	if len(accuracies) != len(em.weights) {
		// Resize weights array
		em.weights = make([]float64, len(accuracies))
	}

	// Use accuracy as weights (higher accuracy = higher weight)
	totalAccuracy := 0.0
	for _, acc := range accuracies {
		totalAccuracy += acc
	}

	if totalAccuracy > 0 {
		for i, acc := range accuracies {
			em.weights[i] = acc / totalAccuracy
		}
	} else {
		// Equal weights if no valid accuracies
		for i := range em.weights {
			em.weights[i] = 1.0 / float64(len(em.weights))
		}
	}
}

// calculateEnsembleAccuracy calculates overall ensemble accuracy
func (em *EnsembleModel) calculateEnsembleAccuracy(accuracies []float64) float64 {
	if len(accuracies) == 0 {
		return 0.5
	}

	// Weighted average of individual model accuracies
	weightedAccuracy := 0.0
	for i, acc := range accuracies {
		weightedAccuracy += acc * em.weights[i]
	}

	return weightedAccuracy
}

// Helper function for minimum
func minimum(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// LSTMPredictionModel adapts LSTMModel to PredictionModel interface
type LSTMPredictionModel struct {
	model       *LSTMModel
	accuracy    float64
	lastTrained time.Time
	mu          sync.RWMutex
}

// NewLSTMPredictionModel creates a new LSTM prediction model adapter
func NewLSTMPredictionModel() *LSTMPredictionModel {
	// Create LSTM model with default parameters
	featureEngineer := NewFeatureEngineer()
	featureCount := len(featureEngineer.GetFeatureNames())

	return &LSTMPredictionModel{
		model: NewLSTMModel(
			featureCount, // input size (number of features)
			64,           // hidden size
			1,            // output size (predicting queue length)
			10,           // sequence length
			0.001,        // learning rate
			100,          // epochs
		),
	}
}

// Train trains the LSTM model
func (lpm *LSTMPredictionModel) Train(data []WorkloadDataPoint) error {
	lpm.mu.Lock()
	defer lpm.mu.Unlock()

	err := lpm.model.Train(data)
	if err != nil {
		return err
	}

	lpm.accuracy = lpm.model.GetAccuracy()
	lpm.lastTrained = time.Now()
	return nil
}

// Predict generates a prediction using the LSTM model
func (lpm *LSTMPredictionModel) Predict(horizon time.Duration) (WorkloadPrediction, error) {
	lpm.mu.RLock()
	defer lpm.mu.RUnlock()

	if lpm.lastTrained.IsZero() {
		return WorkloadPrediction{}, fmt.Errorf("model not trained")
	}

	// For prediction, we need the recent data points
	// This is a simplified approach - in practice, we'd store recent data
	// For now, we'll create a dummy prediction based on the model's accuracy

	// Generate prediction (simplified)
	queueLength, confidence, err := lpm.model.Predict(nil) // This needs historical data
	if err != nil {
		// Fallback prediction
		queueLength = 50
		confidence = lpm.accuracy
	}

	// Estimate nodes needed
	tasksPerNode := 10.0
	predictedNodes := int(math.Ceil(float64(queueLength) / tasksPerNode))

	// Determine recommended action
	var action ScalingAction
	if queueLength > 100 {
		action = ScalingActionScaleUp
	} else if queueLength < 20 {
		action = ScalingActionScaleDown
	} else {
		action = ScalingActionNone
	}

	return WorkloadPrediction{
		Timestamp:         time.Now().Add(horizon),
		PredictedQueue:    queueLength,
		PredictedTasks:    int(float64(queueLength) * 0.8),
		PredictedNodes:    predictedNodes,
		Confidence:        confidence,
		RecommendedAction: action,
	}, nil
}

// GetAccuracy returns the model accuracy
func (lpm *LSTMPredictionModel) GetAccuracy() float64 {
	lpm.mu.RLock()
	defer lpm.mu.RUnlock()
	return lpm.accuracy
}

// GRUPredictionModel adapts GRUModel to PredictionModel interface
type GRUPredictionModel struct {
	model       *GRUModel
	accuracy    float64
	lastTrained time.Time
	mu          sync.RWMutex
}

// NewGRUPredictionModel creates a new GRU prediction model adapter
func NewGRUPredictionModel() *GRUPredictionModel {
	// Create GRU model with default parameters
	featureEngineer := NewFeatureEngineer()
	featureCount := len(featureEngineer.GetFeatureNames())

	return &GRUPredictionModel{
		model: NewGRUModel(
			featureCount, // input size (number of features)
			64,           // hidden size
			1,            // output size (predicting queue length)
			10,           // sequence length
			0.001,        // learning rate
			100,          // epochs
		),
	}
}

// Train trains the GRU model
func (gpm *GRUPredictionModel) Train(data []WorkloadDataPoint) error {
	gpm.mu.Lock()
	defer gpm.mu.Unlock()

	err := gpm.model.Train(data)
	if err != nil {
		return err
	}

	gpm.accuracy = gpm.model.GetAccuracy()
	gpm.lastTrained = time.Now()
	return nil
}

// Predict generates a prediction using the GRU model
func (gpm *GRUPredictionModel) Predict(horizon time.Duration) (WorkloadPrediction, error) {
	gpm.mu.RLock()
	defer gpm.mu.RUnlock()

	if gpm.lastTrained.IsZero() {
		return WorkloadPrediction{}, fmt.Errorf("model not trained")
	}

	// Generate prediction (simplified)
	queueLength, confidence, err := gpm.model.Predict(nil) // This needs historical data
	if err != nil {
		// Fallback prediction
		queueLength = 50
		confidence = gpm.accuracy
	}

	// Estimate nodes needed
	tasksPerNode := 10.0
	predictedNodes := int(math.Ceil(float64(queueLength) / tasksPerNode))

	// Determine recommended action
	var action ScalingAction
	if queueLength > 100 {
		action = ScalingActionScaleUp
	} else if queueLength < 20 {
		action = ScalingActionScaleDown
	} else {
		action = ScalingActionNone
	}

	return WorkloadPrediction{
		Timestamp:         time.Now().Add(horizon),
		PredictedQueue:    queueLength,
		PredictedTasks:    int(float64(queueLength) * 0.8),
		PredictedNodes:    predictedNodes,
		Confidence:        confidence,
		RecommendedAction: action,
	}, nil
}

// GetAccuracy returns the model accuracy
func (gpm *GRUPredictionModel) GetAccuracy() float64 {
	gpm.mu.RLock()
	defer gpm.mu.RUnlock()
	return gpm.accuracy
}

// DeepLearningEnsemblePredictionModel adapts DeepLearningEnsemble to PredictionModel interface
type DeepLearningEnsemblePredictionModel struct {
	ensemble    *DeepLearningEnsemble
	accuracy    float64
	lastTrained time.Time
	mu          sync.RWMutex
}

// NewDeepLearningEnsemblePredictionModel creates a new deep learning ensemble prediction model
func NewDeepLearningEnsemblePredictionModel() *DeepLearningEnsemblePredictionModel {
	ensemble := NewDeepLearningEnsemble()

	// Add LSTM and GRU models to the ensemble
	featureEngineer := NewFeatureEngineer()
	featureCount := len(featureEngineer.GetFeatureNames())

	lstmModel := NewLSTMModel(featureCount, 64, 1, 10, 0.001, 100)
	gruModel := NewGRUModel(featureCount, 64, 1, 10, 0.001, 100)

	ensemble.AddModel(lstmModel, 0.6) // Higher weight for LSTM
	ensemble.AddModel(gruModel, 0.4)  // Lower weight for GRU

	return &DeepLearningEnsemblePredictionModel{
		ensemble: ensemble,
	}
}

// Train trains the ensemble model
func (depm *DeepLearningEnsemblePredictionModel) Train(data []WorkloadDataPoint) error {
	depm.mu.Lock()
	defer depm.mu.Unlock()

	err := depm.ensemble.Train(data)
	if err != nil {
		return err
	}

	depm.accuracy = depm.ensemble.GetAccuracy()
	depm.lastTrained = time.Now()
	return nil
}

// Predict generates a prediction using the ensemble model
func (depm *DeepLearningEnsemblePredictionModel) Predict(horizon time.Duration) (WorkloadPrediction, error) {
	depm.mu.RLock()
	defer depm.mu.RUnlock()

	if depm.lastTrained.IsZero() {
		return WorkloadPrediction{}, fmt.Errorf("model not trained")
	}

	// Generate prediction (simplified)
	queueLength, confidence, err := depm.ensemble.Predict(nil) // This needs historical data
	if err != nil {
		// Fallback prediction
		queueLength = 50
		confidence = depm.accuracy
	}

	// Estimate nodes needed
	tasksPerNode := 10.0
	predictedNodes := int(math.Ceil(float64(queueLength) / tasksPerNode))

	// Determine recommended action
	var action ScalingAction
	if queueLength > 100 {
		action = ScalingActionScaleUp
	} else if queueLength < 20 {
		action = ScalingActionScaleDown
	} else {
		action = ScalingActionNone
	}

	return WorkloadPrediction{
		Timestamp:         time.Now().Add(horizon),
		PredictedQueue:    queueLength,
		PredictedTasks:    int(float64(queueLength) * 0.8),
		PredictedNodes:    predictedNodes,
		Confidence:        confidence,
		RecommendedAction: action,
	}, nil
}

// GetAccuracy returns the ensemble accuracy
func (depm *DeepLearningEnsemblePredictionModel) GetAccuracy() float64 {
	depm.mu.RLock()
	defer depm.mu.RUnlock()
	return depm.accuracy
}
