//go:build windows && cuda

package gpu

/*
#cgo windows CFLAGS: -IC:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.0/include
#cgo windows LDFLAGS: -LC:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.0/lib/x64 -lcudart -lcublas

#include <cuda_runtime.h>
#include <cublas_v2.h>
*/
import "C"
import (
	"fmt"
	"unsafe"
)

// WindowsCUDADetector implements CUDA detection for Windows
type WindowsCUDADetector struct {
	initialized bool
}

// DetectCUDADevices detects CUDA devices on Windows
func (d *WindowsCUDADetector) DetectCUDADevices() ([]CUDADevice, error) {
	if !d.initialized {
		result := C.cudaSetDevice(0)
		if result != C.cudaSuccess {
			return nil, fmt.Errorf("CUDA initialization failed: %v", result)
		}
		d.initialized = true
	}

	var deviceCount C.int
	result := C.cudaGetDeviceCount(&deviceCount)
	if result != C.cudaSuccess {
		return nil, fmt.Errorf("failed to get device count: %v", result)
	}

	devices := make([]CUDADevice, deviceCount)
	for i := 0; i < int(deviceCount); i++ {
		var props C.struct_cudaDeviceProp
		result := C.cudaGetDeviceProperties(&props, C.int(i))
		if result != C.cudaSuccess {
			continue
		}

		device := CUDADevice{
			ID:   i,
			Name: C.GoString(&props.name[0]),
			ComputeCapability: ComputeCapability{
				Major: int(props.major),
				Minor: int(props.minor),
			},
			MemoryMB:            int(props.totalGlobalMem / (1024 * 1024)),
			MultiprocessorCount: int(props.multiProcessorCount),
			ClockRateKHz:        int(props.clockRate),
			WarpSize:            int(props.warpSize),
		}
		devices[i] = device
	}

	return devices, nil
}

// GetCUDAVersion returns CUDA runtime version on Windows
func (d *WindowsCUDADetector) GetCUDAVersion() (string, error) {
	var version C.int
	result := C.cudaRuntimeGetVersion(&version)
	if result != C.cudaSuccess {
		return "", fmt.Errorf("failed to get CUDA version: %v", result)
	}

	major := version / 1000
	minor := (version % 1000) / 10
	return fmt.Sprintf("%d.%d", major, minor), nil
}

// WindowsCUDAMemoryManager handles CUDA memory operations on Windows
type WindowsCUDAMemoryManager struct {
	allocations map[uintptr]size_t
}

// Allocate allocates CUDA memory on Windows
func (m *WindowsCUDAMemoryManager) Allocate(size int) (CUDAMemoryPtr, error) {
	var ptr unsafe.Pointer
	result := C.cudaMalloc(&ptr, C.size_t(size))
	if result != C.cudaSuccess {
		return CUDAMemoryPtr{}, fmt.Errorf("CUDA malloc failed: %v", result)
	}

	if m.allocations == nil {
		m.allocations = make(map[uintptr]size_t)
	}
	m.allocations[uintptr(ptr)] = C.size_t(size)

	return CUDAMemoryPtr{
		Ptr:  ptr,
		Size: size,
	}, nil
}

// Free deallocates CUDA memory on Windows
func (m *WindowsCUDAMemoryManager) Free(ptr CUDAMemoryPtr) error {
	result := C.cudaFree(ptr.Ptr)
	if result != C.cudaSuccess {
		return fmt.Errorf("CUDA free failed: %v", result)
	}

	delete(m.allocations, uintptr(ptr.Ptr))
	return nil
}

// MemcpyHostToDevice copies data from host to device on Windows
func (m *WindowsCUDAMemoryManager) MemcpyHostToDevice(dst CUDAMemoryPtr, src unsafe.Pointer, size int) error {
	result := C.cudaMemcpy(dst.Ptr, src, C.size_t(size), C.cudaMemcpyHostToDevice)
	if result != C.cudaSuccess {
		return fmt.Errorf("CUDA memcpy H2D failed: %v", result)
	}
	return nil
}

// MemcpyDeviceToHost copies data from device to host on Windows
func (m *WindowsCUDAMemoryManager) MemcpyDeviceToHost(dst unsafe.Pointer, src CUDAMemoryPtr, size int) error {
	result := C.cudaMemcpy(dst, src.Ptr, C.size_t(size), C.cudaMemcpyDeviceToHost)
	if result != C.cudaSuccess {
		return fmt.Errorf("CUDA memcpy D2H failed: %v", result)
	}
	return nil
}

// NewWindowsCUDADetector creates a new Windows CUDA detector
func NewWindowsCUDADetector() CUDADetector {
	return &WindowsCUDADetector{}
}

// NewWindowsCUDAMemoryManager creates a new Windows CUDA memory manager
func NewWindowsCUDAMemoryManager() CUDAMemoryManager {
	return &WindowsCUDAMemoryManager{
		allocations: make(map[uintptr]size_t),
	}
}
