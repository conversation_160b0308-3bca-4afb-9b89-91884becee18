//go:build cuda
// +build cuda

package gpu

/*
#cgo CFLAGS: -I/usr/local/cuda/include
#cgo LDFLAGS: -L/usr/local/cuda/lib64 -lcuda -lcudart
#include <cuda_runtime.h>
#include <stdlib.h>

// Stream management helpers
static cudaError_t create_cuda_stream(cudaStream_t* stream) {
    return cudaStreamCreate(stream);
}

static cudaError_t create_cuda_stream_with_flags(cudaStream_t* stream, unsigned int flags) {
    return cudaStreamCreateWithFlags(stream, flags);
}

static cudaError_t destroy_cuda_stream(cudaStream_t stream) {
    return cudaStreamDestroy(stream);
}

static cudaError_t synchronize_cuda_stream(cudaStream_t stream) {
    return cudaStreamSynchronize(stream);
}

static cudaError_t query_cuda_stream(cudaStream_t stream) {
    return cudaStreamQuery(stream);
}

static cudaError_t stream_wait_event(cudaStream_t stream, cudaEvent_t event, unsigned int flags) {
    return cudaStreamWaitEvent(stream, event, flags);
}

// Event management helpers
static cudaError_t create_cuda_event(cudaEvent_t* event) {
    return cudaEventCreate(event);
}

static cudaError_t create_cuda_event_with_flags(cudaEvent_t* event, unsigned int flags) {
    return cudaEventCreateWithFlags(event, flags);
}

static cudaError_t destroy_cuda_event(cudaEvent_t event) {
    return cudaEventDestroy(event);
}

static cudaError_t record_cuda_event(cudaEvent_t event, cudaStream_t stream) {
    return cudaEventRecord(event, stream);
}

static cudaError_t synchronize_cuda_event(cudaEvent_t event) {
    return cudaEventSynchronize(event);
}

static cudaError_t query_cuda_event(cudaEvent_t event) {
    return cudaEventQuery(event);
}

static cudaError_t event_elapsed_time(float* ms, cudaEvent_t start, cudaEvent_t end) {
    return cudaEventElapsedTime(ms, start, end);
}
*/
import "C"
import (
	"fmt"
	"log"
	"sync"
	"unsafe"
)

// Forward-declared interfaces so that CUDAStreamImpl / CUDAEventImpl can be used via
// interface types (needed by tests and other packages).
// These mirror a subset of methods implemented below and intentionally do NOT
// pull in the higher-level types.GPUStream to avoid an import cycle.
type CUDAStream interface {
	Create(flags int) error
	Synchronize() error
	Query() (bool, error)
	RecordEvent(event CUDAEvent) error
	WaitEvent(event CUDAEvent) error
	GetHandle() uintptr
	Destroy() error
}

type CUDAEvent interface {
	Create(flags int) error
	Record(stream CUDAStream) error
	Synchronize() error
	Query() (bool, error)
	ElapsedTime(endEvent CUDAEvent) (float32, error)
	Destroy() error
}

// CUDAStreamImpl implements CUDAStream interface
type CUDAStreamImpl struct {
	handle  C.cudaStream_t
	created bool
	flags   int
	logger  *log.Logger
	mutex   sync.RWMutex
}

// Create creates a new CUDA stream
func (s *CUDAStreamImpl) Create(flags int) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.created {
		return fmt.Errorf("stream already created")
	}

	var result C.cudaError_t
	if flags == 0 {
		result = C.create_cuda_stream(&s.handle)
	} else {
		result = C.create_cuda_stream_with_flags(&s.handle, C.uint(flags))
	}

	if result != C.cudaSuccess {
		return fmt.Errorf("failed to create CUDA stream: %d", result)
	}

	s.created = true
	s.flags = flags
	s.logger.Printf("Created CUDA stream with handle %v, flags: %d", s.handle, flags)
	return nil
}

// Synchronize waits for all operations in the stream to complete
func (s *CUDAStreamImpl) Synchronize() error {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.created {
		return fmt.Errorf("stream not created")
	}

	result := C.synchronize_cuda_stream(s.handle)
	if result != C.cudaSuccess {
		return fmt.Errorf("failed to synchronize CUDA stream: %d", result)
	}

	return nil
}

// Query checks if all operations in the stream are complete
func (s *CUDAStreamImpl) Query() (bool, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.created {
		return false, fmt.Errorf("stream not created")
	}

	result := C.query_cuda_stream(s.handle)
	if result == C.cudaSuccess {
		return true, nil // All operations complete
	} else if result == C.cudaErrorNotReady {
		return false, nil // Operations still pending
	} else {
		return false, fmt.Errorf("failed to query CUDA stream: %d", result)
	}
}

// RecordEvent records an event in the stream
func (s *CUDAStreamImpl) RecordEvent(event CUDAEvent) error {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.created {
		return fmt.Errorf("stream not created")
	}

	eventImpl, ok := event.(*CUDAEventImpl)
	if !ok {
		return fmt.Errorf("invalid event type")
	}

	result := C.record_cuda_event(eventImpl.handle, s.handle)
	if result != C.cudaSuccess {
		return fmt.Errorf("failed to record event in stream: %d", result)
	}

	return nil
}

// WaitEvent makes the stream wait for an event
func (s *CUDAStreamImpl) WaitEvent(event CUDAEvent) error {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.created {
		return fmt.Errorf("stream not created")
	}

	eventImpl, ok := event.(*CUDAEventImpl)
	if !ok {
		return fmt.Errorf("invalid event type")
	}

	result := C.stream_wait_event(s.handle, eventImpl.handle, 0)
	if result != C.cudaSuccess {
		return fmt.Errorf("failed to wait for event in stream: %d", result)
	}

	return nil
}

// GetHandle returns the native CUDA stream handle
func (s *CUDAStreamImpl) GetHandle() uintptr {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return uintptr(unsafe.Pointer(s.handle))
}

// Destroy destroys the CUDA stream
func (s *CUDAStreamImpl) Destroy() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.created {
		return nil // Already destroyed or never created
	}

	// Don't destroy the default stream (handle nil)
	if s.handle == nil {
		s.created = false
		return nil
	}

	result := C.destroy_cuda_stream(s.handle)
	if result != C.cudaSuccess {
		return fmt.Errorf("failed to destroy CUDA stream: %d", result)
	}

	s.created = false
	s.logger.Printf("Destroyed CUDA stream with handle %v", s.handle)
	return nil
}

// CUDAEventImpl implements CUDAEvent interface
type CUDAEventImpl struct {
	handle  C.cudaEvent_t
	created bool
	flags   int
	logger  *log.Logger
	mutex   sync.RWMutex
}

// Create creates a new CUDA event
func (e *CUDAEventImpl) Create(flags int) error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if e.created {
		return fmt.Errorf("event already created")
	}

	var result C.cudaError_t
	if flags == 0 {
		result = C.create_cuda_event(&e.handle)
	} else {
		result = C.create_cuda_event_with_flags(&e.handle, C.uint(flags))
	}

	if result != C.cudaSuccess {
		return fmt.Errorf("failed to create CUDA event: %d", result)
	}

	e.created = true
	e.flags = flags
	e.logger.Printf("Created CUDA event with handle %v, flags: %d", e.handle, flags)
	return nil
}

// Record records the event
func (e *CUDAEventImpl) Record(stream CUDAStream) error {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	if !e.created {
		return fmt.Errorf("event not created")
	}

	var streamHandle C.cudaStream_t
	if stream != nil {
		streamImpl, ok := stream.(*CUDAStreamImpl)
		if !ok {
			return fmt.Errorf("invalid stream type")
		}
		streamHandle = streamImpl.handle
	} else {
		streamHandle = nil // Default stream
	}

	result := C.record_cuda_event(e.handle, streamHandle)
	if result != C.cudaSuccess {
		return fmt.Errorf("failed to record CUDA event: %d", result)
	}

	return nil
}

// Synchronize waits for the event to complete
func (e *CUDAEventImpl) Synchronize() error {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	if !e.created {
		return fmt.Errorf("event not created")
	}

	result := C.synchronize_cuda_event(e.handle)
	if result != C.cudaSuccess {
		return fmt.Errorf("failed to synchronize CUDA event: %d", result)
	}

	return nil
}

// Query checks if the event is complete
func (e *CUDAEventImpl) Query() (bool, error) {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	if !e.created {
		return false, fmt.Errorf("event not created")
	}

	result := C.query_cuda_event(e.handle)
	if result == C.cudaSuccess {
		return true, nil // Event complete
	} else if result == C.cudaErrorNotReady {
		return false, nil // Event still pending
	} else {
		return false, fmt.Errorf("failed to query CUDA event: %d", result)
	}
}

// ElapsedTime calculates elapsed time between two events
func (e *CUDAEventImpl) ElapsedTime(endEvent CUDAEvent) (float32, error) {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	if !e.created {
		return 0, fmt.Errorf("start event not created")
	}

	endImpl, ok := endEvent.(*CUDAEventImpl)
	if !ok {
		return 0, fmt.Errorf("invalid end event type")
	}

	endImpl.mutex.RLock()
	defer endImpl.mutex.RUnlock()

	if !endImpl.created {
		return 0, fmt.Errorf("end event not created")
	}

	var ms C.float
	result := C.event_elapsed_time(&ms, e.handle, endImpl.handle)
	if result != C.cudaSuccess {
		return 0, fmt.Errorf("failed to calculate elapsed time: %d", result)
	}

	return float32(ms), nil
}

// Destroy destroys the CUDA event
func (e *CUDAEventImpl) Destroy() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if !e.created {
		return nil // Already destroyed or never created
	}

	result := C.destroy_cuda_event(e.handle)
	if result != C.cudaSuccess {
		return fmt.Errorf("failed to destroy CUDA event: %d", result)
	}

	e.created = false
	e.logger.Printf("Destroyed CUDA event with handle %v", e.handle)
	return nil
}

// Ensure implementations satisfy interfaces
var _ CUDAStream = (*CUDAStreamImpl)(nil)
var _ CUDAEvent = (*CUDAEventImpl)(nil)
