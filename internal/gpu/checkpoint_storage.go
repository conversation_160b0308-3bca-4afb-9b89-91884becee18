package gpu

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
)

// FileCheckpointStorage implements CheckpointStorage using the filesystem
type FileCheckpointStorage struct {
	basePath string
	mu       sync.RWMutex
}

// NewFileCheckpointStorage creates a new file-based checkpoint storage
func NewFileCheckpointStorage(basePath string) (*FileCheckpointStorage, error) {
	// Create base directory if it doesn't exist
	if err := os.MkdirAll(basePath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create checkpoint directory: %w", err)
	}

	return &FileCheckpointStorage{
		basePath: basePath,
	}, nil
}

// StoreCheckpoint stores a checkpoint to the filesystem
func (fcs *FileCheckpointStorage) StoreCheckpoint(checkpoint *Checkpoint) error {
	fcs.mu.Lock()
	defer fcs.mu.Unlock()

	// Create task directory if it doesn't exist
	taskDir := filepath.Join(fcs.basePath, checkpoint.TaskID)
	if err := os.Mkdir<PERSON>ll(taskDir, 0755); err != nil {
		return fmt.Errorf("failed to create task directory: %w", err)
	}

	// Create checkpoint filename
	filename := fmt.Sprintf("checkpoint_v%d.json", checkpoint.Version)
	checkpointPath := filepath.Join(taskDir, filename)

	// Serialize checkpoint to JSON
	data, err := json.MarshalIndent(checkpoint, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to serialize checkpoint: %w", err)
	}

	// Write to file
	if err := ioutil.WriteFile(checkpointPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write checkpoint file: %w", err)
	}

	// Also store just the data separately for easier access
	dataPath := filepath.Join(taskDir, fmt.Sprintf("data_v%d.bin", checkpoint.Version))
	if err := ioutil.WriteFile(dataPath, checkpoint.Data, 0644); err != nil {
		return fmt.Errorf("failed to write checkpoint data: %w", err)
	}

	return nil
}

// LoadCheckpoint loads a specific checkpoint version
func (fcs *FileCheckpointStorage) LoadCheckpoint(taskID string, version int) (*Checkpoint, error) {
	fcs.mu.RLock()
	defer fcs.mu.RUnlock()

	// Create checkpoint filename
	filename := fmt.Sprintf("checkpoint_v%d.json", version)
	checkpointPath := filepath.Join(fcs.basePath, taskID, filename)

	// Check if file exists
	if _, err := os.Stat(checkpointPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("checkpoint v%d for task %s not found", version, taskID)
	}

	// Read checkpoint file
	data, err := ioutil.ReadFile(checkpointPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read checkpoint file: %w", err)
	}

	// Deserialize checkpoint
	var checkpoint Checkpoint
	if err := json.Unmarshal(data, &checkpoint); err != nil {
		return nil, fmt.Errorf("failed to deserialize checkpoint: %w", err)
	}

	// Load data separately
	dataPath := filepath.Join(fcs.basePath, taskID, fmt.Sprintf("data_v%d.bin", version))
	if checkpoint.Data, err = ioutil.ReadFile(dataPath); err != nil {
		return nil, fmt.Errorf("failed to read checkpoint data: %w", err)
	}

	return &checkpoint, nil
}

// ListCheckpoints lists all checkpoints for a task
func (fcs *FileCheckpointStorage) ListCheckpoints(taskID string) ([]*CheckpointMetadata, error) {
	fcs.mu.RLock()
	defer fcs.mu.RUnlock()

	taskDir := filepath.Join(fcs.basePath, taskID)

	// Check if task directory exists
	if _, err := os.Stat(taskDir); os.IsNotExist(err) {
		return []*CheckpointMetadata{}, nil // Return empty list if no checkpoints exist
	}

	// Read directory contents
	files, err := ioutil.ReadDir(taskDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read task directory: %w", err)
	}

	var metadata []*CheckpointMetadata

	// Process checkpoint files
	for _, file := range files {
		if !strings.HasPrefix(file.Name(), "checkpoint_v") || !strings.HasSuffix(file.Name(), ".json") {
			continue
		}

		// Load checkpoint to get metadata
		var version int
		if _, err := fmt.Sscanf(file.Name(), "checkpoint_v%d.json", &version); err != nil {
			continue
		}

		checkpoint, err := fcs.LoadCheckpoint(taskID, version)
		if err != nil {
			continue // Skip corrupted checkpoints
		}

		metadata = append(metadata, &CheckpointMetadata{
			TaskID:      checkpoint.TaskID,
			Version:     checkpoint.Version,
			Timestamp:   checkpoint.Timestamp,
			NodeID:      checkpoint.NodeID,
			Size:        checkpoint.OriginalSize,
			Hash:        checkpoint.Hash,
			Incremental: checkpoint.Incremental,
			Description: fmt.Sprintf("Checkpoint v%d", checkpoint.Version),
		})
	}

	// Sort by version
	sort.Slice(metadata, func(i, j int) bool {
		return metadata[i].Version < metadata[j].Version
	})

	return metadata, nil
}

// DeleteCheckpoint deletes a specific checkpoint version
func (fcs *FileCheckpointStorage) DeleteCheckpoint(taskID string, version int) error {
	fcs.mu.Lock()
	defer fcs.mu.Unlock()

	// Delete checkpoint file
	checkpointPath := filepath.Join(fcs.basePath, taskID, fmt.Sprintf("checkpoint_v%d.json", version))
	if err := os.Remove(checkpointPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete checkpoint file: %w", err)
	}

	// Delete data file
	dataPath := filepath.Join(fcs.basePath, taskID, fmt.Sprintf("data_v%d.bin", version))
	if err := os.Remove(dataPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete checkpoint data: %w", err)
	}

	return nil
}

// GetLatestCheckpoint gets the latest checkpoint for a task
func (fcs *FileCheckpointStorage) GetLatestCheckpoint(taskID string) (*Checkpoint, error) {
	checkpoints, err := fcs.ListCheckpoints(taskID)
	if err != nil {
		return nil, err
	}

	if len(checkpoints) == 0 {
		return nil, fmt.Errorf("no checkpoints found for task %s", taskID)
	}

	// Get the latest version (last in sorted list)
	latestMetadata := checkpoints[len(checkpoints)-1]
	return fcs.LoadCheckpoint(taskID, latestMetadata.Version)
}

// GetStoragePath returns the base storage path
func (fcs *FileCheckpointStorage) GetStoragePath() string {
	return fcs.basePath
}

// CleanupTask removes all checkpoints for a task
func (fcs *FileCheckpointStorage) CleanupTask(taskID string) error {
	fcs.mu.Lock()
	defer fcs.mu.Unlock()

	taskDir := filepath.Join(fcs.basePath, taskID)
	return os.RemoveAll(taskDir)
}

// GetStorageStats returns storage statistics
func (fcs *FileCheckpointStorage) GetStorageStats() (map[string]interface{}, error) {
	fcs.mu.RLock()
	defer fcs.mu.RUnlock()

	stats := make(map[string]interface{})

	// Count tasks and checkpoints
	taskCount := 0
	checkpointCount := 0
	totalSize := int64(0)

	// Walk through the base directory
	err := filepath.Walk(fcs.basePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() && path != fcs.basePath {
			// This is a task directory
			relPath, _ := filepath.Rel(fcs.basePath, path)
			if !strings.Contains(relPath, string(os.PathSeparator)) {
				taskCount++
			}
		} else if strings.HasPrefix(info.Name(), "checkpoint_v") && strings.HasSuffix(info.Name(), ".json") {
			checkpointCount++
			totalSize += info.Size()
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to calculate storage stats: %w", err)
	}

	stats["task_count"] = taskCount
	stats["checkpoint_count"] = checkpointCount
	stats["total_size_bytes"] = totalSize
	stats["storage_path"] = fcs.basePath

	return stats, nil
}
