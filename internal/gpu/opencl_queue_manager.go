//go:build opencl
// +build opencl

package gpu

import (
	"fmt"
	"log"
	"sort"
	"sync"
	"sync/atomic"
	"time"
)

// QueuePriority defines the priority levels for OpenCL command queues
type QueuePriority int

const (
	QueuePriorityLow    QueuePriority = 0
	QueuePriorityNormal QueuePriority = 1
	QueuePriorityHigh   QueuePriority = 2
)

// QueueProperties defines OpenCL command queue property flags
const (
	QueuePropertyOutOfOrderExec  = 0x01
	QueuePropertyProfiling       = 0x02
	QueuePropertyOnDevice        = 0x04
	QueuePropertyOnDeviceDefault = 0x08
)

// QueueState represents the current state of a command queue
type QueueState int

const (
	QueueStateIdle QueueState = iota
	QueueStateBusy
	QueueStateError
)

// OpenCLCommandQueue interface represents the native OpenCL command queue
type OpenCLCommandQueue interface {
	Create(context uintptr, device uintptr, properties int) error
	Finish() error
	Flush() error
	GetInfo(param int) (interface{}, error)
	GetHandle() uintptr
	Release() error
}

// ManagedOpenCLQueue extends OpenCLCommandQueue with management features
type ManagedOpenCLQueue struct {
	OpenCLCommandQueue
	id         string
	deviceID   int
	contextID  string
	priority   QueuePriority
	properties int
	state      QueueState
	inUse      bool
	lastUsed   time.Time
	createdAt  time.Time

	// Usage statistics
	usageCount int64
	totalTime  time.Duration
	errorCount int64

	// Thread safety
	mu sync.RWMutex
}

// NewManagedOpenCLQueue creates a new managed OpenCL command queue
func NewManagedOpenCLQueue(id string, deviceID int, contextID string, priority QueuePriority, properties int, logger *log.Logger) (*ManagedOpenCLQueue, error) {
	queue := &MockOpenCLCommandQueue{} // In real implementation, use actual OpenCL queue
	if err := queue.Create(0, uintptr(deviceID), properties); err != nil {
		return nil, fmt.Errorf("failed to create OpenCL command queue: %w", err)
	}

	managed := &ManagedOpenCLQueue{
		OpenCLCommandQueue: queue,
		id:                 id,
		deviceID:           deviceID,
		contextID:          contextID,
		priority:           priority,
		properties:         properties,
		state:              QueueStateIdle,
		lastUsed:           time.Now(),
		createdAt:          time.Now(),
	}

	return managed, nil
}

// Management methods
func (q *ManagedOpenCLQueue) GetID() string {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return q.id
}

func (q *ManagedOpenCLQueue) GetDeviceID() int {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return q.deviceID
}

func (q *ManagedOpenCLQueue) GetContextID() string {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return q.contextID
}

func (q *ManagedOpenCLQueue) GetPriority() QueuePriority {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return q.priority
}

func (q *ManagedOpenCLQueue) GetProperties() int {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return q.properties
}

func (q *ManagedOpenCLQueue) GetState() QueueState {
	q.mu.RLock()
	defer q.mu.RUnlock()

	if q.errorCount > 0 {
		return QueueStateError
	}
	if q.inUse {
		return QueueStateBusy
	}
	return QueueStateIdle
}

func (q *ManagedOpenCLQueue) IsInUse() bool {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return q.inUse
}

func (q *ManagedOpenCLQueue) SetInUse(inUse bool) {
	q.mu.Lock()
	defer q.mu.Unlock()

	if !q.inUse && inUse {
		atomic.AddInt64(&q.usageCount, 1)
	}
	q.inUse = inUse
	q.lastUsed = time.Now()
}

func (q *ManagedOpenCLQueue) GetUsageStats() (int64, time.Duration, int64) {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return q.usageCount, q.totalTime, q.errorCount
}

func (q *ManagedOpenCLQueue) UpdateTotalTime(duration time.Duration) {
	q.mu.Lock()
	defer q.mu.Unlock()
	q.totalTime += duration
}

func (q *ManagedOpenCLQueue) IncrementErrorCount() {
	atomic.AddInt64(&q.errorCount, 1)
}

func (q *ManagedOpenCLQueue) GetLastUsed() time.Time {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return q.lastUsed
}

func (q *ManagedOpenCLQueue) Destroy() error {
	q.mu.Lock()
	defer q.mu.Unlock()

	if q.OpenCLCommandQueue != nil {
		return q.OpenCLCommandQueue.Release()
	}
	return nil
}

// QueuePool manages a pool of OpenCL command queues for a single device/context
type QueuePool struct {
	deviceID  int
	contextID string
	minQueues int
	maxQueues int
	logger    *log.Logger

	// Available queues organized by priority
	availableQueues map[QueuePriority][]*ManagedOpenCLQueue
	allQueues       []*ManagedOpenCLQueue

	// Thread safety
	mu sync.Mutex
}

// NewQueuePool creates a new OpenCL command queue pool
func NewQueuePool(deviceID int, contextID string, minQueues, maxQueues int, logger *log.Logger) *QueuePool {
	pool := &QueuePool{
		deviceID:        deviceID,
		contextID:       contextID,
		minQueues:       minQueues,
		maxQueues:       maxQueues,
		logger:          logger,
		availableQueues: make(map[QueuePriority][]*ManagedOpenCLQueue),
		allQueues:       make([]*ManagedOpenCLQueue, 0),
	}

	// Initialize available queues for each priority
	pool.availableQueues[QueuePriorityLow] = make([]*ManagedOpenCLQueue, 0)
	pool.availableQueues[QueuePriorityNormal] = make([]*ManagedOpenCLQueue, 0)
	pool.availableQueues[QueuePriorityHigh] = make([]*ManagedOpenCLQueue, 0)

	// Create minimum number of queues
	for i := 0; i < minQueues; i++ {
		queue, err := pool.createQueue(QueuePriorityNormal)
		if err != nil {
			logger.Printf("Failed to create initial queue %d: %v", i, err)
			continue
		}
		pool.allQueues = append(pool.allQueues, queue)
		pool.availableQueues[QueuePriorityNormal] = append(pool.availableQueues[QueuePriorityNormal], queue)
	}

	logger.Printf("Created OpenCL queue pool for device %d with %d initial queues", deviceID, len(pool.allQueues))
	return pool
}

// AcquireQueue acquires a command queue with the specified priority
func (p *QueuePool) AcquireQueue(priority QueuePriority) (*ManagedOpenCLQueue, error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	// First, try to find an available queue with matching or higher priority
	for pri := priority; pri <= QueuePriorityHigh; pri++ {
		if queues := p.availableQueues[pri]; len(queues) > 0 {
			queue := queues[0]
			p.availableQueues[pri] = queues[1:]
			queue.SetInUse(true)
			p.logger.Printf("Acquired queue %s (priority %v) for device %d", queue.GetID(), pri, p.deviceID)
			return queue, nil
		}
	}

	// If no queue available with desired priority, try lower priorities
	for pri := priority - 1; pri >= QueuePriorityLow; pri-- {
		if queues := p.availableQueues[pri]; len(queues) > 0 {
			queue := queues[0]
			p.availableQueues[pri] = queues[1:]
			queue.SetInUse(true)
			p.logger.Printf("Acquired queue %s (priority %v, requested %v) for device %d", queue.GetID(), pri, priority, p.deviceID)
			return queue, nil
		}
	}

	// If pool is not at capacity, create a new queue
	if len(p.allQueues) < p.maxQueues {
		queue, err := p.createQueue(priority)
		if err != nil {
			return nil, fmt.Errorf("failed to create new queue: %w", err)
		}

		p.allQueues = append(p.allQueues, queue)
		queue.SetInUse(true)
		p.logger.Printf("Created and acquired new queue %s (priority %v) for device %d", queue.GetID(), priority, p.deviceID)
		return queue, nil
	}

	// Pool is at capacity, find LRU queue
	lruQueue := p.findLRUQueue()
	if lruQueue != nil {
		lruQueue.SetInUse(true)
		p.logger.Printf("Acquired LRU queue %s (priority %v) for device %d", lruQueue.GetID(), lruQueue.GetPriority(), p.deviceID)
		return lruQueue, nil
	}

	return nil, fmt.Errorf("no queues available and pool at capacity")
}

// ReleaseQueue releases a command queue back to the pool
func (p *QueuePool) ReleaseQueue(queue *ManagedOpenCLQueue) error {
	if queue == nil {
		return fmt.Errorf("cannot release nil queue")
	}

	p.mu.Lock()
	defer p.mu.Unlock()

	queue.SetInUse(false)

	// Add back to appropriate priority queue
	priority := queue.GetPriority()
	p.availableQueues[priority] = append(p.availableQueues[priority], queue)

	// Sort by last used time (LRU)
	sort.Slice(p.availableQueues[priority], func(i, j int) bool {
		return p.availableQueues[priority][i].GetLastUsed().Before(p.availableQueues[priority][j].GetLastUsed())
	})

	p.logger.Printf("Released queue %s (priority %v) for device %d", queue.GetID(), priority, p.deviceID)
	return nil
}

// GetPoolStats returns statistics about the queue pool
func (p *QueuePool) GetPoolStats() map[string]interface{} {
	p.mu.Lock()
	defer p.mu.Unlock()

	available := 0
	for _, queues := range p.availableQueues {
		available += len(queues)
	}

	priorities := make(map[QueuePriority]int)
	for priority, queues := range p.availableQueues {
		priorities[priority] = len(queues)
	}

	return map[string]interface{}{
		"device_id":    p.deviceID,
		"context_id":   p.contextID,
		"min_queues":   p.minQueues,
		"max_queues":   p.maxQueues,
		"total_queues": len(p.allQueues),
		"available":    available,
		"in_use":       len(p.allQueues) - available,
		"priorities":   priorities,
	}
}

// Cleanup destroys all queues in the pool
func (p *QueuePool) Cleanup() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	var errors []error
	for _, queue := range p.allQueues {
		if err := queue.Destroy(); err != nil {
			errors = append(errors, err)
		}
	}

	p.allQueues = nil
	for priority := range p.availableQueues {
		p.availableQueues[priority] = nil
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors during cleanup: %v", errors)
	}

	p.logger.Printf("Cleaned up OpenCL queue pool for device %d", p.deviceID)
	return nil
}

// Private helper methods
func (p *QueuePool) createQueue(priority QueuePriority) (*ManagedOpenCLQueue, error) {
	queueID := fmt.Sprintf("opencl-queue-dev%d-ctx%s-%d", p.deviceID, p.contextID, time.Now().UnixNano())

	// Set properties based on priority
	properties := QueuePropertyProfiling // Always enable profiling for performance monitoring
	if priority == QueuePriorityHigh {
		// High priority queues could use out-of-order execution for better performance
		properties |= QueuePropertyOutOfOrderExec
	}

	return NewManagedOpenCLQueue(queueID, p.deviceID, p.contextID, priority, properties, p.logger)
}

func (p *QueuePool) findLRUQueue() *ManagedOpenCLQueue {
	var lruQueue *ManagedOpenCLQueue
	var oldestTime time.Time

	for _, queue := range p.allQueues {
		if !queue.IsInUse() {
			lastUsed := queue.GetLastUsed()
			if lruQueue == nil || lastUsed.Before(oldestTime) {
				lruQueue = queue
				oldestTime = lastUsed
			}
		}
	}

	return lruQueue
}

// OpenCLQueueManager manages command queues across multiple OpenCL devices
type OpenCLQueueManager struct {
	pools         map[string]*QueuePool // key: deviceID-contextID
	defaultDevice int
	started       bool
	logger        *log.Logger

	// Performance metrics
	totalAcquires  int64
	totalReleases  int64
	totalCreations int64
	totalErrors    int64

	// Thread safety
	mu sync.RWMutex
}

// NewOpenCLQueueManager creates a new OpenCL queue manager
func NewOpenCLQueueManager(logger *log.Logger) *OpenCLQueueManager {
	if logger == nil {
		logger = log.Default()
	}

	return &OpenCLQueueManager{
		pools:  make(map[string]*QueuePool),
		logger: logger,
	}
}

// Initialize initializes the queue manager for the specified devices and contexts
func (qm *OpenCLQueueManager) Initialize(devices []int, contexts map[int]string, minQueues, maxQueues int) error {
	qm.mu.Lock()
	defer qm.mu.Unlock()

	if qm.started {
		return fmt.Errorf("queue manager already initialized")
	}

	if len(devices) == 0 {
		return fmt.Errorf("no devices specified")
	}

	// Create pools for each device-context combination
	for _, deviceID := range devices {
		contextID, exists := contexts[deviceID]
		if !exists {
			contextID = fmt.Sprintf("default-ctx-%d", deviceID)
		}

		poolKey := fmt.Sprintf("%d-%s", deviceID, contextID)
		pool := NewQueuePool(deviceID, contextID, minQueues, maxQueues, qm.logger)
		qm.pools[poolKey] = pool
	}

	qm.defaultDevice = devices[0]
	qm.started = true

	qm.logger.Printf("OpenCL queue manager initialized with %d device pools", len(qm.pools))
	return nil
}

// AcquireQueue acquires a command queue for the specified device and context
func (qm *OpenCLQueueManager) AcquireQueue(deviceID int, contextID string, priority QueuePriority) (*ManagedOpenCLQueue, error) {
	qm.mu.RLock()
	defer qm.mu.RUnlock()

	if !qm.started {
		return nil, fmt.Errorf("queue manager not initialized")
	}

	poolKey := fmt.Sprintf("%d-%s", deviceID, contextID)
	pool, exists := qm.pools[poolKey]
	if !exists {
		return nil, fmt.Errorf("no pool for device %d context %s", deviceID, contextID)
	}

	queue, err := pool.AcquireQueue(priority)
	if err != nil {
		atomic.AddInt64(&qm.totalErrors, 1)
		return nil, err
	}

	atomic.AddInt64(&qm.totalAcquires, 1)
	return queue, nil
}

// AcquireDefaultQueue acquires a command queue for the default device
func (qm *OpenCLQueueManager) AcquireDefaultQueue(priority QueuePriority) (*ManagedOpenCLQueue, error) {
	qm.mu.RLock()
	defer qm.mu.RUnlock()

	// Find the default device's context
	var contextID string
	for _, pool := range qm.pools {
		if pool.deviceID == qm.defaultDevice {
			contextID = pool.contextID
			break
		}
	}

	if contextID == "" {
		return nil, fmt.Errorf("no context found for default device %d", qm.defaultDevice)
	}

	return qm.AcquireQueue(qm.defaultDevice, contextID, priority)
}

// ReleaseQueue releases a command queue back to its pool
func (qm *OpenCLQueueManager) ReleaseQueue(queue *ManagedOpenCLQueue) error {
	if queue == nil {
		return fmt.Errorf("cannot release nil queue")
	}

	qm.mu.RLock()
	defer qm.mu.RUnlock()

	if !qm.started {
		return fmt.Errorf("queue manager not initialized")
	}

	poolKey := fmt.Sprintf("%d-%s", queue.GetDeviceID(), queue.GetContextID())
	pool, exists := qm.pools[poolKey]
	if !exists {
		return fmt.Errorf("no pool for device %d context %s", queue.GetDeviceID(), queue.GetContextID())
	}

	err := pool.ReleaseQueue(queue)
	if err == nil {
		atomic.AddInt64(&qm.totalReleases, 1)
	} else {
		atomic.AddInt64(&qm.totalErrors, 1)
	}

	return err
}

// GetManagerStats returns statistics about the queue manager
func (qm *OpenCLQueueManager) GetManagerStats() map[string]interface{} {
	qm.mu.RLock()
	defer qm.mu.RUnlock()

	pools := make(map[string]interface{})
	for _, pool := range qm.pools {
		pools[fmt.Sprintf("device_%d_context_%s", pool.deviceID, pool.contextID)] = pool.GetPoolStats()
	}

	return map[string]interface{}{
		"started":         qm.started,
		"total_pools":     len(qm.pools),
		"default_device":  qm.defaultDevice,
		"total_acquires":  qm.totalAcquires,
		"total_releases":  qm.totalReleases,
		"total_creations": qm.totalCreations,
		"total_errors":    qm.totalErrors,
		"pools":           pools,
	}
}

// Cleanup shuts down the queue manager and all pools
func (qm *OpenCLQueueManager) Cleanup() error {
	qm.mu.Lock()
	defer qm.mu.Unlock()

	if !qm.started {
		return nil
	}

	var errors []error
	for poolKey, pool := range qm.pools {
		if err := pool.Cleanup(); err != nil {
			errors = append(errors, fmt.Errorf("pool %s: %w", poolKey, err))
		}
	}

	qm.pools = make(map[string]*QueuePool)
	qm.started = false

	if len(errors) > 0 {
		return fmt.Errorf("errors during cleanup: %v", errors)
	}

	qm.logger.Printf("OpenCL queue manager cleaned up")
	return nil
}

// MockOpenCLCommandQueue implements OpenCLCommandQueue for testing
type MockOpenCLCommandQueue struct {
	handle   uintptr
	created  bool
	released bool
}

func (m *MockOpenCLCommandQueue) Create(context uintptr, device uintptr, properties int) error {
	m.created = true
	m.handle = uintptr(time.Now().UnixNano()) // Use timestamp as mock handle
	return nil
}

func (m *MockOpenCLCommandQueue) Finish() error {
	if !m.created || m.released {
		return fmt.Errorf("queue not available")
	}
	return nil
}

func (m *MockOpenCLCommandQueue) Flush() error {
	if !m.created || m.released {
		return fmt.Errorf("queue not available")
	}
	return nil
}

func (m *MockOpenCLCommandQueue) GetInfo(param int) (interface{}, error) {
	if !m.created || m.released {
		return nil, fmt.Errorf("queue not available")
	}
	return "mock_info", nil
}

func (m *MockOpenCLCommandQueue) GetHandle() uintptr {
	return m.handle
}

func (m *MockOpenCLCommandQueue) Release() error {
	m.released = true
	m.created = false
	return nil
}
