package gpu

import (
	"fmt"
	"math"
	"unsafe"
)

/*
#include <string.h>
*/
import "C"

// Element access methods

// GetFloat32 retrieves a float32 value at the specified indices
func (t *Tensor) GetFloat32(indices ...int64) (float32, error) {
	if t.dtype != TensorFloat32 {
		return 0, fmt.Errorf("tensor data type is %s, not float32", t.dtype)
	}

	index, err := t.linearIndex(indices...)
	if err != nil {
		return 0, err
	}

	if t.device != DeviceCPU {
		return 0, fmt.Errorf("GPU tensor access not yet implemented")
	}

	// Calculate byte offset
	byteOffset := index * int64(t.dtype.Size())
	ptr := unsafe.Pointer(uintptr(t.Data()) + uintptr(byteOffset))
	return *(*float32)(ptr), nil
}

// SetFloat32 sets a float32 value at the specified indices
func (t *Tensor) SetFloat32(value float32, indices ...int64) error {
	if t.dtype != TensorFloat32 {
		return fmt.Errorf("tensor data type is %s, not float32", t.dtype)
	}

	index, err := t.linearIndex(indices...)
	if err != nil {
		return err
	}

	if t.device != DeviceCPU {
		return fmt.Errorf("GPU tensor access not yet implemented")
	}

	// Calculate byte offset
	byteOffset := index * int64(t.dtype.Size())
	ptr := unsafe.Pointer(uintptr(t.Data()) + uintptr(byteOffset))
	*(*float32)(ptr) = value
	return nil
}

// GetInt8 retrieves an int8 value at the specified indices
func (t *Tensor) GetInt8(indices ...int64) (int8, error) {
	if t.dtype != TensorInt8 {
		return 0, fmt.Errorf("tensor data type is %s, not int8", t.dtype)
	}

	index, err := t.linearIndex(indices...)
	if err != nil {
		return 0, err
	}

	if t.device != DeviceCPU {
		return 0, fmt.Errorf("GPU tensor access not yet implemented")
	}

	// Calculate byte offset
	byteOffset := index * int64(t.dtype.Size())
	ptr := unsafe.Pointer(uintptr(t.Data()) + uintptr(byteOffset))
	return *(*int8)(ptr), nil
}

// SetInt8 sets an int8 value at the specified indices
func (t *Tensor) SetInt8(value int8, indices ...int64) error {
	if t.dtype != TensorInt8 {
		return fmt.Errorf("tensor data type is %s, not int8", t.dtype)
	}

	index, err := t.linearIndex(indices...)
	if err != nil {
		return err
	}

	if t.device != DeviceCPU {
		return fmt.Errorf("GPU tensor access not yet implemented")
	}

	// Calculate byte offset
	byteOffset := index * int64(t.dtype.Size())
	ptr := unsafe.Pointer(uintptr(t.Data()) + uintptr(byteOffset))
	*(*int8)(ptr) = value
	return nil
}

// Element-wise operations

// Add performs element-wise addition with another tensor
func (t *Tensor) Add(other *Tensor) (*Tensor, error) {
	if err := t.validateBinaryOp(other); err != nil {
		return nil, err
	}

	result, err := NewTensor(t.shape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	result.SetMemoryPool(t.memPool)

	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU tensor operations not yet implemented")
	}

	return t.performElementWiseOp(other, result, func(a, b float64) float64 { return a + b })
}

// Subtract performs element-wise subtraction with another tensor
func (t *Tensor) Subtract(other *Tensor) (*Tensor, error) {
	if err := t.validateBinaryOp(other); err != nil {
		return nil, err
	}

	result, err := NewTensor(t.shape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	result.SetMemoryPool(t.memPool)

	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU tensor operations not yet implemented")
	}

	return t.performElementWiseOp(other, result, func(a, b float64) float64 { return a - b })
}

// Multiply performs element-wise multiplication with another tensor
func (t *Tensor) Multiply(other *Tensor) (*Tensor, error) {
	if err := t.validateBinaryOp(other); err != nil {
		return nil, err
	}

	result, err := NewTensor(t.shape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	result.SetMemoryPool(t.memPool)

	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU tensor operations not yet implemented")
	}

	return t.performElementWiseOp(other, result, func(a, b float64) float64 { return a * b })
}

// Divide performs element-wise division with another tensor
func (t *Tensor) Divide(other *Tensor) (*Tensor, error) {
	if err := t.validateBinaryOp(other); err != nil {
		return nil, err
	}

	result, err := NewTensor(t.shape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	result.SetMemoryPool(t.memPool)

	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU tensor operations not yet implemented")
	}

	return t.performElementWiseOp(other, result, func(a, b float64) float64 {
		if b == 0 {
			return math.Inf(1) // Return positive infinity for division by zero
		}
		return a / b
	})
}

// Matrix operations

// MatMul performs matrix multiplication with another tensor
func (t *Tensor) MatMul(other *Tensor) (*Tensor, error) {
	// Validate inputs for matrix multiplication
	if t.Rank() != 2 || other.Rank() != 2 {
		return nil, fmt.Errorf("matrix multiplication requires 2D tensors, got %dD and %dD", t.Rank(), other.Rank())
	}

	if t.shape[1] != other.shape[0] {
		return nil, fmt.Errorf("incompatible shapes for matrix multiplication: [%d, %d] x [%d, %d]",
			t.shape[0], t.shape[1], other.shape[0], other.shape[1])
	}

	if t.dtype != other.dtype {
		return nil, fmt.Errorf("data type mismatch: %s vs %s", t.dtype, other.dtype)
	}

	if t.device != other.device {
		return nil, fmt.Errorf("device mismatch: %s vs %s", t.device, other.device)
	}

	// Result shape: [t.shape[0], other.shape[1]]
	resultShape := TensorShape{t.shape[0], other.shape[1]}
	result, err := NewTensor(resultShape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	result.SetMemoryPool(t.memPool)

	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU matrix multiplication not yet implemented")
	}

	// Perform matrix multiplication for CPU tensors
	return t.performMatMulCPU(other, result)
}

// Transpose creates a transposed version of the tensor (2D only for now)
func (t *Tensor) Transpose() (*Tensor, error) {
	if t.Rank() != 2 {
		return nil, fmt.Errorf("transpose currently only supports 2D tensors, got %dD", t.Rank())
	}

	// Transposed shape
	transposedShape := TensorShape{t.shape[1], t.shape[0]}
	result, err := NewTensor(transposedShape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	result.SetMemoryPool(t.memPool)

	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU tensor transpose not yet implemented")
	}

	// Perform transpose for CPU tensors
	return t.performTransposeCPU(result)
}

// Utility methods

// Fill fills the tensor with a specific value
func (t *Tensor) Fill(value interface{}) error {
	if t.device != DeviceCPU {
		return fmt.Errorf("GPU tensor fill not yet implemented")
	}

	numElements := t.NumElements()

	switch t.dtype {
	case TensorFloat32:
		var fillValue float32
		switch v := value.(type) {
		case float32:
			fillValue = v
		case float64:
			fillValue = float32(v)
		case int:
			fillValue = float32(v)
		default:
			return fmt.Errorf("cannot convert %T to float32", value)
		}

		for i := int64(0); i < numElements; i++ {
			byteOffset := i * 4
			ptr := unsafe.Pointer(uintptr(t.Data()) + uintptr(byteOffset))
			*(*float32)(ptr) = fillValue
		}

	case TensorInt8:
		var fillValue int8
		switch v := value.(type) {
		case int8:
			fillValue = v
		case int:
			if v < -128 || v > 127 {
				return fmt.Errorf("value %d out of range for int8", v)
			}
			fillValue = int8(v)
		default:
			return fmt.Errorf("cannot convert %T to int8", value)
		}

		for i := int64(0); i < numElements; i++ {
			byteOffset := i * 1
			ptr := unsafe.Pointer(uintptr(t.Data()) + uintptr(byteOffset))
			*(*int8)(ptr) = fillValue
		}

	default:
		return fmt.Errorf("fill not implemented for data type %s", t.dtype)
	}

	return nil
}

// Zero fills the tensor with zeros
func (t *Tensor) Zero() error {
	if t.device != DeviceCPU {
		return fmt.Errorf("GPU tensor zero not yet implemented")
	}

	// Use C.memset for efficient zeroing
	C.memset(t.Data(), 0, C.size_t(t.size))
	return nil
}

// Ones fills the tensor with ones
func (t *Tensor) Ones() error {
	switch t.dtype {
	case TensorFloat32:
		return t.Fill(float32(1.0))
	case TensorInt8:
		return t.Fill(int8(1))
	case TensorInt32:
		return t.Fill(int32(1))
	case TensorBool:
		return t.Fill(true)
	default:
		return fmt.Errorf("ones not implemented for data type %s", t.dtype)
	}
}

// Data conversion methods (simplified versions)

// ToFloat32 converts tensor to float32 data type
func (t *Tensor) ToFloat32() (*Tensor, error) {
	if t.dtype == TensorFloat32 {
		return t.Clone()
	}
	return nil, fmt.Errorf("data type conversion not yet fully implemented")
}

// ToInt8 converts tensor to int8 data type
func (t *Tensor) ToInt8() (*Tensor, error) {
	if t.dtype == TensorInt8 {
		return t.Clone()
	}
	return nil, fmt.Errorf("data type conversion not yet fully implemented")
}

// ToDevice moves tensor to specified device
func (t *Tensor) ToDevice(device TensorDevice, deviceID int) (*Tensor, error) {
	if t.device == device && t.deviceID == deviceID {
		return t.Clone()
	}
	return nil, fmt.Errorf("device transfer not yet implemented")
}

// Helper methods

// validateBinaryOp validates that two tensors can be used in a binary operation
func (t *Tensor) validateBinaryOp(other *Tensor) error {
	if other == nil {
		return fmt.Errorf("other tensor is nil")
	}

	if t.dtype != other.dtype {
		return fmt.Errorf("data type mismatch: %s vs %s", t.dtype, other.dtype)
	}

	if t.device != other.device {
		return fmt.Errorf("device mismatch: %s vs %s", t.device, other.device)
	}

	// Check shape compatibility (broadcasting not implemented yet)
	if len(t.shape) != len(other.shape) {
		return fmt.Errorf("shape rank mismatch: %d vs %d", len(t.shape), len(other.shape))
	}

	for i, dim := range t.shape {
		if dim != other.shape[i] {
			return fmt.Errorf("shape mismatch at dimension %d: %d vs %d", i, dim, other.shape[i])
		}
	}

	return nil
}

// performElementWiseOp performs an element-wise operation using the provided function
func (t *Tensor) performElementWiseOp(other *Tensor, result *Tensor, op func(float64, float64) float64) (*Tensor, error) {
	numElements := t.NumElements()

	for i := int64(0); i < numElements; i++ {
		var a, b float64
		var err error

		// Get values from both tensors
		a, err = t.getElementAsFloat64(i)
		if err != nil {
			return nil, err
		}

		b, err = other.getElementAsFloat64(i)
		if err != nil {
			return nil, err
		}

		// Perform operation
		resultValue := op(a, b)

		// Set result value
		err = result.setElementFromFloat64(i, resultValue)
		if err != nil {
			return nil, err
		}
	}

	return result, nil
}

// getElementAsFloat64 retrieves an element as float64 regardless of the underlying type
func (t *Tensor) getElementAsFloat64(index int64) (float64, error) {
	byteOffset := index * int64(t.dtype.Size())
	ptr := unsafe.Pointer(uintptr(t.Data()) + uintptr(byteOffset))

	switch t.dtype {
	case TensorFloat32:
		return float64(*(*float32)(ptr)), nil
	case TensorInt8:
		return float64(*(*int8)(ptr)), nil
	case TensorInt32:
		return float64(*(*int32)(ptr)), nil
	case TensorBool:
		if *(*bool)(ptr) {
			return 1.0, nil
		}
		return 0.0, nil
	default:
		return 0, fmt.Errorf("unsupported data type for conversion: %s", t.dtype)
	}
}

// setElementFromFloat64 sets an element from a float64 value, converting to the appropriate type
func (t *Tensor) setElementFromFloat64(index int64, value float64) error {
	byteOffset := index * int64(t.dtype.Size())
	ptr := unsafe.Pointer(uintptr(t.Data()) + uintptr(byteOffset))

	switch t.dtype {
	case TensorFloat32:
		*(*float32)(ptr) = float32(value)
	case TensorInt8:
		// Clamp to int8 range
		if value > 127 {
			*(*int8)(ptr) = 127
		} else if value < -128 {
			*(*int8)(ptr) = -128
		} else {
			*(*int8)(ptr) = int8(value)
		}
	case TensorInt32:
		*(*int32)(ptr) = int32(value)
	case TensorBool:
		*(*bool)(ptr) = value != 0
	default:
		return fmt.Errorf("unsupported data type for conversion: %s", t.dtype)
	}

	return nil
}

// performMatMulCPU performs matrix multiplication on CPU
func (t *Tensor) performMatMulCPU(other *Tensor, result *Tensor) (*Tensor, error) {
	M := t.shape[0]     // rows of A
	N := other.shape[1] // cols of B
	K := t.shape[1]     // cols of A / rows of B

	// Initialize result to zero
	result.Zero()

	// Perform matrix multiplication: C[i][j] = sum(A[i][k] * B[k][j])
	for i := int64(0); i < M; i++ {
		for j := int64(0); j < N; j++ {
			var sum float64

			for k := int64(0); k < K; k++ {
				// Get A[i][k]
				aIndex := i*K + k
				aVal, err := t.getElementAsFloat64(aIndex)
				if err != nil {
					return nil, err
				}

				// Get B[k][j]
				bIndex := k*N + j
				bVal, err := other.getElementAsFloat64(bIndex)
				if err != nil {
					return nil, err
				}

				sum += aVal * bVal
			}

			// Set C[i][j]
			cIndex := i*N + j
			err := result.setElementFromFloat64(cIndex, sum)
			if err != nil {
				return nil, err
			}
		}
	}

	return result, nil
}

// performTransposeCPU performs matrix transpose on CPU
func (t *Tensor) performTransposeCPU(result *Tensor) (*Tensor, error) {
	rows := t.shape[0]
	cols := t.shape[1]

	for i := int64(0); i < rows; i++ {
		for j := int64(0); j < cols; j++ {
			// Get element at [i][j] from original
			srcIndex := i*cols + j
			value, err := t.getElementAsFloat64(srcIndex)
			if err != nil {
				return nil, err
			}

			// Set element at [j][i] in result
			dstIndex := j*rows + i
			err = result.setElementFromFloat64(dstIndex, value)
			if err != nil {
				return nil, err
			}
		}
	}

	return result, nil
}

// Advanced Tensor Operations

// ConvolutionParams holds parameters for convolution operations
type ConvolutionParams struct {
	PaddingH  int64 // Padding height
	PaddingW  int64 // Padding width
	StrideH   int64 // Stride height
	StrideW   int64 // Stride width
	DilationH int64 // Dilation height
	DilationW int64 // Dilation width
}

// DefaultConvolutionParams returns default convolution parameters
func DefaultConvolutionParams() ConvolutionParams {
	return ConvolutionParams{
		PaddingH:  0,
		PaddingW:  0,
		StrideH:   1,
		StrideW:   1,
		DilationH: 1,
		DilationW: 1,
	}
}

// Conv2D performs 2D convolution operation
// Input tensor shape: [batch, channels, height, width] or [channels, height, width]
// Kernel tensor shape: [out_channels, in_channels, kernel_height, kernel_width]
// Returns: [batch, out_channels, out_height, out_width] or [out_channels, out_height, out_width]
func (t *Tensor) Conv2D(kernel *Tensor, params ConvolutionParams) (*Tensor, error) {
	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU convolution not yet implemented")
	}

	if t.dtype != kernel.dtype {
		return nil, fmt.Errorf("input and kernel data types must match: %s vs %s", t.dtype, kernel.dtype)
	}

	// Validate tensor dimensions
	if t.Rank() < 3 || t.Rank() > 4 {
		return nil, fmt.Errorf("input tensor must be 3D or 4D, got %dD", t.Rank())
	}

	if kernel.Rank() != 4 {
		return nil, fmt.Errorf("kernel tensor must be 4D, got %dD", kernel.Rank())
	}

	var batchSize, inChannels, inHeight, inWidth int64
	var hasBatch bool

	if t.Rank() == 4 {
		hasBatch = true
		batchSize = t.shape[0]
		inChannels = t.shape[1]
		inHeight = t.shape[2]
		inWidth = t.shape[3]
	} else {
		hasBatch = false
		batchSize = 1
		inChannels = t.shape[0]
		inHeight = t.shape[1]
		inWidth = t.shape[2]
	}

	outChannels := kernel.shape[0]
	kernelInChannels := kernel.shape[1]
	kernelH := kernel.shape[2]
	kernelW := kernel.shape[3]

	if inChannels != kernelInChannels {
		return nil, fmt.Errorf("input channels (%d) must match kernel input channels (%d)", inChannels, kernelInChannels)
	}

	// Calculate output dimensions
	outHeight := (inHeight+2*params.PaddingH-params.DilationH*(kernelH-1)-1)/params.StrideH + 1
	outWidth := (inWidth+2*params.PaddingW-params.DilationW*(kernelW-1)-1)/params.StrideW + 1

	if outHeight <= 0 || outWidth <= 0 {
		return nil, fmt.Errorf("invalid output dimensions: %dx%d", outHeight, outWidth)
	}

	// Create output tensor
	var outputShape []int64
	if hasBatch {
		outputShape = []int64{batchSize, outChannels, outHeight, outWidth}
	} else {
		outputShape = []int64{outChannels, outHeight, outWidth}
	}

	result, err := NewTensor(outputShape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	result.SetMemoryPool(t.memPool)

	// Initialize output to zero
	result.Zero()

	// Perform convolution
	return t.performConv2DCPU(kernel, result, params, hasBatch)
}

// performConv2DCPU performs 2D convolution on CPU using direct convolution
func (t *Tensor) performConv2DCPU(kernel *Tensor, result *Tensor, params ConvolutionParams, hasBatch bool) (*Tensor, error) {
	var batchSize, inChannels, inHeight, inWidth int64
	var outChannels, outHeight, outWidth int64

	if hasBatch {
		batchSize = t.shape[0]
		inChannels = t.shape[1]
		inHeight = t.shape[2]
		inWidth = t.shape[3]
		outChannels = result.shape[1]
		outHeight = result.shape[2]
		outWidth = result.shape[3]
	} else {
		batchSize = 1
		inChannels = t.shape[0]
		inHeight = t.shape[1]
		inWidth = t.shape[2]
		outChannels = result.shape[0]
		outHeight = result.shape[1]
		outWidth = result.shape[2]
	}

	kernelH := kernel.shape[2]
	kernelW := kernel.shape[3]

	// Iterate over batch
	for b := int64(0); b < batchSize; b++ {
		// Iterate over output channels
		for oc := int64(0); oc < outChannels; oc++ {
			// Iterate over output spatial dimensions
			for oh := int64(0); oh < outHeight; oh++ {
				for ow := int64(0); ow < outWidth; ow++ {
					var sum float64

					// Iterate over input channels
					for ic := int64(0); ic < inChannels; ic++ {
						// Iterate over kernel spatial dimensions
						for kh := int64(0); kh < kernelH; kh++ {
							for kw := int64(0); kw < kernelW; kw++ {
								// Calculate input coordinates
								ih := oh*params.StrideH - params.PaddingH + kh*params.DilationH
								iw := ow*params.StrideW - params.PaddingW + kw*params.DilationW

								// Check bounds (padding)
								if ih >= 0 && ih < inHeight && iw >= 0 && iw < inWidth {
									// Get input value
									var inputIndex int64
									if hasBatch {
										inputIndex = b*inChannels*inHeight*inWidth + ic*inHeight*inWidth + ih*inWidth + iw
									} else {
										inputIndex = ic*inHeight*inWidth + ih*inWidth + iw
									}

									inputVal, err := t.getElementAsFloat64(inputIndex)
									if err != nil {
										return nil, err
									}

									// Get kernel value
									kernelIndex := oc*inChannels*kernelH*kernelW + ic*kernelH*kernelW + kh*kernelW + kw
									kernelVal, err := kernel.getElementAsFloat64(kernelIndex)
									if err != nil {
										return nil, err
									}

									sum += inputVal * kernelVal
								}
							}
						}
					}

					// Set output value
					var outputIndex int64
					if hasBatch {
						outputIndex = b*outChannels*outHeight*outWidth + oc*outHeight*outWidth + oh*outWidth + ow
					} else {
						outputIndex = oc*outHeight*outWidth + oh*outWidth + ow
					}

					err := result.setElementFromFloat64(outputIndex, sum)
					if err != nil {
						return nil, err
					}
				}
			}
		}
	}

	return result, nil
}

// PoolingParams holds parameters for pooling operations
type PoolingParams struct {
	KernelH  int64 // Pooling kernel height
	KernelW  int64 // Pooling kernel width
	StrideH  int64 // Stride height
	StrideW  int64 // Stride width
	PaddingH int64 // Padding height
	PaddingW int64 // Padding width
}

// DefaultPoolingParams returns default pooling parameters
func DefaultPoolingParams(kernelSize int64) PoolingParams {
	return PoolingParams{
		KernelH:  kernelSize,
		KernelW:  kernelSize,
		StrideH:  kernelSize,
		StrideW:  kernelSize,
		PaddingH: 0,
		PaddingW: 0,
	}
}

// MaxPool2D performs 2D max pooling operation
// Input tensor shape: [batch, channels, height, width] or [channels, height, width]
func (t *Tensor) MaxPool2D(params PoolingParams) (*Tensor, error) {
	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU max pooling not yet implemented")
	}

	if t.Rank() < 3 || t.Rank() > 4 {
		return nil, fmt.Errorf("input tensor must be 3D or 4D, got %dD", t.Rank())
	}

	var batchSize, channels, inHeight, inWidth int64
	var hasBatch bool

	if t.Rank() == 4 {
		hasBatch = true
		batchSize = t.shape[0]
		channels = t.shape[1]
		inHeight = t.shape[2]
		inWidth = t.shape[3]
	} else {
		hasBatch = false
		batchSize = 1
		channels = t.shape[0]
		inHeight = t.shape[1]
		inWidth = t.shape[2]
	}

	// Calculate output dimensions
	outHeight := (inHeight+2*params.PaddingH-params.KernelH)/params.StrideH + 1
	outWidth := (inWidth+2*params.PaddingW-params.KernelW)/params.StrideW + 1

	if outHeight <= 0 || outWidth <= 0 {
		return nil, fmt.Errorf("invalid output dimensions: %dx%d", outHeight, outWidth)
	}

	// Create output tensor
	var outputShape []int64
	if hasBatch {
		outputShape = []int64{batchSize, channels, outHeight, outWidth}
	} else {
		outputShape = []int64{channels, outHeight, outWidth}
	}

	result, err := NewTensor(outputShape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	result.SetMemoryPool(t.memPool)

	return t.performMaxPool2DCPU(result, params, hasBatch)
}

// performMaxPool2DCPU performs 2D max pooling on CPU
func (t *Tensor) performMaxPool2DCPU(result *Tensor, params PoolingParams, hasBatch bool) (*Tensor, error) {
	var batchSize, channels, inHeight, inWidth int64
	var outHeight, outWidth int64

	if hasBatch {
		batchSize = t.shape[0]
		channels = t.shape[1]
		inHeight = t.shape[2]
		inWidth = t.shape[3]
		outHeight = result.shape[2]
		outWidth = result.shape[3]
	} else {
		batchSize = 1
		channels = t.shape[0]
		inHeight = t.shape[1]
		inWidth = t.shape[2]
		outHeight = result.shape[1]
		outWidth = result.shape[2]
	}

	// Iterate over batch
	for b := int64(0); b < batchSize; b++ {
		// Iterate over channels
		for c := int64(0); c < channels; c++ {
			// Iterate over output spatial dimensions
			for oh := int64(0); oh < outHeight; oh++ {
				for ow := int64(0); ow < outWidth; ow++ {
					maxVal := math.Inf(-1) // Negative infinity

					// Iterate over pooling window
					for kh := int64(0); kh < params.KernelH; kh++ {
						for kw := int64(0); kw < params.KernelW; kw++ {
							// Calculate input coordinates
							ih := oh*params.StrideH - params.PaddingH + kh
							iw := ow*params.StrideW - params.PaddingW + kw

							// Check bounds
							if ih >= 0 && ih < inHeight && iw >= 0 && iw < inWidth {
								// Get input value
								var inputIndex int64
								if hasBatch {
									inputIndex = b*channels*inHeight*inWidth + c*inHeight*inWidth + ih*inWidth + iw
								} else {
									inputIndex = c*inHeight*inWidth + ih*inWidth + iw
								}

								inputVal, err := t.getElementAsFloat64(inputIndex)
								if err != nil {
									return nil, err
								}

								if inputVal > maxVal {
									maxVal = inputVal
								}
							}
						}
					}

					// Set output value
					var outputIndex int64
					if hasBatch {
						outputIndex = b*channels*outHeight*outWidth + c*outHeight*outWidth + oh*outWidth + ow
					} else {
						outputIndex = c*outHeight*outWidth + oh*outWidth + ow
					}

					err := result.setElementFromFloat64(outputIndex, maxVal)
					if err != nil {
						return nil, err
					}
				}
			}
		}
	}

	return result, nil
}

// AvgPool2D performs 2D average pooling operation
// Input tensor shape: [batch, channels, height, width] or [channels, height, width]
func (t *Tensor) AvgPool2D(params PoolingParams) (*Tensor, error) {
	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU average pooling not yet implemented")
	}

	if t.Rank() < 3 || t.Rank() > 4 {
		return nil, fmt.Errorf("input tensor must be 3D or 4D, got %dD", t.Rank())
	}

	var batchSize, channels, inHeight, inWidth int64
	var hasBatch bool

	if t.Rank() == 4 {
		hasBatch = true
		batchSize = t.shape[0]
		channels = t.shape[1]
		inHeight = t.shape[2]
		inWidth = t.shape[3]
	} else {
		hasBatch = false
		batchSize = 1
		channels = t.shape[0]
		inHeight = t.shape[1]
		inWidth = t.shape[2]
	}

	// Calculate output dimensions
	outHeight := (inHeight+2*params.PaddingH-params.KernelH)/params.StrideH + 1
	outWidth := (inWidth+2*params.PaddingW-params.KernelW)/params.StrideW + 1

	if outHeight <= 0 || outWidth <= 0 {
		return nil, fmt.Errorf("invalid output dimensions: %dx%d", outHeight, outWidth)
	}

	// Create output tensor
	var outputShape []int64
	if hasBatch {
		outputShape = []int64{batchSize, channels, outHeight, outWidth}
	} else {
		outputShape = []int64{channels, outHeight, outWidth}
	}

	result, err := NewTensor(outputShape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	result.SetMemoryPool(t.memPool)

	return t.performAvgPool2DCPU(result, params, hasBatch)
}

// performAvgPool2DCPU performs 2D average pooling on CPU
func (t *Tensor) performAvgPool2DCPU(result *Tensor, params PoolingParams, hasBatch bool) (*Tensor, error) {
	var batchSize, channels, inHeight, inWidth int64
	var outHeight, outWidth int64

	if hasBatch {
		batchSize = t.shape[0]
		channels = t.shape[1]
		inHeight = t.shape[2]
		inWidth = t.shape[3]
		outHeight = result.shape[2]
		outWidth = result.shape[3]
	} else {
		batchSize = 1
		channels = t.shape[0]
		inHeight = t.shape[1]
		inWidth = t.shape[2]
		outHeight = result.shape[1]
		outWidth = result.shape[2]
	}

	// Iterate over batch
	for b := int64(0); b < batchSize; b++ {
		// Iterate over channels
		for c := int64(0); c < channels; c++ {
			// Iterate over output spatial dimensions
			for oh := int64(0); oh < outHeight; oh++ {
				for ow := int64(0); ow < outWidth; ow++ {
					var sum float64
					var count int64

					// Iterate over pooling window
					for kh := int64(0); kh < params.KernelH; kh++ {
						for kw := int64(0); kw < params.KernelW; kw++ {
							// Calculate input coordinates
							ih := oh*params.StrideH - params.PaddingH + kh
							iw := ow*params.StrideW - params.PaddingW + kw

							// Check bounds
							if ih >= 0 && ih < inHeight && iw >= 0 && iw < inWidth {
								// Get input value
								var inputIndex int64
								if hasBatch {
									inputIndex = b*channels*inHeight*inWidth + c*inHeight*inWidth + ih*inWidth + iw
								} else {
									inputIndex = c*inHeight*inWidth + ih*inWidth + iw
								}

								inputVal, err := t.getElementAsFloat64(inputIndex)
								if err != nil {
									return nil, err
								}

								sum += inputVal
								count++
							}
						}
					}

					// Calculate average
					var avgVal float64
					if count > 0 {
						avgVal = sum / float64(count)
					}

					// Set output value
					var outputIndex int64
					if hasBatch {
						outputIndex = b*channels*outHeight*outWidth + c*outHeight*outWidth + oh*outWidth + ow
					} else {
						outputIndex = c*outHeight*outWidth + oh*outWidth + ow
					}

					err := result.setElementFromFloat64(outputIndex, avgVal)
					if err != nil {
						return nil, err
					}
				}
			}
		}
	}

	return result, nil
}

// Activation Functions

// ReLU applies the Rectified Linear Unit activation function element-wise
// ReLU(x) = max(0, x)
func (t *Tensor) ReLU() (*Tensor, error) {
	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU ReLU not yet implemented")
	}

	result, err := NewTensor(t.shape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	result.SetMemoryPool(t.memPool)

	numElements := t.NumElements()
	for i := int64(0); i < numElements; i++ {
		val, err := t.getElementAsFloat64(i)
		if err != nil {
			return nil, err
		}

		// Apply ReLU: max(0, x)
		if val < 0 {
			val = 0
		}

		err = result.setElementFromFloat64(i, val)
		if err != nil {
			return nil, err
		}
	}

	return result, nil
}

// ReLUInPlace applies ReLU activation function in-place (modifies the tensor)
func (t *Tensor) ReLUInPlace() error {
	if t.device != DeviceCPU {
		return fmt.Errorf("GPU ReLU not yet implemented")
	}

	numElements := t.NumElements()
	for i := int64(0); i < numElements; i++ {
		val, err := t.getElementAsFloat64(i)
		if err != nil {
			return err
		}

		// Apply ReLU: max(0, x)
		if val < 0 {
			err = t.setElementFromFloat64(i, 0)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// Sigmoid applies the sigmoid activation function element-wise
// Sigmoid(x) = 1 / (1 + exp(-x))
func (t *Tensor) Sigmoid() (*Tensor, error) {
	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU Sigmoid not yet implemented")
	}

	result, err := NewTensor(t.shape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	result.SetMemoryPool(t.memPool)

	numElements := t.NumElements()
	for i := int64(0); i < numElements; i++ {
		val, err := t.getElementAsFloat64(i)
		if err != nil {
			return nil, err
		}

		// Apply Sigmoid: 1 / (1 + exp(-x))
		sigmoidVal := 1.0 / (1.0 + math.Exp(-val))

		err = result.setElementFromFloat64(i, sigmoidVal)
		if err != nil {
			return nil, err
		}
	}

	return result, nil
}

// Tanh applies the hyperbolic tangent activation function element-wise
// Tanh(x) = (exp(x) - exp(-x)) / (exp(x) + exp(-x))
func (t *Tensor) Tanh() (*Tensor, error) {
	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU Tanh not yet implemented")
	}

	result, err := NewTensor(t.shape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	result.SetMemoryPool(t.memPool)

	numElements := t.NumElements()
	for i := int64(0); i < numElements; i++ {
		val, err := t.getElementAsFloat64(i)
		if err != nil {
			return nil, err
		}

		// Apply Tanh using math.Tanh
		tanhVal := math.Tanh(val)

		err = result.setElementFromFloat64(i, tanhVal)
		if err != nil {
			return nil, err
		}
	}

	return result, nil
}

// Softmax applies the softmax activation function along the last dimension
// Softmax(x_i) = exp(x_i) / sum(exp(x_j)) for all j
func (t *Tensor) Softmax() (*Tensor, error) {
	if t.device != DeviceCPU {
		return nil, fmt.Errorf("GPU Softmax not yet implemented")
	}

	if t.Rank() == 0 {
		return nil, fmt.Errorf("cannot apply softmax to 0D tensor")
	}

	result, err := NewTensor(t.shape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}
	result.SetMemoryPool(t.memPool)

	// Apply softmax along the last dimension
	lastDim := t.shape[len(t.shape)-1]
	numGroups := t.NumElements() / lastDim

	for group := int64(0); group < numGroups; group++ {
		// Find max value for numerical stability
		var maxVal float64 = math.Inf(-1)
		for i := int64(0); i < lastDim; i++ {
			index := group*lastDim + i
			val, err := t.getElementAsFloat64(index)
			if err != nil {
				return nil, err
			}
			if val > maxVal {
				maxVal = val
			}
		}

		// Calculate sum of exp(x - max)
		var sumExp float64
		for i := int64(0); i < lastDim; i++ {
			index := group*lastDim + i
			val, err := t.getElementAsFloat64(index)
			if err != nil {
				return nil, err
			}
			expVal := math.Exp(val - maxVal)
			sumExp += expVal
		}

		// Apply softmax
		for i := int64(0); i < lastDim; i++ {
			index := group*lastDim + i
			val, err := t.getElementAsFloat64(index)
			if err != nil {
				return nil, err
			}
			softmaxVal := math.Exp(val-maxVal) / sumExp

			err = result.setElementFromFloat64(index, softmaxVal)
			if err != nil {
				return nil, err
			}
		}
	}

	return result, nil
}
