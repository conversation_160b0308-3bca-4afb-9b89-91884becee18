package gpu

import (
	"fmt"
	"os/exec"
	"strings"
	"time"
)

// CUDACompiler implements CUDA kernel compilation
type CUDACompiler struct {
	nvccPath    string
	cudaPath    string
	isAvailable bool
}

// NewCUDACompiler creates a new CUDA compiler instance
func NewCUDACompiler(compilerPaths map[string]string) *CUDACompiler {
	compiler := &CUDACompiler{}

	// Check for custom nvcc path
	if path, exists := compilerPaths["nvcc"]; exists {
		compiler.nvccPath = path
	} else {
		// Try to find nvcc in PATH
		if path, err := exec.LookPath("nvcc"); err == nil {
			compiler.nvccPath = path
		}
	}

	// Check if CUDA compiler is available
	compiler.isAvailable = compiler.checkAvailability()

	return compiler
}

// IsAvailable checks if the CUDA compiler is available
func (c *CUDACompiler) IsAvailable() bool {
	return c.isAvailable
}

// checkAvailability verifies CUDA compiler availability
func (c *CUDACompiler) checkAvailability() bool {
	if c.nvccPath == "" {
		return false
	}

	// Try to run nvcc --version
	cmd := exec.Command(c.nvccPath, "--version")
	return cmd.Run() == nil
}

// Compile compiles CUDA kernel source to binary
func (c *CUDACompiler) Compile(source *KernelSource, options CompilationOptions) (*CompiledKernel, error) {
	if !c.isAvailable {
		return nil, fmt.Errorf("CUDA compiler not available")
	}

	startTime := time.Now()

	// Create compiled kernel with stub implementation
	kernel := &CompiledKernel{
		ID:                source.ID + "_compiled",
		SourceID:          source.ID,
		Target:            options.Target,
		OptimizationLevel: options.OptimizationLevel,
		Binary:            []byte("// Compiled CUDA kernel stub"),
		PTX:               "// PTX code stub",
		CompilerVersion:   "11.8", // Stub version
		CompilerFlags:     options.CompilerFlags,
		CompilationTime:   time.Since(startTime),
		CompiledAt:        time.Now(),
		Hash:              "cuda_stub_hash",
		Size:              int64(len("// Compiled CUDA kernel stub")),
		Metadata:          make(map[string]interface{}),
		Performance: KernelPerformanceData{
			RegistersUsed:    32,
			SharedMemoryUsed: 1024,
			MaxOccupancy:     1.0,
			OptimalBlockSize: 256,
		},
	}

	return kernel, nil
}

// ValidateSource validates CUDA kernel source code
func (c *CUDACompiler) ValidateSource(source *KernelSource) error {
	if source.Language != KernelCUDA {
		return fmt.Errorf("source language %s not supported by CUDA compiler", source.Language)
	}

	if strings.TrimSpace(source.Source) == "" {
		return fmt.Errorf("source code is empty")
	}

	return nil
}

// GetSupportedTargets returns supported CUDA compilation targets
func (c *CUDACompiler) GetSupportedTargets() []CompilationTarget {
	return []CompilationTarget{
		{
			Architecture:       "sm_75",
			ComputeCapability:  ComputeCapability{Major: 7, Minor: 5},
			MaxThreadsPerBlock: 1024,
			SharedMemorySize:   49152,
			WarpSize:           32,
			Extensions:         []string{"compute_75", "sm_75"},
		},
		{
			Architecture:       "sm_80",
			ComputeCapability:  ComputeCapability{Major: 8, Minor: 0},
			MaxThreadsPerBlock: 1024,
			SharedMemorySize:   49152,
			WarpSize:           32,
			Extensions:         []string{"compute_80", "sm_80"},
		},
	}
}

// GetOptimalTarget returns optimal target for given GPU
func (c *CUDACompiler) GetOptimalTarget(gpu *GPUInfo) (CompilationTarget, error) {
	if gpu.Type != GPUTypeCUDA {
		return CompilationTarget{}, fmt.Errorf("GPU type %s not supported by CUDA compiler", gpu.Type)
	}

	// Return target based on compute capability
	targets := c.GetSupportedTargets()
	for _, target := range targets {
		if target.ComputeCapability.Major == gpu.ComputeCapability.Major &&
			target.ComputeCapability.Minor == gpu.ComputeCapability.Minor {
			return target, nil
		}
	}

	// Return first available target as fallback
	if len(targets) > 0 {
		return targets[0], nil
	}

	return CompilationTarget{}, fmt.Errorf("no suitable target found for GPU")
}

// GetCompilerVersion returns the CUDA compiler version
func (c *CUDACompiler) GetCompilerVersion() (string, error) {
	if !c.isAvailable {
		return "", fmt.Errorf("CUDA compiler not available")
	}

	return "11.8.0", nil // Stub version
}

// OpenCLCompiler implements OpenCL kernel compilation
type OpenCLCompiler struct {
	isAvailable bool
}

// NewOpenCLCompiler creates a new OpenCL compiler instance
func NewOpenCLCompiler(compilerPaths map[string]string) *OpenCLCompiler {
	compiler := &OpenCLCompiler{
		isAvailable: true, // OpenCL is generally available on most systems
	}
	return compiler
}

// IsAvailable checks if the OpenCL compiler is available
func (c *OpenCLCompiler) IsAvailable() bool {
	return c.isAvailable
}

// Compile compiles OpenCL kernel source to binary
func (c *OpenCLCompiler) Compile(source *KernelSource, options CompilationOptions) (*CompiledKernel, error) {
	startTime := time.Now()

	kernel := &CompiledKernel{
		ID:                source.ID + "_compiled",
		SourceID:          source.ID,
		Target:            options.Target,
		OptimizationLevel: options.OptimizationLevel,
		Binary:            []byte("// Compiled OpenCL kernel stub"),
		SPIRV:             []byte("// SPIR-V bytecode stub"),
		CompilerVersion:   "2.1", // Stub version
		CompilerFlags:     options.CompilerFlags,
		CompilationTime:   time.Since(startTime),
		CompiledAt:        time.Now(),
		Hash:              "opencl_stub_hash",
		Size:              int64(len("// Compiled OpenCL kernel stub")),
		Metadata:          make(map[string]interface{}),
		Performance: KernelPerformanceData{
			RegistersUsed:    16,
			LocalMemoryUsed:  512,
			MaxOccupancy:     0.8,
			OptimalBlockSize: 128,
		},
	}

	return kernel, nil
}

// ValidateSource validates OpenCL kernel source code
func (c *OpenCLCompiler) ValidateSource(source *KernelSource) error {
	if source.Language != KernelOpenCL {
		return fmt.Errorf("source language %s not supported by OpenCL compiler", source.Language)
	}

	if strings.TrimSpace(source.Source) == "" {
		return fmt.Errorf("source code is empty")
	}

	return nil
}

// GetSupportedTargets returns supported OpenCL compilation targets
func (c *OpenCLCompiler) GetSupportedTargets() []CompilationTarget {
	return []CompilationTarget{
		{
			Architecture:       "opencl_1_2",
			MaxThreadsPerBlock: 1024,
			SharedMemorySize:   32768,
			WarpSize:           32,
			Extensions:         []string{"cl_khr_fp64", "cl_khr_global_int32_base_atomics"},
		},
		{
			Architecture:       "opencl_2_0",
			MaxThreadsPerBlock: 1024,
			SharedMemorySize:   65536,
			WarpSize:           32,
			Extensions:         []string{"cl_khr_fp64", "cl_khr_subgroups"},
		},
	}
}

// GetOptimalTarget returns optimal target for given GPU
func (c *OpenCLCompiler) GetOptimalTarget(gpu *GPUInfo) (CompilationTarget, error) {
	targets := c.GetSupportedTargets()
	if len(targets) > 0 {
		return targets[len(targets)-1], nil // Return latest version
	}

	return CompilationTarget{}, fmt.Errorf("no suitable target found for GPU")
}

// GetCompilerVersion returns the OpenCL compiler version
func (c *OpenCLCompiler) GetCompilerVersion() (string, error) {
	return "2.1.0", nil // Stub version
}

// ROCmCompiler implements ROCm/HIP kernel compilation
type ROCmCompiler struct {
	hipccPath   string
	isAvailable bool
}

// NewROCmCompiler creates a new ROCm compiler instance
func NewROCmCompiler(compilerPaths map[string]string) *ROCmCompiler {
	compiler := &ROCmCompiler{}

	// Check for custom hipcc path
	if path, exists := compilerPaths["hipcc"]; exists {
		compiler.hipccPath = path
	} else {
		// Try to find hipcc in PATH
		if path, err := exec.LookPath("hipcc"); err == nil {
			compiler.hipccPath = path
		}
	}

	compiler.isAvailable = compiler.checkAvailability()

	return compiler
}

// IsAvailable checks if the ROCm compiler is available
func (c *ROCmCompiler) IsAvailable() bool {
	return c.isAvailable
}

// checkAvailability verifies ROCm compiler availability
func (c *ROCmCompiler) checkAvailability() bool {
	if c.hipccPath == "" {
		return false
	}

	// Try to run hipcc --version
	cmd := exec.Command(c.hipccPath, "--version")
	return cmd.Run() == nil
}

// Compile compiles ROCm kernel source to binary
func (c *ROCmCompiler) Compile(source *KernelSource, options CompilationOptions) (*CompiledKernel, error) {
	if !c.isAvailable {
		return nil, fmt.Errorf("ROCm compiler not available")
	}

	startTime := time.Now()

	kernel := &CompiledKernel{
		ID:                source.ID + "_compiled",
		SourceID:          source.ID,
		Target:            options.Target,
		OptimizationLevel: options.OptimizationLevel,
		Binary:            []byte("// Compiled ROCm kernel stub"),
		CompilerVersion:   "5.4.0", // Stub version
		CompilerFlags:     options.CompilerFlags,
		CompilationTime:   time.Since(startTime),
		CompiledAt:        time.Now(),
		Hash:              "rocm_stub_hash",
		Size:              int64(len("// Compiled ROCm kernel stub")),
		Metadata:          make(map[string]interface{}),
		Performance: KernelPerformanceData{
			RegistersUsed:    24,
			LocalMemoryUsed:  2048,
			MaxOccupancy:     0.9,
			OptimalBlockSize: 64,
		},
	}

	return kernel, nil
}

// ValidateSource validates ROCm kernel source code
func (c *ROCmCompiler) ValidateSource(source *KernelSource) error {
	if source.Language != KernelROCm {
		return fmt.Errorf("source language %s not supported by ROCm compiler", source.Language)
	}

	if strings.TrimSpace(source.Source) == "" {
		return fmt.Errorf("source code is empty")
	}

	return nil
}

// GetSupportedTargets returns supported ROCm compilation targets
func (c *ROCmCompiler) GetSupportedTargets() []CompilationTarget {
	return []CompilationTarget{
		{
			Architecture:       "gfx906",
			MaxThreadsPerBlock: 1024,
			SharedMemorySize:   65536,
			WarpSize:           64,
			Extensions:         []string{"gfx906"},
		},
		{
			Architecture:       "gfx1030",
			MaxThreadsPerBlock: 1024,
			SharedMemorySize:   65536,
			WarpSize:           32,
			Extensions:         []string{"gfx1030"},
		},
	}
}

// GetOptimalTarget returns optimal target for given GPU
func (c *ROCmCompiler) GetOptimalTarget(gpu *GPUInfo) (CompilationTarget, error) {
	targets := c.GetSupportedTargets()
	if len(targets) > 0 {
		return targets[0], nil
	}

	return CompilationTarget{}, fmt.Errorf("no suitable target found for GPU")
}

// GetCompilerVersion returns the ROCm compiler version
func (c *ROCmCompiler) GetCompilerVersion() (string, error) {
	if !c.isAvailable {
		return "", fmt.Errorf("ROCm compiler not available")
	}

	return "5.4.0", nil // Stub version
}

// Metal compiler removed - Linux only project
