package gpu

import "testing"

func TestEncryptDecryptModel(t *testing.T) {
	mgr := NewModelEncryptionManager(SecurityConfig{EnableModelEncryption: true})
	model := []byte{1, 2, 3, 4}
	encrypted, err := mgr.EncryptModel(model)
	if err != nil {
		t.Fatalf("EncryptModel error: %v", err)
	}
	decrypted, err := mgr.DecryptModel(encrypted)
	if err != nil {
		t.Fatalf("DecryptModel error: %v", err)
	}
	if len(decrypted) != len(model) {
		t.<PERSON>("Expected decrypted length %d, got %d", len(model), len(decrypted))
	}
}

func TestModelObfuscate(t *testing.T) {
	mgr := NewModelEncryptionManager(SecurityConfig{EnableModelEncryption: true})
	model := []byte{5, 6, 7, 8}
	obfuscated, err := mgr.ModelObfuscate(model)
	if err != nil {
		t.Fatalf("ModelObfuscate error: %v", err)
	}
	if len(obfuscated) != len(model) {
		t.Errorf("Expected obfuscated length %d, got %d", len(model), len(obfuscated))
	}
}

func TestEncryptModel_Empty(t *testing.T) {
	mgr := NewModelEncryptionManager(SecurityConfig{EnableModelEncryption: true})
	_, err := mgr.EncryptModel([]byte{})
	if err != nil {
		t.Errorf("EncryptModel should not error on empty input: %v", err)
	}
}
