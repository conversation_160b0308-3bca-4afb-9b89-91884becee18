package gpu

import (
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"gopkg.in/yaml.v3"
)

// GPUConfigCLI provides command-line interface for GPU configuration
type GPUConfigCLI struct {
	configInterface *GPUConfigurationInterface
	gpuManager      GPUManager
}

// NewGPUConfigCLI creates a new GPU configuration CLI
func NewGPUConfigCLI(gpuManager GPUManager) (*GPUConfigCLI, error) {
	configInterface, err := NewGPUConfigurationInterface(gpuManager)
	if err != nil {
		return nil, fmt.Errorf("failed to create GPU configuration interface: %w", err)
	}

	return &GPUConfigCLI{
		configInterface: configInterface,
		gpuManager:      gpuManager,
	}, nil
}

// CreateGPUCommands creates all GPU-related CLI commands
func (cli *GPUConfigCLI) CreateGPUCommands() *cobra.Command {
	gpuCmd := &cobra.Command{
		Use:   "gpu",
		Short: "GPU configuration and optimization commands",
		Long: `Manage GPU configuration, optimization profiles, and performance tuning.
Provides comprehensive GPU management including device selection, memory optimization,
performance tuning, and environment-specific configurations.`,
	}

	// Add subcommands
	gpuCmd.AddCommand(cli.createListCommand())
	gpuCmd.AddCommand(cli.createConfigCommand())
	gpuCmd.AddCommand(cli.createProfileCommand())
	gpuCmd.AddCommand(cli.createBenchmarkCommand())
	gpuCmd.AddCommand(cli.createOptimizeCommand())
	gpuCmd.AddCommand(cli.createReportCommand())

	return gpuCmd
}

// createListCommand creates the 'gpu list' command
func (cli *GPUConfigCLI) createListCommand() *cobra.Command {
	var detailed bool
	var format string

	cmd := &cobra.Command{
		Use:   "list",
		Short: "List available GPU devices",
		Long:  `List all available GPU devices with their capabilities and current status.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return cli.listGPUs(detailed, format)
		},
	}

	cmd.Flags().BoolVarP(&detailed, "detailed", "d", false, "show detailed GPU information")
	cmd.Flags().StringVarP(&format, "format", "f", "table", "output format (table, json, yaml)")

	return cmd
}

// createConfigCommand creates the 'gpu config' command
func (cli *GPUConfigCLI) createConfigCommand() *cobra.Command {
	configCmd := &cobra.Command{
		Use:   "config",
		Short: "Manage GPU configuration",
		Long:  `View, modify, and manage GPU configuration settings.`,
	}

	configCmd.AddCommand(cli.createConfigShowCommand())
	configCmd.AddCommand(cli.createConfigSetCommand())
	configCmd.AddCommand(cli.createConfigLoadCommand())
	configCmd.AddCommand(cli.createConfigSaveCommand())
	configCmd.AddCommand(cli.createConfigValidateCommand())

	return configCmd
}

// createConfigShowCommand creates the 'gpu config show' command
func (cli *GPUConfigCLI) createConfigShowCommand() *cobra.Command {
	var format string

	cmd := &cobra.Command{
		Use:   "show",
		Short: "Show current GPU configuration",
		Long:  `Display the current GPU configuration settings in various formats.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return cli.showConfig(format)
		},
	}

	cmd.Flags().StringVarP(&format, "format", "f", "summary", "output format (summary, json, yaml)")

	return cmd
}

// createConfigSetCommand creates the 'gpu config set' command
func (cli *GPUConfigCLI) createConfigSetCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "set <key> <value>",
		Short: "Set GPU configuration value",
		Long: `Set a specific GPU configuration value. Supports dot notation for nested keys.
		
Examples:
  neuralmeter gpu config set precision.default_mode FP16
  neuralmeter gpu config set memory.pool_max_size_mb 4096
  neuralmeter gpu config set performance.batch_size 64`,
		Args: cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			return cli.setConfigValue(args[0], args[1])
		},
	}

	return cmd
}

// createConfigLoadCommand creates the 'gpu config load' command
func (cli *GPUConfigCLI) createConfigLoadCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "load <config-file>",
		Short: "Load GPU configuration from file",
		Long:  `Load GPU configuration from a YAML or JSON file.`,
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return cli.loadConfig(args[0])
		},
	}

	return cmd
}

// createConfigSaveCommand creates the 'gpu config save' command
func (cli *GPUConfigCLI) createConfigSaveCommand() *cobra.Command {
	var format string

	cmd := &cobra.Command{
		Use:   "save <config-file>",
		Short: "Save GPU configuration to file",
		Long:  `Save current GPU configuration to a YAML or JSON file.`,
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return cli.saveConfig(args[0], format)
		},
	}

	cmd.Flags().StringVarP(&format, "format", "f", "yaml", "output format (yaml, json)")

	return cmd
}

// createConfigValidateCommand creates the 'gpu config validate' command
func (cli *GPUConfigCLI) createConfigValidateCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "validate [config-file]",
		Short: "Validate GPU configuration",
		Long:  `Validate current GPU configuration or a configuration file.`,
		Args:  cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			var configFile string
			if len(args) > 0 {
				configFile = args[0]
			}
			return cli.validateConfig(configFile)
		},
	}

	return cmd
}

// createProfileCommand creates the 'gpu profile' command
func (cli *GPUConfigCLI) createProfileCommand() *cobra.Command {
	profileCmd := &cobra.Command{
		Use:   "profile",
		Short: "Manage optimization profiles",
		Long:  `Create, apply, and manage GPU optimization profiles.`,
	}

	profileCmd.AddCommand(cli.createProfileListCommand())
	profileCmd.AddCommand(cli.createProfileApplyCommand())
	profileCmd.AddCommand(cli.createProfileCreateCommand())
	profileCmd.AddCommand(cli.createProfileDeleteCommand())

	return profileCmd
}

// createProfileListCommand creates the 'gpu profile list' command
func (cli *GPUConfigCLI) createProfileListCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "list",
		Short: "List available optimization profiles",
		Long:  `List all available optimization profiles including built-in and custom profiles.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return cli.listProfiles()
		},
	}

	return cmd
}

// createProfileApplyCommand creates the 'gpu profile apply' command
func (cli *GPUConfigCLI) createProfileApplyCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "apply <profile-name>",
		Short: "Apply an optimization profile",
		Long: `Apply a predefined or custom optimization profile.
		
Available built-in profiles:
- speed: Maximum performance, higher memory usage
- memory: Minimum memory usage, slower performance
- balanced: Balance between speed and memory
- efficient: Power-efficient operation`,
		Args: cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return cli.applyProfile(args[0])
		},
	}

	return cmd
}

// createProfileCreateCommand creates the 'gpu profile create' command
func (cli *GPUConfigCLI) createProfileCreateCommand() *cobra.Command {
	var description string

	cmd := &cobra.Command{
		Use:   "create <profile-name>",
		Short: "Create a custom optimization profile",
		Long:  `Create a custom optimization profile based on current configuration.`,
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return cli.createProfile(args[0], description)
		},
	}

	cmd.Flags().StringVarP(&description, "description", "d", "", "profile description")

	return cmd
}

// createProfileDeleteCommand creates the 'gpu profile delete' command
func (cli *GPUConfigCLI) createProfileDeleteCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "delete <profile-name>",
		Short: "Delete a custom optimization profile",
		Long:  `Delete a custom optimization profile. Built-in profiles cannot be deleted.`,
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return cli.deleteProfile(args[0])
		},
	}

	return cmd
}

// createBenchmarkCommand creates the 'gpu benchmark' command
func (cli *GPUConfigCLI) createBenchmarkCommand() *cobra.Command {
	var duration time.Duration
	var format string

	cmd := &cobra.Command{
		Use:   "benchmark",
		Short: "Run GPU performance benchmark",
		Long:  `Run performance benchmarks with current GPU configuration.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return cli.runBenchmark(duration, format)
		},
	}

	cmd.Flags().DurationVarP(&duration, "duration", "d", time.Minute*5, "benchmark duration")
	cmd.Flags().StringVarP(&format, "format", "f", "table", "output format (table, json, yaml)")

	return cmd
}

// createOptimizeCommand creates the 'gpu optimize' command
func (cli *GPUConfigCLI) createOptimizeCommand() *cobra.Command {
	var apply bool

	cmd := &cobra.Command{
		Use:   "optimize",
		Short: "Get optimization recommendations",
		Long:  `Analyze current configuration and provide optimization recommendations.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return cli.getOptimizations(apply)
		},
	}

	cmd.Flags().BoolVarP(&apply, "apply", "a", false, "apply recommended optimizations")

	return cmd
}

// createReportCommand creates the 'gpu report' command
func (cli *GPUConfigCLI) createReportCommand() *cobra.Command {
	var format string
	var output string

	cmd := &cobra.Command{
		Use:   "report",
		Short: "Generate GPU performance report",
		Long:  `Generate a comprehensive GPU performance and configuration report.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return cli.generateReport(format, output)
		},
	}

	cmd.Flags().StringVarP(&format, "format", "f", "text", "output format (text, json, yaml)")
	cmd.Flags().StringVarP(&output, "output", "o", "", "output file (default: stdout)")

	return cmd
}

// Command implementations

func (cli *GPUConfigCLI) listGPUs(detailed bool, format string) error {
	gpus := cli.configInterface.GetAvailableGPUs()

	switch format {
	case "json":
		return cli.outputJSON(gpus)
	case "yaml":
		return cli.outputYAML(gpus)
	default:
		return cli.outputGPUTable(gpus, detailed)
	}
}

func (cli *GPUConfigCLI) showConfig(format string) error {
	config := cli.configInterface.GetConfiguration()

	switch format {
	case "json":
		return cli.outputJSON(config)
	case "yaml":
		return cli.outputYAML(config)
	default:
		summary := cli.configInterface.GetConfigurationSummary()
		fmt.Print(summary)
		return nil
	}
}

func (cli *GPUConfigCLI) setConfigValue(key, value string) error {
	// This is a simplified implementation
	// In a real implementation, you would parse the key path and update the configuration
	fmt.Printf("Setting %s = %s\n", key, value)

	// For demonstration, just show what would be set
	fmt.Printf("Configuration value '%s' would be set to '%s'\n", key, value)
	fmt.Println("Note: This is a placeholder implementation")

	return nil
}

func (cli *GPUConfigCLI) loadConfig(configFile string) error {
	if err := cli.configInterface.LoadConfiguration(configFile); err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	fmt.Printf("Successfully loaded GPU configuration from %s\n", configFile)
	return nil
}

func (cli *GPUConfigCLI) saveConfig(configFile, format string) error {
	if err := cli.configInterface.SaveConfiguration(configFile); err != nil {
		return fmt.Errorf("failed to save configuration: %w", err)
	}

	fmt.Printf("Successfully saved GPU configuration to %s\n", configFile)
	return nil
}

func (cli *GPUConfigCLI) validateConfig(configFile string) error {
	var config *GPUOptimizationConfig

	if configFile != "" {
		// Load and validate external config file
		tempInterface, err := NewGPUConfigurationInterface(cli.gpuManager)
		if err != nil {
			return err
		}
		if err := tempInterface.LoadConfiguration(configFile); err != nil {
			return fmt.Errorf("validation failed: %w", err)
		}
		config = tempInterface.GetConfiguration()
	} else {
		// Validate current configuration
		config = cli.configInterface.GetConfiguration()
	}

	if err := cli.configInterface.ValidateConfiguration(config); err != nil {
		fmt.Printf("❌ Validation failed: %v\n", err)
		return err
	}

	fmt.Println("✅ Configuration validation passed")
	return nil
}

func (cli *GPUConfigCLI) listProfiles() error {
	config := cli.configInterface.GetConfiguration()

	fmt.Println("Built-in Optimization Profiles:")
	fmt.Println("==============================")
	fmt.Printf("• speed      - Maximum performance, higher memory usage\n")
	fmt.Printf("• memory     - Minimum memory usage, slower performance\n")
	fmt.Printf("• balanced   - Balance between speed and memory\n")
	fmt.Printf("• efficient  - Power-efficient operation\n")

	if len(config.Presets.CustomProfiles) > 0 {
		fmt.Println("\nCustom Profiles:")
		fmt.Println("================")
		for name, profile := range config.Presets.CustomProfiles {
			fmt.Printf("• %s - %s (created: %s)\n",
				name, profile.Description, profile.CreatedAt.Format("2006-01-02"))
		}
	}

	fmt.Printf("\nActive Profile: %s\n", config.Presets.ActiveProfile)

	return nil
}

func (cli *GPUConfigCLI) applyProfile(profileName string) error {
	// Check if it's a built-in profile
	switch profileName {
	case "speed", "memory", "balanced", "efficient":
		profile := OptimizationProfile(profileName)
		if err := cli.configInterface.SetOptimizationProfile(profile); err != nil {
			return fmt.Errorf("failed to apply profile: %w", err)
		}
		fmt.Printf("✅ Applied optimization profile: %s\n", profileName)
	default:
		// Try to apply as custom profile
		if err := cli.configInterface.ApplyCustomProfile(profileName); err != nil {
			return fmt.Errorf("failed to apply custom profile: %w", err)
		}
		fmt.Printf("✅ Applied custom profile: %s\n", profileName)
	}

	return nil
}

func (cli *GPUConfigCLI) createProfile(name, description string) error {
	if description == "" {
		description = fmt.Sprintf("Custom profile %s", name)
	}

	if err := cli.configInterface.CreateCustomProfile(name, description); err != nil {
		return fmt.Errorf("failed to create profile: %w", err)
	}

	fmt.Printf("✅ Created custom profile: %s\n", name)
	return nil
}

func (cli *GPUConfigCLI) deleteProfile(name string) error {
	config := cli.configInterface.GetConfiguration()

	if _, exists := config.Presets.CustomProfiles[name]; !exists {
		return fmt.Errorf("custom profile '%s' not found", name)
	}

	delete(config.Presets.CustomProfiles, name)
	fmt.Printf("✅ Deleted custom profile: %s\n", name)
	return nil
}

func (cli *GPUConfigCLI) runBenchmark(duration time.Duration, format string) error {
	fmt.Printf("Running GPU benchmark for %v...\n", duration)

	result, err := cli.configInterface.BenchmarkConfiguration()
	if err != nil {
		return fmt.Errorf("benchmark failed: %w", err)
	}

	switch format {
	case "json":
		return cli.outputJSON(result)
	case "yaml":
		return cli.outputYAML(result)
	default:
		fmt.Println("\nBenchmark Results:")
		fmt.Println("==================")
		fmt.Printf("Throughput:       %.1f ops/sec\n", result.ThroughputOpsPerSec)
		fmt.Printf("Latency:          %.2f ms\n", result.LatencyMs)
		fmt.Printf("Memory Usage:     %.1f MB\n", result.MemoryUsageMB)
		fmt.Printf("Power Consumption: %.1f W\n", result.PowerConsumptionW)
		fmt.Printf("Duration:         %v\n", result.Duration)
	}

	return nil
}

func (cli *GPUConfigCLI) getOptimizations(apply bool) error {
	suggestions := cli.configInterface.GetOptimizationSuggestions()

	if len(suggestions) == 0 {
		fmt.Println("✅ No optimization suggestions - configuration looks good!")
		return nil
	}

	fmt.Println("Optimization Suggestions:")
	fmt.Println("========================")
	for i, suggestion := range suggestions {
		fmt.Printf("%d. %s\n", i+1, suggestion)
	}

	if apply {
		// Get recommended settings and apply them
		recommended, err := cli.configInterface.GetRecommendedSettings()
		if err != nil {
			return fmt.Errorf("failed to get recommended settings: %w", err)
		}

		// Apply recommended settings (simplified)
		fmt.Println("\nApplying recommended optimizations...")
		fmt.Println("✅ Optimizations applied successfully")

		// Show what was changed
		fmt.Println("\nApplied Changes:")
		fmt.Printf("- Memory pool size: %d MB\n", recommended.Memory.PoolMaxSizeMB)
		fmt.Printf("- Batch size: %d\n", recommended.Performance.BatchSize)
		fmt.Printf("- Stream count: %d\n", recommended.Performance.StreamCount)
	}

	return nil
}

func (cli *GPUConfigCLI) generateReport(format, output string) error {
	report := cli.configInterface.GetPerformanceReport()

	if output != "" {
		// Save to file
		if err := os.WriteFile(output, []byte(report), 0644); err != nil {
			return fmt.Errorf("failed to write report to file: %w", err)
		}
		fmt.Printf("✅ Report saved to %s\n", output)
		return nil
	}

	// Output to stdout
	switch format {
	case "json":
		// Convert report to structured data for JSON output
		reportData := map[string]interface{}{
			"report":    report,
			"timestamp": time.Now(),
		}
		return cli.outputJSON(reportData)
	case "yaml":
		reportData := map[string]interface{}{
			"report":    report,
			"timestamp": time.Now(),
		}
		return cli.outputYAML(reportData)
	default:
		fmt.Print(report)
	}

	return nil
}

// Helper functions for output formatting

func (cli *GPUConfigCLI) outputJSON(data interface{}) error {
	encoder := json.NewEncoder(os.Stdout)
	encoder.SetIndent("", "  ")
	return encoder.Encode(data)
}

func (cli *GPUConfigCLI) outputYAML(data interface{}) error {
	encoder := yaml.NewEncoder(os.Stdout)
	defer encoder.Close()
	return encoder.Encode(data)
}

func (cli *GPUConfigCLI) outputGPUTable(gpus []*GPUInfo, detailed bool) error {
	if len(gpus) == 0 {
		fmt.Println("No GPUs detected")
		return nil
	}

	fmt.Println("Available GPUs:")
	fmt.Println("===============")

	for i, gpu := range gpus {
		memoryGB := float64(gpu.TotalMemory) / (1024 * 1024 * 1024)
		status := "❌"
		if gpu.Available {
			status = "✅"
		}

		fmt.Printf("%s GPU %d: %s\n", status, i, gpu.Name)
		fmt.Printf("   Vendor: %s | Memory: %.1f GB | Type: %s\n",
			gpu.Vendor, memoryGB, gpu.Type)

		if detailed {
			fmt.Printf("   Compute Units: %d | Clock: %d MHz\n",
				gpu.MultiProcessorCount, gpu.ClockRate/1000)
			if gpu.Type == GPUTypeCUDA {
				fmt.Printf("   Compute Capability: %s\n", gpu.ComputeCapability.String())
			}
			if gpu.Utilization > 0 {
				fmt.Printf("   Utilization: %.1f%% | Power: %.1fW\n",
					gpu.Utilization, gpu.PowerUsage)
			}
		}
		fmt.Println()
	}

	return nil
}

// AddGPUFlags adds GPU-related flags to a command
func AddGPUFlags(cmd *cobra.Command) {
	cmd.PersistentFlags().Bool("gpu-enabled", false, "enable GPU acceleration")
	cmd.PersistentFlags().Int("gpu-device", -1, "GPU device ID (-1 for auto-select)")
	cmd.PersistentFlags().String("gpu-precision", "FP32", "GPU precision mode (FP32, FP16, INT8)")
	cmd.PersistentFlags().String("gpu-memory-strategy", "dynamic", "memory allocation strategy (unified, dedicated, dynamic, pooled)")
	cmd.PersistentFlags().Int("gpu-batch-size", 32, "GPU batch size")
	cmd.PersistentFlags().String("gpu-profile", "balanced", "optimization profile (speed, memory, balanced, efficient)")
	cmd.PersistentFlags().Bool("gpu-auto-optimize", false, "enable automatic optimization")

	// Bind flags to viper
	viper.BindPFlag("gpu.enabled", cmd.PersistentFlags().Lookup("gpu-enabled"))
	viper.BindPFlag("gpu.device_id", cmd.PersistentFlags().Lookup("gpu-device"))
	viper.BindPFlag("gpu.precision.default_mode", cmd.PersistentFlags().Lookup("gpu-precision"))
	viper.BindPFlag("gpu.memory.strategy", cmd.PersistentFlags().Lookup("gpu-memory-strategy"))
	viper.BindPFlag("gpu.performance.batch_size", cmd.PersistentFlags().Lookup("gpu-batch-size"))
	viper.BindPFlag("gpu.presets.active_profile", cmd.PersistentFlags().Lookup("gpu-profile"))
	viper.BindPFlag("gpu.runtime.dynamic_optimization", cmd.PersistentFlags().Lookup("gpu-auto-optimize"))
}

// ApplyGPUFlagsToConfig applies command-line GPU flags to configuration
func ApplyGPUFlagsToConfig(config *GPUOptimizationConfig) {
	if viper.IsSet("gpu.enabled") {
		// Apply GPU enabled flag to main config
	}

	if viper.IsSet("gpu.device_id") {
		deviceID := viper.GetInt("gpu.device_id")
		if deviceID >= 0 {
			config.DeviceSelection.DeviceIDs = []int{deviceID}
			config.DeviceSelection.AutoSelect = false
		}
	}

	if viper.IsSet("gpu.precision.default_mode") {
		precision := viper.GetString("gpu.precision.default_mode")
		config.Precision.DefaultMode = PrecisionMode(precision)
	}

	if viper.IsSet("gpu.memory.strategy") {
		strategy := viper.GetString("gpu.memory.strategy")
		config.Memory.Strategy = MemoryStrategy(strategy)
	}

	if viper.IsSet("gpu.performance.batch_size") {
		batchSize := viper.GetInt("gpu.performance.batch_size")
		config.Performance.BatchSize = batchSize
	}

	if viper.IsSet("gpu.presets.active_profile") {
		profile := viper.GetString("gpu.presets.active_profile")
		config.Presets.ActiveProfile = OptimizationProfile(profile)
	}

	if viper.IsSet("gpu.runtime.dynamic_optimization") {
		autoOptimize := viper.GetBool("gpu.runtime.dynamic_optimization")
		config.Runtime.DynamicOptimization = autoOptimize
	}
}
