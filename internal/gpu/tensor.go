package gpu

import (
	"fmt"
	"unsafe"
)

/*
#include <stdlib.h>
#include <string.h>
*/
import "C"

// TODO: Ensure all unsafe.Pointer conversions go through uintptr to satisfy go vet.

// TensorDataType represents the data type of tensor elements
type TensorDataType int

const (
	TensorFloat32 TensorDataType = iota // FP32
	TensorFloat16                       // FP16
	TensorInt8                          // INT8
	TensorInt32                         // INT32
	TensorBool                          // Boolean
	TensorInt4                          // INT4 (4-bit integer)
)

// String returns string representation of tensor data type
func (dt TensorDataType) String() string {
	switch dt {
	case TensorFloat32:
		return "float32"
	case TensorFloat16:
		return "float16"
	case TensorInt8:
		return "int8"
	case TensorInt32:
		return "int32"
	case TensorBool:
		return "bool"
	case TensorInt4:
		return "int4"
	default:
		return "unknown"
	}
}

// Size returns the size in bytes for each element of this data type
func (dt TensorDataType) Size() int {
	switch dt {
	case TensorFloat32:
		return 4
	case TensorFloat16:
		return 2
	case TensorInt8:
		return 1
	case TensorInt32:
		return 4
	case TensorBool:
		return 1
	case TensorInt4:
		return 1 // INT4 stored as 1 byte (2 values per byte)
	default:
		return 0
	}
}

// TensorShape represents the dimensions of a tensor
type TensorShape []int64

// String returns string representation of tensor shape
func (s TensorShape) String() string {
	return fmt.Sprintf("%v", []int64(s))
}

// NumElements returns the total number of elements in the tensor
func (s TensorShape) NumElements() int64 {
	if len(s) == 0 {
		return 0
	}
	total := int64(1)
	for _, dim := range s {
		total *= dim
	}
	return total
}

// IsValid checks if the shape is valid (all dimensions > 0)
func (s TensorShape) IsValid() bool {
	for _, dim := range s {
		if dim <= 0 {
			return false
		}
	}
	return true
}

// Rank returns the number of dimensions
func (s TensorShape) Rank() int {
	return len(s)
}

// Clone creates a copy of the shape
func (s TensorShape) Clone() TensorShape {
	clone := make(TensorShape, len(s))
	copy(clone, s)
	return clone
}

// TensorStrides represents the strides for each dimension
type TensorStrides []int64

// ComputeStrides calculates strides for a given shape (row-major order)
func ComputeStrides(shape TensorShape) TensorStrides {
	if len(shape) == 0 {
		return TensorStrides{}
	}

	strides := make(TensorStrides, len(shape))
	stride := int64(1)

	// Calculate strides from right to left (row-major)
	for i := len(shape) - 1; i >= 0; i-- {
		strides[i] = stride
		stride *= shape[i]
	}

	return strides
}

// TensorMemoryLayout represents how tensor data is laid out in memory
type TensorMemoryLayout int

const (
	RowMajor TensorMemoryLayout = iota // C-style (default)
	ColMajor                           // Fortran-style
)

// String returns string representation of memory layout
func (layout TensorMemoryLayout) String() string {
	switch layout {
	case RowMajor:
		return "row_major"
	case ColMajor:
		return "col_major"
	default:
		return "unknown"
	}
}

// TensorDevice represents where the tensor data is stored
type TensorDevice int

const (
	DeviceCPU TensorDevice = iota
	DeviceGPU
)

// String returns string representation of tensor device
func (device TensorDevice) String() string {
	switch device {
	case DeviceCPU:
		return "cpu"
	case DeviceGPU:
		return "gpu"
	default:
		return "unknown"
	}
}

// Tensor represents a multi-dimensional array with GPU support
type Tensor struct {
	// Shape and layout
	shape   TensorShape
	strides TensorStrides
	layout  TensorMemoryLayout
	dtype   TensorDataType

	// Memory management
	data      unsafe.Pointer // Pointer to data (CPU or GPU)
	devicePtr CUDAMemoryPtr  // GPU memory pointer if on GPU
	device    TensorDevice   // Where the data is stored
	deviceID  int            // GPU device ID (if on GPU)

	// Memory ownership and lifecycle
	ownsMemory bool  // Whether this tensor owns the memory
	size       int64 // Total size in bytes
	offset     int64 // Offset from base pointer (for views)

	// Reference counting for memory management
	refCount *int32

	// Memory pool integration
	memPool *AdvancedMemoryPool
}

// NewTensor creates a new tensor with the given shape and data type
func NewTensor(shape TensorShape, dtype TensorDataType, device TensorDevice, deviceID int) (*Tensor, error) {
	if !shape.IsValid() {
		return nil, fmt.Errorf("invalid tensor shape: %v", shape)
	}

	numElements := shape.NumElements()
	if numElements == 0 {
		return nil, fmt.Errorf("tensor cannot have zero elements")
	}

	elementSize := int64(dtype.Size())
	totalSize := numElements * elementSize

	tensor := &Tensor{
		shape:      shape.Clone(),
		strides:    ComputeStrides(shape),
		layout:     RowMajor,
		dtype:      dtype,
		device:     device,
		deviceID:   deviceID,
		ownsMemory: true,
		size:       totalSize,
		offset:     0,
		refCount:   new(int32),
	}
	*tensor.refCount = 1

	// Allocate memory based on device
	var err error
	switch device {
	case DeviceCPU:
		err = tensor.allocateCPUMemory()
	case DeviceGPU:
		err = tensor.allocateGPUMemory()
	default:
		return nil, fmt.Errorf("unsupported device: %v", device)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to allocate tensor memory: %w", err)
	}

	return tensor, nil
}

// allocateCPUMemory allocates memory on the CPU
func (t *Tensor) allocateCPUMemory() error {
	// Allocate aligned memory for better performance
	t.data = C.malloc(C.size_t(t.size))
	if t.data == nil {
		return fmt.Errorf("failed to allocate CPU memory of size %d bytes", t.size)
	}
	return nil
}

// allocateGPUMemory allocates memory on the GPU using the memory pool
func (t *Tensor) allocateGPUMemory() error {
	if t.memPool == nil {
		return fmt.Errorf("no memory pool available for GPU allocation")
	}

	ptr, err := t.memPool.Allocate(t.size)
	if err != nil {
		return fmt.Errorf("failed to allocate GPU memory: %w", err)
	}

	t.devicePtr = ptr
	t.data = unsafe.Pointer(uintptr(ptr))
	return nil
}

// SetMemoryPool sets the memory pool for GPU allocations
func (t *Tensor) SetMemoryPool(pool *AdvancedMemoryPool) {
	t.memPool = pool
}

// Shape returns the tensor shape
func (t *Tensor) Shape() TensorShape {
	return t.shape.Clone()
}

// Strides returns the tensor strides
func (t *Tensor) Strides() TensorStrides {
	strides := make(TensorStrides, len(t.strides))
	copy(strides, t.strides)
	return strides
}

// DataType returns the tensor data type
func (t *Tensor) DataType() TensorDataType {
	return t.dtype
}

// Device returns the device where tensor is stored
func (t *Tensor) Device() TensorDevice {
	return t.device
}

// DeviceID returns the GPU device ID (if on GPU)
func (t *Tensor) DeviceID() int {
	return t.deviceID
}

// Size returns the total size in bytes
func (t *Tensor) Size() int64 {
	return t.size
}

// NumElements returns the total number of elements
func (t *Tensor) NumElements() int64 {
	return t.shape.NumElements()
}

// Rank returns the number of dimensions
func (t *Tensor) Rank() int {
	return t.shape.Rank()
}

// Data returns the raw data pointer (use with caution)
func (t *Tensor) Data() unsafe.Pointer {
	if t.offset == 0 {
		return t.data
	}
	// Safe usage: computing offset pointer for tensor views/slices
	// This is a valid pattern for offset calculations in memory management
	return unsafe.Add(t.data, uintptr(t.offset))
}

// GPUPtr returns the GPU memory pointer (if on GPU)
func (t *Tensor) GPUPtr() CUDAMemoryPtr {
	if t.device != DeviceGPU {
		return nil
	}
	return CUDAMemoryPtr(unsafe.Add(unsafe.Pointer(t.devicePtr), uintptr(t.offset)))
}

// IsContiguous checks if the tensor is contiguous in memory
func (t *Tensor) IsContiguous() bool {
	expectedStrides := ComputeStrides(t.shape)
	if len(t.strides) != len(expectedStrides) {
		return false
	}

	for i, stride := range t.strides {
		if stride != expectedStrides[i] {
			return false
		}
	}
	return true
}

// linearIndex calculates the linear index for given multi-dimensional indices
func (t *Tensor) linearIndex(indices ...int64) (int64, error) {
	if len(indices) != len(t.shape) {
		return 0, fmt.Errorf("number of indices (%d) doesn't match tensor rank (%d)",
			len(indices), len(t.shape))
	}

	var index int64
	for i, idx := range indices {
		if idx < 0 || idx >= t.shape[i] {
			return 0, fmt.Errorf("index %d out of bounds for dimension %d (size %d)",
				idx, i, t.shape[i])
		}
		index += idx * t.strides[i]
	}

	return index, nil
}

// Clone creates a deep copy of the tensor
func (t *Tensor) Clone() (*Tensor, error) {
	newTensor, err := NewTensor(t.shape, t.dtype, t.device, t.deviceID)
	if err != nil {
		return nil, err
	}

	newTensor.layout = t.layout
	newTensor.memPool = t.memPool

	// Copy data
	if t.device == DeviceCPU {
		C.memcpy(newTensor.data, t.Data(), C.size_t(t.size))
	} else {
		// For GPU, we would use CUDA memory copy
		// This will be implemented when we add CUDA operations
		return nil, fmt.Errorf("GPU tensor cloning not yet implemented")
	}

	return newTensor, nil
}

// View creates a view of the tensor with different shape (no data copy)
func (t *Tensor) View(newShape TensorShape) (*Tensor, error) {
	if !newShape.IsValid() {
		return nil, fmt.Errorf("invalid new shape: %v", newShape)
	}

	if newShape.NumElements() != t.shape.NumElements() {
		return nil, fmt.Errorf("new shape %v has different number of elements than original %v",
			newShape, t.shape)
	}

	if !t.IsContiguous() {
		return nil, fmt.Errorf("cannot view non-contiguous tensor")
	}

	view := &Tensor{
		shape:      newShape.Clone(),
		strides:    ComputeStrides(newShape),
		layout:     t.layout,
		dtype:      t.dtype,
		data:       t.data,
		devicePtr:  t.devicePtr,
		device:     t.device,
		deviceID:   t.deviceID,
		ownsMemory: false, // View doesn't own memory
		size:       t.size,
		offset:     t.offset,
		refCount:   t.refCount,
		memPool:    t.memPool,
	}

	// Increment reference count
	*t.refCount++

	return view, nil
}

// Slice creates a slice of the tensor along specified dimensions
func (t *Tensor) Slice(starts, ends []int64) (*Tensor, error) {
	if len(starts) != len(t.shape) || len(ends) != len(t.shape) {
		return nil, fmt.Errorf("starts and ends must have same length as tensor rank")
	}

	// Validate slice indices
	newShape := make(TensorShape, len(t.shape))
	var offset int64

	for i := range t.shape {
		start, end := starts[i], ends[i]

		// Handle negative indices
		if start < 0 {
			start = t.shape[i] + start
		}
		if end < 0 {
			end = t.shape[i] + end
		}

		// Validate bounds
		if start < 0 || start >= t.shape[i] || end < start || end > t.shape[i] {
			return nil, fmt.Errorf("invalid slice range [%d:%d] for dimension %d (size %d)",
				starts[i], ends[i], i, t.shape[i])
		}

		newShape[i] = end - start
		offset += start * t.strides[i]
	}

	slice := &Tensor{
		shape:      newShape,
		strides:    t.strides, // Keep original strides
		layout:     t.layout,
		dtype:      t.dtype,
		data:       t.data,
		devicePtr:  t.devicePtr,
		device:     t.device,
		deviceID:   t.deviceID,
		ownsMemory: false, // Slice doesn't own memory
		size:       newShape.NumElements() * int64(t.dtype.Size()),
		offset:     t.offset + offset*int64(t.dtype.Size()),
		refCount:   t.refCount,
		memPool:    t.memPool,
	}

	// Increment reference count
	*t.refCount++

	return slice, nil
}

// Free releases the tensor memory
func (t *Tensor) Free() error {
	if !t.ownsMemory {
		// For views/slices, just decrement reference count
		*t.refCount--
		if *t.refCount <= 0 {
			return t.freeMemory()
		}
		return nil
	}

	return t.freeMemory()
}

// freeMemory actually frees the memory
func (t *Tensor) freeMemory() error {
	if t.data == nil {
		return nil
	}

	switch t.device {
	case DeviceCPU:
		C.free(t.data)
		t.data = nil
	case DeviceGPU:
		if t.memPool != nil && t.devicePtr != nil {
			err := t.memPool.Free(t.devicePtr)
			if err != nil {
				return fmt.Errorf("failed to free GPU memory: %w", err)
			}
		}
		t.devicePtr = nil
		t.data = nil
	}

	return nil
}

// String returns a string representation of the tensor
func (t *Tensor) String() string {
	return fmt.Sprintf("Tensor(shape=%v, dtype=%s, device=%s, size=%d bytes)",
		t.shape, t.dtype, t.device, t.size)
}

// TensorOperations interface defines basic tensor operations
type TensorOperations interface {
	// Element access
	GetFloat32(indices ...int64) (float32, error)
	SetFloat32(value float32, indices ...int64) error
	GetInt8(indices ...int64) (int8, error)
	SetInt8(value int8, indices ...int64) error

	// Data conversion
	ToFloat32() (*Tensor, error)
	ToInt8() (*Tensor, error)
	ToDevice(device TensorDevice, deviceID int) (*Tensor, error)

	// Basic operations (will be implemented in tensor_ops.go)
	Add(other *Tensor) (*Tensor, error)
	Subtract(other *Tensor) (*Tensor, error)
	Multiply(other *Tensor) (*Tensor, error)
	Divide(other *Tensor) (*Tensor, error)

	// Matrix operations
	MatMul(other *Tensor) (*Tensor, error)
	Transpose() (*Tensor, error)

	// Utility
	Fill(value interface{}) error
	Zero() error
	Ones() error
}

// Note: TensorOperations interface methods will be implemented in tensor_ops.go
// var _ TensorOperations = (*Tensor)(nil)
