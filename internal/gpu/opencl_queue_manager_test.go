//go:build opencl
// +build opencl

package gpu

import (
	"log"
	"os"
	"sync"
	"testing"
	"time"
)

func TestManagedOpenCLQueue_Creation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	queue, err := NewManagedOpenCLQueue("test-queue", 0, "test-context", QueuePriorityNormal, QueuePropertyProfiling, logger)
	if err != nil {
		t.Fatalf("Failed to create managed OpenCL queue: %v", err)
	}
	defer queue.Destroy()

	if queue.GetID() != "test-queue" {
		t.<PERSON>("Expected ID 'test-queue', got %s", queue.GetID())
	}

	if queue.GetDeviceID() != 0 {
		t.<PERSON><PERSON>rf("Expected device ID 0, got %d", queue.GetDeviceID())
	}

	if queue.GetContextID() != "test-context" {
		t.<PERSON>("Expected context ID 'test-context', got %s", queue.GetContextID())
	}

	if queue.GetPriority() != QueuePriorityNormal {
		t.<PERSON>rf("Expected priority Normal, got %v", queue.GetPriority())
	}

	if queue.GetProperties() != QueuePropertyProfiling {
		t.Errorf("Expected properties %d, got %d", QueuePropertyProfiling, queue.GetProperties())
	}

	if queue.GetState() != QueueStateIdle {
		t.Errorf("Expected state Idle, got %v", queue.GetState())
	}

	if queue.IsInUse() {
		t.Error("Expected queue to not be in use initially")
	}
}

func TestManagedOpenCLQueue_Usage(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	queue, err := NewManagedOpenCLQueue("test-queue", 0, "test-context", QueuePriorityHigh, QueuePropertyProfiling, logger)
	if err != nil {
		t.Fatalf("Failed to create managed OpenCL queue: %v", err)
	}
	defer queue.Destroy()

	// Test setting in use
	queue.SetInUse(true)
	if !queue.IsInUse() {
		t.Error("Expected queue to be in use after SetInUse(true)")
	}
	if queue.GetState() != QueueStateBusy {
		t.Errorf("Expected state Busy after SetInUse(true), got %v", queue.GetState())
	}

	// Test usage stats
	usageCount, totalTime, errorCount := queue.GetUsageStats()
	if usageCount != 1 {
		t.Errorf("Expected usage count 1, got %d", usageCount)
	}
	if totalTime != 0 {
		t.Errorf("Expected total time 0, got %v", totalTime)
	}
	if errorCount != 0 {
		t.Errorf("Expected error count 0, got %d", errorCount)
	}

	// Test releasing
	queue.SetInUse(false)
	if queue.IsInUse() {
		t.Error("Expected queue to not be in use after SetInUse(false)")
	}
	if queue.GetState() != QueueStateIdle {
		t.Errorf("Expected state Idle after SetInUse(false), got %v", queue.GetState())
	}

	// Test error handling
	queue.IncrementErrorCount()
	_, _, errorCount = queue.GetUsageStats()
	if errorCount != 1 {
		t.Errorf("Expected error count 1 after increment, got %d", errorCount)
	}
	if queue.GetState() != QueueStateError {
		t.Errorf("Expected state Error after IncrementErrorCount, got %v", queue.GetState())
	}

	// Test time tracking
	testDuration := 100 * time.Millisecond
	queue.UpdateTotalTime(testDuration)
	_, totalTime, _ = queue.GetUsageStats()
	if totalTime != testDuration {
		t.Errorf("Expected total time %v, got %v", testDuration, totalTime)
	}
}

func TestQueuePool_Creation(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	minQueues, maxQueues := 2, 8

	pool := NewQueuePool(0, "test-context", minQueues, maxQueues, logger)
	defer pool.Cleanup()

	stats := pool.GetPoolStats()
	if stats["device_id"] != 0 {
		t.Errorf("Expected device ID 0, got %v", stats["device_id"])
	}
	if stats["context_id"] != "test-context" {
		t.Errorf("Expected context ID 'test-context', got %v", stats["context_id"])
	}
	if stats["min_queues"] != minQueues {
		t.Errorf("Expected min queues %d, got %v", minQueues, stats["min_queues"])
	}
	if stats["max_queues"] != maxQueues {
		t.Errorf("Expected max queues %d, got %v", maxQueues, stats["max_queues"])
	}
	if stats["available"].(int) != minQueues {
		t.Errorf("Expected %d available queues initially, got %v", minQueues, stats["available"])
	}
}

func TestQueuePool_AcquireRelease(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	minQueues, maxQueues := 2, 4

	pool := NewQueuePool(0, "test-context", minQueues, maxQueues, logger)
	defer pool.Cleanup()

	// Test acquiring queues
	queue1, err := pool.AcquireQueue(QueuePriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire first queue: %v", err)
	}
	if !queue1.IsInUse() {
		t.Error("Expected acquired queue to be in use")
	}

	queue2, err := pool.AcquireQueue(QueuePriorityHigh)
	if err != nil {
		t.Fatalf("Failed to acquire second queue: %v", err)
	}

	// Pool should have no available queues now (started with 2, both acquired)
	stats := pool.GetPoolStats()
	if stats["available"].(int) != 0 {
		t.Errorf("Expected 0 available queues after acquiring 2, got %v", stats["available"])
	}

	// Should create a new queue since we're under max
	queue3, err := pool.AcquireQueue(QueuePriorityLow)
	if err != nil {
		t.Fatalf("Failed to acquire third queue: %v", err)
	}

	// Test releasing queues
	err = pool.ReleaseQueue(queue1)
	if err != nil {
		t.Fatalf("Failed to release first queue: %v", err)
	}
	if queue1.IsInUse() {
		t.Error("Expected released queue to not be in use")
	}

	err = pool.ReleaseQueue(queue2)
	if err != nil {
		t.Fatalf("Failed to release second queue: %v", err)
	}

	err = pool.ReleaseQueue(queue3)
	if err != nil {
		t.Fatalf("Failed to release third queue: %v", err)
	}

	// Should have queues available again
	stats = pool.GetPoolStats()
	if stats["available"].(int) < minQueues {
		t.Errorf("Expected at least %d available queues after releasing, got %v", minQueues, stats["available"])
	}
}

func TestQueuePool_PriorityHandling(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	minQueues, maxQueues := 1, 3

	pool := NewQueuePool(0, "test-context", minQueues, maxQueues, logger)
	defer pool.Cleanup()

	// Acquire the initial queue
	normalQueue, err := pool.AcquireQueue(QueuePriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire normal priority queue: %v", err)
	}

	// Pool should create a high priority queue
	highQueue, err := pool.AcquireQueue(QueuePriorityHigh)
	if err != nil {
		t.Fatalf("Failed to acquire high priority queue: %v", err)
	}

	if highQueue.GetPriority() != QueuePriorityHigh {
		t.Errorf("Expected high priority queue, got %v", highQueue.GetPriority())
	}

	// Test properties for high priority queue (should have out-of-order execution)
	expectedProperties := QueuePropertyProfiling | QueuePropertyOutOfOrderExec
	if highQueue.GetProperties() != expectedProperties {
		t.Errorf("Expected properties %d for high priority queue, got %d", expectedProperties, highQueue.GetProperties())
	}

	// Release queues
	pool.ReleaseQueue(normalQueue)
	pool.ReleaseQueue(highQueue)

	// Verify queues are available by priority
	stats := pool.GetPoolStats()
	priorities := stats["priorities"].(map[QueuePriority]int)
	if priorities[QueuePriorityNormal] == 0 {
		t.Error("Expected normal priority queue to be available")
	}
	if priorities[QueuePriorityHigh] == 0 {
		t.Error("Expected high priority queue to be available")
	}
}

func TestQueuePool_MaxCapacity(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	minQueues, maxQueues := 1, 2

	pool := NewQueuePool(0, "test-context", minQueues, maxQueues, logger)
	defer pool.Cleanup()

	// Acquire all queues to capacity
	queue1, err := pool.AcquireQueue(QueuePriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire first queue: %v", err)
	}

	queue2, err := pool.AcquireQueue(QueuePriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire second queue: %v", err)
	}

	// Pool is at capacity, next acquisition should use LRU strategy
	queue3, err := pool.AcquireQueue(QueuePriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire third queue (should reuse LRU): %v", err)
	}

	// Should be able to release all queues
	pool.ReleaseQueue(queue1)
	pool.ReleaseQueue(queue2)
	pool.ReleaseQueue(queue3)

	stats := pool.GetPoolStats()
	if stats["total_queues"].(int) > maxQueues {
		t.Errorf("Pool exceeded max capacity: %v > %d", stats["total_queues"], maxQueues)
	}
}

func TestOpenCLQueueManager_Initialization(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	manager := NewOpenCLQueueManager(logger)
	defer manager.Cleanup()

	devices := []int{0, 1}
	contexts := map[int]string{0: "context-0", 1: "context-1"}
	minQueues, maxQueues := 2, 8

	err := manager.Initialize(devices, contexts, minQueues, maxQueues)
	if err != nil {
		t.Fatalf("Failed to initialize queue manager: %v", err)
	}

	stats := manager.GetManagerStats()
	if stats["total_pools"] != len(devices) {
		t.Errorf("Expected %d pools, got %v", len(devices), stats["total_pools"])
	}
	if stats["default_device"] != devices[0] {
		t.Errorf("Expected default device %d, got %v", devices[0], stats["default_device"])
	}
	if !stats["started"].(bool) {
		t.Error("Expected manager to be started")
	}

	// Test double initialization
	err = manager.Initialize(devices, contexts, minQueues, maxQueues)
	if err == nil {
		t.Error("Expected error on double initialization")
	}
}

func TestOpenCLQueueManager_AcquireRelease(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	manager := NewOpenCLQueueManager(logger)
	defer manager.Cleanup()

	devices := []int{0}
	contexts := map[int]string{0: "context-0"}
	err := manager.Initialize(devices, contexts, 2, 4)
	if err != nil {
		t.Fatalf("Failed to initialize queue manager: %v", err)
	}

	// Test acquiring queue for specific device and context
	queue1, err := manager.AcquireQueue(0, "context-0", QueuePriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire queue for device 0: %v", err)
	}
	if queue1.GetDeviceID() != 0 {
		t.Errorf("Expected device ID 0, got %d", queue1.GetDeviceID())
	}
	if queue1.GetContextID() != "context-0" {
		t.Errorf("Expected context ID 'context-0', got %s", queue1.GetContextID())
	}

	// Test acquiring default queue
	queue2, err := manager.AcquireDefaultQueue(QueuePriorityHigh)
	if err != nil {
		t.Fatalf("Failed to acquire default queue: %v", err)
	}

	// Test acquiring queue for non-existent device/context
	_, err = manager.AcquireQueue(99, "nonexistent", QueuePriorityNormal)
	if err == nil {
		t.Error("Expected error when acquiring queue for non-existent device/context")
	}

	// Test releasing queues
	err = manager.ReleaseQueue(queue1)
	if err != nil {
		t.Fatalf("Failed to release queue1: %v", err)
	}

	err = manager.ReleaseQueue(queue2)
	if err != nil {
		t.Fatalf("Failed to release queue2: %v", err)
	}

	// Test releasing nil queue
	err = manager.ReleaseQueue(nil)
	if err == nil {
		t.Error("Expected error when releasing nil queue")
	}
}

func TestOpenCLQueueManager_ConcurrentAccess(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	manager := NewOpenCLQueueManager(logger)
	defer manager.Cleanup()

	devices := []int{0}
	contexts := map[int]string{0: "context-0"}
	err := manager.Initialize(devices, contexts, 2, 10)
	if err != nil {
		t.Fatalf("Failed to initialize queue manager: %v", err)
	}

	const numGoroutines = 20
	const operationsPerGoroutine = 10

	var wg sync.WaitGroup
	errors := make(chan error, numGoroutines*operationsPerGoroutine)

	// Launch multiple goroutines to stress test concurrent access
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < operationsPerGoroutine; j++ {
				// Acquire queue
				queue, err := manager.AcquireDefaultQueue(QueuePriorityNormal)
				if err != nil {
					errors <- err
					return
				}

				// Simulate some work
				time.Sleep(time.Millisecond)

				// Release queue
				err = manager.ReleaseQueue(queue)
				if err != nil {
					errors <- err
					return
				}
			}
		}(i)
	}

	wg.Wait()
	close(errors)

	// Check for errors
	for err := range errors {
		t.Errorf("Concurrent access error: %v", err)
	}

	// Verify final state
	stats := manager.GetManagerStats()
	pools := stats["pools"].(map[string]interface{})

	if len(pools) != 1 {
		t.Errorf("Expected 1 pool, got %d", len(pools))
	}

	// Check that we didn't create too many queues
	for poolName, poolStats := range pools {
		poolStatsMap := poolStats.(map[string]interface{})
		if poolStatsMap["total_queues"].(int) > 10 {
			t.Errorf("Too many queues created in pool %s during concurrent test: %v", poolName, poolStatsMap["total_queues"])
		}
	}
}

func TestQueuePool_LRUEviction(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)
	minQueues, maxQueues := 1, 2

	pool := NewQueuePool(0, "test-context", minQueues, maxQueues, logger)
	defer pool.Cleanup()

	// Acquire and release queues to establish usage order
	queue1, _ := pool.AcquireQueue(QueuePriorityNormal)
	time.Sleep(10 * time.Millisecond) // Ensure different timestamps

	queue2, _ := pool.AcquireQueue(QueuePriorityNormal)
	time.Sleep(10 * time.Millisecond)

	// Release in reverse order to test LRU
	pool.ReleaseQueue(queue2) // More recently used
	pool.ReleaseQueue(queue1) // Less recently used

	// At capacity, should get the least recently used queue
	queue3, err := pool.AcquireQueue(QueuePriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire queue for LRU test: %v", err)
	}

	// The returned queue should be marked as in use
	if !queue3.IsInUse() {
		t.Error("Expected LRU queue to be marked as in use")
	}

	pool.ReleaseQueue(queue3)
}

func TestQueuePool_ErrorHandling(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	pool := NewQueuePool(0, "test-context", 1, 2, logger)
	defer pool.Cleanup()

	// Test releasing nil queue
	err := pool.ReleaseQueue(nil)
	if err == nil {
		t.Error("Expected error when releasing nil queue")
	}

	// Test normal operation after error
	queue, err := pool.AcquireQueue(QueuePriorityNormal)
	if err != nil {
		t.Fatalf("Failed to acquire queue after error test: %v", err)
	}

	err = pool.ReleaseQueue(queue)
	if err != nil {
		t.Fatalf("Failed to release queue: %v", err)
	}
}

func TestManagedOpenCLQueue_ThreadSafety(t *testing.T) {
	logger := log.New(os.Stdout, "[TEST] ", log.LstdFlags)

	queue, err := NewManagedOpenCLQueue("thread-test", 0, "test-context", QueuePriorityNormal, QueuePropertyProfiling, logger)
	if err != nil {
		t.Fatalf("Failed to create managed OpenCL queue: %v", err)
	}
	defer queue.Destroy()

	const numGoroutines = 10
	const operationsPerGoroutine = 100

	var wg sync.WaitGroup

	// Test concurrent access to queue metadata
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < operationsPerGoroutine; j++ {
				// These operations should be thread-safe
				_ = queue.GetID()
				_ = queue.GetDeviceID()
				_ = queue.GetContextID()
				_ = queue.GetPriority()
				_ = queue.GetProperties()
				_ = queue.GetState()
				_, _, _ = queue.GetUsageStats()

				// Simulate some work
				queue.UpdateTotalTime(time.Microsecond)

				if j%10 == 0 {
					queue.IncrementErrorCount()
				}
			}
		}()
	}

	wg.Wait()

	// Verify final state is consistent
	usageCount, totalTime, errorCount := queue.GetUsageStats()

	if totalTime == 0 {
		t.Error("Expected non-zero total time after concurrent updates")
	}

	expectedErrors := int64(numGoroutines * operationsPerGoroutine / 10)
	if errorCount != expectedErrors {
		t.Errorf("Expected %d errors, got %d", expectedErrors, errorCount)
	}

	// usageCount should still be 0 since we never called SetInUse(true)
	if usageCount != 0 {
		t.Errorf("Expected usage count 0, got %d", usageCount)
	}
}

func TestQueuePriority_Constants(t *testing.T) {
	// Test priority ordering
	if QueuePriorityLow >= QueuePriorityNormal {
		t.Error("QueuePriorityLow should be less than QueuePriorityNormal")
	}
	if QueuePriorityNormal >= QueuePriorityHigh {
		t.Error("QueuePriorityNormal should be less than QueuePriorityHigh")
	}
}

func TestMockOpenCLCommandQueue(t *testing.T) {
	mock := &MockOpenCLCommandQueue{}

	// Test initial state
	if mock.created {
		t.Error("Mock should not be created initially")
	}
	if mock.released {
		t.Error("Mock should not be released initially")
	}

	// Test creation
	err := mock.Create(0, 1, QueuePropertyProfiling)
	if err != nil {
		t.Fatalf("Failed to create mock queue: %v", err)
	}
	if !mock.created {
		t.Error("Mock should be created after Create()")
	}
	if mock.GetHandle() == 0 {
		t.Error("Mock should have non-zero handle after creation")
	}

	// Test operations on created queue
	err = mock.Finish()
	if err != nil {
		t.Fatalf("Failed to finish mock queue: %v", err)
	}

	err = mock.Flush()
	if err != nil {
		t.Fatalf("Failed to flush mock queue: %v", err)
	}

	info, err := mock.GetInfo(0)
	if err != nil {
		t.Fatalf("Failed to get info from mock queue: %v", err)
	}
	if info != "mock_info" {
		t.Errorf("Expected 'mock_info', got %v", info)
	}

	// Test release
	err = mock.Release()
	if err != nil {
		t.Fatalf("Failed to release mock queue: %v", err)
	}
	if !mock.released {
		t.Error("Mock should be released after Release()")
	}
	if mock.created {
		t.Error("Mock should not be created after Release()")
	}

	// Test operations on released queue
	err = mock.Finish()
	if err == nil {
		t.Error("Expected error when calling Finish() on released queue")
	}

	err = mock.Flush()
	if err == nil {
		t.Error("Expected error when calling Flush() on released queue")
	}

	_, err = mock.GetInfo(0)
	if err == nil {
		t.Error("Expected error when calling GetInfo() on released queue")
	}
}
