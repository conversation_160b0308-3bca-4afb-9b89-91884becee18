package gpu

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
)

// DefaultModelValidator implements the ModelValidator interface
type DefaultModelValidator struct {
	logger *log.Logger
}

// NewModelValidator creates a new model validator
func NewModelValidator(logger *log.Logger) ModelValidator {
	return &DefaultModelValidator{
		logger: logger,
	}
}

// ValidateFormat validates the model file format
func (v *DefaultModelValidator) ValidateFormat(filePath string) (ModelFormat, error) {
	ext := strings.ToLower(filepath.Ext(filePath))

	switch ext {
	case ".onnx":
		return ModelFormatONNX, nil
	case ".pb":
		return ModelFormatTensorFlow, nil
	case ".pt", ".pth":
		return ModelFormatPyTorch, nil
	case ".mlmodel":
		return ModelFormatCoreML, nil
	case ".trt", ".engine":
		return ModelFormatTensorRT, nil
	default:
		// Try to detect format by content
		return v.detectFormatByContent(filePath)
	}
}

// detectFormatByContent attempts to detect format by examining file content
func (v *DefaultModelValidator) detectFormatByContent(filePath string) (ModelFormat, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	// Read first 1024 bytes to check magic numbers/headers
	header := make([]byte, 1024)
	n, err := file.Read(header)
	if err != nil && err != io.EOF {
		return "", fmt.Errorf("failed to read file header: %w", err)
	}

	header = header[:n]

	// Check for ONNX magic bytes (protobuf format)
	if len(header) >= 4 && header[0] == 0x08 && header[1] == 0x01 {
		return ModelFormatONNX, nil
	}

	// Check for TensorFlow SavedModel (protobuf format)
	if strings.Contains(string(header), "tensorflow") {
		return ModelFormatTensorFlow, nil
	}

	// Check for PyTorch (Python pickle format)
	if len(header) >= 2 && header[0] == 0x80 && header[1] == 0x02 {
		return ModelFormatPyTorch, nil
	}

	return "", fmt.Errorf("unknown model format")
}

// ValidateChecksum validates the model file checksum
func (v *DefaultModelValidator) ValidateChecksum(filePath string, expectedChecksum string) error {
	actualChecksum, err := v.calculateFileChecksum(filePath)
	if err != nil {
		return fmt.Errorf("failed to calculate checksum: %w", err)
	}

	if actualChecksum != expectedChecksum {
		return fmt.Errorf("checksum mismatch: expected %s, got %s", expectedChecksum, actualChecksum)
	}

	return nil
}

// ValidateCompatibility validates if the model is compatible with the GPU device
func (v *DefaultModelValidator) ValidateCompatibility(filePath string, deviceInfo *GPUInfo) error {
	format, err := v.ValidateFormat(filePath)
	if err != nil {
		return fmt.Errorf("format validation failed: %w", err)
	}

	// Check device compatibility based on format
	switch format {
	case ModelFormatONNX:
		// ONNX models require CUDA 10.0+ for GPU acceleration
		if deviceInfo.Type == GPUTypeCUDA {
			if deviceInfo.ComputeCapability.Major < 3 {
				return fmt.Errorf("CUDA compute capability %s is too low for ONNX (minimum 3.0)",
					deviceInfo.ComputeCapability.String())
			}
		}

	case ModelFormatTensorFlow:
		// TensorFlow GPU requires CUDA 10.1+ and specific compute capabilities
		if deviceInfo.Type == GPUTypeCUDA {
			if deviceInfo.ComputeCapability.Major < 3 ||
				(deviceInfo.ComputeCapability.Major == 3 && deviceInfo.ComputeCapability.Minor < 5) {
				return fmt.Errorf("CUDA compute capability %s is too low for TensorFlow (minimum 3.5)",
					deviceInfo.ComputeCapability.String())
			}
		}

	case ModelFormatPyTorch:
		// PyTorch requires CUDA 10.2+ and compute capability 3.5+
		if deviceInfo.Type == GPUTypeCUDA {
			if deviceInfo.ComputeCapability.Major < 3 ||
				(deviceInfo.ComputeCapability.Major == 3 && deviceInfo.ComputeCapability.Minor < 5) {
				return fmt.Errorf("CUDA compute capability %s is too low for PyTorch (minimum 3.5)",
					deviceInfo.ComputeCapability.String())
			}
		}

	case ModelFormatTensorRT:
		// TensorRT requires specific NVIDIA GPUs
		if deviceInfo.Type != GPUTypeCUDA {
			return fmt.Errorf("TensorRT models require NVIDIA CUDA GPUs")
		}
		if deviceInfo.ComputeCapability.Major < 5 {
			return fmt.Errorf("TensorRT requires compute capability 5.0+ (got %s)",
				deviceInfo.ComputeCapability.String())
		}

	case ModelFormatCoreML:
		// CoreML is primarily for Apple devices, limited GPU support
		v.logger.Printf("Warning: CoreML models have limited GPU acceleration support")
	}

	// Check memory requirements (basic estimation)
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return fmt.Errorf("failed to get file info: %w", err)
	}

	// Estimate memory requirement (model size * 3 as rough estimate)
	estimatedMemory := fileInfo.Size() * 3
	if estimatedMemory > deviceInfo.FreeMemory {
		return fmt.Errorf("insufficient GPU memory: need ~%d bytes, available %d bytes",
			estimatedMemory, deviceInfo.FreeMemory)
	}

	return nil
}

// GetModelInfo extracts basic information about the model file
func (v *DefaultModelValidator) GetModelInfo(filePath string) (*ModelInfo, error) {
	// Get file information
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	// Validate format
	format, err := v.ValidateFormat(filePath)
	if err != nil {
		return nil, fmt.Errorf("format validation failed: %w", err)
	}

	// Calculate checksum
	checksum, err := v.calculateFileChecksum(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate checksum: %w", err)
	}

	info := &ModelInfo{
		Format:   format,
		Size:     fileInfo.Size(),
		Checksum: checksum,
		Metadata: make(map[string]interface{}),
	}

	// Add file metadata
	info.Metadata["file_name"] = filepath.Base(filePath)
	info.Metadata["file_path"] = filePath
	info.Metadata["modified_time"] = fileInfo.ModTime()

	// Try to extract more detailed information based on format
	switch format {
	case ModelFormatONNX:
		if err := v.extractONNXInfo(filePath, info); err != nil {
			v.logger.Printf("Warning: failed to extract ONNX info: %v", err)
		}
	case ModelFormatTensorFlow:
		if err := v.extractTensorFlowInfo(filePath, info); err != nil {
			v.logger.Printf("Warning: failed to extract TensorFlow info: %v", err)
		}
	case ModelFormatPyTorch:
		if err := v.extractPyTorchInfo(filePath, info); err != nil {
			v.logger.Printf("Warning: failed to extract PyTorch info: %v", err)
		}
	}

	return info, nil
}

// calculateFileChecksum calculates SHA256 checksum of a file
func (v *DefaultModelValidator) calculateFileChecksum(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hasher := sha256.New()
	if _, err := io.Copy(hasher, file); err != nil {
		return "", err
	}

	return hex.EncodeToString(hasher.Sum(nil)), nil
}

// extractONNXInfo extracts information from ONNX model files
func (v *DefaultModelValidator) extractONNXInfo(filePath string, info *ModelInfo) error {
	// This would require ONNX protobuf parsing
	// For now, we'll provide basic information
	info.Metadata["format_version"] = "ONNX"
	info.Metadata["parser"] = "basic"
	return nil
}

// extractTensorFlowInfo extracts information from TensorFlow model files
func (v *DefaultModelValidator) extractTensorFlowInfo(filePath string, info *ModelInfo) error {
	// This would require TensorFlow protobuf parsing
	info.Metadata["format_version"] = "TensorFlow"
	info.Metadata["parser"] = "basic"
	return nil
}

// extractPyTorchInfo extracts information from PyTorch model files
func (v *DefaultModelValidator) extractPyTorchInfo(filePath string, info *ModelInfo) error {
	// This would require PyTorch pickle parsing
	info.Metadata["format_version"] = "PyTorch"
	info.Metadata["parser"] = "basic"
	return nil
}
