// Package gpu provides GPU configuration and optimization interface
package gpu

import (
	"encoding/json"
	"fmt"
	"path/filepath"
	"strings"
	"time"
)

// PrecisionMode represents the precision modes for GPU operations
type PrecisionMode string

const (
	PrecisionFP32 PrecisionMode = "FP32" // 32-bit floating point
	PrecisionFP16 PrecisionMode = "FP16" // 16-bit floating point
	PrecisionINT8 PrecisionMode = "INT8" // 8-bit integer
	PrecisionINT4 PrecisionMode = "INT4" // 4-bit integer (experimental)
)

// MemoryStrategy represents memory allocation strategies
type MemoryStrategy string

const (
	MemoryUnified   MemoryStrategy = "unified"   // Unified memory management
	MemoryDedicated MemoryStrategy = "dedicated" // Dedicated GPU memory
	MemoryDynamic   MemoryStrategy = "dynamic"   // Dynamic allocation based on need
	MemoryPooled    MemoryStrategy = "pooled"    // Memory pooling for efficiency
)

// TensorLayout represents tensor memory layout optimization
type TensorLayout string

const (
	TensorLayoutNCHW TensorLayout = "NCHW" // Batch, Channel, Height, Width
	TensorLayoutNHWC TensorLayout = "NHWC" // Batch, Height, Width, Channel
	TensorLayoutAuto TensorLayout = "auto" // Automatic layout selection
)

// OptimizationProfile represents predefined optimization profiles
type OptimizationProfile string

const (
	ProfileSpeedOptimized  OptimizationProfile = "speed"     // Maximum speed, higher memory usage
	ProfileMemoryOptimized OptimizationProfile = "memory"    // Minimum memory usage, slower speed
	ProfileBalanced        OptimizationProfile = "balanced"  // Balance between speed and memory
	ProfilePowerEfficient  OptimizationProfile = "efficient" // Power-efficient operation
	ProfileCustom          OptimizationProfile = "custom"    // User-defined settings
)

// EnvironmentType represents deployment environments
type EnvironmentType string

const (
	EnvironmentServer   EnvironmentType = "server"   // Server/datacenter deployment
	EnvironmentDesktop  EnvironmentType = "desktop"  // Desktop workstation
	EnvironmentEmbedded EnvironmentType = "embedded" // Embedded/edge device
	EnvironmentCloud    EnvironmentType = "cloud"    // Cloud deployment
)

// GPUOptimizationConfig represents comprehensive GPU optimization settings
type GPUOptimizationConfig struct {
	// Device Selection
	DeviceSelection DeviceSelectionConfig `json:"device_selection" yaml:"device_selection"`

	// Precision and Computation
	Precision PrecisionConfig `json:"precision" yaml:"precision"`

	// Memory Management
	Memory MemoryConfig `json:"memory" yaml:"memory"`

	// Performance Tuning
	Performance PerformanceConfig `json:"performance" yaml:"performance"`

	// Optimization Presets
	Presets PresetsConfig `json:"presets" yaml:"presets"`

	// Environment-specific settings
	Environment EnvironmentConfig `json:"environment" yaml:"environment"`

	// Runtime Adjustments
	Runtime RuntimeConfig `json:"runtime" yaml:"runtime"`
}

// DeviceSelectionConfig handles GPU device selection
type DeviceSelectionConfig struct {
	AutoSelect       bool     `json:"auto_select" yaml:"auto_select"`
	PreferredVendor  string   `json:"preferred_vendor" yaml:"preferred_vendor"` // "nvidia", "amd", "intel", "apple"
	DeviceIDs        []int    `json:"device_ids" yaml:"device_ids"`
	ExcludeDevices   []int    `json:"exclude_devices" yaml:"exclude_devices"`
	MinMemoryGB      float64  `json:"min_memory_gb" yaml:"min_memory_gb"`
	MinComputeUnits  int      `json:"min_compute_units" yaml:"min_compute_units"`
	RequiredFeatures []string `json:"required_features" yaml:"required_features"`
}

// PrecisionConfig handles precision mode settings
type PrecisionConfig struct {
	DefaultMode    PrecisionMode            `json:"default_mode" yaml:"default_mode"`
	MixedPrecision bool                     `json:"mixed_precision" yaml:"mixed_precision"`
	LayerModes     map[string]PrecisionMode `json:"layer_modes" yaml:"layer_modes"`
	AutoCast       bool                     `json:"auto_cast" yaml:"auto_cast"`
	LossScaling    bool                     `json:"loss_scaling" yaml:"loss_scaling"`
}

// MemoryConfig handles memory management settings
type MemoryConfig struct {
	Strategy             MemoryStrategy `json:"strategy" yaml:"strategy"`
	PoolInitialSizeMB    int            `json:"pool_initial_size_mb" yaml:"pool_initial_size_mb"`
	PoolMaxSizeMB        int            `json:"pool_max_size_mb" yaml:"pool_max_size_mb"`
	GrowthPolicy         string         `json:"growth_policy" yaml:"growth_policy"` // "linear", "exponential", "adaptive"
	FragmentationLimit   float64        `json:"fragmentation_limit" yaml:"fragmentation_limit"`
	GCTriggerThreshold   float64        `json:"gc_trigger_threshold" yaml:"gc_trigger_threshold"`
	GCScheduleInterval   time.Duration  `json:"gc_schedule_interval" yaml:"gc_schedule_interval"`
	PreallocationEnabled bool           `json:"preallocation_enabled" yaml:"preallocation_enabled"`
	CacheEnabled         bool           `json:"cache_enabled" yaml:"cache_enabled"`
	CacheSizeMB          int            `json:"cache_size_mb" yaml:"cache_size_mb"`
}

// PerformanceConfig handles performance tuning parameters
type PerformanceConfig struct {
	BatchSize          int          `json:"batch_size" yaml:"batch_size"`
	MaxBatchSize       int          `json:"max_batch_size" yaml:"max_batch_size"`
	DynamicBatching    bool         `json:"dynamic_batching" yaml:"dynamic_batching"`
	WorkGroupSize      int          `json:"work_group_size" yaml:"work_group_size"`
	ThreadCount        int          `json:"thread_count" yaml:"thread_count"`
	StreamCount        int          `json:"stream_count" yaml:"stream_count"`
	TensorLayout       TensorLayout `json:"tensor_layout" yaml:"tensor_layout"`
	KernelOptimization bool         `json:"kernel_optimization" yaml:"kernel_optimization"`
	GraphOptimization  bool         `json:"graph_optimization" yaml:"graph_optimization"`
	FusionEnabled      bool         `json:"fusion_enabled" yaml:"fusion_enabled"`
	CacheUtilization   bool         `json:"cache_utilization" yaml:"cache_utilization"`
}

// PresetsConfig handles optimization presets
type PresetsConfig struct {
	ActiveProfile     OptimizationProfile        `json:"active_profile" yaml:"active_profile"`
	CustomProfiles    map[string]CustomProfile   `json:"custom_profiles" yaml:"custom_profiles"`
	EnvironmentPreset EnvironmentType            `json:"environment_preset" yaml:"environment_preset"`
	AutoTuning        bool                       `json:"auto_tuning" yaml:"auto_tuning"`
	BenchmarkResults  map[string]BenchmarkResult `json:"benchmark_results" yaml:"benchmark_results"`
}

// CustomProfile represents user-defined optimization profile
type CustomProfile struct {
	Name        string                 `json:"name" yaml:"name"`
	Description string                 `json:"description" yaml:"description"`
	Settings    map[string]interface{} `json:"settings" yaml:"settings"`
	CreatedAt   time.Time              `json:"created_at" yaml:"created_at"`
	Performance BenchmarkResult        `json:"performance" yaml:"performance"`
}

// BenchmarkResult stores performance benchmark data
type BenchmarkResult struct {
	ThroughputOpsPerSec float64       `json:"throughput_ops_per_sec" yaml:"throughput_ops_per_sec"`
	LatencyMs           float64       `json:"latency_ms" yaml:"latency_ms"`
	MemoryUsageMB       float64       `json:"memory_usage_mb" yaml:"memory_usage_mb"`
	PowerConsumptionW   float64       `json:"power_consumption_w" yaml:"power_consumption_w"`
	Timestamp           time.Time     `json:"timestamp" yaml:"timestamp"`
	Duration            time.Duration `json:"duration" yaml:"duration"`
}

// EnvironmentConfig handles environment-specific settings
type EnvironmentConfig struct {
	Type                EnvironmentType `json:"type" yaml:"type"`
	PowerBudgetW        float64         `json:"power_budget_w" yaml:"power_budget_w"`
	ThermalThrottling   bool            `json:"thermal_throttling" yaml:"thermal_throttling"`
	NoiseReduction      bool            `json:"noise_reduction" yaml:"noise_reduction"`
	BatteryOptimization bool            `json:"battery_optimization" yaml:"battery_optimization"`
	NetworkConstraints  bool            `json:"network_constraints" yaml:"network_constraints"`
}

// RuntimeConfig handles runtime adjustment settings
type RuntimeConfig struct {
	DynamicOptimization bool          `json:"dynamic_optimization" yaml:"dynamic_optimization"`
	AdaptiveBatching    bool          `json:"adaptive_batching" yaml:"adaptive_batching"`
	LoadBalancing       bool          `json:"load_balancing" yaml:"load_balancing"`
	FailoverEnabled     bool          `json:"failover_enabled" yaml:"failover_enabled"`
	MonitoringInterval  time.Duration `json:"monitoring_interval" yaml:"monitoring_interval"`
	AdjustmentThreshold float64       `json:"adjustment_threshold" yaml:"adjustment_threshold"`
}

// GPUConfigurationInterface provides user-friendly GPU configuration management
type GPUConfigurationInterface struct {
	config         *GPUOptimizationConfig
	gpuManager     GPUManager
	detectedGPUs   []*GPUInfo
	activeProfile  OptimizationProfile
	benchmarkCache map[string]BenchmarkResult
}

// NewGPUConfigurationInterface creates a new GPU configuration interface
func NewGPUConfigurationInterface(gpuManager GPUManager) (*GPUConfigurationInterface, error) {
	config := DefaultGPUOptimizationConfig()
	gci := &GPUConfigurationInterface{
		config:         config,
		gpuManager:     gpuManager,
		activeProfile:  config.Presets.ActiveProfile,
		benchmarkCache: make(map[string]BenchmarkResult),
	}

	// Detect available GPUs
	gpus, err := gpuManager.GetAvailableGPUs()
	if err != nil {
		return nil, fmt.Errorf("failed to detect GPUs: %w", err)
	}
	gci.detectedGPUs = gpus

	return gci, nil
}

// DefaultGPUOptimizationConfig returns default optimization configuration
func DefaultGPUOptimizationConfig() *GPUOptimizationConfig {
	return &GPUOptimizationConfig{
		DeviceSelection: DeviceSelectionConfig{
			AutoSelect:      true,
			PreferredVendor: "nvidia",
			MinMemoryGB:     2.0,
			MinComputeUnits: 128,
		},
		Precision: PrecisionConfig{
			DefaultMode:    PrecisionFP32,
			MixedPrecision: false,
			AutoCast:       true,
			LossScaling:    false,
		},
		Memory: MemoryConfig{
			Strategy:             MemoryDynamic,
			PoolInitialSizeMB:    512,
			PoolMaxSizeMB:        2048,
			GrowthPolicy:         "adaptive",
			FragmentationLimit:   0.1,
			GCTriggerThreshold:   0.8,
			GCScheduleInterval:   time.Minute * 5,
			PreallocationEnabled: true,
			CacheEnabled:         true,
			CacheSizeMB:          256,
		},
		Performance: PerformanceConfig{
			BatchSize:          32,
			MaxBatchSize:       128,
			DynamicBatching:    true,
			WorkGroupSize:      256,
			ThreadCount:        0, // Auto-detect
			StreamCount:        4,
			TensorLayout:       TensorLayoutAuto,
			KernelOptimization: true,
			GraphOptimization:  true,
			FusionEnabled:      true,
			CacheUtilization:   true,
		},
		Presets: PresetsConfig{
			ActiveProfile:     ProfileBalanced,
			CustomProfiles:    make(map[string]CustomProfile),
			EnvironmentPreset: EnvironmentDesktop,
			AutoTuning:        false,
			BenchmarkResults:  make(map[string]BenchmarkResult),
		},
		Environment: EnvironmentConfig{
			Type:                EnvironmentDesktop,
			PowerBudgetW:        300.0,
			ThermalThrottling:   true,
			NoiseReduction:      false,
			BatteryOptimization: false,
			NetworkConstraints:  false,
		},
		Runtime: RuntimeConfig{
			DynamicOptimization: true,
			AdaptiveBatching:    true,
			LoadBalancing:       false,
			FailoverEnabled:     true,
			MonitoringInterval:  time.Second * 10,
			AdjustmentThreshold: 0.15,
		},
	}
}

// LoadConfiguration loads GPU configuration from file
func (gci *GPUConfigurationInterface) LoadConfiguration(configPath string) error {
	var config GPUOptimizationConfig

	ext := strings.ToLower(filepath.Ext(configPath))
	switch ext {
	case ".yaml", ".yml":
		if err := loadYAMLConfig(configPath, &config); err != nil {
			return fmt.Errorf("failed to load YAML config: %w", err)
		}
	case ".json":
		if err := loadJSONConfig(configPath, &config); err != nil {
			return fmt.Errorf("failed to load JSON config: %w", err)
		}
	default:
		return fmt.Errorf("unsupported configuration file format: %s", ext)
	}

	if err := gci.ValidateConfiguration(&config); err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}

	gci.config = &config
	gci.activeProfile = config.Presets.ActiveProfile
	return nil
}

// SaveConfiguration saves current configuration to file
func (gci *GPUConfigurationInterface) SaveConfiguration(configPath string) error {
	ext := strings.ToLower(filepath.Ext(configPath))
	switch ext {
	case ".yaml", ".yml":
		return saveYAMLConfig(configPath, gci.config)
	case ".json":
		return saveJSONConfig(configPath, gci.config)
	default:
		return fmt.Errorf("unsupported configuration file format: %s", ext)
	}
}

// GetConfiguration returns current configuration
func (gci *GPUConfigurationInterface) GetConfiguration() *GPUOptimizationConfig {
	return gci.config
}

// SetOptimizationProfile applies a predefined optimization profile
func (gci *GPUConfigurationInterface) SetOptimizationProfile(profile OptimizationProfile) error {
	switch profile {
	case ProfileSpeedOptimized:
		gci.applySpeedOptimizedProfile()
	case ProfileMemoryOptimized:
		gci.applyMemoryOptimizedProfile()
	case ProfileBalanced:
		gci.applyBalancedProfile()
	case ProfilePowerEfficient:
		gci.applyPowerEfficientProfile()
	case ProfileCustom:
		// Custom profile should already be configured
	default:
		return fmt.Errorf("unknown optimization profile: %s", profile)
	}

	gci.config.Presets.ActiveProfile = profile
	gci.activeProfile = profile
	return nil
}

// CreateCustomProfile creates a new custom optimization profile
func (gci *GPUConfigurationInterface) CreateCustomProfile(name, description string) error {
	if gci.config.Presets.CustomProfiles == nil {
		gci.config.Presets.CustomProfiles = make(map[string]CustomProfile)
	}

	// Capture current settings
	settings := make(map[string]interface{})
	configBytes, _ := json.Marshal(gci.config)
	json.Unmarshal(configBytes, &settings)

	profile := CustomProfile{
		Name:        name,
		Description: description,
		Settings:    settings,
		CreatedAt:   time.Now(),
	}

	gci.config.Presets.CustomProfiles[name] = profile
	return nil
}

// ApplyCustomProfile applies a custom optimization profile
func (gci *GPUConfigurationInterface) ApplyCustomProfile(name string) error {
	profile, exists := gci.config.Presets.CustomProfiles[name]
	if !exists {
		return fmt.Errorf("custom profile '%s' not found", name)
	}

	// Apply settings from custom profile
	configBytes, err := json.Marshal(profile.Settings)
	if err != nil {
		return fmt.Errorf("failed to serialize custom profile: %w", err)
	}

	var config GPUOptimizationConfig
	if err := json.Unmarshal(configBytes, &config); err != nil {
		return fmt.Errorf("failed to deserialize custom profile: %w", err)
	}

	if err := gci.ValidateConfiguration(&config); err != nil {
		return fmt.Errorf("custom profile validation failed: %w", err)
	}

	gci.config = &config
	gci.config.Presets.ActiveProfile = ProfileCustom
	gci.activeProfile = ProfileCustom
	return nil
}

// GetAvailableGPUs returns detected GPU information
func (gci *GPUConfigurationInterface) GetAvailableGPUs() []*GPUInfo {
	return gci.detectedGPUs
}

// GetRecommendedSettings returns recommended settings based on detected hardware
func (gci *GPUConfigurationInterface) GetRecommendedSettings() (*GPUOptimizationConfig, error) {
	if len(gci.detectedGPUs) == 0 {
		return nil, fmt.Errorf("no GPUs detected")
	}

	// Start with default configuration
	recommended := DefaultGPUOptimizationConfig()

	// Analyze best GPU
	bestGPU := gci.findBestGPU()
	if bestGPU == nil {
		return recommended, nil
	}

	// Adjust settings based on GPU capabilities
	gci.adjustForGPUCapabilities(recommended, bestGPU)
	gci.adjustForMemoryCapacity(recommended, bestGPU)
	gci.adjustForComputeCapability(recommended, bestGPU)

	return recommended, nil
}

// ValidateConfiguration validates GPU configuration settings
func (gci *GPUConfigurationInterface) ValidateConfiguration(config *GPUOptimizationConfig) error {
	var errors []string

	// Validate device selection
	if config.DeviceSelection.MinMemoryGB < 0 {
		errors = append(errors, "minimum memory GB must be non-negative")
	}

	// Validate memory configuration
	if config.Memory.PoolInitialSizeMB <= 0 {
		errors = append(errors, "memory pool initial size must be positive")
	}
	if config.Memory.PoolMaxSizeMB < config.Memory.PoolInitialSizeMB {
		errors = append(errors, "memory pool max size must be >= initial size")
	}
	if config.Memory.FragmentationLimit < 0 || config.Memory.FragmentationLimit > 1 {
		errors = append(errors, "fragmentation limit must be between 0 and 1")
	}

	// Validate performance configuration
	if config.Performance.BatchSize <= 0 {
		errors = append(errors, "batch size must be positive")
	}
	if config.Performance.MaxBatchSize < config.Performance.BatchSize {
		errors = append(errors, "max batch size must be >= batch size")
	}

	// Validate environment configuration
	if config.Environment.PowerBudgetW < 0 {
		errors = append(errors, "power budget must be non-negative")
	}

	if len(errors) > 0 {
		return fmt.Errorf("validation errors: %s", strings.Join(errors, "; "))
	}

	return nil
}

// BenchmarkConfiguration runs performance benchmarks with current configuration
func (gci *GPUConfigurationInterface) BenchmarkConfiguration() (*BenchmarkResult, error) {
	// This would run actual benchmarks - for now, return simulated results
	result := &BenchmarkResult{
		ThroughputOpsPerSec: 1000.0,
		LatencyMs:           2.5,
		MemoryUsageMB:       512.0,
		PowerConsumptionW:   150.0,
		Timestamp:           time.Now(),
		Duration:            time.Minute * 5,
	}

	// Cache the result
	profileKey := string(gci.activeProfile)
	gci.benchmarkCache[profileKey] = *result
	gci.config.Presets.BenchmarkResults[profileKey] = *result

	return result, nil
}

// GetOptimizationSuggestions returns suggestions for improving performance
func (gci *GPUConfigurationInterface) GetOptimizationSuggestions() []string {
	var suggestions []string

	if len(gci.detectedGPUs) == 0 {
		suggestions = append(suggestions, "No GPUs detected - consider enabling GPU support")
		return suggestions
	}

	bestGPU := gci.findBestGPU()
	if bestGPU == nil {
		return suggestions
	}

	// Memory-based suggestions
	if bestGPU.TotalMemory > 8*1024*1024*1024 { // > 8GB
		if gci.config.Memory.PoolMaxSizeMB < 4096 {
			suggestions = append(suggestions, "Consider increasing memory pool size to utilize available GPU memory")
		}
		if gci.config.Precision.DefaultMode == PrecisionFP32 {
			suggestions = append(suggestions, "Consider using FP16 precision to reduce memory usage and increase speed")
		}
	}

	// Performance-based suggestions
	if bestGPU.MultiProcessorCount > 20 {
		if gci.config.Performance.StreamCount < 8 {
			suggestions = append(suggestions, "Consider increasing stream count for better parallelization")
		}
		if !gci.config.Performance.DynamicBatching {
			suggestions = append(suggestions, "Enable dynamic batching for better throughput")
		}
	}

	// Environment-based suggestions
	if gci.config.Environment.Type == EnvironmentServer {
		if !gci.config.Runtime.LoadBalancing {
			suggestions = append(suggestions, "Enable load balancing for server deployments")
		}
		if gci.config.Runtime.MonitoringInterval > time.Second*30 {
			suggestions = append(suggestions, "Use shorter monitoring intervals in server environments")
		}
	}

	return suggestions
}

// Helper functions for optimization profiles
func (gci *GPUConfigurationInterface) applySpeedOptimizedProfile() {
	gci.config.Precision.DefaultMode = PrecisionFP16
	gci.config.Precision.MixedPrecision = true
	gci.config.Memory.Strategy = MemoryPooled
	gci.config.Memory.PoolMaxSizeMB = 4096
	gci.config.Performance.DynamicBatching = true
	gci.config.Performance.MaxBatchSize = 256
	gci.config.Performance.StreamCount = 8
	gci.config.Performance.KernelOptimization = true
	gci.config.Performance.GraphOptimization = true
	gci.config.Performance.FusionEnabled = true
	gci.config.Runtime.DynamicOptimization = true
	gci.config.Runtime.AdaptiveBatching = true
}

func (gci *GPUConfigurationInterface) applyMemoryOptimizedProfile() {
	gci.config.Precision.DefaultMode = PrecisionINT8
	gci.config.Precision.MixedPrecision = false
	gci.config.Memory.Strategy = MemoryDynamic
	gci.config.Memory.PoolMaxSizeMB = 1024
	gci.config.Memory.GCTriggerThreshold = 0.6
	gci.config.Performance.BatchSize = 16
	gci.config.Performance.MaxBatchSize = 64
	gci.config.Performance.StreamCount = 2
	gci.config.Performance.DynamicBatching = false
	gci.config.Runtime.DynamicOptimization = false
}

func (gci *GPUConfigurationInterface) applyBalancedProfile() {
	gci.config.Precision.DefaultMode = PrecisionFP32
	gci.config.Precision.MixedPrecision = false
	gci.config.Memory.Strategy = MemoryDynamic
	gci.config.Memory.PoolMaxSizeMB = 2048
	gci.config.Performance.BatchSize = 32
	gci.config.Performance.MaxBatchSize = 128
	gci.config.Performance.StreamCount = 4
	gci.config.Performance.DynamicBatching = true
	gci.config.Runtime.DynamicOptimization = true
}

func (gci *GPUConfigurationInterface) applyPowerEfficientProfile() {
	gci.config.Precision.DefaultMode = PrecisionFP16
	gci.config.Memory.Strategy = MemoryDynamic
	gci.config.Memory.PoolMaxSizeMB = 1024
	gci.config.Performance.BatchSize = 16
	gci.config.Performance.StreamCount = 2
	gci.config.Environment.ThermalThrottling = true
	gci.config.Environment.BatteryOptimization = true
	gci.config.Runtime.MonitoringInterval = time.Second * 30
}

// Helper functions
func (gci *GPUConfigurationInterface) findBestGPU() *GPUInfo {
	if len(gci.detectedGPUs) == 0 {
		return nil
	}

	// Sort GPUs by memory and compute capability
	best := gci.detectedGPUs[0]
	for _, gpu := range gci.detectedGPUs[1:] {
		if gpu.Available && gpu.TotalMemory > best.TotalMemory {
			best = gpu
		}
	}

	return best
}

func (gci *GPUConfigurationInterface) adjustForGPUCapabilities(config *GPUOptimizationConfig, gpu *GPUInfo) {
	// Adjust based on GPU vendor
	switch strings.ToLower(gpu.Vendor) {
	case "nvidia":
		config.DeviceSelection.PreferredVendor = "nvidia"
		if gpu.ComputeCapability.Major >= 7 {
			config.Precision.MixedPrecision = true
		}
	case "amd":
		config.DeviceSelection.PreferredVendor = "amd"
	case "apple":
		config.DeviceSelection.PreferredVendor = "apple"
		config.Memory.Strategy = MemoryUnified
	}
}

func (gci *GPUConfigurationInterface) adjustForMemoryCapacity(config *GPUOptimizationConfig, gpu *GPUInfo) {
	memoryGB := float64(gpu.TotalMemory) / (1024 * 1024 * 1024)

	if memoryGB >= 16 {
		config.Memory.PoolMaxSizeMB = 8192
		config.Performance.MaxBatchSize = 512
	} else if memoryGB >= 8 {
		config.Memory.PoolMaxSizeMB = 4096
		config.Performance.MaxBatchSize = 256
	} else if memoryGB >= 4 {
		config.Memory.PoolMaxSizeMB = 2048
		config.Performance.MaxBatchSize = 128
	} else {
		config.Memory.PoolMaxSizeMB = 1024
		config.Performance.MaxBatchSize = 64
		config.Precision.DefaultMode = PrecisionFP16
	}
}

func (gci *GPUConfigurationInterface) adjustForComputeCapability(config *GPUOptimizationConfig, gpu *GPUInfo) {
	if gpu.MultiProcessorCount > 40 {
		config.Performance.StreamCount = 8
		config.Performance.WorkGroupSize = 512
	} else if gpu.MultiProcessorCount > 20 {
		config.Performance.StreamCount = 6
		config.Performance.WorkGroupSize = 256
	} else {
		config.Performance.StreamCount = 4
		config.Performance.WorkGroupSize = 128
	}
}

// Configuration file I/O helpers
func loadYAMLConfig(configPath string, config *GPUOptimizationConfig) error {
	// Implementation would read and parse YAML file
	// For now, return nil as placeholder
	return nil
}

func loadJSONConfig(configPath string, config *GPUOptimizationConfig) error {
	// Implementation would read and parse JSON file
	// For now, return nil as placeholder
	return nil
}

func saveYAMLConfig(configPath string, config *GPUOptimizationConfig) error {
	// Implementation would save configuration as YAML
	// For now, return nil as placeholder
	return nil
}

func saveJSONConfig(configPath string, config *GPUOptimizationConfig) error {
	// Implementation would save configuration as JSON
	// For now, return nil as placeholder
	return nil
}

// GetConfigurationSummary returns a human-readable configuration summary
func (gci *GPUConfigurationInterface) GetConfigurationSummary() string {
	var summary strings.Builder

	summary.WriteString("GPU Configuration Summary\n")
	summary.WriteString("========================\n\n")

	summary.WriteString(fmt.Sprintf("Active Profile: %s\n", gci.activeProfile))
	summary.WriteString(fmt.Sprintf("Environment: %s\n", gci.config.Environment.Type))
	summary.WriteString(fmt.Sprintf("Precision Mode: %s\n", gci.config.Precision.DefaultMode))
	summary.WriteString(fmt.Sprintf("Memory Strategy: %s\n", gci.config.Memory.Strategy))
	summary.WriteString(fmt.Sprintf("Batch Size: %d (max: %d)\n",
		gci.config.Performance.BatchSize, gci.config.Performance.MaxBatchSize))

	if len(gci.detectedGPUs) > 0 {
		summary.WriteString(fmt.Sprintf("\nDetected GPUs: %d\n", len(gci.detectedGPUs)))
		for i, gpu := range gci.detectedGPUs {
			memoryGB := float64(gpu.TotalMemory) / (1024 * 1024 * 1024)
			summary.WriteString(fmt.Sprintf("  %d. %s (%.1f GB, %s)\n",
				i+1, gpu.Name, memoryGB, gpu.Vendor))
		}
	}

	return summary.String()
}

// GetPerformanceReport returns performance analysis and recommendations
func (gci *GPUConfigurationInterface) GetPerformanceReport() string {
	var report strings.Builder

	report.WriteString("GPU Performance Report\n")
	report.WriteString("======================\n\n")

	// Current configuration analysis
	report.WriteString("Current Configuration Analysis:\n")
	report.WriteString(fmt.Sprintf("- Profile: %s\n", gci.activeProfile))
	report.WriteString(fmt.Sprintf("- Estimated Memory Usage: %d MB\n",
		gci.estimateMemoryUsage()))
	report.WriteString(fmt.Sprintf("- Parallel Streams: %d\n",
		gci.config.Performance.StreamCount))

	// Benchmark results
	if len(gci.benchmarkCache) > 0 {
		report.WriteString("\nBenchmark Results:\n")
		for profile, result := range gci.benchmarkCache {
			report.WriteString(fmt.Sprintf("- %s: %.1f ops/sec, %.2f ms latency\n",
				profile, result.ThroughputOpsPerSec, result.LatencyMs))
		}
	}

	// Optimization suggestions
	suggestions := gci.GetOptimizationSuggestions()
	if len(suggestions) > 0 {
		report.WriteString("\nOptimization Suggestions:\n")
		for i, suggestion := range suggestions {
			report.WriteString(fmt.Sprintf("%d. %s\n", i+1, suggestion))
		}
	}

	return report.String()
}

// Helper function to estimate memory usage
func (gci *GPUConfigurationInterface) estimateMemoryUsage() int {
	baseUsage := gci.config.Memory.PoolInitialSizeMB
	batchMultiplier := float64(gci.config.Performance.BatchSize) / 32.0

	switch gci.config.Precision.DefaultMode {
	case PrecisionFP32:
		return int(float64(baseUsage) * batchMultiplier * 1.0)
	case PrecisionFP16:
		return int(float64(baseUsage) * batchMultiplier * 0.5)
	case PrecisionINT8:
		return int(float64(baseUsage) * batchMultiplier * 0.25)
	default:
		return baseUsage
	}
}
