//go:build linux && cuda
// +build linux,cuda

package gpu

import (
	"bytes"
	"context"
	"fmt"
	"log"
	"os"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"time"

	"neuralmetergo/internal/gpu/types"
)

// CUDADetector implements GPUDetector for CUDA GPUs with runtime detection
type CUDADetector struct {
	logger      *log.<PERSON><PERSON>
	initialized bool
	deviceCount int
	available   bool
}

// NewCUDADetector creates a new CUDA detector with runtime detection
func NewCUDADetector(logger *log.Logger) GPUDetector {
	if logger == nil {
		logger = log.Default()
	}

	detector := &CUDADetector{
		logger: logger,
	}

	// Check if CUDA is available at runtime
	detector.available = detector.checkCUDAAvailability()

	return detector
}

// checkCUDAAvailability checks if CUDA is available on the system
func (c *CUDADetector) checkCUDAAvailability() bool {
	// Check for nvidia-smi
	if _, err := exec.LookPath("nvidia-smi"); err != nil {
		c.logger.Println("nvidia-smi not found in PATH")
		return false
	}

	// Check for CUDA runtime library
	cudaPaths := []string{
		"/usr/local/cuda/lib64/libcudart.so",
		"/usr/lib/x86_64-linux-gnu/libcudart.so",
		"/opt/cuda/lib64/libcudart.so",
	}

	for _, path := range cudaPaths {
		if _, err := os.Stat(path); err == nil {
			c.logger.Printf("Found CUDA runtime at: %s", path)
			return true
		}
	}

	// Check via nvidia-smi command
	cmd := exec.Command("nvidia-smi", "--query-gpu=count", "--format=csv,noheader,nounits")
	if err := cmd.Run(); err != nil {
		c.logger.Printf("nvidia-smi query failed: %v", err)
		return false
	}

	c.logger.Println("CUDA availability confirmed via nvidia-smi")
	return true
}

// Detect enumerates all available CUDA GPUs using nvidia-smi
func (c *CUDADetector) Detect() ([]*GPUInfo, error) {
	if !c.available {
		return []*GPUInfo{}, nil
	}

	if !c.initialized {
		if err := c.Initialize(); err != nil {
			return []*GPUInfo{}, nil
		}
	}

	if c.deviceCount == 0 {
		return []*GPUInfo{}, nil
	}

	var gpus []*GPUInfo
	for i := 0; i < c.deviceCount; i++ {
		gpu, err := c.GetInfo(i)
		if err != nil {
			c.logger.Printf("Failed to get info for CUDA device %d: %v", i, err)
			continue
		}
		gpus = append(gpus, gpu)
	}

	return gpus, nil
}

// GetInfo retrieves detailed information about a specific CUDA GPU using nvidia-smi
func (c *CUDADetector) GetInfo(deviceID int) (*GPUInfo, error) {
	if !c.available {
		return nil, NewGPUError(ErrorTypeDetection, -1, "CUDA not available on system", deviceID)
	}

	if !c.initialized {
		return nil, fmt.Errorf("CUDA detector not initialized")
	}

	if deviceID < 0 || deviceID >= c.deviceCount {
		return nil, fmt.Errorf("invalid device ID %d", deviceID)
	}

	// Use nvidia-smi to get comprehensive GPU information
	cmd := exec.Command("nvidia-smi",
		"--query-gpu=name,memory.total,memory.free,compute_cap,driver_version,clocks.gr,clocks.mem,fan.speed,power.draw,utilization.gpu,pci.bus_id,count",
		"--format=csv,noheader,nounits",
		fmt.Sprintf("--id=%d", deviceID))

	output, err := cmd.Output()
	if err != nil {
		c.logger.Printf("nvidia-smi query failed for device %d: %v", deviceID, err)
		return nil, fmt.Errorf("failed to query GPU %d: %v", deviceID, err)
	}

	c.logger.Printf("nvidia-smi output for device %d: %s", deviceID, strings.TrimSpace(string(output)))

	// Parse nvidia-smi output
	gpu, err := c.parseNvidiaSmiOutput(deviceID, string(output))
	if err != nil {
		c.logger.Printf("Failed to parse nvidia-smi output: %v", err)
		return nil, fmt.Errorf("failed to parse GPU info: %v", err)
	}

	return gpu, nil
}

// parseNvidiaSmiOutput parses nvidia-smi CSV output into GPUInfo
func (c *CUDADetector) parseNvidiaSmiOutput(deviceID int, output string) (*GPUInfo, error) {
	// Clean and split the output
	output = strings.TrimSpace(output)
	fields := strings.Split(output, ", ")

	// Trim whitespace from each field
	for i := range fields {
		fields[i] = strings.TrimSpace(fields[i])
	}

	c.logger.Printf("Parsed %d fields from nvidia-smi output: %v", len(fields), fields)

	if len(fields) < 5 {
		return nil, fmt.Errorf("unexpected nvidia-smi output format, got %d fields: %v", len(fields), fields)
	}

	// Parse memory values (nvidia-smi returns in MiB)
	var totalMem, freeMem int64
	if fields[1] != "[N/A]" && fields[1] != "N/A" {
		fmt.Sscanf(fields[1], "%d", &totalMem)
		totalMem *= 1024 * 1024 // Convert MiB to bytes
	}
	if fields[2] != "[N/A]" && fields[2] != "N/A" {
		fmt.Sscanf(fields[2], "%d", &freeMem)
		freeMem *= 1024 * 1024 // Convert MiB to bytes
	}

	// Parse compute capability
	var major, minor int
	if fields[3] != "[N/A]" && fields[3] != "N/A" {
		fmt.Sscanf(fields[3], "%d.%d", &major, &minor)
	}

	// Parse additional fields if available
	var clockRate, memoryClockRate, fanSpeed int
	var powerUsage, utilization float64

	if len(fields) > 5 && fields[5] != "[N/A]" && fields[5] != "N/A" {
		fmt.Sscanf(fields[5], "%d", &clockRate) // Graphics clock in MHz
	}
	if len(fields) > 6 && fields[6] != "[N/A]" && fields[6] != "N/A" {
		fmt.Sscanf(fields[6], "%d", &memoryClockRate) // Memory clock in MHz
	}
	if len(fields) > 7 && fields[7] != "[N/A]" && fields[7] != "N/A" {
		fmt.Sscanf(fields[7], "%d", &fanSpeed) // Fan speed in %
	}
	if len(fields) > 8 && fields[8] != "[N/A]" && fields[8] != "N/A" {
		fmt.Sscanf(fields[8], "%f", &powerUsage) // Power draw in W
	}
	if len(fields) > 9 && fields[9] != "[N/A]" && fields[9] != "N/A" {
		fmt.Sscanf(fields[9], "%f", &utilization) // GPU utilization in %
	}

	// ---------------------------------------------------
	// Query SM-count (multiprocessor_count) separately so
	// we don’t have to change the main CSV field order.
	// ---------------------------------------------------
	mpCountCmd := exec.Command(
		"nvidia-smi",
		"--query-gpu=multiprocessor_count",
		"--format=csv,noheader,nounits",
		fmt.Sprintf("--id=%d", deviceID),
	)
	mpRaw, mpErr := mpCountCmd.Output()
	mpCount := 0
	if mpErr == nil {
		mpCountStr := strings.TrimSpace(string(mpRaw))
		if v, convErr := strconv.Atoi(mpCountStr); convErr == nil {
			mpCount = v
		}
	}

	// --- fallback via `nvidia-smi -q` when SM count not returned ---
	if mpCount == 0 {
		patternList := []string{
			`Multiprocessor Count\s*:\s*(\d+)`,
			`Streaming Multiprocessors\s*:\s*(\d+)`,
			`Number of Streaming Multiprocessors\s*:\s*(\d+)`,
		}
		if v := fallbackIntFromSmiQMulti(deviceID, patternList, c.logger); v > 0 {
			mpCount = v
		}
	}

	// Query bus width
	busWidth := querySingleInt(deviceID, "pci.bus_width", c.logger)

	if busWidth <= 0 {
		patternList := []string{
			`Bus Width\s*:\s*(\d+)`,
			`Bridge Chip.*?Link Width Current\s*:\s*(\d+)`,
		}
		if v := fallbackIntFromSmiQMulti(deviceID, patternList, c.logger); v > 0 {
			busWidth = v
		}
	}

	// Query architecture
	architecture := architectureFromCompute(ComputeCapability{Major: major, Minor: minor})

	gpu := &GPUInfo{
		ID:                      deviceID,
		Name:                    strings.TrimSpace(fields[0]),
		Vendor:                  "NVIDIA",
		Type:                    GPUTypeCUDA,
		TotalMemory:             totalMem,
		FreeMemory:              freeMem,
		ComputeCapability:       ComputeCapability{Major: major, Minor: minor},
		MultiProcessorCount:     mpCount,
		ClockRate:               clockRate * 1000,       // Convert MHz to kHz
		MemoryClockRate:         memoryClockRate * 1000, // Convert MHz to kHz
		MemoryBusWidth:          busWidth,
		MaxThreadsPerBlock:      1024, // Common CUDA default
		MaxBlockDimensions:      [3]int{1024, 1024, 64},
		MaxGridDimensions:       [3]int{65535, 65535, 65535},
		WarpSize:                32,        // Standard CUDA warp size
		MaxSharedMemoryPerBlock: 48 * 1024, // Common default
		Available:               true,
		PowerUsage:              powerUsage,
		Utilization:             utilization,
		Architecture:            architecture,
	}

	// Fill in any missing fields using NVML (if available)
	UpdateGPUInfoNVML(gpu)

	return gpu, nil
}

// GetMetrics retrieves real-time metrics for a CUDA GPU using nvidia-smi
func (c *CUDADetector) GetMetrics(deviceID int) (*GPUMetrics, error) {
	if !c.available {
		return nil, NewGPUError(ErrorTypeMonitoring, -1, "CUDA not available on system", deviceID)
	}

	if !c.initialized {
		return nil, fmt.Errorf("CUDA detector not initialized")
	}

	if deviceID < 0 || deviceID >= c.deviceCount {
		return nil, fmt.Errorf("invalid device ID %d", deviceID)
	}

	// Query current metrics via nvidia-smi
	cmd := exec.Command("nvidia-smi",
		"--query-gpu=utilization.gpu,utilization.memory,memory.used,memory.total,power.draw,temperature.gpu",
		"--format=csv,noheader,nounits",
		fmt.Sprintf("--id=%d", deviceID))

	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to query GPU metrics %d: %v", deviceID, err)
	}

	// Parse metrics
	var gpuUtil, memUtil, memUsed, memTotal, power, temp float64
	n, err := fmt.Sscanf(string(output), "%f, %f, %f, %f, %f, %f",
		&gpuUtil, &memUtil, &memUsed, &memTotal, &power, &temp)
	if err != nil || n < 6 {
		return nil, fmt.Errorf("failed to parse GPU metrics: %v", err)
	}

	metrics := &GPUMetrics{
		DeviceID:          deviceID,
		Timestamp:         time.Now(),
		GPUUtilization:    gpuUtil,
		MemoryUtilization: memUtil,
		PowerConsumption:  power,
		ClockSpeed:        c.getClockSpeed(deviceID),
		MemoryClockSpeed:  c.getMemoryClockSpeed(deviceID),
		FanSpeed:          c.getFanSpeed(deviceID),
	}

	return metrics, nil
}

// IsSupported returns true if CUDA is available on the system
func (c *CUDADetector) IsSupported() bool {
	return c.available
}

// Initialize initializes the CUDA detector using nvidia-smi
func (c *CUDADetector) Initialize() error {
	if !c.available {
		c.logger.Println("CUDA detector not available on system")
		return NewGPUError(ErrorTypeInitialization, -1, "CUDA not available on system", -1)
	}

	// Get device count via nvidia-smi
	cmd := exec.Command("nvidia-smi", "--query-gpu=count", "--format=csv,noheader,nounits")
	_, err := cmd.Output()
	if err != nil {
		c.logger.Printf("Failed to get CUDA device count: %v", err)
		return NewGPUError(ErrorTypeInitialization, -1, fmt.Sprintf("Failed to query CUDA devices: %v", err), -1)
	}

	// Count devices by querying each one
	deviceCount := 0
	for i := 0; i < 16; i++ { // Check up to 16 devices
		cmd := exec.Command("nvidia-smi",
			"--query-gpu=name", "--format=csv,noheader,nounits",
			fmt.Sprintf("--id=%d", i))
		if err := cmd.Run(); err == nil {
			deviceCount++
		} else {
			break
		}
	}

	c.deviceCount = deviceCount
	c.initialized = true

	c.logger.Printf("CUDA detector initialized with %d devices", c.deviceCount)
	return nil
}

// Cleanup cleans up CUDA resources
func (c *CUDADetector) Cleanup() error {
	c.initialized = false
	c.deviceCount = 0
	return nil
}

// Helper functions using nvidia-smi

func (c *CUDADetector) getPowerUsage(deviceID int) float64 {
	cmd := exec.Command("nvidia-smi",
		"--query-gpu=power.draw", "--format=csv,noheader,nounits",
		fmt.Sprintf("--id=%d", deviceID))

	output, err := cmd.Output()
	if err != nil {
		return 0.0
	}

	var power float64
	fmt.Sscanf(string(output), "%f", &power)
	return power
}

func (c *CUDADetector) getUtilization(deviceID int) float64 {
	cmd := exec.Command("nvidia-smi",
		"--query-gpu=utilization.gpu", "--format=csv,noheader,nounits",
		fmt.Sprintf("--id=%d", deviceID))

	output, err := cmd.Output()
	if err != nil {
		return 0.0
	}

	var util float64
	fmt.Sscanf(string(output), "%f", &util)
	return util
}

func (c *CUDADetector) getClockSpeed(deviceID int) int {
	cmd := exec.Command("nvidia-smi",
		"--query-gpu=clocks.gr", "--format=csv,noheader,nounits",
		fmt.Sprintf("--id=%d", deviceID))

	output, err := cmd.Output()
	if err != nil {
		return 0
	}

	var clock int
	fmt.Sscanf(string(output), "%d", &clock)
	return clock
}

func (c *CUDADetector) getMemoryClockSpeed(deviceID int) int {
	cmd := exec.Command("nvidia-smi",
		"--query-gpu=clocks.mem", "--format=csv,noheader,nounits",
		fmt.Sprintf("--id=%d", deviceID))

	output, err := cmd.Output()
	if err != nil {
		return 0
	}

	var clock int
	fmt.Sscanf(string(output), "%d", &clock)
	return clock
}

func (c *CUDADetector) getFanSpeed(deviceID int) int {
	cmd := exec.Command("nvidia-smi",
		"--query-gpu=fan.speed", "--format=csv,noheader,nounits",
		fmt.Sprintf("--id=%d", deviceID))

	output, err := cmd.Output()
	if err != nil {
		return 0
	}

	var fan int
	fmt.Sscanf(string(output), "%d", &fan)
	return fan
}

// architectureFromCompute maps CUDA compute capability to common architecture names.
func architectureFromCompute(cc ComputeCapability) string {
	switch cc.Major {
	case 3:
		return "Kepler"
	case 5:
		return "Maxwell"
	case 6:
		return "Pascal"
	case 7:
		if cc.Minor >= 5 {
			return "Turing"
		}
		return "Volta"
	case 8:
		if cc.Minor >= 9 {
			return "Ada"
		}
		return "Ampere"
	case 9:
		return "Hopper"
	default:
		return "Unknown"
	}
}

// querySingleInt runs nvidia-smi for a single integer field.
func querySingleInt(deviceID int, field string, logger *log.Logger) int {
	cmd := exec.Command("nvidia-smi",
		fmt.Sprintf("--query-gpu=%s", field),
		"--format=csv,noheader,nounits",
		fmt.Sprintf("--id=%d", deviceID),
	)
	raw, err := cmd.Output()
	if err != nil {
		logger.Printf("nvidia-smi query for %s failed: %v", field, err)
		return 0
	}
	v, _ := strconv.Atoi(strings.TrimSpace(string(raw)))
	return v
}

// fallbackIntFromSmiQ runs `nvidia-smi -q -i <id>` once and tries many regex
// patterns until one matches.  It returns 0 if nothing matches.
func fallbackIntFromSmiQ(deviceID int, pattern string, logger *log.Logger) int {
	return fallbackIntFromSmiQMulti(deviceID, []string{pattern}, logger)
}

func fallbackIntFromSmiQMulti(deviceID int, patterns []string, logger *log.Logger) int {
	cmd := exec.Command("nvidia-smi", "-q", fmt.Sprintf("-i=%d", deviceID))
	out, err := cmd.Output()
	if err != nil {
		logger.Printf("nvidia-smi -q failed for fallback parse: %v", err)
		return 0
	}
	cleaned := bytes.ReplaceAll(out, []byte("\r"), []byte(""))
	for _, p := range patterns {
		re := regexp.MustCompile(p)
		match := re.FindSubmatch(cleaned)
		if len(match) >= 2 {
			if v, err := strconv.Atoi(string(match[1])); err == nil {
				return v
			}
		}
	}
	return 0
}

// CUDABackendWrapper wraps CUDADetector to implement types.GPUBackend interface
type CUDABackendWrapper struct {
	detector GPUDetector
}

// Name returns the backend name
func (c *CUDABackendWrapper) Name() string {
	return "CUDA"
}

// Version returns the backend version
func (c *CUDABackendWrapper) Version() string {
	return "1.0"
}

// Platform returns the platform name
func (c *CUDABackendWrapper) Platform() string {
	return "Linux"
}

// EnumerateDevices lists all CUDA devices
func (c *CUDABackendWrapper) EnumerateDevices(ctx context.Context) ([]types.GPUDevice, error) {
	gpuInfos, err := c.detector.Detect()
	if err != nil {
		return nil, err
	}

	var devices []types.GPUDevice
	for _, info := range gpuInfos {
		device := types.GPUDevice{
			ID:      fmt.Sprintf("cuda:%d", info.ID),
			Index:   info.ID,
			Name:    info.Name,
			Vendor:  info.Vendor,
			Backend: "CUDA",
			Memory: types.MemoryInfo{
				Total: uint64(info.TotalMemory),
				Free:  uint64(info.FreeMemory),
				Used:  uint64(info.TotalMemory - info.FreeMemory),
			},
			Compute: types.ComputeInfo{
				Units:      info.MultiProcessorCount,
				ClockSpeed: info.ClockRate,
				Capability: types.ComputeCapability{
					Major: info.ComputeCapability.Major,
					Minor: info.ComputeCapability.Minor,
				},
			},
			ComputeUnits: info.MultiProcessorCount,
			ClockSpeed:   info.ClockRate,
		}
		devices = append(devices, device)
	}

	return devices, nil
}

// GetDevice returns a specific device by ID
func (c *CUDABackendWrapper) GetDevice(deviceID string) (*types.GPUDevice, error) {
	var id int
	fmt.Sscanf(deviceID, "cuda:%d", &id)

	info, err := c.detector.GetInfo(id)
	if err != nil {
		return nil, err
	}

	return &types.GPUDevice{
		ID:      deviceID,
		Name:    info.Name,
		Backend: "CUDA",
	}, nil
}

// GetCapabilities returns device capabilities
func (c *CUDABackendWrapper) GetCapabilities(device *types.GPUDevice) (*types.GPUCapability, error) {
	return &types.GPUCapability{
		Features: map[types.GPUFeature]bool{
			types.FeatureCompute: true,
		},
	}, nil
}

// SupportsFeature checks if device supports a feature
func (c *CUDABackendWrapper) SupportsFeature(device *types.GPUDevice, feature types.GPUFeature) bool {
	return feature == types.FeatureCompute
}

// CreateContext creates a GPU context for the device
func (c *CUDABackendWrapper) CreateContext(device *types.GPUDevice) (types.GPUContext, error) {
	var deviceID int
	fmt.Sscanf(device.ID, "cuda:%d", &deviceID)

	return &CUDAContext{
		deviceID: deviceID,
		detector: c.detector,
	}, nil
}

// CreateMemoryManager creates a memory manager
func (c *CUDABackendWrapper) CreateMemoryManager(ctx types.GPUContext) (types.GPUMemoryManager, error) {
	return &CUDAMemoryManager{ctx: ctx}, nil
}

// CreateExecutor creates an executor
func (c *CUDABackendWrapper) CreateExecutor(ctx types.GPUContext) (types.GPUExecutor, error) {
	return &CUDAExecutor{ctx: ctx}, nil
}

// CUDAContext implements types.GPUContext
type CUDAContext struct {
	deviceID int
	detector GPUDetector
}

func (c *CUDAContext) GetDevice() *types.GPUDevice {
	return &types.GPUDevice{
		ID:      fmt.Sprintf("cuda:%d", c.deviceID),
		Backend: "CUDA",
	}
}

func (c *CUDAContext) IsValid() bool {
	return true
}

func (c *CUDAContext) Synchronize() error {
	return nil
}

func (c *CUDAContext) GetDeviceID() string {
	return fmt.Sprintf("cuda:%d", c.deviceID)
}

func (c *CUDAContext) GetBackend() string {
	return "CUDA"
}

func (c *CUDAContext) Destroy() error {
	return nil
}

// CUDAMemoryManager implements types.GPUMemoryManager
type CUDAMemoryManager struct {
	ctx types.GPUContext
}

func (c *CUDAMemoryManager) Allocate(size uint64) (types.GPUMemory, error) {
	return &CUDAMemory{size: size}, nil
}

func (c *CUDAMemoryManager) AllocateType(size uint64, memType types.MemoryType) (types.GPUMemory, error) {
	return &CUDAMemory{size: size}, nil
}

func (c *CUDAMemoryManager) GetStats() types.GPUMemoryStats {
	return types.GPUMemoryStats{}
}

func (c *CUDAMemoryManager) Cleanup() error {
	return nil
}

// CUDAMemory implements types.GPUMemory
type CUDAMemory struct {
	size uint64
}

func (c *CUDAMemory) Ptr() uintptr {
	return 0
}

func (c *CUDAMemory) Size() uint64 {
	return c.size
}

func (c *CUDAMemory) Type() types.MemoryType {
	return types.MemoryTypeDiscrete
}

func (c *CUDAMemory) Free() error {
	return nil
}

func (c *CUDAMemory) CopyFrom(src []byte) error {
	return nil
}

func (c *CUDAMemory) CopyTo(dst []byte) error {
	return nil
}

func (c *CUDAMemory) CopyFromGPU(src types.GPUMemory) error {
	return nil
}

// CUDAExecutor implements types.GPUExecutor
type CUDAExecutor struct {
	ctx types.GPUContext
}

func (c *CUDAExecutor) CreateKernel(source string, entryPoint string) (types.GPUKernel, error) {
	return &CUDAKernel{name: entryPoint}, nil
}

func (c *CUDAExecutor) CreateStream() (types.GPUStream, error) {
	return &CUDAStreamStub{id: "cuda_stream"}, nil
}

func (c *CUDAExecutor) CreateEvent() (types.GPUEvent, error) {
	return &CUDAEventStub{}, nil
}

func (c *CUDAExecutor) Synchronize() error {
	return nil
}

func (c *CUDAExecutor) GetStreams() []types.GPUStream {
	return []types.GPUStream{}
}

// CUDAKernel implements types.GPUKernel
type CUDAKernel struct {
	name string
}

func (c *CUDAKernel) GetName() string {
	return c.name
}

func (c *CUDAKernel) Launch(grid types.GridDimension, block types.GridDimension, args []interface{}, stream types.GPUStream) error {
	return nil
}

func (c *CUDAKernel) GetAttributes() map[string]interface{} {
	return map[string]interface{}{}
}

func (c *CUDAKernel) Destroy() error {
	return nil
}

// CUDAStreamStub implements types.GPUStream (pure-Go fallback when CGO streams are unavailable)
type CUDAStreamStub struct {
	id string
}

func (c *CUDAStreamStub) ID() string {
	return c.id
}

func (c *CUDAStreamStub) Submit(kernel types.GPUKernel, grid types.GridDimension, block types.GridDimension, args []interface{}) error {
	return nil
}

func (c *CUDAStreamStub) Synchronize() error {
	return nil
}

func (c *CUDAStreamStub) Query() types.StreamState {
	return 0
}

func (c *CUDAStreamStub) Destroy() error {
	return nil
}

// CUDAEventStub implements types.GPUEvent
type CUDAEventStub struct{}

func (c *CUDAEventStub) ID() string {
	return "cuda_event"
}

func (c *CUDAEventStub) Record(stream types.GPUStream) error {
	return nil
}

func (c *CUDAEventStub) Wait() error {
	return nil
}

func (c *CUDAEventStub) Query() types.EventState {
	return 0
}

func (c *CUDAEventStub) ElapsedTime(start types.GPUEvent) (time.Duration, error) {
	return 0, nil
}

func (c *CUDAEventStub) Destroy() error {
	return nil
}
