package gpu

import (
	"bytes"
	"compress/gzip"
	"encoding/binary"
	"fmt"
	"io"
	"math"
	"time"

	"github.com/klauspost/compress/zstd"
	"github.com/pierrec/lz4/v4"
)

// LZ4Compressor implements fast LZ4 compression
type LZ4Compressor struct {
	ratio              float64
	decompressionSpeed time.Duration
}

func (lz4Comp *LZ4Compressor) Compress(data []byte) ([]byte, error) {
	var buf bytes.Buffer
	writer := lz4.NewWriter(&buf)

	_, err := writer.Write(data)
	if err != nil {
		return nil, fmt.Errorf("lz4 compression failed: %w", err)
	}

	err = writer.Close()
	if err != nil {
		return nil, fmt.Errorf("lz4 compression close failed: %w", err)
	}

	compressed := buf.Bytes()
	lz4Comp.ratio = float64(len(data)) / float64(len(compressed))

	return compressed, nil
}

func (lz4Comp *LZ4Compressor) Decompress(compressedData []byte) ([]byte, error) {
	reader := lz4.NewReader(bytes.NewReader(compressedData))

	var buf bytes.Buffer
	start := time.Now()
	_, err := io.Copy(&buf, reader)
	lz4Comp.decompressionSpeed = time.Since(start)

	if err != nil {
		return nil, fmt.Errorf("lz4 decompression failed: %w", err)
	}

	return buf.Bytes(), nil
}

func (lz4Comp *LZ4Compressor) Name() string {
	return "lz4"
}

func (lz4Comp *LZ4Compressor) CompressionRatio() float64 {
	return lz4Comp.ratio
}

func (lz4Comp *LZ4Compressor) DecompressionSpeed() time.Duration {
	return lz4Comp.decompressionSpeed
}

func (lz4Comp *LZ4Compressor) SupportsGPU() bool {
	return false
}

// ZstdCompressor implements Zstandard compression with configurable levels
type ZstdCompressor struct {
	Level              int
	ratio              float64
	decompressionSpeed time.Duration
}

func (z *ZstdCompressor) Compress(data []byte) ([]byte, error) {
	encoder, err := zstd.NewWriter(nil, zstd.WithEncoderLevel(zstd.EncoderLevelFromZstd(z.Level)))
	if err != nil {
		return nil, fmt.Errorf("zstd encoder creation failed: %w", err)
	}
	defer encoder.Close()

	compressed := encoder.EncodeAll(data, make([]byte, 0, len(data)))
	z.ratio = float64(len(data)) / float64(len(compressed))

	return compressed, nil
}

func (z *ZstdCompressor) Decompress(compressedData []byte) ([]byte, error) {
	decoder, err := zstd.NewReader(nil)
	if err != nil {
		return nil, fmt.Errorf("zstd decoder creation failed: %w", err)
	}
	defer decoder.Close()

	start := time.Now()
	decompressed, err := decoder.DecodeAll(compressedData, nil)
	z.decompressionSpeed = time.Since(start)

	if err != nil {
		return nil, fmt.Errorf("zstd decompression failed: %w", err)
	}

	return decompressed, nil
}

func (z *ZstdCompressor) Name() string {
	return fmt.Sprintf("zstd_level_%d", z.Level)
}

func (z *ZstdCompressor) CompressionRatio() float64 {
	return z.ratio
}

func (z *ZstdCompressor) DecompressionSpeed() time.Duration {
	return z.decompressionSpeed
}

func (z *ZstdCompressor) SupportsGPU() bool {
	return false
}

// GzipCompressor implements traditional gzip compression
type GzipCompressor struct {
	Level              int
	ratio              float64
	decompressionSpeed time.Duration
}

func (g *GzipCompressor) Compress(data []byte) ([]byte, error) {
	var buf bytes.Buffer
	writer, err := gzip.NewWriterLevel(&buf, g.Level)
	if err != nil {
		return nil, fmt.Errorf("gzip writer creation failed: %w", err)
	}

	_, err = writer.Write(data)
	if err != nil {
		writer.Close()
		return nil, fmt.Errorf("gzip compression failed: %w", err)
	}

	err = writer.Close()
	if err != nil {
		return nil, fmt.Errorf("gzip compression close failed: %w", err)
	}

	compressed := buf.Bytes()
	g.ratio = float64(len(data)) / float64(len(compressed))

	return compressed, nil
}

func (g *GzipCompressor) Decompress(compressedData []byte) ([]byte, error) {
	reader, err := gzip.NewReader(bytes.NewReader(compressedData))
	if err != nil {
		return nil, fmt.Errorf("gzip reader creation failed: %w", err)
	}
	defer reader.Close()

	var buf bytes.Buffer
	start := time.Now()
	_, err = io.Copy(&buf, reader)
	g.decompressionSpeed = time.Since(start)

	if err != nil {
		return nil, fmt.Errorf("gzip decompression failed: %w", err)
	}

	return buf.Bytes(), nil
}

func (g *GzipCompressor) Name() string {
	return fmt.Sprintf("gzip_level_%d", g.Level)
}

func (g *GzipCompressor) CompressionRatio() float64 {
	return g.ratio
}

func (g *GzipCompressor) DecompressionSpeed() time.Duration {
	return g.decompressionSpeed
}

func (g *GzipCompressor) SupportsGPU() bool {
	return false
}

// TensorFloatCompressor implements specialized compression for floating-point tensor data
type TensorFloatCompressor struct {
	ratio              float64
	decompressionSpeed time.Duration
}

func (tf *TensorFloatCompressor) Compress(data []byte) ([]byte, error) {
	if len(data)%4 != 0 {
		return nil, fmt.Errorf("tensor float data must be aligned to 4 bytes")
	}

	// Convert to float32 array
	floats := make([]float32, len(data)/4)
	for i := 0; i < len(floats); i++ {
		floats[i] = math.Float32frombits(binary.LittleEndian.Uint32(data[i*4 : (i+1)*4]))
	}

	// Apply quantization and delta encoding
	compressed := tf.compressFloatArray(floats)

	// Apply secondary compression with zstd
	encoder, err := zstd.NewWriter(nil, zstd.WithEncoderLevel(zstd.SpeedFastest))
	if err != nil {
		return nil, fmt.Errorf("tensor float secondary compression failed: %w", err)
	}
	defer encoder.Close()

	finalCompressed := encoder.EncodeAll(compressed, make([]byte, 0, len(compressed)))
	tf.ratio = float64(len(data)) / float64(len(finalCompressed))

	return finalCompressed, nil
}

func (tf *TensorFloatCompressor) Decompress(compressedData []byte) ([]byte, error) {
	start := time.Now()

	// First decompress with zstd
	decoder, err := zstd.NewReader(nil)
	if err != nil {
		return nil, fmt.Errorf("tensor float secondary decompression failed: %w", err)
	}
	defer decoder.Close()

	intermediate, err := decoder.DecodeAll(compressedData, nil)
	if err != nil {
		return nil, fmt.Errorf("tensor float zstd decompression failed: %w", err)
	}

	// Decompress float array
	floats, err := tf.decompressFloatArray(intermediate)
	if err != nil {
		return nil, fmt.Errorf("tensor float array decompression failed: %w", err)
	}

	// Convert back to bytes
	result := make([]byte, len(floats)*4)
	for i, f := range floats {
		binary.LittleEndian.PutUint32(result[i*4:(i+1)*4], math.Float32bits(f))
	}

	tf.decompressionSpeed = time.Since(start)
	return result, nil
}

func (tf *TensorFloatCompressor) compressFloatArray(floats []float32) []byte {
	var buf bytes.Buffer

	// Write header with count
	binary.Write(&buf, binary.LittleEndian, uint32(len(floats)))

	if len(floats) == 0 {
		return buf.Bytes()
	}

	// Write first value as reference
	binary.Write(&buf, binary.LittleEndian, floats[0])

	// Delta encode subsequent values with quantization
	for i := 1; i < len(floats); i++ {
		delta := floats[i] - floats[i-1]

		// Quantize delta to reduce precision
		quantizedDelta := tf.quantizeDelta(delta)
		binary.Write(&buf, binary.LittleEndian, quantizedDelta)
	}

	return buf.Bytes()
}

func (tf *TensorFloatCompressor) decompressFloatArray(data []byte) ([]float32, error) {
	reader := bytes.NewReader(data)

	// Read count
	var count uint32
	err := binary.Read(reader, binary.LittleEndian, &count)
	if err != nil {
		return nil, fmt.Errorf("failed to read float array count: %w", err)
	}

	if count == 0 {
		return []float32{}, nil
	}

	floats := make([]float32, count)

	// Read first value
	err = binary.Read(reader, binary.LittleEndian, &floats[0])
	if err != nil {
		return nil, fmt.Errorf("failed to read first float value: %w", err)
	}

	// Read and reconstruct deltas
	for i := 1; i < int(count); i++ {
		var quantizedDelta int16
		err = binary.Read(reader, binary.LittleEndian, &quantizedDelta)
		if err != nil {
			return nil, fmt.Errorf("failed to read delta at position %d: %w", i, err)
		}

		delta := tf.dequantizeDelta(quantizedDelta)
		floats[i] = floats[i-1] + delta
	}

	return floats, nil
}

func (tf *TensorFloatCompressor) quantizeDelta(delta float32) int16 {
	// Scale and quantize to 16-bit signed integer
	scaled := delta * 1000.0 // Adjust scale factor based on expected precision
	if scaled > 32767 {
		return 32767
	}
	if scaled < -32768 {
		return -32768
	}
	return int16(scaled)
}

func (tf *TensorFloatCompressor) dequantizeDelta(quantized int16) float32 {
	return float32(quantized) / 1000.0
}

func (tf *TensorFloatCompressor) Name() string {
	return "tensor_float"
}

func (tf *TensorFloatCompressor) CompressionRatio() float64 {
	return tf.ratio
}

func (tf *TensorFloatCompressor) DecompressionSpeed() time.Duration {
	return tf.decompressionSpeed
}

func (tf *TensorFloatCompressor) SupportsGPU() bool {
	return false
}

// DeltaVarintCompressor implements delta encoding with variable-length integers
type DeltaVarintCompressor struct {
	ratio              float64
	decompressionSpeed time.Duration
}

func (dv *DeltaVarintCompressor) Compress(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return []byte{}, nil
	}

	var buf bytes.Buffer

	// Write first byte as-is
	buf.WriteByte(data[0])

	// Delta encode subsequent bytes
	for i := 1; i < len(data); i++ {
		delta := int(data[i]) - int(data[i-1])
		dv.writeVarint(&buf, delta)
	}

	compressed := buf.Bytes()
	dv.ratio = float64(len(data)) / float64(len(compressed))

	return compressed, nil
}

func (dv *DeltaVarintCompressor) Decompress(compressedData []byte) ([]byte, error) {
	start := time.Now()

	if len(compressedData) == 0 {
		dv.decompressionSpeed = time.Since(start)
		return []byte{}, nil
	}

	reader := bytes.NewReader(compressedData)
	var result []byte

	// Read first byte
	firstByte, err := reader.ReadByte()
	if err != nil {
		return nil, fmt.Errorf("failed to read first byte: %w", err)
	}
	result = append(result, firstByte)

	// Read and reconstruct deltas
	for reader.Len() > 0 {
		delta, err := dv.readVarint(reader)
		if err != nil {
			return nil, fmt.Errorf("failed to read varint delta: %w", err)
		}

		nextByte := int(result[len(result)-1]) + delta
		if nextByte < 0 || nextByte > 255 {
			return nil, fmt.Errorf("delta reconstruction resulted in invalid byte value: %d", nextByte)
		}

		result = append(result, byte(nextByte))
	}

	dv.decompressionSpeed = time.Since(start)
	return result, nil
}

func (dv *DeltaVarintCompressor) writeVarint(buf *bytes.Buffer, value int) {
	// Simple varint encoding for signed integers
	if value >= 0 {
		// Positive values: 0xxxxxxx format
		if value < 128 {
			buf.WriteByte(byte(value))
		} else {
			// Use multiple bytes for larger values
			buf.WriteByte(byte(value&0x7F | 0x80))
			dv.writeVarint(buf, value>>7)
		}
	} else {
		// Negative values: 1xxxxxxx format
		absValue := -value
		if absValue < 128 {
			buf.WriteByte(byte(absValue | 0x80))
		} else {
			buf.WriteByte(byte(absValue&0x7F | 0x80))
			dv.writeVarint(buf, -(absValue >> 7))
		}
	}
}

func (dv *DeltaVarintCompressor) readVarint(reader *bytes.Reader) (int, error) {
	b, err := reader.ReadByte()
	if err != nil {
		return 0, err
	}

	if b&0x80 == 0 {
		// Positive single byte
		return int(b), nil
	}

	// Multi-byte or negative
	value := int(b & 0x7F)
	if reader.Len() > 0 {
		next, err := dv.readVarint(reader)
		if err != nil {
			return 0, err
		}
		value |= next << 7
	}

	// Check if this was a negative number
	if b&0x80 != 0 && value > 0 {
		value = -value
	}

	return value, nil
}

func (dv *DeltaVarintCompressor) Name() string {
	return "delta_varint"
}

func (dv *DeltaVarintCompressor) CompressionRatio() float64 {
	return dv.ratio
}

func (dv *DeltaVarintCompressor) DecompressionSpeed() time.Duration {
	return dv.decompressionSpeed
}

func (dv *DeltaVarintCompressor) SupportsGPU() bool {
	return false
}

// GorillaTimeSeriesCompressor implements Gorilla-style time series compression
type GorillaTimeSeriesCompressor struct {
	ratio              float64
	decompressionSpeed time.Duration
}

func (g *GorillaTimeSeriesCompressor) Compress(data []byte) ([]byte, error) {
	// For simplicity, this is a basic implementation
	// A full Gorilla implementation would handle timestamps and values separately

	// Apply delta encoding followed by zstd compression
	deltaCompressor := &DeltaVarintCompressor{}
	deltaCompressed, err := deltaCompressor.Compress(data)
	if err != nil {
		return nil, fmt.Errorf("gorilla delta compression failed: %w", err)
	}

	// Apply zstd compression
	encoder, err := zstd.NewWriter(nil, zstd.WithEncoderLevel(zstd.SpeedFastest))
	if err != nil {
		return nil, fmt.Errorf("gorilla zstd compression failed: %w", err)
	}
	defer encoder.Close()

	compressed := encoder.EncodeAll(deltaCompressed, make([]byte, 0, len(deltaCompressed)))
	g.ratio = float64(len(data)) / float64(len(compressed))

	return compressed, nil
}

func (g *GorillaTimeSeriesCompressor) Decompress(compressedData []byte) ([]byte, error) {
	start := time.Now()

	// First decompress with zstd
	decoder, err := zstd.NewReader(nil)
	if err != nil {
		return nil, fmt.Errorf("gorilla zstd decompression failed: %w", err)
	}
	defer decoder.Close()

	deltaCompressed, err := decoder.DecodeAll(compressedData, nil)
	if err != nil {
		return nil, fmt.Errorf("gorilla zstd decode failed: %w", err)
	}

	// Decompress deltas
	deltaCompressor := &DeltaVarintCompressor{}
	result, err := deltaCompressor.Decompress(deltaCompressed)
	if err != nil {
		return nil, fmt.Errorf("gorilla delta decompression failed: %w", err)
	}

	g.decompressionSpeed = time.Since(start)
	return result, nil
}

func (g *GorillaTimeSeriesCompressor) Name() string {
	return "gorilla_ts"
}

func (g *GorillaTimeSeriesCompressor) CompressionRatio() float64 {
	return g.ratio
}

func (g *GorillaTimeSeriesCompressor) DecompressionSpeed() time.Duration {
	return g.decompressionSpeed
}

func (g *GorillaTimeSeriesCompressor) SupportsGPU() bool {
	return false
}

// SparseTensorCompressor implements compression optimized for sparse tensors
type SparseTensorCompressor struct {
	ratio              float64
	decompressionSpeed time.Duration
}

func (st *SparseTensorCompressor) Compress(data []byte) ([]byte, error) {
	if len(data)%4 != 0 {
		return nil, fmt.Errorf("sparse tensor data must be aligned to 4 bytes")
	}

	var buf bytes.Buffer

	// Write header
	binary.Write(&buf, binary.LittleEndian, uint32(len(data)/4)) // Number of float32 values

	// Compress using run-length encoding for zeros
	zeroCount := uint32(0)
	for i := 0; i < len(data); i += 4 {
		value := math.Float32frombits(binary.LittleEndian.Uint32(data[i : i+4]))

		if value == 0.0 {
			zeroCount++
		} else {
			// Write accumulated zeros if any
			if zeroCount > 0 {
				buf.WriteByte(0) // Zero marker
				binary.Write(&buf, binary.LittleEndian, zeroCount)
				zeroCount = 0
			}
			// Write non-zero value
			buf.WriteByte(1) // Non-zero marker
			binary.Write(&buf, binary.LittleEndian, value)
		}
	}

	// Write final zero count if needed
	if zeroCount > 0 {
		buf.WriteByte(0)
		binary.Write(&buf, binary.LittleEndian, zeroCount)
	}

	// Apply secondary compression
	encoder, err := zstd.NewWriter(nil, zstd.WithEncoderLevel(zstd.SpeedDefault))
	if err != nil {
		return nil, fmt.Errorf("sparse tensor secondary compression failed: %w", err)
	}
	defer encoder.Close()

	compressed := encoder.EncodeAll(buf.Bytes(), make([]byte, 0, buf.Len()))
	st.ratio = float64(len(data)) / float64(len(compressed))

	return compressed, nil
}

func (st *SparseTensorCompressor) Decompress(compressedData []byte) ([]byte, error) {
	start := time.Now()

	// First decompress with zstd
	decoder, err := zstd.NewReader(nil)
	if err != nil {
		return nil, fmt.Errorf("sparse tensor secondary decompression failed: %w", err)
	}
	defer decoder.Close()

	intermediate, err := decoder.DecodeAll(compressedData, nil)
	if err != nil {
		return nil, fmt.Errorf("sparse tensor zstd decompression failed: %w", err)
	}

	reader := bytes.NewReader(intermediate)

	// Read header
	var totalCount uint32
	err = binary.Read(reader, binary.LittleEndian, &totalCount)
	if err != nil {
		return nil, fmt.Errorf("failed to read sparse tensor header: %w", err)
	}

	result := make([]byte, totalCount*4)
	position := 0

	// Decompress run-length encoded data
	for reader.Len() > 0 && position < len(result) {
		marker, err := reader.ReadByte()
		if err != nil {
			break
		}

		if marker == 0 {
			// Zero run
			var zeroCount uint32
			err = binary.Read(reader, binary.LittleEndian, &zeroCount)
			if err != nil {
				return nil, fmt.Errorf("failed to read zero count: %w", err)
			}

			// Skip zeros (already initialized to zero)
			position += int(zeroCount) * 4
		} else {
			// Non-zero value
			var value float32
			err = binary.Read(reader, binary.LittleEndian, &value)
			if err != nil {
				return nil, fmt.Errorf("failed to read non-zero value: %w", err)
			}

			if position+4 <= len(result) {
				binary.LittleEndian.PutUint32(result[position:position+4], math.Float32bits(value))
				position += 4
			}
		}
	}

	st.decompressionSpeed = time.Since(start)
	return result, nil
}

func (st *SparseTensorCompressor) Name() string {
	return "sparse_tensor"
}

func (st *SparseTensorCompressor) CompressionRatio() float64 {
	return st.ratio
}

func (st *SparseTensorCompressor) DecompressionSpeed() time.Duration {
	return st.decompressionSpeed
}

func (st *SparseTensorCompressor) SupportsGPU() bool {
	return false
}
