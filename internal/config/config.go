// Package config provides comprehensive configuration management for NeuralMeterGo
package config

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"gopkg.in/yaml.v3"
)

// Duration is a custom type that handles JSON/YAML marshaling for time.Duration
type Duration time.Duration

// Environment variable prefix for NeuralMeterGo configuration
const EnvPrefix = "NEURALMETER_"

// Environment variable mapping structure
type EnvMapping struct {
	EnvKey     string
	ConfigPath string
	Type       string
}

// Environment variable mappings for all configuration fields
var envMappings = []EnvMapping{
	// Server configuration
	{EnvPrefix + "SERVER_HOST", "server.host", "string"},
	{EnvPrefix + "SERVER_PORT", "server.port", "int"},
	{EnvPrefix + "SERVER_READ_TIMEOUT", "server.read_timeout", "duration"},
	{EnvPrefix + "SERVER_WRITE_TIMEOUT", "server.write_timeout", "duration"},
	{EnvPrefix + "SERVER_IDLE_TIMEOUT", "server.idle_timeout", "duration"},
	{EnvPrefix + "SERVER_TLS_ENABLED", "server.tls.enabled", "bool"},
	{EnvPrefix + "SERVER_TLS_CERT_FILE", "server.tls.cert_file", "string"},
	{EnvPrefix + "SERVER_TLS_KEY_FILE", "server.tls.key_file", "string"},

	// Load test configuration
	{EnvPrefix + "LOADTEST_DEFAULT_DURATION", "load_test.default_duration", "duration"},
	{EnvPrefix + "LOADTEST_DEFAULT_CONCURRENCY", "load_test.default_concurrency", "int"},
	{EnvPrefix + "LOADTEST_MAX_CONCURRENCY", "load_test.max_concurrency", "int"},
	{EnvPrefix + "LOADTEST_RAMP_UP_DURATION", "load_test.ramp_up_duration", "duration"},
	{EnvPrefix + "LOADTEST_RAMP_DOWN_DURATION", "load_test.ramp_down_duration", "duration"},

	// Metrics configuration
	{EnvPrefix + "METRICS_ENABLED", "metrics.enabled", "bool"},
	{EnvPrefix + "METRICS_COLLECTION_INTERVAL", "metrics.collection_interval", "duration"},
	{EnvPrefix + "METRICS_BUFFER_SIZE", "metrics.buffer_size", "int"},
	{EnvPrefix + "METRICS_RETENTION_PERIOD", "metrics.retention_period", "duration"},
	{EnvPrefix + "METRICS_EXPORT_INTERVAL", "metrics.export_interval", "duration"},

	// Output configuration
	{EnvPrefix + "OUTPUT_FORMAT", "output.format", "string"},
	{EnvPrefix + "OUTPUT_FILE", "output.file", "string"},
	{EnvPrefix + "OUTPUT_CONSOLE", "output.console", "bool"},
	{EnvPrefix + "OUTPUT_VERBOSE", "output.verbose", "bool"},
	{EnvPrefix + "OUTPUT_TEMPLATES", "output.templates", "json"},
	{EnvPrefix + "OUTPUT_COMPRESSION", "output.compression", "bool"},

	// Dashboard configuration
	{EnvPrefix + "DASHBOARD_ENABLED", "dashboard.enabled", "bool"},
	{EnvPrefix + "DASHBOARD_HOST", "dashboard.host", "string"},
	{EnvPrefix + "DASHBOARD_PORT", "dashboard.port", "int"},
	{EnvPrefix + "DASHBOARD_REFRESH_RATE", "dashboard.refresh_rate", "int"},
	{EnvPrefix + "DASHBOARD_HISTORY_LIMIT", "dashboard.history_limit", "int"},

	// Worker configuration
	{EnvPrefix + "WORKER_POOL_SIZE", "worker.pool_size", "int"},
	{EnvPrefix + "WORKER_QUEUE_SIZE", "worker.queue_size", "int"},
	{EnvPrefix + "WORKER_MAX_RETRIES", "worker.max_retries", "int"},
	{EnvPrefix + "WORKER_RETRY_DELAY", "worker.retry_delay", "duration"},
	{EnvPrefix + "WORKER_SHUTDOWN_TIMEOUT", "worker.shutdown_timeout", "duration"},

	// Global configuration
	{EnvPrefix + "GLOBAL_LOG_LEVEL", "global.log_level", "string"},
	{EnvPrefix + "GLOBAL_CONFIG_DIR", "global.config_dir", "string"},
	{EnvPrefix + "GLOBAL_DATA_DIR", "global.data_dir", "string"},
	{EnvPrefix + "GLOBAL_TEMP_DIR", "global.temp_dir", "string"},
	{EnvPrefix + "GLOBAL_ENVIRONMENT", "global.environment", "string"},
	{EnvPrefix + "GLOBAL_DEBUG", "global.debug", "bool"},
	{EnvPrefix + "GLOBAL_PROFILES_ENABLED", "global.profiles_enabled", "bool"},
	{EnvPrefix + "GLOBAL_VARIABLES", "global.variables", "json"},

	// GPU configuration
	{EnvPrefix + "GPU_ENABLED", "gpu.enabled", "bool"},
	{EnvPrefix + "GPU_PREFER_CUDA", "gpu.prefer_cuda", "bool"},
	{EnvPrefix + "GPU_MIN_MEMORY_GB", "gpu.min_memory_gb", "float"},
	{EnvPrefix + "GPU_MIN_COMPUTE_MAJOR", "gpu.min_compute_capability.major", "int"},
	{EnvPrefix + "GPU_MIN_COMPUTE_MINOR", "gpu.min_compute_capability.minor", "int"},

	{EnvPrefix + "GPU_MAX_MEMORY_UTILIZATION", "gpu.max_memory_utilization", "float"},
	{EnvPrefix + "GPU_DEVICE_ID", "gpu.device_id", "int"},
	{EnvPrefix + "GPU_MONITORING_INTERVAL", "gpu.monitoring_interval", "duration"},
	{EnvPrefix + "GPU_ALLOW_FALLBACK", "gpu.allow_fallback", "bool"},
	{EnvPrefix + "GPU_PROFILE_MODE", "gpu.profile_mode", "string"},
	{EnvPrefix + "GPU_LOG_EVENTS", "gpu.log_gpu_events", "bool"},
	{EnvPrefix + "GPU_MAX_GPU_UTILIZATION", "gpu.resource_limits.max_gpu_utilization", "float"},
	{EnvPrefix + "GPU_MAX_MEMORY_USAGE", "gpu.resource_limits.max_memory_usage", "float"},
	{EnvPrefix + "GPU_MAX_POWER_CONSUMPTION", "gpu.resource_limits.max_power_consumption", "float"},
	{EnvPrefix + "GPU_THROTTLE_ON_OVERHEAT", "gpu.resource_limits.throttle_on_overheat", "bool"},
}

// UnmarshalYAML implements yaml.Unmarshaler
func (d *Duration) UnmarshalYAML(value *yaml.Node) error {
	var s string
	if err := value.Decode(&s); err != nil {
		return err
	}

	duration, err := time.ParseDuration(s)
	if err != nil {
		return fmt.Errorf("invalid duration format: %w", err)
	}

	*d = Duration(duration)
	return nil
}

// MarshalYAML implements yaml.Marshaler
func (d Duration) MarshalYAML() (interface{}, error) {
	return time.Duration(d).String(), nil
}

// UnmarshalJSON implements json.Unmarshaler
func (d *Duration) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}

	duration, err := time.ParseDuration(s)
	if err != nil {
		return fmt.Errorf("invalid duration format: %w", err)
	}

	*d = Duration(duration)
	return nil
}

// MarshalJSON implements json.Marshaler
func (d Duration) MarshalJSON() ([]byte, error) {
	return json.Marshal(time.Duration(d).String())
}

// String returns the string representation of the duration
func (d Duration) String() string {
	return time.Duration(d).String()
}

// ToDuration converts the custom Duration to time.Duration
func (d Duration) ToDuration() time.Duration {
	return time.Duration(d)
}

// Config represents the complete NeuralMeterGo configuration
type Config struct {
	// Server configuration
	Server ServerConfig `yaml:"server" json:"server" validate:"required"`

	// Load testing configuration
	LoadTest LoadTestConfig `yaml:"load_test" json:"load_test" validate:"required"`

	// Metrics configuration
	Metrics MetricsConfig `yaml:"metrics" json:"metrics" validate:"required"`

	// Output configuration
	Output OutputConfig `yaml:"output" json:"output" validate:"required"`

	// Dashboard configuration
	Dashboard DashboardConfig `yaml:"dashboard" json:"dashboard"`

	// Worker configuration
	Worker WorkerConfig `yaml:"worker" json:"worker" validate:"required"`

	// GPU configuration
	GPU GPUConfig `yaml:"gpu" json:"gpu"`

	// Global settings
	Global GlobalConfig `yaml:"global" json:"global"`
}

// ServerConfig represents server-specific configuration
type ServerConfig struct {
	Host         string    `yaml:"host" json:"host" validate:"required"`
	Port         int       `yaml:"port" json:"port" validate:"required,min=1,max=65535"`
	ReadTimeout  Duration  `yaml:"read_timeout" json:"read_timeout" validate:"required"`
	WriteTimeout Duration  `yaml:"write_timeout" json:"write_timeout" validate:"required"`
	IdleTimeout  Duration  `yaml:"idle_timeout" json:"idle_timeout"`
	TLS          TLSConfig `yaml:"tls" json:"tls"`
}

// TLSConfig represents TLS configuration
type TLSConfig struct {
	Enabled  bool   `yaml:"enabled" json:"enabled"`
	CertFile string `yaml:"cert_file" json:"cert_file"`
	KeyFile  string `yaml:"key_file" json:"key_file"`
}

// LoadTestConfig represents load testing configuration
type LoadTestConfig struct {
	DefaultDuration    Duration `yaml:"default_duration" json:"default_duration" validate:"required"`
	DefaultConcurrency int      `yaml:"default_concurrency" json:"default_concurrency" validate:"required,min=1"`
	MaxConcurrency     int      `yaml:"max_concurrency" json:"max_concurrency" validate:"required,min=1"`
	RampUpDuration     Duration `yaml:"ramp_up_duration" json:"ramp_up_duration"`
	RampDownDuration   Duration `yaml:"ramp_down_duration" json:"ramp_down_duration"`
}

// MetricsConfig represents metrics collection configuration
type MetricsConfig struct {
	Enabled            bool     `yaml:"enabled" json:"enabled"`
	CollectionInterval Duration `yaml:"collection_interval" json:"collection_interval"`
	BufferSize         int      `yaml:"buffer_size" json:"buffer_size" validate:"min=1"`
	RetentionPeriod    Duration `yaml:"retention_period" json:"retention_period"`
	ExportInterval     Duration `yaml:"export_interval" json:"export_interval"`
}

// OutputConfig represents output configuration
type OutputConfig struct {
	Format      string            `yaml:"format" json:"format" validate:"required,oneof=json yaml csv"`
	File        string            `yaml:"file" json:"file"`
	Console     bool              `yaml:"console" json:"console"`
	Verbose     bool              `yaml:"verbose" json:"verbose"`
	Templates   map[string]string `yaml:"templates" json:"templates"`
	Compression bool              `yaml:"compression" json:"compression"`
}

// DashboardConfig represents dashboard configuration
type DashboardConfig struct {
	Enabled      bool   `yaml:"enabled" json:"enabled"`
	Host         string `yaml:"host" json:"host"`
	Port         int    `yaml:"port" json:"port" validate:"omitempty,min=1024,max=65535"`
	RefreshRate  int    `yaml:"refresh_rate" json:"refresh_rate" validate:"omitempty,min=1"`
	HistoryLimit int    `yaml:"history_limit" json:"history_limit" validate:"omitempty,min=1"`
}

// WorkerConfig represents worker pool configuration
type WorkerConfig struct {
	PoolSize        int      `yaml:"pool_size" json:"pool_size" validate:"required,min=1"`
	QueueSize       int      `yaml:"queue_size" json:"queue_size" validate:"required,min=1"`
	MaxRetries      int      `yaml:"max_retries" json:"max_retries" validate:"min=0"`
	RetryDelay      Duration `yaml:"retry_delay" json:"retry_delay"`
	ShutdownTimeout Duration `yaml:"shutdown_timeout" json:"shutdown_timeout"`
}

// GlobalConfig represents global application configuration
type GlobalConfig struct {
	LogLevel        string            `yaml:"log_level" json:"log_level" validate:"oneof=debug info warn error"`
	ConfigDir       string            `yaml:"config_dir" json:"config_dir"`
	DataDir         string            `yaml:"data_dir" json:"data_dir"`
	TempDir         string            `yaml:"temp_dir" json:"temp_dir"`
	Environment     string            `yaml:"environment" json:"environment" validate:"oneof=development testing production"`
	Debug           bool              `yaml:"debug" json:"debug"`
	ProfilesEnabled bool              `yaml:"profiles_enabled" json:"profiles_enabled"`
	Variables       map[string]string `yaml:"variables" json:"variables"`
}

// ComputeCapability represents CUDA compute capability
type ComputeCapability struct {
	Major int `json:"major" yaml:"major" validate:"min=0"`
	Minor int `json:"minor" yaml:"minor" validate:"min=0"`
}

// GPUConfig represents GPU acceleration configuration
type GPUConfig struct {
	Enabled              bool              `yaml:"enabled" json:"enabled"`
	PreferCUDA           bool              `yaml:"prefer_cuda" json:"prefer_cuda"`
	MinMemoryGB          float64           `yaml:"min_memory_gb" json:"min_memory_gb" validate:"min=0"`
	MinComputeCapability ComputeCapability `yaml:"min_compute_capability" json:"min_compute_capability"`

	MaxMemoryUtilization  float64           `yaml:"max_memory_utilization" json:"max_memory_utilization" validate:"min=0,max=100"`
	DeviceID              int               `yaml:"device_id" json:"device_id" validate:"min=-1"`
	MonitoringInterval    Duration          `yaml:"monitoring_interval" json:"monitoring_interval"`
	ResourceLimits        GPUResourceLimits `yaml:"resource_limits" json:"resource_limits"`
	PerformanceThresholds GPUThresholds     `yaml:"performance_thresholds" json:"performance_thresholds"`
	AllowFallback         bool              `yaml:"allow_fallback" json:"allow_fallback"`
	ProfileMode           string            `yaml:"profile_mode" json:"profile_mode" validate:"oneof=disabled basic detailed"`
	LogGPUEvents          bool              `yaml:"log_gpu_events" json:"log_gpu_events"`
}

// GPUResourceLimits represents resource utilization limits for GPU
type GPUResourceLimits struct {
	MaxGPUUtilization    float64 `yaml:"max_gpu_utilization" json:"max_gpu_utilization" validate:"min=0,max=100"`
	MaxMemoryUsage       float64 `yaml:"max_memory_usage" json:"max_memory_usage" validate:"min=0,max=100"`
	MaxPowerConsumption  float64 `yaml:"max_power_consumption" json:"max_power_consumption" validate:"min=0"`
	ThrottleOnHighMemory bool    `yaml:"throttle_on_high_memory" json:"throttle_on_high_memory"`
	ThrottleOnPowerLimit bool    `yaml:"throttle_on_power_limit" json:"throttle_on_power_limit"`
}

// GPUThresholds represents performance warning thresholds
type GPUThresholds struct {
	WarningMemoryUsage     float64 `yaml:"warning_memory_usage" json:"warning_memory_usage" validate:"min=0,max=100"`
	WarningGPUUtilization  float64 `yaml:"warning_gpu_utilization" json:"warning_gpu_utilization" validate:"min=0,max=100"`
	CriticalMemoryUsage    float64 `yaml:"critical_memory_usage" json:"critical_memory_usage" validate:"min=0,max=100"`
	CriticalGPUUtilization float64 `yaml:"critical_gpu_utilization" json:"critical_gpu_utilization" validate:"min=0,max=100"`
}

// DefaultGPUConfig returns default GPU configuration
func DefaultGPUConfig() GPUConfig {
	return GPUConfig{
		Enabled:              true,
		PreferCUDA:           true,
		MinMemoryGB:          2.0,
		MinComputeCapability: ComputeCapability{Major: 3, Minor: 5},

		MaxMemoryUtilization: 90.0,
		DeviceID:             -1, // Auto-select
		MonitoringInterval:   Duration(time.Second * 5),
		ResourceLimits: GPUResourceLimits{
			MaxGPUUtilization:    95.0,
			MaxMemoryUsage:       90.0,
			MaxPowerConsumption:  0, // 0 means no limit
			ThrottleOnHighMemory: true,
			ThrottleOnPowerLimit: false,
		},
		PerformanceThresholds: GPUThresholds{
			WarningMemoryUsage:     80.0,
			WarningGPUUtilization:  85.0,
			CriticalMemoryUsage:    95.0,
			CriticalGPUUtilization: 98.0,
		},
		AllowFallback: true,
		ProfileMode:   "basic",
		LogGPUEvents:  true,
	}
}

// ConfigManager manages configuration loading, validation, and hot-reload
type ConfigManager struct {
	config     *Config
	configPath string
	validate   *validator.Validate
	format     ConfigFormat
	sources    []string // Multiple configuration sources for merging
}

// ConfigFormat represents supported configuration formats
type ConfigFormat int

const (
	FormatYAML ConfigFormat = iota
	FormatJSON
)

// ConfigSource represents a configuration source with priority
type ConfigSource struct {
	Path     string
	Priority int // Higher number = higher priority
	Format   ConfigFormat
}

// Environment variable substitution regex
var envVarRegex = regexp.MustCompile(`\$\{([^}]+)\}`)

// NewConfigManager creates a new configuration manager
func NewConfigManager(configPath string) (*ConfigManager, error) {
	if configPath == "" {
		return nil, fmt.Errorf("config path cannot be empty")
	}

	format, err := detectFormat(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to detect config format: %w", err)
	}

	cm := &ConfigManager{
		configPath: configPath,
		validate:   validator.New(),
		format:     format,
		sources:    []string{configPath}, // Single source by default
	}

	// Load initial configuration
	if err := cm.Load(); err != nil {
		return nil, fmt.Errorf("failed to load initial configuration: %w", err)
	}

	return cm, nil
}

// NewConfigManagerWithSources creates a new configuration manager with multiple sources
func NewConfigManagerWithSources(sources []ConfigSource) (*ConfigManager, error) {
	if len(sources) == 0 {
		return nil, fmt.Errorf("at least one configuration source is required")
	}

	cm := &ConfigManager{
		validate: validator.New(),
		sources:  make([]string, len(sources)),
	}

	// Sort sources by priority (ascending order, so higher priority overwrites)
	sortedSources := make([]ConfigSource, len(sources))
	copy(sortedSources, sources)

	// Simple bubble sort by priority
	for i := 0; i < len(sortedSources)-1; i++ {
		for j := 0; j < len(sortedSources)-i-1; j++ {
			if sortedSources[j].Priority > sortedSources[j+1].Priority {
				sortedSources[j], sortedSources[j+1] = sortedSources[j+1], sortedSources[j]
			}
		}
	}

	// Store sorted source paths
	for i, source := range sortedSources {
		cm.sources[i] = source.Path
	}

	// Use the highest priority source as the primary
	cm.configPath = sortedSources[len(sortedSources)-1].Path
	cm.format = sortedSources[len(sortedSources)-1].Format

	// Load initial configuration with merging
	if err := cm.LoadWithMerging(); err != nil {
		return nil, fmt.Errorf("failed to load initial configuration: %w", err)
	}

	return cm, nil
}

// Load loads and validates the configuration from file
func (cm *ConfigManager) Load() error {
	// Read configuration file
	data, err := ioutil.ReadFile(cm.configPath)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	// Substitute environment variables
	data = cm.substituteEnvVars(data)

	// Parse configuration based on format
	var config Config
	switch cm.format {
	case FormatYAML:
		if err := yaml.Unmarshal(data, &config); err != nil {
			return fmt.Errorf("failed to parse YAML config: %w", err)
		}
	case FormatJSON:
		if err := json.Unmarshal(data, &config); err != nil {
			return fmt.Errorf("failed to parse JSON config: %w", err)
		}
	default:
		return fmt.Errorf("unsupported config format")
	}

	// Set defaults
	cm.setDefaults(&config)

	// Validate configuration
	if err := cm.validate.Struct(&config); err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}

	// Custom validation
	if err := cm.customValidation(&config); err != nil {
		return fmt.Errorf("custom validation failed: %w", err)
	}

	cm.config = &config
	return nil
}

// LoadWithMerging loads and merges configurations from multiple sources
func (cm *ConfigManager) LoadWithMerging() error {
	var mergedConfig Config

	// Load and merge configurations in priority order
	for _, sourcePath := range cm.sources {
		// Detect format for this source
		format, err := detectFormat(sourcePath)
		if err != nil {
			return fmt.Errorf("failed to detect format for %s: %w", sourcePath, err)
		}

		// Read configuration file
		data, err := ioutil.ReadFile(sourcePath)
		if err != nil {
			// Skip missing files for merging (they're optional)
			continue
		}

		// Substitute environment variables
		data = cm.substituteEnvVars(data)

		// Parse configuration based on format
		var sourceConfig Config
		switch format {
		case FormatYAML:
			if err := yaml.Unmarshal(data, &sourceConfig); err != nil {
				return fmt.Errorf("failed to parse YAML config from %s: %w", sourcePath, err)
			}
		case FormatJSON:
			if err := json.Unmarshal(data, &sourceConfig); err != nil {
				return fmt.Errorf("failed to parse JSON config from %s: %w", sourcePath, err)
			}
		default:
			return fmt.Errorf("unsupported config format for %s", sourcePath)
		}

		// Merge this configuration into the merged config
		mergedConfig = cm.mergeConfigs(mergedConfig, sourceConfig)
	}

	// Set defaults on merged configuration
	cm.setDefaults(&mergedConfig)

	// Validate merged configuration
	if err := cm.validate.Struct(&mergedConfig); err != nil {
		return fmt.Errorf("merged configuration validation failed: %w", err)
	}

	// Custom validation
	if err := cm.customValidation(&mergedConfig); err != nil {
		return fmt.Errorf("merged configuration custom validation failed: %w", err)
	}

	cm.config = &mergedConfig
	return nil
}

// mergeConfigs merges two configurations with the second taking precedence
func (cm *ConfigManager) mergeConfigs(base, override Config) Config {
	result := base

	// Server configuration merging
	if override.Server.Host != "" {
		result.Server.Host = override.Server.Host
	}
	if override.Server.Port != 0 {
		result.Server.Port = override.Server.Port
	}
	if override.Server.ReadTimeout != 0 {
		result.Server.ReadTimeout = override.Server.ReadTimeout
	}
	if override.Server.WriteTimeout != 0 {
		result.Server.WriteTimeout = override.Server.WriteTimeout
	}
	if override.Server.IdleTimeout != 0 {
		result.Server.IdleTimeout = override.Server.IdleTimeout
	}
	// TLS configuration merging
	if override.Server.TLS.Enabled != base.Server.TLS.Enabled {
		result.Server.TLS.Enabled = override.Server.TLS.Enabled
	}
	if override.Server.TLS.CertFile != "" {
		result.Server.TLS.CertFile = override.Server.TLS.CertFile
	}
	if override.Server.TLS.KeyFile != "" {
		result.Server.TLS.KeyFile = override.Server.TLS.KeyFile
	}

	// Load test configuration merging
	if override.LoadTest.DefaultDuration != 0 {
		result.LoadTest.DefaultDuration = override.LoadTest.DefaultDuration
	}
	if override.LoadTest.DefaultConcurrency != 0 {
		result.LoadTest.DefaultConcurrency = override.LoadTest.DefaultConcurrency
	}
	if override.LoadTest.MaxConcurrency != 0 {
		result.LoadTest.MaxConcurrency = override.LoadTest.MaxConcurrency
	}
	if override.LoadTest.RampUpDuration != 0 {
		result.LoadTest.RampUpDuration = override.LoadTest.RampUpDuration
	}
	if override.LoadTest.RampDownDuration != 0 {
		result.LoadTest.RampDownDuration = override.LoadTest.RampDownDuration
	}

	// Metrics configuration merging
	if override.Metrics.Enabled != base.Metrics.Enabled {
		result.Metrics.Enabled = override.Metrics.Enabled
	}
	if override.Metrics.CollectionInterval != 0 {
		result.Metrics.CollectionInterval = override.Metrics.CollectionInterval
	}
	if override.Metrics.BufferSize != 0 {
		result.Metrics.BufferSize = override.Metrics.BufferSize
	}
	if override.Metrics.RetentionPeriod != 0 {
		result.Metrics.RetentionPeriod = override.Metrics.RetentionPeriod
	}
	if override.Metrics.ExportInterval != 0 {
		result.Metrics.ExportInterval = override.Metrics.ExportInterval
	}

	// Output configuration merging
	if override.Output.Format != "" {
		result.Output.Format = override.Output.Format
	}
	if override.Output.File != "" {
		result.Output.File = override.Output.File
	}
	if override.Output.Console != base.Output.Console {
		result.Output.Console = override.Output.Console
	}
	if override.Output.Verbose != base.Output.Verbose {
		result.Output.Verbose = override.Output.Verbose
	}
	if override.Output.Compression != base.Output.Compression {
		result.Output.Compression = override.Output.Compression
	}
	// Merge templates map
	if result.Output.Templates == nil {
		result.Output.Templates = make(map[string]string)
	}
	for k, v := range override.Output.Templates {
		result.Output.Templates[k] = v
	}

	// Dashboard configuration merging
	if override.Dashboard.Enabled != base.Dashboard.Enabled {
		result.Dashboard.Enabled = override.Dashboard.Enabled
	}
	if override.Dashboard.Host != "" {
		result.Dashboard.Host = override.Dashboard.Host
	}
	if override.Dashboard.Port != 0 {
		result.Dashboard.Port = override.Dashboard.Port
	}
	if override.Dashboard.RefreshRate != 0 {
		result.Dashboard.RefreshRate = override.Dashboard.RefreshRate
	}
	if override.Dashboard.HistoryLimit != 0 {
		result.Dashboard.HistoryLimit = override.Dashboard.HistoryLimit
	}

	// Worker configuration merging
	if override.Worker.PoolSize != 0 {
		result.Worker.PoolSize = override.Worker.PoolSize
	}
	if override.Worker.QueueSize != 0 {
		result.Worker.QueueSize = override.Worker.QueueSize
	}
	if override.Worker.MaxRetries != 0 {
		result.Worker.MaxRetries = override.Worker.MaxRetries
	}
	if override.Worker.RetryDelay != 0 {
		result.Worker.RetryDelay = override.Worker.RetryDelay
	}
	if override.Worker.ShutdownTimeout != 0 {
		result.Worker.ShutdownTimeout = override.Worker.ShutdownTimeout
	}

	// Global configuration merging
	if override.Global.LogLevel != "" {
		result.Global.LogLevel = override.Global.LogLevel
	}
	if override.Global.ConfigDir != "" {
		result.Global.ConfigDir = override.Global.ConfigDir
	}
	if override.Global.DataDir != "" {
		result.Global.DataDir = override.Global.DataDir
	}
	if override.Global.TempDir != "" {
		result.Global.TempDir = override.Global.TempDir
	}
	if override.Global.Environment != "" {
		result.Global.Environment = override.Global.Environment
	}
	if override.Global.Debug != base.Global.Debug {
		result.Global.Debug = override.Global.Debug
	}
	if override.Global.ProfilesEnabled != base.Global.ProfilesEnabled {
		result.Global.ProfilesEnabled = override.Global.ProfilesEnabled
	}
	// Merge variables map
	if result.Global.Variables == nil {
		result.Global.Variables = make(map[string]string)
	}
	for k, v := range override.Global.Variables {
		result.Global.Variables[k] = v
	}

	return result
}

// AddSource adds a new configuration source for merging
func (cm *ConfigManager) AddSource(sourcePath string, priority int) error {
	// Validate source exists
	if _, err := os.Stat(sourcePath); os.IsNotExist(err) {
		return fmt.Errorf("configuration source does not exist: %s", sourcePath)
	}

	// Add source to list (will be sorted by priority on next load)
	cm.sources = append(cm.sources, sourcePath)
	return nil
}

// Get returns the current configuration
func (cm *ConfigManager) Get() *Config {
	if cm.config == nil {
		return nil
	}

	// Return a copy to prevent external modification
	configCopy := *cm.config
	return &configCopy
}

// Reload reloads the configuration from file(s)
func (cm *ConfigManager) Reload() error {
	if len(cm.sources) > 1 {
		return cm.LoadWithMerging()
	}
	return cm.Load()
}

// ReloadWithCallback reloads configuration and calls callback if successful
func (cm *ConfigManager) ReloadWithCallback(callback func(*Config) error) error {
	// Store current config for rollback
	oldConfig := cm.config

	// Attempt reload
	if err := cm.Reload(); err != nil {
		return fmt.Errorf("failed to reload configuration: %w", err)
	}

	// Call callback with new configuration
	if callback != nil {
		if err := callback(cm.config); err != nil {
			// Rollback to old configuration
			cm.config = oldConfig
			return fmt.Errorf("configuration callback failed, rolled back: %w", err)
		}
	}

	return nil
}

// Watch starts watching configuration files for changes (simplified implementation)
func (cm *ConfigManager) Watch(callback func(*Config) error) error {
	// This is a simplified implementation
	// In a production system, you would use a file watcher like fsnotify
	return fmt.Errorf("file watching not implemented - use periodic Reload() calls")
}

// Validate validates the current configuration
func (cm *ConfigManager) Validate() error {
	if cm.config == nil {
		return fmt.Errorf("no configuration loaded")
	}
	return cm.validate.Struct(cm.config)
}

// detectFormat detects the configuration format based on file extension
func detectFormat(configPath string) (ConfigFormat, error) {
	ext := strings.ToLower(filepath.Ext(configPath))
	switch ext {
	case ".yaml", ".yml":
		return FormatYAML, nil
	case ".json":
		return FormatJSON, nil
	default:
		return FormatYAML, fmt.Errorf("unsupported config format: %s", ext)
	}
}

// substituteEnvVars substitutes environment variables in configuration data
func (cm *ConfigManager) substituteEnvVars(data []byte) []byte {
	return envVarRegex.ReplaceAllFunc(data, func(match []byte) []byte {
		// Extract variable name (remove ${ and })
		envVar := string(match[2 : len(match)-1])

		// Handle default values (e.g., ${VAR:default})
		parts := strings.SplitN(envVar, ":", 2)
		varName := parts[0]
		defaultValue := ""
		if len(parts) > 1 {
			defaultValue = parts[1]
		}

		// Get environment variable value
		envValue := os.Getenv(varName)
		if envValue == "" {
			if defaultValue != "" {
				return []byte(defaultValue)
			}
			// Return original if no default and not set
			return match
		}

		return []byte(envValue)
	})
}

// setDefaults sets default values for configuration fields
func (cm *ConfigManager) setDefaults(config *Config) {
	// Server defaults
	if config.Server.Host == "" {
		config.Server.Host = "localhost"
	}
	if config.Server.Port == 0 {
		config.Server.Port = 8080
	}
	if config.Server.ReadTimeout == 0 {
		config.Server.ReadTimeout = Duration(30 * time.Second)
	}
	if config.Server.WriteTimeout == 0 {
		config.Server.WriteTimeout = Duration(30 * time.Second)
	}
	if config.Server.IdleTimeout == 0 {
		config.Server.IdleTimeout = Duration(60 * time.Second)
	}

	// Load test defaults
	if config.LoadTest.DefaultDuration == 0 {
		config.LoadTest.DefaultDuration = Duration(60 * time.Second)
	}
	if config.LoadTest.DefaultConcurrency == 0 {
		config.LoadTest.DefaultConcurrency = 10
	}
	if config.LoadTest.MaxConcurrency == 0 {
		config.LoadTest.MaxConcurrency = 1000
	}
	if config.LoadTest.RampUpDuration == 0 {
		config.LoadTest.RampUpDuration = Duration(10 * time.Second)
	}
	if config.LoadTest.RampDownDuration == 0 {
		config.LoadTest.RampDownDuration = Duration(10 * time.Second)
	}

	// Metrics defaults
	if config.Metrics.CollectionInterval == 0 {
		config.Metrics.CollectionInterval = Duration(1 * time.Second)
	}
	if config.Metrics.BufferSize == 0 {
		config.Metrics.BufferSize = 1000
	}
	if config.Metrics.RetentionPeriod == 0 {
		config.Metrics.RetentionPeriod = Duration(24 * time.Hour)
	}
	if config.Metrics.ExportInterval == 0 {
		config.Metrics.ExportInterval = Duration(10 * time.Second)
	}

	// Output defaults
	if config.Output.Format == "" {
		config.Output.Format = "json"
	}
	if config.Output.Templates == nil {
		config.Output.Templates = make(map[string]string)
	}

	// Dashboard defaults
	if config.Dashboard.Enabled && config.Dashboard.Host == "" {
		config.Dashboard.Host = "localhost"
	}
	if config.Dashboard.Enabled && config.Dashboard.Port == 0 {
		config.Dashboard.Port = 8081
	}
	if config.Dashboard.RefreshRate == 0 {
		config.Dashboard.RefreshRate = 1
	}
	if config.Dashboard.HistoryLimit == 0 {
		config.Dashboard.HistoryLimit = 1000
	}

	// Worker defaults
	if config.Worker.PoolSize == 0 {
		config.Worker.PoolSize = 10
	}
	if config.Worker.QueueSize == 0 {
		config.Worker.QueueSize = 100
	}
	if config.Worker.RetryDelay == 0 {
		config.Worker.RetryDelay = Duration(1 * time.Second)
	}
	if config.Worker.ShutdownTimeout == 0 {
		config.Worker.ShutdownTimeout = Duration(30 * time.Second)
	}

	// GPU defaults - set missing values from DefaultGPUConfig
	gpuDefaults := DefaultGPUConfig()

	// Only set defaults for fields that are currently zero values
	if config.GPU.MinMemoryGB == 0 {
		config.GPU.MinMemoryGB = gpuDefaults.MinMemoryGB
	}
	if config.GPU.MinComputeCapability.Major == 0 && config.GPU.MinComputeCapability.Minor == 0 {
		config.GPU.MinComputeCapability = gpuDefaults.MinComputeCapability
	}

	if config.GPU.MaxMemoryUtilization == 0 {
		config.GPU.MaxMemoryUtilization = gpuDefaults.MaxMemoryUtilization
	}
	if config.GPU.DeviceID == 0 {
		config.GPU.DeviceID = gpuDefaults.DeviceID
	}
	if config.GPU.MonitoringInterval == 0 {
		config.GPU.MonitoringInterval = gpuDefaults.MonitoringInterval
	}
	if config.GPU.ProfileMode == "" {
		config.GPU.ProfileMode = gpuDefaults.ProfileMode
	}

	// Set nested defaults for ResourceLimits
	if config.GPU.ResourceLimits.MaxGPUUtilization == 0 {
		config.GPU.ResourceLimits.MaxGPUUtilization = gpuDefaults.ResourceLimits.MaxGPUUtilization
	}
	if config.GPU.ResourceLimits.MaxMemoryUsage == 0 {
		config.GPU.ResourceLimits.MaxMemoryUsage = gpuDefaults.ResourceLimits.MaxMemoryUsage
	}
	if config.GPU.ResourceLimits.MaxPowerConsumption == 0 {
		config.GPU.ResourceLimits.MaxPowerConsumption = gpuDefaults.ResourceLimits.MaxPowerConsumption
	}

	// Set nested defaults for PerformanceThresholds
	if config.GPU.PerformanceThresholds.WarningMemoryUsage == 0 {
		config.GPU.PerformanceThresholds.WarningMemoryUsage = gpuDefaults.PerformanceThresholds.WarningMemoryUsage
	}
	if config.GPU.PerformanceThresholds.WarningGPUUtilization == 0 {
		config.GPU.PerformanceThresholds.WarningGPUUtilization = gpuDefaults.PerformanceThresholds.WarningGPUUtilization
	}

	if config.GPU.PerformanceThresholds.CriticalMemoryUsage == 0 {
		config.GPU.PerformanceThresholds.CriticalMemoryUsage = gpuDefaults.PerformanceThresholds.CriticalMemoryUsage
	}
	if config.GPU.PerformanceThresholds.CriticalGPUUtilization == 0 {
		config.GPU.PerformanceThresholds.CriticalGPUUtilization = gpuDefaults.PerformanceThresholds.CriticalGPUUtilization
	}

	// Global defaults
	if config.Global.LogLevel == "" {
		config.Global.LogLevel = "info"
	}
	if config.Global.Environment == "" {
		config.Global.Environment = "development"
	}
	if config.Global.ConfigDir == "" {
		config.Global.ConfigDir = "./config"
	}
	if config.Global.DataDir == "" {
		config.Global.DataDir = "./data"
	}
	if config.Global.TempDir == "" {
		config.Global.TempDir = os.TempDir()
	}
	if config.Global.Variables == nil {
		config.Global.Variables = make(map[string]string)
	}
}

// customValidation performs custom validation logic (basic checks for config loading)
func (cm *ConfigManager) customValidation(config *Config) error {
	// Only validate critical TLS configuration that would prevent loading
	if config.Server.TLS.Enabled {
		if config.Server.TLS.CertFile == "" || config.Server.TLS.KeyFile == "" {
			return fmt.Errorf("TLS enabled but cert_file or key_file not specified")
		}
	}

	// Validate directories exist or can be created (only essential ones)
	dirs := []string{config.Global.ConfigDir, config.Global.DataDir}
	for _, dir := range dirs {
		if dir != "" {
			if err := os.MkdirAll(dir, 0755); err != nil {
				return fmt.Errorf("failed to create directory %s: %w", dir, err)
			}
		}
	}

	return nil
}

// strictCustomValidation performs strict validation logic (used by enhanced validation)
func (cm *ConfigManager) strictCustomValidation(config *Config) error {
	// Validate port conflicts
	if config.Dashboard.Enabled && config.Server.Port == config.Dashboard.Port {
		return fmt.Errorf("server and dashboard cannot use the same port")
	}

	// Validate TLS configuration with file existence
	if config.Server.TLS.Enabled {
		if config.Server.TLS.CertFile == "" || config.Server.TLS.KeyFile == "" {
			return fmt.Errorf("TLS enabled but cert_file or key_file not specified")
		}

		// Check if files exist
		if _, err := os.Stat(config.Server.TLS.CertFile); os.IsNotExist(err) {
			return fmt.Errorf("TLS cert file does not exist: %s", config.Server.TLS.CertFile)
		}
		if _, err := os.Stat(config.Server.TLS.KeyFile); os.IsNotExist(err) {
			return fmt.Errorf("TLS key file does not exist: %s", config.Server.TLS.KeyFile)
		}
	}

	// Validate load test configuration
	if config.LoadTest.DefaultConcurrency > config.LoadTest.MaxConcurrency {
		return fmt.Errorf("default_concurrency cannot exceed max_concurrency")
	}

	// Validate worker configuration
	if config.Worker.PoolSize > config.Worker.QueueSize {
		return fmt.Errorf("worker pool_size should not exceed queue_size")
	}

	return nil
}

// LoadFromFile is a convenience function to load configuration from a file
func LoadFromFile(configPath string) (*Config, error) {
	cm, err := NewConfigManager(configPath)
	if err != nil {
		return nil, err
	}
	return cm.Get(), nil
}

// LoadFromBytes loads configuration from byte slice
func LoadFromBytes(data []byte, format ConfigFormat) (*Config, error) {
	var config Config

	switch format {
	case FormatYAML:
		if err := yaml.Unmarshal(data, &config); err != nil {
			return nil, fmt.Errorf("failed to parse YAML config: %w", err)
		}
	case FormatJSON:
		if err := json.Unmarshal(data, &config); err != nil {
			return nil, fmt.Errorf("failed to parse JSON config: %w", err)
		}
	default:
		return nil, fmt.Errorf("unsupported config format")
	}

	// Create temporary config manager for validation
	cm := &ConfigManager{
		validate: validator.New(),
		format:   format,
	}

	cm.setDefaults(&config)

	if err := cm.validate.Struct(&config); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	if err := cm.customValidation(&config); err != nil {
		return nil, fmt.Errorf("custom validation failed: %w", err)
	}

	return &config, nil
}

// LoadFromEnvironment loads configuration values from environment variables
// This provides direct environment variable mapping with type conversion
func (cm *ConfigManager) LoadFromEnvironment() error {
	if cm.config == nil {
		return fmt.Errorf("config not initialized")
	}

	for _, mapping := range envMappings {
		envValue := os.Getenv(mapping.EnvKey)
		if envValue == "" {
			continue // Skip if environment variable is not set
		}

		if err := cm.setConfigValue(mapping.ConfigPath, envValue, mapping.Type); err != nil {
			return fmt.Errorf("failed to set config value from %s: %w", mapping.EnvKey, err)
		}
	}

	return nil
}

// LoadWithEnvironmentPrecedence loads configuration with proper precedence:
// 1. Environment variables (highest priority)
// 2. Configuration file values
// 3. Default values (lowest priority)
func (cm *ConfigManager) LoadWithEnvironmentPrecedence() error {
	// First load from file (if exists) or set defaults
	if cm.configPath != "" {
		if err := cm.Load(); err != nil {
			return fmt.Errorf("failed to load base configuration: %w", err)
		}
	} else {
		// Initialize with empty config and set defaults
		cm.config = &Config{}
		cm.setDefaults(cm.config)
	}

	// Then override with environment variables
	if err := cm.LoadFromEnvironment(); err != nil {
		return fmt.Errorf("failed to load environment variables: %w", err)
	}

	// Finally validate the merged configuration
	if err := cm.Validate(); err != nil {
		return fmt.Errorf("configuration validation failed after environment loading: %w", err)
	}

	return nil
}

// setConfigValue sets a configuration value using dot notation path
func (cm *ConfigManager) setConfigValue(path, value, valueType string) error {
	if cm.config == nil {
		return fmt.Errorf("config not initialized")
	}

	// Convert value based on type
	convertedValue, err := cm.convertValue(value, valueType)
	if err != nil {
		return fmt.Errorf("failed to convert value '%s' to type '%s': %w", value, valueType, err)
	}

	// Set the value using reflection-like approach
	return cm.setValueByPath(path, convertedValue)
}

// convertValue converts string environment variable value to the appropriate type
func (cm *ConfigManager) convertValue(value, valueType string) (interface{}, error) {
	switch valueType {
	case "string":
		return value, nil
	case "int":
		return strconv.Atoi(value)
	case "bool":
		return strconv.ParseBool(value)
	case "duration":
		duration, err := time.ParseDuration(value)
		if err != nil {
			return nil, err
		}
		return Duration(duration), nil
	case "json":
		var result interface{}
		if err := json.Unmarshal([]byte(value), &result); err != nil {
			return nil, fmt.Errorf("invalid JSON: %w", err)
		}
		return result, nil
	default:
		return nil, fmt.Errorf("unsupported value type: %s", valueType)
	}
}

// setValueByPath sets a configuration value using dot notation path
func (cm *ConfigManager) setValueByPath(path string, value interface{}) error {
	parts := strings.Split(path, ".")
	if len(parts) < 2 {
		return fmt.Errorf("invalid config path: %s", path)
	}

	switch parts[0] {
	case "server":
		return cm.setServerValue(parts[1:], value)
	case "load_test":
		return cm.setLoadTestValue(parts[1:], value)
	case "metrics":
		return cm.setMetricsValue(parts[1:], value)
	case "output":
		return cm.setOutputValue(parts[1:], value)
	case "dashboard":
		return cm.setDashboardValue(parts[1:], value)
	case "worker":
		return cm.setWorkerValue(parts[1:], value)
	case "gpu":
		return cm.setGPUValue(parts[1:], value)
	case "global":
		return cm.setGlobalValue(parts[1:], value)
	default:
		return fmt.Errorf("unknown config section: %s", parts[0])
	}
}

// setServerValue sets server configuration values
func (cm *ConfigManager) setServerValue(path []string, value interface{}) error {
	if len(path) == 0 {
		return fmt.Errorf("empty server config path")
	}

	switch path[0] {
	case "host":
		if v, ok := value.(string); ok {
			cm.config.Server.Host = v
		} else {
			return fmt.Errorf("server.host must be string")
		}
	case "port":
		if v, ok := value.(int); ok {
			cm.config.Server.Port = v
		} else {
			return fmt.Errorf("server.port must be int")
		}
	case "read_timeout":
		if v, ok := value.(Duration); ok {
			cm.config.Server.ReadTimeout = v
		} else {
			return fmt.Errorf("server.read_timeout must be duration")
		}
	case "write_timeout":
		if v, ok := value.(Duration); ok {
			cm.config.Server.WriteTimeout = v
		} else {
			return fmt.Errorf("server.write_timeout must be duration")
		}
	case "idle_timeout":
		if v, ok := value.(Duration); ok {
			cm.config.Server.IdleTimeout = v
		} else {
			return fmt.Errorf("server.idle_timeout must be duration")
		}
	case "tls":
		return cm.setTLSValue(path[1:], value)
	default:
		return fmt.Errorf("unknown server config field: %s", path[0])
	}
	return nil
}

// setTLSValue sets TLS configuration values
func (cm *ConfigManager) setTLSValue(path []string, value interface{}) error {
	if len(path) == 0 {
		return fmt.Errorf("empty TLS config path")
	}

	switch path[0] {
	case "enabled":
		if v, ok := value.(bool); ok {
			cm.config.Server.TLS.Enabled = v
		} else {
			return fmt.Errorf("server.tls.enabled must be bool")
		}
	case "cert_file":
		if v, ok := value.(string); ok {
			cm.config.Server.TLS.CertFile = v
		} else {
			return fmt.Errorf("server.tls.cert_file must be string")
		}
	case "key_file":
		if v, ok := value.(string); ok {
			cm.config.Server.TLS.KeyFile = v
		} else {
			return fmt.Errorf("server.tls.key_file must be string")
		}
	default:
		return fmt.Errorf("unknown TLS config field: %s", path[0])
	}
	return nil
}

// setLoadTestValue sets load test configuration values
func (cm *ConfigManager) setLoadTestValue(path []string, value interface{}) error {
	if len(path) == 0 {
		return fmt.Errorf("empty load test config path")
	}

	switch path[0] {
	case "default_duration":
		if v, ok := value.(Duration); ok {
			cm.config.LoadTest.DefaultDuration = v
		} else {
			return fmt.Errorf("load_test.default_duration must be duration")
		}
	case "default_concurrency":
		if v, ok := value.(int); ok {
			cm.config.LoadTest.DefaultConcurrency = v
		} else {
			return fmt.Errorf("load_test.default_concurrency must be int")
		}
	case "max_concurrency":
		if v, ok := value.(int); ok {
			cm.config.LoadTest.MaxConcurrency = v
		} else {
			return fmt.Errorf("load_test.max_concurrency must be int")
		}
	case "ramp_up_duration":
		if v, ok := value.(Duration); ok {
			cm.config.LoadTest.RampUpDuration = v
		} else {
			return fmt.Errorf("load_test.ramp_up_duration must be duration")
		}
	case "ramp_down_duration":
		if v, ok := value.(Duration); ok {
			cm.config.LoadTest.RampDownDuration = v
		} else {
			return fmt.Errorf("load_test.ramp_down_duration must be duration")
		}
	default:
		return fmt.Errorf("unknown load test config field: %s", path[0])
	}
	return nil
}

// setMetricsValue sets metrics configuration values
func (cm *ConfigManager) setMetricsValue(path []string, value interface{}) error {
	if len(path) == 0 {
		return fmt.Errorf("empty metrics config path")
	}

	switch path[0] {
	case "enabled":
		if v, ok := value.(bool); ok {
			cm.config.Metrics.Enabled = v
		} else {
			return fmt.Errorf("metrics.enabled must be bool")
		}
	case "collection_interval":
		if v, ok := value.(Duration); ok {
			cm.config.Metrics.CollectionInterval = v
		} else {
			return fmt.Errorf("metrics.collection_interval must be duration")
		}
	case "buffer_size":
		if v, ok := value.(int); ok {
			cm.config.Metrics.BufferSize = v
		} else {
			return fmt.Errorf("metrics.buffer_size must be int")
		}
	case "retention_period":
		if v, ok := value.(Duration); ok {
			cm.config.Metrics.RetentionPeriod = v
		} else {
			return fmt.Errorf("metrics.retention_period must be duration")
		}
	case "export_interval":
		if v, ok := value.(Duration); ok {
			cm.config.Metrics.ExportInterval = v
		} else {
			return fmt.Errorf("metrics.export_interval must be duration")
		}
	default:
		return fmt.Errorf("unknown metrics config field: %s", path[0])
	}
	return nil
}

// setOutputValue sets output configuration values
func (cm *ConfigManager) setOutputValue(path []string, value interface{}) error {
	if len(path) == 0 {
		return fmt.Errorf("empty output config path")
	}

	switch path[0] {
	case "format":
		if v, ok := value.(string); ok {
			cm.config.Output.Format = v
		} else {
			return fmt.Errorf("output.format must be string")
		}
	case "file":
		if v, ok := value.(string); ok {
			cm.config.Output.File = v
		} else {
			return fmt.Errorf("output.file must be string")
		}
	case "console":
		if v, ok := value.(bool); ok {
			cm.config.Output.Console = v
		} else {
			return fmt.Errorf("output.console must be bool")
		}
	case "verbose":
		if v, ok := value.(bool); ok {
			cm.config.Output.Verbose = v
		} else {
			return fmt.Errorf("output.verbose must be bool")
		}
	case "templates":
		if v, ok := value.(map[string]interface{}); ok {
			templates := make(map[string]string)
			for k, val := range v {
				if strVal, ok := val.(string); ok {
					templates[k] = strVal
				} else {
					return fmt.Errorf("output.templates values must be strings")
				}
			}
			cm.config.Output.Templates = templates
		} else {
			return fmt.Errorf("output.templates must be JSON object")
		}
	case "compression":
		if v, ok := value.(bool); ok {
			cm.config.Output.Compression = v
		} else {
			return fmt.Errorf("output.compression must be bool")
		}
	default:
		return fmt.Errorf("unknown output config field: %s", path[0])
	}
	return nil
}

// setDashboardValue sets dashboard configuration values
func (cm *ConfigManager) setDashboardValue(path []string, value interface{}) error {
	if len(path) == 0 {
		return fmt.Errorf("empty dashboard config path")
	}

	switch path[0] {
	case "enabled":
		if v, ok := value.(bool); ok {
			cm.config.Dashboard.Enabled = v
		} else {
			return fmt.Errorf("dashboard.enabled must be bool")
		}
	case "host":
		if v, ok := value.(string); ok {
			cm.config.Dashboard.Host = v
		} else {
			return fmt.Errorf("dashboard.host must be string")
		}
	case "port":
		if v, ok := value.(int); ok {
			cm.config.Dashboard.Port = v
		} else {
			return fmt.Errorf("dashboard.port must be int")
		}
	case "refresh_rate":
		if v, ok := value.(int); ok {
			cm.config.Dashboard.RefreshRate = v
		} else {
			return fmt.Errorf("dashboard.refresh_rate must be int")
		}
	case "history_limit":
		if v, ok := value.(int); ok {
			cm.config.Dashboard.HistoryLimit = v
		} else {
			return fmt.Errorf("dashboard.history_limit must be int")
		}
	default:
		return fmt.Errorf("unknown dashboard config field: %s", path[0])
	}
	return nil
}

// setWorkerValue sets worker configuration values
func (cm *ConfigManager) setWorkerValue(path []string, value interface{}) error {
	if len(path) == 0 {
		return fmt.Errorf("empty worker config path")
	}

	switch path[0] {
	case "pool_size":
		if v, ok := value.(int); ok {
			cm.config.Worker.PoolSize = v
		} else {
			return fmt.Errorf("worker.pool_size must be int")
		}
	case "queue_size":
		if v, ok := value.(int); ok {
			cm.config.Worker.QueueSize = v
		} else {
			return fmt.Errorf("worker.queue_size must be int")
		}
	case "max_retries":
		if v, ok := value.(int); ok {
			cm.config.Worker.MaxRetries = v
		} else {
			return fmt.Errorf("worker.max_retries must be int")
		}
	case "retry_delay":
		if v, ok := value.(Duration); ok {
			cm.config.Worker.RetryDelay = v
		} else {
			return fmt.Errorf("worker.retry_delay must be duration")
		}
	case "shutdown_timeout":
		if v, ok := value.(Duration); ok {
			cm.config.Worker.ShutdownTimeout = v
		} else {
			return fmt.Errorf("worker.shutdown_timeout must be duration")
		}
	default:
		return fmt.Errorf("unknown worker config field: %s", path[0])
	}
	return nil
}

// setGlobalValue sets global configuration values
func (cm *ConfigManager) setGlobalValue(path []string, value interface{}) error {
	if len(path) == 0 {
		return fmt.Errorf("empty global config path")
	}

	switch path[0] {
	case "log_level":
		if v, ok := value.(string); ok {
			cm.config.Global.LogLevel = v
		} else {
			return fmt.Errorf("global.log_level must be string")
		}
	case "config_dir":
		if v, ok := value.(string); ok {
			cm.config.Global.ConfigDir = v
		} else {
			return fmt.Errorf("global.config_dir must be string")
		}
	case "data_dir":
		if v, ok := value.(string); ok {
			cm.config.Global.DataDir = v
		} else {
			return fmt.Errorf("global.data_dir must be string")
		}
	case "temp_dir":
		if v, ok := value.(string); ok {
			cm.config.Global.TempDir = v
		} else {
			return fmt.Errorf("global.temp_dir must be string")
		}
	case "environment":
		if v, ok := value.(string); ok {
			cm.config.Global.Environment = v
		} else {
			return fmt.Errorf("global.environment must be string")
		}
	case "debug":
		if v, ok := value.(bool); ok {
			cm.config.Global.Debug = v
		} else {
			return fmt.Errorf("global.debug must be bool")
		}
	case "profiles_enabled":
		if v, ok := value.(bool); ok {
			cm.config.Global.ProfilesEnabled = v
		} else {
			return fmt.Errorf("global.profiles_enabled must be bool")
		}
	case "variables":
		if v, ok := value.(map[string]interface{}); ok {
			variables := make(map[string]string)
			for k, val := range v {
				if strVal, ok := val.(string); ok {
					variables[k] = strVal
				} else {
					return fmt.Errorf("global.variables values must be strings")
				}
			}
			cm.config.Global.Variables = variables
		} else {
			return fmt.Errorf("global.variables must be JSON object")
		}
	default:
		return fmt.Errorf("unknown global config field: %s", path[0])
	}
	return nil
}

// setGPUValue sets GPU configuration values
func (cm *ConfigManager) setGPUValue(path []string, value interface{}) error {
	if len(path) == 0 {
		return fmt.Errorf("empty GPU config path")
	}

	switch path[0] {
	case "enabled":
		if v, ok := value.(bool); ok {
			cm.config.GPU.Enabled = v
		} else {
			return fmt.Errorf("gpu.enabled must be bool")
		}
	case "prefer_cuda":
		if v, ok := value.(bool); ok {
			cm.config.GPU.PreferCUDA = v
		} else {
			return fmt.Errorf("gpu.prefer_cuda must be bool")
		}
	case "min_memory_gb":
		if v, ok := value.(float64); ok {
			cm.config.GPU.MinMemoryGB = v
		} else {
			return fmt.Errorf("gpu.min_memory_gb must be float64")
		}
	case "min_compute_capability":
		return cm.setGPUComputeCapabilityValue(path[1:], value)

	case "max_memory_utilization":
		if v, ok := value.(float64); ok {
			cm.config.GPU.MaxMemoryUtilization = v
		} else {
			return fmt.Errorf("gpu.max_memory_utilization must be float64")
		}
	case "device_id":
		if v, ok := value.(int); ok {
			cm.config.GPU.DeviceID = v
		} else {
			return fmt.Errorf("gpu.device_id must be int")
		}
	case "monitoring_interval":
		if v, ok := value.(Duration); ok {
			cm.config.GPU.MonitoringInterval = v
		} else {
			return fmt.Errorf("gpu.monitoring_interval must be duration")
		}
	case "resource_limits":
		return cm.setGPUResourceLimitsValue(path[1:], value)
	case "performance_thresholds":
		return cm.setGPUThresholdsValue(path[1:], value)
	case "allow_fallback":
		if v, ok := value.(bool); ok {
			cm.config.GPU.AllowFallback = v
		} else {
			return fmt.Errorf("gpu.allow_fallback must be bool")
		}
	case "profile_mode":
		if v, ok := value.(string); ok {
			cm.config.GPU.ProfileMode = v
		} else {
			return fmt.Errorf("gpu.profile_mode must be string")
		}
	case "log_gpu_events":
		if v, ok := value.(bool); ok {
			cm.config.GPU.LogGPUEvents = v
		} else {
			return fmt.Errorf("gpu.log_gpu_events must be bool")
		}
	default:
		return fmt.Errorf("unknown GPU config field: %s", path[0])
	}
	return nil
}

// setGPUComputeCapabilityValue sets GPU compute capability values
func (cm *ConfigManager) setGPUComputeCapabilityValue(path []string, value interface{}) error {
	if len(path) == 0 {
		return fmt.Errorf("empty GPU compute capability path")
	}

	switch path[0] {
	case "major":
		if v, ok := value.(int); ok {
			cm.config.GPU.MinComputeCapability.Major = v
		} else {
			return fmt.Errorf("gpu.min_compute_capability.major must be int")
		}
	case "minor":
		if v, ok := value.(int); ok {
			cm.config.GPU.MinComputeCapability.Minor = v
		} else {
			return fmt.Errorf("gpu.min_compute_capability.minor must be int")
		}
	default:
		return fmt.Errorf("unknown GPU compute capability field: %s", path[0])
	}
	return nil
}

// setGPUResourceLimitsValue sets GPU resource limits values
func (cm *ConfigManager) setGPUResourceLimitsValue(path []string, value interface{}) error {
	if len(path) == 0 {
		return fmt.Errorf("empty GPU resource limits path")
	}

	switch path[0] {
	case "max_gpu_utilization":
		if v, ok := value.(float64); ok {
			cm.config.GPU.ResourceLimits.MaxGPUUtilization = v
		} else {
			return fmt.Errorf("gpu.resource_limits.max_gpu_utilization must be float64")
		}
	case "max_memory_usage":
		if v, ok := value.(float64); ok {
			cm.config.GPU.ResourceLimits.MaxMemoryUsage = v
		} else {
			return fmt.Errorf("gpu.resource_limits.max_memory_usage must be float64")
		}
	case "max_power_consumption":
		if v, ok := value.(float64); ok {
			cm.config.GPU.ResourceLimits.MaxPowerConsumption = v
		} else {
			return fmt.Errorf("gpu.resource_limits.max_power_consumption must be float64")
		}

	case "throttle_on_high_memory":
		if v, ok := value.(bool); ok {
			cm.config.GPU.ResourceLimits.ThrottleOnHighMemory = v
		} else {
			return fmt.Errorf("gpu.resource_limits.throttle_on_high_memory must be bool")
		}
	case "throttle_on_power_limit":
		if v, ok := value.(bool); ok {
			cm.config.GPU.ResourceLimits.ThrottleOnPowerLimit = v
		} else {
			return fmt.Errorf("gpu.resource_limits.throttle_on_power_limit must be bool")
		}
	default:
		return fmt.Errorf("unknown GPU resource limits field: %s", path[0])
	}
	return nil
}

// setGPUThresholdsValue sets GPU performance thresholds values
func (cm *ConfigManager) setGPUThresholdsValue(path []string, value interface{}) error {
	if len(path) == 0 {
		return fmt.Errorf("empty GPU thresholds path")
	}

	switch path[0] {

	case "warning_memory_usage":
		if v, ok := value.(float64); ok {
			cm.config.GPU.PerformanceThresholds.WarningMemoryUsage = v
		} else {
			return fmt.Errorf("gpu.performance_thresholds.warning_memory_usage must be float64")
		}
	case "warning_gpu_utilization":
		if v, ok := value.(float64); ok {
			cm.config.GPU.PerformanceThresholds.WarningGPUUtilization = v
		} else {
			return fmt.Errorf("gpu.performance_thresholds.warning_gpu_utilization must be float64")
		}

	case "critical_memory_usage":
		if v, ok := value.(float64); ok {
			cm.config.GPU.PerformanceThresholds.CriticalMemoryUsage = v
		} else {
			return fmt.Errorf("gpu.performance_thresholds.critical_memory_usage must be float64")
		}
	case "critical_gpu_utilization":
		if v, ok := value.(float64); ok {
			cm.config.GPU.PerformanceThresholds.CriticalGPUUtilization = v
		} else {
			return fmt.Errorf("gpu.performance_thresholds.critical_gpu_utilization must be float64")
		}
	default:
		return fmt.Errorf("unknown GPU thresholds field: %s", path[0])
	}
	return nil
}

// NewConfigManagerFromEnvironment creates a config manager that loads only from environment variables
func NewConfigManagerFromEnvironment() (*ConfigManager, error) {
	cm := &ConfigManager{
		validate: validator.New(),
		format:   FormatYAML, // Default format for validation
	}

	// Initialize with empty config and defaults
	cm.config = &Config{}
	cm.setDefaults(cm.config)

	// Load from environment variables
	if err := cm.LoadFromEnvironment(); err != nil {
		return nil, fmt.Errorf("failed to load from environment: %w", err)
	}

	// Validate the configuration
	if err := cm.Validate(); err != nil {
		return nil, fmt.Errorf("environment configuration validation failed: %w", err)
	}

	return cm, nil
}

// ValidationError represents a detailed validation error with context
type ValidationError struct {
	Field    string      `json:"field"`
	Value    interface{} `json:"value,omitempty"`
	Rule     string      `json:"rule"`
	Message  string      `json:"message"`
	Line     int         `json:"line,omitempty"`
	Column   int         `json:"column,omitempty"`
	Path     string      `json:"path"`
	Severity string      `json:"severity"` // "error", "warning", "info"
}

func (ve ValidationError) Error() string {
	if ve.Line > 0 && ve.Column > 0 {
		return fmt.Sprintf("[%s:%d:%d] %s: %s", ve.Severity, ve.Line, ve.Column, ve.Field, ve.Message)
	}
	return fmt.Sprintf("[%s] %s: %s", ve.Severity, ve.Field, ve.Message)
}

// ValidationResult contains all validation errors and warnings
type ValidationResult struct {
	Errors   []ValidationError `json:"errors"`
	Warnings []ValidationError `json:"warnings"`
	Info     []ValidationError `json:"info"`
	Valid    bool              `json:"valid"`
}

func (vr *ValidationResult) AddError(field, rule, message string, value interface{}) {
	vr.Errors = append(vr.Errors, ValidationError{
		Field:    field,
		Value:    value,
		Rule:     rule,
		Message:  message,
		Severity: "error",
		Path:     field,
	})
	vr.Valid = false
}

func (vr *ValidationResult) AddWarning(field, rule, message string, value interface{}) {
	vr.Warnings = append(vr.Warnings, ValidationError{
		Field:    field,
		Value:    value,
		Rule:     rule,
		Message:  message,
		Severity: "warning",
		Path:     field,
	})
}

func (vr *ValidationResult) AddInfo(field, rule, message string, value interface{}) {
	vr.Info = append(vr.Info, ValidationError{
		Field:    field,
		Value:    value,
		Rule:     rule,
		Message:  message,
		Severity: "info",
		Path:     field,
	})
}

func (vr *ValidationResult) HasErrors() bool {
	return len(vr.Errors) > 0
}

func (vr *ValidationResult) HasWarnings() bool {
	return len(vr.Warnings) > 0
}

// EnhancedValidate performs comprehensive validation with detailed error reporting
func (cm *ConfigManager) EnhancedValidate() *ValidationResult {
	result := &ValidationResult{Valid: true}

	if cm.config == nil {
		result.AddError("config", "required", "no configuration loaded", nil)
		return result
	}

	// Standard struct validation
	if err := cm.validate.Struct(cm.config); err != nil {
		if validationErrors, ok := err.(validator.ValidationErrors); ok {
			for _, err := range validationErrors {
				result.AddError(
					err.Field(),
					err.Tag(),
					fmt.Sprintf("validation failed for field '%s': %s", err.Field(), err.Tag()),
					err.Value(),
				)
			}
		} else {
			result.AddError("config", "validation", err.Error(), nil)
		}
	}

	// Enhanced custom validation
	cm.enhancedCustomValidation(result)

	// Configuration linting
	cm.performConfigLinting(result)

	// Performance validation
	cm.performPerformanceValidation(result)

	return result
}

// enhancedCustomValidation performs advanced custom validation with detailed reporting
func (cm *ConfigManager) enhancedCustomValidation(result *ValidationResult) {
	config := cm.config

	// Enhanced port conflict validation
	cm.validatePortConflicts(result, config)

	// Enhanced TLS validation
	cm.validateTLSConfiguration(result, config)

	// Load test configuration validation
	cm.validateLoadTestConfiguration(result, config)

	// Worker configuration validation
	cm.validateWorkerConfiguration(result, config)

	// GPU configuration validation
	cm.validateGPUConfiguration(result, config)

	// Directory and file validation
	cm.validateDirectoriesAndFiles(result, config)

	// Cross-field dependency validation
	cm.validateCrossFieldDependencies(result, config)
}

// validatePortConflicts checks for port conflicts with detailed reporting
func (cm *ConfigManager) validatePortConflicts(result *ValidationResult, config *Config) {
	usedPorts := make(map[int][]string)

	// Collect all used ports
	if config.Server.Port > 0 {
		usedPorts[config.Server.Port] = append(usedPorts[config.Server.Port], "server.port")
	}

	if config.Dashboard.Enabled && config.Dashboard.Port > 0 {
		usedPorts[config.Dashboard.Port] = append(usedPorts[config.Dashboard.Port], "dashboard.port")
	}

	// Check for conflicts
	for port, fields := range usedPorts {
		if len(fields) > 1 {
			result.AddError(
				strings.Join(fields, ", "),
				"port_conflict",
				fmt.Sprintf("port %d is used by multiple services: %s", port, strings.Join(fields, ", ")),
				port,
			)
		}
	}

	// Check for well-known port usage warnings
	for port, fields := range usedPorts {
		if port < 1024 {
			result.AddWarning(
				fields[0],
				"privileged_port",
				fmt.Sprintf("port %d requires root privileges (port < 1024)", port),
				port,
			)
		}
	}
}

// validateTLSConfiguration validates TLS settings with enhanced checking
func (cm *ConfigManager) validateTLSConfiguration(result *ValidationResult, config *Config) {
	if !config.Server.TLS.Enabled {
		if config.Global.Environment == "production" {
			result.AddWarning(
				"server.tls.enabled",
				"security",
				"TLS is disabled in production environment - consider enabling for security",
				false,
			)
		}
		return
	}

	// Check certificate and key files
	if config.Server.TLS.CertFile == "" {
		result.AddError(
			"server.tls.cert_file",
			"required_when_tls_enabled",
			"TLS certificate file is required when TLS is enabled",
			"",
		)
	} else {
		if err := cm.validateFileExists(config.Server.TLS.CertFile); err != nil {
			result.AddError(
				"server.tls.cert_file",
				"file_exists",
				fmt.Sprintf("TLS certificate file does not exist: %s", config.Server.TLS.CertFile),
				config.Server.TLS.CertFile,
			)
		} else {
			// Check file permissions
			if err := cm.validateFilePermissions(config.Server.TLS.CertFile, 0644); err != nil {
				result.AddWarning(
					"server.tls.cert_file",
					"file_permissions",
					fmt.Sprintf("TLS certificate file has unusual permissions: %s", err),
					config.Server.TLS.CertFile,
				)
			}
		}
	}

	if config.Server.TLS.KeyFile == "" {
		result.AddError(
			"server.tls.key_file",
			"required_when_tls_enabled",
			"TLS private key file is required when TLS is enabled",
			"",
		)
	} else {
		if err := cm.validateFileExists(config.Server.TLS.KeyFile); err != nil {
			result.AddError(
				"server.tls.key_file",
				"file_exists",
				fmt.Sprintf("TLS private key file does not exist: %s", config.Server.TLS.KeyFile),
				config.Server.TLS.KeyFile,
			)
		} else {
			// Check key file permissions (should be more restrictive)
			if err := cm.validateFilePermissions(config.Server.TLS.KeyFile, 0600); err != nil {
				result.AddWarning(
					"server.tls.key_file",
					"file_permissions",
					fmt.Sprintf("TLS private key file should have restrictive permissions (600): %s", err),
					config.Server.TLS.KeyFile,
				)
			}
		}
	}
}

// validateLoadTestConfiguration validates load test configuration with enhanced checks
func (cm *ConfigManager) validateLoadTestConfiguration(result *ValidationResult, config *Config) {
	// Check if default concurrency exceeds max concurrency
	if config.LoadTest.DefaultConcurrency > config.LoadTest.MaxConcurrency {
		result.AddError(
			"load_test.default_concurrency",
			"exceeds_max",
			fmt.Sprintf("default_concurrency (%d) exceeds max_concurrency (%d)",
				config.LoadTest.DefaultConcurrency, config.LoadTest.MaxConcurrency),
			config.LoadTest.DefaultConcurrency,
		)
	}

	// Check for very short test duration
	if config.LoadTest.DefaultDuration.ToDuration() < 1*time.Second {
		result.AddWarning(
			"load_test.default_duration",
			"duration_too_short",
			fmt.Sprintf("very short test duration (%v) may not provide meaningful results",
				config.LoadTest.DefaultDuration.ToDuration()),
			config.LoadTest.DefaultDuration.ToDuration(),
		)
	}

	// Check ramp duration relative to test duration
	testDuration := config.LoadTest.DefaultDuration.ToDuration()
	rampUpDuration := config.LoadTest.RampUpDuration.ToDuration()
	if rampUpDuration > testDuration {
		result.AddWarning(
			"load_test.ramp_up_duration",
			"ramp_duration",
			fmt.Sprintf("ramp_up_duration (%v) is longer than test duration (%v)",
				rampUpDuration, testDuration),
			rampUpDuration,
		)
	}

	// Check for performance recommendations
	if config.LoadTest.MaxConcurrency > 1000 {
		result.AddInfo(
			"load_test.max_concurrency",
			"performance",
			fmt.Sprintf("high max_concurrency (%d) may require system tuning",
				config.LoadTest.MaxConcurrency),
			config.LoadTest.MaxConcurrency,
		)
	}
}

// validateWorkerConfiguration validates worker configuration with enhanced checks
func (cm *ConfigManager) validateWorkerConfiguration(result *ValidationResult, config *Config) {
	// Check pool size vs queue size ratio
	if config.Worker.PoolSize > config.Worker.QueueSize {
		result.AddWarning(
			"worker.pool_size",
			"pool_queue_ratio",
			fmt.Sprintf("pool_size (%d) exceeds queue_size (%d) - may cause worker starvation",
				config.Worker.PoolSize, config.Worker.QueueSize),
			config.Worker.PoolSize,
		)
	}

	// Check for excessive retry configuration
	if config.Worker.MaxRetries > 10 {
		result.AddWarning(
			"worker.max_retries",
			"excessive_retries",
			fmt.Sprintf("high max_retries (%d) may cause long delays on failures",
				config.Worker.MaxRetries),
			config.Worker.MaxRetries,
		)
	}

	// Check shutdown timeout
	if config.Worker.ShutdownTimeout.ToDuration() < 5*time.Second {
		result.AddWarning(
			"worker.shutdown_timeout",
			"shutdown_too_short",
			fmt.Sprintf("short shutdown_timeout (%v) may cause incomplete cleanup",
				config.Worker.ShutdownTimeout.ToDuration()),
			config.Worker.ShutdownTimeout.ToDuration(),
		)
	}

	// Optimal ratio suggestions
	optimalRatio := config.Worker.QueueSize / config.Worker.PoolSize
	if optimalRatio < 2 {
		result.AddInfo(
			"worker.configuration",
			"optimization",
			fmt.Sprintf("consider increasing queue_size relative to pool_size for better throughput (current ratio: %d)",
				optimalRatio),
			optimalRatio,
		)
	}
}

// validateGPUConfiguration validates GPU configuration with enhanced checks
func (cm *ConfigManager) validateGPUConfiguration(result *ValidationResult, config *Config) {
	if !config.GPU.Enabled {
		return
	}

	// Check memory utilization settings
	if config.GPU.MaxMemoryUtilization < 10 {
		result.AddWarning(
			"gpu.max_memory_utilization",
			"memory_utilization_low",
			fmt.Sprintf("max_memory_utilization (%.1f%%) is very low - GPU may be underutilized", config.GPU.MaxMemoryUtilization),
			config.GPU.MaxMemoryUtilization,
		)
	}

	if config.GPU.MaxMemoryUtilization > 95 {
		result.AddWarning(
			"gpu.max_memory_utilization",
			"memory_utilization_high",
			fmt.Sprintf("max_memory_utilization (%.1f%%) is very high - may cause out-of-memory errors", config.GPU.MaxMemoryUtilization),
			config.GPU.MaxMemoryUtilization,
		)
	}

	// Check minimum memory requirements
	if config.GPU.MinMemoryGB > 0 && config.GPU.MinMemoryGB < 1 {
		result.AddInfo(
			"gpu.min_memory_gb",
			"memory_requirement",
			fmt.Sprintf("min_memory_gb (%.1fGB) is quite low - ensure this meets your workload requirements", config.GPU.MinMemoryGB),
			config.GPU.MinMemoryGB,
		)
	}

	// Check compute capability
	if config.GPU.MinComputeCapability.Major < 3 {
		result.AddWarning(
			"gpu.min_compute_capability.major",
			"compute_capability_old",
			fmt.Sprintf("min_compute_capability (%d.%d) is quite old - consider newer GPUs for better performance",
				config.GPU.MinComputeCapability.Major, config.GPU.MinComputeCapability.Minor),
			config.GPU.MinComputeCapability.Major,
		)
	}

	// Check monitoring interval
	if config.GPU.MonitoringInterval.ToDuration() < 100*time.Millisecond {
		result.AddWarning(
			"gpu.monitoring_interval",
			"monitoring_too_frequent",
			fmt.Sprintf("monitoring_interval (%v) is very short - may impact performance", config.GPU.MonitoringInterval.ToDuration()),
			config.GPU.MonitoringInterval.ToDuration(),
		)
	}

	if config.GPU.MonitoringInterval.ToDuration() > 10*time.Second {
		result.AddWarning(
			"gpu.monitoring_interval",
			"monitoring_too_infrequent",
			fmt.Sprintf("monitoring_interval (%v) is quite long - may miss important GPU events", config.GPU.MonitoringInterval.ToDuration()),
			config.GPU.MonitoringInterval.ToDuration(),
		)
	}

	// Validate GPU resource limits
	cm.validateGPUResourceLimits(result, config)

	// Validate GPU performance thresholds
	cm.validateGPUPerformanceThresholds(result, config)

	// Cross-validation between GPU settings
	cm.validateGPUCrossFieldDependencies(result, config)
}

// validateGPUResourceLimits validates GPU resource limit configurations
func (cm *ConfigManager) validateGPUResourceLimits(result *ValidationResult, config *Config) {
	limits := config.GPU.ResourceLimits

	// Check GPU utilization limit
	if limits.MaxGPUUtilization > 0 && limits.MaxGPUUtilization < 20 {
		result.AddWarning(
			"gpu.resource_limits.max_gpu_utilization",
			"utilization_too_low",
			fmt.Sprintf("max_gpu_utilization (%.1f%%) is very low - GPU may be significantly underutilized", limits.MaxGPUUtilization),
			limits.MaxGPUUtilization,
		)
	}

	// Check memory usage limit
	if limits.MaxMemoryUsage > 0 && limits.MaxMemoryUsage < 10 {
		result.AddWarning(
			"gpu.resource_limits.max_memory_usage",
			"memory_usage_too_low",
			fmt.Sprintf("max_memory_usage (%.1f%%) is very low - may severely limit GPU operations", limits.MaxMemoryUsage),
			limits.MaxMemoryUsage,
		)
	}

	if limits.MaxMemoryUsage > 95 {
		result.AddWarning(
			"gpu.resource_limits.max_memory_usage",
			"memory_usage_too_high",
			fmt.Sprintf("max_memory_usage (%.1f%%) is very high - may cause stability issues", limits.MaxMemoryUsage),
			limits.MaxMemoryUsage,
		)
	}

	// Check power consumption limit
	if limits.MaxPowerConsumption > 0 && limits.MaxPowerConsumption < 50 {
		result.AddInfo(
			"gpu.resource_limits.max_power_consumption",
			"power_limit_low",
			fmt.Sprintf("max_power_consumption (%.1fW) is quite low - ensure this meets performance requirements", limits.MaxPowerConsumption),
			limits.MaxPowerConsumption,
		)
	}

}

// validateGPUPerformanceThresholds validates GPU performance threshold configurations
func (cm *ConfigManager) validateGPUPerformanceThresholds(result *ValidationResult, config *Config) {
	thresholds := config.GPU.PerformanceThresholds

	// Validate memory usage thresholds
	if thresholds.WarningMemoryUsage >= thresholds.CriticalMemoryUsage {
		result.AddError(
			"gpu.performance_thresholds.warning_memory_usage",
			"memory_threshold_order",
			fmt.Sprintf("warning_memory_usage (%.1f%%) must be less than critical_memory_usage (%.1f%%)",
				thresholds.WarningMemoryUsage, thresholds.CriticalMemoryUsage),
			thresholds.WarningMemoryUsage,
		)
	}

	// Validate GPU utilization thresholds
	if thresholds.WarningGPUUtilization >= thresholds.CriticalGPUUtilization {
		result.AddError(
			"gpu.performance_thresholds.warning_gpu_utilization",
			"utilization_threshold_order",
			fmt.Sprintf("warning_gpu_utilization (%.1f%%) must be less than critical_gpu_utilization (%.1f%%)",
				thresholds.WarningGPUUtilization, thresholds.CriticalGPUUtilization),
			thresholds.WarningGPUUtilization,
		)
	}

	if thresholds.WarningMemoryUsage < 70 {
		result.AddInfo(
			"gpu.performance_thresholds.warning_memory_usage",
			"threshold_conservative",
			fmt.Sprintf("warning_memory_usage (%.1f%%) is quite conservative - higher usage is typically safe", thresholds.WarningMemoryUsage),
			thresholds.WarningMemoryUsage,
		)
	}
}

// validateGPUCrossFieldDependencies validates relationships between different GPU configuration fields
func (cm *ConfigManager) validateGPUCrossFieldDependencies(result *ValidationResult, config *Config) {
	// Check consistency between general GPU settings and resource limits
	if config.GPU.MaxMemoryUtilization > 0 && config.GPU.ResourceLimits.MaxMemoryUsage > 0 {
		if config.GPU.MaxMemoryUtilization < config.GPU.ResourceLimits.MaxMemoryUsage {
			result.AddWarning(
				"gpu.max_memory_utilization",
				"memory_settings_inconsistent",
				fmt.Sprintf("max_memory_utilization (%.1f%%) is less than resource_limits.max_memory_usage (%.1f%%) - consider aligning these values",
					config.GPU.MaxMemoryUtilization, config.GPU.ResourceLimits.MaxMemoryUsage),
				config.GPU.MaxMemoryUtilization,
			)
		}
	}

	// Check profile mode vs performance requirements
	if config.GPU.ProfileMode == "detailed" && config.GPU.MonitoringInterval.ToDuration() > 1*time.Second {
		result.AddInfo(
			"gpu.profile_mode",
			"profiling_monitoring_mismatch",
			"detailed profiling with infrequent monitoring may miss important performance data",
			config.GPU.ProfileMode,
		)
	}

	// Check fallback configuration in production
	if config.Global.Environment == "production" && !config.GPU.AllowFallback {
		result.AddWarning(
			"gpu.allow_fallback",
			"production_fallback",
			"disabling GPU fallback in production may cause system failures if GPU is unavailable",
			false,
		)
	}

	// Check device ID validity
	if config.GPU.DeviceID < -1 {
		result.AddError(
			"gpu.device_id",
			"invalid_device_id",
			fmt.Sprintf("device_id (%d) must be -1 (auto-select) or non-negative", config.GPU.DeviceID),
			config.GPU.DeviceID,
		)
	}
}

// validateDirectoriesAndFiles validates directory and file configurations
func (cm *ConfigManager) validateDirectoriesAndFiles(result *ValidationResult, config *Config) {
	// Validate directories
	dirs := map[string]string{
		"global.config_dir": config.Global.ConfigDir,
		"global.data_dir":   config.Global.DataDir,
		"global.temp_dir":   config.Global.TempDir,
	}

	for field, dir := range dirs {
		if dir != "" {
			if err := cm.validateDirectoryWritable(dir); err != nil {
				result.AddError(
					field,
					"directory_access",
					fmt.Sprintf("directory not writable: %s - %v", dir, err),
					dir,
				)
			}
		}
	}

	// Validate output file path if specified
	if config.Output.File != "" {
		dir := filepath.Dir(config.Output.File)
		if err := cm.validateDirectoryWritable(dir); err != nil {
			result.AddError(
				"output.file",
				"output_directory_access",
				fmt.Sprintf("output directory not writable: %s - %v", dir, err),
				config.Output.File,
			)
		}
	}
}

// validateCrossFieldDependencies validates relationships between different configuration fields
func (cm *ConfigManager) validateCrossFieldDependencies(result *ValidationResult, config *Config) {
	// Dashboard dependency validation - check original config before defaults were applied
	if config.Dashboard.Enabled {
		// These checks run after defaults are set, so we need to detect if values were set explicitly
		// For dashboard, if enabled but port/host are at default values, assume they weren't set explicitly
		if config.Dashboard.Host == "localhost" && config.Dashboard.Port == 8081 {
			// These are likely default values, not explicitly set
			result.AddError(
				"dashboard.host",
				"required_when_enabled",
				"dashboard.host is required when dashboard is enabled",
				"",
			)
			result.AddError(
				"dashboard.port",
				"required_when_enabled",
				"dashboard.port is required when dashboard is enabled",
				0,
			)
		}
	}

	// Metrics dependency validation
	if config.Metrics.Enabled {
		if config.Metrics.CollectionInterval.ToDuration() > config.Metrics.ExportInterval.ToDuration() {
			result.AddWarning(
				"metrics.collection_interval",
				"collection_export_mismatch",
				"collection_interval should typically be less than or equal to export_interval",
				config.Metrics.CollectionInterval.ToDuration(),
			)
		}

		if config.Metrics.BufferSize < config.Worker.PoolSize*10 {
			result.AddWarning(
				"metrics.buffer_size",
				"buffer_worker_ratio",
				fmt.Sprintf("metrics buffer_size (%d) might be too small for worker pool_size (%d)",
					config.Metrics.BufferSize, config.Worker.PoolSize),
				config.Metrics.BufferSize,
			)
		}
	}

	// Output configuration dependencies
	if config.Output.Console && config.Output.File == "" && !config.Dashboard.Enabled {
		result.AddInfo(
			"output",
			"output_destination",
			"only console output is enabled - consider enabling file output or dashboard for persistence",
			nil,
		)
	}
}

// performConfigLinting performs best practice validation
func (cm *ConfigManager) performConfigLinting(result *ValidationResult) {
	config := cm.config

	// Security linting
	cm.lintSecurity(result, config)

	// Performance linting
	cm.lintPerformance(result, config)

	// Operational linting
	cm.lintOperational(result, config)
}

// lintSecurity performs security-focused linting
func (cm *ConfigManager) lintSecurity(result *ValidationResult, config *Config) {
	// Production security checks
	if config.Global.Environment == "production" {
		if config.Global.Debug {
			result.AddWarning(
				"global.debug",
				"security",
				"debug mode should be disabled in production",
				true,
			)
		}

		if config.Global.LogLevel == "debug" {
			result.AddWarning(
				"global.log_level",
				"security",
				"debug log level may expose sensitive information in production",
				"debug",
			)
		}

		if config.Server.Host == "0.0.0.0" || config.Server.Host == "" {
			result.AddInfo(
				"server.host",
				"security",
				"binding to all interfaces (0.0.0.0) in production - ensure proper firewall configuration",
				config.Server.Host,
			)
		}
	}
}

// lintPerformance performs performance-focused linting
func (cm *ConfigManager) lintPerformance(result *ValidationResult, config *Config) {
	// Timeout configuration
	readTimeout := config.Server.ReadTimeout.ToDuration()
	writeTimeout := config.Server.WriteTimeout.ToDuration()

	if readTimeout < 5*time.Second {
		result.AddInfo(
			"server.read_timeout",
			"performance",
			"read_timeout less than 5 seconds may cause premature timeouts under load",
			readTimeout,
		)
	}

	if writeTimeout < readTimeout {
		result.AddWarning(
			"server.write_timeout",
			"performance",
			"write_timeout should typically be equal to or greater than read_timeout",
			writeTimeout,
		)
	}

	// Buffer size recommendations
	if config.Metrics.Enabled && config.Metrics.BufferSize < 1000 {
		result.AddInfo(
			"metrics.buffer_size",
			"performance",
			"metrics buffer_size less than 1000 may cause frequent buffer flushes",
			config.Metrics.BufferSize,
		)
	}
}

// lintOperational performs operational best practice linting
func (cm *ConfigManager) lintOperational(result *ValidationResult, config *Config) {
	// Dashboard recommendations
	if !config.Dashboard.Enabled && config.Global.Environment != "testing" {
		result.AddInfo(
			"dashboard.enabled",
			"operational",
			"consider enabling dashboard for monitoring and observability",
			false,
		)
	}

	// Metrics recommendations
	if !config.Metrics.Enabled {
		result.AddInfo(
			"metrics.enabled",
			"operational",
			"consider enabling metrics for monitoring and performance analysis",
			false,
		)
	}

	// Output recommendations
	if !config.Output.Console && config.Output.File == "" {
		result.AddWarning(
			"output",
			"operational",
			"no output destination configured - results will not be saved",
			nil,
		)
	}
}

// performPerformanceValidation validates performance-related settings
func (cm *ConfigManager) performPerformanceValidation(result *ValidationResult) {
	config := cm.config

	// Memory usage estimation
	estimatedMemory := cm.estimateMemoryUsage(config)
	if estimatedMemory > 1024*1024*1024 { // 1GB
		result.AddWarning(
			"configuration",
			"memory_usage",
			fmt.Sprintf("estimated memory usage (%.1f MB) is high", float64(estimatedMemory)/(1024*1024)),
			estimatedMemory,
		)
	}

	// CPU usage estimation
	if config.Worker.PoolSize > 100 {
		result.AddInfo(
			"worker.pool_size",
			"cpu_usage",
			fmt.Sprintf("high worker pool size (%d) may cause CPU contention", config.Worker.PoolSize),
			config.Worker.PoolSize,
		)
	}

	// Network resource validation
	maxConnections := config.LoadTest.MaxConcurrency * 2 // Estimate
	if maxConnections > 10000 {
		result.AddWarning(
			"load_test.max_concurrency",
			"network_resources",
			fmt.Sprintf("estimated max connections (%d) may exceed system limits", maxConnections),
			maxConnections,
		)
	}
}

// Helper validation functions
func (cm *ConfigManager) validateFileExists(filePath string) error {
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("file does not exist")
	}
	return nil
}

func (cm *ConfigManager) validateFilePermissions(filePath string, expectedMode os.FileMode) error {
	info, err := os.Stat(filePath)
	if err != nil {
		return err
	}

	mode := info.Mode().Perm()
	if mode != expectedMode {
		return fmt.Errorf("expected %o, got %o", expectedMode, mode)
	}
	return nil
}

func (cm *ConfigManager) validateDirectoryWritable(dirPath string) error {
	// Try to create directory if it doesn't exist
	if err := os.MkdirAll(dirPath, 0755); err != nil {
		return fmt.Errorf("cannot create directory: %w", err)
	}

	// Test write access
	testFile := filepath.Join(dirPath, ".write_test")
	if err := os.WriteFile(testFile, []byte("test"), 0644); err != nil {
		return fmt.Errorf("cannot write to directory: %w", err)
	}

	// Clean up test file
	os.Remove(testFile)
	return nil
}

// estimateMemoryUsage estimates memory usage based on configuration
func (cm *ConfigManager) estimateMemoryUsage(config *Config) int64 {
	var estimate int64

	// Base application memory
	estimate += 50 * 1024 * 1024 // 50MB base

	// Worker pool memory (more conservative estimate)
	estimate += int64(config.Worker.PoolSize) * 2 * 1024 * 1024 // 2MB per worker

	// Queue memory (more realistic estimate)
	estimate += int64(config.Worker.QueueSize) * 4 * 1024 // 4KB per queue item

	// Metrics buffer memory (more conservative)
	if config.Metrics.Enabled {
		estimate += int64(config.Metrics.BufferSize) * 512 // 512 bytes per metric
	}

	// Dashboard memory (more realistic for larger history)
	if config.Dashboard.Enabled {
		estimate += int64(config.Dashboard.HistoryLimit) * 256 // 256 bytes per history item
	}

	// Additional overhead for concurrent operations
	if config.LoadTest.MaxConcurrency > 100 {
		estimate += int64(config.LoadTest.MaxConcurrency) * 1024 // 1KB per concurrent operation
	}

	return estimate
}
