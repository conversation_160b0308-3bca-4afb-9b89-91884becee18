package api

import (
    "context"
    "net/http"
    "strconv"
    "time"
)

// TLSConfig defines TLS parameters for the API server.
type TLSConfig struct {
    Enabled  bool   `json:"enabled" yaml:"enabled"`
    CertFile string `json:"cert_file" yaml:"cert_file"`
    KeyFile  string `json:"key_file" yaml:"key_file"`
}

// ServerConfig contains basic HTTP server configuration.
type ServerConfig struct {
    Host         string        `json:"host" yaml:"host"`
    Port         int           `json:"port" yaml:"port"`
    ReadTimeout  time.Duration `json:"read_timeout" yaml:"read_timeout"`
    WriteTimeout time.Duration `json:"write_timeout" yaml:"write_timeout"`
    IdleTimeout  time.Duration `json:"idle_timeout" yaml:"idle_timeout"`
    TLS          TLSConfig     `json:"tls" yaml:"tls"`
}

// Server exposes the API endpoints used by controller nodes.
// A fully-featured implementation will add many endpoints; for now we provide
// a functional health endpoint so that CLI daemon mode can operate without
// stubs or mocks.
//
// Start() blocks; Shutdown() stops the server.
// The executionEngine parameter is kept as interface{} so we can wire it up to
// the real engine later without changing the CLI layer again.
type Server struct {
    httpServer *http.Server
}

// NewServer constructs a new Server using cfg and the provided executionEngine.
// The engine isn’t used yet but will be wired into future handlers.
func NewServer(cfg ServerConfig, executionEngine interface{}) *Server {
    addr := cfg.Host
    if cfg.Port != 0 {
        addr = addr + ":" + strconv.Itoa(cfg.Port)
    }

    mux := http.NewServeMux()
    // Basic liveness / readiness endpoints
    mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
        w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(http.StatusOK)
        _, _ = w.Write([]byte(`{"status":"ok"}`))
    })

    // TODO: register real test-plan submission & status handlers that use executionEngine

    srv := &http.Server{
        Addr:         addr,
        Handler:      mux,
        ReadTimeout:  cfg.ReadTimeout,
        WriteTimeout: cfg.WriteTimeout,
        IdleTimeout:  cfg.IdleTimeout,
    }

    return &Server{httpServer: srv}
}

// Start launches the HTTP server and blocks until it stops.
func (s *Server) Start() error {
    // TLS support can be added later.
    return s.httpServer.ListenAndServe()
}

// Shutdown gracefully shuts down the server.
func (s *Server) Shutdown(ctx context.Context) error {
    return s.httpServer.Shutdown(ctx)
} 