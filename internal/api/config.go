// Package api provides REST API endpoints for UI integration
package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"neuralmetergo/internal/config"
)

// ConfigurationAPI provides REST API endpoints for configuration management
// @ui_api This struct handles all configuration-related API endpoints for the UI
type ConfigurationAPI struct {
	configManager  *config.ConfigManager
	subscribers    map[string]chan ConfigChangeEvent
	mutex          sync.RWMutex
	reloadCallback func(*config.Config, *config.Config)
}

// ConfigChangeEvent represents a configuration change notification
type ConfigChangeEvent struct {
	Type      string                 `json:"type"`
	Timestamp time.Time              `json:"timestamp"`
	Changes   map[string]interface{} `json:"changes"`
	Source    string                 `json:"source"`
}

// ConfigResponse represents a standard API response for configuration operations
type ConfigResponse struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Message string      `json:"message,omitempty"`
}

// ConfigValidationRequest represents a configuration validation request
type ConfigValidationRequest struct {
	ConfigData map[string]interface{} `json:"config_data"`
	Format     string                 `json:"format"` // "yaml" or "json"
}

// ConfigValidationResponse represents the result of configuration validation
type ConfigValidationResponse struct {
	Valid    bool     `json:"valid"`
	Errors   []string `json:"errors,omitempty"`
	Warnings []string `json:"warnings,omitempty"`
}

// NewConfigurationAPI creates a new Configuration API instance
// @ui_api Constructor for the configuration API with ConfigManager integration
func NewConfigurationAPI(configManager *config.ConfigManager) *ConfigurationAPI {
	api := &ConfigurationAPI{
		configManager: configManager,
		subscribers:   make(map[string]chan ConfigChangeEvent),
	}

	// Set up a callback for configuration changes
	api.reloadCallback = api.notifyConfigChange

	return api
}

// GetLoadTestConfig handles GET /api/config/load-test
// @ui_api Returns the current load test configuration
func (api *ConfigurationAPI) GetLoadTestConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	config := api.configManager.Get()
	if config == nil {
		api.writeErrorResponse(w, http.StatusInternalServerError, "Configuration not loaded")
		return
	}

	response := ConfigResponse{
		Success: true,
		Data:    config.LoadTest,
		Message: "Load test configuration retrieved successfully",
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// UpdateLoadTestConfig handles POST /api/config/load-test
// @ui_api Updates the load test configuration
func (api *ConfigurationAPI) UpdateLoadTestConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var loadTestConfig config.LoadTestConfig
	if err := json.NewDecoder(r.Body).Decode(&loadTestConfig); err != nil {
		api.writeErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Invalid JSON: %v", err))
		return
	}

	// Get current config and update load test section
	currentConfig := api.configManager.Get()
	if currentConfig == nil {
		api.writeErrorResponse(w, http.StatusInternalServerError, "Configuration not loaded")
		return
	}

	// Store old config for change detection
	oldConfig := *currentConfig

	// Create updated config
	updatedConfig := *currentConfig
	updatedConfig.LoadTest = loadTestConfig

	// Validate the updated configuration using existing validation
	if err := api.configManager.Validate(); err != nil {
		api.writeErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Configuration validation failed: %v", err))
		return
	}

	// Note: In a real implementation, we would need a method to update the config
	// For now, we'll simulate the update and trigger the callback
	api.notifyConfigChange(&oldConfig, &updatedConfig)

	response := ConfigResponse{
		Success: true,
		Data:    loadTestConfig,
		Message: "Load test configuration updated successfully",
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// GetConfigSchema handles GET /api/config/schema
// @ui_api Returns the configuration schema for validation and UI generation
func (api *ConfigurationAPI) GetConfigSchema(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// Generate JSON schema from the Config struct
	schema := api.generateConfigSchema()

	response := ConfigResponse{
		Success: true,
		Data:    schema,
		Message: "Configuration schema retrieved successfully",
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// ValidateConfig handles POST /api/config/validate
// @ui_api Validates configuration data without applying it
func (api *ConfigurationAPI) ValidateConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var request ConfigValidationRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		api.writeErrorResponse(w, http.StatusBadRequest, fmt.Sprintf("Invalid JSON: %v", err))
		return
	}

	// Convert map to config struct and validate
	validationResult := api.validateConfigData(request.ConfigData, request.Format)

	response := ConfigResponse{
		Success: true,
		Data:    validationResult,
		Message: "Configuration validation completed",
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// SubscribeToChanges handles WebSocket connections for real-time config updates
// @ui_api Provides real-time configuration change notifications via WebSocket
func (api *ConfigurationAPI) SubscribeToChanges(w http.ResponseWriter, r *http.Request) {
	// This would be implemented with WebSocket upgrader for real-time notifications
	// For now, we'll return the subscription endpoint info
	response := ConfigResponse{
		Success: true,
		Message: "WebSocket subscription endpoint for real-time config updates",
		Data: map[string]string{
			"endpoint": "/api/config/subscribe",
			"protocol": "websocket",
			"events":   "config_changed, validation_error, reload_success",
		},
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// RegisterRoutes registers all configuration API routes
// @ui_api Registers all configuration management endpoints
func (api *ConfigurationAPI) RegisterRoutes(mux *http.ServeMux) {
	mux.HandleFunc("/api/config/load-test", func(w http.ResponseWriter, r *http.Request) {
		switch r.Method {
		case http.MethodGet:
			api.GetLoadTestConfig(w, r)
		case http.MethodPost:
			api.UpdateLoadTestConfig(w, r)
		default:
			api.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		}
	})

	mux.HandleFunc("/api/config/schema", api.GetConfigSchema)
	mux.HandleFunc("/api/config/validate", api.ValidateConfig)
	mux.HandleFunc("/api/config/subscribe", api.SubscribeToChanges)
}

// Helper methods

func (api *ConfigurationAPI) writeJSONResponse(w http.ResponseWriter, statusCode int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(data)
}

func (api *ConfigurationAPI) writeErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	response := ConfigResponse{
		Success: false,
		Error:   message,
	}
	api.writeJSONResponse(w, statusCode, response)
}

func (api *ConfigurationAPI) notifyConfigChange(oldConfig, newConfig *config.Config) {
	api.mutex.RLock()
	defer api.mutex.RUnlock()

	event := ConfigChangeEvent{
		Type:      "config_changed",
		Timestamp: time.Now(),
		Changes:   api.detectChanges(oldConfig, newConfig),
		Source:    "api",
	}

	// Notify all subscribers
	for _, ch := range api.subscribers {
		select {
		case ch <- event:
		default:
			// Channel is full, skip this subscriber
		}
	}
}

func (api *ConfigurationAPI) detectChanges(oldConfig, newConfig *config.Config) map[string]interface{} {
	changes := make(map[string]interface{})

	// Simple change detection based on actual LoadTestConfig fields
	if oldConfig != nil && newConfig != nil {
		if oldConfig.LoadTest.DefaultDuration != newConfig.LoadTest.DefaultDuration {
			changes["load_test.default_duration"] = map[string]interface{}{
				"old": oldConfig.LoadTest.DefaultDuration.String(),
				"new": newConfig.LoadTest.DefaultDuration.String(),
			}
		}
		if oldConfig.LoadTest.DefaultConcurrency != newConfig.LoadTest.DefaultConcurrency {
			changes["load_test.default_concurrency"] = map[string]interface{}{
				"old": oldConfig.LoadTest.DefaultConcurrency,
				"new": newConfig.LoadTest.DefaultConcurrency,
			}
		}
		if oldConfig.LoadTest.MaxConcurrency != newConfig.LoadTest.MaxConcurrency {
			changes["load_test.max_concurrency"] = map[string]interface{}{
				"old": oldConfig.LoadTest.MaxConcurrency,
				"new": newConfig.LoadTest.MaxConcurrency,
			}
		}
		// Add more field comparisons as needed
	}

	return changes
}

func (api *ConfigurationAPI) generateConfigSchema() map[string]interface{} {
	// Generate a basic JSON schema for the configuration based on actual structure
	return map[string]interface{}{
		"$schema": "http://json-schema.org/draft-07/schema#",
		"type":    "object",
		"properties": map[string]interface{}{
			"server": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host": map[string]interface{}{
						"type":        "string",
						"description": "Server host address",
						"default":     "localhost",
					},
					"port": map[string]interface{}{
						"type":        "integer",
						"description": "Server port number",
						"minimum":     1,
						"maximum":     65535,
						"default":     8080,
					},
				},
			},
			"load_test": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"default_duration": map[string]interface{}{
						"type":        "string",
						"description": "Default test duration (e.g., '30s', '5m')",
						"pattern":     "^[0-9]+(ns|us|µs|ms|s|m|h)$",
						"default":     "30s",
					},
					"default_concurrency": map[string]interface{}{
						"type":        "integer",
						"description": "Default number of concurrent connections",
						"minimum":     1,
						"maximum":     10000,
						"default":     10,
					},
					"max_concurrency": map[string]interface{}{
						"type":        "integer",
						"description": "Maximum number of concurrent connections",
						"minimum":     1,
						"maximum":     10000,
						"default":     100,
					},
					"ramp_up_duration": map[string]interface{}{
						"type":        "string",
						"description": "Ramp up duration (e.g., '10s', '2m')",
						"pattern":     "^[0-9]+(ns|us|µs|ms|s|m|h)$",
						"default":     "10s",
					},
					"ramp_down_duration": map[string]interface{}{
						"type":        "string",
						"description": "Ramp down duration (e.g., '10s', '2m')",
						"pattern":     "^[0-9]+(ns|us|µs|ms|s|m|h)$",
						"default":     "10s",
					},
				},
			},
		},
	}
}

func (api *ConfigurationAPI) validateConfigData(configData map[string]interface{}, format string) ConfigValidationResponse {
	// Convert map to JSON and then to Config struct for validation
	jsonData, err := json.Marshal(configData)
	if err != nil {
		return ConfigValidationResponse{
			Valid:  false,
			Errors: []string{fmt.Sprintf("Failed to marshal config data: %v", err)},
		}
	}

	var tempConfig config.Config
	if err := json.Unmarshal(jsonData, &tempConfig); err != nil {
		return ConfigValidationResponse{
			Valid:  false,
			Errors: []string{fmt.Sprintf("Failed to parse config data: %v", err)},
		}
	}

	// Create a temporary ConfigManager to validate the config
	tempManager, err := config.NewConfigManager("temp.yaml")
	if err != nil {
		return ConfigValidationResponse{
			Valid:  false,
			Errors: []string{fmt.Sprintf("Failed to create validator: %v", err)},
		}
	}

	// Use enhanced validation for better error reporting
	validationResult := tempManager.EnhancedValidate()

	var errors []string
	var warnings []string

	for _, err := range validationResult.Errors {
		errors = append(errors, err.Message)
	}

	for _, warn := range validationResult.Warnings {
		warnings = append(warnings, warn.Message)
	}

	return ConfigValidationResponse{
		Valid:    validationResult.Valid,
		Errors:   errors,
		Warnings: warnings,
	}
}
