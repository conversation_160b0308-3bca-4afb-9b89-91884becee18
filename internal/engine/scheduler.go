// Package engine provides test plan execution orchestration
package engine

import (
	"context"
	"log"
	"math"
	"sync"
	"time"

	"neuralmetergo/internal/parser"
)

// ExecutionScheduler manages timing, concurrency, and scenario scheduling
type ExecutionScheduler struct {
	testPlan         *parser.TestPlan
	config           *EngineConfig
	rampUpController *RampUpController
	scenarioManager  *ScenarioManager
	timingController *TimingController
	mu               sync.RWMutex
}

// RampUpController manages the ramp-up phase
type RampUpController struct {
	duration     time.Duration
	steps        int
	stepDuration time.Duration
	targetLoad   int
	currentStep  int
	mu           sync.RWMutex
}

// ScenarioManager handles scenario execution and weight distribution
type ScenarioManager struct {
	scenarios       []parser.Scenario
	weights         map[string]float64
	requestCounts   map[string]int64
	totalRequests   int64
	currentScenario int
	mu              sync.RWMutex
}

// TimingController manages request timing and intervals
type TimingController struct {
	requestInterval time.Duration
	lastRequestTime time.Time
	requestCounter  int64
	mu              sync.RWMutex
}

// NewExecutionScheduler creates a new execution scheduler
func NewExecutionScheduler(testPlan *parser.TestPlan, config *EngineConfig) *ExecutionScheduler {
	scheduler := &ExecutionScheduler{
		testPlan: testPlan,
		config:   config,
	}

	// Initialize ramp-up controller
	scheduler.rampUpController = &RampUpController{
		duration:    testPlan.RampUp.Duration,
		steps:       10, // Default to 10 steps
		targetLoad:  testPlan.Concurrency,
		currentStep: 0,
	}
	if scheduler.rampUpController.duration > 0 {
		scheduler.rampUpController.stepDuration = scheduler.rampUpController.duration / time.Duration(scheduler.rampUpController.steps)
	}

	// Initialize scenario manager
	scheduler.scenarioManager = &ScenarioManager{
		scenarios:     testPlan.Scenarios,
		weights:       make(map[string]float64),
		requestCounts: make(map[string]int64),
		totalRequests: 0,
	}

	// Calculate scenario weights
	totalWeight := 0.0
	for _, scenario := range testPlan.Scenarios {
		weight := float64(scenario.Weight)
		if weight <= 0 {
			weight = 1.0 // Default weight
		}
		scheduler.scenarioManager.weights[scenario.Name] = weight
		totalWeight += weight
	}

	// Normalize weights
	for name, weight := range scheduler.scenarioManager.weights {
		scheduler.scenarioManager.weights[name] = weight / totalWeight
	}

	// Initialize timing controller
	scheduler.timingController = &TimingController{
		requestInterval: time.Duration(0), // Will be calculated dynamically
		lastRequestTime: time.Now(),
		requestCounter:  0,
	}

	return scheduler
}

// Start begins the execution scheduling
func (s *ExecutionScheduler) Start(ctx context.Context, wg *sync.WaitGroup) {
	defer wg.Done()

	log.Printf("Starting execution scheduler")

	// Calculate request rate and timing
	s.calculateRequestTiming()

	// Start scenario scheduling
	ticker := time.NewTicker(s.timingController.requestInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			log.Printf("Execution scheduler stopped")
			return
		case <-ticker.C:
			s.scheduleNextRequest()
		}
	}
}

// calculateRequestTiming calculates the request timing based on test plan
func (s *ExecutionScheduler) calculateRequestTiming() {
	s.timingController.mu.Lock()
	defer s.timingController.mu.Unlock()

	// Calculate total requests expected
	totalDuration := s.testPlan.Duration.Duration
	concurrency := s.testPlan.Concurrency

	if totalDuration > 0 && concurrency > 0 {
		// Calculate requests per second target
		// This is a simplified calculation - real implementation would be more sophisticated
		requestsPerSecond := float64(concurrency) * 1.0 // Assume 1 request per second per worker as baseline

		// Adjust based on scenarios and their complexities
		avgRequestsPerScenario := 0.0
		for _, scenario := range s.scenarioManager.scenarios {
			avgRequestsPerScenario += float64(len(scenario.Requests))
		}
		if len(s.scenarioManager.scenarios) > 0 {
			avgRequestsPerScenario /= float64(len(s.scenarioManager.scenarios))
		}

		if avgRequestsPerScenario > 0 {
			// Adjust interval based on average requests per scenario
			s.timingController.requestInterval = time.Duration(float64(time.Second) / (requestsPerSecond * avgRequestsPerScenario))
		} else {
			s.timingController.requestInterval = time.Second / time.Duration(concurrency)
		}

		// Ensure minimum interval
		if s.timingController.requestInterval < time.Millisecond {
			s.timingController.requestInterval = time.Millisecond
		}

		log.Printf("Calculated request interval: %v (target RPS: %.2f)",
			s.timingController.requestInterval, requestsPerSecond)
	}
}

// scheduleNextRequest schedules the next request based on scenario weights
func (s *ExecutionScheduler) scheduleNextRequest() {
	// This is a placeholder - the actual request scheduling will be handled
	// by the RequestCoordinator. The scheduler primarily manages timing.

	s.timingController.mu.Lock()
	s.timingController.requestCounter++
	s.timingController.lastRequestTime = time.Now()
	s.timingController.mu.Unlock()

	// Update scenario selection using round-robin with weights
	s.selectNextScenario()
}

// selectNextScenario selects the next scenario based on weights
func (s *ExecutionScheduler) selectNextScenario() {
	s.scenarioManager.mu.Lock()
	defer s.scenarioManager.mu.Unlock()

	if len(s.scenarioManager.scenarios) == 0 {
		return
	}

	// Simple round-robin for now - can be enhanced with more sophisticated algorithms
	s.scenarioManager.currentScenario = (s.scenarioManager.currentScenario + 1) % len(s.scenarioManager.scenarios)

	// Update request count for the selected scenario
	scenarioName := s.scenarioManager.scenarios[s.scenarioManager.currentScenario].Name
	s.scenarioManager.requestCounts[scenarioName]++
	s.scenarioManager.totalRequests++
}

// GetCurrentScenario returns the currently selected scenario
func (s *ExecutionScheduler) GetCurrentScenario() *parser.Scenario {
	s.scenarioManager.mu.RLock()
	defer s.scenarioManager.mu.RUnlock()

	if len(s.scenarioManager.scenarios) == 0 {
		return nil
	}

	return &s.scenarioManager.scenarios[s.scenarioManager.currentScenario]
}

// GetScenarioStats returns statistics about scenario execution
func (s *ExecutionScheduler) GetScenarioStats() map[string]ScenarioStats {
	s.scenarioManager.mu.RLock()
	defer s.scenarioManager.mu.RUnlock()

	stats := make(map[string]ScenarioStats)

	for _, scenario := range s.scenarioManager.scenarios {
		weight := s.scenarioManager.weights[scenario.Name]
		requestCount := s.scenarioManager.requestCounts[scenario.Name]

		var percentage float64
		if s.scenarioManager.totalRequests > 0 {
			percentage = float64(requestCount) / float64(s.scenarioManager.totalRequests) * 100
		}

		stats[scenario.Name] = ScenarioStats{
			Name:               scenario.Name,
			Weight:             weight,
			RequestCount:       requestCount,
			Percentage:         percentage,
			RequestsInScenario: len(scenario.Requests),
		}
	}

	return stats
}

// GetRampUpProgress returns the current ramp-up progress
func (s *ExecutionScheduler) GetRampUpProgress() RampUpProgress {
	s.rampUpController.mu.RLock()
	defer s.rampUpController.mu.RUnlock()

	var progress float64
	if s.rampUpController.steps > 0 {
		progress = float64(s.rampUpController.currentStep) / float64(s.rampUpController.steps)
	}

	return RampUpProgress{
		CurrentStep:  s.rampUpController.currentStep,
		TotalSteps:   s.rampUpController.steps,
		Progress:     progress,
		CurrentLoad:  s.calculateCurrentLoad(),
		TargetLoad:   s.rampUpController.targetLoad,
		StepDuration: s.rampUpController.stepDuration,
	}
}

// calculateCurrentLoad calculates the current load during ramp-up
func (s *ExecutionScheduler) calculateCurrentLoad() int {
	if s.rampUpController.steps == 0 {
		return s.rampUpController.targetLoad
	}

	loadPerStep := float64(s.rampUpController.targetLoad) / float64(s.rampUpController.steps)
	currentLoad := int(math.Ceil(loadPerStep * float64(s.rampUpController.currentStep)))

	if currentLoad > s.rampUpController.targetLoad {
		currentLoad = s.rampUpController.targetLoad
	}

	return currentLoad
}

// GetTimingStats returns timing statistics
func (s *ExecutionScheduler) GetTimingStats() TimingStats {
	s.timingController.mu.RLock()
	defer s.timingController.mu.RUnlock()

	return TimingStats{
		RequestInterval: s.timingController.requestInterval,
		LastRequestTime: s.timingController.lastRequestTime,
		RequestCounter:  s.timingController.requestCounter,
		CurrentRPS:      s.calculateCurrentRPS(),
	}
}

// calculateCurrentRPS calculates the current requests per second
func (s *ExecutionScheduler) calculateCurrentRPS() float64 {
	if s.timingController.requestInterval == 0 {
		return 0
	}
	return float64(time.Second) / float64(s.timingController.requestInterval)
}

// ScenarioStats represents statistics for a scenario
type ScenarioStats struct {
	Name               string  `json:"name"`
	Weight             float64 `json:"weight"`
	RequestCount       int64   `json:"request_count"`
	Percentage         float64 `json:"percentage"`
	RequestsInScenario int     `json:"requests_in_scenario"`
}

// RampUpProgress represents the current ramp-up progress
type RampUpProgress struct {
	CurrentStep  int           `json:"current_step"`
	TotalSteps   int           `json:"total_steps"`
	Progress     float64       `json:"progress"` // 0.0 to 1.0
	CurrentLoad  int           `json:"current_load"`
	TargetLoad   int           `json:"target_load"`
	StepDuration time.Duration `json:"step_duration"`
}

// TimingStats represents timing statistics
type TimingStats struct {
	RequestInterval time.Duration `json:"request_interval"`
	LastRequestTime time.Time     `json:"last_request_time"`
	RequestCounter  int64         `json:"request_counter"`
	CurrentRPS      float64       `json:"current_rps"`
}
