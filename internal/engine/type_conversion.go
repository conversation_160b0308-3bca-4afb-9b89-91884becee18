package engine

import (
	"fmt"
	"strconv"
	"strings"
)

// ToInt converts a string to int
func ToInt(s string) (int, error) {
	s = strings.TrimSpace(s)
	if s == "" {
		return 0, fmt.Errorf("empty string cannot be converted to int")
	}
	return strconv.Atoi(s)
}

// ToIntSlice converts a []string to []int
func ToIntSlice(arr []string) ([]int, error) {
	res := make([]int, len(arr))
	for i, s := range arr {
		v, err := ToInt(s)
		if err != nil {
			return nil, fmt.Errorf("element %d: %w", i, err)
		}
		res[i] = v
	}
	return res, nil
}

// ToFloat converts a string to float64
func ToFloat(s string) (float64, error) {
	s = strings.TrimSpace(s)
	if s == "" {
		return 0, fmt.Errorf("empty string cannot be converted to float64")
	}
	return strconv.ParseFloat(s, 64)
}

// ToFloatSlice converts a []string to []float64
func ToFloatSlice(arr []string) ([]float64, error) {
	res := make([]float64, len(arr))
	for i, s := range arr {
		v, err := ToFloat(s)
		if err != nil {
			return nil, fmt.Errorf("element %d: %w", i, err)
		}
		res[i] = v
	}
	return res, nil
}

// ToBool converts a string to bool (accepts 1/0, true/false, yes/no, on/off)
func ToBool(s string) (bool, error) {
	s = strings.TrimSpace(strings.ToLower(s))
	switch s {
	case "1", "true", "yes", "on":
		return true, nil
	case "0", "false", "no", "off":
		return false, nil
	default:
		return false, fmt.Errorf("cannot convert '%s' to bool", s)
	}
}

// ToBoolSlice converts a []string to []bool
func ToBoolSlice(arr []string) ([]bool, error) {
	res := make([]bool, len(arr))
	for i, s := range arr {
		v, err := ToBool(s)
		if err != nil {
			return nil, fmt.Errorf("element %d: %w", i, err)
		}
		res[i] = v
	}
	return res, nil
}
