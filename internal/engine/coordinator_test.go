package engine

import (
	"neuralmetergo/internal/client"
	"neuralmetergo/internal/parser"
	"testing"
)

func TestExtractResponseVariables_XPathMultipleValues(t *testing.T) {
	coordinator := &RequestCoordinator{
		variableStore: &VariableStore{
			variables: make(map[string]interface{}),
		},
	}
	request := parser.Request{
		Extract: []parser.Extract{
			{Name: "item_value", Type: "xpath", Path: "/root/item"},
			{Name: "item_id", Type: "xpath", Path: "/root/item/@id"},
		},
	}
	xmlBody := []byte(`<root><item id="a">foo</item><item id="b">bar</item><item id="c">baz</item></root>`)
	response := &client.Response{
		StatusCode: 200,
		Body:       xmlBody,
		Headers:    map[string]string{"Content-Type": "application/xml"},
	}
	result := &RequestResult{Extracted: make(map[string]string)}

	err := coordinator.extractResponseVariables(request, response, result)
	if err != nil {
		t.Fatalf("Extraction error: %v", err)
	}

	coordinator.variableStore.mu.RLock()
	defer coordinator.variableStore.mu.RUnlock()

	if v, ok := coordinator.variableStore.variables["item_value"]; ok {
		arr, ok := v.([]string)
		if !ok {
			t.Fatalf("item_value not stored as []string, got: %T", v)
		}
		if len(arr) != 3 || arr[0] != "foo" || arr[1] != "bar" || arr[2] != "baz" {
			t.Errorf("item_value array mismatch: %v", arr)
		}
	} else {
		t.Errorf("item_value not found in variable store")
	}

	if v, ok := coordinator.variableStore.variables["item_id"]; ok {
		arr, ok := v.([]string)
		if !ok {
			t.Fatalf("item_id not stored as []string, got: %T", v)
		}
		if len(arr) != 3 || arr[0] != "a" || arr[1] != "b" || arr[2] != "c" {
			t.Errorf("item_id array mismatch: %v", arr)
		}
	} else {
		t.Errorf("item_id not found in variable store")
	}
}

func TestExtractResponseVariables_XPathTypeConversion(t *testing.T) {
	coordinator := &RequestCoordinator{
		variableStore: &VariableStore{
			variables: make(map[string]interface{}),
		},
	}
	request := parser.Request{
		Extract: []parser.Extract{
			{Name: "int_value", Type: "xpath", Path: "/root/item", As: "int"},
			{Name: "float_value", Type: "xpath", Path: "/root/price", As: "float"},
			{Name: "bool_value", Type: "xpath", Path: "/root/enabled", As: "bool"},
			{Name: "int_array", Type: "xpath", Path: "/root/nums/num", As: "int"},
			{Name: "bool_array", Type: "xpath", Path: "/root/bools/b", As: "bool"},
		},
	}
	xmlBody := []byte(`<root><item>42</item><price>3.14</price><enabled>true</enabled><nums><num>1</num><num>2</num><num>3</num></nums><bools><b>true</b><b>false</b><b>yes</b></bools></root>`)
	response := &client.Response{
		StatusCode: 200,
		Body:       xmlBody,
		Headers:    map[string]string{"Content-Type": "application/xml"},
	}
	result := &RequestResult{Extracted: make(map[string]string)}

	err := coordinator.extractResponseVariables(request, response, result)
	if err != nil {
		t.Fatalf("Extraction error: %v", err)
	}

	coordinator.variableStore.mu.RLock()
	defer coordinator.variableStore.mu.RUnlock()

	if v, ok := coordinator.variableStore.variables["int_value"]; ok {
		iv, ok := v.(int)
		if !ok || iv != 42 {
			t.Errorf("int_value: expected 42 (int), got %v (%T)", v, v)
		}
	} else {
		t.Errorf("int_value not found in variable store")
	}

	if v, ok := coordinator.variableStore.variables["float_value"]; ok {
		fv, ok := v.(float64)
		if !ok || fv != 3.14 {
			t.Errorf("float_value: expected 3.14 (float64), got %v (%T)", v, v)
		}
	} else {
		t.Errorf("float_value not found in variable store")
	}

	if v, ok := coordinator.variableStore.variables["bool_value"]; ok {
		bv, ok := v.(bool)
		if !ok || bv != true {
			t.Errorf("bool_value: expected true (bool), got %v (%T)", v, v)
		}
	} else {
		t.Errorf("bool_value not found in variable store")
	}

	if v, ok := coordinator.variableStore.variables["int_array"]; ok {
		arr, ok := v.([]int)
		if !ok || len(arr) != 3 || arr[0] != 1 || arr[2] != 3 {
			t.Errorf("int_array: expected [1 2 3] ([]int), got %v (%T)", v, v)
		}
	} else {
		t.Errorf("int_array not found in variable store")
	}

	if v, ok := coordinator.variableStore.variables["bool_array"]; ok {
		arr, ok := v.([]bool)
		if !ok || len(arr) != 3 || arr[0] != true || arr[1] != false || arr[2] != true {
			t.Errorf("bool_array: expected [true false true] ([]bool), got %v (%T)", v, v)
		}
	} else {
		t.Errorf("bool_array not found in variable store")
	}
}
