package engine

import (
	"testing"
)

func TestToInt(t *testing.T) {
	tests := []struct {
		input    string
		expected int
		wantErr  bool
	}{
		{"42", 42, false},
		{"  7 ", 7, false},
		{"-5", -5, false},
		{"", 0, true},
		{"abc", 0, true},
	}
	for _, tc := range tests {
		t.Run(tc.input, func(t *testing.T) {
			v, err := ToInt(tc.input)
			if tc.wantErr && err == nil {
				t.<PERSON>("expected error for input '%s'", tc.input)
			}
			if !tc.wantErr && v != tc.expected {
				t.<PERSON><PERSON>("expected %d, got %d", tc.expected, v)
			}
		})
	}
}

func TestToIntSlice(t *testing.T) {
	_, err := ToIntSlice([]string{"1", "2", "x"})
	if err == nil {
		t.Error("expected error for invalid element in slice")
	}
	v, err := ToIntSlice([]string{"3", "4", "5"})
	if err != nil || len(v) != 3 || v[0] != 3 || v[2] != 5 {
		t.<PERSON><PERSON>("unexpected result: %v, err: %v", v, err)
	}
}

func TestToFloat(t *testing.T) {
	tests := []struct {
		input    string
		expected float64
		wantErr  bool
	}{
		{"3.14", 3.14, false},
		{" 2.5 ", 2.5, false},
		{"-1.0", -1.0, false},
		{"", 0, true},
		{"abc", 0, true},
	}
	for _, tc := range tests {
		t.Run(tc.input, func(t *testing.T) {
			v, err := ToFloat(tc.input)
			if tc.wantErr && err == nil {
				t.Errorf("expected error for input '%s'", tc.input)
			}
			if !tc.wantErr && v != tc.expected {
				t.Errorf("expected %f, got %f", tc.expected, v)
			}
		})
	}
}

func TestToFloatSlice(t *testing.T) {
	_, err := ToFloatSlice([]string{"1.1", "2.2", "x"})
	if err == nil {
		t.Error("expected error for invalid element in slice")
	}
	v, err := ToFloatSlice([]string{"3.3", "4.4", "5.5"})
	if err != nil || len(v) != 3 || v[0] != 3.3 || v[2] != 5.5 {
		t.Errorf("unexpected result: %v, err: %v", v, err)
	}
}

func TestToBool(t *testing.T) {
	tests := []struct {
		input    string
		expected bool
		wantErr  bool
	}{
		{"true", true, false},
		{"1", true, false},
		{"yes", true, false},
		{"on", true, false},
		{"false", false, false},
		{"0", false, false},
		{"no", false, false},
		{"off", false, false},
		{"maybe", false, true},
		{"", false, true},
	}
	for _, tc := range tests {
		t.Run(tc.input, func(t *testing.T) {
			v, err := ToBool(tc.input)
			if tc.wantErr && err == nil {
				t.Errorf("expected error for input '%s'", tc.input)
			}
			if !tc.wantErr && v != tc.expected {
				t.Errorf("expected %v, got %v", tc.expected, v)
			}
		})
	}
}

func TestToBoolSlice(t *testing.T) {
	_, err := ToBoolSlice([]string{"true", "false", "x"})
	if err == nil {
		t.Error("expected error for invalid element in slice")
	}
	v, err := ToBoolSlice([]string{"true", "false", "yes"})
	if err != nil || len(v) != 3 || v[0] != true || v[1] != false || v[2] != true {
		t.Errorf("unexpected result: %v, err: %v", v, err)
	}
}
