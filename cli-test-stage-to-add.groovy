        stage('Run CLI Tests') {
            when {
                allOf {
                    not { params.SKIP_CLI_TESTS }
                    not { equals expected: 'none', actual: params.CLI_TEST_SUITE }
                }
            }
            steps {
                script {
                    echo "🧪 Running comprehensive CLI tests..."
                    echo "Test Suite: ${params.CLI_TEST_SUITE}"
                    
                    sshagent([JENKINS_CREDENTIALS_ID]) {
                        // Set environment variables for the test script
                        env.TEST_SUITE = params.CLI_TEST_SUITE
                        env.CLI_BINARY_PATH = "${DEPLOY_PATH}/${CLI_BINARY_NAME}"
                        env.SSH_USER = 'neuro'
                        
                        // Run the CLI test script
                        sh './test/cli-scripts/run-cli-tests-from-main-pipeline.sh'
                    }
                    
                    echo "✅ CLI tests completed"
                }
            }
            post {
                always {
                    // Archive CLI test results
                    archiveArtifacts artifacts: 'test-results/**/*', allowEmptyArchive: true
                }
            }
        }