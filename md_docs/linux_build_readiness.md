# Linux Build Readiness Checklist

## Pre-Transfer Verification ✅

### Code Quality Checks
- ✅ **Compilation Success**: `go build ./...` completes without errors on macOS
- ✅ **Dependencies Verified**: `go mod tidy && go mod verify` successful  
- ✅ **No Relative Imports**: All imports use proper module paths
- ✅ **Build Tags Properly Set**: All GPU backends have correct `//go:build` tags
- ✅ **No Circular Dependencies**: Fixed Metal backend import cycles

### GPU Backend Implementation Status

#### CUDA Backend ✅ READY
- ✅ **Core Implementation**: `internal/gpu/backends/cuda_backend.go`
- ✅ **Context Management**: `internal/gpu/backends/cuda_context.go`
- ✅ **Memory Management**: `internal/gpu/backends/cuda_memory.go`
- ✅ **Linux-Specific Code**: `internal/gpu/cuda_linux.go`
- ✅ **Build Tags**: `//go:build (linux || windows) && cuda`
- ✅ **CGO Directives**: Proper CUDA library linking
```go
#cgo CFLAGS: -I/usr/local/cuda/include
#cgo LDFLAGS: -L/usr/local/cuda/lib64 -lcuda -lcudart
```

#### ROCm Backend ✅ READY  
- ✅ **Core Implementation**: `internal/gpu/backends/rocm_backend.go`
- ✅ **Build Tags**: `//go:build rocm`
- ✅ **CGO Directives**: Proper ROCm library linking
```go
#cgo CFLAGS: -I/opt/rocm/include
#cgo LDFLAGS: -L/opt/rocm/lib -lhip -lhsa-runtime64
```

#### OpenCL Backend ✅ READY
- ✅ **Core Implementation**: `internal/gpu/opencl_backend.go` 
- ✅ **Build Tags**: `//go:build opencl`
- ✅ **CGO Directives**: Cross-platform OpenCL linking
```go
#cgo CFLAGS: -I/usr/include -I/opt/intel/opencl/include
#cgo LDFLAGS: -lOpenCL
```

#### oneAPI Backend ✅ READY
- ✅ **Core Implementation**: `internal/gpu/backends/oneapi_backend.go`
- ✅ **Build Tags**: `//go:build oneapi`
- ✅ **Abstraction Layer**: Integrated with unified interface

### Integration Layer ✅ READY
- ✅ **Abstraction Manager**: `internal/gpu/abstraction_manager.go`
- ✅ **Backend Registry**: Platform-specific backend registration
- ✅ **Unified Interface**: All backends implement `types.GPUBackend`
- ✅ **Detection Bridge**: Conversion between old and new systems
- ✅ **Error Handling**: Consistent error types and reporting

## Linux Environment Requirements

### System Prerequisites
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y build-essential git

# Go installation (if not present)
wget https://go.dev/dl/go1.21.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.21.linux-amd64.tar.gz
export PATH=$PATH:/usr/local/go/bin
```

### CUDA Installation (NVIDIA GPUs)
```bash
# Add NVIDIA package repository
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/x86_64/cuda-keyring_1.0-1_all.deb
sudo dpkg -i cuda-keyring_1.0-1_all.deb
sudo apt-get update

# Install CUDA Toolkit
sudo apt-get install -y cuda-toolkit-12-0

# Set environment variables
export CUDA_ROOT=/usr/local/cuda
export PATH=$CUDA_ROOT/bin:$PATH
export LD_LIBRARY_PATH=$CUDA_ROOT/lib64:$LD_LIBRARY_PATH
```

### ROCm Installation (AMD GPUs)
```bash
# Add ROCm repository
wget -q -O - https://repo.radeon.com/rocm/rocm.gpg.key | sudo apt-key add -
echo 'deb [arch=amd64] https://repo.radeon.com/rocm/apt/5.7/ ubuntu main' | sudo tee /etc/apt/sources.list.d/rocm.list
sudo apt-get update

# Install ROCm
sudo apt-get install -y rocm-dev hip-dev

# Set environment variables
export ROCM_ROOT=/opt/rocm
export PATH=$ROCM_ROOT/bin:$PATH
export LD_LIBRARY_PATH=$ROCM_ROOT/lib:$LD_LIBRARY_PATH
```

### OpenCL Installation
```bash
# Install OpenCL headers and development libraries
sudo apt-get install -y opencl-headers opencl-dev

# For Intel GPUs (optional)
sudo apt-get install -y intel-opencl-icd
```

### oneAPI Installation (Intel GPUs)
```bash
# Add Intel repository
wget -O- https://apt.repos.intel.com/intel-gpg-keys/GPG-PUB-KEY-INTEL-SW-PRODUCTS.PUB | gpg --dearmor | sudo tee /usr/share/keyrings/oneapi-archive-keyring.gpg > /dev/null
echo "deb [signed-by=/usr/share/keyrings/oneapi-archive-keyring.gpg] https://apt.repos.intel.com/oneapi all main" | sudo tee /etc/apt/sources.list.d/oneAPI.list
sudo apt-get update

# Install oneAPI Base Toolkit
sudo apt-get install -y intel-basekit

# Source environment
source /opt/intel/oneapi/setvars.sh
```

## Linux Testing Protocol

### 1. Code Transfer
```bash
# Method 1: Git clone (recommended)
git clone <repository-url>
cd NeuralMeterGo

# Method 2: SCP transfer
scp -r /path/to/NeuralMeterGo user@linux-machine:/path/to/destination/
```

### 2. Environment Setup
```bash
# Set Go environment
export GOPATH=$HOME/go
export PATH=$PATH:/usr/local/go/bin:$GOPATH/bin

# Set GPU environment variables (as per installation above)
export CUDA_ROOT=/usr/local/cuda
export ROCM_ROOT=/opt/rocm
export PATH=$CUDA_ROOT/bin:$ROCM_ROOT/bin:$PATH
export LD_LIBRARY_PATH=$CUDA_ROOT/lib64:$ROCM_ROOT/lib:$LD_LIBRARY_PATH

# Source oneAPI (if installed)
source /opt/intel/oneapi/setvars.sh
```

### 3. Compilation Testing
```bash
# Test basic compilation (no GPU backends)
go build ./...

# Test individual backends
go build -tags "cuda" ./...
go build -tags "rocm" ./...  
go build -tags "opencl" ./...
go build -tags "oneapi" ./...

# Test combined backends
go build -tags "cuda rocm opencl oneapi" ./...
```

### 4. Runtime Testing
```bash
# Build with all available backends
go build -tags "cuda rocm opencl oneapi" -o neuralmeter ./cmd/neuralmeter

# Test GPU detection
./neuralmeter gpu list

# Test specific backends (if available)
./neuralmeter gpu list --backend=cuda
./neuralmeter gpu list --backend=rocm
./neuralmeter gpu list --backend=opencl

# Test GPU capabilities
./neuralmeter gpu test
```

### 5. Validation Checklist

#### Compilation Validation
- [ ] **Basic Build**: `go build ./...` succeeds
- [ ] **CUDA Build**: `go build -tags "cuda" ./...` succeeds (if CUDA installed)
- [ ] **ROCm Build**: `go build -tags "rocm" ./...` succeeds (if ROCm installed)
- [ ] **OpenCL Build**: `go build -tags "opencl" ./...` succeeds
- [ ] **oneAPI Build**: `go build -tags "oneapi" ./...` succeeds (if oneAPI installed)
- [ ] **Combined Build**: All available backends compile together

#### Runtime Validation
- [ ] **GPU Detection**: `./neuralmeter gpu list` shows available GPUs
- [ ] **Backend Registration**: Appropriate backends appear in output
- [ ] **Device Information**: GPU memory, compute units, capabilities shown correctly
- [ ] **Error Handling**: Graceful handling when backends unavailable

#### Performance Validation
- [ ] **Memory Allocation**: GPU memory operations work correctly
- [ ] **Compute Operations**: Basic GPU computations execute successfully  
- [ ] **Multi-GPU Support**: Multiple GPUs detected and accessible (if available)
- [ ] **Backend Switching**: Can switch between available backends

## Expected Output Examples

### Successful GPU Detection
```bash
$ ./neuralmeter gpu list
Available GPU Backends: CUDA, ROCm, OpenCL
GPU 0: GeForce RTX 4090, Vendor: NVIDIA, Memory: 24.0 GB, Type: CUDA
GPU 1: Radeon RX 7900 XT, Vendor: AMD, Memory: 20.0 GB, Type: ROCm  
GPU 2: Intel Arc A770, Vendor: Intel, Memory: 16.0 GB, Type: OpenCL
```

### Backend-Specific Detection
```bash
$ ./neuralmeter gpu list --backend=cuda
CUDA Backend:
GPU 0: GeForce RTX 4090
  Memory: 24576 MB (24.0 GB)
  Compute Capability: 8.9
  Multiprocessors: 128
  CUDA Cores: 16384
```

## Troubleshooting Guide

### Common Issues

#### 1. CUDA Compilation Errors
```bash
# Error: cuda.h not found
export CUDA_ROOT=/usr/local/cuda
export CPATH=$CUDA_ROOT/include:$CPATH

# Error: libcuda.so not found  
export LD_LIBRARY_PATH=$CUDA_ROOT/lib64:$LD_LIBRARY_PATH
sudo ldconfig
```

#### 2. ROCm Compilation Errors
```bash
# Error: hip headers not found
export ROCM_ROOT=/opt/rocm
export CPATH=$ROCM_ROOT/include:$CPATH

# Error: libhip.so not found
export LD_LIBRARY_PATH=$ROCM_ROOT/lib:$LD_LIBRARY_PATH
```

#### 3. OpenCL Compilation Errors
```bash
# Error: CL/cl.h not found
sudo apt-get install opencl-headers opencl-dev

# Error: libOpenCL.so not found
sudo apt-get install ocl-icd-opencl-dev
```

#### 4. Runtime Detection Issues
```bash
# No GPUs detected
# Check driver installation
nvidia-smi  # For NVIDIA
rocm-smi    # For AMD
clinfo      # For OpenCL

# Verify library loading
ldd ./neuralmeter | grep -E "(cuda|hip|OpenCL)"
```

## Success Criteria

### Minimum Requirements (Pass)
- ✅ Code compiles successfully on Linux
- ✅ At least one GPU backend functional
- ✅ GPU detection works for available hardware
- ✅ Basic GPU operations execute without errors

### Optimal Requirements (Excellent)
- ✅ All relevant backends compile and function
- ✅ Multiple GPUs detected correctly
- ✅ Performance metrics within expected ranges
- ✅ Error handling robust across all scenarios
- ✅ Memory management stable under load

## Post-Testing Actions

### If Successful ✅
1. **Update Task Status**: Mark Task 69.14 (Linux Binary) as DONE
2. **Document Results**: Record GPU detection output and performance
3. **Commit Changes**: Any Linux-specific fixes or optimizations
4. **Prepare Windows Testing**: Transfer code to Windows environment

### If Issues Found ⚠️
1. **Document Problems**: Record specific errors and environment details
2. **Create Fix Tasks**: Break down issues into addressable subtasks
3. **Update Code**: Implement fixes and test locally if possible
4. **Re-test**: Verify fixes resolve issues
5. **Update Documentation**: Improve troubleshooting guide

## Conclusion

The NeuralMeter codebase is **READY FOR LINUX COMPILATION AND TESTING**. All GPU backends have proper implementations, build tags, and CGO directives. The main requirements are:

1. **Linux Environment**: Ubuntu/Debian with build tools
2. **GPU Drivers**: CUDA, ROCm, and/or OpenCL as appropriate for hardware
3. **Testing Protocol**: Follow the systematic testing approach outlined above

The code architecture is sound and should compile successfully on Linux with appropriate dependencies installed. 