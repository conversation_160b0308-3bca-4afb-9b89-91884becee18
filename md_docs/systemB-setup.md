# Enhanced System B Setup for NeuralMeter CLI Testing

## Overview

This document provides a comprehensive, robust solution for **System B** (remote target services) to support testing of the NeuralMeter CLI across all Task 6 subtasks. The setup includes HTTP/HTTPS protocols, WebSocket services, SSL/TLS, load balancing, rate limiting, and comprehensive monitoring.

## Task Coverage Matrix

| Subtask | Component | Addressed |
|---------|-----------|-----------|
| **6.1** | CLI Command Structure | ✅ Basic HTTP endpoints for CLI testing |
| **6.2** | Server Daemon Mode | ✅ Long-running services for daemon lifecycle testing |
| **6.3** | Remote API Server | ✅ RESTful endpoints for API orchestration testing |
| **6.4** | Test Plan Processing | ✅ Varied endpoints for YAML test plan execution |
| **6.5** | GPU-Accelerated Load Testing | ✅ High-performance endpoints for GPU load testing |
| **6.6** | HTTP Load Generation Engine | ✅ Connection pooling and sustained load endpoints |
| **6.7** | Real-time Result Streaming | ✅ WebSocket and SSE endpoints for streaming tests |
| **6.8** | GPU Command Integration | ✅ Performance benchmarking endpoints |
| **6.9** | Configuration Management | ✅ Environment-specific endpoints |
| **6.10** | Output Formatting and Logging | ✅ Structured response formats |

## Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        System B - Target Services               │
├─────────────────────────────────────────────────────────────────┤
│ Load Balancer (HAProxy)                                        │
│ ├── HTTP/HTTPS (80/443) ──────┬─────────────────────────────────┤
│ ├── WebSocket (8080)          │                                 │
│ └── Monitoring (9090)         │                                 │
├───────────────────────────────┼─────────────────────────────────┤
│ Application Services          │                                 │
│ ├── nginx (Reverse Proxy)     │ Web Server Layer               │
│ ├── Backend API (8001-8003)   │ Application Layer              │
│ ├── WebSocket Server (8080)   │ Real-time Layer                │
│ ├── Rate Limiter (Redis)      │ Control Layer                  │
│ └── Monitoring (Prometheus)   │ Observability Layer            │
├───────────────────────────────┼─────────────────────────────────┤
│ Database Layer                │                                 │
│ ├── PostgreSQL (5432)         │ Persistent Storage             │
│ └── Redis (6379)              │ Cache & Rate Limiting          │
└─────────────────────────────────────────────────────────────────┘
```

## System Requirements

### Hardware Specifications
- **OS**: Ubuntu 22.04 LTS or RHEL 9+
- **CPU**: 8+ cores (16+ recommended for high-load testing)
- **RAM**: 16GB+ (32GB recommended for sustained load)
- **Network**: 10GbE (1GbE minimum)
- **Storage**: NVMe SSD with 200GB+ available space

### Network Configuration
```bash
# Increase network limits for high-load testing
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.core.netdev_max_backlog = 5000' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 8192' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_fin_timeout = 30' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_keepalive_time = 120' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_tw_reuse = 1' >> /etc/sysctl.conf
echo 'fs.file-max = 1000000' >> /etc/sysctl.conf
sysctl -p
```

## Installation & Setup

### 1. Base System Setup

```bash
#!/bin/bash
# setup-system-b.sh - Comprehensive System B Setup Script

set -euo pipefail

LOG_FILE="/var/log/neuralmeter-setup.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

echo "=== NeuralMeter System B Setup Started $(date) ==="

# Update system and install base packages
apt update && apt upgrade -y
apt install -y \
    curl wget git vim htop iotop \
    build-essential software-properties-common \
    certbot nginx-full \
    postgresql-14 postgresql-contrib \
    redis-server redis-tools \
    python3 python3-pip python3-venv \
    nodejs npm \
    docker.io docker-compose \
    prometheus prometheus-node-exporter \
    haproxy \
    ssl-cert \
    jq ncdu tree \
    net-tools tcpdump wireshark-common

# Install Go 1.21+ for custom services
wget https://go.dev/dl/go1.21.6.linux-amd64.tar.gz -O /tmp/go.tar.gz
tar -C /usr/local -xzf /tmp/go.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> /etc/profile
export PATH=$PATH:/usr/local/go/bin

# Create service user
useradd -r -s /bin/bash -d /opt/neuralmeter neuralmeter
mkdir -p /opt/neuralmeter/{bin,config,logs,data}
chown -R neuralmeter:neuralmeter /opt/neuralmeter

echo "=== Base system setup completed ==="
```

### 2. SSL/TLS Certificate Setup

```bash
#!/bin/bash
# setup-certificates.sh

# Create certificate directory
mkdir -p /etc/neuralmeter/ssl

# Generate root CA
openssl genrsa -out /etc/neuralmeter/ssl/ca.key 4096
openssl req -new -x509 -days 3650 -key /etc/neuralmeter/ssl/ca.key \
    -out /etc/neuralmeter/ssl/ca.crt \
    -subj "/C=US/ST=Test/L=Test/O=NeuralMeter/CN=NeuralMeter-CA"

# Generate server certificate
openssl genrsa -out /etc/neuralmeter/ssl/server.key 2048
openssl req -new -key /etc/neuralmeter/ssl/server.key \
    -out /etc/neuralmeter/ssl/server.csr \
    -subj "/C=US/ST=Test/L=Test/O=NeuralMeter/CN=neuralmeter.local"

# Create server certificate with SAN
cat > /etc/neuralmeter/ssl/server.conf << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = Test
L = Test
O = NeuralMeter
CN = neuralmeter.local

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = neuralmeter.local
DNS.2 = system-b
DNS.3 = localhost
IP.1 = 127.0.0.1
IP.2 = 10.0.0.0/8
IP.3 = **********/12
IP.4 = ***********/16
EOF

openssl x509 -req -in /etc/neuralmeter/ssl/server.csr \
    -CA /etc/neuralmeter/ssl/ca.crt \
    -CAkey /etc/neuralmeter/ssl/ca.key \
    -CAcreateserial -out /etc/neuralmeter/ssl/server.crt \
    -days 365 -extensions v3_req \
    -extfile /etc/neuralmeter/ssl/server.conf

# Set permissions
chmod 600 /etc/neuralmeter/ssl/*.key
chmod 644 /etc/neuralmeter/ssl/*.crt
chown -R neuralmeter:neuralmeter /etc/neuralmeter/ssl
```

### 3. HAProxy Load Balancer Configuration

```bash
# /etc/haproxy/haproxy.cfg
cat > /etc/haproxy/haproxy.cfg << 'EOF'
global
    log stdout local0
    chroot /var/lib/haproxy
    stats socket /run/haproxy/admin.sock mode 660 level admin
    stats timeout 30s
    user haproxy
    group haproxy
    daemon
    
    # Tuning for high performance
    maxconn 100000
    nbproc 1
    nbthread 8
    
    # SSL Configuration
    ssl-default-bind-ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256
    ssl-default-bind-options ssl-min-ver TLSv1.2 no-tls-tickets

defaults
    mode http
    log global
    option httplog
    option dontlognull
    option http-server-close
    option forwardfor except *********/8
    option redispatch
    retries 3
    timeout http-request 10s
    timeout queue 1m
    timeout connect 10s
    timeout client 1m
    timeout server 1m
    timeout http-keep-alive 10s
    timeout check 10s
    maxconn 50000

# Statistics interface
listen stats
    bind *:8404
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE

# HTTP Frontend (Subtasks 6.1, 6.3, 6.4, 6.6, 6.10)
frontend http_frontend
    bind *:80
    bind *:443 ssl crt /etc/neuralmeter/ssl/server.pem
    redirect scheme https code 301 if !{ ssl_fc }
    
    # Rate limiting per IP
    stick-table type ip size 100k expire 30s store http_req_rate(10s)
    http-request track-sc0 src
    http-request reject if { sc_http_req_rate(0) gt 1000 }
    
    # Route to appropriate backend
    acl is_api path_beg /api/
    acl is_health path_beg /health
    acl is_metrics path_beg /metrics
    acl is_websocket hdr(Upgrade) -i websocket
    
    use_backend websocket_backend if is_websocket
    use_backend api_backend if is_api
    use_backend health_backend if is_health
    use_backend metrics_backend if is_metrics
    default_backend web_backend

# Web Backend (nginx)
backend web_backend
    balance roundrobin
    option httpchk GET /health
    server nginx1 127.0.0.1:8001 check
    server nginx2 127.0.0.1:8002 check
    server nginx3 127.0.0.1:8003 check

# API Backend (Subtasks 6.3, 6.4)
backend api_backend
    balance leastconn
    option httpchk GET /api/health
    server api1 127.0.0.1:8011 check
    server api2 127.0.0.1:8012 check
    server api3 127.0.0.1:8013 check

# Health Check Backend
backend health_backend
    server health1 127.0.0.1:8021 check

# Metrics Backend (Subtask 6.8)
backend metrics_backend
    server prometheus 127.0.0.1:9090 check

# WebSocket Backend (Subtask 6.7)
backend websocket_backend
    balance source
    option httpchk GET /ws/health
    server ws1 127.0.0.1:8080 check
    server ws2 127.0.0.1:8081 check
EOF

# Create combined certificate for HAProxy
cat /etc/neuralmeter/ssl/server.crt /etc/neuralmeter/ssl/server.key > /etc/neuralmeter/ssl/server.pem
chmod 600 /etc/neuralmeter/ssl/server.pem
```

### 4. nginx Reverse Proxy Setup

```bash
# /etc/nginx/sites-available/neuralmeter
cat > /etc/nginx/sites-available/neuralmeter << 'EOF'
# nginx configuration optimized for high-performance testing

# Upstream definitions for load balancing
upstream backend_pool {
    least_conn;
    server 127.0.0.1:8001 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8002 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8003 max_fails=3 fail_timeout=30s;
    keepalive 100;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=1000r/s;
limit_req_zone $binary_remote_addr zone=general_limit:10m rate=2000r/s;
limit_conn_zone $binary_remote_addr zone=conn_limit:10m;

# Server block for HTTP/HTTPS
server {
    listen 8001 default_server;
    listen 8002;
    listen 8003;
    server_name neuralmeter.local system-b localhost;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # Performance tuning
    client_max_body_size 100M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    keepalive_timeout 65s;
    keepalive_requests 1000;
    
    # Rate limiting
    limit_req zone=general_limit burst=50 nodelay;
    limit_conn conn_limit 1000;
    
    # Root directory
    root /opt/neuralmeter/web;
    index index.html index.htm;
    
    # Health check endpoint (Subtasks 6.1, 6.2, 6.3)
    location /health {
        access_log off;
        return 200 '{"status":"healthy","timestamp":"$time_iso8601","server":"$hostname"}';
        add_header Content-Type application/json;
    }
    
    # Detailed health check with system info (Subtask 6.8)
    location /health/detailed {
        content_by_lua_block {
            local cjson = require "cjson"
            local health_data = {
                status = "healthy",
                timestamp = ngx.time(),
                server = ngx.var.hostname,
                connections = {
                    active = ngx.var.connections_active,
                    reading = ngx.var.connections_reading,
                    writing = ngx.var.connections_writing,
                    waiting = ngx.var.connections_waiting
                },
                load = {
                    cpu_count = 8,  -- Will be dynamic in real implementation
                    memory_total = "16GB"
                }
            }
            ngx.header.content_type = "application/json"
            ngx.say(cjson.encode(health_data))
        }
    }
    
    # API endpoints (Subtasks 6.3, 6.4, 6.10)
    location /api/ {
        limit_req zone=api_limit burst=100 nodelay;
        
        # CORS headers for cross-origin testing
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Authorization, Content-Type, X-Requested-With" always;
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            return 204;
        }
        
        proxy_pass http://backend_pool;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Connection pooling
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # Load testing endpoints (Subtasks 6.5, 6.6)
    location /load/ {
        # High-performance configuration
        proxy_pass http://backend_pool;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_buffering off;
        proxy_connect_timeout 1s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Metrics endpoint (Subtask 6.8)
    location /metrics {
        access_log off;
        proxy_pass http://127.0.0.1:9090/metrics;
    }
    
    # Static content for testing
    location /static/ {
        expires 1h;
        add_header Cache-Control "public, immutable";
        try_files $uri $uri/ =404;
    }
    
    # WebSocket proxy (Subtask 6.7)
    location /ws/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_read_timeout 86400s;
        proxy_send_timeout 86400s;
    }
    
    # Default location for general testing
    location / {
        try_files $uri $uri/ @backend;
    }
    
    location @backend {
        proxy_pass http://backend_pool;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/neuralmeter /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Create web directory
mkdir -p /opt/neuralmeter/web
echo '<!DOCTYPE html><html><head><title>NeuralMeter Test Server</title></head><body><h1>System B - Ready for Testing</h1><p>Server Time: <span id="time"></span></p><script>document.getElementById("time").textContent = new Date().toISOString();</script></body></html>' > /opt/neuralmeter/web/index.html
```

### 5. Backend API Services (Go Implementation)

```go
// /opt/neuralmeter/bin/api-server.go
package main

import (
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "os"
    "strconv"
    "sync/atomic"
    "time"
    
    "github.com/gorilla/mux"
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promhttp"
)

var (
    requestCounter = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status"},
    )
    
    requestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "http_request_duration_seconds",
            Help: "HTTP request duration in seconds",
            Buckets: prometheus.DefBuckets,
        },
        []string{"method", "endpoint"},
    )
    
    activeConnections int64
)

type HealthResponse struct {
    Status      string    `json:"status"`
    Timestamp   time.Time `json:"timestamp"`
    ServerPort  string    `json:"server_port"`
    ActiveConns int64     `json:"active_connections"`
    Uptime      string    `json:"uptime"`
}

type LoadTestData struct {
    ID       string                 `json:"id"`
    Data     map[string]interface{} `json:"data"`
    Size     int                    `json:"size"`
    Generate bool                   `json:"generate,omitempty"`
}

func init() {
    prometheus.MustRegister(requestCounter)
    prometheus.MustRegister(requestDuration)
}

func middlewareMetrics(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        start := time.Now()
        atomic.AddInt64(&activeConnections, 1)
        defer atomic.AddInt64(&activeConnections, -1)
        
        ww := &responseWriter{ResponseWriter: w, statusCode: 200}
        next.ServeHTTP(ww, r)
        
        duration := time.Since(start).Seconds()
        requestDuration.WithLabelValues(r.Method, r.URL.Path).Observe(duration)
        requestCounter.WithLabelValues(r.Method, r.URL.Path, fmt.Sprintf("%d", ww.statusCode)).Inc()
    })
}

type responseWriter struct {
    http.ResponseWriter
    statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
    rw.statusCode = code
    rw.ResponseWriter.WriteHeader(code)
}

func healthHandler(startTime time.Time) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        port := os.Getenv("PORT")
        if port == "" {
            port = "8001"
        }
        
        response := HealthResponse{
            Status:      "healthy",
            Timestamp:   time.Now(),
            ServerPort:  port,
            ActiveConns: atomic.LoadInt64(&activeConnections),
            Uptime:      time.Since(startTime).String(),
        }
        
        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(response)
    }
}

func apiDataHandler(w http.ResponseWriter, r *http.Request) {
    switch r.Method {
    case "GET":
        size := 1024 // Default size
        if s := r.URL.Query().Get("size"); s != "" {
            if parsed, err := strconv.Atoi(s); err == nil && parsed > 0 {
                size = parsed
            }
        }
        
        data := make(map[string]interface{})
        data["message"] = "Generated test data"
        data["timestamp"] = time.Now()
        data["size"] = size
        
        // Generate payload of requested size
        if size > 100 {
            padding := make([]byte, size-100)
            for i := range padding {
                padding[i] = byte('a' + (i % 26))
            }
            data["padding"] = string(padding)
        }
        
        response := LoadTestData{
            ID:   fmt.Sprintf("data-%d", time.Now().UnixNano()),
            Data: data,
            Size: size,
        }
        
        w.Header().Set("Content-Type", "application/json")
        json.NewEncoder(w).Encode(response)
        
    case "POST":
        var payload LoadTestData
        if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
            http.Error(w, "Invalid JSON", http.StatusBadRequest)
            return
        }
        
        // Simulate processing time
        if payload.Generate {
            time.Sleep(time.Millisecond * 10)
        }
        
        payload.ID = fmt.Sprintf("processed-%d", time.Now().UnixNano())
        
        w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(http.StatusCreated)
        json.NewEncoder(w).Encode(payload)
        
    default:
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
    }
}

func loadTestHandler(w http.ResponseWriter, r *http.Request) {
    // High-performance endpoint for load testing
    vars := mux.Vars(r)
    testType := vars["type"]
    
    switch testType {
    case "fast":
        w.Header().Set("Content-Type", "application/json")
        fmt.Fprintf(w, `{"status":"ok","type":"fast","timestamp":%d}`, time.Now().UnixNano())
        
    case "slow":
        time.Sleep(time.Millisecond * 100)
        w.Header().Set("Content-Type", "application/json")
        fmt.Fprintf(w, `{"status":"ok","type":"slow","timestamp":%d}`, time.Now().UnixNano())
        
    case "cpu":
        // CPU-intensive task
        sum := 0
        for i := 0; i < 100000; i++ {
            sum += i
        }
        w.Header().Set("Content-Type", "application/json")
        fmt.Fprintf(w, `{"status":"ok","type":"cpu","result":%d,"timestamp":%d}`, sum, time.Now().UnixNano())
        
    default:
        http.Error(w, "Unknown test type", http.StatusBadRequest)
    }
}

func main() {
    port := os.Getenv("PORT")
    if port == "" {
        port = "8001"
    }
    
    startTime := time.Now()
    
    r := mux.NewRouter()
    r.Use(middlewareMetrics)
    
    // Health endpoints
    r.HandleFunc("/health", healthHandler(startTime))
    r.HandleFunc("/api/health", healthHandler(startTime))
    
    // API endpoints
    r.HandleFunc("/api/data", apiDataHandler)
    r.HandleFunc("/api/data/{id}", apiDataHandler)
    
    // Load testing endpoints
    r.HandleFunc("/load/{type}", loadTestHandler)
    
    // Metrics endpoint
    r.Handle("/metrics", promhttp.Handler())
    
    // Static content
    r.PathPrefix("/static/").Handler(http.StripPrefix("/static/", http.FileServer(http.Dir("/opt/neuralmeter/web/static/"))))
    
    log.Printf("Starting API server on port %s", port)
    log.Fatal(http.ListenAndServe(":"+port, r))
}
```

### 6. WebSocket Server (Subtask 6.7)

```go
// /opt/neuralmeter/bin/websocket-server.go
package main

import (
    "encoding/json"
    "log"
    "net/http"
    "sync"
    "time"
    
    "github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
    CheckOrigin: func(r *http.Request) bool {
        return true // Allow all origins for testing
    },
}

type Client struct {
    conn *websocket.Conn
    send chan []byte
}

type Hub struct {
    clients    map[*Client]bool
    broadcast  chan []byte
    register   chan *Client
    unregister chan *Client
    mutex      sync.RWMutex
}

type Message struct {
    Type      string      `json:"type"`
    Timestamp time.Time   `json:"timestamp"`
    Data      interface{} `json:"data"`
}

func NewHub() *Hub {
    return &Hub{
        clients:    make(map[*Client]bool),
        broadcast:  make(chan []byte),
        register:   make(chan *Client),
        unregister: make(chan *Client),
    }
}

func (h *Hub) Run() {
    for {
        select {
        case client := <-h.register:
            h.mutex.Lock()
            h.clients[client] = true
            h.mutex.Unlock()
            
            // Send welcome message
            welcome := Message{
                Type:      "welcome",
                Timestamp: time.Now(),
                Data:      map[string]interface{}{"message": "Connected to WebSocket server"},
            }
            data, _ := json.Marshal(welcome)
            select {
            case client.send <- data:
            default:
                close(client.send)
                delete(h.clients, client)
            }
            
        case client := <-h.unregister:
            h.mutex.Lock()
            if _, ok := h.clients[client]; ok {
                delete(h.clients, client)
                close(client.send)
            }
            h.mutex.Unlock()
            
        case message := <-h.broadcast:
            h.mutex.RLock()
            for client := range h.clients {
                select {
                case client.send <- message:
                default:
                    close(client.send)
                    delete(h.clients, client)
                }
            }
            h.mutex.RUnlock()
        }
    }
}

func (h *Hub) ServeWS(w http.ResponseWriter, r *http.Request) {
    conn, err := upgrader.Upgrade(w, r, nil)
    if err != nil {
        log.Println(err)
        return
    }
    
    client := &Client{conn: conn, send: make(chan []byte, 256)}
    h.register <- client
    
    go client.writePump(h)
    go client.readPump(h)
}

func (c *Client) readPump(hub *Hub) {
    defer func() {
        hub.unregister <- c
        c.conn.Close()
    }()
    
    c.conn.SetReadLimit(512)
    c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
    c.conn.SetPongHandler(func(string) error {
        c.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
        return nil
    })
    
    for {
        _, message, err := c.conn.ReadMessage()
        if err != nil {
            break
        }
        
        // Echo message back with timestamp
        response := Message{
            Type:      "echo",
            Timestamp: time.Now(),
            Data:      map[string]interface{}{"received": string(message)},
        }
        data, _ := json.Marshal(response)
        hub.broadcast <- data
    }
}

func (c *Client) writePump(hub *Hub) {
    ticker := time.NewTicker(54 * time.Second)
    defer func() {
        ticker.Stop()
        c.conn.Close()
    }()
    
    for {
        select {
        case message, ok := <-c.send:
            c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
            if !ok {
                c.conn.WriteMessage(websocket.CloseMessage, []byte{})
                return
            }
            
            w, err := c.conn.NextWriter(websocket.TextMessage)
            if err != nil {
                return
            }
            w.Write(message)
            
            // Add queued chat messages to the current websocket message
            n := len(c.send)
            for i := 0; i < n; i++ {
                w.Write([]byte{'\n'})
                w.Write(<-c.send)
            }
            
            if err := w.Close(); err != nil {
                return
            }
            
        case <-ticker.C:
            c.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
            if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
                return
            }
        }
    }
}

func healthHandler(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(http.StatusOK)
    json.NewEncoder(w).Encode(map[string]interface{}{
        "status":    "healthy",
        "timestamp": time.Now(),
        "service":   "websocket-server",
    })
}

func main() {
    hub := NewHub()
    go hub.Run()
    
    // Start metrics broadcaster
    go func() {
        ticker := time.NewTicker(5 * time.Second)
        defer ticker.Stop()
        
        for range ticker.C {
            metrics := Message{
                Type:      "metrics",
                Timestamp: time.Now(),
                Data: map[string]interface{}{
                    "connected_clients": len(hub.clients),
                    "server_time":      time.Now().Unix(),
                    "memory_usage":     "simulated", // In real implementation, use runtime.ReadMemStats
                },
            }
            data, _ := json.Marshal(metrics)
            hub.broadcast <- data
        }
    }()
    
    http.HandleFunc("/ws", hub.ServeWS)
    http.HandleFunc("/ws/health", healthHandler)
    
    log.Println("WebSocket server starting on :8080")
    log.Fatal(http.ListenAndServe(":8080", nil))
}
```

### 7. Service Deployment Scripts

```bash
#!/bin/bash
# deploy-services.sh - Deploy all System B services

set -euo pipefail

NEURALMETER_DIR="/opt/neuralmeter"
BIN_DIR="$NEURALMETER_DIR/bin"
CONFIG_DIR="$NEURALMETER_DIR/config"

echo "=== Building Go services ==="

# Build API server
cd /tmp
cat > go.mod << EOF
module neuralmeter-test-services

go 1.21

require (
    github.com/gorilla/mux v1.8.0
    github.com/gorilla/websocket v1.5.0
    github.com/prometheus/client_golang v1.17.0
)
EOF

go mod tidy

# Build API servers
for port in 8011 8012 8013; do
    echo "Building API server for port $port..."
    PORT=$port go build -o "$BIN_DIR/api-server-$port" -ldflags="-X main.defaultPort=$port" /tmp/api-server.go
done

# Build WebSocket servers
for port in 8080 8081; do
    echo "Building WebSocket server for port $port..."
    PORT=$port go build -o "$BIN_DIR/websocket-server-$port" -ldflags="-X main.defaultPort=$port" /tmp/websocket-server.go
done

echo "=== Creating systemd services ==="

# API server services
for port in 8011 8012 8013; do
    cat > /etc/systemd/system/neuralmeter-api-$port.service << EOF
[Unit]
Description=NeuralMeter API Server (Port $port)
After=network.target
Wants=network.target

[Service]
Type=simple
User=neuralmeter
Group=neuralmeter
Environment=PORT=$port
Environment=GOMAXPROCS=2
ExecStart=$BIN_DIR/api-server-$port
Restart=always
RestartSec=3
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ProtectKernelTunables=true
ProtectControlGroups=true
RestrictRealtime=true
MemoryDenyWriteExecute=true

# Resource limits
LimitNOFILE=100000
LimitNPROC=1000

[Install]
WantedBy=multi-user.target
EOF
done

# WebSocket server services
for port in 8080 8081; do
    cat > /etc/systemd/system/neuralmeter-ws-$port.service << EOF
[Unit]
Description=NeuralMeter WebSocket Server (Port $port)
After=network.target
Wants=network.target

[Service]
Type=simple
User=neuralmeter
Group=neuralmeter
Environment=PORT=$port
Environment=GOMAXPROCS=2
ExecStart=$BIN_DIR/websocket-server-$port
Restart=always
RestartSec=3
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true

# Resource limits
LimitNOFILE=100000
LimitNPROC=1000

[Install]
WantedBy=multi-user.target
EOF
done

# Health check service
cat > /etc/systemd/system/neuralmeter-health.service << 'EOF'
[Unit]
Description=NeuralMeter Health Check Service
After=network.target

[Service]
Type=simple
User=neuralmeter
Group=neuralmeter
ExecStart=/usr/bin/python3 /opt/neuralmeter/bin/health-server.py
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

echo "=== Creating health check service ==="
cat > /opt/neuralmeter/bin/health-server.py << 'EOF'
#!/usr/bin/env python3
import json
import time
import psutil
import socket
from http.server import HTTPServer, BaseHTTPRequestHandler

class HealthHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            health_data = {
                'status': 'healthy',
                'timestamp': time.time(),
                'system': {
                    'cpu_percent': psutil.cpu_percent(),
                    'memory_percent': psutil.virtual_memory().percent,
                    'disk_percent': psutil.disk_usage('/').percent,
                    'load_average': psutil.getloadavg(),
                    'uptime': time.time() - psutil.boot_time()
                },
                'network': {
                    'hostname': socket.gethostname(),
                    'connections': len(psutil.net_connections())
                }
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(health_data).encode())
        else:
            self.send_response(404)
            self.end_headers()

if __name__ == '__main__':
    server = HTTPServer(('127.0.0.1', 8021), HealthHandler)
    print("Health server starting on port 8021")
    server.serve_forever()
EOF

chmod +x /opt/neuralmeter/bin/health-server.py

echo "=== Starting services ==="
systemctl daemon-reload

# Enable and start all services
services=(
    "neuralmeter-api-8011"
    "neuralmeter-api-8012" 
    "neuralmeter-api-8013"
    "neuralmeter-ws-8080"
    "neuralmeter-ws-8081"
    "neuralmeter-health"
    "nginx"
    "haproxy"
    "redis"
    "postgresql"
    "prometheus"
)

for service in "${services[@]}"; do
    systemctl enable "$service"
    systemctl start "$service"
    sleep 2
    
    if systemctl is-active --quiet "$service"; then
        echo "✅ $service started successfully"
    else
        echo "❌ $service failed to start"
        systemctl status "$service" --no-pager -l
    fi
done

echo "=== Service status summary ==="
systemctl list-units 'neuralmeter-*' --no-pager
```

### 8. Monitoring and Prometheus Configuration

```yaml
# /etc/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "neuralmeter_rules.yml"

scrape_configs:
  # System metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
  
  # API servers
  - job_name: 'neuralmeter-api'
    metrics_path: '/metrics'
    static_configs:
      - targets:
        - 'localhost:8011'
        - 'localhost:8012'
        - 'localhost:8013'
  
  # HAProxy metrics
  - job_name: 'haproxy'
    static_configs:
      - targets: ['localhost:8404']
  
  # nginx metrics (requires nginx-prometheus-exporter)
  - job_name: 'nginx'
    static_configs:
      - targets: ['localhost:9113']

# Alert rules
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - localhost:9093
```

### 9. Testing and Validation Scripts

```bash
#!/bin/bash
# validate-system-b.sh - Comprehensive validation of System B setup

echo "=== NeuralMeter System B Validation ==="

FAILED_TESTS=0

test_endpoint() {
    local url="$1"
    local expected_status="$2"
    local description="$3"
    
    echo -n "Testing $description... "
    
    status=$(curl -s -o /dev/null -w "%{http_code}" "$url" || echo "000")
    
    if [ "$status" = "$expected_status" ]; then
        echo "✅ PASS ($status)"
    else
        echo "❌ FAIL (got $status, expected $expected_status)"
        ((FAILED_TESTS++))
    fi
}

test_websocket() {
    local url="$1"
    local description="$2"
    
    echo -n "Testing $description... "
    
    # Use websocat if available, otherwise curl
    if command -v websocat >/dev/null; then
        echo "test" | timeout 5s websocat "$url" >/dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo "✅ PASS"
        else
            echo "❌ FAIL"
            ((FAILED_TESTS++))
        fi
    else
        echo "⚠️  SKIP (websocat not available)"
    fi
}

echo "1. Basic Health Checks"
test_endpoint "http://localhost/health" "200" "Primary health endpoint"
test_endpoint "http://localhost:8404/stats" "200" "HAProxy stats"
test_endpoint "http://localhost:9090/metrics" "200" "Prometheus metrics"

echo -e "\n2. API Endpoints (Subtasks 6.3, 6.4)"
test_endpoint "http://localhost/api/health" "200" "API health endpoint"
test_endpoint "http://localhost/api/data" "200" "API data endpoint"
test_endpoint "http://localhost/api/data?size=2048" "200" "API data with size parameter"

echo -e "\n3. Load Testing Endpoints (Subtasks 6.5, 6.6)"
test_endpoint "http://localhost/load/fast" "200" "Fast load endpoint"
test_endpoint "http://localhost/load/slow" "200" "Slow load endpoint"
test_endpoint "http://localhost/load/cpu" "200" "CPU-intensive endpoint"

echo -e "\n4. SSL/HTTPS Endpoints"
test_endpoint "https://localhost/health" "200" "HTTPS health endpoint"
test_endpoint "https://localhost/api/health" "200" "HTTPS API health"

echo -e "\n5. WebSocket Endpoints (Subtask 6.7)"
test_websocket "ws://localhost:8080/ws" "WebSocket connection"

echo -e "\n6. Service Status Check"
services=("nginx" "haproxy" "redis" "postgresql" "prometheus")
for service in "${services[@]}"; do
    echo -n "Checking $service service... "
    if systemctl is-active --quiet "$service"; then
        echo "✅ RUNNING"
    else
        echo "❌ NOT RUNNING"
        ((FAILED_TESTS++))
    fi
done

echo -e "\n7. Performance Validation"
echo -n "Testing concurrent connections... "
# Test 100 concurrent requests
if command -v ab >/dev/null; then
    ab -n 1000 -c 100 -q http://localhost/health >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ PASS"
    else
        echo "❌ FAIL"
        ((FAILED_TESTS++))
    fi
else
    echo "⚠️  SKIP (apache bench not available)"
fi

echo -e "\n8. Resource Usage Check"
cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
echo "CPU Usage: ${cpu_usage}%"
echo "Memory Usage: ${mem_usage}%"

echo -e "\n=== Validation Summary ==="
if [ $FAILED_TESTS -eq 0 ]; then
    echo "🎉 All tests passed! System B is ready for NeuralMeter CLI testing."
    exit 0
else
    echo "❌ $FAILED_TESTS test(s) failed. Please check the configuration."
    exit 1
fi
```

### 10. Task-Specific Testing Protocols

#### Subtask 6.1: CLI Command Structure Testing
```bash
# Test basic CLI commands against System B
neuralmeter --version
neuralmeter --help
neuralmeter run --url http://system-b-ip/health --requests 10
```

#### Subtask 6.2: Server Daemon Mode Testing
```bash
# Start daemon and test long-running operations
neuralmeter server start --daemon --api-port 8080
curl -X POST http://system-a:8080/api/tests -d '{"target":"http://system-b-ip","duration":"5m"}'
```

#### Subtask 6.3: Remote API Server Testing
```bash
# Test API orchestration
curl -X POST http://system-a:8080/api/tests \
  -H "Content-Type: application/json" \
  -d '{"test_plan":"load-test.yaml","target_url":"http://system-b-ip","duration":"5m","concurrency":100}'
```

#### Subtask 6.4: Test Plan Processing
```yaml
# test-plan.yaml for System B
name: "System B Comprehensive Test"
global:
  target_host: "http://system-b-ip"
scenarios:
  - name: "API Load Test"
    requests:
      - method: GET
        url: "${target_host}/api/health"
      - method: POST
        url: "${target_host}/api/data"
        body: '{"test": "data"}'
load_pattern:
  type: "ramp_up"
  users: 1000
  duration: "10m"
```

#### Subtask 6.5: GPU-Accelerated Load Testing
```bash
# Verify GPU detection and run accelerated tests
neuralmeter gpu list
neuralmeter run --gpu-accelerated test-plan.yaml
nvidia-smi -l 1  # Monitor GPU utilization
```

#### Subtask 6.6: HTTP Load Generation Engine
```bash
# Test connection pooling and sustained load
neuralmeter run --url http://system-b-ip/load/fast --concurrency 5000 --duration 10m
# Monitor connections: ss -tuln | grep :80
```

#### Subtask 6.7: Real-time Result Streaming
```bash
# Test WebSocket streaming
neuralmeter run --stream-results ws://system-b-ip:8080/ws test-plan.yaml
```

#### Subtask 6.8: GPU Command Integration
```bash
# Test GPU performance monitoring
neuralmeter gpu status
neuralmeter gpu benchmark --target http://system-b-ip/load/cpu
```

#### Subtask 6.9: Configuration Management
```bash
# Test configuration with System B endpoints
neuralmeter config validate --config neuralmeter.yaml
neuralmeter run --config neuralmeter.yaml --target http://system-b-ip
```

#### Subtask 6.10: Output Formatting and Logging
```bash
# Test various output formats
neuralmeter run --output json --log-level debug test-plan.yaml
neuralmeter run --output text --log-file /var/log/neuralmeter.log test-plan.yaml
```

## Setup Installation Guide

### Quick Setup (Automated)
```bash
# Download and run the complete setup script
curl -fsSL https://raw.githubusercontent.com/your-repo/neuralmeter/main/scripts/setup-system-b.sh | sudo bash

# Validate installation
curl -fsSL https://raw.githubusercontent.com/your-repo/neuralmeter/main/scripts/validate-system-b.sh | bash
```

### Manual Setup Steps
1. **Prepare System**: Run base system setup script
2. **Configure Certificates**: Generate SSL certificates for HTTPS testing
3. **Deploy Load Balancer**: Configure HAProxy for traffic distribution
4. **Setup Web Server**: Configure nginx with performance optimizations
5. **Deploy Services**: Build and deploy Go-based backend services
6. **Configure Monitoring**: Setup Prometheus and health monitoring
7. **Validate Setup**: Run comprehensive validation tests

## Performance Characteristics

### Expected Throughput
- **HTTP Requests**: 100,000+ RPS sustained
- **Concurrent Connections**: 50,000+ simultaneous
- **WebSocket Connections**: 10,000+ concurrent
- **SSL/TLS Throughput**: 25,000+ RPS
- **API Response Time**: <10ms average
- **Memory Usage**: <8GB under full load
- **CPU Usage**: <80% under sustained load

### Scaling Capabilities
- **Horizontal Scaling**: Add more backend instances
- **Vertical Scaling**: Increase CPU/Memory as needed
- **Load Distribution**: Automatic via HAProxy
- **Health Monitoring**: Automatic service recovery
- **Resource Management**: Dynamic allocation based on load

## Troubleshooting Guide

### Common Issues
1. **Port Conflicts**: Check `netstat -tulpn` for port usage
2. **Certificate Issues**: Verify SSL certificate generation
3. **Service Startup**: Check `systemctl status service-name`
4. **Performance Issues**: Monitor with `htop`, `iotop`, `nethogs`
5. **Network Issues**: Verify firewall rules and network connectivity

### Log Locations
- **HAProxy**: `/var/log/haproxy.log`
- **nginx**: `/var/log/nginx/access.log`, `/var/log/nginx/error.log`
- **API Services**: `/opt/neuralmeter/logs/api-*.log`
- **WebSocket**: `/opt/neuralmeter/logs/websocket.log`
- **System**: `/var/log/neuralmeter-setup.log`

### Monitoring Commands
```bash
# Service health
systemctl list-units 'neuralmeter-*' --all

# Resource usage
htop
iotop -o
nethogs
ss -tuln

# Application metrics
curl http://localhost:9090/metrics
curl http://localhost:8404/stats
curl http://localhost/health/detailed
```

This comprehensive System B setup provides a robust, scalable, and thoroughly testable environment for validating all aspects of the NeuralMeter CLI implementation across every Task 6 subtask, with clear traceability to specific requirements and comprehensive monitoring capabilities.

---

## Additional Information: System Architecture Clarification

### Understanding the Two-System Setup

It's important to understand the distinct roles of each system in this testing architecture:

**System A - Performance Testing Tool (NeuralMeter CLI)**:
- **Runs your NeuralMeter CLI** (written in Go)
- **GENERATES load** and sends HTTP requests
- **MEASURES performance** metrics (response times, throughput, errors)
- **Reports results** back to you
- This is the **tool being developed and tested**

**System B - Target Services (This Document)**:
- **RECEIVES requests** from System A's CLI
- **RESPONDS to the load** testing attacks
- **Acts as realistic backend services** that your CLI tests against
- This is the **target being tested** (the "victim")

### Data Flow and Verification Process

```
System A (Your CLI) --------HTTP requests-------> System B (Target Services)
                    <-------HTTP responses--------

Example:
neuralmeter run --url http://system-b-ip/api/data --concurrency 1000
```

### What Section 5 (Backend API Services) Actually Does

The Go code in section 5 is **NOT** your NeuralMeter CLI. Instead, it creates **sophisticated target services** that simulate real-world backend APIs. Here's what's being verified:

1. **Load Generation Accuracy**: 
   - Your CLI reports: "I sent 1000 requests"
   - System B reports: "I received 1000 requests" 
   - Verification: Do the numbers match?

2. **Response Handling**: 
   - System B sends various response types (fast, slow, CPU-intensive)
   - Your CLI must handle all response patterns correctly

3. **Connection Management**: 
   - System B tracks connection patterns and reuse
   - Verify your CLI efficiently manages connection pools

4. **Performance Measurement**: 
   - System B provides known response times
   - Verify your CLI measures and reports them accurately

### Key Point

System B acts as a **"smart punching bag"** - sophisticated target services that provide realistic backend behavior for your NeuralMeter CLI to test against. The Go services on System B are **test targets**, not the testing tool itself.

This two-system approach ensures your CLI is tested against real network conditions, actual HTTP servers, and genuine service behaviors rather than mocks or stubs.