# NeuralMeter CLI GPU Architecture - Task 6 Implementation

## Problem Statement

The current CLI architecture tries to handle all platforms in one implementation, causing:
- Complex build tag management
- Platform detection at wrong time (CLI startup vs test execution)
- Confusion between different GPU APIs (CUDA, Metal, DirectML)
- Unnecessary CPU fallback implementation

## Solution: Platform-Specific CLI Builds

Build **separate CLI binaries** for each target platform with platform-specific GPU support built-in.

## Architecture Overview

### Core Principle: One CLI Binary Per Platform

```
neuralmeter-linux    → Built on Linux, includes CUDA support
neuralmeter-macos    → Built on macOS, includes Metal support  
neuralmeter-windows  → Built on Windows, includes DirectML support
```

### Directory Structure

```
cmd/neuralmeter/
├── main.go                    # Platform-agnostic entry point
├── cli/
│   ├── app.go                 # Core CLI application
│   ├── run.go                 # Test execution command
│   ├── gpu.go                 # GPU info command
│   └── config.go              # Configuration command
└── gpu/
    ├── factory.go             # GPU provider factory
    ├── provider_linux.go      # CUDA provider (build: linux)
    ├── provider_macos.go      # Metal provider (build: darwin)
    └── provider_windows.go    # DirectML provider (build: windows)
```

## Implementation

### 1. Main Entry Point (Platform-Agnostic)

```go
// cmd/neuralmeter/main.go
package main

import (
    "log"
    "os"
    "github.com/neuralmeter/cmd/neuralmeter/cli"
    "github.com/neuralmeter/cmd/neuralmeter/gpu"
)

func main() {
    // Create GPU factory for this platform
    gpuFactory := gpu.NewFactory()
    
    // Create CLI app
    app := cli.NewApp(gpuFactory)
    
    // Execute CLI
    if err := app.Execute(); err != nil {
        log.Printf("Error: %v", err)
        os.Exit(1)
    }
}
```

### 2. CLI Application Core

```go
// cmd/neuralmeter/cli/app.go
package cli

import (
    "github.com/spf13/cobra"
    "github.com/neuralmeter/cmd/neuralmeter/gpu"
)

type App struct {
    rootCmd     *cobra.Command
    gpuFactory  gpu.Factory
    gpuProvider gpu.Provider // nil until initialized
}

func NewApp(factory gpu.Factory) *App {
    app := &App{
        gpuFactory: factory,
    }
    
    app.rootCmd = &cobra.Command{
        Use:   "neuralmeter",
        Short: "AI-powered load testing tool",
    }
    
    // Add commands
    app.rootCmd.AddCommand(app.createRunCommand())
    app.rootCmd.AddCommand(app.createGPUCommand())
    app.rootCmd.AddCommand(app.createVersionCommand())
    
    return app
}

func (a *App) Execute() error {
    return a.rootCmd.Execute()
}
```

### 3. GPU Factory Interface

```go
// cmd/neuralmeter/gpu/factory.go
package gpu

type Provider interface {
    Initialize() error
    Detect() ([]Device, error)
    GetInfo() (*Info, error)
    Cleanup() error
}

type Factory interface {
    CreateProvider() (Provider, error)
}

type Device struct {
    ID     int
    Name   string
    Memory int64
}

type Info struct {
    Available     bool
    DeviceCount   int
    Platform      string
    Version       string
}
```

### 4. Platform-Specific Implementations

#### Linux (CUDA)

```go
// cmd/neuralmeter/gpu/provider_linux.go
//go:build linux

package gpu

/*
#cgo LDFLAGS: -lcuda -lcudart
#include <cuda_runtime.h>
*/
import "C"
import (
    "fmt"
    "unsafe"
)

type linuxFactory struct{}
type cudaProvider struct {
    initialized bool
}

func NewFactory() Factory {
    return &linuxFactory{}
}

func (f *linuxFactory) CreateProvider() (Provider, error) {
    return &cudaProvider{}, nil
}

func (p *cudaProvider) Initialize() error {
    if p.initialized {
        return nil
    }
    
    // Initialize CUDA runtime
    ret := C.cudaSetDevice(0)
    if ret != C.cudaSuccess {
        return fmt.Errorf("CUDA initialization failed: %v", ret)
    }
    
    p.initialized = true
    return nil
}

func (p *cudaProvider) Detect() ([]Device, error) {
    if !p.initialized {
        return nil, fmt.Errorf("provider not initialized")
    }
    
    var deviceCount C.int
    ret := C.cudaGetDeviceCount(&deviceCount)
    if ret != C.cudaSuccess {
        return nil, fmt.Errorf("failed to get device count: %v", ret)
    }
    
    devices := make([]Device, deviceCount)
    for i := 0; i < int(deviceCount); i++ {
        var props C.struct_cudaDeviceProp
        ret := C.cudaGetDeviceProperties(&props, C.int(i))
        if ret != C.cudaSuccess {
            continue
        }
        
        devices[i] = Device{
            ID:     i,
            Name:   C.GoString(&props.name[0]),
            Memory: int64(props.totalGlobalMem),
        }
    }
    
    return devices, nil
}

func (p *cudaProvider) GetInfo() (*Info, error) {
    devices, err := p.Detect()
    if err != nil {
        return &Info{Available: false}, nil
    }
    
    var version C.int
    C.cudaRuntimeGetVersion(&version)
    
    return &Info{
        Available:   len(devices) > 0,
        DeviceCount: len(devices),
        Platform:    "CUDA",
        Version:     fmt.Sprintf("%d.%d", version/1000, (version%1000)/10),
    }, nil
}

func (p *cudaProvider) Cleanup() error {
    if p.initialized {
        C.cudaDeviceReset()
        p.initialized = false
    }
    return nil
}
```

#### macOS (Metal)

```go
// cmd/neuralmeter/gpu/provider_macos.go
//go:build darwin

package gpu

/*
#cgo LDFLAGS: -framework Metal -framework Foundation
#import <Metal/Metal.h>
#import <Foundation/Foundation.h>
*/
import "C"
import (
    "fmt"
    "unsafe"
)

type macosFactory struct{}
type metalProvider struct {
    device C.id
}

func NewFactory() Factory {
    return &macosFactory{}
}

func (f *macosFactory) CreateProvider() (Provider, error) {
    return &metalProvider{}, nil
}

func (p *metalProvider) Initialize() error {
    // Get default Metal device
    p.device = C.MTLCreateSystemDefaultDevice()
    if p.device == nil {
        return fmt.Errorf("no Metal device available")
    }
    return nil
}

func (p *metalProvider) Detect() ([]Device, error) {
    if p.device == nil {
        return nil, fmt.Errorf("provider not initialized")
    }
    
    // For simplicity, return single device
    // In reality, you'd enumerate all available devices
    namePtr := C.objc_msgSend(p.device, C.sel_getUid("name"))
    name := C.NSString_cString(namePtr)
    
    return []Device{{
        ID:     0,
        Name:   C.GoString(name),
        Memory: 0, // Metal doesn't expose memory size directly
    }}, nil
}

func (p *metalProvider) GetInfo() (*Info, error) {
    devices, err := p.Detect()
    if err != nil {
        return &Info{Available: false}, nil
    }
    
    return &Info{
        Available:   len(devices) > 0,
        DeviceCount: len(devices),
        Platform:    "Metal",
        Version:     "3.0", // You'd get actual version
    }, nil
}

func (p *metalProvider) Cleanup() error {
    if p.device != nil {
        C.CFRelease(p.device)
        p.device = nil
    }
    return nil
}
```

#### Windows (DirectML)

```go
// cmd/neuralmeter/gpu/provider_windows.go
//go:build windows

package gpu

/*
#cgo LDFLAGS: -ldirectml -ld3d12
#include <d3d12.h>
#include <directml.h>
*/
import "C"
import "fmt"

type windowsFactory struct{}
type directmlProvider struct {
    device *C.ID3D12Device
}

func NewFactory() Factory {
    return &windowsFactory{}
}

func (f *windowsFactory) CreateProvider() (Provider, error) {
    return &directmlProvider{}, nil
}

func (p *directmlProvider) Initialize() error {
    // Initialize D3D12 device
    var hr C.HRESULT
    hr = C.D3D12CreateDevice(nil, C.D3D_FEATURE_LEVEL_11_0, 
        &C.IID_ID3D12Device, unsafe.Pointer(&p.device))
    
    if C.FAILED(hr) {
        return fmt.Errorf("failed to create D3D12 device: %x", hr)
    }
    
    return nil
}

func (p *directmlProvider) Detect() ([]Device, error) {
    if p.device == nil {
        return nil, fmt.Errorf("provider not initialized")
    }
    
    // Enumerate adapters and return device info
    // This is simplified - real implementation would enumerate DXGI adapters
    return []Device{{
        ID:     0,
        Name:   "DirectML Device",
        Memory: 0, // Would query actual memory
    }}, nil
}

func (p *directmlProvider) GetInfo() (*Info, error) {
    devices, err := p.Detect()
    if err != nil {
        return &Info{Available: false}, nil
    }
    
    return &Info{
        Available:   len(devices) > 0,
        DeviceCount: len(devices),
        Platform:    "DirectML",
        Version:     "1.9",
    }, nil
}

func (p *directmlProvider) Cleanup() error {
    if p.device != nil {
        p.device.Release()
        p.device = nil
    }
    return nil
}
```

### 5. CLI Commands with Lazy GPU Initialization

```go
// cmd/neuralmeter/cli/run.go
package cli

import (
    "fmt"
    "github.com/spf13/cobra"
)

func (a *App) createRunCommand() *cobra.Command {
    return &cobra.Command{
        Use:   "run <test-plan>",
        Short: "Execute a load test",
        Args:  cobra.ExactArgs(1),
        RunE:  a.runTest,
    }
}

func (a *App) runTest(cmd *cobra.Command, args []string) error {
    // Initialize GPU when needed
    if err := a.initializeGPU(); err != nil {
        return fmt.Errorf("GPU initialization failed: %w", err)
    }
    
    fmt.Printf("Executing test plan: %s\n", args[0])
    
    // Get GPU info
    info, err := a.gpuProvider.GetInfo()
    if err != nil {
        return fmt.Errorf("failed to get GPU info: %w", err)
    }
    
    fmt.Printf("Using GPU: %s %s (%d devices)\n", 
        info.Platform, info.Version, info.DeviceCount)
    
    // Execute test...
    return nil
}

func (a *App) initializeGPU() error {
    if a.gpuProvider != nil {
        return nil // Already initialized
    }
    
    provider, err := a.gpuFactory.CreateProvider()
    if err != nil {
        return err
    }
    
    if err := provider.Initialize(); err != nil {
        return err
    }
    
    a.gpuProvider = provider
    return nil
}
```

```go
// cmd/neuralmeter/cli/gpu.go
package cli

import (
    "fmt"
    "github.com/spf13/cobra"
)

func (a *App) createGPUCommand() *cobra.Command {
    cmd := &cobra.Command{
        Use:   "gpu",
        Short: "GPU information and diagnostics",
    }
    
    cmd.AddCommand(&cobra.Command{
        Use:   "info",
        Short: "Show GPU information",
        RunE:  a.gpuInfo,
    })
    
    return cmd
}

func (a *App) gpuInfo(cmd *cobra.Command, args []string) error {
    // Initialize GPU to get info
    if err := a.initializeGPU(); err != nil {
        return fmt.Errorf("GPU not available: %w", err)
    }
    
    info, err := a.gpuProvider.GetInfo()
    if err != nil {
        return err
    }
    
    fmt.Printf("GPU Platform: %s\n", info.Platform)
    fmt.Printf("Version: %s\n", info.Version)
    fmt.Printf("Available: %v\n", info.Available)
    fmt.Printf("Device Count: %d\n", info.DeviceCount)
    
    devices, err := a.gpuProvider.Detect()
    if err != nil {
        return err
    }
    
    for _, device := range devices {
        fmt.Printf("\nGPU %d:\n", device.ID)
        fmt.Printf("  Name: %s\n", device.Name)
        if device.Memory > 0 {
            fmt.Printf("  Memory: %d MB\n", device.Memory/(1024*1024))
        }
    }
    
    return nil
}
```

## Build System

### Platform-Specific Build Scripts

#### Linux Build

```bash
#!/bin/bash
# build/linux/build.sh

echo "Building NeuralMeter CLI for Linux (CUDA)..."

# Check CUDA installation
if ! command -v nvcc &> /dev/null; then
    echo "Error: CUDA toolkit not found. Install CUDA 11.8+ first."
    exit 1
fi

# Set environment
export CGO_ENABLED=1
export GOOS=linux
export GOARCH=amd64

# Build with CUDA support
go build -tags linux \
    -ldflags="-s -w" \
    -o bin/neuralmeter-linux \
    cmd/neuralmeter/main.go

echo "✅ Built: bin/neuralmeter-linux"
```

#### macOS Build

```bash
#!/bin/bash
# build/macos/build.sh

echo "Building NeuralMeter CLI for macOS (Metal)..."

# Check Xcode tools
if ! xcode-select -p &> /dev/null; then
    echo "Error: Xcode command line tools not found."
    exit 1
fi

# Set environment
export CGO_ENABLED=1
export GOOS=darwin

# Detect architecture
if [[ $(uname -m) == "arm64" ]]; then
    export GOARCH=arm64
    OUTPUT="bin/neuralmeter-macos-arm64"
else
    export GOARCH=amd64
    OUTPUT="bin/neuralmeter-macos-amd64"
fi

# Build with Metal support
go build -tags darwin \
    -ldflags="-s -w" \
    -o $OUTPUT \
    cmd/neuralmeter/main.go

echo "✅ Built: $OUTPUT"
```

#### Windows Build

```bash
#!/bin/bash
# build/windows/build.sh (run on Windows or cross-compile)

echo "Building NeuralMeter CLI for Windows (DirectML)..."

# Set environment
export CGO_ENABLED=1
export GOOS=windows
export GOARCH=amd64

# Build with DirectML support
go build -tags windows \
    -ldflags="-s -w" \
    -o bin/neuralmeter-windows.exe \
    cmd/neuralmeter/main.go

echo "✅ Built: bin/neuralmeter-windows.exe"
```

## Usage Examples

### GPU Information

```bash
# Linux
$ ./neuralmeter-linux gpu info
GPU Platform: CUDA
Version: 12.0
Available: true
Device Count: 1

GPU 0:
  Name: NVIDIA GeForce RTX 3080
  Memory: 10240 MB

# macOS
$ ./neuralmeter-macos gpu info  
GPU Platform: Metal
Version: 3.0
Available: true
Device Count: 1

GPU 0:
  Name: Apple M2 Pro

# Windows
$ ./neuralmeter-windows.exe gpu info
GPU Platform: DirectML
Version: 1.9
Available: true
Device Count: 1

GPU 0:
  Name: DirectML Device
```

### Test Execution

```bash
# All platforms
$ ./neuralmeter-{platform} run test.yaml
Executing test plan: test.yaml
Using GPU: CUDA 12.0 (1 devices)
✅ Test completed successfully
```

## Key Benefits

1. **Simple Build Process**: One command per platform
2. **No Runtime Platform Detection**: Platform decided at build time
3. **Clean Code**: No complex build tags in business logic
4. **Optimal Performance**: Platform-specific optimizations
5. **Clear Error Messages**: Platform-specific error handling
6. **Easy Distribution**: Single binary per platform

## Distribution Strategy

```
releases/
├── v1.0.0/
│   ├── neuralmeter-linux-amd64
│   ├── neuralmeter-macos-amd64  
│   ├── neuralmeter-macos-arm64
│   └── neuralmeter-windows-amd64.exe
└── checksums.txt
```

This architecture eliminates the complexity of runtime platform detection and provides a clean, maintainable solution for Task 6.