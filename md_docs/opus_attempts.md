# NeuralMeter Architecture - Platform-Separated Development Guide

## **Executive Summary**

NeuralMeter is a GPU-accelerated load testing platform that replaces JMeter with modern Go-based performance testing. This document defines the correct architecture using **single branch with directory-based platform separation** to eliminate development confusion and ensure functional code instead of stubs.

## **CRITICAL: Understanding the Problem**

Claude Sonnet has been creating stub implementations because tasks 69-88 mix all platforms in single tasks. This causes:
- Platform confusion (trying to implement CUDA on macOS)
- Stub/mock code instead of real implementations
- Build tag complexity that doesn't work
- Triple implementation attempts in single files

## **Key Architecture Decisions**

1. **Build Tags for ide ** - Directory structure enforces platform separation, add cursor rules to enforce only the correct os can work on the code for the same platform.
2. **NO CPU Fallbacks** - GPU is mandatory for all platforms (NO CPU LOAD TESTING)
3. **NO Stubs or Mocks** - Real GPU API implementations only
4. **Single Branch** - All platforms in one branch, separated by directories
5. **Platform-Specific Cursor IDEs** - Each platform developed in its own OS-specific Cursor IDE cursor rules and tags to enforce this

## **Product Overview**

### **Build Outputs (4 Total)**
1. **neuralmeter-macos** - macOS UI + Local Engine + Metal GPU
2. **neuralmeter-windows** - Windows UI + Local Engine + DirectML GPU  
3. **neuralmeter-linux** - Linux UI + Local Engine + CUDA GPU
4. **neuralmeter-linux-worker** - Linux Headless Worker (Enterprise only)

### **Deployment Modes**
- **Standalone**: Complete UI + Engine runs locally with GPU acceleration
- **Enterprise**: UI orchestrates headless Linux workers in cloud/datacenter

## **Directory Structure**

```
neuralmeter/
├── cmd/
│   ├── neuralmeter/           # Main UI+Engine binary
│   │   └── main.go           # Single entry point for all platforms
│   └── worker/               # Headless worker binary
│       └── main.go           # Linux-only enterprise worker
├── internal/
│   ├── core/                 # Shared interfaces (Any Platform)
│   │   ├── interfaces.go     # GPUProvider, Worker interfaces
│   │   ├── types.go         # Common types
│   │   └── contracts.go     # API contracts
│   ├── ui/                  # Fyne UI (Any Platform)
│   ├── engine/              # Local test engine (Any Platform)
│   ├── config/              # Configuration (Any Platform)
│   ├── metrics/             # Metrics (Any Platform)
│   ├── gpu/                 # GPU implementations
│   │   ├── linux/           # Linux GPU (CUDA)
│   │   │   └── cuda/
│   │   │       ├── detection.go
│   │   │       ├── loading.go
│   │   │       └── metrics.go
│   │   ├── macos/           # macOS GPU (Metal)
│   │   │   └── metal/
│   │   │       ├── detection.go
│   │   │       ├── loading.go
│   │   │       └── metrics.go
│   │   └── windows/         # Windows GPU (DirectML)
│   │       └── directml/
│   │           ├── detection.go
│   │           ├── loading.go
│   │           └── metrics.go
│   └── workers/             # Worker implementations
│       ├── manager.go       # Shared worker manager
│       └── linux/           # Linux-specific worker code
├── build/
│   ├── linux/               # Linux build scripts
│   ├── macos/               # macOS build scripts
│   └── windows/             # Windows build scripts
└── .cursor/
    └── rules/               # Cursor IDE platform rules
        ├── linux.rules      # Linux-specific rules
        ├── macos.rules      # macOS-specific rules
        └── windows.rules    # Windows-specific rules
```

## **Platform Development Rules**

### **Shared Code (Any Platform)**
- **Location**: `internal/{core,ui,engine,config,metrics}`
- **Cursor Tags**: `@platform:shared`
- **Requirements**: Pure Go, no platform-specific code
- **Example Tasks**: Tasks 1-60 (HTTP, config, UI, metrics)

### **Linux Development (Linux Only)**
- **Location**: `internal/gpu/linux/cuda/`
- **Cursor Tags**: `@platform:linux @gpu:cuda`
- **Requirements**: Linux with CUDA toolkit, real CUDA API calls
- **NO**: CPU fallback, mocks, or stubs
- **Example**: Task 69-Linux (CUDA Detection)

### **macOS Development (macOS Only)**
- **Location**: `internal/gpu/macos/metal/`
- **Cursor Tags**: `@platform:macos @gpu:metal`
- **Requirements**: macOS with Xcode, real Metal framework calls
- **NO**: CPU fallback, mocks, or stubs
- **Example**: Task 69-macOS (Metal Detection)

### **Windows Development (Windows Only)**
- **Location**: `internal/gpu/windows/directml/`
- **Cursor Tags**: `@platform:windows @gpu:directml`
- **Requirements**: Windows with MinGW, real DirectML API calls
- **NO**: CPU fallback, mocks, or stubs
- **Example**: Task 69-Windows (DirectML Detection)

## **Cursor IDE Configuration**

### **CRITICAL: Use Platform-Specific Cursor IDEs**

**Problem**: Mac Cursor tries to write Windows/Linux code, creates stubs
**Solution**: Each platform MUST use its own Cursor IDE instance do not create mock or stubs

### **Setup Requirements**

1. **Linux Development**
   - Use Cursor on Linux machine (or Linux VM)
   - Install CUDA toolkit before starting
   - Configure Cursor with Linux rules

2. **macOS Development**
   - Use Cursor on Mac machine
   - Ensure Xcode Command Line Tools installed
   - Configure Cursor with macOS rules

3. **Windows Development**
   - Use Cursor on Windows machine
   - Install MinGW-w64 and DirectML SDK
   - Configure Cursor with Windows rules

### **Cursor Rules Configuration**

Create `.cursor/rules/` directory with platform-specific rules:

#### **Linux Rules** (`.cursor/rules/linux.rules`)
```yaml
platform: linux
allowed_paths:
  - internal/gpu/linux/**
  - internal/workers/linux/**
  - cmd/worker/**
forbidden_paths:
  - internal/gpu/macos/**
  - internal/gpu/windows/**
requirements:
  - CUDA toolkit installed
  - Real CUDA API usage
  - No CPU fallback
  - No mock implementations
build_tags:
  - linux
  - cuda
context: |
  You are developing on Linux for CUDA GPUs only.
  Use real CUDA API calls with CGO.
  Never implement CPU fallbacks.
  Never create mock implementations.
```

#### **macOS Rules** (`.cursor/rules/macos.rules`)
```yaml
platform: macos
allowed_paths:
  - internal/gpu/macos/**
forbidden_paths:
  - internal/gpu/linux/**
  - internal/gpu/windows/**
  - cmd/worker/**
requirements:
  - Metal framework available
  - Real Metal API usage
  - No CPU fallback
  - No mock implementations
build_tags:
  - darwin
  - metal
context: |
  You are developing on macOS for Metal GPUs only.
  Use real Metal framework calls with CGO.
  Never implement CPU fallbacks.
  Never create mock implementations.
```

#### **Windows Rules** (`.cursor/rules/windows.rules`)
```yaml
platform: windows
allowed_paths:
  - internal/gpu/windows/**
forbidden_paths:
  - internal/gpu/linux/**
  - internal/gpu/macos/**
  - cmd/worker/**
requirements:
  - DirectML SDK available
  - Real DirectML API usage
  - No CPU fallback
  - No mock implementations
build_tags:
  - windows
  - directml
context: |
  You are developing on Windows for DirectML GPUs only.
  Use real DirectML API calls.
  Never implement CPU fallbacks.
  Never create mock implementations.
```

#### **Shared Rules** (`.cursor/rules/shared.rules`)
```yaml
platform: shared
allowed_paths:
  - internal/core/**
  - internal/ui/**
  - internal/engine/**
  - internal/config/**
  - internal/metrics/**
forbidden_paths:
  - internal/gpu/linux/**
  - internal/gpu/macos/**
  - internal/gpu/windows/**
context: |
  You are developing shared components.
  No platform-specific code allowed.
  Use interfaces for GPU abstraction.
```

## **Task Breakdown: From Mixed to Platform-Specific**

### **Original Problem Tasks (69-88)**
These tasks currently mix all platforms, causing Claude to create stubs:

| Original Task | Problem | Solution |
|--------------|---------|----------|
| Task 69: GPU Detection | Mixes CUDA/Metal/DirectML | Split into 69-Core, 69-Linux, 69-macOS, 69-Windows |
| Task 70: Model Loading | Triple implementation | Split into 70-Core, 70-Linux, 70-macOS, 70-Windows |
| Task 71: Performance Metrics | Platform confusion | Split into 71-Core, 71-Linux, 71-macOS, 71-Windows |
| Task 72: Error Handling | Generic stubs | Split into 72-Core, 72-Linux, 72-macOS, 72-Windows |
| Task 73: Multi-GPU Support | Complex conditionals | Split into 73-Core, 73-Linux, 73-macOS, 73-Windows |
| Task 74: Configuration | Mixed validation | Split into 74-Core, 74-Linux, 74-macOS, 74-Windows |
| Tasks 75-88: Advanced GPU | All mixed platforms | Each split into Core + 3 platform variants |

### **New Task Structure Example**

#### **Task 69: GPU Detection (Original - PROBLEMATIC)**
```yaml
Title: "GPU Capability Detection & CUDA Interface Implementation"
Details: "Implement GPU hardware detection and CUDA/ONNX Runtime interface"
Platform: ALL (Windows, Linux, macOS) # PROBLEM!
Result: Claude creates stubs for all platforms
```

#### **Task 69 Split (FIXED)**

**Task 69-Core: GPU Interface Definition**
```yaml
Title: "Define GPU Provider Interface"
Location: internal/core/interfaces.go
Platform: Shared (Any OS)
Details: |
  - Define GPUProvider interface
  - Define GPUDevice struct
  - Define GPUMetrics struct
  - No implementation, interfaces only
```

**Task 69-Linux: CUDA Detection**
```yaml
Title: "CUDA GPU Detection Implementation"
Location: internal/gpu/linux/cuda/detection.go
Platform: Linux ONLY
Cursor: Linux Cursor IDE
Details: |
  - Real CUDA API calls using CGO
  - #include <cuda.h>
  - cudaGetDeviceCount()
  - NO CPU fallback
  - NO mocks
```

**Task 69-macOS: Metal Detection**
```yaml
Title: "Metal GPU Detection Implementation"
Location: internal/gpu/macos/metal/detection.go
Platform: macOS ONLY
Cursor: macOS Cursor IDE
Details: |
  - Real Metal framework calls
  - #import <Metal/Metal.h>
  - MTLCopyAllDevices()
  - NO CPU fallback
  - NO mocks
```

**Task 69-Windows: DirectML Detection**
```yaml
Title: "DirectML GPU Detection Implementation"
Location: internal/gpu/windows/directml/detection.go
Platform: Windows ONLY
Cursor: Windows Cursor IDE
Details: |
  - Real DirectML API calls
  - DirectML device enumeration
  - NO CPU fallback
  - NO mocks
```

## **Complete Task Mapping (Tasks 69-88)**

### **Phase 1: Core GPU Implementation (Tasks 69-74)**

| Original | Core Task | Linux Task | macOS Task | Windows Task |
|----------|-----------|------------|------------|--------------|
| Task 69: GPU Detection | 69-Core: Define interfaces | 69-Linux: CUDA detection | 69-macOS: Metal detection | 69-Windows: DirectML detection |
| Task 70: Model Loading | 70-Core: Loading interface | 70-Linux: CUDA loading | 70-macOS: Metal loading | 70-Windows: DirectML loading |
| Task 71: Performance Metrics | 71-Core: Metrics interface | 71-Linux: CUDA metrics | 71-macOS: Metal metrics | 71-Windows: DirectML metrics |
| Task 72: Error Handling | 72-Core: Error types | 72-Linux: CUDA errors | 72-macOS: Metal errors | 72-Windows: DirectML errors |
| Task 73: Multi-GPU | 73-Core: Multi-GPU interface | 73-Linux: CUDA multi-GPU | 73-macOS: Metal multi-GPU | 73-Windows: DirectML multi-GPU |
| Task 74: Configuration | 74-Core: Config schema | 74-Linux: CUDA config | 74-macOS: Metal config | 74-Windows: DirectML config |

### **Phase 2: Advanced GPU Operations (Tasks 75-82)**

| Original | Core Task | Linux Task | macOS Task | Windows Task |
|----------|-----------|------------|------------|--------------|
| Task 75: Memory Pool | 75-Core: Pool interface | 75-Linux: CUDA memory | 75-macOS: Metal memory | 75-Windows: DirectML memory |
| Task 76: Kernel Compilation | 76-Core: Kernel interface | 76-Linux: CUDA kernels | 76-macOS: Metal shaders | 76-Windows: DirectML ops |
| Task 77: Tensor Operations | 77-Core: Tensor interface | 77-Linux: cuDNN ops | 77-macOS: MPS ops | 77-Windows: DirectML tensors |
| Task 78: Stream Management | 78-Core: Stream interface | 78-Linux: CUDA streams | 78-macOS: Metal queues | 78-Windows: DirectML queues |
| Task 79: Quantization | 79-Core: Quantize interface | 79-Linux: CUDA quantize | 79-macOS: Metal quantize | 79-Windows: DirectML quantize |
| Task 80: Batch Processing | 80-Core: Batch interface | 80-Linux: CUDA batching | 80-macOS: Metal batching | 80-Windows: DirectML batching |
| Task 81: Profiling | 81-Core: Profile interface | 81-Linux: nvprof | 81-macOS: Metal profiler | 81-Windows: PIX profiler |
| Task 82: Cluster Coordination | 82-Core: Cluster interface | 82-Linux: CUDA IPC | 82-macOS: N/A | 82-Windows: N/A |

### **Phase 3: Enterprise GPU Features (Tasks 83-88)**

| Original | Core Task | Linux Task | macOS Task | Windows Task |
|----------|-----------|------------|------------|--------------|
| Task 83: Auto-scaling | 83-Core: Scale interface | 83-Linux: CUDA scaling | 83-macOS: N/A | 83-Windows: N/A |
| Task 84: HAL | 84-Core: HAL interface | 84-Linux: Multi-vendor | 84-macOS: Metal HAL | 84-Windows: Multi-vendor |
| Task 85: Security | 85-Core: Security interface | 85-Linux: CUDA MPS | 85-macOS: Metal security | 85-Windows: DirectML security |
| Task 86: Power Management | 86-Core: Power interface | 86-Linux: nvidia-smi | 86-macOS: Metal power | 86-Windows: DirectML power |
| Task 87: Checkpoint/Recovery | 87-Core: Checkpoint interface | 87-Linux: CUDA checkpoint | 87-macOS: N/A | 87-Windows: N/A |
| Task 88: Optimization | 88-Core: Optimize interface | 88-Linux: TensorRT | 88-macOS: Metal optimize | 88-Windows: DirectML optimize |

**Note**: N/A tasks are not applicable for that platform (e.g., cluster features on desktop OS)

### **Linux Builds**
```bash
# Headless Worker (Enterprise)
cd neuralmeter
CGO_ENABLED=1 GOOS=linux GOARCH=amd64 \
go build -o bin/neuralmeter-linux-worker \
cmd/worker/main.go

# UI + Engine (Standalone)
CGO_ENABLED=1 GOOS=linux GOARCH=amd64 \
go build -o bin/neuralmeter-linux \
cmd/neuralmeter/main.go
```

### **macOS Build**
```bash
cd neuralmeter
CGO_ENABLED=1 GOOS=darwin GOARCH=amd64 \
go build -o bin/neuralmeter-macos \
cmd/neuralmeter/main.go
```

### **Windows Build**
```bash
cd neuralmeter
set CGO_ENABLED=1
set GOOS=windows
set GOARCH=amd64
go build -o bin/neuralmeter-windows.exe cmd/neuralmeter/main.go
```

## **Development Workflow**

### **1. Shared Component Development**
```bash
# Work on UI, engine, config (any platform)
cd internal/ui/
# Edit files...
go test ./...
```

### **2. Platform-Specific GPU Development**

#### **Linux GPU Work**
```bash
# Must be on Linux machine
cd internal/gpu/linux/cuda/
# Implement real CUDA calls
vim detection.go
# Test with CUDA
go test -tags "linux cuda" ./...
```

#### **macOS GPU Work**
```bash
# Must be on macOS machine
cd internal/gpu/macos/metal/
# Implement real Metal calls
vim detection.go
# Test with Metal
go test -tags "darwin metal" ./...
```

#### **Windows GPU Work**
```bash
# Must be on Windows machine
cd internal/gpu/windows/directml/
# Implement real DirectML calls
vim detection.go
# Test with DirectML
go test -tags "windows directml" ./...
```

## **Implementation Guidelines**

### **DO**
- ✅ Use directory structure to separate platforms
- ✅ Implement real GPU API calls for each platform
- ✅ Test on actual hardware for each platform
- ✅ Use interfaces in `internal/core` for abstraction
- ✅ Keep platform code isolated in platform directories

### **DON'T**
- ❌ Mix platform code in single file
- ❌ Use build tags in implementation files
- ❌ Implement CPU fallbacks (GPU is mandatory)
- ❌ Create mock or stub implementations
- ❌ Try to handle multiple platforms in one task

## **Example Implementation**

### **Step 1: Define Interface** (Any Platform)
```go
// internal/core/interfaces.go
package core

type GPUProvider interface {
    Detect() ([]GPUDevice, error)
    LoadModel(path string) error
    GetMetrics() (*GPUMetrics, error)
}

type GPUDevice struct {
    ID       int
    Name     string
    Memory   int64
    Platform string // "cuda", "metal", "directml"
}
```

### **Step 2: Linux Implementation** (Linux Only)
```go
// internal/gpu/linux/cuda/detection.go
package cuda

// #cgo LDFLAGS: -lcuda -lcudart
// #include <cuda.h>
// #include <cuda_runtime.h>
import "C"

type CUDAProvider struct{}

func (c *CUDAProvider) Detect() ([]core.GPUDevice, error) {
    // Real CUDA API calls
    var count C.int
    result := C.cudaGetDeviceCount(&count)
    if result != C.CUDA_SUCCESS {
        return nil, fmt.Errorf("CUDA not available")
    }
    // ... real implementation
}
```

### **Step 3: macOS Implementation** (macOS Only)
```go
// internal/gpu/macos/metal/detection.go
package metal

// #cgo LDFLAGS: -framework Metal -framework Foundation
// #include <Metal/Metal.h>
import "C"

type MetalProvider struct{}

func (m *MetalProvider) Detect() ([]core.GPUDevice, error) {
    // Real Metal API calls
    devices := C.MTLCopyAllDevices()
    if devices == nil {
        return nil, fmt.Errorf("Metal not available")
    }
    // ... real implementation
}
```

## **Testing Strategy**

### **Unit Tests**
- Test each platform implementation on its target platform
- No cross-platform testing of platform-specific code
- Use interfaces for testing shared components

### **Integration Tests**
- Test UI + Engine + GPU on each platform
- Test enterprise mode with Linux workers
- Test failover scenarios (GPU not available)

### **Performance Tests**
- Benchmark GPU operations on each platform
- Compare performance across platforms
- Validate memory usage and optimization

## **Common Pitfalls & Solutions**

| Problem | Solution |
|---------|----------|
| Claude adds CPU fallback | Explicitly state "NO CPU fallback" in task |
| Claude creates stubs | Require "real API calls only" in task |
| Claude mixes platforms | Use platform-specific task IDs (69-Linux) |
| Build tag confusion | Don't use build tags, use directories |
| Wrong platform code | Cursor rules enforce platform boundaries |

## **Common Pitfalls & Solutions**

### **1. Claude Creates Stubs/Mocks**
**Problem**: Claude writes mock implementations instead of real GPU calls
**Solution**: 
- Explicitly state "NO mocks, NO stubs, real API calls only"
- Provide example of real API usage in task description
- Include specific header files and functions to use

### **2. Platform Confusion**
**Problem**: Mac Cursor tries to implement CUDA code
**Solution**:
- Use platform-specific Cursor instances
- Configure Cursor rules to prevent cross-platform code
- Name tasks with platform suffix (e.g., "Task 69-Linux")

### **3. CPU Fallback Implementation**
**Problem**: Claude adds CPU fallback when GPU not available
**Solution**:
- Explicitly state "NO CPU fallback - GPU is mandatory"
- Return error if GPU not available
- This is GPU-only load testing tool

### **4. Build Tag Confusion**
**Problem**: Complex build tags that don't work correctly
**Solution**:
- Don't use build tags in implementation files
- Use directory structure for platform separation
- Build tags only in build commands, not source files

### **5. Triple Implementation in One File**
**Problem**: Trying to handle all platforms with conditionals
**Solution**:
- Separate files for each platform
- No conditional compilation in implementation
- Clean, platform-specific code only

### **Example Task Description (GOOD)**
```yaml
Task 70-Linux: CUDA Model Loading Implementation
Location: internal/gpu/linux/cuda/loading.go
Platform: Linux ONLY
Requirements:
  - Use real CUDA API calls
  - Include cudnn.h for DNN operations
  - NO CPU fallback - return error if GPU fails
  - NO mock implementations
  - Real memory allocation with cudaMalloc
  - Real model loading with cuDNN functions
Example functions to use:
  - cudnnCreate()
  - cudnnSetTensor4dDescriptor()
  - cudaMalloc()
  - cudaMemcpy()
```

### **Example Task Description (BAD)**
```yaml
Task 70: GPU Model Loading  # Missing platform
Details: Implement model loading with GPU support  # Too vague
Platform: Cross-platform  # WRONG - causes stubs
```