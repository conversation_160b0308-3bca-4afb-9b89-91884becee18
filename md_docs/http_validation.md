# HTTP Specification Validation

This document describes the comprehensive HTTP specification validation capabilities implemented in the NeuralMeterGo validation engine as part of Task 50.

## Overview

The HTTP validation component provides complete validation of HTTP-specific elements in test plans, ensuring compliance with RFC standards and catching common configuration errors before test execution.

## Validation Categories

### 1. HTTP Method Validation

**Validates**: All HTTP methods against RFC 7231 standards
**Supported Methods**: GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS
**Error Codes**: `INVALID_HTTP_METHOD`

**Features**:
- Case-sensitive validation (methods must be uppercase)
- Comprehensive support for all standard HTTP methods
- Clear error messages for invalid methods

**Example Valid Methods**:
```yaml
requests:
  - method: GET
  - method: POST
  - method: PUT
  - method: DELETE
  - method: PATCH
  - method: HEAD
  - method: OPTIONS
```

**Example Invalid Methods** (will trigger validation errors):
```yaml
requests:
  - method: INVALID    # Error: Invalid method
  - method: get        # Error: Must be uppercase
  - method: ""         # Error: Empty method
```

### 2. URL Format Validation

**Validates**: URL formats, template variables, and base URL relationships
**Error Codes**: `INVALID_URL`

**Supported URL Types**:
- **Absolute URLs**: `https://api.example.com/users`
- **Relative URLs with Base URL**: `/users` (requires `global.base_url`)
- **Template Variables**: `/users/{{user_id}}`, `{{base_url}}/api/users`
- **Query Parameters**: `/search?q={{query}}&limit={{limit}}`

**Validation Rules**:
- Empty URLs are rejected
- Relative URLs require a base URL in global configuration
- Template variables with `{{...}}` syntax are allowed
- Malformed URLs are caught and reported

**Examples**:
```yaml
# Valid configurations
global:
  base_url: "https://api.example.com"

scenarios:
  - requests:
    - url: "/users"                           # Valid: relative with base
    - url: "https://api.example.com/posts"    # Valid: absolute
    - url: "/users/{{user_id}}"              # Valid: template variable
    - url: "{{base_url}}/api/v1/data"        # Valid: full template

# Invalid configurations  
scenarios:
  - requests:
    - url: "/users"        # Invalid: relative without base_url
    - url: ""             # Invalid: empty URL
    - url: "not-a-url"    # Invalid: malformed URL
```

### 3. Header Validation

**Validates**: HTTP header names and values according to RFC 7230
**Severity**: Warning level (non-blocking)
**Error Codes**: `INVALID_HEADER`

**Validation Rules**:
- Header names must contain only valid characters: `[a-zA-Z0-9!#$%&'*+\-.^_`|~]`
- Header values cannot contain newline characters (`\n` or `\r`)
- Common headers are fully supported

**Examples**:
```yaml
# Valid headers
headers:
  Content-Type: "application/json"
  Authorization: "Bearer token123"
  X-Custom-Field: "value"
  User-Agent: "NeuralMeter/1.0"

# Invalid headers (warnings)
headers:
  "Invalid Header!": "value"              # Warning: Invalid character !
  "Normal-Header": "value\nwith\nnewlines" # Warning: Contains newlines
```

### 4. Request Body Validation

**Validates**: Request body format based on content type
**Severity**: Warning level
**Error Codes**: `INVALID_BODY`

**Features**:
- Content-type aware validation
- JSON format validation for `application/json` content type
- Method-specific validation (POST, PUT, PATCH typically have bodies)
- Flexible validation for different content types

**Examples**:
```yaml
# Valid body configurations
- method: POST
  headers:
    Content-Type: "application/json"
  body: '{"name": "test", "value": 123}'

- method: POST
  headers:
    Content-Type: "application/xml"
  body: '<root><item>value</item></root>'

# Invalid body (warning)
- method: POST
  headers:
    Content-Type: "application/json"
  body: 'not valid json'                   # Warning: Invalid JSON format
```

### 5. Assertion Validation

**Validates**: Assertion types, operators, and required fields
**Error Codes**: `INVALID_ASSERTION_TYPE`, `INVALID_ASSERTION_OPERATOR`, `MISSING_ASSERTION_VALUE`, `MISSING_JSON_PATH`, `MISSING_HEADER_NAME`

**Supported Assertion Types**:
- `status_code`: Validate HTTP status codes
- `response_time`: Validate response timing
- `contains`: Check response content
- `json_path`: JSONPath-based validation
- `header_exists`: Verify header presence
- `regex`: Regular expression matching

**Supported Operators**:
- `eq`, `ne`: Equality operators
- `lt`, `le`, `gt`, `ge`: Comparison operators  
- `contains`, `not_contains`: String matching
- `matches`: Pattern matching

**Examples**:
```yaml
# Valid assertions
assertions:
  - type: status_code
    value: 200
  - type: response_time
    operator: lt
    value: 1000
  - type: json_path
    field: "$.data.id"
    operator: gt
    value: 0
  - type: header_exists
    field: "Content-Type"

# Invalid assertions (errors)
assertions:
  - type: invalid_type          # Error: Invalid assertion type
    value: 200
  - type: status_code          # Error: Missing required value
  - type: json_path            # Error: Missing field for json_path
    value: 200
```

### 6. Extraction Validation

**Validates**: Data extraction configurations
**Error Codes**: `INVALID_EXTRACT_TYPE`, `MISSING_EXTRACT_PATH`, `INVALID_REGEX`

**Supported Extraction Types**:
- `json_path`: JSONPath expressions
- `xpath`: XPath expressions
- `regex`: Regular expressions
- `header`: HTTP header extraction
- `css_selector`: CSS selectors

**Validation Rules**:
- All extractions require a name and path
- Regex patterns are validated for syntax
- Type-specific validation rules apply

**Examples**:
```yaml
# Valid extractions
extract:
  - name: user_id
    type: json_path
    path: "$.data.id"
  - name: title
    type: xpath
    path: "//title/text()"
  - name: token
    type: regex
    path: '"token":"([^"]+)"'
  - name: content_type
    type: header
    path: "Content-Type"

# Invalid extractions (errors)
extract:
  - name: bad_type
    type: invalid_type           # Error: Invalid extraction type
    path: "$.data"
  - name: no_path
    type: json_path             # Error: Missing path
  - name: bad_regex
    type: regex
    path: "[invalid regex"      # Error: Invalid regex syntax
```

## Integration Features

### Template Variable Support

The HTTP validator fully supports template variables in URLs and other fields:

```yaml
scenarios:
  - requests:
    - url: "/users/{{user_id}}/posts/{{post_id}}"
    - url: "{{base_url}}/api/{{version}}/data"
    - body: '{"username": "{{username}}", "password": "{{password}}"}'
```

### Base URL Resolution

Relative URLs are properly validated against base URL configuration:

```yaml
global:
  base_url: "https://api.example.com"

scenarios:
  - requests:
    - url: "/users"              # Resolves to https://api.example.com/users
    - url: "/api/v1/posts"       # Resolves to https://api.example.com/api/v1/posts
```

### Multi-Scenario Support

Validation works across complex test plans with multiple scenarios:

```yaml
scenarios:
  - name: "Authentication Flow"
    requests:
      - name: "Login"
        method: POST
        url: "/auth/login"
        assertions:
          - type: status_code
            value: 200
        extract:
          - name: auth_token
            type: json_path
            path: "$.token"
            
  - name: "API Operations"
    requests:
      - name: "Get User Data"
        method: GET
        url: "/users/profile"
        headers:
          Authorization: "Bearer {{auth_token}}"
```

## Validation Engine Integration

The HTTP validator integrates seamlessly with the broader validation engine:

### Configuration Options

```go
config := &validation.EngineConfig{
    StrictMode:        true,   // Enable strict HTTP validation
    EnableWarnings:    true,   // Include header/body warnings
    EnableSuggestions: true,   // Provide improvement suggestions
}
engine := validation.NewValidationEngineWithConfig(config)
```

### Programmatic Usage

```go
// Validate a test plan
result := engine.ValidateTestPlan(testPlan)

// Check for HTTP-specific issues
for _, issue := range result.Issues {
    if issue.Category == "http" {
        fmt.Printf("HTTP Issue: %s - %s\n", issue.Code, issue.Message)
    }
}
```

### Error Reporting

HTTP validation provides detailed, actionable error messages:

```
Error: INVALID_HTTP_METHOD - Invalid HTTP method 'INVALID'. Valid methods: GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS
Field: scenarios[0].requests[0].method
Suggestion: Use a standard HTTP method

Warning: INVALID_HEADER - Header values cannot contain newline characters  
Field: scenarios[0].requests[0].headers.Content-Type
Category: http
```

## Testing and Quality Assurance

The HTTP validation implementation includes comprehensive test coverage:

- **Unit Tests**: Individual validator component testing
- **Integration Tests**: Full validation engine testing with HTTP components
- **Fixture Tests**: Real-world test plan validation
- **Edge Case Tests**: Template variables, complex scenarios, error conditions

## Performance Characteristics

- **Efficient Validation**: Minimal performance overhead
- **Early Detection**: Catches issues before test execution
- **Configurable Strictness**: Adjustable validation levels
- **Detailed Reporting**: Comprehensive error context

## Compliance and Standards

The HTTP validator ensures compliance with:

- **RFC 7231**: HTTP/1.1 Semantics and Content (HTTP methods)
- **RFC 7230**: HTTP/1.1 Message Syntax and Routing (headers)
- **JSONPath**: Standard JSONPath expressions
- **RegExp**: Standard regular expression syntax

This comprehensive HTTP validation ensures that test plans are correctly configured and will execute successfully, catching common configuration errors early in the development process. 