# Task 6 CLI Commands and Expected Outputs

## Overview

This document provides a comprehensive list of CLI commands for testing the NeuralMeter CLI implementation across all Task 6 subtasks. Each command is mapped to specific subtasks with expected outputs and validation criteria.

## Command Testing Matrix

### Task 6.1: CLI Command Structure

| Command | Expected Output | Validation Criteria | Subtask ID |
|---------|----------------|-------------------|------------|
| `neuralmeter --version` | `NeuralMeter CLI v1.0.0 (build: abc123)` | Version string format, build info present | 6.1 |
| `neuralmeter --help` | Help text with available commands and options | All major command groups listed (run, server, gpu, config) | 6.1 |
| `neuralmeter run --help` | Detailed help for run command | Options: --url, --concurrency, --duration, --config | 6.1 |
| `neuralmeter run --url http://system-b-ip/health --requests 10` | ```<br>Starting load test...<br>Target: http://system-b-ip/health<br>Requests: 10<br>Concurrency: 1<br><br>Results:<br>Total requests: 10<br>Successful: 10<br>Failed: 0<br>Average response time: 15ms<br>95th percentile: 25ms<br>``` | Successful connection to System B, accurate request count | 6.1 |

### Task 6.2: Server Daemon Mode

| Command | Expected Output | Validation Criteria | Subtask ID |
|---------|----------------|-------------------|------------|
| `neuralmeter server start --daemon` | ```<br>Starting NeuralMeter server in daemon mode...<br>PID: 12345<br>API endpoint: http://localhost:8080<br>Server started successfully<br>``` | PID file created at `/var/run/neuralmeter.pid` | 6.2 |
| `neuralmeter server status` | ```<br>NeuralMeter Server Status:<br>Status: Running<br>PID: 12345<br>Uptime: 2h 15m 30s<br>Active jobs: 3<br>Memory usage: 256MB<br>``` | Accurate PID, uptime calculation | 6.2 |
| `neuralmeter server stop` | ```<br>Stopping NeuralMeter server...<br>Gracefully shutting down active jobs...<br>Server stopped successfully<br>``` | Graceful shutdown, PID file removed | 6.2 |
| `neuralmeter server restart` | ```<br>Restarting NeuralMeter server...<br>Stopping current instance (PID: 12345)...<br>Starting new instance...<br>New PID: 12346<br>Server restarted successfully<br>``` | Old process terminated, new PID assigned | 6.2 |

### Task 6.3: Remote API Server

| Command | Expected Output | Validation Criteria | Subtask ID |
|---------|----------------|-------------------|------------|
| `neuralmeter server start --api-port 8080` | ```<br>Starting NeuralMeter API server...<br>Listening on: http://0.0.0.0:8080<br>API endpoints available:<br>  POST /api/tests - Submit test plans<br>  GET /api/tests/status - Job status<br>  GET /api/metrics/live - Real-time metrics<br>``` | API server accessible on specified port | 6.3 |
| `curl -X POST http://localhost:8080/api/tests -H "Content-Type: application/json" -d '{"target_url":"http://system-b-ip","duration":"5m","concurrency":100}'` | ```json<br>{<br>  "job_id": "test-job-789",<br>  "status": "queued",<br>  "target_url": "http://system-b-ip",<br>  "duration": "5m",<br>  "concurrency": 100,<br>  "created_at": "2025-07-15T10:30:00Z"<br>}<br>``` | Valid job ID returned, test queued | 6.3 |
| `curl http://localhost:8080/api/tests/test-job-789/status` | ```json<br>{<br>  "job_id": "test-job-789",<br>  "status": "running",<br>  "progress": 65,<br>  "elapsed_time": "3m 15s",<br>  "remaining_time": "1m 45s",<br>  "current_rps": 847,<br>  "total_requests": 156780<br>}<br>``` | Accurate progress tracking, real-time metrics | 6.3 |
| `curl http://localhost:8080/api/metrics/live` | ```json<br>{<br>  "timestamp": "2025-07-15T10:33:15Z",<br>  "active_jobs": 2,<br>  "total_rps": 1543,<br>  "memory_usage": "412MB",<br>  "cpu_usage": "67%",<br>  "gpu_utilization": "84%"<br>}<br>``` | Real-time system and job metrics | 6.3 |

### Task 6.4: Test Plan Processing

| Command | Expected Output | Validation Criteria | Subtask ID |
|---------|----------------|-------------------|------------|
| `neuralmeter run test-plan.yaml` | ```<br>Loading test plan: test-plan.yaml<br>Validating configuration...<br>Target: http://system-b-ip<br>Scenarios: 2<br>Total virtual users: 1000<br><br>Starting scenario: Basic Load Test<br>Ramping up to 500 users over 2m...<br>Starting scenario: API Stress Test<br>Ramping up to 500 users over 2m...<br><br>Test Results Summary:<br>Duration: 10m 0s<br>Total requests: 485,230<br>Success rate: 99.97%<br>Average response time: 23ms<br>``` | YAML parsed correctly, multiple scenarios executed | 6.4 |
| `neuralmeter validate test-plan.yaml` | ```<br>Validating test plan: test-plan.yaml<br>✅ Schema validation passed<br>✅ Target URLs reachable<br>✅ Load patterns valid<br>✅ Assertions syntax correct<br>Test plan is valid and ready for execution<br>``` | All validation checks pass | 6.4 |
| `neuralmeter run --config config.yaml --target http://system-b-ip` | ```<br>Loading configuration: config.yaml<br>Override target: http://system-b-ip<br>Configuration merged successfully<br><br>Test execution starting...<br>Target: http://system-b-ip (overridden)<br>Workers: 8<br>Connection pool size: 1000<br>``` | Configuration loading and CLI override working | 6.4 |

### Task 6.5: GPU-Accelerated Load Testing

| Command | Expected Output | Validation Criteria | Subtask ID |
|---------|----------------|-------------------|------------|
| `neuralmeter gpu list` | ```<br>Available GPU devices:<br>GPU 0: NVIDIA RTX 4090 (24GB VRAM)<br>  - CUDA Version: 12.1<br>  - Compute Capability: 8.9<br>  - Memory Free: 23.2GB<br>  - Temperature: 42°C<br><br>GPU 1: NVIDIA RTX 4080 (16GB VRAM)<br>  - CUDA Version: 12.1<br>  - Compute Capability: 8.9<br>  - Memory Free: 15.8GB<br>  - Temperature: 38°C<br>``` | GPU detection and status information | 6.5 |
| `neuralmeter run --gpu-accelerated test-plan.yaml` | ```<br>GPU acceleration enabled<br>Selected GPU: 0 (NVIDIA RTX 4090)<br>Loading AI models...<br>Model loaded: 2.3s<br>GPU memory allocated: 4.2GB<br><br>Starting GPU-accelerated load test...<br>Payload generation rate: 15,000 prompts/sec<br>GPU utilization: 87%<br>``` | GPU properly utilized, high payload generation rate | 6.5 |
| `neuralmeter gpu benchmark --target http://system-b-ip/load/cpu` | ```<br>GPU Benchmark Mode<br>Target: http://system-b-ip/load/cpu<br>Warming up GPU...<br><br>Benchmark Results:<br>GPU-accelerated: 45,230 RPS<br>CPU-only: 8,940 RPS<br>Performance improvement: 5.06x<br>GPU utilization: 92%<br>Memory usage: 6.8GB<br>``` | Significant performance improvement with GPU | 6.5 |

### Task 6.6: HTTP Load Generation Engine

| Command | Expected Output | Validation Criteria | Subtask ID |
|---------|----------------|-------------------|------------|
| `neuralmeter run --url http://system-b-ip/load/fast --concurrency 5000 --duration 10m` | ```<br>High-concurrency load test starting...<br>Target: http://system-b-ip/load/fast<br>Concurrency: 5000<br>Duration: 10m<br><br>Connection pool initialized: 5000 connections<br>Ramp-up complete: 15s<br><br>Live metrics (5m elapsed):<br>Current RPS: 52,340<br>Active connections: 4,998<br>Connection reuse: 96.4%<br>Response time p95: 8ms<br>``` | High RPS sustained, efficient connection reuse | 6.6 |
| `neuralmeter run --load-pattern burst --peak-rps 75000 --target http://system-b-ip` | ```<br>Burst load pattern activated<br>Target peak RPS: 75,000<br>Burst duration: 30s<br>Recovery time: 60s<br><br>Burst 1: Ramping to 75,000 RPS...<br>Peak achieved: 74,832 RPS<br>Burst complete, cooling down...<br><br>Burst 2: Ramping to 75,000 RPS...<br>Peak achieved: 75,124 RPS<br>``` | Peak RPS achieved, burst patterns working | 6.6 |
| `neuralmeter run --url http://system-b-ip --connection-pool-size 10000` | ```<br>Custom connection pool configuration<br>Pool size: 10,000 connections<br>Pool warmup: 25s<br><br>Pool statistics:<br>Active: 9,847<br>Idle: 153<br>Reuse rate: 98.2%<br>Pool efficiency: Excellent<br>``` | Large connection pools managed efficiently | 6.6 |

### Task 6.7: Real-time Result Streaming

| Command | Expected Output | Validation Criteria | Subtask ID |
|---------|----------------|-------------------|------------|
| `neuralmeter run --stream-results ws://localhost:9090 test-plan.yaml` | ```<br>Connecting to streaming endpoint: ws://localhost:9090<br>WebSocket connected successfully<br>Starting test with real-time streaming...<br><br>Streaming live metrics...<br>[10:30:15] RPS: 1,245 | Resp Time: 12ms | Errors: 0<br>[10:30:20] RPS: 2,847 | Resp Time: 15ms | Errors: 2<br>[10:30:25] RPS: 4,231 | Resp Time: 18ms | Errors: 1<br>``` | WebSocket connection established, metrics streaming | 6.7 |
| `neuralmeter run --stream-format json --stream-results http://localhost:8080/metrics/live` | ```<br>Streaming to HTTP endpoint in JSON format<br>Stream interval: 5 seconds<br><br>Sending metrics batch 1...<br>Sending metrics batch 2...<br>Sending metrics batch 3...<br>Stream completed: 120 batches sent<br>``` | JSON format streaming, batch delivery | 6.7 |
| `neuralmeter server start --stream-port 9090` | ```<br>Starting server with streaming capability<br>Stream server listening on: ws://0.0.0.0:9090<br>HTTP metrics endpoint: http://0.0.0.0:9090/metrics<br>Ready for streaming clients<br>``` | Streaming server available for connections | 6.7 |

### Task 6.8: GPU Command Integration

| Command | Expected Output | Validation Criteria | Subtask ID |
|---------|----------------|-------------------|------------|
| `neuralmeter gpu status` | ```<br>GPU Status Report<br>==================<br>GPU 0: NVIDIA RTX 4090<br>  Status: Available<br>  Temperature: 45°C<br>  Memory: 18.2GB / 24GB (76%)<br>  Utilization: 23%<br>  Power: 185W / 450W<br><br>CUDA Status: Ready<br>Driver Version: 535.86.10<br>``` | Detailed GPU status and health information | 6.8 |
| `neuralmeter gpu monitor --interval 2s` | ```<br>GPU Monitoring (2s intervals)<br>Press Ctrl+C to stop...<br><br>[10:30:00] GPU 0: 34% util, 67°C, 12.4GB mem<br>[10:30:02] GPU 0: 87% util, 71°C, 18.7GB mem<br>[10:30:04] GPU 0: 91% util, 74°C, 19.2GB mem<br>[10:30:06] GPU 0: 89% util, 73°C, 18.9GB mem<br>``` | Real-time GPU monitoring during tests | 6.8 |
| `neuralmeter gpu benchmark --duration 60s` | ```<br>GPU Benchmark Test (60 seconds)<br>Testing inference performance...<br><br>Warming up... (10s)<br>Benchmark running... (60s)<br><br>Results:<br>Peak inference rate: 23,450 inferences/sec<br>Average inference rate: 22,180 inferences/sec<br>Memory bandwidth: 1,847 GB/s<br>Power efficiency: 47.2 inferences/watt<br>``` | GPU performance characteristics measured | 6.8 |

### Task 6.9: Configuration Management

| Command | Expected Output | Validation Criteria | Subtask ID |
|---------|----------------|-------------------|------------|
| `neuralmeter config validate --config config.yaml` | ```<br>Validating configuration: config.yaml<br><br>✅ Syntax validation passed<br>✅ Schema validation passed<br>✅ Target endpoints reachable<br>✅ GPU configuration valid<br>✅ Load patterns valid<br>✅ Output settings valid<br><br>Configuration is valid<br>``` | All configuration sections validated | 6.9 |
| `neuralmeter config show --config config.yaml` | ```<br>Current Configuration:<br>==================<br>Targets:<br>  - http://system-b-ip:80<br>  - https://system-b-ip:443<br><br>Load Settings:<br>  Default concurrency: 100<br>  Default duration: 5m<br>  Connection timeout: 30s<br><br>GPU Settings:<br>  Enabled: true<br>  Device: auto<br>  Memory limit: 80%<br>``` | Configuration properly parsed and displayed | 6.9 |
| `neuralmeter run --config config.yaml --set concurrency=500` | ```<br>Loading configuration: config.yaml<br>Applying override: concurrency=500<br><br>Configuration applied:<br>Base config: config.yaml<br>Overrides: concurrency=500<br><br>Final settings:<br>Concurrency: 500 (overridden)<br>Duration: 5m (from config)<br>Target: http://system-b-ip (from config)<br>``` | Configuration overrides working correctly | 6.9 |

### Task 6.10: Output Formatting and Logging

| Command | Expected Output | Validation Criteria | Subtask ID |
|---------|----------------|-------------------|------------|
| `neuralmeter run --output json --url http://system-b-ip/health --requests 100` | ```json<br>{<br>  "test_id": "test-20250715-103045",<br>  "start_time": "2025-07-15T10:30:45Z",<br>  "end_time": "2025-07-15T10:31:23Z",<br>  "duration": "38.234s",<br>  "target": "http://system-b-ip/health",<br>  "total_requests": 100,<br>  "successful_requests": 100,<br>  "failed_requests": 0,<br>  "success_rate": 100.0,<br>  "average_response_time": "15.3ms",<br>  "min_response_time": "8.1ms",<br>  "max_response_time": "47.2ms",<br>  "percentiles": {<br>    "p50": "14.2ms",<br>    "p95": "28.7ms",<br>    "p99": "41.5ms"<br>  }<br>}<br>``` | Valid JSON format, all metrics included | 6.10 |
| `neuralmeter run --output text --log-level debug --url http://system-b-ip` | ```<br>[DEBUG] 2025-07-15T10:30:45Z Starting load test<br>[DEBUG] 2025-07-15T10:30:45Z Initializing HTTP client<br>[DEBUG] 2025-07-15T10:30:45Z Connection pool created: 100 connections<br>[INFO]  2025-07-15T10:30:46Z Test started, target: http://system-b-ip<br>[DEBUG] 2025-07-15T10:30:47Z Request 1: 200 OK, 12ms<br>[DEBUG] 2025-07-15T10:30:47Z Request 2: 200 OK, 15ms<br>[INFO]  2025-07-15T10:31:45Z Test completed successfully<br><br>Test Results:<br>Duration: 59.2s<br>Total Requests: 5,432<br>Success Rate: 99.98%<br>Average Response Time: 14.7ms<br>``` | Debug logging with detailed information | 6.10 |
| `neuralmeter run --log-file /var/log/neuralmeter-test.log --output csv test.yaml` | ```<br>Logging to: /var/log/neuralmeter-test.log<br>Output format: CSV<br><br>Test execution started...<br>Results saved to: test-results-20250715-103045.csv<br><br>timestamp,request_id,response_time,status_code,error<br>2025-07-15T10:30:45.123Z,1,12.3,200,<br>2025-07-15T10:30:45.145Z,2,15.7,200,<br>2025-07-15T10:30:45.167Z,3,11.9,200,<br>``` | Log file created, CSV output format | 6.10 |

## Error Scenarios and Validation

### Network Error Handling

| Command | Expected Output | Validation Criteria | Subtask ID |
|---------|----------------|-------------------|------------|
| `neuralmeter run --url http://unreachable-host/test` | ```<br>Starting load test...<br>ERROR: Failed to connect to target<br>Target: http://unreachable-host/test<br>Error: dial tcp: lookup unreachable-host: no such host<br><br>Test aborted due to connection failure<br>``` | Proper error handling and reporting | All |
| `neuralmeter server start --api-port 80` | ```<br>ERROR: Failed to start API server<br>Port 80: permission denied<br><br>Suggestion: Try a port > 1024 or run with sudo<br>Example: neuralmeter server start --api-port 8080<br>``` | Clear error messages with suggestions | 6.2, 6.3 |

### Configuration Error Handling

| Command | Expected Output | Validation Criteria | Subtask ID |
|---------|----------------|-------------------|------------|
| `neuralmeter run invalid-config.yaml` | ```<br>ERROR: Configuration validation failed<br>File: invalid-config.yaml<br>Issues found:<br>  Line 12: Invalid load pattern 'invalid_pattern'<br>  Line 18: Missing required field 'target_url'<br>  Line 25: Invalid duration format '5x'<br><br>Please fix these issues and try again<br>``` | Detailed validation errors with line numbers | 6.9 |

## Performance Validation Commands

### Stress Testing

| Command | Expected Output | Validation Criteria | Subtask ID |
|---------|----------------|-------------------|------------|
| `neuralmeter run --concurrency 10000 --duration 30m --url http://system-b-ip/load/fast` | ```<br>High-load stress test<br>Target: http://system-b-ip/load/fast<br>Concurrency: 10,000<br>Duration: 30 minutes<br><br>Warming up connection pool...<br>Pool ready: 10,000 connections<br><br>Test running... (15m elapsed)<br>Current metrics:<br>  RPS: 78,543<br>  Active connections: 9,997<br>  Memory usage: 1.2GB<br>  CPU usage: 67%<br>``` | Sustained high-load performance | 6.6 |

## Notes

1. **System B Dependency**: All commands assume System B is running and accessible at `system-b-ip`
2. **Timestamps**: All timestamps should use ISO 8601 format (UTC)
3. **Metrics Accuracy**: Response times and counts should match System B's measurements within 1% margin
4. **Error Handling**: All commands should provide helpful error messages and suggestions
5. **Resource Management**: Memory usage should remain stable during long-running tests
6. **Signal Handling**: All commands should respond to SIGINT (Ctrl+C) gracefully

## Validation Checklist

For each command test:
- [ ] Command executes without errors
- [ ] Output format matches expected structure
- [ ] Metrics are accurate (compare with System B logs)
- [ ] Error handling works correctly
- [ ] Resource usage stays within limits
- [ ] All specified subtask requirements are met