# Task Update Commands - OS Build Tags Implementation

## **Immediate Action Items**

These are the specific Claude Task Master commands to implement OS build tags across all GPU-related tasks:

### **Core Task Updates**

#### **Task 6 - CLI Interface Implementation**
```bash
task-master update --id=6 --prompt="CRITICAL UPDATE: Add OS-specific build tags and platform guidance:

BUILD TAGS:
- macOS: //go:build macos && metal (requires macOS machine, Metal framework)
- Linux: //go:build linux && (cuda || rocm) (requires Linux machine, CUDA/ROCm)
- Windows: //go:build windows && directml (requires Windows machine, DirectML)

PLATFORM SWITCH INSTRUCTIONS:
- For macOS binary: Switch to macOS machine, install Xcode Command Line Tools, build with 'go build -tags \"macos metal\" -o neuralmeter-macos'
- For Linux binary: Switch to Linux machine, install CUDA/ROCm, build with 'go build -tags \"linux cuda rocm\" -o neuralmeter-linux'  
- For Windows binary: Switch to Windows machine, install Visual Studio tools, build with 'go build -tags \"windows directml\" -o neuralmeter-windows.exe'

CRITICAL: No cross-compilation - each binary MUST be built on target OS for proper GPU library linking. Remove all CPU fallback code."
```

#### **Task 69 - GPU Detection & Initialization**
```bash
task-master update --id=69 --prompt="MAJOR UPDATE: Add platform-specific build tags and remove CPU fallback:

PLATFORM BUILD TAGS:
- macOS Metal: //go:build macos && metal && !cuda && !rocm
- Linux CUDA/ROCm: //go:build linux && (cuda || rocm) && !metal
- Windows DirectML: //go:build windows && directml && !metal && !cuda

PLATFORM REQUIREMENTS:
- macOS: Must use Metal.framework on macOS machine
- Linux: Must use CUDA Driver API or ROCm HIP on Linux machine  
- Windows: Must use DirectML API on Windows machine

SWITCH INSTRUCTIONS:
- Metal implementation: Switch to macOS, verify Metal support with 'system_profiler SPDisplaysDataType'
- CUDA implementation: Switch to Linux, install CUDA toolkit, verify with 'nvidia-smi'
- ROCm implementation: Switch to Linux, install ROCm, verify with 'rocm-smi'
- DirectML implementation: Switch to Windows, install Windows SDK, verify DirectML headers

CRITICAL: Replace ALL mock GPU implementations with real vendor APIs. Remove CPU fallback completely."
```

#### **Task 70 - GPU Model Loading & Inference Pipeline**  
```bash
task-master update --id=70 --prompt="UPDATE: Add OS build tags for model loading:

PLATFORM IMPLEMENTATIONS:
- macOS (//go:build macos && metal): Metal command buffers, MPS neural networks, MTLDevice model loading
- Linux (//go:build linux && (cuda || rocm)): CUDA memory allocation, cuDNN model loading, or ROCm MIOpen
- Windows (//go:build windows && directml): DirectML operator graphs, D3D12 resource management

SWITCH REQUIREMENTS:
- macOS: Use Mac with Metal Performance Shaders for neural network model loading
- Linux: Use Linux with NVIDIA GPU for CUDA or AMD GPU for ROCm tensor operations
- Windows: Use Windows with DirectML for ONNX Runtime integration

Remove any CPU-based model loading - GPU exclusive for performance."
```

#### **Task 72 - GPU Error Handling & Recovery**
```bash
task-master update --id=72 --prompt="CRITICAL UPDATE: Platform-specific error handling, remove CPU fallback:

PLATFORM ERROR HANDLING:
- macOS (//go:build macos && metal): Metal device lost, command buffer failures, MPS errors - recover via Metal device reset
- Linux (//go:build linux && (cuda || rocm)): CUDA OOM, driver errors, context loss - recover via CUDA/ROCm context reset  
- Windows (//go:build windows && directml): DirectML device removal, D3D12 failures - recover via DirectML device recreation

CRITICAL CHANGE: REMOVE ALL CPU FALLBACK CODE. GPU failure = test failure. This is a GPU performance tool - it should NOT degrade to CPU.

SWITCH INSTRUCTIONS:
- Metal errors: Implement on macOS with Metal error codes
- CUDA errors: Implement on Linux with CUDA error handling
- DirectML errors: Implement on Windows with DirectML/D3D12 error handling"
```

### **GPU Subtask Updates**

#### **Task 69 Subtasks - Platform-Specific Detection**
```bash
# Task 69.1 - Basic GPU Detection
task-master update-subtask --id=69.1 --prompt="Add build tag: //go:build (macos && metal) || (linux && (cuda || rocm)) || (windows && directml). 
SWITCH TO: macOS for Metal detection, Linux for CUDA/ROCm detection, Windows for DirectML detection.
Remove mock implementations - use real GPU vendor APIs only."

# Task 69.2 - CUDA Interface Integration  
task-master update-subtask --id=69.2 --prompt="Add build tag: //go:build linux && cuda && !metal && !directml.
SWITCH TO: Linux machine with NVIDIA GPU and CUDA Toolkit installed.
Implement real CUDA Driver API calls - no mocks or CPU fallback."

# Task 69.3 - GPU Memory Management
task-master update-subtask --id=69.3 --prompt="Add platform tags:
- macOS: //go:build macos && metal (Metal unified memory)
- Linux: //go:build linux && (cuda || rocm) (CUDA/ROCm device memory)  
- Windows: //go:build windows && directml (DirectML/D3D12 resources)
SWITCH TO appropriate platform for real GPU memory allocation."

# Task 69.4 - Performance Monitoring Integration
task-master update-subtask --id=69.4 --prompt="Add build tags for platform-specific monitoring:
- macOS: //go:build macos && metal (Metal performance counters)
- Linux: //go:build linux && (cuda || rocm) (NVML/ROCm monitoring APIs)
- Windows: //go:build windows && directml (DirectML telemetry)
SWITCH TO target platform for native GPU monitoring implementation."

# Task 69.5 - Error Handling and Recovery
task-master update-subtask --id=69.5 --prompt="Add platform-specific error handling tags. Remove CPU fallback.
- macOS: Metal error codes and device recovery
- Linux: CUDA/ROCm error handling and context recovery
- Windows: DirectML/D3D12 error handling and device recovery
SWITCH TO target platform for native GPU error handling."

# Task 69.6 - Configuration and Optimization
task-master update-subtask --id=69.6 --prompt="Add platform optimization tags:
- macOS: Metal shader optimization and MPS tuning
- Linux: CUDA kernel optimization and ROCm tuning  
- Windows: DirectML operator optimization
SWITCH TO target platform for vendor-specific optimizations."
```

#### **Task 70 Subtasks - Model Loading Platform Tags**
```bash
# Update all Task 70 subtasks with platform requirements
for subtask in 70.1 70.2 70.3 70.4 70.5; do
  task-master update-subtask --id=$subtask --prompt="Add platform build tags:
  - macOS: //go:build macos && metal (Metal model loading)
  - Linux: //go:build linux && (cuda || rocm) (CUDA/ROCm tensor ops)
  - Windows: //go:build windows && directml (DirectML operator graphs)
  SWITCH TO appropriate platform for native GPU model loading implementation."
done
```

#### **Task 72 Subtasks - Error Handling Platform Tags**
```bash
# Task 72.1 through 72.8 - Platform-specific error handling
for subtask in 72.1 72.2 72.3 72.4 72.5 72.6 72.7 72.8; do
  task-master update-subtask --id=$subtask --prompt="Add platform error handling tags and REMOVE CPU fallback:
  - macOS: //go:build macos && metal (Metal device errors only)
  - Linux: //go:build linux && (cuda || rocm) (CUDA/ROCm errors only)
  - Windows: //go:build windows && directml (DirectML errors only)
  CRITICAL: No CPU fallback - GPU required for operation. SWITCH TO target platform for implementation."
done
```

### **Additional GPU Task Updates**

#### **Task 71 - GPU Performance Metrics**
```bash
task-master update --id=71 --prompt="Add platform-specific performance monitoring build tags:
- macOS: //go:build macos && metal (Metal performance shaders counters)
- Linux: //go:build linux && (cuda || rocm) (NVML/ROCm monitoring APIs)
- Windows: //go:build windows && directml (DirectML telemetry and D3D12 counters)
SWITCH TO target platform for native GPU performance monitoring implementation."
```

#### **Task 73 - Multi-GPU Support**
```bash
task-master update --id=73 --prompt="Add multi-GPU platform tags:
- macOS: //go:build macos && metal (Multiple Metal devices, external GPU support)
- Linux: //go:build linux && (cuda || rocm) (CUDA multi-GPU or ROCm multi-device)
- Windows: //go:build windows && directml (DirectML multi-adapter support)
REQUIREMENTS: Multi-GPU hardware on target platform. SWITCH TO platform with multiple GPUs for testing."
```

#### **Task 74 - GPU Configuration Interface**
```bash
task-master update --id=74 --prompt="Add configuration platform tags:
- macOS: //go:build macos && metal (Metal device configuration)
- Linux: //go:build linux && (cuda || rocm) (CUDA/ROCm device settings)
- Windows: //go:build windows && directml (DirectML adapter configuration)
Platform-specific GPU configuration interfaces required."
```

### **Build System Updates**

#### **Add Build Documentation**
```bash
task-master add-task --title="Platform Build Documentation" --prompt="Create comprehensive build documentation with OS-specific instructions:

MACOS BUILD:
- Prerequisites: macOS 10.14+, Xcode Command Line Tools, Metal framework
- Command: go build -tags 'macos metal' -o neuralmeter-macos cmd/neuralmeter/main.go
- Test: ./neuralmeter-macos gpu list

LINUX BUILD:
- Prerequisites: Ubuntu 18.04+, build-essential, CUDA Toolkit OR ROCm
- Command: go build -tags 'linux cuda rocm' -o neuralmeter-linux cmd/neuralmeter/main.go  
- Test: ./neuralmeter-linux gpu list

WINDOWS BUILD:
- Prerequisites: Windows 10 1903+, Visual Studio Build Tools, Windows SDK
- Command: go build -tags 'windows directml' -o neuralmeter-windows.exe cmd/neuralmeter/main.go
- Test: neuralmeter-windows.exe gpu list

CRITICAL: No cross-compilation - each binary must be built on target OS."
```

### **Global Updates**

#### **Remove CPU Fallback Globally**
```bash
task-master update --global --prompt="ARCHITECTURAL CHANGE: This is a GPU-focused performance testing tool. Remove ALL CPU fallback code from ALL GPU operations. CPU should only be used for:
- Application control and coordination
- User interface rendering  
- Configuration management
- Logging and monitoring

GPU operations that fail should cause test failure, not CPU fallback. Update all tasks to reflect this GPU-first philosophy with platform-specific build tags."
```

## **Validation Commands**

### **Verify Updates Applied**
```bash
# Check that build tags were added to key tasks
task-master show 6,69,70,72 --format=detailed

# Verify CPU fallback removal
task-master search "CPU fallback" --should-be-empty

# Confirm platform guidance added
task-master search "switch to" --count-expected=20+
```

### **Build Validation Per Platform**

#### **macOS Validation**
```bash
# Should only work on macOS
go build -tags 'macos metal' -o neuralmeter-macos cmd/neuralmeter/main.go
./neuralmeter-macos gpu list
# Should detect Metal GPUs or fail with clear message
```

#### **Linux Validation** 
```bash
# Should only work on Linux with CUDA/ROCm
go build -tags 'linux cuda rocm' -o neuralmeter-linux cmd/neuralmeter/main.go
./neuralmeter-linux gpu list
# Should detect CUDA/ROCm GPUs or fail with clear message
```

#### **Windows Validation**
```bash
# Should only work on Windows with DirectML
go build -tags 'windows directml' -o neuralmeter-windows.exe cmd/neuralmeter/main.go
neuralmeter-windows.exe gpu list
# Should detect DirectML adapters or fail with clear message
```

## **Expected Outcomes**

After applying these updates:

1. **Clear Platform Guidance**: Every GPU task shows which OS to use
2. **No Cross-Platform Confusion**: Build tags prevent wrong-platform builds  
3. **Real GPU Implementation**: All mock code replaced with vendor APIs
4. **No CPU Fallback**: Tool fails gracefully when GPU unavailable
5. **Platform-Specific Binaries**: Three optimized binaries for each OS
6. **Developer Productivity**: Clear switch instructions reduce setup time

## **Implementation Order**

1. **Start with Core Tasks**: Apply updates to Tasks 6, 69, 70, 72 first
2. **Update Subtasks**: Apply platform tags to all GPU subtasks  
3. **Add Documentation**: Create build documentation task
4. **Global Cleanup**: Remove CPU fallback references globally
5. **Validate Builds**: Test on each platform to ensure tags work
6. **Update Dependencies**: Ensure task dependencies reflect platform requirements