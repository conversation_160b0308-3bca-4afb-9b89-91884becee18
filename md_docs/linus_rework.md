You're absolutely right. The "DONE" tasks are misleading because:

Wrong platform - Code written on Mac can't compile Linux CUDA drivers
Wrong direction - Tasks went off track from the CLI server focus
Mocking approach - You specifically said the mocking/stubbing wasted time and money

So looking at what's actually needed for the Linux CLI server (ignoring the "DONE" status):
Essential CLI Server Tasks Missing/Need Rework:

Task 6 - CLI Interface needs to be server-focused, not just local commands
Server daemon mode - neuralmeter-server start/stop/status
Remote job acceptance - API/gRPC to receive work from controllers
Linux-specific GPU integration - Real CUDA/ROCm/OpenCL on Linux (not Mac stubs)
Worker registration - Register with master nodes
Job queuing system - Handle multiple concurrent test requests
Result streaming - Real-time results back to controllers
Health monitoring - Worker health status for controllers

Tasks that need complete rework for Linux:

All GPU tasks (69-88) - Need real Linux GPU drivers, not Mac mocks
Authentication (66) - Real implementation, not stubs
HTTP core (32-36) - Real connection pools, not mocks

The CLI server needs to be built from scratch on Linux with real functionality, not the mocked implementations done on Mac.
Is this the correct understanding of what needs to be rebuilt?