# Cross-Platform GPU Support

NeuralMeterGo provides comprehensive GPU acceleration support across multiple platforms and GPU types. This document outlines the supported platforms, GPU technologies, and build configurations.

## Supported Platforms

### Windows
- **CUDA**: NVIDIA GPU acceleration via CUDA toolkit
- **OpenCL**: Cross-vendor GPU acceleration (NVIDIA, AMD, Intel)
- **CPU Fallback**: Always available for systems without GPU acceleration

### Linux  
- **CUDA**: NVIDIA GPU acceleration via CUDA toolkit
- **OpenCL**: Cross-vendor GPU acceleration (NVIDIA, AMD, Intel)
- **CPU Fallback**: Always available for systems without GPU acceleration

### macOS
- **Metal**: Apple GPU acceleration (Intel Iris, Apple Silicon GPU)
- **CUDA**: NVIDIA GPU acceleration (Intel Macs with external/eGPU NVIDIA cards)
- **OpenCL**: Cross-vendor GPU acceleration (deprecated on newer macOS)
- **CPU Fallback**: Always available for systems without GPU acceleration

## GPU Technology Overview

### CUDA (NVIDIA)
- **Platforms**: Windows, Linux, macOS (limited)
- **Hardware**: NVIDIA GeForce, Quadro, Tesla GPUs
- **Minimum Compute Capability**: 3.5 (configurable)
- **Features**: Comprehensive GPU compute, memory management, streams
- **Performance**: Highest performance for NVIDIA hardware

### Metal (Apple)
- **Platforms**: macOS only
- **Hardware**: Intel Iris Pro, Apple Silicon (M1/M2/M3) GPUs
- **Features**: Native macOS GPU acceleration, optimized for Apple hardware
- **Performance**: Optimal for Apple Silicon, good for Intel Macs

### OpenCL (Cross-Platform)
- **Platforms**: Windows, Linux, macOS
- **Hardware**: NVIDIA, AMD, Intel GPUs
- **Features**: Cross-vendor compatibility, good fallback option
- **Performance**: Good cross-platform performance, vendor-dependent optimization

### CPU Fallback
- **Platforms**: All platforms
- **Hardware**: Any x86_64 or ARM64 CPU
- **Features**: Always available, no GPU dependencies
- **Performance**: Lower than GPU acceleration but universally compatible

## Build Configuration

### Build Tags

Use Go build tags to include specific GPU support:

```bash
# CUDA support only
go build -tags cuda

# OpenCL support only  
go build -tags opencl

# Metal support only (macOS)
go build -tags metal

# Multiple GPU technologies
go build -tags "cuda opencl metal"

# No GPU acceleration (CPU only)
go build
```

### Platform-Specific Builds

Use the provided Makefile for convenient cross-platform builds:

```bash
# Current platform with all available GPU support
make build-all-gpu

# Specific GPU technology
make build-cuda
make build-opencl
make build-metal

# Cross-platform builds
make build-linux      # CUDA + OpenCL
make build-windows    # CUDA + OpenCL  
make build-macos      # CUDA + OpenCL + Metal
make build-macos-arm64 # Metal optimized for Apple Silicon
```

## Runtime Detection

NeuralMeterGo automatically detects available GPU technologies at runtime:

```go
import "neuralmetergo/internal/gpu"

// Create GPU manager
config := gpu.DefaultGPUConfig()
manager := gpu.NewManager(config, logger)

// Get available GPUs
gpus, err := manager.GetAvailableGPUs()
if err != nil {
    log.Printf("GPU detection failed: %v", err)
    // Falls back to CPU processing
}

// Select best GPU automatically
bestGPU, err := manager.SelectBestGPU(config)
if err != nil {
    log.Printf("No suitable GPU found: %v", err)
    // Use CPU processing
}
```

## Platform Priority

GPU technologies are prioritized in the following order:

1. **CUDA** (highest priority - best performance for NVIDIA)
2. **Metal** (high priority - optimal for Apple hardware)  
3. **OpenCL** (medium priority - good cross-platform compatibility)
4. **CPU** (fallback - always available)

## Configuration Options

### GPU Requirements
```go
config := gpu.GPUConfig{
    Enabled:              true,
    PreferCUDA:          true,              // Prefer CUDA over other options
    MinMemoryGB:         2.0,               // Minimum GPU memory required
    MinComputeCapability: gpu.ComputeCapability{Major: 3, Minor: 5}, // CUDA only
    MaxTemperature:      85,                // Maximum operating temperature
    MaxMemoryUtilization: 90.0,             // Maximum memory usage percentage
    DeviceID:            -1,                // Auto-select best device
}
```

### Platform-Specific Notes

#### Windows
- Install CUDA Toolkit for NVIDIA GPU support
- Install GPU vendor's OpenCL SDK for OpenCL support
- Visual Studio Build Tools required for CGO compilation

#### Linux
- Install CUDA Toolkit: `/usr/local/cuda`
- Install OpenCL headers: `opencl-headers` package
- Ensure proper GPU drivers are installed

#### macOS
- Xcode Command Line Tools required for Metal support
- CUDA support limited to older Intel Macs with external NVIDIA GPUs
- Metal automatically available on all modern Macs

## Testing

### Cross-Platform Testing
```bash
# Test on current platform
make test

# Test with specific GPU support
make test-cuda
make test-opencl
make test-metal

# Comprehensive GPU detection test
go test -v ./test/unit/gpu/
```

### CI/CD Considerations
- Tests gracefully handle missing GPU hardware
- CPU fallback always tested regardless of platform
- Platform-specific tests only run on appropriate OS

## Performance Characteristics

| Platform | GPU Type | Typical Performance | Memory Bandwidth | Power Efficiency |
|----------|----------|-------------------|------------------|------------------|
| Windows  | CUDA     | Excellent         | Very High        | Good             |
| Windows  | OpenCL   | Good              | High             | Good             |
| Linux    | CUDA     | Excellent         | Very High        | Good             |
| Linux    | OpenCL   | Good              | High             | Good             |
| macOS    | Metal    | Excellent         | High             | Excellent        |
| macOS    | CUDA     | Good              | High             | Fair             |
| All      | CPU      | Baseline          | Low              | Variable         |

## Troubleshooting

### Common Issues

#### CUDA Not Detected
- Verify CUDA Toolkit installation
- Check NVIDIA driver version compatibility
- Ensure `nvidia-smi` command works
- Check GPU compute capability meets minimum requirements

#### Metal Not Available
- Verify macOS version (10.11+ required)
- Check if Metal build tag was specified
- Ensure Xcode Command Line Tools are installed
- Verify GPU supports Metal (all modern Macs do)

#### OpenCL Issues
- Install vendor-specific OpenCL SDK
- Check driver installation
- Verify OpenCL runtime is available
- Some newer macOS versions have deprecated OpenCL

#### Build Issues
- Install CGO dependencies for your platform
- Set appropriate environment variables (CGO_ENABLED=1)
- Ensure cross-compilation targets are installed

### Debug Commands
```bash
# Check GPU detection
./neuralmeter --gpu-info

# Verbose GPU logging  
./neuralmeter --log-level=debug --gpu-verbose

# Test specific GPU device
./neuralmeter --gpu-device=0 --gpu-test
```

## Future Enhancements

- **Vulkan Compute**: Cross-platform compute shaders
- **DirectML**: Windows Machine Learning acceleration
- **ROCm**: AMD GPU compute platform (Linux)
- **Intel oneAPI**: Intel GPU acceleration
- **Multi-GPU Support**: Distribute work across multiple GPUs
- **Unified Memory**: Automatic CPU/GPU memory management

## Contributing

When adding new GPU platform support:

1. Create platform-specific detector implementation
2. Add appropriate build tags
3. Update platform detection logic
4. Add cross-platform tests
5. Update this documentation
6. Test on target hardware when possible

See `internal/gpu/metal_darwin.go` and `internal/gpu/cuda_linux.go` for implementation examples.

# Cross-Platform GPU Build Strategy

## Overview

NeuralMeter implements a cross-platform GPU acceleration system that supports multiple GPU backends across different operating systems. This document outlines the build strategy, deployment approach, and development workflow.

## Build Strategy

### Platform-Specific Binaries

The deployment model uses platform-specific binaries that include all relevant GPU backends for each platform:

- **macOS Binary**: Metal + OpenCL (built on macOS)
- **Linux Binary**: CUDA + ROCm + OpenCL + oneAPI (built on Linux)  
- **Windows Binary**: CUDA + DirectML + OpenCL + oneAPI (built on Windows)

### Runtime Detection vs Build-Time Compilation

**Key Principle**: All backends are compiled into each platform's binary, but only activate based on runtime driver availability.

- **No build tags required for deployment**: Everything compiled in, activated based on availability
- **Runtime detection**: App detects available drivers and uses appropriate backend
- **Graceful fallback**: If preferred backend unavailable, falls back to next available option

## CGO Dependencies and Native Linking

### Why Cross-Compilation Won't Work

GPU backends require CGO and native library linking:

```go
// CUDA example - requires native CUDA libraries
/*
#cgo CFLAGS: -I/usr/local/cuda/include
#cgo LDFLAGS: -L/usr/local/cuda/lib64 -lcuda -lcudart
#include <cuda_runtime.h>
*/
```

**Critical Requirements**:
- **CUDA**: Must link against `/usr/local/cuda/lib64/libcuda.so` and `libcudart.so` on Linux
- **ROCm**: Requires `/opt/rocm/lib/libhip.so` and related libraries on Linux
- **DirectML**: Needs Windows DirectML runtime libraries
- **Metal**: Uses macOS-specific Objective-C frameworks
- **OpenCL**: Platform-specific OpenCL implementations

### Platform-Specific Build Requirements

#### Linux Build Environment
```bash
# CUDA dependencies
export CUDA_ROOT=/usr/local/cuda
export PATH=$CUDA_ROOT/bin:$PATH
export LD_LIBRARY_PATH=$CUDA_ROOT/lib64:$LD_LIBRARY_PATH

# ROCm dependencies  
export ROCM_ROOT=/opt/rocm
export PATH=$ROCM_ROOT/bin:$PATH
export LD_LIBRARY_PATH=$ROCM_ROOT/lib:$LD_LIBRARY_PATH

# oneAPI dependencies
source /opt/intel/oneapi/setvars.sh

# OpenCL
sudo apt-get install opencl-headers opencl-dev
```

#### Windows Build Environment
```cmd
# CUDA
set CUDA_PATH=C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.0
set PATH=%CUDA_PATH%\bin;%PATH%

# DirectML (via Windows SDK)
# oneAPI
call "C:\Program Files (x86)\Intel\oneAPI\setvars.bat"
```

#### macOS Build Environment  
```bash
# Metal (built-in with Xcode)
# OpenCL (built-in with macOS)
xcode-select --install
```

## Code Architecture for Cross-Platform Support

### Build Tag System

The codebase uses Go build tags to conditionally compile backend implementations:

```go
// Platform + Backend specific
//go:build (linux || windows) && cuda
//go:build darwin && metal
//go:build rocm
//go:build opencl
//go:build oneapi
//go:build directml

// Platform only
//go:build darwin
//go:build linux  
//go:build windows

// Fallback implementations
//go:build !cuda
//go:build !opencl && !rocm && !oneapi && !directml && !cuda
```

### Backend Registration System

The abstraction manager automatically registers available backends per platform:

```go
// internal/gpu/abstraction_manager.go
func (am *AbstractionManager) registerBackends() {
    switch GetCurrentPlatform() {
    case "darwin":
        am.registerDarwinBackends()  // Metal, CUDA, OpenCL
    case "linux":
        am.registerLinuxBackends()   // CUDA, ROCm, OpenCL, oneAPI
    case "windows":
        am.registerWindowsBackends() // CUDA, DirectML, OpenCL, oneAPI
    }
}
```

### Unified Interface

All backends implement the same interface:

```go
type GPUBackend interface {
    Name() string
    EnumerateDevices(ctx context.Context) ([]GPUDevice, error)
    CreateContext(device *GPUDevice) (GPUContext, error)
    GetCapabilities(device *GPUDevice) (*GPUCapability, error)
    // ... other methods
}
```

## Development Workflow

### 1. Development Phase (Any Platform)

- **Primary Development**: Can be done on any platform (macOS recommended for this project)
- **Code Changes**: All backend code can be written and reviewed
- **Testing**: Unit tests and mocks can run on any platform
- **Compilation Check**: `go build ./...` verifies syntax and basic compilation

### 2. Platform-Specific Testing Phase

Each platform requires native compilation and testing:

#### macOS Testing
```bash
# Test Metal + OpenCL
go build -tags "metal opencl" ./...
./neuralmeter gpu list
./neuralmeter gpu test
```

#### Linux Testing  
```bash
# Test CUDA + ROCm + OpenCL + oneAPI
go build -tags "cuda rocm opencl oneapi" ./...
./neuralmeter gpu list
./neuralmeter gpu test
```

#### Windows Testing
```cmd
# Test CUDA + DirectML + OpenCL + oneAPI  
go build -tags "cuda directml opencl oneapi" ./...
neuralmeter.exe gpu list
neuralmeter.exe gpu test
```

### 3. Binary Distribution

Each platform produces its own optimized binary:

- **neuralmeter-darwin-amd64**: macOS Intel binary
- **neuralmeter-darwin-arm64**: macOS Apple Silicon binary  
- **neuralmeter-linux-amd64**: Linux x86_64 binary
- **neuralmeter-windows-amd64.exe**: Windows x86_64 binary

## Current Implementation Status

### ✅ Completed Platforms

#### macOS (Darwin)
- **Status**: ✅ **WORKING**
- **Backends**: Metal ✅, OpenCL ✅
- **Testing**: Successful detection of Apple M1 GPU
- **Build**: `go build ./...` successful
- **Runtime**: `./neuralmeter gpu list` shows proper GPU detection

### 🔄 Pending Platforms

#### Linux
- **Status**: 🔄 **READY FOR TESTING**
- **Backends**: CUDA, ROCm, OpenCL, oneAPI
- **Requirements**: Linux environment with GPU drivers installed
- **Next Steps**: Transfer code to Linux system for compilation and testing

#### Windows  
- **Status**: 🔄 **READY FOR TESTING**
- **Backends**: CUDA, DirectML, OpenCL, oneAPI
- **Requirements**: Windows environment with GPU drivers installed
- **Next Steps**: Transfer code to Windows system for compilation and testing

## Code Readiness Assessment

### ✅ Ready for Linux Compilation

The codebase is ready for Linux compilation with the following backends:

1. **CUDA Backend** (`//go:build (linux || windows) && cuda`)
   - ✅ Complete implementation in `backends/cuda_backend.go`
   - ✅ Context management in `backends/cuda_context.go`  
   - ✅ Memory management in `backends/cuda_memory.go`
   - ✅ Linux-specific code in `cuda_linux.go`
   - ✅ Proper CGO directives for CUDA libraries

2. **ROCm Backend** (`//go:build rocm`)
   - ✅ Complete implementation in `backends/rocm_backend.go`
   - ✅ HIP runtime integration
   - ✅ Proper CGO directives for ROCm libraries

3. **OpenCL Backend** (`//go:build opencl`)
   - ✅ Complete implementation in `opencl_backend.go`
   - ✅ Cross-platform OpenCL headers
   - ✅ Platform-agnostic OpenCL integration

4. **oneAPI Backend** (`//go:build oneapi`)
   - ✅ Basic structure in `backends/oneapi_backend.go`
   - ✅ Abstraction layer ready

### ✅ Ready for Windows Compilation

The codebase is ready for Windows compilation with these backends:

1. **CUDA Backend** 
   - ✅ Windows-specific code in `cuda_windows.go`
   - ✅ Same backend implementation as Linux

2. **DirectML Backend** (`//go:build directml`)
   - ✅ Abstraction layer in `abstraction_directml.go`
   - ⚠️ Implementation needs completion

3. **OpenCL Backend**
   - ✅ Cross-platform implementation ready

4. **oneAPI Backend**
   - ✅ Cross-platform implementation ready

## Testing Strategy

### Pre-Transfer Testing (Current Platform)
```bash
# Verify compilation
go build ./...

# Check for missing dependencies
go mod tidy
go mod verify

# Run available tests
go test ./...
```

### Post-Transfer Testing (Target Platform)

#### Linux Testing Script
```bash
#!/bin/bash
# test_linux_gpu.sh

echo "Testing Linux GPU backends..."

# Test CUDA
if command -v nvidia-smi &> /dev/null; then
    echo "Testing CUDA backend..."
    go build -tags "cuda" ./...
    ./neuralmeter gpu list --backend=cuda
fi

# Test ROCm  
if command -v rocm-smi &> /dev/null; then
    echo "Testing ROCm backend..."
    go build -tags "rocm" ./...
    ./neuralmeter gpu list --backend=rocm
fi

# Test OpenCL
echo "Testing OpenCL backend..."
go build -tags "opencl" ./...
./neuralmeter gpu list --backend=opencl

# Test All Available
go build -tags "cuda rocm opencl oneapi" ./...
./neuralmeter gpu list
```

## Impact on GPU Tasks

### All GPU Tasks Affected

This cross-platform approach affects **ALL** GPU-related tasks (Tasks 69-88):

1. **Task 69**: ✅ macOS implementation complete, Linux/Windows ready for testing
2. **Tasks 70-88**: All backend implementations use this same pattern

### Required Updates to Existing Tasks

#### Task Status Updates Needed:
- **Task 69.13**: macOS Binary - ✅ **DONE** 
- **Task 69.14**: Linux Binary - 🔄 **PENDING** (ready for testing)
- **Task 69.15**: Windows Binary - 🔄 **PENDING** (ready for testing)

#### Documentation Updates:
- All GPU task documentation should reference this cross-platform approach
- Testing strategies should account for platform-specific requirements
- Build instructions should specify platform requirements

## Next Steps

### Immediate Actions:
1. ✅ **Document approach** (this document)
2. 🔄 **Transfer code to Linux environment**
3. 🔄 **Install Linux GPU drivers** (CUDA, ROCm)
4. 🔄 **Test Linux compilation and GPU detection**
5. 🔄 **Transfer code to Windows environment**  
6. 🔄 **Test Windows compilation and GPU detection**

### Long-term Strategy:
- **CI/CD Pipeline**: Set up automated testing on all three platforms
- **Binary Distribution**: Automate platform-specific binary builds
- **Performance Benchmarking**: Cross-platform performance validation
- **Driver Compatibility**: Test against different driver versions

## Conclusion

The NeuralMeter codebase is architecturally ready for cross-platform GPU support. The abstraction layer, backend implementations, and build system are all properly structured. The main requirement now is platform-specific testing and validation on Linux and Windows systems with appropriate GPU drivers installed.

The approach ensures maximum compatibility while maintaining clean separation of concerns and allowing for future backend additions. 