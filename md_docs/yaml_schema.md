# NeuralMeter YAML Schema Documentation

This document describes the YAML schema structure for NeuralMeter load testing configurations.

## Overview

NeuralMeter uses YAML files to define load testing scenarios. The schema supports comprehensive test configurations including:

- Test plan metadata and timing
- Global settings and variables
- Multiple test scenarios with weighted distribution
- HTTP request specifications
- Response assertions and data extraction
- Output configuration and metrics

## Schema Structure

### Top-Level Configuration

```yaml
version: "1.0"                    # Required: Schema version
name: "Test Plan Name"            # Required: Human-readable test plan name
description: "Plan description"   # Optional: Detailed description
duration: "5m"                    # Required: Total test duration
concurrency: 100                  # Required: Number of concurrent users (min: 1)
ramp_up: "30s"                   # Optional: Time to ramp up to full concurrency
```

### Global Settings

```yaml
global:
  base_url: "https://api.example.com"  # Optional: Base URL for all requests
  timeout: "30s"                       # Optional: Default timeout for requests
  headers:                             # Optional: Default headers for all requests
    User-Agent: "NeuralMeter/1.0"
    Accept: "application/json"
  variables:                           # Optional: Global variables
    api_version: "v1"
    environment: "production"
  rate_limit:                          # Optional: Rate limiting configuration
    requests_per_second: 50            # Optional: Max requests per second (min: 1)
    burst_size: 10                     # Optional: Burst size (min: 1)
    delay: "100ms"                     # Optional: Delay between requests
```

### Variables

Variables can be defined at multiple levels (global, plan, scenario, request):

```yaml
variables:
  - name: "username"                   # Required: Variable name
    type: "faker"                      # Required: Variable type (static, random, faker, csv)
    method: "username"                 # Optional: Method for faker type
    value: "static_value"              # Optional: Static value
    file: "data.csv"                   # Optional: CSV file path for csv type
    description: "Variable description" # Optional: Documentation
```

**Variable Types:**
- `static`: Fixed value specified in `value` field
- `random`: Random values (implementation-specific)
- `faker`: Fake data using specified `method`
- `csv`: Values from CSV file specified in `file` field

### Scenarios

Test scenarios define the actual test behavior:

```yaml
scenarios:
  - name: "User Authentication"        # Required: Scenario name
    description: "Test login flow"     # Optional: Scenario description
    weight: 60                         # Optional: Percentage weight (1-100, omit for equal distribution)
    variables:                         # Optional: Scenario-specific variables
      - name: "endpoint"
        type: "static"
        value: "/auth/login"
    requests:                          # Required: List of HTTP requests
      - name: "Login Request"          # Optional: Request name
        method: "POST"                 # Required: HTTP method (GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS)
        url: "/auth/login"             # Required: Request URL (absolute or relative to base_url)
        headers:                       # Optional: Request-specific headers
          Content-Type: "application/json"
        body:                          # Optional: Request body (any valid YAML structure)
          username: "{{.username}}"
          password: "{{.password}}"
        timeout: "10s"                 # Optional: Request timeout
        assertions:                    # Optional: Response validation rules
          - type: "status_code"        # Required: Assertion type
            operator: "eq"             # Optional: Comparison operator
            value: 200                 # Optional: Expected value
            description: "Login success" # Optional: Assertion description
        extract:                       # Optional: Data extraction from response
          - name: "auth_token"         # Required: Variable name to store extracted data
            type: "json_path"          # Required: Extraction type
            path: "$.token"            # Required: Path/pattern for extraction
            default: ""                # Optional: Default value if extraction fails
        variables:                     # Optional: Request-specific variables
          attempt_count: "1"
```

### Assertions

Response validation rules support multiple assertion types:

```yaml
assertions:
  - type: "status_code"               # Status code validation
    operator: "eq"                    # Operators: eq, ne, lt, le, gt, ge
    value: 200
  - type: "response_time"             # Response time validation
    operator: "lt"
    value: "2s"
  - type: "contains"                  # Response body contains text
    value: "success"
  - type: "json_path"                 # JSON path validation
    field: "$.user.id"
    operator: "ne"
    value: ""
  - type: "header_exists"             # Header presence validation
    field: "Content-Type"
```

**Assertion Types:**
- `status_code`: Validate HTTP status code
- `response_time`: Validate response time duration
- `contains`: Check if response body contains text
- `json_path`: Validate JSON response using JSONPath
- `header_exists`: Check if response header exists

**Operators:**
- `eq`: Equal to
- `ne`: Not equal to
- `lt`: Less than
- `le`: Less than or equal to
- `gt`: Greater than
- `ge`: Greater than or equal to
- `contains`: Contains substring
- `not_contains`: Does not contain substring

### Data Extraction

Extract data from responses for use in subsequent requests:

```yaml
extract:
  - name: "user_id"                   # Variable name to store extracted value
    type: "json_path"                 # Extraction method
    path: "$.user.id"                 # JSONPath expression
    default: "unknown"                # Default value if extraction fails
  - name: "session_token"
    type: "header"                    # Extract from response header
    path: "X-Session-Token"
  - name: "csrf_token"
    type: "regex"                     # Extract using regular expression
    path: 'name="csrf_token" value="([^"]+)"'
  - name: "page_title"
    type: "xpath"                     # Extract using XPath (for HTML/XML)
    path: "//title/text()"
```

**Extraction Types:**
- `json_path`: Extract from JSON response using JSONPath
- `xpath`: Extract from XML/HTML using XPath
- `regex`: Extract using regular expression
- `header`: Extract from response header

### Output Configuration

Configure test results output:

```yaml
output:
  format: ["json", "html", "csv"]     # Optional: Output formats
  file: "results/test_results"        # Optional: Output file prefix
  metrics: ["response_time", "throughput", "error_rate", "p95"] # Optional: Metrics to include
  detailed: true                      # Optional: Include detailed results
```

**Available Formats:**
- `json`: JSON format results
- `html`: HTML report
- `csv`: CSV data export

**Available Metrics:**
- `response_time`: Average response time
- `throughput`: Requests per second
- `error_rate`: Percentage of failed requests
- `success_rate`: Percentage of successful requests
- `p50`: 50th percentile response time
- `p95`: 95th percentile response time
- `p99`: 99th percentile response time

## Duration Format

All duration fields support Go's duration format:

- `s`: seconds (e.g., `30s`)
- `m`: minutes (e.g., `5m`)
- `h`: hours (e.g., `2h`)
- Combined: `1h30m45s`

## Variable Templating

Variables can be referenced in various fields using Go template syntax:

```yaml
url: "{{.global.base_url}}/{{.api_version}}/users"
headers:
  Authorization: "Bearer {{.auth_token}}"
body:
  username: "{{.test_username}}"
  timestamp: "{{.timestamp}}"
```

## Validation Rules

The schema includes comprehensive validation:

- **Required fields**: Must be present and non-empty
- **Type validation**: Ensures correct data types
- **Range validation**: Numeric fields have min/max constraints
- **Enum validation**: Restricted to specific values
- **URL validation**: Proper URL format checking
- **Duration validation**: Valid Go duration format

## Example Complete Configuration

```yaml
version: "1.0"
name: "API Load Test Suite"
description: "Comprehensive load test for user API"
duration: "5m"
concurrency: 100
ramp_up: "30s"

global:
  base_url: "https://api.example.com"
  headers:
    User-Agent: "NeuralMeter/1.0"
  timeout: "30s"
  variables:
    api_version: "v1"

variables:
  - name: "test_username"
    type: "faker"
    method: "username"

scenarios:
  - name: "Authentication Flow"
    weight: 70
    requests:
      - method: "POST"
        url: "/{{.api_version}}/auth/login"
        body:
          username: "{{.test_username}}"
          password: "password123"
        assertions:
          - type: "status_code"
            operator: "eq"
            value: 200
        extract:
          - name: "auth_token"
            type: "json_path"
            path: "$.token"

  - name: "Profile Access"
    weight: 30
    requests:
      - method: "GET"
        url: "/{{.api_version}}/profile"
        headers:
          Authorization: "Bearer {{.auth_token}}"
        assertions:
          - type: "status_code"
            operator: "eq"
            value: 200

output:
  format: ["json", "html"]
  metrics: ["response_time", "throughput", "error_rate", "p95"]
```

## Best Practices

1. **Use descriptive names**: Make scenario and request names clear and meaningful
2. **Set appropriate timeouts**: Configure realistic timeouts for your API
3. **Validate responses**: Include assertions to catch issues early
4. **Extract key data**: Use extraction to pass data between requests
5. **Weight scenarios realistically**: Distribute load according to real usage patterns
6. **Use variables**: Leverage variables for maintainable configurations
7. **Document your tests**: Use description fields to explain test purposes 