Primary Goal
Deliver a head-less Linux-only NeuralMeter CLI (binary built from cmd/neuralmeter) that can be copied to System A and run against a remote System B stack.
Mac / Windows / GUI code can stay in the repo, but must be completely irrelevant to the default Linux build.
Required CLI Capabilities for Task 6.x
6.1 (root + sub-command structure) – already outlined; must compile & show correct help/version info.
6.2 daemon mode (start/stop/status)
6.3 embedded REST API server for remote orchestration
6.4 YAML test-plan loader / executor
6.5 GPU-accelerated execution (detect GPUs, run tests on them, gpu list / status / benchmark)
6.6..6.10 HTTP engine, streaming results, config management, logging, etc.
Test/Build Constraints
No X-window / GLFW deps in the default build. GUI code must be behind a gui tag (but we shouldn’t touch it further).
The CLI does need CUDA support. Build & test with go build -tags cuda (and, when CGO stream code is required, cuda,cuda_cgo).
The Linux host already has full CUDA toolkit + drivers, so tests involving NVCC / CGO can run.
Unit/integration tests should pass head-less; GUI/X11 failures are unacceptable.
What Still Needs Doing (immediate, CLI-only scope)
Undo any edits that introduced naming collisions or unnecessary tags in core GPU files if they break a plain go build -tags cuda ./cmd/neuralmeter.
Add/verify runtime driver check (nvidia-smi parse) at CLI start-up; exit with clear error if driver <-- minimum.
Finish flag wiring in cmd/neuralmeter/main.go so the commands referenced throughout the test approach exist and call real code paths (run, server start, gpu list/status, etc.).
Run go vet -tags cuda ./... and go test -tags cuda ./...; fix any remaining compile/test errors only in packages required by the CLI.
Prepare a build script that produces the binary (bin/neuralmeter) and a small README snippet telling the user to copy it to System A.
Out-of-Scope for this step
GUI (fyne, GLFW) code – leave untouched beyond existing //go:build gui header.
macOS/Windows folders – untouched.
Deep refactors of internal GPU CGO stream layer – only adjust if the CLI or its tests truly depend on it.
If that matches your expectation, the next concrete step is to make sure a clean

Here is a quick triage of the failure dump you posted. I have grouped the problems by “why they failed” and whether the test is actually relevant to the Linux, head-less CLI build we are focusing on.
1. GUI / macOS / OpenGL / Metal code
• internal/ui package fails to build
Metal buffer-pool logs, macOS Metal tests, Fyne/GLFW references
Status: Not relevant for the Linux CLI.
Action: leave those files/tests behind the //go:build gui (or darwin/gui) tags so they are not compiled in the default -tags cuda run.
2. CGO-heavy CUDA stream layer
• internal/gpu CGO stream/event implementation and its unit tests (cuda_stream_*)
Currently behind our extra tag cuda_cgo, but we ran the full suite with -tags "cuda,cuda_cgo" so they built and ran.
Status: Relevant only when we deliberately test CGO streams.
For everyday “CLI only” runs we can exclude cuda_cgo; keep the tests, but run them on a machine that has full NVCC tool-chain when we want to validate that layer.
3. Project-structure / housekeeping tests
Test file: test/task1_validation_test.go
Failures: missing pkg/, test/benchmark/, go 1.21 stanza, etc.
Status: Legitimate hygiene checks. They do not block CLI execution, but
they should pass eventually. We can fix directory stubs and go.mod later.
4. Metrics / timeout / sliding-window logic
Failures in:
Apply to o3_assumtion...
)
These are pure Go tests that exercise core CLI subsystems (HTTP client, retry, metrics). The failures indicate real logic or expectation mismatches (e.g. P50 calculation, retry timing).
Status: RELEVANT – these highlight bugs we eventually have to fix for a reliable CLI.
5. Worker package panic (negative WaitGroup)
internal/worker
unit test panicked → real concurrency bug.
Status: RELEVANT – the worker runs long-lived load threads; needs fixing.
6. Integration tests (HTTP time-outs)
test/integration
failing due to expected timeouts not happening.
Status: RELEVANT – they model exactly the remote-target timeout scenarios described in the Test Approach doc. We need those green before claiming Task-6 HTTP engine is ready.
Summary: which tests we can ignore for now
SAFE TO SKIP IN DEFAULT CLI BUILD
Everything under internal/ui (guard with gui tag)
Metal / macOS specific GPU tests (already behind darwin / metal tags)
CUDA CGO stream tests – run only with cuda_cgo when needed
MUST EVENTUALLY PASS
Metrics / client / timeout / sliding-window tests
Worker concurrency tests
Integration timeout tests
Project-structure validation (easy housekeeping)
Next immediate engineering step (if you agree):
Run tests again without cuda_cgo and without GUI packages to confirm we’re only seeing the “real” logic failures.
Start fixing the highest-impact logic failures (timeouts / metrics).


