# GPU Follow-Up TODOs

| ID | Description | Status | Depends On |
|----|-------------|--------|------------|
| gpu_nvml_integration | Integrate NVML into CUDA detector to obtain accurate SM count and PCIe / memory-bus width when structured `nvidia-smi` queries are missing. | pending | — |
| gpu_other_backends | After NVML is working, implement equivalent SM / bus-width retrieval for ROCm (via rocm-smi) and OpenCL detectors. | pending | gpu_nvml_integration |

_This file mirrors the internal Taskmaster todo entries so it’s easy to track in the repo._ 