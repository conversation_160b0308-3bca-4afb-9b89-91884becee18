---
## Remote Test-Server Setup (Native Linux)

This guide shows how to stand up a **minimal remote target** on a vanilla Debian/Ubuntu box that the NeuralMeter worker (`neuralmeter-cuda`) can hit during integration and performance testing.

The reference setup runs **two lightweight services**:

| Port | Service | Purpose |
|------|---------|---------|
| 8080 | NGINX static site | `/health.json` (readiness) and `/page.html` (static payload) for HTTP GET/POST scenarios |
| 50051 | Go gRPC Echo | Bidirectional streaming Echo RPC used by NeuralMeter’s gRPC client path |

Everything is installed through `apt` (no Docker required) and managed via **systemd** so the services survive reboots.

---
### 1  Prerequisites
1. Fresh Ubuntu 22.04 LTS server (or any Debian-based distro).
2. A user with sudo privileges.
3. Outbound Internet access for `apt` and `go` module downloads.

---
### 2  Folder Layout
```
└─ test/
   ├─ remote-server/
   │  ├─ html/
   │  │   ├─ health.json          # {"status":"ok"}
   │  │   └─ page.html           # simple 5 KB HTML page
   │  ├─ setup_http_service.sh   # installs & configures NGINX
   │  ├─ setup_grpc_service.sh   # builds & registers gRPC echo service
   │  └─ grpc/
   │      ├─ proto/echo.proto    # proto definition
   │      └─ echo_server.go      # tiny Go implementation
   └─ cli-scripts/
      ├─ gpu_list.sh             # already present
      └─ benchmark_http.sh       # exercises HTTP target through the worker
```

---
### 3  HTTP Target (Port 8080)
`setup_http_service.sh` performs the following:
1. Installs **NGINX**.
2. Copies the static assets from `html/` into `/var/www/neuralmeter`.
3. Writes an NGINX site file that listens on **8080**.
4. Enables the site & restarts NGINX.

Run it once as root (or via sudo):
```bash
cd test/remote-server
sudo ./setup_http_service.sh
```
Verify:
```bash
curl http://<server-ip>:8080/health.json   # => {"status":"ok"}
```

---
### 4  gRPC Echo Target (Port 50051)
`setup_grpc_service.sh` will:
1. Install **Go** and **protoc** if missing.
2. Generate Go stubs from `proto/echo.proto`.
3. Build `echo_server.go` and place the binary in `/usr/local/bin`.
4. Register a **systemd** unit (`nm_test_grpc.service`).

Run:
```bash
cd test/remote-server
sudo ./setup_grpc_service.sh
```
Check status:
```bash
systemctl status nm_test_grpc.service
```

---
### 5  Testing with NeuralMeter CLI
Set the worker host/port and execute the helper scripts:
```bash
export WORKER=your-worker-host:8080
# GPU sanity check
./test/cli-scripts/gpu_list.sh
# Simple HTTP benchmark (assumes you have a test plan at test/plans/http_get.yaml)
./test/cli-scripts/benchmark_http.sh
```

---
### 6  Tear-Down / Cleanup
```bash
sudo systemctl disable --now nm_test_grpc.service
sudo rm /etc/systemd/system/nm_test_grpc.service
sudo apt-get purge -y nginx golang-go protobuf-compiler
sudo rm -rf /var/www/neuralmeter
```

Feel free to extend these services or add new endpoints as your test coverage grows.  The goal is to provide **just enough** remote surface area for NeuralMeter’s daemon and CLI to exercise while you finish Task 6’s subtasks.  Happy testing! 🎯 