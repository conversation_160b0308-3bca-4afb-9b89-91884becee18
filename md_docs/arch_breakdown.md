# NeuralMeter Architecture V4 - Single Branch Platform Separation

## **Executive Summary**

This document defines the complete architecture for NeuralMeter - a GPU-accelerated load testing platform that replaces JMeter with modern Go-based performance testing capabilities. The architecture uses a single branch with strict platform separation to eliminate development confusion while supporting both standalone and enterprise deployment modes.

---

## **Core Architecture Principles**

### **Deployment Modes**
1. **Standalone Mode**: Complete UI + Engine + GPU acceleration runs locally on any platform
2. **Enterprise Mode**: UI coordinates with headless Linux workers across the network can be deployed in cloud (like JMeter `-n` mode)

### **Platform Strategy**
- **Single Git branch** with directory-based platform separation
- **build tags** - directory structure enforces platform boundaries, build tags need to be added to curor rules, to enforce the correct platform can work on the correct code, tag - Windoes, tag - Linux, tag - Mac, tag - Shared
- **Shared core components** - no code duplication for common functionality, tag - Shared
- **Platform-specific GPU implementations** - real GPU API integration per platform

---

## **Platform Breakdown & Responsibilities**

### **UI/Engine Work: `internal/{ui,engine,config}` (Any Platform)**

#### **What This Includes:**
- **Standalone performance testing** - can run full tests locally on Windows/Mac/Linux
- **UI dashboard** - fyne interface that works on any platform  
- **Local engine** - can execute performance tests directly on the local machine
- **Enterprise coordination** - UI can also orchestrate remote Linux workers

#### **Platform Independence:**
- **Runs anywhere** - Windows, macOS, Linux with full functionality
- **Complete testing capability** - doesn't need remote workers for basic testing
- **Fyne dashboard** - go fyne interface 

---

### **Linux GPU Worker/engine: `internal/gpu/linux/` (Linux Only)**

#### **What This Contains:**
- **HEADLESS enterprise workers** - no UI, command-line only (like JMeter headless mode: `jmeter -n`)
- **AWS/cloud deployment** - distributed workers in cloud/physical infrastructure  
- **API-driven workers** - receives test jobs from UI running elsewhere
- **High-scale CUDA** - server-grade GPU acceleration for enterprise loads
- **Worker-only execution** - pure execution engine, no interface

#### **Enterprise Deployment:**
- **Headless operation** - runs without any GUI (like `jmeter -n -t testplan.jmx`)
- **Cloud infrastructure** - deployed in cloud/physical infrastructure as worker nodes
- **API endpoints** - receives work from UI dashboard running on developer machines
- **Distributed execution** - multiple workers handling large-scale load tests
- **Production GPU hardware** - NVIDIA Tesla/A100/H100 for maximum performance

#### **Build Requirements:**
- **Must be built on Linux** - CUDA toolkit
- **Go 1.23.4+** with CGO enabled
- **Real GPU libraries** - no mocks or CPU fallback

---

### **macOS Development: `internal/gpu/macos/` (macOS Only)** - Mac

#### **What This Contains:**
- **Full local testing** - complete UI + engine + Metal GPU acceleration
- **Development platform** - development environment
- **Standalone capable** - can run entire test suite locally without remote workers
- **Metal GPU acceleration** - Apple Silicon GPU acceleration for local testing

#### **Development Capabilities:**
- **Complete application** - UI + engine + GPU acceleration in one package
- **Local development** - no need for remote infrastructure during development
- **Metal performance** - leverage Apple Silicon GPUs for local high-performance testing
- **Full feature testing** - test all functionality locally before deploying workers, test cli against local worker, no mocks or stubs

#### **Build Requirements:**
- **Must be built on macOS** - Metal framework linking
- **Go 1.23.4+** with CGO enabled
- **Xcode Command Line Tools** - for Metal framework access
- **gpu detection** - detect os and gpu before building in gpu

---

### **Windows GPU Work: `internal/gpu/windows/` (Windows Only)** -Windows

#### **What This Contains:**
- **Full local testing** - complete UI + engine + DirectML GPU acceleration
- **Standalone capable** - can run entire test suite locally no stubs no mocks
- **DirectML GPU acceleration** - Windows GPU abstraction for all GPU vendors
- **Enterprise option** - ui can talk with headless workers

#### **Windows Capabilities:**
- **Complete application** - UI + engine + GPU acceleration in one package
- **DirectML support** - works with NVIDIA, AMD, Intel GPUs on Windows
- **Standalone operation** - full testing without remote infrastructure
- **Optional headless mode** - has cli engine needed by ui to run local performance tests

#### **Build Requirements:**
- **Must be built on Windows** - DirectML libraries, Windows SDK
- **Go 1.23.4+** with CGO enabled
- **MinGW-w64 GCC compiler** - required for Windows CGO compilation
- **Windows SDK 10.0.18362+** - for DirectML headers
- **gcc.exe in PATH** - MinGW toolchain requirement

---

## **Directory Structure**

```
neuralmeter/
├── cmd/
│   ├── neuralmeter/              # Main CLI (OS-independent)
│   └── worker/                   # Worker binary
│       ├── main.go              # OS-independent entry point
│       ├── linux/               # Linux-specific worker code
│       ├── macos/               # macOS-specific worker code
│       └── windows/             # Windows-specific worker code
├── internal/
│   ├── core/                    # Shared interfaces & types
│   │   ├── interfaces.go        # Platform-agnostic interfaces
│   │   ├── types.go            # Common types/structs
│   │   └── contracts.go        # API contracts
│   ├── ui/                     # Web dashboard (OS-independent)
│   ├── engine/                 # Local test engine (OS-independent)
│   ├── config/                 # Configuration (OS-independent)
│   ├── metrics/                # Metrics collection (OS-independent)
│   ├── gpu/                    # GPU implementations
│   │   ├── interfaces.go       # GPU interface definitions
│   │   ├── common/            # Shared GPU utilities
│   │   ├── linux/             # Linux GPU implementations
│   │   │   ├── cuda/          # NVIDIA CUDA implementation
│   │   │   ├── rocm/          # AMD ROCm implementation
│   │   │   └── opencl/        # OpenCL implementation
│   │   ├── macos/             # macOS GPU implementations
│   │   │   ├── metal/         # Apple Metal implementation
│   │   │   └── opencl/        # OpenCL fallback
│   │   └── windows/           # Windows GPU implementations
│   │       ├── directml/      # DirectML implementation
│   │       ├── cuda/          # Windows CUDA implementation
│   │       └── opencl/        # OpenCL fallback
│   └── workers/               # Worker implementations
│       ├── manager.go         # OS-independent worker manager
│       ├── linux/             # Linux-specific workers
│       ├── macos/             # macOS-specific workers
│       └── windows/           # Windows-specific workers
├── build/
│   ├── linux/                 # Linux build scripts & configs
│   ├── macos/                 # macOS build scripts & configs
│   └── windows/               # Windows build scripts & configs
└── deployment/
    ├── docker/                # Linux container deployments
    ├── kubernetes/            # K8s manifests
    └── cloud/                 # Cloud deployment configs
```

---

## **How This Addresses Task Breakdown Problems**

### **Current Task Issues:**
1. **Task confusion** - Tasks 69-88 mix all platforms causing Claude to work on wrong platform
2. **Triple implementation** - Same GPU functionality implemented 3x for each platform  
3. **Build tag complexity** - Complex conditional compilation across tasks
4. **Platform switching overhead** - Constant platform switching requirements
5. **CPU fallback problems** - Claude adds CPU fallback when told not to
6. **Stubbed functionality** - Mock implementations instead of real GPU APIs

### **Task 69 Example - Current vs Fixed:**

#### **Current Task 69 (Problematic):**
```yaml
"GPU Detection & Initialization" 
- Implement CUDA detection (Linux)
- Implement Metal detection (macOS)  
- Implement DirectML detection (Windows)
- Build tags: //go:build (linux && cuda) || (macos && metal) || (windows && directml)
```
**Result**: Claude tries to implement all 3 platforms, gets confused, implements mocks

#### **Fixed Task Structure:**
```yaml
Task 69-Core: "GPU Interface Definition" (Any platform)
- Define GPUProvider interface in internal/core/interfaces.go
- Define common GPU types and contracts
- Platform-agnostic interface design

Task 69-Linux: "CUDA Detection Implementation" (Linux only)  
- File: internal/gpu/linux/cuda/detection.go
- Real CUDA API calls, no mocks, no CPU fallback
- Linux-specific implementation only

Task 69-macOS: "Metal Detection Implementation" (macOS only)
- File: internal/gpu/macos/metal/detection.go  
- Real Metal framework calls, no CPU fallback
- macOS-specific implementation only

Task 69-Windows: "DirectML Detection Implementation" (Windows only)
- File: internal/gpu/windows/directml/detection.go
- Real DirectML API calls, no CPU fallback
- Windows-specific implementation only
```

### **Task Breakdown Benefits:**

#### **1. Eliminates Platform Confusion**
- **Before**: "Implement GPU detection with CUDA, Metal, and DirectML"
- **After**: "Implement CUDA detection in internal/gpu/linux/cuda/detection.go"
- **Result**: Impossible to work on wrong platform code

#### **2. No Triple Implementation**
- **Before**: Implement same detection logic 3 times in one task
- **After**: One focused implementation per platform per task
- **Result**: Each platform gets real implementation, not mocks

#### **3. Clear File Targeting**
- **Before**: "Work on GPU detection" (unclear where)
- **After**: "Work on internal/gpu/linux/cuda/detection.go" (exact file)
- **Result**: Claude knows exactly which files to modify

#### **4. Platform-Specific Requirements**
- **Before**: "Must work on Windows, Linux, and macOS"
- **After**: "Must work on Linux with CUDA toolkit installed"
- **Result**: Clear development environment requirements

#### **5. No CPU Fallback Confusion**
- **Before**: "Implement GPU detection with CPU fallback"
- **After**: "Implement CUDA detection with no CPU fallback - GPU required"
- **Result**: Clear requirement that GPU is mandatory

### **Updated Task Structure:**

```yaml
# Core/Shared Tasks (Any Platform)
Task 1-52: HTTP, config, metrics, UI (existing, no change needed)
Task 60: Core GPU Interfaces (internal/core/interfaces.go)

# Linux Production Workers (Linux Only) - HEADLESS
Task 69-Linux: CUDA Detection (internal/gpu/linux/cuda/)
Task 70-Linux: CUDA Model Loading (internal/gpu/linux/cuda/)
Task 71-Linux: CUDA Performance Metrics (internal/gpu/linux/cuda/)
Task 72-Linux: CUDA Error Handling (internal/gpu/linux/cuda/)
Task 73-Linux: Multi-GPU CUDA Support (internal/gpu/linux/cuda/)

# macOS Local Development (macOS Only) - UI + ENGINE
Task 69-macOS: Metal Detection (internal/gpu/macos/metal/)
Task 70-macOS: Metal Model Loading (internal/gpu/macos/metal/)
Task 71-macOS: Metal Performance Metrics (internal/gpu/macos/metal/)

# Windows Local Development (Windows Only) - UI + ENGINE
Task 69-Windows: DirectML Detection (internal/gpu/windows/directml/)
Task 70-Windows: DirectML Model Loading (internal/gpu/windows/directml/)
Task 71-Windows: DirectML Performance Metrics (internal/gpu/windows/directml/)
```

---

## **Build System**

### **Platform-Specific Build Commands**

#### **Linux (Production Workers)**
```bash
# build/linux/build.sh
#!/bin/bash
echo "Building Linux headless workers with CUDA/ROCm..."
CGO_ENABLED=1 GOOS=linux GOARCH=amd64 \
go build -tags "linux cuda rocm opencl" \
-o bin/neuralmeter-linux-worker \
cmd/worker/main.go

# Build UI + Engine for Linux
CGO_ENABLED=1 GOOS=linux GOARCH=amd64 \
go build -tags "linux cuda rocm opencl" \
-o bin/neuralmeter-linux \
cmd/neuralmeter/main.go
```

#### **macOS (Development + Local Testing)**
```bash
# build/macos/build.sh
#!/bin/bash
echo "Building macOS with Metal GPU acceleration..."
CGO_ENABLED=1 GOOS=darwin GOARCH=amd64 \
go build -tags "macos metal opencl" \
-o bin/neuralmeter-macos \
cmd/neuralmeter/main.go
```

#### **Windows (Local Testing)**
```bash
# build/windows/build.sh (run on Windows)
echo "Building Windows with DirectML GPU acceleration..."
set CGO_ENABLED=1
set GOOS=windows
set GOARCH=amd64
go build -tags "windows directml cuda opencl" -o bin/neuralmeter-windows cmd/neuralmeter/main.go
```

---

## **Development Workflow**

### **Day-to-Day Development Rules**

#### **UI/Engine Development (Any Platform)**
```bash
# Work on cross-platform components
cd internal/ui/
cd internal/engine/
cd internal/config/
cd internal/metrics/

# Build and test locally
go build -tags "core" cmd/neuralmeter/main.go
```

#### **Linux Worker Development (Linux Required)**
```bash
# SSH to Linux machine or use Linux VM
ssh linux-dev-box
cd neuralmeter/internal/gpu/linux/cuda/

# Build with real CUDA libraries
CGO_ENABLED=1 go build -tags "linux cuda"
```

#### **macOS Local Development (macOS Required)**
```bash
# Work on Metal implementation locally
cd internal/gpu/macos/metal/

# Build with Metal framework
CGO_ENABLED=1 go build -tags "macos metal"
./neuralmeter-macos --test-local
```

#### **Windows Local Development (Windows Required)**
```bash
# Work on DirectML implementation
cd internal/gpu/windows/directml/

# Build with DirectML (requires MinGW-w64)
set CGO_ENABLED=1
go build -tags "windows directml"
neuralmeter-windows.exe --test-local
```

---

## **Deployment Strategies**

### **Standalone Deployment**
- **Target**: Individual developers, small teams
- **Components**: UI + Engine + GPU acceleration
- **Platforms**: Windows, macOS, Linux
- **GPU**: Local GPU acceleration (Metal/DirectML/CUDA)

### **Enterprise Deployment**
- **Target**: Large-scale load testing, cloud infrastructure
- **Components**: 
  - **Control plane**: UI dashboard (any platform)
  - **Workers**: Headless Linux workers (cloud-deployed)
- **Architecture**: Distributed, scalable
- **GPU**: Enterprise GPU hardware (Tesla/A100/H100)

### **Hybrid Deployment**
- **Local development**: macOS with Metal GPU
- **Production testing**: Linux workers in AWS/GCP
- **Coordination**: macOS UI orchestrates cloud workers

---

## **Architecture Summary**

### **Standalone Mode (Any Platform):**
- **UI + Local Engine** runs complete performance tests locally
- **No remote infrastructure needed** for basic testing
- **Full GPU acceleration** on each platform (Metal/DirectML/CUDA)

### **Enterprise Mode:**
- **UI Dashboard** runs on developer machine (any platform)
- **Headless Linux Workers** deployed in cloud (like `jmeter -n` mode)  
- **Distributed coordination** - UI orchestrates multiple remote workers
- **Scalable testing** - enterprise-level load generation

### **Key Solutions:**
1. **"Claude ignores build tags"** → Fixed: No build tags needed, directory separation enforces platform
2. **"Works on wrong platform"** → Fixed: Impossible to work on wrong platform directory  
3. **"Stubbed functionality"** → Fixed: Each platform gets real implementation
4. **"CPU fallback when asked not to"** → Fixed: Platform-specific tasks specify "no CPU fallback"
5. **"Triple implementation work"** → Fixed: One implementation per platform

**The directory structure enforces the task separation that build tags were supposed to provide but failed to deliver.**