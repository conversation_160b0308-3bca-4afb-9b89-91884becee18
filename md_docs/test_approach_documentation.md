# Task 6 Test Approach: Remote Linux Machine Setup for NeuralMeter CLI Testing

## Overview

This document defines the comprehensive test approach for Task 6 (CLI Interface Implementation) using **System A** (CLI performance tool) to test against **System B** (remote target services). **NO DOCKER** - all services run natively on Linux machines for maximum performance and realistic testing conditions.

## Architecture

```
┌─────────────────────┐    Network     ┌─────────────────────┐
│    SYSTEM A         │    Traffic     │    SYSTEM B         │
│ (CLI Performance    │ =============> │ (Target Services)   │
│  Testing Tool)      │                │                     │
│                     │                │                     │
│ - NeuralMeter CLI   │                │ - nginx             │
│ - GPU Hardware      │                │ - WebSocket Server  │
│ - Daemon Process    │                │ - SSL/TLS Services  │
│ - API Server        │                │ - Rate Limiting     │
│ - Result Streaming  │                │ - Load Balancing    │
└─────────────────────┘                └─────────────────────┘
```

## System A: CLI Performance Testing Tool Setup

### Hardware Requirements
- **Linux Distribution**: Ubuntu 22.04 LTS or CentOS 8+
- **GPU Hardware**: NVIDIA RTX 3080+ or Tesla V100+ with CUDA 11.8+
- **CPU**: 8+ cores, 3.0GHz+ (for sustained load generation)
- **RAM**: 16GB+ (32GB recommended for high-scale testing)
- **Network**: Gigabit Ethernet (10GbE preferred for high load testing)
- **Storage**: SSD with 100GB+ free space

### Software Dependencies
```bash
# CUDA Toolkit Installation
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/cuda-keyring_1.0-1_all.deb
sudo dpkg -i cuda-keyring_1.0-1_all.deb
sudo apt-get update
sudo apt-get -y install cuda-toolkit-11-8

# NVIDIA Drivers
sudo apt install nvidia-driver-525 nvidia-utils-525

# Go 1.21+ Installation
wget https://go.dev/dl/go1.21.6.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.21.6.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc

# Development Tools
sudo apt update && sudo apt install -y \
    build-essential \
    git \
    htop \
    iotop \
    nethogs \
    tcpdump \
    wireshark-common \
    stress-ng
```

### NeuralMeter CLI Deployment
```bash
# Clone and build
git clone <neuralmeter-repo>
cd neuralmeter
go mod tidy
go build -o neuralmeter cmd/neuralmeter/main.go

# Install as system service
sudo cp neuralmeter /usr/local/bin/
sudo cp scripts/neuralmeter.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable neuralmeter
```

## System B: Target Services Setup (NO DOCKER)

### Base Linux Configuration
- **Distribution**: Ubuntu 22.04 LTS or CentOS 8+
- **CPU**: 4+ cores (8+ for high-load testing)
- **RAM**: 8GB+ (16GB for intensive testing)
- **Network**: Gigabit Ethernet
- **Storage**: SSD recommended for log performance

### Prerequisite: Ensure 'nginx' User Exists and Has Correct Permissions

Before proceeding with service setup, you must ensure that a dedicated 'nginx' user exists on your system. This user is required by both the nginx configuration (`user nginx;`) and the systemd service files for backend services (`User=nginx`).

### Check if the 'nginx' User Exists
```bash
getent passwd nginx
```
If this command returns no output, the user does not exist and must be created.

### Create the 'nginx' User (if missing)
```bash
sudo useradd --system --no-create-home --shell /usr/sbin/nologin nginx
```

### Ensure Required Directories Exist Before Setting Permissions
Before setting ownership, make sure the following directories exist:
```bash
sudo mkdir -p /var/log/nginx
sudo mkdir -p /var/www/load-test
sudo mkdir -p /etc/nginx/ssl
```

Then set the correct ownership:
```bash
sudo chown -R nginx:nginx /var/log/nginx
sudo chown -R nginx:nginx /var/www/load-test
sudo chown -R nginx:nginx /etc/nginx/ssl
```

This ensures you do not encounter errors if the directories are missing.

**Note:** If you prefer to use a different user (e.g., 'www-data'), update all references to 'nginx' in your configs and service files accordingly.

---

### Native Service Installation

#### 1. nginx High-Performance Setup
```bash
# Install nginx from source for maximum performance
sudo apt update
sudo apt install -y build-essential libpcre3-dev libssl-dev zlib1g-dev libgeoip-dev

# Download and compile nginx with performance modules
wget http://nginx.org/download/nginx-1.24.0.tar.gz
tar -xzf nginx-1.24.0.tar.gz
cd nginx-1.24.0

./configure \
    --prefix=/etc/nginx \
    --sbin-path=/usr/sbin/nginx \
    --conf-path=/etc/nginx/nginx.conf \
    --error-log-path=/var/log/nginx/error.log \
    --http-log-path=/var/log/nginx/access.log \
    --with-http_ssl_module \
    --with-http_v2_module \
    --with-http_realip_module \
    --with-http_gzip_static_module \
    --with-http_secure_link_module \
    --with-http_stub_status_module \
    --with-threads \
    --with-file-aio

make && sudo make install
```

#### 2. nginx Performance Configuration
```nginx
# /etc/nginx/nginx.conf
user nginx;
worker_processes auto;
worker_rlimit_nofile 100000;

events {
    worker_connections 10000;
    use epoll;
    multi_accept on;
}

http {
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 30;
    keepalive_requests 1000;
    
    # Rate limiting for load testing
    limit_req_zone $binary_remote_addr zone=api:10m rate=1000r/s;
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    
    # Logging for test validation
    log_format detailed '$remote_addr - $remote_user [$time_local] '
                       '"$request" $status $body_bytes_sent '
                       '"$http_referer" "$http_user_agent" '
                       'rt=$request_time uct="$upstream_connect_time" '
                       'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log detailed;
    
    # Virtual hosts for different test scenarios
    server {
        listen 80;
        server_name load-test.local;
        root /var/www/load-test;
        
        location / {
            return 200 "Load Test Response: $request_id\n";
            add_header Content-Type text/plain;
        }
        
        location /api/ {
            limit_req zone=api burst=100 nodelay;
            limit_conn conn_limit_per_ip 50;
            proxy_pass http://backend_pool;
        }
        
        location /stream {
            proxy_pass http://websocket_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
    
    # SSL/TLS testing server
    server {
        listen 443 ssl http2;
        server_name ssl-test.local;
        
        ssl_certificate /etc/nginx/ssl/server.crt;
        ssl_certificate_key /etc/nginx/ssl/server.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;
        
        location / {
            return 200 "SSL Load Test Response\n";
            add_header Content-Type text/plain;
        }
    }
    
    # Backend pool for load balancing tests
    upstream backend_pool {
        least_conn;
        server 127.0.0.1:8001;
        server 127.0.0.1:8002;
        server 127.0.0.1:8003;
        keepalive 32;
    }
    
    upstream websocket_backend {
        server 127.0.0.1:9001;
    }
}
```

#### 3. Backend Services Setup (Step-by-Step)

To set up the backend services required for testing, you need to create three HTTP backend scripts and one WebSocket backend script. Below are the details for each script.

---

### A. HTTP Backend Scripts

You will need to create three separate Python scripts, each listening on a different port (8001, 8002, and 8003). Each script responds to HTTP GET requests with a JSON payload containing the port, timestamp, request path, and client IP.

**1. HTTP Backend for Port 8001**
- Save the following script as `/usr/local/bin/http-backend-8001.py` and make it executable.

```python
#!/usr/bin/env python3
import http.server
import socketserver
import json
from datetime import datetime

class TestHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        response = {
            'server_port': 8001,
            'timestamp': datetime.now().isoformat(),
            'request_path': self.path,
            'client_ip': self.client_address[0]
        }
        self.wfile.write(json.dumps(response).encode())

with socketserver.TCPServer(("", 8001), TestHandler) as httpd:
    print("Backend server running on port 8001")
    httpd.serve_forever()
```

**2. HTTP Backend for Port 8002**
- Save the following script as `/usr/local/bin/http-backend-8002.py` and make it executable.

```python
#!/usr/bin/env python3
import http.server
import socketserver
import json
from datetime import datetime

class TestHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        response = {
            'server_port': 8002,
            'timestamp': datetime.now().isoformat(),
            'request_path': self.path,
            'client_ip': self.client_address[0]
        }
        self.wfile.write(json.dumps(response).encode())

with socketserver.TCPServer(("", 8002), TestHandler) as httpd:
    print("Backend server running on port 8002")
    httpd.serve_forever()
```

**3. HTTP Backend for Port 8003**
- Save the following script as `/usr/local/bin/http-backend-8003.py` and make it executable.

```python
#!/usr/bin/env python3
import http.server
import socketserver
import json
from datetime import datetime

class TestHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        response = {
            'server_port': 8003,
            'timestamp': datetime.now().isoformat(),
            'request_path': self.path,
            'client_ip': self.client_address[0]
        }
        self.wfile.write(json.dumps(response).encode())

with socketserver.TCPServer(("", 8003), TestHandler) as httpd:
    print("Backend server running on port 8003")
    httpd.serve_forever()
```

---

### B. WebSocket Backend Script

You will also need to create a WebSocket backend script that listens on port 9001 and streams JSON data to connected clients. Before using this script, ensure that the Python dependencies `websockets` and `asyncio` are installed.

- Save the following script as `/usr/local/bin/websocket-server.py` and make it executable.

```python
#!/usr/bin/env python3
import asyncio
import websockets
import json
import time

async def handle_client(websocket, path):
    print(f"Client connected: {websocket.remote_address}")
    try:
        while True:
            data = {
                'timestamp': time.time(),
                'server_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'client_ip': websocket.remote_address[0],
                'message_count': getattr(handle_client, 'counter', 0)
            }
            handle_client.counter = getattr(handle_client, 'counter', 0) + 1
            await websocket.send(json.dumps(data))
            await asyncio.sleep(0.1)
    except websockets.exceptions.ConnectionClosed:
        print(f"Client disconnected: {websocket.remote_address}")

start_server = websockets.serve(handle_client, "localhost", 9001)
print("WebSocket server starting on port 9001")
asyncio.get_event_loop().run_until_complete(start_server)
asyncio.get_event_loop().run_forever()
```

---

After saving these scripts to the correct locations and making them executable, your backend services will be ready for systemd setup in the next section.

#### 4. Systemd Service Configuration

NOTE: For clarity in plain-text editors, all file contents to be pasted are shown as indented blocks. Commands to run are also indented and described in ALL CAPS.

-----

STEP 1: CREATE THE SERVICE FILE FOR HTTP BACKEND 8001

OPEN THE FILE FOR EDITING:
    sudo nano /etc/systemd/system/http-backend-8001.service

PASTE THE FOLLOWING INTO /etc/systemd/system/http-backend-8001.service:
    [Unit]
    Description=HTTP Backend Server on port 8001
    After=network.target

    [Service]
    Type=simple
    User=nginx
    ExecStart=/usr/local/bin/http-backend-8001.py
    Restart=always
    RestartSec=3

    [Install]
    WantedBy=multi-user.target

SAVE AND EXIT THE EDITOR (in nano: Ctrl+O, Enter, Ctrl+X).

-----

STEP 2: CREATE THE SERVICE FILE FOR HTTP BACKEND 8002

OPEN THE FILE FOR EDITING:
    sudo nano /etc/systemd/system/http-backend-8002.service

PASTE THE FOLLOWING INTO /etc/systemd/system/http-backend-8002.service:
    [Unit]
    Description=HTTP Backend Server on port 8002
    After=network.target

    [Service]
    Type=simple
    User=nginx
    ExecStart=/usr/local/bin/http-backend-8002.py
    Restart=always
    RestartSec=3

    [Install]
    WantedBy=multi-user.target

SAVE AND EXIT THE EDITOR.

-----

STEP 3: CREATE THE SERVICE FILE FOR HTTP BACKEND 8003

OPEN THE FILE FOR EDITING:
    sudo nano /etc/systemd/system/http-backend-8003.service

PASTE THE FOLLOWING INTO /etc/systemd/system/http-backend-8003.service:
    [Unit]
    Description=HTTP Backend Server on port 8003
    After=network.target

    [Service]
    Type=simple
    User=nginx
    ExecStart=/usr/local/bin/http-backend-8003.py
    Restart=always
    RestartSec=3

    [Install]
    WantedBy=multi-user.target

SAVE AND EXIT THE EDITOR.

-----

STEP 4: CREATE THE SERVICE FILE FOR THE WEBSOCKET BACKEND (PORT 9001)

OPEN THE FILE FOR EDITING:
    sudo nano /etc/systemd/system/websocket-server.service

PASTE THE FOLLOWING INTO /etc/systemd/system/websocket-server.service:
    [Unit]
    Description=WebSocket Server for Streaming Tests
    After=network.target

    [Service]
    Type=simple
    User=nginx
    ExecStart=/usr/local/bin/websocket-server.py
    Restart=always
    RestartSec=3

    [Install]
    WantedBy=multi-user.target

SAVE AND EXIT THE EDITOR.

-----

STEP 5: SET PERMISSIONS FOR ALL SERVICE FILES

RUN THE FOLLOWING COMMANDS:
    sudo chown root:root /etc/systemd/system/http-backend-8001.service
    sudo chown root:root /etc/systemd/system/http-backend-8002.service
    sudo chown root:root /etc/systemd/system/http-backend-8003.service
    sudo chown root:root /etc/systemd/system/websocket-server.service
    sudo chmod 644 /etc/systemd/system/http-backend-8001.service
    sudo chmod 644 /etc/systemd/system/http-backend-8002.service
    sudo chmod 644 /etc/systemd/system/http-backend-8003.service
    sudo chmod 644 /etc/systemd/system/websocket-server.service

-----

STEP 6: RELOAD SYSTEMD TO RECOGNIZE THE NEW SERVICE FILES

RUN THE FOLLOWING COMMAND:
    sudo systemctl daemon-reload

-----

STEP 7: ENABLE AND START EACH SERVICE

RUN THE FOLLOWING COMMANDS:
    sudo systemctl enable http-backend-8001
    sudo systemctl enable http-backend-8002
    sudo systemctl enable http-backend-8003
    sudo systemctl enable websocket-server
    sudo systemctl start http-backend-8001
    sudo systemctl start http-backend-8002
    sudo systemctl start http-backend-8003
    sudo systemctl start websocket-server

-----

STEP 8: CHECK SERVICE STATUS AND TROUBLESHOOT IF NEEDED

TO CHECK IF A SERVICE IS RUNNING:
    sudo systemctl status http-backend-8001
    sudo systemctl status http-backend-8002
    sudo systemctl status http-backend-8003
    sudo systemctl status websocket-server

IF YOU SEE "active (running)", THE SERVICE IS RUNNING CORRECTLY.

TO VIEW LOGS FOR TROUBLESHOOTING:
    sudo journalctl -u http-backend-8001
    sudo journalctl -u http-backend-8002
    sudo journalctl -u http-backend-8003
    sudo journalctl -u websocket-server

-----

This format ensures that what you need to add to each file is visually distinct and easy to follow in any plain-text editor.

#### 5. SSL Certificate Generation
```bash
# Create self-signed certificates for SSL testing
sudo mkdir -p /etc/nginx/ssl
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/nginx/ssl/server.key \
    -out /etc/nginx/ssl/server.crt \
    -subj "/C=US/ST=Test/L=Test/O=NeuralMeter/CN=ssl-test.local"
```

## Testing Methodology by Subtask

### Task 6.1: CLI Command Structure (Completed)
**Validation Approach:**
- Deploy CLI binary to System A
- Test basic commands: `neuralmeter --help`, `neuralmeter --version`
- Verify server commands target System B correctly
- Test: `neuralmeter run --url http://system-b-ip/` against nginx

### Task 6.2: Server Daemon Mode
**Test Protocol:**
1. Start daemon on System A: `neuralmeter server start --daemon`
2. Configure System B nginx endpoints
3. Submit long-running test jobs targeting System B
4. Test daemon lifecycle: stop, restart, status during active testing
5. Verify graceful shutdown preserves test integrity
6. Monitor system resources on both machines

**Success Criteria:**
- Daemon survives terminal disconnection
- PID file management works correctly
- Graceful shutdown doesn't corrupt test results
- Multiple concurrent test jobs execute properly

### Task 6.3: Remote API Server
**Test Protocol:**
1. Start API server on System A: `neuralmeter server start --api-port 8080`
2. Deploy nginx on System B with test endpoints
3. Submit test plans via API: `curl -X POST http://system-a:8080/api/tests`
4. Verify API orchestrates load tests against System B
5. Test API resilience during System B service disruptions

**API Test Scenarios:**
```bash
# Test plan submission
curl -X POST http://system-a:8080/api/tests \
  -H "Content-Type: application/json" \
  -d '{
    "test_plan": "load-test.yaml",
    "target_url": "http://system-b-ip",
    "duration": "5m",
    "concurrency": 100
  }'

# Status monitoring
curl http://system-a:8080/api/tests/status

# Real-time metrics
curl http://system-a:8080/api/metrics/live
```

### Task 6.4: Test Plan Processing
**Test Protocol:**
1. Create YAML test plans targeting System B endpoints
2. Deploy test plans to System A
3. Execute: `neuralmeter run test-plan.yaml`
4. Verify URL resolution to System B services
5. Validate test step execution against remote targets

**Sample Test Plan:**
```yaml
# test-plan.yaml
name: "System B Load Test"
global:
  target_host: "http://system-b-ip"
  
scenarios:
  - name: "Basic Load Test"
    requests:
      - method: GET
        url: "${target_host}/api/health"
        assertions:
          - status_code: 200
      - method: POST
        url: "${target_host}/api/data"
        body: '{"test": "data"}'
        
load_pattern:
  type: "ramp_up"
  users: 1000
  duration: "10m"
```

### Task 6.5: GPU-Accelerated Load Testing
**Test Protocol:**
1. Verify GPU detection on System A: `neuralmeter gpu list`
2. Configure System B for high-load scenarios
3. Execute GPU-accelerated tests: `neuralmeter run --gpu-accelerated test-plan.yaml`
4. Monitor GPU utilization: `nvidia-smi -l 1`
5. Compare performance: with/without GPU acceleration

**Performance Validation:**
- Measure requests/second with GPU vs CPU-only
- Monitor GPU memory usage during tests
- Verify load generation scales with GPU resources
- Test GPU resource allocation under concurrent tests

### Task 6.6: HTTP Load Generation Engine
**Test Protocol:**
1. Configure System B nginx with connection limits
2. Execute sustained load tests from System A
3. Monitor System B nginx access logs: `tail -f /var/log/nginx/access.log`
4. Test connection pool efficiency and reuse
5. Validate various load patterns (ramp-up, sustained, burst)

**Connection Pool Testing:**
```bash
# Monitor connections from System A to System B
# On System A:
ss -tuln | grep :80

# On System B:
netstat -an | grep :80 | wc -l
```

### Task 6.7: Real-time Result Streaming
**Test Protocol:**
1. Start load test on System A targeting System B
2. Enable real-time streaming: `neuralmeter run --stream-results ws://controller:9090`
3. Monitor streaming data quality and latency
4. Test stream resilience during network interruptions
5. Validate metric accuracy against System B logs

**Stream Validation:**
- Compare streamed metrics with System B nginx logs
- Test stream reconnection capabilities
- Verify data consistency during network issues
- Monitor streaming overhead on test performance

### Task 6.8: GPU Command Integration
**Test Protocol:**
1. Test GPU commands on System A: `neuralmeter gpu status`
2. Execute GPU benchmarks: `neuralmeter gpu benchmark`
3. Monitor GPU during active load testing of System B
4. Verify GPU optimization improves load generation
5. Test GPU resource allocation across multiple tests

### Task 6.9: Configuration Management
**Test Protocol:**
1. Create configuration targeting System B endpoints
2. Test configuration loading: `neuralmeter config validate`
3. Verify hot-reload: modify config during active testing
4. Test environment variable substitution for System B URLs
5. Validate configuration precedence rules

### Task 6.10: Output Formatting and Logging
**Test Protocol:**
1. Execute tests with various output formats: `--output json`, `--output text`
2. Monitor structured logging during System B testing
3. Test log parsing for CI/CD integration
4. Verify error message clarity and debugging information
5. Validate log rotation and management

## Performance Validation Metrics

### System A (CLI Tool) Metrics
- **CPU Usage**: <80% during sustained load generation
- **Memory Usage**: <16GB for 10,000 concurrent users
- **GPU Utilization**: >70% during GPU-accelerated tests
- **Network Throughput**: >1 Gbps sustained output to System B
- **Request Generation Rate**: >50,000 RPS with GPU acceleration
- **Connection Pool Efficiency**: >95% connection reuse rate
- **Response Time**: <1ms internal processing overhead

### System B (Target Services) Metrics
- **nginx Request Handling**: >100,000 RPS capacity
- **Connection Handling**: >10,000 concurrent connections
- **Response Time**: <10ms average response time
- **Error Rate**: <0.1% under sustained load
- **Resource Usage**: CPU <80%, Memory <8GB

### End-to-End Performance Targets
- **Load Generation Scaling**: Linear scaling with GPU cores
- **Network Efficiency**: >90% bandwidth utilization
- **Test Accuracy**: <1% variance in reported vs actual metrics
- **System Stability**: 24+ hour continuous operation capability

## Monitoring and Observability

### System A Monitoring Scripts
```bash
#!/bin/bash
# monitor-system-a.sh - Real-time monitoring of CLI performance tool

echo "=== NeuralMeter CLI Performance Monitoring ==="
while true; do
    echo "$(date): System A Status"
    
    # CPU and Memory
    echo "CPU/Memory: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')% CPU, $(free -h | awk 'NR==2{printf "%.1f/%.1fGB (%.2f%%)", $3/1024/1024/1024, $2/1024/1024/1024, $3*100/$2}')"
    
    # GPU Status
    if command -v nvidia-smi &> /dev/null; then
        echo "GPU: $(nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total --format=csv,noheader,nounits | awk -F, '{printf "%s%% GPU, %sMB/%sMB VRAM", $1, $2, $3}')"
    fi
    
    # Network connections to System B
    CONNECTIONS=$(ss -tn state established | grep ":80\|:443" | wc -l)
    echo "Active connections to System B: $CONNECTIONS"
    
    # NeuralMeter process status
    if pgrep neuralmeter > /dev/null; then
        PID=$(pgrep neuralmeter)
        echo "NeuralMeter PID: $PID ($(ps -p $PID -o %cpu,rss --no-headers | awk '{printf "%.1f%% CPU, %.1fMB RAM", $1, $2/1024}'))"
    else
        echo "NeuralMeter: NOT RUNNING"
    fi
    
    echo "----------------------------------------"
    sleep 5
done
```

### System B Monitoring Scripts
```bash
#!/bin/bash
# monitor-system-b.sh - Real-time monitoring of target services

echo "=== System B Target Services Monitoring ==="
while true; do
    echo "$(date): System B Status"
    
    # nginx status
    NGINX_CONNECTIONS=$(curl -s http://localhost/nginx_status | grep "Active connections" | awk '{print $3}' 2>/dev/null || echo "N/A")
    echo "nginx active connections: $NGINX_CONNECTIONS"
    
    # Request rate from access logs (last minute)
    REQUESTS_LAST_MINUTE=$(tail -1000 /var/log/nginx/access.log | awk -v now="$(date +%s)" -v minute_ago="$(($(date +%s) - 60))" '$4 ~ /\[.*\]/ {gsub(/\[|\]/, "", $4); cmd="date -d \""$4"\" +%s"; cmd | getline timestamp; close(cmd); if (timestamp >= minute_ago) count++} END {print count+0}')
    echo "Requests/minute: $REQUESTS_LAST_MINUTE"
    
    # Backend service status
    for port in 8001 8002 8003; do
        if curl -s --connect-timeout 1 http://localhost:$port > /dev/null; then
            echo "Backend $port: UP"
        else
            echo "Backend $port: DOWN"
        fi
    done
    
    # WebSocket server status
    if curl -s --connect-timeout 1 http://localhost:9001 > /dev/null; then
        echo "WebSocket server: UP"
    else
        echo "WebSocket server: DOWN"
    fi
    
    # System resources
    echo "System: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')% CPU, $(free -h | awk 'NR==2{printf "%.1f/%.1fGB", $3/1024/1024/1024, $2/1024/1024/1024}')"
    
    echo "----------------------------------------"
    sleep 5
done
```

### Log Analysis Scripts
```bash
#!/bin/bash
# analyze-test-results.sh - Post-test analysis

echo "=== Test Results Analysis ==="

# nginx access log analysis
echo "nginx Access Log Analysis:"
echo "Total requests: $(wc -l < /var/log/nginx/access.log)"
echo "Status code distribution:"
awk '{print $9}' /var/log/nginx/access.log | sort | uniq -c | sort -nr

echo "Response time statistics:"
awk '{print $NF}' /var/log/nginx/access.log | grep -E '^[0-9]' | awk '{sum+=$1; n++} END {if(n>0) printf "Avg: %.3fs, Total requests: %d\n", sum/n, n}'

echo "Top client IPs:"
awk '{print $1}' /var/log/nginx/access.log | sort | uniq -c | sort -nr | head -10

# Error log analysis
if [ -s /var/log/nginx/error.log ]; then
    echo "nginx Errors:"
    tail -20 /var/log/nginx/error.log
else
    echo "No nginx errors detected"
fi
```

## Failure Scenario Testing

### Network Disruption Tests
```bash
# Simulate network latency between System A and B
sudo tc qdisc add dev eth0 root netem delay 100ms 10ms

# Simulate packet loss
sudo tc qdisc add dev eth0 root netem loss 1%

# Simulate bandwidth limitation
sudo tc qdisc add dev eth0 root tbf rate 100mbit burst 32kbit latency 400ms

# Remove network simulation
sudo tc qdisc del dev eth0 root
```

### Service Failure Simulation
```bash
# Stop specific backend services
sudo systemctl stop http-backend-8001

# Restart nginx during active testing
sudo systemctl restart nginx

# Simulate high CPU load on System B
stress-ng --cpu 4 --timeout 60s

# Simulate memory pressure
stress-ng --vm 2 --vm-bytes 80% --timeout 60s
```

### GPU Failure Testing
```bash
# Simulate GPU unavailability (System A)
sudo rmmod nvidia_drm nvidia_modeset nvidia

# Test CLI behavior without GPU
neuralmeter run test-plan.yaml --no-gpu

# Restore GPU functionality
sudo modprobe nvidia nvidia_modeset nvidia_drm
```

## Test Validation Criteria

### Functional Validation
- [ ] CLI commands execute successfully against System B
- [ ] Daemon mode operates continuously with proper lifecycle management
- [ ] API server orchestrates tests correctly against remote targets
- [ ] Test plans parse and execute against System B endpoints
- [ ] GPU acceleration improves load generation measurably
- [ ] HTTP engine generates sustained load efficiently
- [ ] Results stream accurately in real-time
- [ ] GPU commands manage hardware resources correctly
- [ ] Configuration changes redirect tests to correct System B endpoints
- [ ] Output formats and logging capture accurate test data

### Performance Validation
- [ ] System A generates >50,000 RPS with GPU acceleration
- [ ] System B handles >100,000 RPS without degradation
- [ ] Connection pools maintain >95% reuse efficiency
- [ ] GPU utilization exceeds 70% during accelerated tests
- [ ] Memory usage remains under 16GB for 10,000 concurrent users
- [ ] Network throughput sustains >1 Gbps to System B
- [ ] Response time variance <1% between reported and actual
- [ ] System operates continuously for 24+ hours

### Reliability Validation
- [ ] Graceful shutdown preserves test integrity
- [ ] Network disruptions don't corrupt test results
- [ ] Service failures trigger appropriate error handling
- [ ] GPU failures gracefully degrade to CPU-only operation
- [ ] Configuration errors provide clear diagnostic information
- [ ] Log rotation and management work correctly
- [ ] Resource exhaustion triggers appropriate warnings
- [ ] Recovery procedures restore full functionality

## Development Workflow

### Code Change → Test Cycle
1. **Develop**: Implement changes in NeuralMeter CLI codebase
2. **Build**: Compile CLI binary on development machine
3. **Deploy**: Transfer binary to System A Linux machine
4. **Configure**: Update System B target services if needed
5. **Test**: Execute specific subtask tests against System B
6. **Monitor**: Observe System A and System B metrics
7. **Analyze**: Review logs and performance data
8. **Iterate**: Return to step 1 based on results

### Automated Testing Pipeline
```bash
#!/bin/bash
# automated-test-cycle.sh

echo "=== Automated Test Cycle for Task 6 ==="

# Build and deploy CLI
echo "Building NeuralMeter CLI..."
go build -o neuralmeter cmd/neuralmeter/main.go

echo "Deploying to System A..."
scp neuralmeter system-a-ip:/usr/local/bin/

# Restart services
echo "Restarting System A daemon..."
ssh system-a-ip "sudo systemctl restart neuralmeter"

echo "Verifying System B services..."
ssh system-b-ip "sudo systemctl status nginx http-backend-8001 http-backend-8002 http-backend-8003 websocket-server"

# Execute test suite
echo "Running test suite..."
ssh system-a-ip "neuralmeter run --config /etc/neuralmeter/test-suite.yaml --target http://system-b-ip"

# Collect results
echo "Collecting test results..."
scp system-a-ip:/var/log/neuralmeter/test-results.json ./results/
scp system-b-ip:/var/log/nginx/access.log ./results/nginx-access.log

echo "Test cycle complete. Results in ./results/"
```

## Security Considerations

### System A Security
- Restrict SSH access to development team only
- Use SSH key authentication, disable password auth
- Configure firewall to allow only necessary outbound connections
- Monitor system logs for unusual activity
- Regular security updates for CUDA drivers and system packages

### System B Security
- Isolate test environment from production networks
- Use non-standard ports for services where possible
- Implement rate limiting to prevent resource exhaustion
- Monitor for unusual traffic patterns
- Regular backup of configuration and logs

### Network Security
- Use private network segment for System A ↔ System B communication
- Implement network monitoring and intrusion detection
- Use SSL/TLS for encrypted traffic testing scenarios
- Regular security audits of test infrastructure

## Conclusion

This comprehensive test approach ensures Task 6 (CLI Interface Implementation) is thoroughly validated using real hardware, actual network communication, and genuine service interactions. The **NO STUBS OR MOCKS** philosophy is maintained throughout, providing confidence that the NeuralMeter CLI will perform effectively in production environments.

The clear separation between System A (performance testing tool) and System B (target services) eliminates confusion about traffic direction and system responsibilities, enabling focused development and accurate performance validation.

---