# NeuralMeter CLI – Command Test Matrix
> Reference commands used by the automated Test Approach  
> (`test_approach_documentation.md`) to confirm that **System A**  
> (NeuralMeter CLI) satisfies each Task-6 requirement.  
> All commands assume:
> * `neuralmeter` is available on `$PATH`
> * Server daemon (if required) listens on `localhost:8080`

---

## 6.1 CLI Command Structure Setup
| Purpose                     | Command                                  | Success criteria                        |
|-----------------------------|-------------------------------------------|-----------------------------------------|
| Global help                 | `neuralmeter --help`                     | Prints command tree, exits 0            |
| Sub-command help            | `neuralmeter help run`                   | Prints help for *run*, exits 0          |
| Version info                | `neuralmeter --version`                  | Shows version string, exits 0           |
| Invalid command handling    | `neuralmeter UNKNOWN`                    | Non-zero exit, friendly error message   |

## 6.2 Server Daemon Mode Implementation
| Purpose      | Command                                                           |
|--------------|-------------------------------------------------------------------|
| Start daemon | `neuralmeter server start`                                        |
| Foreground   | `neuralmeter server start --daemon=false --port 8080`             |
| Status       | `neuralmeter server status`                                       |
| Stop daemon  | `neuralmeter server stop`                                         |
| Force stop   | `neuralmeter server stop --force`                                 |
| Restart      | `neuralmeter server restart`                                      |

## 6.3 Remote API Server Setup
| Purpose                | Command                                                                           |
|------------------------|-----------------------------------------------------------------------------------|
| Health-check endpoint  | `curl http://localhost:8080/healthz`                                              |
| List API routes        | `curl http://localhost:8080/`                                                     |
| Submit test job (ex.)  | `curl -X POST -H "Content-Type: application/json" \`<br>`  --data-binary @example-test-plan.yaml \`<br>`  http://localhost:8080/api/v1/jobs` |

## 6.4 Job Queue Integration
| Purpose     | Command                                   |
|-------------|-------------------------------------------|
| List jobs   | `neuralmeter job list`                    |
| Job status  | `neuralmeter job status <job-id>`         |

## 6.5 Worker Registration System
| Purpose                     | Command                                       |
|-----------------------------|-----------------------------------------------|
| Show master connections     | `neuralmeter worker masters`                  |
| Manual heartbeat (one-shot) | `neuralmeter worker heartbeat --once`         |

## 6.6 Test Execution Integration
| Purpose          | Command                                    |
|------------------|--------------------------------------------|
| Run test plan    | `neuralmeter run test-plan.yaml`           |
| Validate only    | `neuralmeter validate test-plan.yaml`      |

## 6.7 Result Streaming Implementation
| Purpose                    | Command                                                          |
|----------------------------|------------------------------------------------------------------|
| JSON streaming             | `neuralmeter run --output-format json test-plan.yaml`            |
| Follow live WebSocket      | `wscat -c ws://localhost:8080/stream/<job-id>`                   |

## 6.8 GPU Command Integration
| Purpose      | Command                       |
|--------------|------------------------------|
| List devices | `neuralmeter gpu list`       |
| GPU status   | `neuralmeter gpu status 0`   |

## 6.9 Configuration Management Integration
| Purpose             | Command                                        |
|---------------------|------------------------------------------------|
| Show config (text)  | `neuralmeter config`                           |
| Show config (JSON)  | `neuralmeter config --output-format json`      |

## 6.10 Output Formatting & Logging
| Purpose           | Command                                                  |
|-------------------|----------------------------------------------------------|
| Default (text)    | `neuralmeter run test-plan.yaml`                         |
| JSON output       | `neuralmeter run -o json test-plan.yaml`                 |
| YAML output       | `neuralmeter run -o yaml test-plan.yaml`                 |
| Debug log level   | `neuralmeter --log-level debug run test-plan.yaml`       |

---

### Usage Notes
1. Copy/paste the relevant section when implementing or reviewing a sub-task.  
2. All commands should return exit-code 0 on success unless “error path” is being tested.  
3. For CI automation wrap each command in `bash -e -c "<cmd>"` to halt on failure.  
4. Extend this file whenever new CLI flags or sub-commands are added.
```

Save this file to make the command list part of your documentation and Test-Approach workflow.