//go:build darwin

package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"gopkg.in/yaml.v3"

	"neuralmetergo/internal/config"
	"neuralmetergo/internal/engine"
	"neuralmetergo/internal/gpu/macos/metal"
	"neuralmetergo/internal/parser"
	"neuralmetergo/internal/validation"
)

var (
	cfgFile      string
	verbose      bool
	quiet        bool
	logLevel     string
	outputFormat string
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "neuralmeter-macos",
	Short: "NeuralMeter for macOS - Advanced Load Testing Tool with Metal GPU Support",
	Long: `NeuralMeter for macOS is a high-performance load testing tool designed for modern applications
with native Metal GPU acceleration for Apple Silicon and Intel Macs.

Features:
  • High-performance HTTP load testing
  • Advanced metrics collection and aggregation
  • Metal GPU acceleration for enhanced performance on macOS
  • Intelligent worker pool management
  • Comprehensive result analysis and reporting
  • Multi-format configuration support (YAML, JSON)

Metal GPU Support:
  • Native Metal framework integration
  • Optimized for Apple Silicon (M1/M2/M3) and Intel GPUs
  • Hardware-accelerated load generation
  • Real-time GPU performance monitoring`,
	Version: "1.0.0-macos",
}

// runCmd represents the run command
var runCmd = &cobra.Command{
	Use:   "run <test-plan.yaml>",
	Short: "Execute load tests with Metal GPU acceleration",
	Long: `Execute load tests using the specified test plan configuration with Metal GPU acceleration.
The test plan should be a YAML or JSON file containing test scenarios,
configuration settings, and execution parameters.

Metal GPU acceleration is automatically enabled when available and provides:
  • Faster request generation
  • Enhanced throughput capabilities
  • Real-time performance monitoring
  • Optimized memory management

Examples:
  neuralmeter-macos run test-plan.yaml
  neuralmeter-macos run --config custom-config.yaml test-plan.yaml
  neuralmeter-macos run --verbose --output-format json test-plan.yaml`,
	Args: cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		return runTests(args[0])
	},
}

// validateCmd represents the validate command
var validateCmd = &cobra.Command{
	Use:   "validate <test-plan.yaml>",
	Short: "Validate a test plan configuration",
	Long: `Validate the syntax and structure of a test plan configuration file.
This command checks for configuration errors, validates dependencies,
and ensures the test plan is ready for execution with Metal GPU acceleration.

Examples:
  neuralmeter-macos validate test-plan.yaml
  neuralmeter-macos validate --verbose test-plan.yaml`,
	Args: cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		return validateTestPlan(args[0])
	},
}

// configCmd represents the config command
var configCmd = &cobra.Command{
	Use:   "config",
	Short: "Display current configuration including Metal GPU status",
	Long: `Display the current NeuralMeter configuration including all settings
from configuration files, environment variables, and command-line flags.
Also shows Metal GPU availability and status.

Examples:
  neuralmeter-macos config
  neuralmeter-macos config --output-format json
  neuralmeter-macos config --output-format yaml`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return showConfig()
	},
}

// versionCmd represents the version command
var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Show version information and Metal GPU status",
	Long:  `Display NeuralMeter version information, build details, and Metal GPU capabilities.`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Printf("NeuralMeter for macOS v%s\n", rootCmd.Version)
		fmt.Println("High-performance load testing tool with Metal GPU acceleration")
		fmt.Println("Built with Go and Metal framework")

		// Display Metal GPU status
		showMetalStatus()
	},
}

// metalCmd represents Metal GPU specific commands
var metalCmd = &cobra.Command{
	Use:   "metal",
	Short: "Metal GPU management and information",
	Long: `Manage and view information about Metal GPU devices and capabilities.
Provides detailed information about available Metal devices, performance
characteristics, and GPU utilization.`,
}

// metalInfoCmd shows Metal GPU information
var metalInfoCmd = &cobra.Command{
	Use:   "info",
	Short: "Display Metal GPU device information",
	Long:  `Display detailed information about available Metal GPU devices.`,
	Run: func(cmd *cobra.Command, args []string) {
		showDetailedMetalInfo()
	},
}

// metalStatusCmd shows Metal GPU status
var metalStatusCmd = &cobra.Command{
	Use:   "status",
	Short: "Display Metal GPU status and utilization",
	Long:  `Display current Metal GPU status, utilization, and performance metrics.`,
	Run: func(cmd *cobra.Command, args []string) {
		showMetalStatus()
	},
}

func main() {
	cobra.OnInitialize(initConfig)

	// Add global flags
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is ./neuralmeter.yaml)")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "enable verbose output")
	rootCmd.PersistentFlags().BoolVarP(&quiet, "quiet", "q", false, "enable quiet mode")
	rootCmd.PersistentFlags().StringVar(&logLevel, "log-level", "info", "log level (debug, info, warn, error)")

	// Add output format flag to relevant commands
	runCmd.Flags().StringVarP(&outputFormat, "output-format", "o", "text", "output format (text, json, yaml)")
	configCmd.Flags().StringVarP(&outputFormat, "output-format", "o", "text", "output format (text, json, yaml)")
	validateCmd.Flags().BoolVarP(&verbose, "verbose", "v", false, "enable verbose validation output")

	// Add Metal GPU commands
	metalCmd.AddCommand(metalInfoCmd)
	metalCmd.AddCommand(metalStatusCmd)

	// Add commands to root
	rootCmd.AddCommand(runCmd)
	rootCmd.AddCommand(validateCmd)
	rootCmd.AddCommand(configCmd)
	rootCmd.AddCommand(versionCmd)
	rootCmd.AddCommand(metalCmd)

	// Execute the root command
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

// initConfig reads in config file and ENV variables if set
func initConfig() {
	if cfgFile != "" {
		// Use config file from the flag
		viper.SetConfigFile(cfgFile)
	} else {
		// Search for config in current directory
		viper.AddConfigPath(".")
		viper.SetConfigName("neuralmeter")
		viper.SetConfigType("yaml")
	}

	// Read in environment variables that match
	viper.SetEnvPrefix("NEURALMETER")
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Set defaults
	setConfigDefaults()

	// If a config file is found, read it in
	if err := viper.ReadInConfig(); err == nil && verbose {
		fmt.Fprintf(os.Stderr, "Using config file: %s\n", viper.ConfigFileUsed())
	}

	// Configure logging based on flags
	configureLogging()
}

// setConfigDefaults sets default configuration values
func setConfigDefaults() {
	// Worker configuration
	viper.SetDefault("worker.pool_size", 10)
	viper.SetDefault("worker.queue_size", 1000)
	viper.SetDefault("worker.timeout", "30s")

	// HTTP client configuration
	viper.SetDefault("http.timeout", "30s")
	viper.SetDefault("http.max_idle_conns", 100)
	viper.SetDefault("http.max_conns_per_host", 10)

	// Metal GPU configuration
	viper.SetDefault("gpu.enabled", true)
	viper.SetDefault("gpu.backend", "metal")
	viper.SetDefault("gpu.device_preference", "discrete")
	viper.SetDefault("gpu.memory_pool_size", "256MB")

	// Metrics configuration
	viper.SetDefault("metrics.collection_interval", "1s")
	viper.SetDefault("metrics.buffer_size", 10000)

	// Server configuration
	viper.SetDefault("server.host", "localhost")
	viper.SetDefault("server.port", 8080)
}

// configureLogging sets up logging based on configuration
func configureLogging() {
	level := logLevel
	if quiet {
		level = "error"
	} else if verbose {
		level = "debug"
	}

	switch level {
	case "debug":
		log.SetOutput(os.Stderr)
	case "info":
		log.SetOutput(os.Stderr)
	case "warn":
		log.SetOutput(os.Stderr)
	case "error":
		log.SetOutput(os.Stderr)
	default:
		log.SetOutput(os.Stderr)
	}
}

// runTests executes a test plan file with Metal GPU acceleration
func runTests(testPlanFile string) error {
	if verbose {
		fmt.Printf("Running test plan: %s\n", testPlanFile)
	}

	// Check if file exists
	if _, err := os.Stat(testPlanFile); os.IsNotExist(err) {
		return fmt.Errorf("test plan file not found: %s", testPlanFile)
	}

	// Create parser and parse test plan
	parser := parser.NewParser()
	testPlan, err := parser.ParseFile(testPlanFile)
	if err != nil {
		return fmt.Errorf("failed to parse test plan: %w", err)
	}

	// Validate test plan
	validator := validation.NewValidationEngine()
	result := validator.ValidateTestPlan(testPlan)
	if !result.IsValid() {
		return fmt.Errorf("test plan validation failed with %d error(s)", result.Summary.ErrorCount)
	}

	// Create execution engine with Metal GPU support
	engine := engine.NewExecutionEngine(testPlan, nil)

	// Initialize and start the engine
	if err := engine.Initialize(); err != nil {
		return fmt.Errorf("failed to initialize execution engine: %w", err)
	}

	if err := engine.Start(); err != nil {
		return fmt.Errorf("failed to start execution engine: %w", err)
	}

	// Wait for completion and get metrics
	for {
		state := engine.GetState()
		if state.Status.String() == "completed" || state.Status.String() == "failed" {
			break
		}
		time.Sleep(100 * time.Millisecond)
	}

	// Get final metrics
	metrics := engine.GetMetrics()

	// Display results
	return displayResults(metrics)
}

// validateTestPlan validates a test plan file
func validateTestPlan(testPlanFile string) error {
	if verbose {
		fmt.Printf("Validating test plan: %s\n", testPlanFile)
	}

	// Check if file exists
	if _, err := os.Stat(testPlanFile); os.IsNotExist(err) {
		return fmt.Errorf("test plan file not found: %s", testPlanFile)
	}

	// Create parser and parse test plan
	parser := parser.NewParser()
	testPlan, err := parser.ParseFile(testPlanFile)
	if err != nil {
		return fmt.Errorf("failed to parse test plan: %w", err)
	}

	// Validate test plan
	validator := validation.NewValidationEngine()
	result := validator.ValidateTestPlan(testPlan)

	// Check if validation passed
	if !result.IsValid() {
		// Display validation issues
		fmt.Printf("❌ Test plan '%s' validation failed\n", filepath.Base(testPlanFile))
		fmt.Printf("\nValidation Issues:\n")
		for i, issue := range result.Issues {
			if issue.Severity == validation.SeverityError {
				fmt.Printf("  %d. [ERROR] %s\n", i+1, issue.Message)
				if issue.Field != "" {
					fmt.Printf("     Field: %s\n", issue.Field)
				}
			}
		}
		return fmt.Errorf("validation failed with %d error(s)", result.Summary.ErrorCount)
	}

	fmt.Printf("✅ Test plan '%s' is valid\n", filepath.Base(testPlanFile))
	if verbose {
		fmt.Printf("   • Scenarios: %d\n", len(testPlan.Scenarios))
		fmt.Printf("   • Duration: %s\n", testPlan.Duration.Duration.String())
		if testPlan.Concurrency > 0 {
			fmt.Printf("   • Concurrency: %d\n", testPlan.Concurrency)
		}

		// Show warnings if any
		if result.Summary.WarningCount > 0 {
			fmt.Printf("\nWarnings:\n")
			for i, issue := range result.Issues {
				if issue.Severity == validation.SeverityWarning {
					fmt.Printf("  %d. [WARNING] %s\n", i+1, issue.Message)
					if issue.Field != "" {
						fmt.Printf("     Field: %s\n", issue.Field)
					}
				}
			}
		}
	}

	return nil
}

// showConfig displays the current configuration including Metal GPU status
func showConfig() error {
	cfg, err := loadConfiguration()
	if err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	switch outputFormat {
	case "json":
		return outputConfigJSON(cfg)
	case "yaml":
		return outputConfigYAML(cfg)
	default:
		return outputConfigText(cfg)
	}
}

// loadConfiguration loads the configuration using the config package
func loadConfiguration() (*config.Config, error) {
	// Create config manager with config file if provided
	var configManager *config.ConfigManager
	var err error

	if cfgFile != "" {
		configManager, err = config.NewConfigManager(cfgFile)
	} else {
		// Try to find default config file
		defaultPaths := []string{"neuralmeter.yaml", "neuralmeter.yml", "config.yaml", "config.yml"}
		for _, path := range defaultPaths {
			if _, err := os.Stat(path); err == nil {
				configManager, err = config.NewConfigManager(path)
				break
			}
		}
		if configManager == nil {
			// Create from environment if no config file found
			configManager, err = config.NewConfigManagerFromEnvironment()
		}
	}

	if err != nil {
		return nil, err
	}

	// Load configuration
	if err := configManager.Load(); err != nil {
		return nil, err
	}

	return configManager.Get(), nil
}

// displayResults displays test execution results
func displayResults(results interface{}) error {
	switch outputFormat {
	case "json":
		return outputJSON(results)
	case "yaml":
		return outputYAML(results)
	default:
		return outputText(results)
	}
}

// outputConfigText displays configuration in text format with Metal GPU info
func outputConfigText(cfg *config.Config) error {
	fmt.Println("NeuralMeter for macOS Configuration:")
	fmt.Printf("  Worker Pool Size: %d\n", cfg.Worker.PoolSize)
	fmt.Printf("  Queue Size: %d\n", cfg.Worker.QueueSize)
	fmt.Printf("  Metrics Collection Interval: %s\n", cfg.Metrics.CollectionInterval.String())
	fmt.Printf("  Server Host: %s\n", cfg.Server.Host)
	fmt.Printf("  Server Port: %d\n", cfg.Server.Port)
	fmt.Printf("  GPU Enabled: %t\n", cfg.GPU.Enabled)

	if cfgFile != "" {
		fmt.Printf("  Config File: %s\n", cfgFile)
	}

	// Show Metal GPU status
	fmt.Println("\nMetal GPU Status:")
	showMetalStatus()

	return nil
}

// outputConfigJSON displays configuration in JSON format
func outputConfigJSON(cfg *config.Config) error {
	encoder := json.NewEncoder(os.Stdout)
	encoder.SetIndent("", "  ")
	return encoder.Encode(cfg)
}

// outputConfigYAML displays configuration in YAML format
func outputConfigYAML(cfg *config.Config) error {
	encoder := yaml.NewEncoder(os.Stdout)
	defer encoder.Close()
	return encoder.Encode(cfg)
}

// outputText displays results in text format
func outputText(results interface{}) error {
	fmt.Println("Test execution completed.")
	fmt.Printf("Results: %+v\n", results)
	return nil
}

// outputJSON displays results in JSON format
func outputJSON(results interface{}) error {
	encoder := json.NewEncoder(os.Stdout)
	encoder.SetIndent("", "  ")
	return encoder.Encode(results)
}

// outputYAML displays results in YAML format
func outputYAML(results interface{}) error {
	encoder := yaml.NewEncoder(os.Stdout)
	defer encoder.Close()
	return encoder.Encode(results)
}

// initializeMetalProvider initializes the Metal GPU provider
func initializeMetalProvider() (*metal.Provider, error) {
	// Create Metal provider using the factory
	factory := metal.NewFactory()
	provider, err := factory.CreateProvider()
	if err != nil {
		return nil, fmt.Errorf("failed to create Metal provider: %w", err)
	}

	// Initialize the provider
	if err := provider.Initialize(); err != nil {
		return nil, fmt.Errorf("failed to initialize Metal provider: %w", err)
	}

	return provider, nil
}

// showMetalStatus displays Metal GPU status
func showMetalStatus() {
	provider, err := initializeMetalProvider()
	if err != nil {
		fmt.Printf("  Status: ❌ Not Available (%v)\n", err)
		return
	}
	defer provider.Cleanup()

	devices, err := provider.GetDevices()
	if err != nil {
		fmt.Printf("  Status: ❌ Error getting devices (%v)\n", err)
		return
	}

	fmt.Printf("  Status: ✅ Available\n")
	fmt.Printf("  Devices: %d\n", len(devices))

	if verbose {
		for i, device := range devices {
			fmt.Printf("    Device %d:\n", i+1)
			fmt.Printf("      Name: %s\n", device.GetName())
			fmt.Printf("      Platform: %s\n", device.GetPlatform().String())
			fmt.Printf("      Memory: %d MB\n", device.GetMemorySize()/(1024*1024))
		}
	}
}

// showDetailedMetalInfo displays detailed Metal GPU information
func showDetailedMetalInfo() {
	fmt.Println("Metal GPU Device Information:")
	fmt.Println("=============================")

	provider, err := initializeMetalProvider()
	if err != nil {
		fmt.Printf("❌ Metal GPU not available: %v\n", err)
		return
	}
	defer provider.Cleanup()

	devices, err := provider.GetDevices()
	if err != nil {
		fmt.Printf("❌ Error getting Metal devices: %v\n", err)
		return
	}

	if len(devices) == 0 {
		fmt.Println("❌ No Metal devices found")
		return
	}

	for i, device := range devices {
		fmt.Printf("\nDevice %d:\n", i+1)
		fmt.Printf("  Name: %s\n", device.GetName())
		fmt.Printf("  Platform: %s\n", device.GetPlatform().String())
		fmt.Printf("  Available: %t\n", device.IsAvailable())
		fmt.Printf("  Memory: %d MB\n", device.GetMemorySize()/(1024*1024))
		fmt.Printf("  Compute Capability: %s\n", device.GetComputeCapability())
	}
}
