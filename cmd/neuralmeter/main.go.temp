package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"neuralmetergo/internal/api"
	"neuralmetergo/internal/config"
	"neuralmetergo/internal/engine"
	"neuralmetergo/internal/gpu"
	"neuralmetergo/internal/parser"
	"neuralmetergo/internal/validation"
	"neuralmetergo/internal/worker"

	"github.com/sevlyar/go-daemon"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"gopkg.in/yaml.v3"
)

var (
	cfgFile             string
	verbose             bool
	quiet               bool
	logLevel            string
	outputFormat        string
	serverPort          int
	daemonMode          bool
	forceAction         bool
	pidFile             string
	masterNodes         []string
	enableDaemonLogging bool
)

var rootCmd = &cobra.Command{
	Use:     "neuralmeter",
	Short:   "NeuralMeter - Advanced Load Testing Tool",
	Long:    `NeuralMeter is a high-performance load testing tool...`,
	Version: "1.0.0",
}

var runCmd = &cobra.Command{
	Use:   "run <test-plan.yaml>",
	Short: "Execute load tests from a test plan",
	Args:  cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		return runTests(args[0])
	},
}

var validateCmd = &cobra.Command{
	Use:   "validate <test-plan.yaml>",
	Short: "Validate a test plan configuration",
	Args:  cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		return validateTestPlan(args[0])
	},
}

var configCmd = &cobra.Command{
	Use:   "config",
	Short: "Display current configuration",
	RunE: func(cmd *cobra.Command, args []string) error {
		return showConfig()
	},
}

var versionCmd = &cobra.Command{
	Use: "version",
	Short: "Show version information",
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Printf("NeuralMeter v%s\n", rootCmd.Version)
	},
}

var metricsCmd = &cobra.Command{
	Use:   "metrics",
	Short: "View and export metrics",
	RunE: func(cmd *cobra.Command, args []string) error {
		fmt.Println("Metrics functionality is planned for future releases.")
		return nil
	},
}

var serverCmd = &cobra.Command{
	Use:   "server",
	Short: "Server daemon management commands",
}

var serverStartCmd = &cobra.Command{
	Use:   "start",
	Short: "Start the NeuralMeter server daemon",
	RunE: func(cmd *cobra.Command, args []string) error {
		return startServerDaemon()
	},
}

var serverStopCmd = &cobra.Command{
	Use:   "stop",
	Short: "Stop the NeuralMeter server daemon",
	RunE: func(cmd *cobra.Command, args []string) error {
		return stopServerDaemon()
	},
}

var serverStatusCmd = &cobra.Command{
	Use:   "status",
	Short: "Show the NeuralMeter server status",
	Run:   showServerStatus,
}

var serverRestartCmd = &cobra.Command{
	Use:   "restart",
	Short: "Restart the NeuralMeter server daemon",
	RunE: func(cmd *cobra.Command, args []string) error {
		return restartServerDaemon()
	},
}

func main() {
	cobra.OnInitialize(initConfig)

	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is ./neuralmeter.yaml)")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "enable verbose output")
	rootCmd.PersistentFlags().BoolVarP(&quiet, "quiet", "q", false, "enable quiet mode")
	rootCmd.PersistentFlags().StringVar(&logLevel, "log-level", "info", "log level (debug, info, warn, error)")

	runCmd.Flags().StringVarP(&outputFormat, "output-format", "o", "text", "output format (text, json, yaml)")
	configCmd.Flags().StringVarP(&outputFormat, "output-format", "o", "text", "output format (text, json, yaml)")
	validateCmd.Flags().BoolVarP(&verbose, "verbose", "v", false, "enable verbose validation output")

	serverStartCmd.Flags().IntVarP(&serverPort, "port", "p", 8080, "server port")
	serverStartCmd.Flags().BoolVarP(&daemonMode, "daemon", "d", true, "run in daemon mode")
	serverStartCmd.Flags().StringVar(&pidFile, "pid-file", "/var/run/neuralmeter.pid", "PID file location")
	serverStartCmd.Flags().StringSliceVar(&masterNodes, "masters", []string{}, "master node addresses")
	serverStartCmd.Flags().BoolVar(&enableDaemonLogging, "enable-daemon-log", false, "Enable daemon logging to file")

	serverStopCmd.Flags().BoolVarP(&forceAction, "force", "f", false, "force stop without graceful shutdown")
	serverStopCmd.Flags().StringVar(&pidFile, "pid-file", "/var/run/neuralmeter.pid", "PID file location")

	serverStatusCmd.Flags().StringVarP(&outputFormat, "output-format", "o", "text", "output format (text, json, yaml)")

	serverCmd.AddCommand(serverStartCmd, serverStopCmd, serverStatusCmd, serverRestartCmd)
	rootCmd.AddCommand(runCmd, validateCmd, configCmd, versionCmd, metricsCmd, serverCmd, initializeGPUCommands())

	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func initConfig() {
	if cfgFile != "" {
		viper.SetConfigFile(cfgFile)
	} else {
		home, err := os.UserHomeDir()
		cobra.CheckErr(err)
		viper.AddConfigPath(home)
		viper.AddConfigPath(".")
		viper.SetConfigName("neuralmeter")
	}

	viper.AutomaticEnv()
	viper.SetEnvPrefix("NEURALMETER")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			log.Fatalf("Error reading config file: %s", err)
		}
	}

	setConfigDefaults()
}

func setConfigDefaults() {
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("global.log_level", "info")
	// Add other defaults from config package
}

func configureLogging() {
	// Basic logging configuration
	log.SetFlags(log.LstdFlags | log.Lshortfile)
}

func runTests(testPlanFile string) error {
	cfg, err := loadConfiguration()
	if err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	if cfg.Global.Debug {
		log.Println("Debug mode enabled")
	}
	configureLogging()

	testPlan, err := parser.ParseTestPlan(testPlanFile)
	if err != nil {
		return fmt.Errorf("failed to parse test plan: %w", err)
	}

	validationEngine, err := validation.NewEngine(cfg)
	if err != nil {
		return fmt.Errorf("failed to create validation engine: %w", err)
	}

	if err := validationEngine.Validate(testPlan); err != nil {
		return fmt.Errorf("test plan validation failed: %w", err)
	}

	coordinator, err := engine.NewCoordinator(cfg, testPlan)
	if err != nil {
		return fmt.Errorf("failed to create test coordinator: %w", err)
	}

	results, err := coordinator.Run()
	if err != nil {
		return fmt.Errorf("test execution failed: %w", err)
	}

	return displayResults(results)
}

func validateTestPlan(testPlanFile string) error {
	cfg, err := loadConfiguration()
	if err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	if cfg.Global.Debug {
		log.Println("Debug mode enabled")
	}
	configureLogging()

	testPlan, err := parser.ParseTestPlan(testPlanFile)
	if err != nil {
		return fmt.Errorf("failed to parse test plan: %w", err)
	}

	validationEngine, err := validation.NewEngine(cfg)
	if err != nil {
		return fmt.Errorf("failed to create validation engine: %w", err)
	}

	if err := validationEngine.Validate(testPlan); err != nil {
		return fmt.Errorf("test plan validation failed: %w", err)
	}

	fmt.Println("Test plan validation successful.")
	return nil
}

func showConfig() error {
	cfg, err := loadConfiguration()
	if err != nil {
		return err
	}
	return displayResults(cfg)
}

func loadConfiguration() (*config.Config, error) {
	manager, err := config.NewConfigManager(cfgFile)
	if err != nil {
		return nil, fmt.Errorf("failed to create config manager: %w", err)
	}

	if err := manager.LoadWithMerging(); err != nil {
		return nil, fmt.Errorf("failed to load configuration: %w", err)
	}

	return manager.Get(), nil
}

func displayResults(results interface{}) error {
	var err error
	switch outputFormat {
	case "json":
		err = outputJSON(results)
	case "yaml":
		err = outputYAML(results)
	default:
		err = outputText(results)
	}
	return err
}

func outputConfigText(cfg *config.Config) error {
	fmt.Println("NeuralMeter Configuration:")
	fmt.Println("==========================")
	fmt.Printf("Server Host: %s\n", cfg.Server.Host)
	fmt.Printf("Server Port: %d\n", cfg.Server.Port)
	fmt.Printf("Log Level: %s\n", cfg.Global.LogLevel)
	fmt.Printf("GPU Enabled: %t\n", cfg.GPU.Enabled)
	fmt.Printf("Worker Pool Size: %d\n", cfg.Worker.PoolSize)
	fmt.Println("==========================")
	return nil
}

func outputConfigJSON(cfg *config.Config) error {
	data, err := json.MarshalIndent(cfg, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config to JSON: %w", err)
	}
	fmt.Println(string(data))
	return nil
}

func outputConfigYAML(cfg *config.Config) error {
	data, err := yaml.Marshal(cfg)
	if err != nil {
		return fmt.Errorf("failed to marshal config to YAML: %w", err)
	}
	fmt.Println(string(data))
	return nil
}

func outputText(results interface{}) error {
	// Generic text output
	fmt.Printf("%+v\n", results)
	return nil
}

func outputJSON(results interface{}) error {
	data, err := json.MarshalIndent(results, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal results to JSON: %w", err)
	}
	fmt.Println(string(data))
	return nil
}

func outputYAML(results interface{}) error {
	data, err := yaml.Marshal(results)
	if err != nil {
		return fmt.Errorf("failed to marshal results to YAML: %w", err)
	}
	fmt.Println(string(data))
	return nil
}

func initializeGPUCommands() *cobra.Command {
	gpuCmd := &cobra.Command{
		Use:   "gpu",
		Short: "GPU management and monitoring commands",
	}

	listCmd := &cobra.Command{
		Use:   "list",
		Short: "List available GPUs",
		RunE: func(cmd *cobra.Command, args []string) error {
			gpuManager := gpu.NewManager(gpu.DefaultGPUConfig(), log.Default())
			gpus, err := gpuManager.GetAvailableGPUs()
			if err != nil {
				return fmt.Errorf("failed to list GPUs: %w", err)
			}
			if len(gpus) == 0 {
				fmt.Println("No GPUs detected.")
				return nil
			}
			return displayResults(gpus)
		},
	}

	gpuCmd.AddCommand(listCmd)
	return gpuCmd
}

func startServerDaemon() error {
	if daemonMode {
		logPath := "/var/log/neuralmeter_daemon.log"
		if !enableDaemonLogging {
			logPath = "/dev/null"
		}

		cntxt := &daemon.Context{
			PidFileName: pidFile,
			PidFilePerm: 0644,
			LogFileName: logPath,
			LogFilePerm: 0640,
			WorkDir:     "./",
			Umask:       027,
		}

		d, err := cntxt.Reborn()
		if err != nil {
			log.Fatalf("Failed to reborn daemon: %v", err)
		}
		if d != nil {
			return nil
		}
		defer cntxt.Release()

		log.Println("Daemon started")
	}

	cfg, err := loadConfiguration()
	if err != nil {
		log.Fatalf("Failed to load configuration in daemon: %v", err)
	}

	if err := runServerForeground(cfg); err != nil {
		log.Fatalf("Server error: %v", err)
	}
	return nil
}

func stopServerDaemon() error {
	pid, err := readPIDFile()
	if err != nil {
		return fmt.Errorf("failed to read PID file: %w", err)
	}

	process, err := os.FindProcess(pid)
	if err != nil {
		return fmt.Errorf("failed to find process: %w", err)
	}

	if forceAction {
		return process.Kill()
	}
	return process.Signal(syscall.SIGTERM)
}

func showServerStatus(cmd *cobra.Command, args []string) {
	running, process, pid, uptime, err := isServerRunning()
	if err != nil {
		log.Printf("Error checking server status: %v", err)
		fmt.Println("Server Status: Error")
		return
	}

	if running {
		fmt.Println("Server Status: Running")
		fmt.Printf("PID: %d\n", pid)
		fmt.Printf("Process: %v\n", process)
		fmt.Printf("Uptime: %v\n", uptime)
	} else {
		fmt.Println("Server Status: Not Running")
	}
}

func restartServerDaemon() error {
	if err := stopServerDaemon(); err != nil {
		log.Printf("Failed to stop server during restart, proceeding to start: %v", err)
	}
	time.Sleep(2 * time.Second) // Give time for socket to be released
	return startServerDaemon()
}

func isServerRunning() (bool, *os.Process, int, time.Duration, error) {
	pid, err := readPIDFile()
	if err != nil {
		if os.IsNotExist(err) {
			return false, nil, 0, 0, nil
		}
		return false, nil, 0, 0, fmt.Errorf("failed to read PID file: %w", err)
	}

	process, err := os.FindProcess(pid)
	if err != nil {
		return false, nil, 0, 0, nil
	}

	err = process.Signal(syscall.Signal(0))
	if err == nil {
		// Cannot reliably get uptime here without more complex os-specific code.
		return true, process, pid, 0, nil
	}

	if os.Is(err, os.ErrProcessDone) {
		removePIDFile()
		return false, nil, 0, 0, nil
	}

	return false, nil, 0, 0, fmt.Errorf("error checking process status: %w", err)
}

func readPIDFile() (int, error) {
	pidData, err := ioutil.ReadFile(pidFile)
	if err != nil {
		return 0, err
	}
	return strconv.Atoi(strings.TrimSpace(string(pidData)))
}

func writePIDFile() error {
	return ioutil.WriteFile(pidFile, []byte(fmt.Sprintf("%d", os.Getpid())), 0644)
}

func removePIDFile() error {
	return os.Remove(pidFile)
}

func runServerForeground(cfg *config.Config) error {
	if err := writePIDFile(); err != nil {
		return fmt.Errorf("failed to write PID file: %w", err)
	}
	defer removePIDFile()

	log.Printf("Starting server on port %d", cfg.Server.Port)

	gpuManager := gpu.NewManager(cfg.GPU, log.Default())
	_, err := gpuManager.GetAvailableGPUs()
	if err != nil {
		log.Printf("Warning: could not initialize GPUs: %v", err)
	}

	jobQueue := worker.NewJobQueue(cfg.Worker.QueueSize)
	poolConfig := worker.PoolConfig{
		MinWorkers: cfg.Worker.PoolSize,
		MaxWorkers: cfg.Worker.PoolSize,
		AutoScale:  false,
	}
	workerPool := worker.NewWorkerPoolWithConfig(poolConfig, jobQueue)
	if err := workerPool.Start(); err != nil {
		return fmt.Errorf("failed to start worker pool: %w", err)
	}
	defer workerPool.Stop()

	configManager, _ := config.NewConfigManager(cfgFile)
	mux := http.NewServeMux()
	configAPI := api.NewConfigurationAPI(configManager)
	configAPI.RegisterRoutes(mux)

	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      mux,
		ReadTimeout:  cfg.Server.ReadTimeout.ToDuration(),
		WriteTimeout: cfg.Server.WriteTimeout.ToDuration(),
		IdleTimeout:  cfg.Server.IdleTimeout.ToDuration(),
	}

	stop := make(chan os.Signal, 1)
	signal.Notify(stop, os.Interrupt, syscall.SIGTERM)

	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Could not listen on %s: %v\n", server.Addr, err)
		}
	}()
	log.Println("Server is ready to handle requests")

	<-stop

	log.Println("Shutting down server...")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server shutdown failed: %v", err)
	}

	log.Println("Server gracefully stopped")

	return nil
} 