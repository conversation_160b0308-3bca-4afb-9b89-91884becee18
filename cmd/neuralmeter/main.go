package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"syscall"
	"time"

	"neuralmetergo/internal/api"
	appconfig "neuralmetergo/internal/config"
	"neuralmetergo/internal/engine"
	"neuralmetergo/internal/gpu"
	appconfig "neuralmetergo/internal/config"
	"neuralmetergo/internal/parser"
	"neuralmetergo/internal/validation"
	"os/exec"

	"github.com/sevlyar/go-daemon"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"gopkg.in/yaml.v3"
)

var (
	cfgFile      string
	verbose      bool
	quiet        bool
	logLevel     string
	outputFormat string

	// Run command specific flags
	url           string
	requests      int
	concurrency   int
	duration      time.Duration
	method        string
	headers       []string
	body          string
	timeout       time.Duration
	rate          float64
	config        string
	streamResults string // WebSocket endpoint for streaming results

	// Server-specific flags
	serverPort          int
	daemonMode          bool
	forceAction         bool
	pidFile             string
	masterNodes         []string
	enableDaemonLogging bool // New flag for daemon logging
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "neuralmeter",
	Short: "NeuralMeter - Advanced Load Testing Tool",
	Long: `NeuralMeter is a high-performance load testing tool designed for modern applications.
It provides comprehensive testing capabilities with advanced metrics collection,
GPU acceleration support, and intelligent workload distribution.

Features:
  • High-performance HTTP load testing
  • Advanced metrics collection and aggregation
  • GPU acceleration for enhanced performance
  • Intelligent worker pool management
  • Comprehensive result analysis and reporting
  • Multi-format configuration support (YAML, JSON)`,
	Version: "1.0.0",
}

// runCmd represents the run command
var runCmd = &cobra.Command{
	Use:   "run <test-plan.yaml>",
	Short: "Execute load tests from a test plan",
	Long: `Execute load tests using the specified test plan configuration.
The test plan should be a YAML or JSON file containing test scenarios,
configuration settings, and execution parameters.

Examples:
  neuralmeter run test-plan.yaml
  neuralmeter run --config custom-config.yaml test-plan.yaml
  neuralmeter run --verbose --output-format json test-plan.yaml`,
	Args: cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		return runTests(args[0])
	},
}

// validateCmd represents the validate command
var validateCmd = &cobra.Command{
	Use:   "validate <test-plan.yaml>",
	Short: "Validate a test plan configuration",
	Long: `Validate the syntax and structure of a test plan configuration file.
This command checks for configuration errors, validates dependencies,
and ensures the test plan is ready for execution.

Examples:
  neuralmeter validate test-plan.yaml
  neuralmeter validate --verbose test-plan.yaml`,
	Args: cobra.ExactArgs(1),
	RunE: func(cmd *cobra.Command, args []string) error {
		return validateTestPlan(args[0])
	},
}

// configCmd represents the config command
var configCmd = &cobra.Command{
	Use:   "config",
	Short: "Display current configuration",
	Long: `Display the current NeuralMeter configuration including all settings
from configuration files, environment variables, and command-line flags.

Examples:
  neuralmeter config
  neuralmeter config --output-format json
  neuralmeter config --output-format yaml`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return showConfig()
	},
}

// versionCmd represents the version command
var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Show version information",
	Long:  `Display NeuralMeter version information and build details.`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Printf("NeuralMeter v%s\n", rootCmd.Version)
		fmt.Println("High-performance load testing tool")

		// Detect CUDA version for display; silently ignore errors.
		if cudaVer, err := getCUDAVersion(); err == nil && cudaVer != "" {
			fmt.Printf("CUDA Runtime: %s\n", cudaVer)
		}

		fmt.Println("Built with Go")
	},
}

// metricsCmd represents the metrics command (placeholder for future functionality)
var metricsCmd = &cobra.Command{
	Use:   "metrics",
	Short: "View and export metrics",
	Long: `View collected metrics and export them in various formats.
This command provides access to performance metrics, statistics,
and analysis results from previous test runs.

Note: This feature is planned for future releases.`,
	RunE: func(cmd *cobra.Command, args []string) error {
		fmt.Println("Metrics functionality is planned for future releases.")
		fmt.Println("Current test runs will display metrics at completion.")
		return nil
	},
}

// serverCmd represents the server command group
var serverCmd = &cobra.Command{
	Use:   "server",
	Short: "Server daemon management commands",
	Long: `Manage the NeuralMeter server daemon for receiving and executing
remote test plans from controller nodes.

Available subcommands:
  start   - Start the server daemon
  stop    - Stop the server daemon  
  status  - Check daemon status
  restart - Restart the server daemon`,
}

// serverStartCmd represents the server start command
var serverStartCmd = &cobra.Command{
	Use:   "start",
	Short: "Start the NeuralMeter server daemon",
	Long: `Start the NeuralMeter server daemon in the background to accept
remote test plans and job submissions from controller nodes.

The daemon will:
  • Listen for HTTP/gRPC connections from controllers
  • Accept and queue test plan execution requests
  • Execute tests using local GPU resources
  • Stream results back to controllers
  • Register with master nodes for workload distribution

Examples:
  neuralmeter server start
  neuralmeter server start --port 8080
  neuralmeter server start --daemon
  neuralmeter server start --config custom-server.yaml`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return startServerDaemon()
	},
}

// serverStopCmd represents the server stop command
var serverStopCmd = &cobra.Command{
	Use:   "stop",
	Short: "Stop the NeuralMeter server daemon",
	Long: `Stop the running NeuralMeter server daemon gracefully.

This will:
  • Complete any running tests
  • Close connections to controllers
  • Clean up resources and temp files
  • Remove PID file

Examples:
  neuralmeter server stop
  neuralmeter server stop --force`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return stopServerDaemon()
	},
}

// serverStatusCmd represents the server status command
var serverStatusCmd = &cobra.Command{
	Use:   "status",
	Short: "Show the NeuralMeter server status",
	Long:  `Checks if the NeuralMeter server daemon is running and displays its status.`,
	Run:   showServerStatus,
}

// serverRestartCmd represents the server restart command
var serverRestartCmd = &cobra.Command{
	Use:   "restart",
	Short: "Restart the NeuralMeter server daemon",
	Long: `Restart the NeuralMeter server daemon by stopping and starting it.

This is equivalent to running 'neuralmeter server stop' followed by
'neuralmeter server start' with the same configuration.

Examples:
  neuralmeter server restart
  neuralmeter server restart --force`,
	RunE: func(cmd *cobra.Command, args []string) error {
		return restartServerDaemon()
	},
}

func main() {
	cobra.OnInitialize(initConfig)

	// Add global flags
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is ./neuralmeter.yaml)")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "enable verbose output")
	rootCmd.PersistentFlags().BoolVarP(&quiet, "quiet", "q", false, "enable quiet mode")
	rootCmd.PersistentFlags().StringVar(&logLevel, "log-level", "info", "log level (debug, info, warn, error)")

	// Add output format flag to relevant commands
	runCmd.Flags().StringVarP(&outputFormat, "output-format", "o", "text", "output format (text, json, yaml)")
	runCmd.Flags().StringVar(&url, "url", "", "Target URL for load testing")
	runCmd.Flags().IntVar(&requests, "requests", 0, "Total number of requests to make")
	runCmd.Flags().IntVar(&concurrency, "concurrency", 10, "Number of concurrent workers")
	runCmd.Flags().DurationVar(&duration, "duration", 0, "Duration of the test (e.g., 30s, 5m)")
	runCmd.Flags().StringVar(&method, "method", "GET", "HTTP method to use (GET, POST, etc.)")
	runCmd.Flags().StringSliceVar(&headers, "headers", []string{}, "Custom HTTP headers (e.g., 'Authorization: Bearer token')")
	runCmd.Flags().StringVar(&body, "body", "", "Request body data")
	runCmd.Flags().DurationVar(&timeout, "timeout", 30*time.Second, "Request timeout duration")
	runCmd.Flags().Float64Var(&rate, "rate", 0, "Requests per second limit")
	runCmd.Flags().StringVar(&config, "config", "", "YAML configuration file for test parameters")
	runCmd.Flags().StringVar(&streamResults, "stream-results", "", "WebSocket endpoint for streaming real-time results (e.g., ws://localhost:8080/ws)")
	configCmd.Flags().StringVarP(&outputFormat, "output-format", "o", "text", "output format (text, json, yaml)")
	validateCmd.Flags().BoolVarP(&verbose, "verbose", "v", false, "enable verbose validation output")

	// Add server command flags
	serverStartCmd.Flags().IntVarP(&serverPort, "port", "p", 8080, "server port")
	serverStartCmd.Flags().BoolVarP(&daemonMode, "daemon", "d", true, "run in daemon mode")
	serverStartCmd.Flags().StringVar(&pidFile, "pid-file", "./neuralmeter.pid", "PID file location")
	serverStartCmd.Flags().StringSliceVar(&masterNodes, "masters", []string{}, "master node addresses")
	serverStartCmd.Flags().BoolVar(&enableDaemonLogging, "enable-daemon-log", false, "Enable daemon logging to file")

	// Add force flag to server stop and restart commands
	serverStopCmd.Flags().BoolVarP(&forceAction, "force", "f", false, "force action without graceful shutdown")
	// Allow custom PID file path for stop command to match start
	serverStopCmd.Flags().StringVar(&pidFile, "pid-file", "./neuralmeter.pid", "PID file location")
	serverRestartCmd.Flags().BoolVarP(&forceAction, "force", "f", false, "force action without graceful shutdown")
	// Allow custom PID file path for restart command as well
	serverRestartCmd.Flags().StringVar(&pidFile, "pid-file", "./neuralmeter.pid", "PID file location")

	// Add commands to root
	rootCmd.AddCommand(runCmd)
	rootCmd.AddCommand(validateCmd)
	rootCmd.AddCommand(configCmd)
	rootCmd.AddCommand(versionCmd)
	rootCmd.AddCommand(metricsCmd)
	rootCmd.AddCommand(serverCmd)

	// Add server subcommands
	serverCmd.AddCommand(serverStartCmd)
	serverCmd.AddCommand(serverStopCmd)
	serverCmd.AddCommand(serverStatusCmd)
	serverCmd.AddCommand(serverRestartCmd)

	// Add GPU commands
	rootCmd.AddCommand(initializeGPUCommands())

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func initConfig() {
	if cfgFile != "" {
		// Use config file from the flag.
		viper.SetConfigFile(cfgFile)
	} else {
		// Search config in current directory
		viper.AddConfigPath(".")
		viper.SetConfigName("neuralmeter")
		viper.SetConfigType("yaml") // Allow YAML or JSON
	}

	viper.AutomaticEnv() // read in environment variables that match

	// Set default values before reading config
	setConfigDefaults()

	if err := viper.ReadInConfig(); err == nil {
		if verbose {
			fmt.Printf("Using config file: %s\n", viper.ConfigFileUsed())
		}
	} else {
		// If config file not found, that's okay, we'll use defaults and env vars
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			if verbose {
				fmt.Println("No config file found, using defaults and environment variables.")
			}
		} else {
			fmt.Printf("Error reading config file: %v\n", err)
		}
	}

	// Configure logging after config is loaded
	configureLogging()

	// Fail fast if CUDA runtime < 11.8 (test-plan requirement)
	if err := checkCUDAVersion("11.8"); err != nil {
		log.Fatalf("CUDA version check failed: %v", err)
	}
}

func setConfigDefaults() {
	// Server defaults
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.read_timeout", "10s")
	viper.SetDefault("server.write_timeout", "10s")
	viper.SetDefault("server.idle_timeout", "120s")
	viper.SetDefault("server.tls.enabled", false)
	viper.SetDefault("server.tls.cert_file", "")
	viper.SetDefault("server.tls.key_file", "")

	// Load test defaults
	viper.SetDefault("load_test.default_duration", "60s")
	viper.SetDefault("load_test.default_concurrency", 10)
	viper.SetDefault("load_test.max_concurrency", 1000)
	viper.SetDefault("load_test.ramp_up_duration", "0s")
	viper.SetDefault("load_test.ramp_down_duration", "0s")

	// Metrics defaults
	viper.SetDefault("metrics.enabled", true)
	viper.SetDefault("metrics.collection_interval", "1s")
	viper.SetDefault("metrics.buffer_size", 1000)
	viper.SetDefault("metrics.retention_period", "24h")
	viper.SetDefault("metrics.export_interval", "5s")

	// Output defaults
	viper.SetDefault("output.format", "text")
	viper.SetDefault("output.file", "")
	viper.SetDefault("output.console", true)
	viper.SetDefault("output.verbose", false)
	viper.SetDefault("output.templates", map[string]string{})
	viper.SetDefault("output.compression", false)

	// Dashboard defaults
	viper.SetDefault("dashboard.enabled", false)
	viper.SetDefault("dashboard.host", "127.0.0.1")
	viper.SetDefault("dashboard.port", 9000)
	viper.SetDefault("dashboard.refresh_rate", 2) // seconds
	viper.SetDefault("dashboard.history_limit", 1000)

	// GPU defaults (ensure detection enabled by default)
	viper.SetDefault("gpu.enabled", true)
	viper.SetDefault("gpu.prefer_cuda", true)
	viper.SetDefault("gpu.min_memory_gb", 2.0)
	viper.SetDefault("gpu.min_compute_capability.major", 3)
	viper.SetDefault("gpu.min_compute_capability.minor", 5)
	viper.SetDefault("gpu.max_memory_utilization", 90.0)
	viper.SetDefault("gpu.device_id", -1)
	viper.SetDefault("gpu.monitoring_interval", "5s")

	// Worker defaults
	viper.SetDefault("worker.pool_size", 100)
	viper.SetDefault("worker.queue_size", 10000)
	viper.SetDefault("worker.max_retries", 3)
	viper.SetDefault("worker.retry_delay", "100ms")
	viper.SetDefault("worker.shutdown_timeout", "5s")

	// GPU defaults
	viper.SetDefault("gpu.enabled", false)
	viper.SetDefault("gpu.prefer_cuda", true)
	viper.SetDefault("gpu.min_memory_gb", 2.0)
	viper.SetDefault("gpu.min_compute_capability.major", 3)
	viper.SetDefault("gpu.min_compute_capability.minor", 5)
	viper.SetDefault("gpu.max_memory_utilization", 90.0)
	viper.SetDefault("gpu.device_id", -1)
	viper.SetDefault("gpu.monitoring_interval", "5s")
	viper.SetDefault("gpu.allow_fallback", false)
	viper.SetDefault("gpu.profile_mode", "disabled")
	viper.SetDefault("gpu.log_gpu_events", false)
	viper.SetDefault("gpu.resource_limits.max_gpu_utilization", 95.0)
	viper.SetDefault("gpu.resource_limits.max_memory_usage", 95.0)
	viper.SetDefault("gpu.resource_limits.max_power_consumption", 250.0)
	viper.SetDefault("gpu.resource_limits.throttle_on_high_memory", true)
	viper.SetDefault("gpu.resource_limits.throttle_on_power_limit", true)

	// Global defaults
	viper.SetDefault("global.log_level", "info")
	viper.SetDefault("global.config_dir", "./config")
	viper.SetDefault("global.data_dir", "./data")
	viper.SetDefault("global.temp_dir", os.TempDir())
	viper.SetDefault("global.environment", "development")
	viper.SetDefault("global.debug", false)
	viper.SetDefault("global.profiles_enabled", false)
	viper.SetDefault("global.variables", map[string]string{})
}

func configureLogging() {
	if quiet {
		log.SetOutput(ioutil.Discard)
		return
	}

	// Set log level based on config
	switch strings.ToLower(logLevel) {
	case "debug":
		log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
	case "info":
		log.SetFlags(log.Ldate | log.Ltime)
	case "warn", "error":
		log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
	default:
		log.SetFlags(log.Ldate | log.Ltime)
		log.Println("Invalid log level, defaulting to info.")
	}
}

func runTests(testPlanFile string) error {
	// Load configuration
	appConfigManager, appConfig, err := loadConfiguration()
	if err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	if appConfig.Global.Debug {
		log.Printf("Running tests with debug enabled.")
	}

	// Validate configuration first
	if err := appConfigManager.Validate(); err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}

	// Parse the test plan if provided, otherwise use CLI flags
	var testPlan *parser.TestPlan
	if testPlanFile != "" {
		testPlanParser := parser.NewParser()
		testPlan, err = testPlanParser.ParseFile(testPlanFile)
		if err != nil {
			return fmt.Errorf("failed to parse test plan '%s': %w", testPlanFile, err)
		}
	} else {
		// Create a test plan from CLI flags
		testPlan = &parser.TestPlan{
			Name: "CLI Test",
			Scenarios: []parser.Scenario{
				{
					Name: "Default Scenario",
					Requests: []parser.Request{
						{
							URL:     url,
							Method:  method,
							Body:    body,
							Headers: make(map[string]string),
						},
					},
				},
			},
		}

		// Parse headers from CLI flag
		for _, header := range headers {
			parts := strings.SplitN(header, ":", 2)
			if len(parts) == 2 {
				testPlan.Scenarios[0].Requests[0].Headers[strings.TrimSpace(parts[0])] = strings.TrimSpace(parts[1])
			}
		}
	}

	// Initialize the validation engine
	validationEngine := validation.NewValidationEngine()

	// Validate the test plan using the validation engine
	validationResult := validationEngine.ValidateTestPlan(testPlan)
	if !validationResult.Valid {
		for _, issue := range validationResult.Issues {
			if issue.Severity == validation.SeverityError {
				log.Printf("Validation Error: %s (%s) at %s", issue.Message, issue.Rule, issue.Field)
			}
		}
		for _, issue := range validationResult.Issues {
			if issue.Severity == validation.SeverityWarning {
				log.Printf("Validation Warning: %s (%s) at %s", issue.Message, issue.Rule, issue.Field)
			}
		}
		return fmt.Errorf("test plan validation failed: %d errors, %d warnings", validationResult.Summary.ErrorCount, validationResult.Summary.WarningCount)
	} else if validationResult.Summary.WarningCount > 0 {
		for _, issue := range validationResult.Issues {
			if issue.Severity == validation.SeverityWarning {
				log.Printf("Validation Warning: %s (%s) at %s", issue.Message, issue.Rule, issue.Field)
			}
		}
		log.Printf("Test plan '%s' validated successfully with %d warnings. Proceeding with execution.", testPlan.Name, validationResult.Summary.WarningCount)
	} else {
		log.Printf("Test plan '%s' validated successfully.", testPlan.Name)
	}

	// Initialize the execution engine
	engineConfig := engine.DefaultEngineConfig()
	// Populate engineConfig.GPUConfig from appConfig.GPU
	if appConfig.GPU.Enabled {
	if appConfig.GPU.Enabled {
		engineConfig.GPUConfig = &gpu.GPUConfig{
			Enabled:     appConfig.GPU.Enabled,
			PreferCUDA:  appConfig.GPU.PreferCUDA,
			MinMemoryGB: appConfig.GPU.MinMemoryGB,
			MinComputeCapability: gpu.ComputeCapability{
				Major: appConfig.GPU.MinComputeCapability.Major,
				Minor: appConfig.GPU.MinComputeCapability.Minor,
			},
			MaxMemoryUtilization: appConfig.GPU.MaxMemoryUtilization,
			DeviceID:             appConfig.GPU.DeviceID,
			MonitoringInterval:   appConfig.GPU.MonitoringInterval.ToDuration(),
			ResourceLimits:        appConfig.GPU.ResourceLimits,
			PerformanceThresholds: appConfig.GPU.PerformanceThresholds,
			AllowFallback:         appConfig.GPU.AllowFallback,
			ProfileMode:           appConfig.GPU.ProfileMode,
			LogGPUEvents:          appConfig.GPU.LogGPUEvents,
		}
	} else {
		engineConfig.GPUConfig = nil
	}

	executionEngine := engine.NewExecutionEngine(testPlan, engineConfig)

	// Initialize the engine
	if err := executionEngine.Initialize(); err != nil {
		return fmt.Errorf("failed to initialize execution engine: %w", err)
	}

	// Create a context for the engine to run in
	ctx, cancel := context.WithCancel(context.Background())
	_ = ctx
	defer cancel()

	// Handle graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		log.Println("Received shutdown signal. Stopping engine...")
		cancel()
		executionEngine.Stop()
	}()

	// Start the engine
	log.Printf("Starting execution of test plan: %s", testPlan.Name)
	if err := executionEngine.Start(); err != nil {
		return fmt.Errorf("execution engine failed to start or encountered an error: %w", err)
	}

	log.Printf("Test plan execution completed.")

	// Get and display final results
	finalMetrics := executionEngine.GetMetrics()
	log.Printf("Total Requests: %d, Succeeded: %d, Failed: %d, Avg Latency: %s, Throughput: %.2f req/s",
		finalMetrics.RequestsExecuted, finalMetrics.RequestsSucceeded, finalMetrics.RequestsFailed,
		finalMetrics.AverageLatency, finalMetrics.Throughput)

	return nil
}

func validateTestPlan(testPlanFile string) error {
	appConfigManager, appConfig, err := loadConfiguration()
	if err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	if appConfig.Global.Debug {
		log.Printf("Validating test plan with debug enabled.")
	}

	if err := appConfigManager.Validate(); err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}

	testPlanParser := parser.NewParser()
	testPlan, err := testPlanParser.ParseFile(testPlanFile)
	if err != nil {
		return fmt.Errorf("failed to parse test plan '%s': %w", testPlanFile, err)
	}

	validationEngine := validation.NewValidationEngine() // Corrected: takes no arguments

	validationResult := validationEngine.ValidateTestPlan(testPlan)
	if !validationResult.Valid {
		for _, issue := range validationResult.Issues { // Corrected: iterate through Issues
			if issue.Severity == validation.SeverityError {
				log.Printf("Validation Error: %s (%s) at %s", issue.Message, issue.Rule, issue.Field)
			}
		}
		for _, issue := range validationResult.Issues { // Corrected: iterate through Issues
			if issue.Severity == validation.SeverityWarning {
				log.Printf("Validation Warning: %s (%s) at %s", issue.Message, issue.Rule, issue.Field)
			}
		}
		return fmt.Errorf("test plan validation failed: %d errors, %d warnings", validationResult.Summary.ErrorCount, validationResult.Summary.WarningCount) // Corrected: access via Summary
	} else if validationResult.Summary.WarningCount > 0 { // Corrected: access via Summary
		for _, issue := range validationResult.Issues { // Corrected: iterate through Issues
			if issue.Severity == validation.SeverityWarning {
				log.Printf("Validation Warning: %s (%s) at %s", issue.Message, issue.Rule, issue.Field)
			}
		}
		log.Printf("Test plan '%s' validated successfully with %d warnings. It is ready for execution, but consider addressing warnings.", testPlan.Name, validationResult.Summary.WarningCount) // Corrected: access via Summary
	} else {
		log.Printf("Test plan '%s' validated successfully. No errors or warnings found.", testPlan.Name)
	}

	return nil
}

func showConfig() error {
	_, appConfig, err := loadConfiguration()
	if err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	return displayResults(appConfig)
}

func loadConfiguration() (*appconfig.ConfigManager, *appconfig.Config, error) {
	var cm *appconfig.ConfigManager
	var err error

	// If user didn’t pass --config and no default file exists, fall back to env-only config.
	if cfgFile == "" {
		cm, err = appconfig.NewConfigManagerFromEnvironment()
		if err != nil {
			return nil, nil, fmt.Errorf("failed to build config from environment: %w", err)
		}
		return cm, cm.Get(), nil
	}

	cm, err = appconfig.NewConfigManager(cfgFile)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create config manager: %w", err)
	}

	if err := cm.LoadWithEnvironmentPrecedence(); err != nil {
		return nil, nil, fmt.Errorf("failed to load configuration: %w", err)
	}

	return cm, cm.Get(), nil
}

func displayResults(results interface{}) error {
	switch outputFormat {
	case "json":
		return outputJSON(results)
	case "yaml":
		return outputYAML(results)
	case "text":
		// For text output, we specifically handle Config type
		if cfg, ok := results.(*appconfig.Config); ok {
			return outputConfigText(cfg)
		}
		return outputText(results)
	default:
		return fmt.Errorf("unsupported output format: %s", outputFormat)
	}
}

func outputConfigText(cfg *appconfig.Config) error {
	// A simple text output for configuration. Can be expanded for more detail.
	fmt.Println("--- NeuralMeter Configuration ---")
	fmt.Printf("Server: %s:%d (Read: %s, Write: %s)\n", cfg.Server.Host, cfg.Server.Port, cfg.Server.ReadTimeout.String(), cfg.Server.WriteTimeout.String())
	fmt.Printf("Load Test: Concurrency %d (Max: %d), Duration %s\n", cfg.LoadTest.DefaultConcurrency, cfg.LoadTest.MaxConcurrency, cfg.LoadTest.DefaultDuration.String())
	fmt.Printf("Metrics: Enabled %t, Interval %s\n", cfg.Metrics.Enabled, cfg.Metrics.CollectionInterval.String())
	fmt.Printf("GPU: Enabled %t, Prefer CUDA %t, Min Memory %f GB\n", cfg.GPU.Enabled, cfg.GPU.PreferCUDA, cfg.GPU.MinMemoryGB)
	fmt.Println("---------------------------------")
	return nil
}

func outputConfigJSON(cfg *appconfig.Config) error {
	data, err := json.MarshalIndent(cfg, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config to JSON: %w", err)
	}
	fmt.Println(string(data))
	return nil
}

func outputConfigYAML(cfg *appconfig.Config) error {
	data, err := yaml.Marshal(cfg)
	if err != nil {
		return fmt.Errorf("failed to marshal config to YAML: %w", err)
	}
	fmt.Println(string(data))
	return nil
}

func outputText(results interface{}) error {
	fmt.Printf("%+v\n", results)
	return nil
}

func outputJSON(results interface{}) error {
	data, err := json.MarshalIndent(results, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal results to JSON: %w", err)
	}
	fmt.Println(string(data))
	return nil
}

func outputYAML(results interface{}) error {
	data, err := yaml.Marshal(results)
	if err != nil {
		return fmt.Errorf("failed to marshal results to YAML: %w", err)
	}
	fmt.Println(string(data))
	return nil
}

// readPIDFile reads the PID from the PID file
func readPIDFile() (int, error) {
	data, err := ioutil.ReadFile(pidFile)
	if err != nil {
		return 0, fmt.Errorf("failed to read PID file: %w", err)
	}
	pid, err := strconv.Atoi(strings.TrimSpace(string(data)))
	if err != nil {
		return 0, fmt.Errorf("invalid PID in file: %w", err)
	}
	return pid, nil
}

// writePIDFile writes the current process PID to the PID file
func writePIDFile() error {
	if err := os.MkdirAll(filepath.Dir(pidFile), 0755); err != nil {
		return fmt.Errorf("failed to create PID file directory: %w", err)
	}
	return ioutil.WriteFile(pidFile, []byte(fmt.Sprintf("%d", os.Getpid())), 0644)
}

// removePIDFile removes the PID file
func removePIDFile() error {
	return os.Remove(pidFile)
}

func initializeGPUCommands() *cobra.Command {
	gpuCmd := &cobra.Command{
		Use:   "gpu",
		Short: "Manage GPU operations",
		Long: `Commands for GPU management, including device listing and status.

Examples:
  neuralmeter gpu list
  neuralmeter gpu status`,
	}

	gpuCmd.AddCommand(&cobra.Command{
		Use:   "list",
		Short: "List available GPU devices",
		Long:  `Lists all detected GPU devices and their properties.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			appConfigManager, appConfig, err := loadConfiguration() // Corrected type
			if err != nil {
				return fmt.Errorf("failed to load configuration: %w", err)
			}
			if err := appConfigManager.Validate(); err != nil {
				return fmt.Errorf("configuration validation failed: %w", err)
			}

			// Create gpu.GPUConfig from appConfig.GPU for gpu.NewManager
			gpuConfig := gpu.GPUConfig{
				Enabled:     appConfig.GPU.Enabled,
				PreferCUDA:  appConfig.GPU.PreferCUDA,
				MinMemoryGB: appConfig.GPU.MinMemoryGB,
				MinComputeCapability: gpu.ComputeCapability{
					Major: appConfig.GPU.MinComputeCapability.Major,
					Minor: appConfig.GPU.MinComputeCapability.Minor,
				},
				MaxMemoryUtilization: appConfig.GPU.MaxMemoryUtilization,
				DeviceID:             appConfig.GPU.DeviceID,
				MonitoringInterval:   appConfig.GPU.MonitoringInterval.ToDuration(),
			}

			gpuManager := gpu.NewManager(gpuConfig, log.Default()) // Corrected: gpu.NewManager now takes gpu.GPUConfig
			gpus, err := gpuManager.GetAvailableGPUs()
			if err != nil {
				return fmt.Errorf("failed to get available GPUs: %w", err)
			}
			if len(gpus) == 0 {
				fmt.Println("No compatible GPU devices found.")
				return nil
			}

			fmt.Println("Available GPU Devices:")
			for i, g := range gpus {
				fmt.Printf("  Device %d:\n", i)
				fmt.Printf("    Name: %s\n", g.Name)
				if g.Vendor != "" {
					fmt.Printf("    Vendor: %s\n", g.Vendor)
				}
				if g.Architecture != "" {
					fmt.Printf("    Type: %s  (Arch: %s)\n", g.Type, g.Architecture)
				} else {
					fmt.Printf("    Type: %s\n", g.Type)
				}

				// --- Memory ---
				totalGB := float64(g.TotalMemory) / (1024 * 1024 * 1024)
				freeGB := float64(g.FreeMemory) / (1024 * 1024 * 1024)
				util := g.MemoryUtilization()
				fmt.Printf("    Memory: %.1f GB total / %.1f GB free (%.0f %% util)\n", totalGB, freeGB, util)

				// --- Compute / cores ---
				if g.Type == gpu.GPUTypeCUDA {
					fmt.Printf("    Compute Capability: %d.%d – %d SMs\n", g.ComputeCapability.Major, g.ComputeCapability.Minor, g.MultiProcessorCount)
				}

				// --- Bus & clocks ---
				if g.MemoryBusWidth > 0 {
					fmt.Printf("    Memory Bus: %d-bit", g.MemoryBusWidth)
					if g.MemoryClockRate > 0 {
						fmt.Printf(" @ %.0f MHz", float64(g.MemoryClockRate)/1000)
					}
					fmt.Println()
				}
				if g.ClockRate > 0 {
					fmt.Printf("    Core Clock: %.0f MHz\n", float64(g.ClockRate)/1000)
				}

				// --- Power & utilisation ---
				if g.PowerUsage > 0 {
					fmt.Printf("    Power Usage: %.2f W\n", g.PowerUsage)
				}
				if g.Utilization > 0 {
					fmt.Printf("    GPU Utilisation: %.0f %%\n", g.Utilization)
				}

				fmt.Printf("    Available: %t\n", g.Available)
				fmt.Println()
			}
			return nil
		},
	})

	gpuCmd.AddCommand(&cobra.Command{
		Use:   "status <device-id>",
		Short: "Show status of a specific GPU device",
		Long:  `Displays real-time metrics and status for a specified GPU device.`,
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			deviceID, err := strconv.Atoi(args[0])
			if err != nil {
				return fmt.Errorf("invalid device ID: %w", err)
			}

			appConfigManager, appConfig, err := loadConfiguration() // Corrected type
			if err != nil {
				return fmt.Errorf("failed to load configuration: %w", err)
			}
			if err := appConfigManager.Validate(); err != nil {
				return fmt.Errorf("configuration validation failed: %w", err)
			}

			// Create gpu.GPUConfig from appConfig.GPU for gpu.NewManager
			gpuConfig := gpu.GPUConfig{
				Enabled:     appConfig.GPU.Enabled,
				PreferCUDA:  appConfig.GPU.PreferCUDA,
				MinMemoryGB: appConfig.GPU.MinMemoryGB,
				MinComputeCapability: gpu.ComputeCapability{
					Major: appConfig.GPU.MinComputeCapability.Major,
					Minor: appConfig.GPU.MinComputeCapability.Minor,
				},
				MaxMemoryUtilization: appConfig.GPU.MaxMemoryUtilization,
				DeviceID:             appConfig.GPU.DeviceID,
				MonitoringInterval:   appConfig.GPU.MonitoringInterval.ToDuration(),
			}

			gpuManager := gpu.NewManager(gpuConfig, log.Default()) // Corrected: gpu.NewManager now takes gpu.GPUConfig

			// Check if the device is available
			if !gpuManager.IsGPUAvailable(deviceID) {
				return fmt.Errorf("GPU device %d is not available or not compatible", deviceID)
			}

			metrics, err := gpuManager.GetCurrentMetrics(deviceID) // Corrected: GetDeviceStatus to GetCurrentMetrics
			if err != nil {
				return fmt.Errorf("failed to get GPU metrics for device %d: %w", deviceID, err)
			}

			fmt.Printf("GPU Device %d Status:\n", deviceID)
			fmt.Printf("  Timestamp: %s\n", metrics.Timestamp.Format(time.RFC3339))
			fmt.Printf("  GPU Utilization: %.2f %%\n", metrics.GPUUtilization)
			fmt.Printf("  Memory Utilization: %.2f %%\n", metrics.MemoryUtilization)
			fmt.Printf("  Power Consumption: %.2f W\n", metrics.PowerConsumption)
			fmt.Printf("  Clock Speed: %d MHz\n", metrics.ClockSpeed)
			fmt.Printf("  Memory Clock Speed: %d MHz\n", metrics.MemoryClockSpeed)
			fmt.Printf("  Fan Speed: %d RPM\n", metrics.FanSpeed)

			return nil
		},
	})
	return gpuCmd
}

func startServerDaemon() error {
	// If the user requested foreground mode, skip daemonisation entirely.
	if !daemonMode {
		fmt.Println("Starting NeuralMeter server in foreground mode …")

		manager, cfg, err := loadConfiguration()
		if err != nil {
			return fmt.Errorf("failed to load configuration: %w", err)
		}

		// runServerForeground is blocking; return any error directly.
		return runServerForeground(manager, cfg)
	}

	// Daemon mode (default) – fork into background and write PID file.
	cntxt := &daemon.Context{
		PidFileName: pidFile,
		LogFileName: "/tmp/neuralmeter_daemon.log", // default log file, overridden below
		LogFilePerm: 0644,
		WorkDir:     "./",
		Umask:       027,
		Args:        os.Args,
	}

	if enableDaemonLogging {
		cntxt.LogFileName = "/var/log/neuralmeter_daemon.log"
	}

	d, err := cntxt.Reborn()
	if err != nil {
		return fmt.Errorf("failed to start daemon: %w", err)
	}
	if d != nil {
		// Parent process exits after successful fork.
		fmt.Printf("NeuralMeter server daemon started with PID %d\n", d.Pid)
		return nil
	}
	defer cntxt.Release()

	fmt.Println("NeuralMeter server daemon running …")

	// Child (daemon) continues here.
	manager, cfg, err := loadConfiguration()
	if err != nil {
		log.Fatalf("Failed to load configuration for daemon: %v", err)
	}

	if err := runServerForeground(manager, cfg); err != nil {
		log.Fatalf("Server terminated with error: %v", err)
	}
	return nil
}

func stopServerDaemon() error {
	pid, err := readPIDFile()
	if err != nil {
		if os.IsNotExist(err) {
			fmt.Println("NeuralMeter server daemon is not running (PID file not found).")
			return nil
		}
		return fmt.Errorf("failed to read PID file: %w", err)
	}

	proc, err := os.FindProcess(pid)
	if err != nil {
		return fmt.Errorf("failed to find process with PID %d: %w", pid, err)
	}

	// Attempt graceful shutdown first
	if !forceAction {
		fmt.Printf("Attempting graceful shutdown of NeuralMeter server (PID: %d)...\n", pid)
		// Send TERM signal for graceful shutdown
		if err := proc.Signal(syscall.SIGTERM); err != nil {
			fmt.Printf("Warning: Failed to send SIGTERM to PID %d: %v. Attempting to force kill.\n", pid, err)
			// Fallback to force kill if graceful fails
			if err := proc.Signal(syscall.SIGKILL); err != nil {
				return fmt.Errorf("failed to force kill PID %d: %w", pid, err)
			}
		}

		// Wait for the process to exit
		done := make(chan struct{})
		go func() {
			_, _ = proc.Wait() // Wait for process to exit
			close(done)
		}()

		select {
		case <-time.After(10 * time.Second): // Wait up to 10 seconds for graceful shutdown
			fmt.Printf("Graceful shutdown timed out for PID %d. Forcing kill...\n", pid)
			if err := proc.Signal(syscall.SIGKILL); err != nil {
				return fmt.Errorf("failed to force kill PID %d after timeout: %w", pid, err)
			}
			<-done // Wait for it to actually exit after force kill
		case <-done:
			fmt.Println("NeuralMeter server daemon stopped gracefully.")
		}
	} else {
		// Force kill
		fmt.Printf("Forcing stop of NeuralMeter server (PID: %d)...\n", pid)
		if err := proc.Signal(syscall.SIGKILL); err != nil {
			return fmt.Errorf("failed to force kill PID %d: %w", pid, err)
		}
		// Wait briefly for process to terminate
		time.Sleep(1 * time.Second)
		fmt.Println("NeuralMeter server daemon forced stopped.")
	}

	// Always attempt to remove PID file
	if err := removePIDFile(); err != nil {
		fmt.Printf("Warning: Failed to remove PID file '%s': %v\n", pidFile, err)
	}

	return nil
}

func showServerStatus(cmd *cobra.Command, args []string) {
	isRunning, _, pid, uptime, err := isServerRunning() // Corrected: discard unused 'process' variable
	if err != nil {
		if os.IsNotExist(err) {
			fmt.Println("NeuralMeter server daemon is not running (PID file not found).")
		} else {
			fmt.Printf("Error checking server status: %v\n", err)
		}
		return
	}

	if isRunning {
		fmt.Printf("NeuralMeter server daemon is running with PID %d\n", pid)
		fmt.Printf("Uptime: %s\n", uptime.Round(time.Second).String())
	} else {
		fmt.Println("NeuralMeter server daemon is not running.")
	}
}

// Add PID-file flag to status command for consistency
func init() {
	serverStatusCmd.Flags().StringVar(&pidFile, "pid-file", "./neuralmeter.pid", "PID file location")
}

func restartServerDaemon() error {
	if err := stopServerDaemon(); err != nil {
		fmt.Printf("Error stopping server daemon: %v\n", err)
		// Decide whether to proceed with start or return
		if !forceAction {
			return err
		}
		fmt.Println("Attempting to start anyway due to --force flag...")
	} else {
		fmt.Println("Server daemon stopped. Starting new instance...")
	}

	// Small delay to ensure resources are released
	time.Sleep(2 * time.Second)

	return startServerDaemon()
}

// isServerRunning checks if the daemon is running by PID file and process existence
func isServerRunning() (bool, *os.Process, int, time.Duration, error) {
	pid, err := readPIDFile()
	if err != nil {
		return false, nil, 0, 0, err // PID file not found or unreadable
	}

	proc, err := os.FindProcess(pid)
	if err != nil {
		// Process not found implies it's not running or PID is stale
		_ = removePIDFile() // Clean up stale PID file
		return false, nil, 0, 0, fmt.Errorf("process with PID %d not found: %w", pid, err)
	}

	// Check if the process is actually running and is our process
	// Send a signal 0 to check existence without killing
	if err := proc.Signal(syscall.Signal(0)); err != nil {
		_ = removePIDFile() // Clean up stale PID file
		return false, nil, 0, 0, fmt.Errorf("process with PID %d is not running: %w", pid, err)
	}

	// Get process start time to calculate uptime
	startTime, err := getProcessStartTime(pid)
	if err != nil {
		return true, proc, pid, 0, fmt.Errorf("failed to get process start time for PID %d: %w", pid, err)
	}
	uptime := time.Since(startTime)

	return true, proc, pid, uptime, nil
}

// getProcessStartTime attempts to get the start time of a process by PID
// This is OS-specific. For Linux, it reads /proc/<pid>/stat
func getProcessStartTime(pid int) (time.Time, error) {
	// For Linux, read /proc/<pid>/stat
	statPath := fmt.Sprintf("/proc/%d/stat", pid)
	data, err := ioutil.ReadFile(statPath)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to read %s: %w", statPath, err)
	}

	// The stat file format is complex, but we need the 22nd field (start time)
	// which is in jiffies since system boot.
	// We also need the system uptime and jiffies per second (USER_HZ) from /proc/stat
	fields := strings.Fields(string(data))
	if len(fields) < 22 {
		return time.Time{}, fmt.Errorf("unexpected /proc/%d/stat format", pid)
	}

	startTimeJiffies, err := strconv.ParseInt(fields[21], 10, 64) // 0-indexed, so 22nd field is index 21
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse start time jiffies: %w", err)
	}

	// Read system uptime from /proc/uptime
	uptimeData, err := ioutil.ReadFile("/proc/uptime")
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to read /proc/uptime: %w", err)
	}
	uptimeFields := strings.Fields(string(uptimeData))
	if len(uptimeFields) < 1 {
		return time.Time{}, fmt.Errorf("unexpected /proc/uptime format")
	}
	systemUptimeSeconds, err := strconv.ParseFloat(uptimeFields[0], 64)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse system uptime: %w", err)
	}

	// Get jiffies per second (USER_HZ)
	// On most systems, this is 100 or 1000. It's usually a compile-time constant.
	// For simplicity, assume 100. For robustness, one might need sysconf(_SC_CLK_TCK)
	// For Linux, USER_HZ is typically 100.
	jiffiesPerSecond := float64(100) // Common value for USER_HZ on Linux

	// Calculate system boot time
	systemBootTime := time.Now().Add(-time.Duration(systemUptimeSeconds) * time.Second)

	// Calculate process start time
	// Process start time = System Boot Time + (startTimeJiffies / jiffiesPerSecond) seconds
	processStartTime := systemBootTime.Add(time.Duration(float64(startTimeJiffies)/jiffiesPerSecond) * time.Second)

	return processStartTime, nil
}

func runServerForeground(manager *appconfig.ConfigManager, cfg *appconfig.Config) error { // Corrected type: config.ConfigManager
	// ----- GPU availability check (always attempt detection) -----
	gpuCfg := &gpu.GPUConfig{
		Enabled:              true, // detect regardless of user flag; this flag only controls engine later
		PreferCUDA:           cfg.GPU.PreferCUDA,
		MinMemoryGB:          cfg.GPU.MinMemoryGB,
		MinComputeCapability: gpu.ComputeCapability{Major: cfg.GPU.MinComputeCapability.Major, Minor: cfg.GPU.MinComputeCapability.Minor},
		MaxMemoryUtilization: cfg.GPU.MaxMemoryUtilization,
		DeviceID:             cfg.GPU.DeviceID,
		MonitoringInterval:   cfg.GPU.MonitoringInterval.ToDuration(),
	}

	gpuManager := gpu.NewManager(gpuCfg, log.Default())
	gpus, err := gpuManager.GetAvailableGPUs()
	if err != nil {
		log.Printf("GPU detection error: %v", err)
	}

	if len(gpus) == 0 {
		if cfg.GPU.Enabled {
			return fmt.Errorf("no compatible GPU detected – NeuralMeter requires a CUDA, ROCm, Metal, or OpenCL GPU")
		}
		log.Println("No compatible GPU detected; proceeding in CPU-only mode")
	} else {
		if cfg.GPU.Enabled {
			log.Printf("GPU detection successful – %d device(s) available", len(gpus))
		} else {
			log.Printf("GPU detected (%d device(s)) but GPU acceleration is disabled via configuration; proceeding in CPU-only mode", len(gpus))
		}
	}

	log.Printf("Starting NeuralMeter server on %s:%d (TLS: %t)...", cfg.Server.Host, cfg.Server.Port, cfg.Server.TLS.Enabled)

	// Setup API server
	apiConfig := api.ServerConfig{
		Host:         cfg.Server.Host,
		Port:         cfg.Server.Port,
		ReadTimeout:  cfg.Server.ReadTimeout.ToDuration(),
		WriteTimeout: cfg.Server.WriteTimeout.ToDuration(),
		IdleTimeout:  cfg.Server.IdleTimeout.ToDuration(),
		TLS: api.TLSConfig{
			Enabled:  cfg.Server.TLS.Enabled,
			CertFile: cfg.Server.TLS.CertFile,
			KeyFile:  cfg.Server.TLS.KeyFile,
		},
	}

	// Initialize the execution engine for the server to use
	engineConfig := engine.DefaultEngineConfig()
	// Populate engineConfig.GPUConfig from cfg.GPU
	if cfg.GPU.Enabled {
		engineConfig.GPUConfig = &gpu.GPUConfig{
			Enabled:     cfg.GPU.Enabled,
			PreferCUDA:  cfg.GPU.PreferCUDA,
			MinMemoryGB: cfg.GPU.MinMemoryGB,
			MinComputeCapability: gpu.ComputeCapability{
				Major: cfg.GPU.MinComputeCapability.Major,
				Minor: cfg.GPU.MinComputeCapability.Minor,
			},
			MaxMemoryUtilization: cfg.GPU.MaxMemoryUtilization,
			DeviceID:             cfg.GPU.DeviceID,
			MonitoringInterval:   cfg.GPU.MonitoringInterval.ToDuration(),
		}
	} else {
		engineConfig.GPUConfig = nil // Explicitly set to nil if GPU is disabled
	}

	// Create execution engine without a test plan for now; it will be fully
	// initialised later when a controller submits a plan.
	serverEngine := engine.NewExecutionEngine(nil, engineConfig)

	apiServer := api.NewServer(apiConfig, serverEngine) // Pass the engine to the API server

	// Context for server shutdown
	ctx, cancel := context.WithCancel(context.Background())
	_ = ctx // ctx currently unused; retained for future extension
	defer cancel()

	// Handle OS signals for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		log.Println("Received shutdown signal. Shutting down server...")
		apiServer.Shutdown(ctx)
		cancel() // Signal context cancellation
	}()

	// Start the server
	if err := apiServer.Start(); err != nil && err != http.ErrServerClosed {
		return fmt.Errorf("server failed to start: %w", err)
	}

	log.Println("Server gracefully stopped.")
	return nil
}

// checkCUDAVersion ensures the system CUDA runtime is at least minVersion.
// It tries the structured query first and gracefully falls back
// to parsing the plain nvidia-smi banner when the flag is unsupported.
// If the CUDA version cannot be detected the function logs a warning and
// returns nil so the CLI can still run (useful for CI containers).
func checkCUDAVersion(minVersion string) error {
	// --- primary method: structured query (CUDA 10.1+ drivers) ---
	out, err := exec.Command("nvidia-smi",
		"--query-gpu=cuda_version",
		"--format=csv,noheader").Output()

	var versionStr string
	if err == nil {
		versionStr = strings.TrimSpace(string(out))
	} else {
		// --- fallback: parse plain nvidia-smi banner ---
		fallbackOut, err2 := exec.Command("nvidia-smi").Output()
		if err2 != nil {
			log.Printf("WARNING: cannot determine CUDA version (nvidia-smi failed: %v); continuing without strict check", err2)
			return nil
		}
		re := regexp.MustCompile(`CUDA Version:\s*([0-9]+\.[0-9]+)`) // e.g. "CUDA Version: 12.2"
		match := re.FindStringSubmatch(string(fallbackOut))
		if len(match) < 2 {
			log.Printf("WARNING: could not parse CUDA version from nvidia-smi output; continuing without strict check")
			return nil
		}
		versionStr = match[1]
	}

	if versionStr == "" {
		// Nothing to compare – allow execution but warn.
		log.Printf("WARNING: CUDA version could not be detected; skipping version check")
		return nil
	}

	if compareVersions(versionStr, minVersion) < 0 {
		return fmt.Errorf("CUDA %s found, but ≥ %s required", versionStr, minVersion)
	}
	return nil
}

// compareVersions returns -1, 0, +1 for a vs b (semantic “major.minor”).
func compareVersions(a, b string) int {
	parse := func(s string) (major, minor int) {
		parts := strings.SplitN(s, ".", 3)
		if len(parts) > 0 {
			major, _ = strconv.Atoi(parts[0])
		}
		if len(parts) > 1 {
			minor, _ = strconv.Atoi(parts[1])
		}
		return
	}
	amj, ami := parse(a)
	bmj, bmi := parse(b)
	switch {
	case amj < bmj:
		return -1
	case amj > bmj:
		return 1
	case ami < bmi:
		return -1
	case ami > bmi:
		return 1
	default:
		return 0
	}
}

// getCUDAVersion returns the CUDA runtime version reported by nvidia-smi or
// an empty string if it cannot be determined.
func getCUDAVersion() (string, error) {
	// Try structured query first.
	out, err := exec.Command("nvidia-smi", "--query-gpu=cuda_version", "--format=csv,noheader").Output()
	if err == nil {
		v := strings.TrimSpace(string(out))
		if v != "" {
			return v, nil
		}
	}

	// Fallback: plain nvidia-smi header parsing.
	headerOut, err2 := exec.Command("nvidia-smi").Output()
	if err2 != nil {
		return "", err2
	}
	re := regexp.MustCompile(`CUDA Version:\s*([0-9]+\.[0-9]+)`) // e.g. "CUDA Version: 12.2"
	match := re.FindStringSubmatch(string(headerOut))
	if len(match) >= 2 {
		return match[1], nil
	}
	return "", fmt.Errorf("CUDA version not found")
}
