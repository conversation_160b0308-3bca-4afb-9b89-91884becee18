NeuralMeter Windows Edition is a high-performance load testing tool designed 
specifically for Windows environments with DirectML GPU acceleration support.

This tool provides comprehensive HTTP/API load testing capabilities with:
- DirectML GPU acceleration for enhanced performance
- Advanced metrics collection and analysis
- Real-time monitoring and reporting
- Flexible test plan configuration
- Windows-optimized performance

Usage:
  neuralmeter-windows [command]

Available Commands:
  completion  Generate the autocompletion script for the specified shell
  config      Configuration management
  help        Help about any command
  run         Execute a load test plan
  validate    Validate a test plan configuration
  version     Show version information

Flags:
      --config string      config file (default is ./neuralmeter.yaml)
  -h, --help               help for neuralmeter-windows
      --log-level string   log level (debug, info, warn, error) (default "info")
      --output string      output format (text, json) (default "text")
  -q, --quiet              quiet output
  -v, --verbose            verbose output
      --version            version for neuralmeter-windows

Use "neuralmeter-windows [command] --help" for more information about a command.
