############################################################
# NeuralMeter CLI - Architecture Documentation
############################################################


### 1. Introduction and Project Overview

The NeuralMeter CLI is a high-performance, command-line interface tool built in Go, designed primarily for **Linux-based performance load testing**. Its core purpose is to act as a robust, headless load generation engine capable of simulating diverse network traffic and AI workloads against target services. **Crucially, due to its specialized nature, including CUDA GPU acceleration and Linux-specific functionalities, all building, execution, and comprehensive testing of the CLI must be performed on a remote Linux machine.**

While the project initially included cross-platform UI components and had a broader scope, the current strategic focus is explicitly on developing and perfecting the **Linux CLI engine**. This ensures a solid foundation for high-performance, automated testing and CI/CD integration.

Future iterations may introduce separate UI clients (for macOS, Windows, and Linux) that communicate with this core CLI engine, but all load generation and core logic will reside within the CLI engine itself.

## Remote Execution Environment

A critical architectural constraint for the NeuralMotion Linux CLI Worker is its exclusive execution on a remote Linux machine. This is necessitated by specific hardware and operating system dependencies required for GPU-accelerated operations and performance load testing that cannot be met by local development environments. All build, testing, and operational activities related to the CLI must be performed on a remote Linux system with appropriate hardware and drivers.

For detailed information on deployment requirements, build and test automation, connection methods, and cross-platform considerations, please refer to the [Deployment Architecture](deployment.md) document.


### 2. High-Level Architecture

The NeuralMeter CLI operates as a self-contained application, executing performance tests and managing resources directly from the command line. It interacts with target systems (System B) to generate load and collect metrics.

**System Architecture Overview:**

- **System A**: Remote Linux machine running the NeuralMeter CLI application
- **System B**: Target services being load tested by the CLI
- **Jenkins**: CI/CD orchestrator for building and deploying the CLI to System A

Here's a conceptual overview:

    +---------------------------+
    | SYSTEM A                  |
    | NEURALMETER CLI (LINUX)   |
    |                           |
    | - Command-line Interface  |
    | - Configuration Loader    |
    | - Test Execution Engine   |
    | - HTTP/WebSocket Client   |
    | - GPU Integration (CUDA)  |
    | - Metrics Collection      |
    | - Result Export           |
    +----------|----------------+
               | Network Traffic
               v
    +---------------------------+
    | SYSTEM B (Target Services)|
    |                           |
    | - Nginx                   |
    | - Backend HTTP Services   |
    | - WebSocket Server        |
    +---------------------------+

**System Roles:**
- **System A (192.168.1.155)**: Remote Linux machine with GPU capabilities that runs the compiled NeuralMeter CLI application
- **System B (192.168.1.177)**: Target services (web servers, APIs, etc.) that the CLI load tests against
- **Jenkins (192.168.1.183)**: CI/CD system that builds the CLI and deploys it to System A

**Deployment Flow:**
1. Jenkins builds the NeuralMeter CLI from source code
2. Jenkins deploys the compiled binary to System A
3. System A runs the CLI application with GPU acceleration
4. System A generates load against System B (target services)
5. System A collects metrics and generates test reports

In future phases, a UI layer would interact with the CLI engine via an API (e.g., REST/gRPC), orchestrating tests and retrieving results. However, the CLI engine remains the primary processing unit for load generation.


### 3. Execution Flow (A CLI Command Lifecycle)

This section describes the typical flow of execution when a user invokes a command via the NeuralMeter CLI. This outlines how a request is processed from the command line through the internal components.

1.  **Command Invocation:**
    - The user executes a command (e.g., `neuralmeter run --config test-plan.yaml`).
    - The `cmd/neuralmeter/` package, utilizing a CLI framework (like Cobra), parses the command, arguments, and flags.

2.  **Configuration Loading and Validation:**
    - The `config/` package loads the specified configuration file (`test-plan.yaml`) and applies any command-line overrides.
    - The configuration is validated against defined schemas to ensure correctness and completeness using the `validation/` package.

3.  **Engine Initialization:**
    - The `engine/` package is initialized, which serves as the central orchestrator for the test run.
    - Depending on the test plan, the `gpu/` package is initialized to detect available GPUs and prepare resources (e.g., memory pools, CUDA contexts).
    - The `metrics/` package sets up collectors and aggregators for real-time performance data.

4.  **Worker Pool and Load Generation:**
    - The `engine/` creates and manages a pool of `worker/` routines (goroutines).
    - Each `worker/` is responsible for executing test scenarios defined in the configuration.
    - The `client/` package handles the actual HTTP/WebSocket requests to System B (target services), applying retry policies and error reporting.
    - For AI-powered workloads, the `gpu/` package generates payloads (e.g., LLM prompts, embeddings) and potentially performs model inference.

5.  **Real-time Metrics Collection and Streaming:**
    - As workers execute requests, performance data (RPS, latencies, error rates, GPU utilization) is collected by the `metrics/` package.
    - This data is aggregated in real-time and can be streamed to the console or exported via configured interfaces (e.g., Prometheus, JSON file).

6.  **Result Processing and Reporting:**
    - Upon completion of the test, the `results/` package processes the raw metrics.
    - It performs data grouping and aggregation to generate final reports in various formats (JSON, CSV, HTML).

7.  **Resource Cleanup:**
    - All allocated resources (e.g., GPU memory, network connections) are properly released.


### 4. Configuration Management

Configuration is a critical aspect of the NeuralMeter CLI, dictating test parameters, deployment modes, and resource allocation. The `config/` package is central to this.

- **YAML/JSON as Primary Format:**
    - Test plans and application settings are primarily defined in YAML or JSON files, making them human-readable, version-controllable, and easily integrated into CI/CD pipelines.

- **Layered Configuration:**
    - Configuration can be sourced from multiple layers:
        1.  Default values (embedded in code).
        2.  Configuration files (e.g., `neuralmeter.yaml`, custom test plans).
        3.  Environment variables (for sensitive data or environment-specific overrides).
        4.  Command-line flags (highest precedence, for ad-hoc overrides).

- **Validation:**
    - The `validation/` package provides schema validation for configuration files, ensuring that all required fields are present and correctly formatted.

- **Dynamic Updates (Future Consideration):**
    - While not a primary focus for the initial Linux CLI engine, future iterations (especially with daemon mode and APIs) might explore hot-reloading or dynamic updates of certain configuration parameters.


### 5. Testing Strategy

A robust testing strategy is essential for ensuring the reliability and performance of the NeuralMeter CLI. The `test/` directory organizes all testing efforts.

- **Unit Tests:**
    - Located alongside the code they test (`_test.go` files).
    - Focus on isolated functions, methods, and small logical units.
    - Utilize Go's `testing` package and table-driven tests for comprehensive coverage.
    - Mocking is minimized, favoring testing against real interfaces.

- **Integration Tests:**
    - Reside in `test/integration/`.
    - Verify interactions between multiple components or with external dependencies (e.g., actual HTTP/WebSocket servers, GPU drivers).
    - Often involve setting up minimal test environments.

- **Benchmarks:**
    - Located in `test/benchmarks/`.
    - Measure the performance of critical code paths (e.g., request generation rate, data processing).
    - Used to track performance regressions and optimize bottlenecks.

- **End-to-End (E2E) Tests:**
    - Simulating full CLI command execution against a complete System B setup (as described in `md_docs/test_approach_documentation.md`).
    - These are the highest-level tests, verifying the entire system functionality from user command to result output.

- **Test Coverage:**
    - Tools like `go test -cover` are used to monitor test coverage, aiming for high coverage in critical business logic and core components.


### 6. Observability (Metrics, Logging, Tracing)

Observability is paramount for understanding the behavior and performance of the NeuralMeter CLI, especially in headless and distributed environments. The project integrates OpenTelemetry for this purpose.

- **Metrics (via `metrics/` package):**
    - Collects key performance indicators (RPS, latency, error rates, CPU/GPU utilization, memory usage, connection counts).
    - Aggregates data over time windows and provides percentiles.
    - Exports metrics to various formats (e.g., Prometheus for scraping, JSON/CSV for file output).
    - Utilizes `otel.Meter` (or similar Go-idiomatic metric libraries) for instrumentation.

- **Structured Logging:**
    - Uses a structured logging library (e.g., zerolog, zap) to output machine-readable logs.
    - Logs include contextual information (e.g., trace IDs, test IDs, worker IDs) to facilitate debugging.
    - Log levels are used to control verbosity (INFO, DEBUG, WARN, ERROR).

- **Distributed Tracing:**
    - Employs OpenTelemetry for distributed tracing.
    - Spans are created for significant operations (e.g., command execution, request processing, GPU calls).
    - Trace IDs are propagated across internal components and potentially to external services to correlate related operations.
    - Utilizes `otel.Tracer` for creating spans and managing context.


### 7. BMAD v4 Architectural Components

The following documents provide sharded details of the NeuralMeter CLI architecture, as per BMAD v4 standards. These files are automatically referenced by the Dev Agent for focused insights.

- **Coding Standards and Design Principles:**
    - See: `docs/architecture/coding-standards.md`

- **Technology Stack and Dependencies:**
    - See: `docs/architecture/tech-stack.md`

- **Project Source Tree and Internal Package Analysis:**
    - See: `docs/architecture/source-tree.md`

--- 