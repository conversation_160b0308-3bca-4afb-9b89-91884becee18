### 1. Technology Stack and Dependencies

The NeuralMeter CLI leverages a curated set of external Go modules, prioritizing stability, performance, and alignment with Go idioms. The `go.mod` file explicitly lists these dependencies.

- **CLI Framework:**
    - Typically Cobra for building robust and user-friendly command-line interfaces.

- **Configuration Parsing:**
    - Libraries for YAML/JSON parsing and unmarshaling (e.g., `gopkg.in/yaml.v3`, `encoding/json`).

- **Logging:**
    - A structured logging library (e.g., `github.com/rs/zerolog`).

- **Observability:**
    - OpenTelemetry SDK and API (e.g., `go.opentelemetry.io/otel`).

- **HTTP/Network:**
    - Standard library `net/http` for HTTP clients and servers.
    - Libraries for WebSocket clients/servers are evaluated as needed, but the primary focus is on Go's native capabilities for efficient network communication.
    - **Authentication Support**: Includes Basic, Bearer (token-based), and Custom Header authentication for target services.
    - **Supported Protocols for Load Generation**: Primarily HTTP/1.1 and HTTP/2, with WebSocket and gRPC for specialized workloads.

- **GPU Interaction:**
    - CUDA bindings for Go (e.g., `github.com/vladimirvivien/go-nvml`, `github.com/rai-project/go-nvml` or similar low-level bindings).

- **Utilities:**
    - Various small utility libraries as needed, always evaluated for necessity and impact.

### 2. Development Environment

- **Primary Development Machine**: Local IDE (e.g., Cursor) for code writing and editing.
- **Build and Execution Environment**: All code compilation, testing, and execution of the NeuralMeter CLI **must occur on a remote Linux machine**.
    - This is due to specific hardware requirements (e.g., CUDA-enabled GPUs) and operating system dependencies.
    - The remote machine serves as the primary development and testing environment for running the `neuralmeter` binary. 