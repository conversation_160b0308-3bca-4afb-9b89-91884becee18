# Deployment Architecture

This section outlines the deployment strategy for the NeuralMotion Linux CLI Worker, focusing on the requirements for remote Linux environments, build and test automation, and connection methods.

## 1. Remote Linux Deployment Requirements

The NeuralMotion Linux CLI Worker is designed for deployment on a remote Linux machine. This is a critical architectural constraint due to hardware and operating system dependencies required for GPU-accelerated operations and performance load testing. Key requirements include:

*   **Operating System:** Linux distributions (e.g., Ubuntu, CentOS, Debian) that support the necessary Go runtime and GPU drivers (NVIDIA CUDA, AMD ROCm, Intel OpenCL).
*   **Hardware:** Access to a Linux machine with compatible GPU hardware for effective performance load testing and deep learning model execution.
*   **Networking:** Stable network connectivity between development/CI/CD environments and the remote Linux deployment target.
*   **Security:** Secure access mechanisms and appropriate firewall rules to protect the deployment environment.
*   **Credential Management:** Infrastructure credentials (SSH keys, API tokens, etc.) and environment-specific details like IP addresses will be securely managed and injected into the CI/CD pipelines (e.g., Jenkins). Direct storage of credentials in version control or local files is strictly avoided for automated deployments.

## 2. Build and Test Automation Needs

To ensure efficient and reliable deployments, robust build and test automation are essential. This includes:

*   **Automated Builds:** A continuous integration (CI) system capable of compiling the Go CLI application for Linux targets. This involves fetching dependencies, cross-compiling if necessary (though the primary focus is Linux-native builds), and packaging the executable.
*   **Automated Testing:** Integration of unit, integration, and performance tests into the CI/CD pipeline. These tests should be executable on the remote Linux environment to validate functionality and performance against real hardware conditions.
*   **Artifact Management:** Secure storage and versioning of built artifacts (e.g., executable binaries) for easy retrieval and deployment.

## 3. Connection Methods (SSH, CI/CD)

Interaction with and deployment to the remote Linux machine will primarily leverage the following secure connection methods:

*   **SSH (Secure Shell):** For direct command-line access, manual deployments, troubleshooting, and secure file transfer.
*   **CI/CD Pipelines:** Integration with CI/CD platforms (e.g., Jenkins, GitLab CI, GitHub Actions) that can orchestrate automated builds, test execution, and deployment to the remote Linux environment via SSH or other secure agents. This allows for automated provisioning, configuration management, and application deployment.

## 4. Cross-Platform Considerations for the Go CLI

While the primary focus is on a robust Linux CLI engine, future cross-platform considerations for the Go CLI (e.g., macOS and Windows versions) will involve:

*   **Conditional Compilation:** Utilizing Go's build tags to manage platform-specific code, particularly for GPU abstraction layers (`internal/gpu/backends/` and `internal/gpu/macos/metal/`, `internal/windows/gpu/`).
*   **Dependency Management:** Ensuring external dependencies are compatible across target operating systems or providing platform-specific alternatives.
*   **UI Decoupling:** Maintaining a clear separation between the core CLI logic and any potential future cross-platform UI components to enable independent development and deployment.
*   **Testing Matrix:** Establishing a comprehensive testing matrix to validate functionality and performance on each supported operating system.

## 5. Hardware Capability Checks and Feature Gating

To ensure a robust user experience and prevent runtime failures on environments with varying hardware capabilities, the NeuralMotion CLI will implement VRAM and other hardware capability checks at startup. This allows for 'graceful degradation' or 'feature gating' where functionalities are dynamically enabled or disabled based on the detected system specifications.

*   **VRAM Detection:** The CLI will detect the total available VRAM on the target GPU(s) during initialization, primarily leveraging components within `internal/gpu/detection.go` and `internal/gpu/types/interfaces.go` (`MemoryInfo`). Users can inspect these specifications by running the `neuralmeter gpu list` command.
*   **Threshold-Based Gating:**
    *   **Basic Load Testing:** A minimum VRAM threshold (e.g., 2 GB as a hard floor for GPU-accelerated operations) will be defined. If VRAM falls below this, the system will either issue a critical error and exit, or revert to a purely CPU-based fallback mode if one is implemented.
    *   **AI Testing Features:** A higher VRAM threshold (e.g., 8 GB, 16 GB+) will be required for AI-specific workloads (e.g., deep learning model inference, AI payload generation). If the detected VRAM is insufficient, these features will be automatically disabled, and relevant commands or configuration options will be unavailable or produce clear errors.
*   **User Notification:** Clear, actionable messages will be provided to the user via the CLI output, informing them about detected hardware limitations and which features are disabled as a result. For example, 'Warning: Detected 2GB VRAM. AI testing features (requiring >8GB) will be disabled.', or 'Error: Insufficient VRAM (1GB detected). Cannot run GPU-accelerated tests; minimum 2GB required.'
*   **Optional Metrics Handling:** While metrics like GPU temperature are collected for advanced optimizations (e.g., thermal control in batch processing), these are not mandatory for core functionality. The system will gracefully operate without them in environments where such data is unavailable (e.g., certain virtualized GPU setups), disabling or adjusting features that rely on them without causing critical failures.

This approach ensures that the application runs reliably on diverse hardware, provides transparent feedback to the user, and guides them on necessary hardware upgrades for full functionality. 