### 1. Project Source Tree Structure

The NeuralMeter CLI project adheres to a standard Go project layout, promoting modularity and clear separation of concerns. This structure is designed to support the current CLI-centric development while allowing for future expansion.

- **cmd/**
    This directory contains the main packages for executable commands. Each subdirectory here represents a distinct CLI application or subcommand.
    - `cmd/neuralmeter/`: The primary entry point for the main NeuralMeter CLI tool.

- **internal/**
    This is the core of the NeuralMeter CLI engine. It contains private application logic and shared code that is not intended for external consumption. Components here are highly cohesive and loosely coupled.

- **test/**
    Contains all test-related code, organized by type (unit, integration, benchmarks).

- **config_output.txt, config-test-error.txt, config-test-output.txt, example-test-plan.yaml, go.mod, go.sum, gpu.test, help-error.txt, help-output.txt, neuralmeter.yaml, package-lock.json, package.json, prd.txt, README.md, restructure_gpu_tasks.py, run-help-error.txt, run-help-output.txt, scripts/, tasks.json, test-error.txt, test-output.txt, test-plan.yaml, Make<PERSON><PERSON>, AGENTS.md, bin/**
    These files and directories represent various configuration files, project metadata, build artifacts, and test-related assets.


### 2. Detailed Internal Package Analysis (`internal/`)

The `internal/` directory houses the core logic and functionalities of the NeuralMeter CLI. Each subdirectory here represents a distinct module with specific responsibilities. This section provides a high-level overview of each critical internal package.


- **api/**
    - **Purpose**: Defines the internal API structures and server logic, particularly for the daemon mode where the CLI exposes an API for remote job submission and status monitoring. This section handles HTTP/gRPC server setup and request parsing.
    - **Key Files**: `config.go`, `server.go`
    - **Responsibilities**: API endpoint definition, request/response handling, server initialization.

- **client/**
    - **Purpose**: Contains the logic for the NeuralMeter CLI to act as a client, making requests to target services (System B) or potentially to other NeuralMeter instances in a distributed setup. This includes handling HTTP/WebSocket connections, retries, and error reporting.
    - **Key Files**: `backoff.go`, `client.go`, `error_reporting.go`, `errors.go`, `retry_policies.go`, `retry.go`
    - **Responsibilities**: Managing network connections, executing requests, handling client-side errors, implementing retry mechanisms.

- **config/**
    - **Purpose**: Manages the loading, parsing, and validation of application and test configurations (e.g., `neuralmeter.yaml`, test plans). It ensures that all settings are correctly interpreted and available throughout the application.
    - **Key File**: `config.go`
    - **Responsibilities**: Reading configuration files (YAML/JSON), validating configuration schema, providing configuration accessors.

- **core/**
    - **Purpose**: Provides fundamental utilities and core types used across various parts of the NeuralMeter CLI. This includes common error definitions, factory patterns for creating components, and interfaces related to GPU interactions.
    - **Key Files**: `errors.go`, `factory.go`, `gpu_interfaces.go`
    - **Responsibilities**: Defining common errors, abstracting object creation, standardizing GPU interface contracts.

- **dashboard/**
    - **Purpose**: Primarily responsible for generating or serving data for a dashboard, or internal reporting specific to the CLI's operation. Its role is now focused on data output relevant to a headless environment, rather than serving an interactive UI.
    - **Key File**: `dashboard.go`
    - **Responsibilities**: Data aggregation for reporting, headless dashboard data preparation, internal status communication.

- **engine/**
    - **Purpose**: The central orchestrator for test execution. It coordinates test scenarios, manages worker pools, schedules load generation, and handles the overall lifecycle of a performance test run.
    - **Key Files**: `coordinator.go`, `coordinator_test.go`, `engine.go`, `scheduler.go`, `type_conversion.go`, `type_conversion_test.go`
    - **Responsibilities**: Test plan interpretation, worker management, load scheduling, result aggregation.

- **gpu/**
    - **Purpose**: Manages all interactions with GPU hardware, particularly for CUDA-accelerated workloads. This is a critical component for AI-powered load generation, handling device detection, memory management, kernel compilation, and performance optimization.
    - **Key Files**: Numerous, including `abstraction.go`, `cuda.go`, `detection.go`, `memory_pool.go`, `kernel_compiler.go`, `workload_predictor.go`, `deep_learning_models.go`, `backends/cuda_backend.go`, `macos/metal/detection.go`, `windows/gpu/gpu_state_serializer.go` (and many others covering advanced features like batching, profiling, security, etc.).
    - **Responsibilities**: GPU device discovery, memory allocation, AI-driven workload execution (including intelligent workload prediction, LLM prompts, embeddings generation, and leveraging deep learning models for advanced scenarios), performance profiling, cross-API synchronization, secure memory handling, and various optimization strategies.

- **metrics/**
    - **Purpose**: Collects, aggregates, and exports performance metrics generated during load tests. It supports real-time monitoring and various output formats for analysis.
    - **Key Files**: `aggregator.go`, `collector.go`, `export/`, `storage.go`, `time_buckets.go`
    - **Responsibilities**: Data collection, aggregation (e.g., percentiles), storage, and integration with export formats (Prometheus, JSON, CSV).

- **parser/**
    - **Purpose**: Handles the parsing of various input formats, primarily for test plans, configuration files, or other domain-specific languages within the CLI.
    - **Key File**: `parser.go`
    - **Responsibilities**: Lexical analysis and syntactic parsing of input data.

- **results/**
    - **Purpose**: Manages the processing, grouping, and final output of test results. It takes raw metrics and transforms them into meaningful reports for users.
    - **Key Files**: `aggregator.go`, `grouping.go`
    - **Responsibilities**: Post-processing of raw metrics, data correlation, generating final reports.

- **ui/**
    - **Purpose**: Contains components that were part of a previous UI focus. In the current Linux CLI-centric architecture, this package is largely dormant and its code is either repurposed for CLI-internal display mechanisms or earmarked for a future, decoupled UI client that would communicate externally with the CLI engine.
    - **Key Files**: `app.go`, `form_builder.go`, `layout.go`
    - **Responsibilities**: (Currently de-emphasized for core CLI; potential for future external UI client support) UI rendering logic for interactive components.

- **validation/**
    - **Purpose**: Implements logic for validating various aspects of the application, such as configuration schemas, input parameters, or test results against defined criteria.
    - **Key Files**: `engine.go`, `schema.go`, `validators.go`
    - **Responsibilities**: Defining validation rules, executing validation checks, reporting validation errors.

- **windows/** and **macos/** (within `internal/gpu/backends` and `internal/gpu/macos/metal`)
    - **Purpose**: These directories contain OS-specific implementations, particularly for GPU interaction and system-level calls. Given the primary strategic focus on the **Linux CLI engine**, the Windows and macOS-specific code paths for GPU backends are currently secondary and would be inactive in typical Linux CLI builds. They remain for potential future cross-platform expansion but are not central to the current core mandate.
    - **Responsibilities**: Providing hardware abstraction and OS-specific integrations for their respective platforms (currently de-prioritized for the Linux CLI-first development).

- **worker/**
    - **Purpose**: Manages the individual worker routines or goroutines that execute load generation tasks. These workers are responsible for making requests, handling responses, and reporting metrics back to the `engine`.
    - **Key File**: `worker.go`
    - **Responsibilities**: Concurrent execution of load tests, managing client connections, interacting with the `client` and `metrics` packages.

- **xmlxpath/**
    - **Purpose**: Provides XML parsing and XPath evaluation capabilities. This might be used for parsing specific configuration formats, test result structures, or integrating with systems that rely on XML.
    - **Key Files**: `xpath_engine.go`, `xpath_engine_test.go`
    - **Responsibilities**: XML document traversal and data extraction. 