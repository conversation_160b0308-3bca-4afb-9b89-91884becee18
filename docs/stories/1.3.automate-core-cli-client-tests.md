# Story: Create YAML-Based Test Plans for CLI Automation

## Status: Draft

## Story
As a developer,
I want to create YAML-based test plans for the NeuralMeter CLI that can be executed and integrated into Jenkins CI/CD,
so that I can automate comprehensive testing of the CLI functionality with configurable test scenarios.

## Acceptance Criteria
- YAML test plans are created for all core CLI functionality scenarios
- Test plans can be executed by the CLI using `neuralmeter run --config <test-plan.yaml>`
- Test plans are designed for Jenkins CI/CD integration with proper exit codes
- Test plans cover basic HTTP load testing, high-concurrency scenarios, and WebSocket streaming
- Test plans include proper validation and error handling
- Test plans are configurable for different environments (System B IP, endpoints, etc.)

## Development Tasks

### **Phase 1: Create Core Test Plan Templates**
- [ ] **Task 1.3.1**: Create basic HTTP load test YAML template
  - Template for simple HTTP GET/POST requests
  - Configurable URL, concurrency, duration, and request count
  - Basic validation and error thresholds

- [ ] **Task 1.3.2**: Create high-concurrency test YAML template
  - Template for stress testing with high concurrency
  - Configurable ramp-up and ramp-down periods
  - Performance thresholds and validation criteria

- [ ] **Task 1.3.3**: Create WebSocket streaming test YAML template
  - Template for WebSocket connection testing
  - Real-time result streaming configuration
  - Connection validation and error handling

### **Phase 2: Create Specific Test Plans**
- [ ] **Task 1.3.4**: Create `test/plans/basic-connectivity.yaml`
  - Test basic connectivity to System B endpoints
  - Validate `/health`, `/api/` endpoints
  - Quick validation for CI/CD pipeline

- [ ] **Task 1.3.5**: Create `test/plans/load-test-basic.yaml`
  - Standard load test with moderate concurrency
  - 30-second duration, 10 concurrent users
  - Performance baseline validation

- [ ] **Task 1.3.6**: Create `test/plans/load-test-stress.yaml`
  - High-concurrency stress test
  - 5000 concurrent users, 60-second duration
  - Performance limits validation

- [ ] **Task 1.3.7**: Create `test/plans/websocket-streaming.yaml`
  - WebSocket streaming test with real-time results
  - Connection validation and data streaming
  - Integration with System B WebSocket endpoints

### **Phase 3: Jenkins Integration**
- [ ] **Task 1.3.8**: Create Jenkins pipeline test execution
  - Execute test plans in Jenkins pipeline
  - Parse CLI output and extract test results
  - Set proper exit codes for CI/CD integration

- [ ] **Task 1.3.9**: Create test result reporting
  - Generate test reports in Jenkins
  - Track performance metrics over time
  - Alert on test failures or performance regressions

## Test Plan YAML Structure Example
```yaml
name: "Basic Connectivity Test"
description: "Test basic connectivity to System B endpoints"
target:
  base_url: "http://{{.SystemB_IP}}"
  timeout: "30s"
  max_retries: 3

scenarios:
  - name: "Health Check"
    endpoint: "/health"
    method: "GET"
    expected_status: 200
    concurrency: 1
    duration: "10s"
    
  - name: "API Endpoint"
    endpoint: "/api/"
    method: "GET"
    expected_status: 200
    concurrency: 5
    duration: "30s"

validation:
  max_response_time: "2s"
  max_error_rate: 0.01
  min_throughput: 10

output:
  format: "json"
  file: "test-results/connectivity-test.json"
  stream_results: "ws://{{.SystemB_IP}}:8080/stream"
```

## Dev Notes
- **YAML-FIRST APPROACH**: Test plans should be YAML-based, similar to JMeter test plans
- **CLI INTEGRATION**: All test plans must work with `neuralmeter run --config <plan.yaml>`
- **JENKINS READY**: Test plans must integrate with Jenkins CI/CD pipeline
- **CONFIGURABLE**: Use environment variables for System B IP and other configurable values
- **VALIDATION**: Include proper validation criteria and error thresholds
- **REPORTING**: Generate test reports suitable for CI/CD integration

## Completion Notes
- **Status**: Draft - Development tasks identified
- **Dependencies**: Requires completion of Story 1.2 (CLI functionality)
- **Files to Create**: 
  - `test/plans/` directory with YAML test plans
  - Jenkins pipeline updates for test execution
  - Test result reporting and validation

## File List
- `test/plans/basic-connectivity.yaml`
- `test/plans/load-test-basic.yaml`
- `test/plans/load-test-stress.yaml`
- `test/plans/websocket-streaming.yaml`
- `Jenkinsfile` (updated for test plan execution)
- `docs/test-plans/` (documentation for test plans) 