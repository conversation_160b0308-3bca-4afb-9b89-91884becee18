# NeuralMeter CLI - Product Requirements Document

## Executive Summary

NeuralMeterGo CLI is a high-performance, command-line load testing engine built in Go for Linux systems with CUDA GPU acceleration. It provides a modern, headless alternative to JMeter's non-GUI mode, designed for automated testing, CI/CD pipelines, and distributed load generation at scale. The system emphasizes real-world testing against actual servers with no mocks or stubs, ensuring accurate performance measurements, code base is well developed and originally included windows and mac ui components, adjust the code base for a pure linux cli engine approach.

## Product Vision

### Problem Statement

Current CLI load testing tools suffer from:

- Poor resource efficiency and limited scalability
- Lack of GPU acceleration for AI-powered workloads
- Complex configuration and poor automation support
- Reliance on mocks that don't reflect real performance
- Limited real-time monitoring capabilities in headless mode

### Solution

NeuralMeterGo CLI addresses these challenges through:

- **Pure CLI Design**: Command-line interface for all operations
- **CUDA GPU Acceleration**: Native CUDA support for AI workload generation
- **Real Server Testing**: All testing against actual infrastructure
- **YAML/JSON Configuration**: Simple, version-controlled test definitions
- **Headless Operation**: Designed for servers, containers, and CI/CD

### Target Users

- **DevOps Engineers**: Automated performance testing in CI/CD
- **SRE Teams**: Production load testing and capacity planning
- **QA Automation**: Headless testing in test environments
- **Cloud Engineers**: Distributed load testing on GPU instances

### 1. Goals and Background Context

#### Goals

- Document the current state of the NeuralMeter CLI, distinguishing between implemented and pending features.
- Provide a clear product vision and problem statement.
- Outline functional and non-functional requirements.
- Define high-level UI/UX goals (even for a CLI, considering future UI integration).
- Capture technical assumptions and architectural decisions.
- List and detail epics and user stories for future development.

#### Background Context

The NeuralMeter CLI project began with a broader scope, including cross-platform UI components. However, the strategic focus has shifted to developing and perfecting a robust Linux CLI engine for high-performance load testing. This PRD aims to align documentation with this refined focus, serving as a comprehensive guide for continued development.

#### Change Log

| Date       | Version | Description                                          | Author    |
| ---------- | ------- | ---------------------------------------------------- | --------- |
| 2024-07-30 | 1.0     | Initial draft based on existing PRD and architecture | John (PM) |

### 2. Requirements

#### Functional

- FR1: The NeuralMeter CLI shall provide a command-line interface for running performance tests. (Implemented)
- FR2: The CLI shall support YAML/JSON configuration for test plans. (Implemented)
- FR3: The CLI shall support HTTP/1.1 and HTTP/2 protocols for load generation. (Implemented)
- FR4: The CLI shall support WebSocket and gRPC protocols for load generation. (Implemented)
- FR5: The CLI shall include GPU acceleration (CUDA) for AI workload generation. (Partially Implemented - Core integration exists, advanced AI payload generation/model inference is ongoing)
- FR6: The CLI shall provide mechanisms for real-time metrics collection and export. (Implemented - Basic metrics collection and Prometheus/file export)
- FR7: The CLI shall support Basic, Bearer, and Custom Header authentication for target services. (Implemented)
- FR8: The CLI shall enable scenario-based testing with weighted distribution. (Implemented)
- FR9: The CLI shall support dynamic variable extraction and reuse within test scenarios. (Implemented)
- FR10: The CLI shall allow for response validation using multiple criteria. (Implemented)
- FR11: The CLI shall support daemon mode operation for background service and remote job submission. (Partially Implemented - Basic daemon structure exists, full API for job submission and auto-scaling is pending)
- FR12: The CLI shall support distributed mode for coordinating across multiple instances. (Pending)
- FR13: The CLI shall provide commands for GPU management (list, status, benchmark). (Partially Implemented - Basic commands exist, advanced benchmarking and status is ongoing)
- FR14: The CLI shall support various result export formats (JSON, CSV, JUnit XML, HTML). (Implemented - Basic JSON/CSV export, others pending)

#### Non Functional

- NFR1: The CLI shall be high-performance, capable of generating significant load.
- NFR2: The CLI shall be resource-efficient, particularly concerning CPU and memory usage.
- NFR3: The CLI shall provide accurate performance measurements with no mocks or stubs.
- NFR4: The CLI shall be designed for headless operation, suitable for CI/CD pipelines.
- NFR5: The CLI shall maintain a modular and extensible architecture for future development.
- NFR6: The CLI shall provide robust error handling and clear error reporting.
- NFR7: The CLI shall have comprehensive unit, integration, and benchmark tests.

### 3. User Interface Design Goals (CLI Context)

#### Overall UX Vision

To provide a highly intuitive and efficient command-line experience for load testing, with clear command structures, meaningful output, and easy configuration. While primarily a CLI, future considerations for a decoupled GUI will prioritize seamless integration and data visualization.

#### Key Interaction Paradigms

- Command-line arguments for immediate control.
- Configuration files (YAML/JSON) for complex test definitions.
- Clear, structured console output for real-time feedback.
- Non-interactive by default, but with options for verbose output and monitoring.

#### Core Screens and Views

- Command help/usage.
- Real-time metrics dashboard (console-based).
- Test execution summary report.
- GPU device listing and status.

#### Accessibility: None

#### Branding: Minimal CLI branding, focus on clarity and readability.

#### Target Device and Platforms: Desktop Only (Linux CLI)

### 4. Technical Assumptions

#### Repository Structure: Polyrepo (implied by decoupled UI/CLI)

#### Service Architecture: Monolith (for the core CLI engine)

#### Testing Requirements: Unit + Integration + Benchmarks (with E2E tests for full system validation)

#### Additional Technical Assumptions and Requests

- Go (latest stable version) as the primary development language.
- Cobra for CLI command parsing.
- YAML/JSON for configuration.
- OpenTelemetry for observability (metrics, logging, tracing).
- Focus on Go-idiomatic concurrency patterns.
- Cross-platform considerations for GPU backends (CUDA, Metal), even if Linux is primary.
- **CI/CD Integration:** Jenkins or a similar CI/CD system will be used for automated builds, testing, and deployment orchestration to remote Linux environments. This system will also securely manage infrastructure credentials and environment-specific IP addresses.

### 5. Epic List

- Epic 1: Core CLI Load Generation Engine (HTTP/WebSocket) - **(Largely Implemented)**
  - Goal: Establish the foundational CLI, configuration loading, basic HTTP/WebSocket client, worker pool, and metrics collection.
- Epic 2: Advanced Protocol Support (gRPC) - **(Pending)**
  - Goal: Extend the CLI's capabilities to include gRPC protocol for load testing.
- Epic 3: Core GPU Acceleration & AI Workload Generation - **(In Progress)**
  - Goal: Implement robust CUDA integration, intelligent workload prediction, and AI payload generation for realistic testing.
- Epic 4: Advanced CLI Features & Reporting - **(Partially Implemented)**
  - Goal: Enhance CLI commands, refine real-time monitoring, and expand reporting formats (e.g., JUnit XML, HTML).
- Epic 5: Daemon and Distributed Mode Implementation - **(Pending)**
  - Goal: Develop the full daemon mode with API for remote job submission and distributed load coordination across multiple CLI instances.

### 6. Epic Details

#### Epic 1 Core CLI Load Generation Engine (HTTP/WebSocket)

Goal: Establish the foundational CLI, configuration loading, basic HTTP/WebSocket client, worker pool, and metrics collection. This epic provides the core functionality for basic load testing.

##### Story 1.1 Project Setup and Initial CLI

As a developer, I want to set up the Go project structure and initial CLI command, so that I can begin building the core application.

- Acceptance Criteria: - Go module initialized. - Basic `neuralmeter` command defined using Cobra. - Project structure aligns with Go best practices.
  Status: Completed

##### Story 1.2 Configuration Loading and Validation

As a user, I want to define test plans in YAML/JSON, so that I can easily configure and version my load tests.

- Acceptance Criteria: - YAML/JSON configuration files can be loaded. - Configuration schema validation is in place. - Environment variable overrides are supported.
  Status: Completed

##### Story 1.3 Basic HTTP/WebSocket Client and Worker Pool

As a user, I want the CLI to generate HTTP/WebSocket requests, so that I can test web services.

- Acceptance Criteria: - HTTP/1.1 and HTTP/2 client implemented. - WebSocket client implemented. - Worker pool manages concurrent request generation. - Basic (username/password) and Bearer token authentication are supported.
  Status: Completed

##### Story 1.4 Core Metrics Collection and Console Output

As a user, I want to see real-time performance metrics, so that I can monitor test progress and identify immediate issues.

- Acceptance Criteria: - RPS, latency (P50, P90, P95, P99), error rate, and throughput are collected. - Metrics are aggregated in real-time. - Metrics are displayed in the console during test execution.
  Status: Completed

##### Story 1.5 Basic Result Export (JSON/CSV)

As a user, I want to export test results, so that I can analyze them further in external tools.

- Acceptance Criteria: - Test results can be exported to JSON format. - Test results can be exported to CSV format.
  Status: Completed

#### Epic 2 Advanced Protocol Support (gRPC)

Goal: Extend the CLI's capabilities to include gRPC protocol for load testing.

##### Story 2.1 gRPC Client Implementation

As a user, I want the CLI to generate gRPC requests, so that I can test gRPC services.

- Acceptance Criteria: - gRPC client implemented to support unary and streaming calls. - gRPC request payloads can be defined in configuration. - gRPC response validation mechanisms are in place.
  Status: Pending

#### Epic 3 Core GPU Acceleration & AI Workload Generation

Goal: Implement robust CUDA integration, intelligent workload prediction, and AI payload generation for realistic testing.

##### Story 3.1 GPU Device Detection and Basic CUDA Interface

As a user, I want the CLI to detect available GPUs and interact with them, so that I can leverage GPU acceleration for my tests.

- Acceptance Criteria: - CUDA-enabled GPU devices are detected. - Basic CUDA interface for resource allocation is implemented. - GPU status can be queried via CLI commands.
  Status: Completed

##### Story 3.2 AI Payload Generation Engine

As a user, I want the CLI to generate AI-specific payloads (e.g., LLM prompts, embeddings), so that I can simulate realistic AI workloads.

- Acceptance Criteria: - Logic for generating LLM prompts based on configuration. - Support for generating various types of embeddings. - Integration with GPU for accelerated payload generation.
  Status: In Progress

##### Story 3.3 Intelligent Workload Prediction and Optimization

As a user, I want the CLI to intelligently adjust workload generation based on real-time GPU metrics and predictive models, so that I can achieve optimal GPU utilization and more realistic test scenarios.

- Acceptance Criteria: - Workload prediction models (e.g., linear regression, LSTM, ensemble) are integrated. - CLI dynamically adjusts request rates and batch sizes based on predictions. - GPU memory management is optimized based on predicted usage.
  Status: In Progress

#### Epic 4 Advanced CLI Features & Reporting

Goal: Enhance CLI commands, refine real-time monitoring, and expand reporting formats (e.g., JUnit XML, HTML).

##### Story 4.1 Enhanced CLI Commands and Options

As a user, I want more advanced command-line options, so that I can fine-tune my tests without modifying configuration files.

- Acceptance Criteria: - Additional command-line flags for common test parameters. - Improved CLI output for better readability.
  Status: Completed

##### Story 4.2 Expanded Result Export Formats

As a user, I want to export test results in more formats, so that I can integrate with various reporting tools and CI/CD pipelines.

- Acceptance Criteria: - Export to JUnit XML format. - Export to HTML report format.
  Status: Pending

##### Story 4.3 Advanced Metrics and Observability

As a user, I want deeper insights into test performance, so that I can diagnose complex issues.

- Acceptance Criteria: - Integration with OpenTelemetry for distributed tracing. - Granular logging with configurable levels. - Advanced GPU metrics (e.g., kernel execution times).
  Status: In Progress

### 7. Checklist Results Report

To be completed after PRD review and approval.

### 8. Next Steps

#### UX Expert Prompt

This section will contain the prompt for the UX Expert to begin designing any necessary UI/UX for the CLI or future decoupled UI based on this PRD.

#### Architect Prompt

This section will contain the prompt for the Architect to review and validate the technical aspects and assumptions outlined in this PRD, and to begin detailed design of pending features.
