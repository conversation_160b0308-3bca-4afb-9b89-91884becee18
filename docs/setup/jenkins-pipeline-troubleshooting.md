# Jenkins Pipeline Troubleshooting Guide

## Issue: Pipeline Only Shows "Post Actions"

When a Jenkins pipeline skips directly to "Post Actions" without executing the main stages, it typically indicates one of these issues:

### 1. **Agent/Node Issues**

**Symptoms**: Pipeline starts but no stages execute
**Causes**:

- No available Jenkins agents/nodes
- Agent offline or disconnected
- Agent doesn't meet pipeline requirements

**Solutions**:

```groovy
// Check agent configuration
pipeline {
    agent any  // Try this first
    // OR
    agent { label 'linux' }  // If you need specific agent
    // OR
    agent none  // Then specify agent per stage
}
```

**Diagnostic Steps**:

1. Go to Jenkins → Manage Jenkins → Manage Nodes
2. Check if agents are online and have available executors
3. Look for agent connection errors

### 2. **Missing Credentials**

**Symptoms**: <PERSON>pel<PERSON> fails early, especially with SSH operations
**Causes**:

- SSH credentials not configured
- Wrong credential ID
- Credential permissions issues

**Solutions**:

1. **Configure SSH Credentials**:

   - <PERSON> → <PERSON><PERSON> Jenkins → Credentials
   - Add SSH Username with private key
   - ID: `system-a-ssh-key`
   - Username: `neuro`
   - Private Key: Upload or paste SSH private key

2. **Test Credentials**:
   ```groovy
   // Temporarily comment out credential usage for testing
   environment {
       SSH_USER = 'neuro'
       // SSH_KEY = credentials('system-a-ssh-key')  // Comment out initially
   }
   ```

### 3. **Network Connectivity Issues**

**Symptoms**: Pipeline fails on network operations
**Causes**:

- Jenkins can't reach System A or System B
- Firewall blocking connections
- Services not running on target systems

**Diagnostic Steps**:

1. **Run Diagnostic Pipeline**: Use `jenkins-cli-diagnostic-pipeline.groovy`
2. **Manual Network Test** from Jenkins agent:
   ```bash
   ping *************  # System A
   ping *************  # System B
   curl http://*************/  # System B HTTP
   ```

### 4. **Build Environment Issues**

**Symptoms**: Go build fails or tools missing
**Causes**:

- Go not installed on Jenkins agent
- Wrong Go version
- Missing build dependencies

**Solutions**:

1. **Install Go on Jenkins Agent**:

   ```bash
   # On Jenkins agent machine
   wget https://golang.org/dl/go1.21.5.linux-amd64.tar.gz
   sudo tar -C /usr/local -xzf go1.21.5.linux-amd64.tar.gz
   export PATH=$PATH:/usr/local/go/bin
   ```

2. **Configure Go in Jenkins**:
   - Jenkins → Manage Jenkins → Global Tool Configuration
   - Add Go installation

### 5. **Pipeline Syntax Errors**

**Symptoms**: Pipeline fails to parse or start
**Causes**:

- Groovy syntax errors
- Invalid pipeline structure
- Missing required sections

**Solutions**:

1. **Use Pipeline Syntax Checker**:

   - Jenkins → Pipeline Syntax
   - Validate your pipeline script

2. **Start with Minimal Pipeline**:
   ```groovy
   pipeline {
       agent any
       stages {
           stage('Test') {
               steps {
                   echo 'Hello World'
               }
           }
       }
   }
   ```

## Step-by-Step Troubleshooting Process

### Step 1: Run Diagnostic Pipeline

1. Create new Jenkins job with `jenkins-cli-diagnostic-pipeline.groovy`
2. Run with `TEST_LEVEL = 'diagnostic'`
3. Check output for environment and connectivity issues

### Step 2: Check Prerequisites

- [ ] Jenkins agent available and online
- [ ] Go installed on Jenkins agent
- [ ] Network connectivity to both systems
- [ ] System B services running (nginx on port 80)

### Step 3: Configure SSH Access

1. **Generate SSH key pair** (if not exists):

   ```bash
   ssh-keygen -t rsa -b 4096 -f ~/.ssh/neuralmeter_key
   ```

2. **Copy public key to System A**:

   ```bash
   ssh-copy-id -i ~/.ssh/neuralmeter_key.pub neuro@*************
   ```

3. **Add private key to Jenkins credentials**:
   - Credentials → Add → SSH Username with private key
   - ID: `system-a-ssh-key`
   - Username: `neuro`
   - Private Key: Paste content of `~/.ssh/neuralmeter_key`

### Step 4: Test SSH Connection

```bash
# Test from Jenkins agent
ssh -i ~/.ssh/neuralmeter_key neuro@************* 'echo "SSH test successful"'
```

### Step 5: Verify System B Services

```bash
# Test System B services
curl http://*************/
curl http://*************/health
curl http://*************/api/

# Check nginx status on System B
ssh neuro@************* 'sudo systemctl status nginx'
```

## Common Error Messages and Solutions

### "No available executors"

**Solution**:

- Check Jenkins → Manage Nodes
- Ensure at least one agent is online
- Increase number of executors if needed

### "Credentials not found"

**Solution**:

- Verify credential ID matches exactly: `system-a-ssh-key`
- Check credential permissions
- Test credential manually

### "Connection refused" or "Host unreachable"

**Solution**:

- Verify IP addresses are correct
- Check network connectivity
- Ensure target services are running

### "Go command not found"

**Solution**:

- Install Go on Jenkins agent
- Add Go to PATH
- Configure Go tool in Jenkins

### "Permission denied (publickey)"

**Solution**:

- Verify SSH key is correct
- Check SSH key permissions (600 for private key)
- Ensure public key is in authorized_keys on target system

## Testing Sequence

### Phase 1: Basic Connectivity

1. Run diagnostic pipeline with `TEST_LEVEL = 'diagnostic'`
2. Verify all environment checks pass
3. Confirm network connectivity to both systems

### Phase 2: Build Testing

1. Run diagnostic pipeline with `TEST_LEVEL = 'connectivity'`
2. Verify Go build works
3. Confirm CLI binary can be created

### Phase 3: SSH Testing

1. Configure SSH credentials in Jenkins
2. Run diagnostic pipeline with `TEST_LEVEL = 'basic'`
3. Test SSH connectivity to System A

### Phase 4: Full Pipeline

1. Once all diagnostics pass, run full CLI test pipeline
2. Start with `TEST_SUITE = 'connectivity'`
3. Progress to `TEST_SUITE = 'full'`

## Monitoring and Logs

### Jenkins Console Output

- Check each stage for specific error messages
- Look for timeout issues
- Note any permission errors

### System Logs

- **System A**: `/var/log/auth.log` for SSH issues
- **System B**: `/var/log/nginx/error.log` for web service issues
- **Jenkins**: Jenkins system log for agent issues

### Network Diagnostics

```bash
# From Jenkins agent
netstat -an | grep :22    # SSH connectivity
netstat -an | grep :80    # HTTP connectivity
ss -tulpn | grep :8080    # Jenkins port
```

This troubleshooting guide should help identify why your pipeline is jumping to post actions. Start with the diagnostic pipeline to isolate the issue!
