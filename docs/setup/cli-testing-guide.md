# NeuralMeter CLI Testing Guide

## Overview

This guide explains how to test the NeuralMeter CLI execution on System A against target services on System B using Jenkins automation.

## Architecture

```
Jenkins CI/CD ──builds──> System A (CLI Execution) ──tests──> System B (Target Services)
     │                         │                                    │
     │                    GPU-accelerated                     nginx + Flask API
     │                    NeuralMeter CLI                     Load test targets
     │                                                              │
     └──collects results────────┘                                  │
                                                                   │
System A: ************* (CLI execution machine with GPU)         │
System B: ************* (Target services for load testing) ──────┘
```

## Prerequisites

### Jenkins Setup
1. Jenkins installed and running at http://*************:8080/
2. SSH credentials configured for System A access
3. Go build environment available on Jenkins agent
4. Network connectivity to both System A and System B

### System A (CLI Execution Machine)
- **Host**: *************
- **User**: neuro (with sudo access)
- **Requirements**:
  - Ubuntu Linux with GPU drivers
  - CUDA/ROCm/OpenCL installed
  - Go runtime >= 1.21
  - SSH access configured
  - Directory structure: `/home/<USER>/cli/`, `/home/<USER>/logs/`, `/home/<USER>/test-results/`

### System B (Target Services)
- **Host**: *************
- **User**: neuro
- **Services**:
  - nginx on port 80 (endpoints: `/`, `/health`, `/api/`)
  - Flask API on port 5000 (optional)
  - SSH access configured

## Test Scripts

### 1. Quick Connectivity Test
**File**: `test/cli-scripts/quick-connectivity-test.sh`
**Purpose**: Fast connectivity verification between System A and System B
**Usage**: Run before full test suite to verify basic connectivity

**Checks**:
- CLI binary exists and is executable
- Go runtime available
- Network connectivity to System B
- System B HTTP services responding
- Basic CLI functionality
- GPU detection (optional)

### 2. Full System Test
**File**: `test/cli-scripts/system-a-to-system-b-test.sh`
**Purpose**: Comprehensive CLI testing from System A to System B
**Usage**: Complete test suite with load testing and validation

**Test Categories**:
- CLI basic functionality
- GPU detection and availability
- Test plan validation
- CLI execution against System B
- Server daemon functionality
- Results analysis

### 3. Jenkins Pipeline
**File**: `test/cli-scripts/jenkins-cli-test-pipeline.groovy`
**Purpose**: Automated Jenkins pipeline for CLI testing
**Usage**: Jenkins job configuration for automated testing

## Running Tests

### Option 1: Jenkins Pipeline (Recommended)

1. **Create Jenkins Job**:
   ```bash
   # In Jenkins UI:
   # 1. New Item → Pipeline
   # 2. Name: "NeuralMeter-CLI-Tests"
   # 3. Pipeline → Definition: "Pipeline script from SCM"
   # 4. SCM: Git, Repository URL: <your-repo>
   # 5. Script Path: test/cli-scripts/jenkins-cli-test-pipeline.groovy
   ```

2. **Configure Parameters**:
   - `SYSTEM_A_HOST`: ************* (default)
   - `SYSTEM_B_HOST`: ************* (default)
   - `TEST_SUITE`: full/connectivity/basic/load
   - `CLEANUP_AFTER_TEST`: true (default)

3. **Run Pipeline**:
   - Click "Build with Parameters"
   - Select desired test suite
   - Monitor execution in Jenkins console

### Option 2: Manual Execution on System A

1. **SSH to System A**:
   ```bash
   ssh neuro@*************
   ```

2. **Set Environment Variables**:
   ```bash
   export SYSTEM_B_HOST=*************
   export CLI_BINARY=/home/<USER>/cli/neuralmeter
   export TEST_RESULTS_DIR=/home/<USER>/test-results
   ```

3. **Run Quick Test**:
   ```bash
   ./cli-scripts/quick-connectivity-test.sh
   ```

4. **Run Full Test**:
   ```bash
   ./cli-scripts/system-a-to-system-b-test.sh
   ```

## Test Configuration

### Environment Variables
- `SYSTEM_B_HOST`: Target system IP address (default: *************)
- `CLI_BINARY`: Path to NeuralMeter CLI binary (default: ./neuralmeter)
- `TEST_RESULTS_DIR`: Directory for test results (default: ./test-results)
- `LOG_FILE`: Log file path (default: ${TEST_RESULTS_DIR}/system-a-to-b-test.log)

### Test Plans
The test scripts automatically generate test plans for:
- **Basic HTTP Test**: Simple GET requests to System B
- **API Test**: API endpoint testing
- **Load Test**: Concurrent load testing with ramp-up/down

### Configuration File
**File**: `test/cli-scripts/test-config.yaml`
Contains comprehensive test configuration including:
- System requirements
- Test plan definitions
- Expected results thresholds
- GPU requirements
- Monitoring settings

## Test Results

### Output Files
- `test-report.txt`: Summary test report
- `*.json`: JSON result files from CLI execution
- `system-a-to-b-test.log`: Detailed execution log
- `jenkins-test-report.md`: Jenkins-specific report

### Success Criteria
- All connectivity checks pass
- CLI binary executes successfully
- GPU detection works (if GPU available)
- Load tests complete without errors
- Test plans validate successfully
- Server daemon starts/stops correctly

### Failure Scenarios
- Network connectivity issues between systems
- CLI binary missing or not executable
- System B services not responding
- GPU drivers not properly installed
- Test plan validation failures
- Load test execution errors

## Troubleshooting

### Common Issues

1. **SSH Connection Failed**:
   ```bash
   # Check SSH key configuration
   ssh -i ~/.ssh/id_rsa neuro@*************
   
   # Verify SSH service running
   sudo systemctl status ssh
   ```

2. **CLI Binary Not Found**:
   ```bash
   # Check binary location
   ls -la /home/<USER>/cli/neuralmeter
   
   # Verify permissions
   chmod +x /home/<USER>/cli/neuralmeter
   ```

3. **System B Not Responding**:
   ```bash
   # Test connectivity
   ping *************
   curl http://*************/health
   
   # Check nginx status on System B
   sudo systemctl status nginx
   ```

4. **GPU Not Detected**:
   ```bash
   # Check NVIDIA GPU
   nvidia-smi
   
   # Check AMD GPU
   rocminfo
   
   # Verify drivers
   lsmod | grep nvidia
   ```

5. **Go Runtime Issues**:
   ```bash
   # Check Go installation
   go version
   which go
   
   # Verify environment
   echo $GOPATH
   echo $PATH
   ```

### Log Analysis
- Check `/home/<USER>/test-results/system-a-to-b-test.log` for detailed execution logs
- Review Jenkins console output for pipeline issues
- Examine individual test result JSON files for specific failures

## Integration with Story 0.1

This CLI testing implementation completes the task:
- **Task**: "Test CLI execution on System A against System B target services"
- **Status**: Ready for execution through Jenkins pipeline
- **Verification**: Comprehensive test suite validates all CLI functionality

### Next Steps
1. Execute Jenkins pipeline to run CLI tests
2. Review test results and address any failures
3. Document any issues encountered
4. Mark story task as complete upon successful test execution

## Security Considerations

- SSH keys properly configured and secured
- neuro user has appropriate sudo access
- Network access restricted to required ports
- Test data does not contain sensitive information
- Cleanup procedures remove temporary files

## Performance Monitoring

The test scripts monitor:
- CPU utilization during tests
- Memory usage
- GPU utilization (if available)
- Network connections
- Test execution times
- Load test performance metrics

## Maintenance

- Regularly update test plans as CLI features evolve
- Monitor System B service health
- Update GPU drivers as needed
- Review and update test thresholds
- Archive old test results periodically