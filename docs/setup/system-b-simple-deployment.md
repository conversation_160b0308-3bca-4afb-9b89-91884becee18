# System B Simple Deployment Guide

## Quick Start

This is a simplified deployment that focuses on getting the core System B services working quickly on Ubuntu 22.04.

### What This Deploys

- **API Services**: 3 Python HTTP servers on ports 8011, 8012, 8013
- **Health Service**: Python health monitoring server on port 8021
- **No complex dependencies**: No HAProxy, WebSocket, SSL, or Prometheus

### Deployment

```bash
# Run the simple deployment
sudo ./scripts/deploy-system-b-simple-working.sh

# Validate the deployment
./scripts/validate-system-b-working.sh
```

### Expected Output

```
=== Simple System B Deployment Started ===
[INFO] Creating directories...
[INFO] Installing dependencies...
[INFO] Creating API server...
[INFO] Creating health server...
[INFO] Creating API services...
[INFO] Creating health service...
[INFO] Starting services...
[SUCCESS] API service on port 8011 is running
[SUCCESS] API service on port 8012 is running
[SUCCESS] API service on port 8013 is running
[SUCCESS] Health service is running
[INFO] Testing services...
[SUCCESS] API service on port 8011 is responding
[SUCCESS] API service on port 8012 is responding
[SUCCESS] API service on port 8013 is responding
[SUCCESS] Health service is responding

🎉 System B deployment successful!

Working services:
• API Services: http://localhost:8011, 8012, 8013
• Health Service: http://localhost:8021/health
```

### Testing the Services

```bash
# Test API health endpoints
curl http://localhost:8011/health
curl http://localhost:8012/health
curl http://localhost:8013/health

# Test API data endpoints
curl http://localhost:8011/api/data
curl http://localhost:8012/api/data
curl http://localhost:8013/api/data

# Test health service
curl http://localhost:8021/health
curl http://localhost:8021/health/system
```

### Service Management

```bash
# Check service status
sudo systemctl status neuralmeter-api-8011
sudo systemctl status neuralmeter-api-8012
sudo systemctl status neuralmeter-api-8013
sudo systemctl status neuralmeter-health

# Restart services
sudo systemctl restart neuralmeter-api-8011
sudo systemctl restart neuralmeter-health

# View logs
sudo journalctl -u neuralmeter-api-8011 -f
sudo journalctl -u neuralmeter-health -f
```

### Troubleshooting

#### Service Won't Start

```bash
# Check detailed status
sudo systemctl status neuralmeter-api-8011 -l

# Check logs
sudo journalctl -u neuralmeter-api-8011 --no-pager

# Check if port is in use
sudo ss -tuln | grep 8011

# Check file permissions
ls -la /opt/neuralmeter/bin/
```

#### Port Already in Use

```bash
# Find what's using the port
sudo lsof -i :8011

# Kill the process if needed
sudo kill -9 <PID>

# Restart the service
sudo systemctl restart neuralmeter-api-8011
```

#### Permission Issues

```bash
# Fix ownership
sudo chown -R neuro:neuro /opt/neuralmeter

# Fix permissions
sudo chmod +x /opt/neuralmeter/bin/*.py
```

### Load Testing Ready

Once deployed, System B is ready for load testing from System A (NeuralMeter CLI):

- **API Endpoints**: `http://localhost:8011/api/data`, `8012`, `8013`
- **Health Monitoring**: `http://localhost:8021/health`
- **Simple HTTP responses**: No complex WebSocket or SSL requirements

This deployment provides the essential System B functionality needed for CLI load testing without the complexity of HAProxy, SSL certificates, or WebSocket services.