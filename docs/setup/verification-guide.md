# Environment Setup Verification Guide

This guide provides step-by-step instructions for verifying the environment setup for the NeuralMotion CLI project, including Jenkins CI/CD, System A (Build Machine), and System B (Test Server).

## Prerequisites

- <PERSON> is installed and accessible at http://*************:8080/
- Jenkins admin access (for credential configuration)
- SSH access to System A and System B
- Sudo privileges on System A and System B (for user setup and key distribution)

## Step 1: Jenkins Configuration

### 1.1 Install Required Jenkins Plugins

Access Jenkins at http://*************:8080/ and install the following plugins:

1. **SSH Agent Plugin** - For SSH connectivity to remote systems
2. **Pipeline Plugin** - For running declarative pipelines
3. **Git Plugin** - For source code management
4. **Blue Ocean Plugin** - For enhanced pipeline visualization

### 1.2 Create Neuro User on Target Systems

**Note**: This step must be performed BEFORE generating SSH keys, as the neuro user must exist on the target systems. We use 'neuro' instead of 'jenkins' to avoid conflicts with the existing Jenkins installation.

1. **Create Neuro User on System A (Build Machine)**:
   ```bash
   # SSH to System A as a user with sudo privileges
   # Note: Replace 'your-username' with the actual username that has sudo access
   ssh your-username@*************
   
   # Create neuro user if it doesn't exist
   sudo useradd -m -s /bin/bash neuro
   sudo usermod -aG sudo neuro
   echo "neuro:5FKWd9Dz" | sudo chpasswd
   
   # Verify user creation
   id neuro
   ```

2. **Create Neuro User on System B (Test Server)**:
   ```bash
   # SSH to System B as a user with sudo privileges
   # Note: Replace 'your-username' with the actual username that has sudo access
   ssh your-username@*************
   
   # Create neuro user if it doesn't exist
   sudo useradd -m -s /bin/bash neuro
   sudo usermod -aG sudo neuro
   echo "neuro:5FKWd9Dz" | sudo chpasswd
   
   # Verify user creation
   id neuro
   ```

3. **Verify Neuro User Home Directories**:
   ```bash
   # Check System A
   ssh your-username@************* 'ls -la /home/<USER>/'
   
   # Check System B
   ssh your-username@************* 'ls -la /home/<USER>/'
   ```

### 1.3 Generate SSH Key Pair

**Note**: This step should be performed as the `neuro` user on the Jenkins machine. The SSH keys must be generated in the neuro user's home directory, not the setup user's directory.

1. **Switch to Neuro User and Generate SSH Key Pair**:
   ```bash
   # Switch to neuro user
   sudo su - neuro
   
   # Create .ssh directory if it doesn't exist
   mkdir -p ~/.ssh
   chmod 700 ~/.ssh
   
   # Generate a new SSH key pair (RSA 4096-bit for maximum compatibility)
   ssh-keygen -t rsa -b 4096 -f ~/.ssh/neuro_key -N ""
   
   # Or generate Ed25519 key (more secure, modern systems)
   ssh-keygen -t ed25519 -f ~/.ssh/neuro_key -N ""
   
   # Set proper permissions
   chmod 600 ~/.ssh/neuro_key
   chmod 644 ~/.ssh/neuro_key.pub
   
   # Verify key generation
   ls -la ~/.ssh/neuro_key*
   ```

2. **Copy Public Key to Target Systems**:
   ```bash
   # Copy to System A (Build Machine)
   ssh-copy-id -i ~/.ssh/neuro_key.pub neuro@*************
   
   # Copy to System B (Test Server)
   ssh-copy-id -i ~/.ssh/neuro_key.pub neuro@*************
   ```

3. **Test SSH Connectivity**:
   ```bash
   # Test connection to System A
   ssh -i ~/.ssh/neuro_key neuro@************* 'echo "SSH to System A successful"'
   
   # Test connection to System B
   ssh -i ~/.ssh/neuro_key neuro@************* 'echo "SSH to System B successful"'
   ```

### 1.4 Configure SSH Credentials in Jenkins

**Note**: This step requires Jenkins admin access, not sudo privileges.

1. Go to **Manage Jenkins** > **Manage Credentials**
2. Click **System** > **Global credentials** > **Add Credentials**
3. Configure SSH credentials:
   - **Kind**: SSH Username with private key
   - **Scope**: Global
   - **ID**: `ssh-credentials`
   - **Username**: `neuro`
   - **Private Key**: 
     - Select **Enter directly**
     - Copy the contents of `/home/<USER>/.ssh/neuro_key` (the private key file from neuro user's directory)
     - **Important**: Copy the ENTIRE key including the BEGIN and END lines
   - **Description**: SSH credentials for System A and System B

### 1.5 Create Pipeline Job

1. Go to **New Item** (from Jenkins dashboard)
2. Enter an item name: `neuralmeter-environment-verification`
3. Select **Pipeline** from the item types list
4. Click **OK**
5. Configure the pipeline:
   - **Description**: `Environment verification pipeline for NeuralMotion CLI project`
   - **Pipeline** section:
   - **Definition**: Pipeline script from SCM
   - **SCM**: Git
     - **Repository URL**: `http://*************:3000/paul/NeuralMotion_Linux_Cli_Worker.git` (adjust IP/port as needed)
     - **Credentials**: Leave empty (for public repository) or add username/password credentials if private
     - **Branch Specifier**: `*/main` (or your default branch)
   - **Script Path**: `Jenkinsfile`

### 1.6 Configure Webhook for Automatic Pipeline Execution

To make the pipeline run automatically when code is pushed to Gitea:

1. **In Jenkins Pipeline Configuration**:
   - Go to your pipeline job configuration
   - Scroll down to **Build Triggers** section
   - Check **"Poll SCM"** for polling-based triggering
   - OR check **"GitHub hook trigger for GITScm polling"** for webhook-based triggering
   - If using polling, set schedule: `H/5 * * * *` (every 5 minutes)

2. **In Gitea Repository Settings**:
   - Go to your repository in Gitea
   - Click **Settings** > **Webhooks**
   - Click **Add Webhook** > **Gitea**
   - Configure:
     - **Target URL**: `http://*************:8080/gitea-webhook/post`
     - **HTTP Method**: POST
     - **Post Content Type**: application/json
     - **Secret**: (leave empty for now)
     - **Trigger On**: Check **Push Events**
   - Click **Add Webhook**

3. **Test Webhook**:
   - Make a small change to any file in your repository
   - Push the change to Gitea
   - Check Jenkins to see if the pipeline automatically starts

6. Click **Save**

## Step 2: Testing the Complete Pipeline

### 2.1 Test with Your Existing Account

Since you're using your existing account for pushing to Gitea, we'll test both SSH and HTTP access methods:

#### **Option A: HTTP Access (Recommended for Testing)**

1. **Configure HTTP Credentials in Jenkins**:
   - Go to **Manage Jenkins** > **Manage Credentials**
   - Click **System** > **Global credentials** > **Add Credentials**
   - Configure:
     - **Kind**: Username with password
     - **Scope**: Global
     - **ID**: `gitea-http-credentials`
     - **Username**: `paul` (your Gitea username)
     - **Password**: (your Gitea password)
     - **Description**: HTTP credentials for Gitea repository access

2. **Update Pipeline Configuration**:
   - Go to your pipeline job configuration
   - In **Pipeline** section:
     - **Repository URL**: `http://*************:3000/paul/NeuralMotion_Linux_Cli_Worker.git`
     - **Credentials**: Select `gitea-http-credentials`
   - Click **Save**

3. **Test HTTP Access**:
   ```bash
   # Test from Jenkins machine
   curl -u paul:your-password http://*************:3000/paul/NeuralMotion_Linux_Cli_Worker.git/info/refs
   ```

#### **Option B: SSH Access (Alternative)**

1. **Add Your SSH Key to Gitea**:
   - Go to your Gitea profile > **Settings** > **SSH Keys**
   - Add your existing SSH public key
   - Test: `ssh -T gitea@*************`

2. **Configure SSH Credentials in Jenkins**:
   - Go to **Manage Jenkins** > **Manage Credentials**
   - Click **System** > **Global credentials** > **Add Credentials**
   - Configure:
     - **Kind**: SSH Username with private key
     - **Scope**: Global
     - **ID**: `gitea-ssh-credentials`
     - **Username**: `gitea`
     - **Private Key**: Your existing private key
     - **Description**: SSH credentials for Gitea repository access

3. **Update Pipeline Configuration**:
   - Go to your pipeline job configuration
   - In **Pipeline** section:
     - **Repository URL**: `gitea@*************:paul/NeuralMotion_Linux_Cli_Worker.git`
     - **Credentials**: Select `gitea-ssh-credentials`
   - Click **Save**

### 2.2 Manual Pipeline Testing

1. **Trigger Manual Build**:
   - Go to your pipeline job in Jenkins
   - Click **Build Now**
   - Monitor the build progress

2. **Check Build Logs**:
   - Click on the build number
   - Click **Console Output**
   - Verify each stage completes successfully

3. **Expected Pipeline Flow**:
   ```
   ✅ Checkout Code
   ✅ Build NeuralMeter CLI
   ✅ Deploy to System A
   ✅ Verify System A Setup
   ✅ Verify System B Services
   ✅ Run Basic Load Test
   ```

### 2.3 Automated Pipeline Testing

1. **Enable Webhook Triggering**:
   - In Jenkins pipeline configuration:
     - Check **"GitHub hook trigger for GITScm polling"**
   - In Gitea repository settings:
     - Add webhook: `http://*************:8080/gitea-webhook/post`
     - Trigger on: **Push Events**

2. **Test Automated Triggering**:
   ```bash
   # Make a small change to test
   echo "# Test change $(date)" >> README.md
   git add README.md
   git commit -m "Test automated pipeline trigger"
   git push origin main
   ```

3. **Verify Automated Execution**:
   - Check Jenkins dashboard for new build
   - Monitor pipeline execution
   - Verify all stages complete successfully

### 2.4 Troubleshooting Common Issues

#### **Webhook Troubleshooting (Most Common Issue)**

If webhooks aren't triggering Jenkins jobs:

1. **Check Jenkins Webhook Plugin**:
   ```bash
   # Verify Gitea Integration plugin is installed
   # Go to Manage Jenkins > Manage Plugins > Installed
   # Look for: "Gitea Integration" or "GitHub Integration"
   ```

2. **Test Webhook URL Manually**:
   ```bash
   # From Gitea server, test if Jenkins is reachable
   curl -X POST http://*************:8080/gitea-webhook/post
   
   # Should return HTTP 200 or 404 (not connection refused)
   ```

3. **Check Jenkins Webhook Endpoint**:
   ```bash
   # Alternative webhook URLs to try:
   http://*************:8080/github-webhook/post
   http://*************:8080/bitbucket-hook/post
   http://*************:8080/git/notifyCommit?url=http://*************:3000/paul/NeuralMotion_Linux_Cli_Worker.git
   ```

4. **Verify Jenkins Pipeline Configuration**:
   - Go to your pipeline job configuration
   - In **Build Triggers** section:
     - Check **"GitHub hook trigger for GITScm polling"** (even for Gitea)
     - OR check **"Poll SCM"** with schedule: `H/5 * * * *` (every 5 minutes)

5. **Check Gitea Webhook Delivery**:
   - Go to your repository in Gitea
   - Click **Settings** > **Webhooks**
   - Click on your webhook
   - Check **Recent Deliveries** tab
   - Look for failed deliveries and error messages

6. **Test with Polling Instead**:
   ```bash
   # Temporarily disable webhook and use polling
   # In Jenkins pipeline configuration:
   # - Uncheck webhook trigger
   # - Check "Poll SCM"
   # - Set schedule: H/2 * * * * (every 2 minutes)
   # - Save and test
   ```

7. **Check Jenkins Logs**:
   ```bash
   # SSH to Jenkins machine
   ssh your-username@*************
   
   # Check Jenkins logs
   sudo tail -f /var/log/jenkins/jenkins.log
   
   # Or check system logs
   sudo journalctl -u jenkins -f
   ```

8. **Verify Network Connectivity**:
   ```bash
   # Test from Gitea to Jenkins
   ssh your-username@*************
   curl -v http://*************:8080/
   
   # Test from Jenkins to Gitea
   ssh your-username@*************
   curl -v http://*************:3000/
   ```

#### **Gitea Access Issues**:
```bash
# Test HTTP access
curl -I http://*************:3000/paul/NeuralMotion_Linux_Cli_Worker.git

# Test SSH access
ssh -T gitea@*************

# Check Jenkins Git plugin
# Go to Manage Jenkins > System Configuration > Git installations
```

#### **SSH Connection Issues**:
```bash
# Test SSH to System A
ssh -i ~/.ssh/neuro_key neuro@************* 'echo "System A accessible"'

# Test SSH to System B
ssh -i ~/.ssh/neuro_key neuro@************* 'echo "System B accessible"'

# Check SSH agent in Jenkins
# Verify credentials are loaded correctly
```

#### **Build Issues**:
```bash
# Test Go build locally
cd cmd/neuralmeter
go build -o neuralmeter .

# Check Go version on Jenkins
go version

# Verify dependencies
go mod tidy
go mod verify
```

### 2.5 Success Criteria

The pipeline is working correctly when:

1. ✅ **Code checkout** completes without errors
2. ✅ **CLI build** produces executable binary
3. ✅ **Deployment** copies binary to System A successfully
4. ✅ **System verification** shows all services running
5. ✅ **Load test** completes with results
6. ✅ **Automated triggering** works on code push

### 2.6 Analyzing Build Failures

When a build fails, follow these steps to diagnose the issue:

#### **Step 1: Check Build Logs**
1. **Go to your pipeline job in Jenkins**
2. **Click on the failed build number**
3. **Click "Console Output"**
4. **Look for error messages and failed stages**

#### **Step 2: Common Build Failure Points**

**Stage: Checkout Code**
```bash
# Common issues:
# - Repository URL incorrect
# - Credentials not configured
# - Network connectivity issues
# - Branch doesn't exist
```

**Stage: Build NeuralMeter CLI**
```bash
# Common issues:
# - Go not installed on Jenkins
# - Missing dependencies
# - Compilation errors
# - Permission issues
# - CUDA build tag not specified (use: go build -tags cuda)
# - NVML headers not found (install: libnvidia-ml-dev)
# - CGO paths not configured (set CGO_CFLAGS and CGO_LDFLAGS)
```

**Stage: Deploy to System A**
```bash
# Common issues:
# - SSH connection failed
# - Credentials not loaded
# - System A not accessible
# - Permission denied on target
```

**Stage: Verify System A Setup**
```bash
# Common issues:
# - Go not installed on System A
# - GPU drivers not installed
# - SSH key not working
# - User permissions incorrect
```

**Stage: Verify System B Services**
```bash
# Common issues:
# - Nginx not running
# - Services not accessible
# - Network connectivity issues
# - Wrong IP addresses
```

**Stage: Run Basic Load Test**
```bash
# Common issues:
# - CLI binary not executable
# - Test configuration errors
# - Network connectivity between A and B
# - Target services not responding
```

#### **Step 3: Quick Diagnostic Commands**

**Test Jenkins Environment:**
```bash
# SSH to Jenkins machine
ssh your-username@*************

# Check Go installation
go version

# Check if build directory exists
ls -la cmd/neuralmeter/

# Check CUDA/NVML installation
dpkg -l | grep libnvidia-ml
find /usr -name "nvml.h" 2>/dev/null

# Test CUDA build
cd cmd/neuralmeter
export CGO_CFLAGS="-I/usr/include -I/usr/local/cuda/include"
export CGO_LDFLAGS="-L/usr/lib/x86_64-linux-gnu -L/usr/local/cuda/lib64"
go build -tags cuda -o neuralmeter-cuda .
```

**Test System A Connectivity:**
```bash
# From Jenkins machine
ssh -i ~/.ssh/neuro_key neuro@************* 'go version'
ssh -i ~/.ssh/neuro_key neuro@************* 'nvidia-smi'
```

**Test System B Services:**
```bash
# From Jenkins machine
ssh -i ~/.ssh/neuro_key neuro@************* 'systemctl status nginx'
ssh -i ~/.ssh/neuro_key neuro@************* 'curl -s http://localhost/'
```

#### **Step 4: Fix Common Issues**

**If Go is missing on Jenkins:**
```bash
# Install Go on Jenkins machine
sudo apt update
sudo apt install golang-go
```

**If CUDA build fails on Jenkins:**
```bash
# Install NVML development headers
sudo apt install libnvidia-ml-dev

# Install CUDA toolkit (if needed)
sudo apt install nvidia-cuda-toolkit

# Set CGO environment variables
export CGO_CFLAGS="-I/usr/include -I/usr/local/cuda/include"
export CGO_LDFLAGS="-L/usr/lib/x86_64-linux-gnu -L/usr/local/cuda/lib64"

# Build with CUDA tag
go build -tags cuda -o neuralmeter-cuda cmd/neuralmeter/main.go
```

**If SSH credentials fail:**
```bash
# Test SSH connection manually
ssh -i ~/.ssh/neuro_key neuro@************* 'echo "SSH test"'
```

**If System A/B services are down:**
```bash
# Restart services on System B
ssh -i ~/.ssh/neuro_key neuro@************* 'sudo systemctl restart nginx'
```

#### **Step 5: Re-run Failed Build**

After fixing the issue:
1. **Go to your pipeline job**
2. **Click "Build Now"**
3. **Monitor the build progress**
4. **Check console output for success**

**Note**: Using HTTP access is simpler than SSH. If the repository is private, you'll need to add username/password credentials:

1. **Configure Git HTTP Credentials in Jenkins** (if repository is private):
   - Go to **Manage Jenkins** > **Manage Credentials**
   - Click **System** > **Global credentials** > **Add Credentials**
   - Configure username/password credentials:
     - **Kind**: Username with password
     - **Scope**: Global
     - **ID**: `gitea-http-credentials`
     - **Username**: `neuro` (or your Gitea username)
     - **Password**: (your Gitea password)
     - **Description**: HTTP credentials for Gitea repository access

2. **Update Pipeline Configuration** (if repository is private):
   - **Credentials**: Select the HTTP credentials (`gitea-http-credentials`) created above

**Note**: HTTP access is much simpler than SSH. The repository URL uses HTTP protocol and Jenkins can access it directly without complex SSH key setup.

## Step 3: System A Setup (NeuralMeter CLI Execution Machine with GPU)

### 3.1 Run Setup Script

```bash
# SSH to System A as neuro user
ssh -i ~/.ssh/neuro_key neuro@*************

# Download and run setup script
wget http://*************:3000/paul/NeuralMotion_Linux_Cli_Worker/raw/branch/main/scripts/setup-system-a.sh
chmod +x setup-system-a.sh
sudo ./setup-system-a.sh
```

### 3.2 Verify Setup

After running the script, verify the following:

```bash
# Check GPU detection and drivers
nvidia-smi  # For NVIDIA GPUs
# OR
rocminfo    # For AMD GPUs

# Check CUDA installation (NVIDIA)
nvcc --version

# Check Go installation (for running CLI)
go version

# Check neuro user
id neuro

# Check SSH directory
ls -la /home/<USER>/.ssh/
```

### 3.3 Verify SSH Keys and Sudo Access (from Jenkins Machine)

**Note**: These commands should be run from the Jenkins machine to verify connectivity to System A.

```bash
# Test SSH connectivity from Jenkins to System A (keys should already be configured from Step 1.3)
ssh -i ~/.ssh/neuro_key neuro@************* 'echo "SSH connection verified"'

# Verify sudo access configuration on System A
# SSH to System A as neuro user and test sudo access
ssh -i ~/.ssh/neuro_key neuro@*************
sudo whoami
# Should return "root" if sudo access is working

# Note: The setup script already configures sudo access automatically
```

## Step 4: System B Setup (Target Services)

### 4.1 Run Setup Script

```bash
# SSH to System B as neuro user
ssh -i ~/.ssh/neuro_key neuro@*************

# Download and run setup script
wget http://*************:3000/paul/NeuralMotion_Linux_Cli_Worker/raw/branch/main/scripts/setup-system-b.sh
chmod +x setup-system-b.sh
sudo ./setup-system-b.sh
```

### 4.2 Verify Target Services Setup

After running the script, verify the target services environment:

```bash
# Check web server status (if applicable)
sudo systemctl status nginx
# OR
sudo systemctl status apache2

# Check service ports
netstat -tlnp | grep :80
netstat -tlnp | grep :443

# Check system resources
free -h
df -h /

# Check service directories
ls -la /var/www/
```

### 4.3 Verify SSH Keys and Sudo Access (from Jenkins Machine)

**Note**: These commands should be run from the Jenkins machine to verify connectivity to System B.

```bash
# Test SSH connectivity from Jenkins to System B (keys should already be configured from Step 1.3)
ssh -i ~/.ssh/neuro_key neuro@************* 'echo "SSH connection verified"'

# Verify sudo access configuration on System B
# SSH to System B as neuro user and test sudo access
ssh -i ~/.ssh/neuro_key neuro@*************
sudo whoami
# Should return "root" if sudo access is working

# Note: The setup script already configures sudo access automatically
```

## Step 5: Update Jenkins Pipeline Configuration

### 5.1 Update Environment Variables

Edit the `Jenkinsfile` and update the environment variables:

```groovy
environment {
    SYSTEM_A_HOST = '*************'
    SYSTEM_B_HOST = '*************'
    JENKINS_CREDENTIALS_ID = 'ssh-credentials'
}
```

### 5.2 Run Verification Pipeline

1. Go to the Jenkins pipeline job
2. Click **Build Now**
3. Monitor the build output for each stage

## Step 6: Verification Checklist

### 6.1 Jenkins Verification

- [ ] Jenkins is accessible at http://*************:8080/
- [ ] Required plugins are installed
- [ ] SSH credentials are configured
- [ ] Pipeline job is created and configured

### 6.2 System A Verification

- [ ] GPU drivers are installed and detected (NVIDIA/AMD/Intel)
- [ ] CUDA/ROCm/OpenCL is installed (as appropriate)
- [ ] Go is installed and working (for running CLI)
- [ ] Neuro user exists with proper permissions
- [ ] SSH access is configured for neuro user
- [ ] CLI execution directories are created with proper permissions

### 6.3 System B Verification

- [ ] Web servers are installed and running (nginx, apache, etc.)
- [ ] Target services are accessible on expected ports
- [ ] Service directories are properly configured
- [ ] System resources are adequate for target services
- [ ] Network connectivity is available for load testing
- [ ] Services are configured to handle load test traffic

### 6.4 Connectivity Verification

- [ ] Jenkins can SSH to System A
- [ ] Jenkins can SSH to System B
- [ ] Pipeline stages execute successfully
- [ ] All verification commands return expected output

## Security Considerations

### Sudo Access for Jenkins User

The jenkins user needs sudo access to perform system-level operations during builds and tests. There are two approaches:

#### **Production-Ready Sudo Access (Password Required)**
```bash
# Allow neuro user sudo access with password (production standard)
neuro ALL=(ALL) ALL
```

**Security Recommendations:**
- Always require password for sudo access in production
- Use Jenkins credential store for password management
- Regularly rotate neuro user passwords
- Monitor sudo usage logs
- Consider using Docker containers for isolated build environments

### SSH Key Security
- Use strong SSH keys (RSA 4096-bit or Ed25519)
- Regularly rotate SSH keys
- Restrict SSH access to specific IP addresses when possible
- Use SSH key passphrases for additional security

## Troubleshooting

### Common Issues

1. **SSH Connection Failed**
   - Verify SSH keys are properly configured
   - Check firewall settings
   - Ensure neuro user exists on target systems

2. **Target Services Not Accessible**
   - Verify web servers are running
   - Check port configurations
   - Review service logs

3. **Build Dependencies Missing**
   - Run setup scripts with sudo privileges
   - Check package manager repositories
   - Verify internet connectivity

4. **Permission Denied**
   - Ensure neuro user has proper permissions
   - Check directory ownership
   - Verify SSH key permissions

### Log Locations

- **Jenkins**: `/var/log/jenkins/jenkins.log`
- **System A**: `/home/<USER>/builds/`
- **System B**: `/home/<USER>/logs/`

## Next Steps

After successful verification:

1. **Update Story 0.1**: Mark all tasks as completed
2. **Proceed to Story 0.2**: Verify basic CLI functionality
3. **Document Issues**: Report any discrepancies or problems found
4. **Update Documentation**: Modify setup guides based on findings

## Support

For issues or questions:

1. Check the troubleshooting section above
2. Review Jenkins and system logs
3. Consult the architecture documentation
4. Report issues in the project repository 