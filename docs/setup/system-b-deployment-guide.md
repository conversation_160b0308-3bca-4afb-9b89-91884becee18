# System B Deployment Guide

## Overview

This guide provides instructions for deploying a fully functional System B test environment for comprehensive NeuralMeter CLI testing. System B serves as the target services that System A (NeuralMeter CLI) will load test.

## Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        System B - Target Services               │
├─────────────────────────────────────────────────────────────────┤
│ Load Balancer (HAProxy)                                        │
│ ├── HTTP/HTTPS (80/443) ──────┬─────────────────────────────────┤
│ ├── WebSocket (8080)          │                                 │
│ └── Monitoring (9090)         │                                 │
├───────────────────────────────┼─────────────────────────────────┤
│ Application Services          │                                 │
│ ├── nginx (Reverse Proxy)     │ Web Server Layer               │
│ ├── Backend API (8011-8013)   │ Application Layer              │
│ ├── WebSocket Server (8080)   │ Real-time Layer                │
│ ├── Health Service (8021)     │ Monitoring Layer               │
│ └── Prometheus (9090)         │ Observability Layer            │
└─────────────────────────────────────────────────────────────────┘
```

## Prerequisites

- Ubuntu 22.04 LTS or similar Linux distribution
- Root access for system configuration
- Minimum 8GB RAM, 4 CPU cores
- Network connectivity for package installation

## Quick Deployment

### Option 1: Complete Automated Deployment

```bash
# Clone the repository and navigate to scripts
cd /path/to/neuralmeter-project/scripts

# Run the complete deployment script (requires root)
sudo ./deploy-system-b-complete.sh
```

This script will:
1. Set up SSL/TLS certificates
2. Configure HAProxy load balancer
3. Deploy Go-based API services
4. Deploy WebSocket services
5. Deploy Python health service
6. Configure Prometheus monitoring
7. Run comprehensive validation

### Option 2: Step-by-Step Deployment

If you prefer manual control over each step:

```bash
# 1. SSL/TLS Certificate Setup
sudo ./setup-certificates.sh

# 2. HAProxy Configuration
sudo ./configure-haproxy.sh

# 3. Deploy API Services
sudo ./deploy-api-services.sh

# 4. Deploy WebSocket Services
sudo ./deploy-websocket-services.sh

# 5. Deploy Health Service
sudo ./deploy-health-service.sh

# 6. Configure Prometheus
sudo ./configure-prometheus.sh

# 7. Validate Complete Setup
sudo ./validate-system-b.sh
```

## Service Endpoints

After successful deployment, the following endpoints will be available:

### Load Balancer (HAProxy)
- **HTTP**: `http://system-b-ip/` (port 80, redirects to HTTPS)
- **HTTPS**: `https://system-b-ip/` (port 443)
- **Stats**: `http://system-b-ip:8404/stats`

### Web Servers (Nginx)
- **Server 1**: `http://system-b-ip:8001/`
- **Server 2**: `http://system-b-ip:8002/`
- **Server 3**: `http://system-b-ip:8003/`

### API Services (Go)
- **API 1**: `http://system-b-ip:8011/api/`
- **API 2**: `http://system-b-ip:8012/api/`
- **API 3**: `http://system-b-ip:8013/api/`

### WebSocket Services (Go)
- **WebSocket 1**: `ws://system-b-ip:8080/ws`
- **WebSocket 2**: `ws://system-b-ip:8081/ws`

### Health Monitoring (Python)
- **Basic Health**: `http://system-b-ip:8021/health`
- **Detailed Health**: `http://system-b-ip:8021/health/detailed`
- **System Health**: `http://system-b-ip:8021/health/system`
- **Services Health**: `http://system-b-ip:8021/health/services`

### Monitoring (Prometheus)
- **Web UI**: `http://system-b-ip:9090`
- **Metrics**: `http://system-b-ip:9090/metrics`
- **Health**: `http://system-b-ip:9090/-/healthy`

## Testing System B

### Basic Connectivity Test

```bash
# Test basic web endpoint
curl -f http://system-b-ip:8001/

# Test API endpoint
curl -f http://system-b-ip:8011/api/health

# Test WebSocket health
curl -f http://system-b-ip:8080/health

# Test health service
curl -f http://system-b-ip:8021/health

# Test Prometheus
curl -f http://system-b-ip:9090/-/healthy
```

### Load Testing from System A

Once System B is deployed, configure System A (NeuralMeter CLI) to target System B:

```yaml
# Example test plan for System A
name: "System A to System B Load Test"
description: "Load test targeting System B services"
version: "1.0"

duration: "60s"
concurrency: 10
ramp_up: "10s"

scenarios:
  - name: "system_b_load_test"
    description: "Load test targeting System B services"
    weight: 100
    requests:
      - name: "homepage_load"
        method: "GET"
        url: "http://system-b-ip/"
        headers:
          User-Agent: "NeuralMeter-SystemA-to-SystemB/1.0"
        timeout: "10s"
      - name: "api_load"
        method: "GET"
        url: "http://system-b-ip/api/data"
        timeout: "10s"

global:
  timeout: "10s"

output:
  format: ["json"]
  detailed: true
```

## Service Management

### Systemd Services

All services are managed via systemd:

```bash
# Check service status
systemctl status neuralmeter-api-8011
systemctl status neuralmeter-api-8012
systemctl status neuralmeter-api-8013
systemctl status neuralmeter-ws-8080
systemctl status neuralmeter-ws-8081
systemctl status neuralmeter-health
systemctl status haproxy
systemctl status nginx
systemctl status prometheus

# Restart services
systemctl restart neuralmeter-api-8011
systemctl restart neuralmeter-ws-8080
systemctl restart neuralmeter-health

# View logs
journalctl -u neuralmeter-api-8011 -f
journalctl -u neuralmeter-health -f
```

### Configuration Files

- **HAProxy**: `/etc/haproxy/haproxy.cfg`
- **Nginx**: `/etc/nginx/sites-available/neuralmeter`
- **SSL Certificates**: `/etc/neuralmeter/ssl/`
- **Prometheus**: `/etc/prometheus/prometheus.yml`
- **Service Binaries**: `/opt/neuralmeter/bin/`

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check what's using a port
   ss -tuln | grep :8011
   
   # Kill process using port
   sudo fuser -k 8011/tcp
   ```

2. **Service Won't Start**
   ```bash
   # Check service logs
   journalctl -u neuralmeter-api-8011 --no-pager
   
   # Check binary permissions
   ls -la /opt/neuralmeter/bin/
   ```

3. **SSL Certificate Issues**
   ```bash
   # Verify certificate
   openssl x509 -in /etc/neuralmeter/ssl/server.crt -text -noout
   
   # Check certificate permissions
   ls -la /etc/neuralmeter/ssl/
   ```

4. **Go Module Issues**
   ```bash
   # If Go services fail to build
   cd /tmp
   go mod tidy
   go mod download
   ```

### Validation Script

Run the comprehensive validation script to check all components:

```bash
sudo ./validate-system-b.sh
```

This script performs 50+ tests covering:
- System packages installation
- SSL/TLS certificate configuration
- Service configurations and status
- Network port availability
- Endpoint connectivity
- File permissions and ownership

## Performance Tuning

### System Limits

For high-load testing, adjust system limits:

```bash
# Increase file descriptor limits
echo '* soft nofile 100000' >> /etc/security/limits.conf
echo '* hard nofile 100000' >> /etc/security/limits.conf

# Increase network limits
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.core.netdev_max_backlog = 5000' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 8192' >> /etc/sysctl.conf
sysctl -p
```

### Service Tuning

- **HAProxy**: Adjust `maxconn` values in `/etc/haproxy/haproxy.cfg`
- **Nginx**: Tune `worker_processes` and `worker_connections`
- **Go Services**: Set `GOMAXPROCS` environment variable in systemd services

## Monitoring

### Prometheus Metrics

Access Prometheus at `http://system-b-ip:9090` to monitor:
- Service availability and response times
- System resource usage (CPU, memory, disk)
- Network traffic and connection counts
- Custom NeuralMeter metrics

### Health Endpoints

The health service provides comprehensive monitoring:
- System resource utilization
- Service connectivity status
- Load averages and performance metrics

## Security Considerations

- SSL/TLS certificates are self-signed for testing purposes
- Services run under dedicated `neuralmeter` user
- Firewall rules should be configured for production use
- Consider using proper CA-signed certificates for production

## Next Steps

1. **Configure System A**: Set up NeuralMeter CLI to target System B endpoints
2. **Run Load Tests**: Execute comprehensive load tests from System A
3. **Monitor Performance**: Use Prometheus and health endpoints to monitor system performance
4. **Scale Testing**: Adjust concurrency and load patterns based on System B capacity

## Support

For issues or questions:
1. Check the validation script output
2. Review service logs via `journalctl`
3. Verify network connectivity between System A and System B
4. Ensure all prerequisites are met