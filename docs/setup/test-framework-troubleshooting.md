# Test Framework Troubleshooting Guide

## Common Issues and Solutions

### 1. "No such file or directory" for test-orchestrator.log

**Problem**: `tee: ./test-results/test-orchestrator.log: No such file or directory`

**Cause**: Test results directory doesn't exist when orchestrator starts

**Solution**: The orchestrator now creates the directory automatically, but if you see this:
```bash
# On System A, manually create the directory
ssh neuro@************* 'mkdir -p /home/<USER>/test-results'
```

### 2. Test orchestrator running on Jenkins instead of System A

**Problem**: Tests fail because orchestrator runs on Jenkins, not System A

**Cause**: Missing deployment of test framework to System A

**Solution**: The Jenkinsfile now includes a "Deploy Test Framework" stage that:
- Copies `test-orchestrator.sh` to System A
- Copies all test suites to System A
- Sets proper permissions

### 3. CLI binary not found

**Problem**: `CLI binary not found at: /home/<USER>/cli/neuralmeter`

**Solutions**:
```bash
# Check if binary exists
ssh neuro@************* 'ls -la /home/<USER>/cli/neuralmeter'

# Check permissions
ssh neuro@************* 'chmod +x /home/<USER>/cli/neuralmeter'

# Test binary
ssh neuro@************* '/home/<USER>/cli/neuralmeter --version'
```

### 4. System B connectivity issues

**Problem**: Cannot reach System B from System A

**Solutions**:
```bash
# Test connectivity from System A
ssh neuro@************* 'ping -c 3 *************'

# Test HTTP service
ssh neuro@************* 'curl -s http://*************/'

# Check nginx on System B
ssh neuro@************* 'sudo systemctl status nginx'
```

### 5. Test suite script not executable

**Problem**: `Permission denied` when running test suites

**Solution**:
```bash
# Fix permissions on System A
ssh neuro@************* 'chmod +x /home/<USER>/test/test-suites/*.sh'
ssh neuro@************* 'chmod +x /home/<USER>/test/test-orchestrator.sh'
```

## Debugging Steps

### 1. Run Framework Test First
```bash
# Test the framework setup
./test/test-orchestrator.sh test-framework
```

### 2. Check Environment Variables
```bash
# On System A, verify environment
ssh neuro@************* 'env | grep -E "(SYSTEM_|CLI_|TEST_)"'
```

### 3. Manual Test Execution
```bash
# Run individual test suite manually
ssh neuro@************* '
    cd /home/<USER>
    export SYSTEM_A_HOST=************* &&
    export SYSTEM_B_HOST=************* &&
    export CLI_BINARY_PATH=/home/<USER>/cli/neuralmeter &&
    export SSH_USER=neuro &&
    export TEST_RESULTS_DIR=/home/<USER>/test-results &&
    ./test/test-suites/connectivity.sh
'
```

### 4. Check File Structure
```bash
# Verify all files are deployed
ssh neuro@************* 'find /home/<USER>/test -type f -name "*.sh" -exec ls -la {} \;'
```

## Test Framework Structure

```
/home/<USER>/
├── test/
│   ├── test-orchestrator.sh          # Main orchestrator
│   ├── test-suites/                  # Individual test suites
│   │   ├── test-framework.sh         # Framework validation
│   │   ├── connectivity.sh           # Connectivity tests
│   │   ├── load-basic.sh            # Basic load tests
│   │   └── performance.sh           # Performance tests
│   └── test-config.json             # Configuration
├── cli-scripts/                     # Legacy CLI scripts
│   ├── quick-connectivity-test.sh
│   └── system-a-to-system-b-test.sh
├── cli/
│   └── neuralmeter                  # CLI binary
└── test-results/                    # Test output
    ├── test-orchestrator.log
    └── [suite-name]/               # Per-suite results
```

## Jenkins Pipeline Stages

1. **Deploy Test Framework**: Copies test files to System A
2. **Run CLI Tests**: 
   - First runs `test-framework` suite to validate setup
   - Then runs main test suites (`connectivity`, `load-basic`)
3. **Collect Results**: Copies results back to Jenkins

## Quick Fixes

### Reset Test Environment
```bash
# Clean and recreate test environment on System A
ssh neuro@************* '
    rm -rf /home/<USER>/test /home/<USER>/test-results
    mkdir -p /home/<USER>/test/test-suites /home/<USER>/test-results
'
```

### Manual Framework Deployment
```bash
# If Jenkins deployment fails, deploy manually
scp -r test/ neuro@*************:/home/<USER>/
ssh neuro@************* 'chmod +x /home/<USER>/test/test-orchestrator.sh /home/<USER>/test/test-suites/*.sh'
```

### Test Individual Components
```bash
# Test CLI binary
ssh neuro@************* '/home/<USER>/cli/neuralmeter --help'

# Test System B connectivity
ssh neuro@************* 'curl -s http://*************/health'

# Test GPU detection
ssh neuro@************* 'nvidia-smi'
```

This troubleshooting guide should help resolve the most common issues with the test framework deployment and execution.