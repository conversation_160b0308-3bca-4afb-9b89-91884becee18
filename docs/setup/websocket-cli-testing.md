# WebSocket Streaming CLI Testing Guide

## Overview

This guide explains how to test WebSocket streaming functionality using the NeuralMeter CLI tool with proper YAML test plans.

## Prerequisites

1. **System B WebSocket Services**: Ensure WebSocket servers are running on System B

   ```bash
   # Deploy WebSocket addon to System B
   sudo ./scripts/deploy-websocket-addon.sh

   # Verify WebSocket services are running
   systemctl status neuralmeter-ws-8080
   systemctl status neuralmeter-ws-8081
   ```

2. **NeuralMeter CLI**: Ensure the CLI is built and available on System A
   ```bash
   ./neuralmeter --version
   ```

## WebSocket Streaming Test Commands

### Basic WebSocket Streaming Test

```bash
# Test basic WebSocket streaming functionality
./neuralmeter run --stream-results ws://*************:8080/ws test/basic-websocket-test.yaml
```

### Comprehensive WebSocket Streaming Test

```bash
# Test comprehensive WebSocket streaming with multiple scenarios
./neuralmeter run --stream-results ws://*************:8080/ws test/websocket-streaming-test.yaml
```

### Alternative WebSocket Port Testing

```bash
# Test WebSocket streaming on port 8081
./neuralmeter run --stream-results ws://*************:8081/ws test/websocket-streaming-test.yaml
```

## Test Plan Files

### basic-websocket-test.yaml

- **Purpose**: Simple WebSocket streaming verification
- **Duration**: 10 seconds
- **Concurrency**: 1 user
- **Target**: Basic API health endpoint with streaming

### websocket-streaming-test.yaml

- **Purpose**: Comprehensive WebSocket streaming test
- **Duration**: 15 seconds
- **Concurrency**: 2 users
- **Target**: Multiple API endpoints with real-time streaming

## Expected Behavior

When WebSocket streaming is working correctly, you should see:

1. **Connection Establishment**: CLI connects to WebSocket endpoint
2. **Real-time Updates**: Live streaming of test metrics during execution
3. **Streaming Data**: Real-time updates including:
   - Current RPS (Requests Per Second)
   - Response times
   - Active connections
   - Error counts
   - Progress indicators

## Verification Steps

1. **Start the test**:

   ```bash
   ./neuralmeter run --stream-results ws://*************:8080/ws test/websocket-streaming-test.yaml
   ```

2. **Observe streaming output**: You should see real-time updates during test execution

3. **Check final results**: Test should complete with summary statistics

4. **Verify WebSocket connection**: CLI should successfully connect to and stream through WebSocket

## Troubleshooting

### WebSocket Connection Failed

```bash
# Check if WebSocket service is running
systemctl status neuralmeter-ws-8080

# Check if port is listening
ss -tuln | grep :8080

# Test WebSocket server directly
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" http://*************:8080/
```

### No Streaming Data

- Verify the `--stream-results` flag is used
- Check WebSocket endpoint URL is correct
- Ensure test plan has streaming configuration

### Connection Timeout

- Verify System B WebSocket services are running
- Check network connectivity between System A and System B
- Verify firewall settings allow WebSocket connections

## Manual Verification Commands

```bash
# Verify CLI can connect to WebSocket endpoint
./neuralmeter validate test/websocket-streaming-test.yaml

# Test without streaming first
./neuralmeter run test/basic-websocket-test.yaml

# Then test with streaming
./neuralmeter run --stream-results ws://*************:8080/ws test/basic-websocket-test.yaml
```

## Success Criteria

✅ CLI successfully connects to WebSocket endpoint
✅ Real-time streaming data is displayed during test execution  
✅ Test completes successfully with final summary
✅ WebSocket connection is properly closed after test
✅ No connection errors or timeouts occur
