---
# NeuralMeter Linux CLI Worker

## Overview
NeuralMeter is a high-performance, GPU-accelerated load-testing engine.  
This repository contains the **Linux-only** worker binary (`neuralmeter-cuda`) which automatically detects and utilises NVIDIA CUDA, AMD ROCm, or generic OpenCL GPUs at runtime.

## Prerequisites
- **Go 1.22+** ( `https://go.dev/dl/` )  
- **CUDA 11.4+** _or_ **ROCm 5.x** _or_ a recent **OpenCL** runtime (the binary auto-detects whichever is present)  
- `git`, `make`, and a standard build tool-chain (GCC / Clang)

> ⚠️  _No build-tags are required – the worker compiles everything into a single binary and chooses the best backend at runtime._

## Building the CLI
```bash
# 1. Clone the repo (SSH or HTTPS)
$ git clone <your-private-repo-url> neuralmeter && cd neuralmeter

# 2. Compile the worker (produces ./neuralmeter-cuda)
#    Re-run this command **every time you pull new changes** so your binary
#    includes the latest fixes.
$ go build -o neuralmeter-cuda cmd/neuralmeter/main.go

# 3. Verify the binary and show help
$ ./neuralmeter-cuda --help
```

A successful build on a CUDA-equipped system should detect your GPU automatically:
```bash
$ ./neuralmeter-cuda gpu list
Available GPU Devices:
  Device 0:
    Name: NVIDIA GeForce RTX 3060
    Vendor: NVIDIA
    Type: CUDA (Arch: Ampere)
    Memory: 12.0 GB total / 11.6 GB free (3 % util)
    Compute Capability: 8.6 – 1920 SMs
    Memory Bus: 256-bit @ 7000 MHz
    Core Clock: 1920 MHz
    Power Usage: 25.0 W
    GPU Utilisation: 2 %
    Available: true
```

## Common Commands
The CLI is organised with **Cobra**; top-level help is available via `--help` on any sub-command.

### GPU Commands
| Command | Purpose |
|---------|---------|
| `neuralmeter-cuda gpu list` | Enumerate all detected GPUs and basic specs |
| `neuralmeter-cuda gpu status <device-id>` | Live utilisation / power / clocks for a single GPU |

### Server Commands
| Command | Purpose |
|---------|---------|
| `./[--foreground]` | Start the worker in daemon or foreground mode |
| `neuralmeter-cuda server stop` | Stop the daemon and remove the PID file |
| `neuralmeter-cuda server restart` | Convenient wrapper around stop → start |
| `neuralmeter-cuda server status` | Show PID, uptime and port information |

### Test Execution
The worker executes **Test Plans** (YAML / JSON).  Simplest invocation:
```bash
$ ./neuralmeter-cuda run --plan ./plans/example.yaml
```
Internally this will:
1. Parse & validate the test plan  
2. Select the best GPU according to `gpu.*` config  
3. Stream real-time results back to the controller (gRPC)

## Configuration
The global config file lives at `~/.config/neuralmeter/config.yaml` and is created on first launch.  
Important sections:
```yaml
gpu:
  enabled: true           # master on/off switch
  prefer_cuda: true       # true = CUDA > ROCm > OpenCL
  min_memory_gb: 4        # reject GPUs with <4 GiB
  min_compute_capability: # CUDA SM version
    major: 6
    minor: 1
  monitoring_interval: 5s # metrics polling
```

Run `neuralmeter-cuda config show` to dump the current effective configuration.

## Docker
For reproducible runs, an example **CUDA 12.4** container is provided:
```bash
$ docker compose --profile cuda up --build
```
Inside the container:
```bash
root@worker$ go build -o neuralmeter-cuda cmd/neuralmeter/main.go
root@worker$ ./neuralmeter-cuda gpu list
```
Make sure your host exposes `/dev/nvidia*` to the container (`--gpus all`).

## Troubleshooting
- **Build fails with `libcudart.so` not found** → ensure the CUDA Toolkit is installed _and_ `LD_LIBRARY_PATH` contains the runtime path.
- **`gpu list` shows devices but values are zero** → check that the user inside Docker has permission to query `nvidia-smi` (add to `video` group).
- **Unsupported GPU** → fallback to OpenCL is automatic if the driver stack is present.

---
© 2025 NeuralMotion Inc.
--- 

## Task 6 – End-to-End Validation Matrix

The following table shows exactly **how each sub-task of Task 6 is verified using the NeuralMeter CLI against the native remote test-server** you install with `setup_http_service.sh` and `setup_grpc_service.sh`.

| # | Sub-Task | CLI-Driven Test Sequence | Success Criteria |
|---|----------|--------------------------|------------------|
| 1 | CLI Command Structure | `neuralmeter-cuda --help`, `version`, `gpu help` | Commands exit 0, help text printed |
| 2 | Server Daemon Mode | `server start --daemon`, `server status`, `server stop` | PID lifecycle works, clean shutdown |
| 3 | Remote API Server | Submit real plan via CLI: `run --plan test/plans/http_static.yaml --controller http://localhost:8080/api` | Job accepted; `/health` 200 OK |
| 4 | Job Queue Integration | Submit 3 plans back-to-back and watch with `jobs list --watch` | Queue drains to zero, no drops |
| 5 | Worker Registration | Run controller (`controller run --port 9000`); worker registers & heartbeats | Worker visible in controller list |
| 6 | Test Execution | Submit `mixed_http_grpc.yaml`; worker executes vs SUT | Result bundle stored & downloadable |
| 7 | Result Streaming | `jobs stream <id> --format json` during run | Frames arrive < 1 s latency |
| 8 | GPU Command Integration | `gpu list/benchmark --server localhost:8080` | Devices shown; GFLOPs > 0 |
| 9 | Config Management | `config show`, `config validate` (with bad YAML) | Good config passes, bad fails |
| 10 | Output & Logging | `--output json` / `table`, check `/var/log/neuralmeter/worker.log` | Formats valid, errors logged |

### Example Plans & Scripts
| Plan file | Purpose |
|-----------|---------|
| `test/plans/http_static.yaml` | GET `http://SUT:8080/page.html` latency/RPS |
| `test/plans/http_json_post.yaml` | POST `http://SUT:8080/api/echo` body perf |
| `test/plans/grpc_echo.yaml` | gRPC streaming Echo perf |
| `test/plans/mixed_http_grpc.yaml` | Mixed workload stress test |

CLI helpers live in `test/cli-scripts/`:
* `gpu_list.sh` – GPU sanity check
* `benchmark_http.sh` – runs `http_static.yaml`
* (add more like `benchmark_grpc.sh`, `benchmark_mixed.sh` as you create plans)

Run the server setup scripts on a clean Ubuntu box, start the NeuralMeter daemon locally, then execute the matrix above to sign-off Task 6. 