name: "Simple Test Plan"
description: "A simple test plan for testing the macOS CLI"
version: "1.0"

duration: "30s"
concurrency: 10
ramp_up: "5s"

scenarios:
  - name: "basic_http_test"
    description: "Basic HTTP GET test"
    weight: 100
    requests:
      - name: "get_homepage"
        method: "GET"
        url: "https://httpbin.org/get"
        headers:
          User-Agent: "NeuralMeter-macOS/1.0"
        timeout: "10s"
        
global:
  timeout: "10s"

output:
  format: ["json"]
  detailed: true 