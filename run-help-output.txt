Execute a load test plan from a YAML configuration file.
The test plan defines scenarios, endpoints, and performance requirements.

Example:
  neuralmeter-windows run test-plan.yaml
  neuralmeter-windows run --config custom.yaml test-plan.yaml

Usage:
  neuralmeter-windows run [test-plan.yaml] [flags]

Flags:
      --duration duration    test duration (default 30s)
      --gpu                  enable DirectML GPU acceleration (default true)
  -h, --help                 help for run
      --output-file string   output file for results
      --rps int              requests per second (default 100)
      --workers int          number of worker goroutines (default 10)

Global Flags:
      --config string      config file (default is ./neuralmeter.yaml)
      --log-level string   log level (debug, info, warn, error) (default "info")
      --output string      output format (text, json) (default "text")
  -q, --quiet              quiet output
  -v, --verbose            verbose output
