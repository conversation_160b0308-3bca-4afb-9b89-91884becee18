version: "1.0"
name: "Sample API Load Test"
description: "A simple load test for testing the CLI interface"
duration: "10s"
concurrency: 5
ramp_up: "2s"

global:
  base_url: "https://httpbin.org"
  headers:
    User-Agent: "NeuralMeterGo/0.1.0"
  timeout: "5s"

scenarios:
  - name: "GET Requests"
    description: "Test basic GET requests"
    weight: 60
    requests:
      - name: "Get IP"
        method: GET
        url: "/ip"
        assertions:
          - type: status_code
            value: 200
            description: "Should return 200 OK"
      - name: "Get User Agent"
        method: GET
        url: "/user-agent"
        assertions:
          - type: status_code
            value: 200

  - name: "POST Requests"
    description: "Test POST requests with data"
    weight: 40
    requests:
      - name: "Echo POST"
        method: POST
        url: "/post"
        headers:
          Content-Type: "application/json"
        body:
          message: "Hello from NeuralMeterGo"
          timestamp: "{{timestamp}}"
        assertions:
          - type: status_code
            value: 200
          - type: response_time
            operator: lt
            value: 2000
            description: "Response time should be under 2 seconds"

variables:
  - name: "timestamp"
    type: "faker"
    method: "unix_time"

output:
  format: ["json"]
  detailed: true
  metrics: ["response_time", "throughput", "error_rate", "p95"] 