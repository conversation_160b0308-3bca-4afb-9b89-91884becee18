# NeuralMeterGo Cross-Platform Build Configuration

# Default target
.PHONY: all
all: build

# Basic build without any GPU acceleration
.PHONY: build
build:
	go build -o bin/neuralmeter ./cmd/neuralmeter

# Build with CUDA support (Linux/Windows)
.PHONY: build-cuda
build-cuda:
	go build -tags cuda -o bin/neuralmeter ./cmd/neuralmeter

# Build with OpenCL support (cross-platform)
.PHONY: build-opencl
build-opencl:
	go build -tags opencl -o bin/neuralmeter ./cmd/neuralmeter

# Build with Metal support (macOS only)
.PHONY: build-metal
build-metal:
	go build -tags metal -o bin/neuralmeter ./cmd/neuralmeter

# Build with CUDA and OpenCL support
.PHONY: build-cuda-opencl
build-cuda-opencl:
	go build -tags "cuda opencl" -o bin/neuralmeter ./cmd/neuralmeter

# Build with all GPU acceleration (platform-dependent)
.PHONY: build-all-gpu
build-all-gpu:
ifeq ($(shell uname),Darwin)
	go build -tags "cuda opencl metal" -o bin/neuralmeter ./cmd/neuralmeter
else ifeq ($(shell uname),Linux)
	go build -tags "cuda opencl" -o bin/neuralmeter ./cmd/neuralmeter
else
	go build -tags "cuda opencl" -o bin/neuralmeter ./cmd/neuralmeter
endif

# Test targets
.PHONY: test
test:
	go test ./...

.PHONY: test-cuda
test-cuda:
	go test -tags cuda ./...

.PHONY: test-opencl
test-opencl:
	go test -tags opencl ./...

.PHONY: test-metal
test-metal:
	go test -tags metal ./...

# Cross-platform builds
.PHONY: build-linux
build-linux:
	GOOS=linux GOARCH=amd64 go build -tags "cuda opencl" -o bin/neuralmeter-linux ./cmd/neuralmeter

.PHONY: build-windows
build-windows:
	GOOS=windows GOARCH=amd64 go build -tags "cuda opencl" -o bin/neuralmeter-windows.exe ./cmd/neuralmeter

.PHONY: build-macos
build-macos:
	GOOS=darwin GOARCH=amd64 go build -tags "cuda opencl metal" -o bin/neuralmeter-macos ./cmd/neuralmeter

.PHONY: build-macos-arm64
build-macos-arm64:
	GOOS=darwin GOARCH=arm64 go build -tags "cuda opencl metal" -o bin/neuralmeter-macos-arm64 ./cmd/neuralmeter

# Clean build artifacts
.PHONY: clean
clean:
	rm -rf bin/

# Install dependencies (placeholder - would install CUDA, OpenCL SDKs)
.PHONY: deps
deps:
	@echo "Installing development dependencies..."
	@echo "Note: You may need to manually install:"
	@echo "  - CUDA Toolkit (for CUDA support)"
	@echo "  - OpenCL SDK (for OpenCL support)"
	@echo "  - Xcode Command Line Tools (for Metal support on macOS)"

# Development helpers
.PHONY: fmt
fmt:
	go fmt ./...

.PHONY: vet
vet:
	go vet ./...

.PHONY: lint
lint:
	golangci-lint run

# Help target
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  build          - Basic build without GPU acceleration"
	@echo "  build-cuda     - Build with CUDA support"
	@echo "  build-opencl   - Build with OpenCL support"
	@echo "  build-metal    - Build with Metal support (macOS only)"
	@echo "  build-all-gpu  - Build with all available GPU support for current platform"
	@echo "  test           - Run tests"
	@echo "  test-cuda      - Run tests with CUDA tags"
	@echo "  test-opencl    - Run tests with OpenCL tags"
	@echo "  test-metal     - Run tests with Metal tags"
	@echo "  build-linux    - Cross-compile for Linux with GPU support"
	@echo "  build-windows  - Cross-compile for Windows with GPU support"
	@echo "  build-macos    - Cross-compile for macOS with GPU support"
	@echo "  clean          - Remove build artifacts"
	@echo "  fmt            - Format Go code"
	@echo "  vet            - Run go vet"
	@echo "  lint           - Run golangci-lint"
	@echo "  help           - Show this help message" 