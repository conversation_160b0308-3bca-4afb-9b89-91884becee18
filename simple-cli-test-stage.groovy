        stage('Run CLI Tests') {
            steps {
                script {
                    echo "🧪 Running CLI connectivity tests..."
                    
                    sshagent([JENKINS_CREDENTIALS_ID]) {
                        // Set environment variables
                        env.TEST_SUITE = 'connectivity'
                        env.CLI_BINARY_PATH = "${DEPLOY_PATH}/${CLI_BINARY_NAME}"
                        env.SSH_USER = 'neuro'
                        
                        // Run the CLI test script
                        sh './test/cli-scripts/run-cli-tests-from-main-pipeline.sh'
                    }
                    
                    echo "✅ CLI tests completed"
                }
            }
            post {
                always {
                    // Archive CLI test results
                    archiveArtifacts artifacts: 'test-results/**/*', allowEmptyArchive: true
                }
            }
        }